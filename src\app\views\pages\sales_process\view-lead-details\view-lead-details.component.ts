import { Component, OnInit, ViewChild, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbNavModule, NgbTooltipModule ,NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { SalesData } from '../sales-list/sales-list.component';
import { ProductDataService } from '../../../../core/services/product-data.service';
import { ProductOneComponent } from '../product_type/product-one/product-one.component';
import { ProductTwoComponent } from '../product_type/product-two/product-two.component';
import { SalesService, SalesResponse, SalesCreateRequest, PeopleInformation } from '../../../../core/services/sales.service';
import { ProductTypeService, ProductType, SubProductType } from '../../../../core/services/product-type.service';
import { LocationService } from '../../../../core/services/location.service';
import { EmployeeService } from '../../../../core/services/employee.service';
import { MasterService } from '../../../../core/services/master.service';
import { LeadCategoryService, LeadCategory, Source } from '../../../../core/services/lead-category.service';
import { CarComponent } from '../product_type/product-four/car/car.component';
import { LifeComponent } from '../product_type/product-four/life/life.component';
import { PropertyComponent } from '../product_type/product-three/property/property.component';
import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

const defaultAccordion = {
  htmlCode: 
`<div ngbAccordion [closeOthers]="true">
  <div ngbAccordionItem [collapsed]="false">
    <h2 ngbAccordionHeader>
      <button ngbAccordionButton>Simple</button>
    </h2>
    <div ngbAccordionCollapse>
      <div ngbAccordionBody>
        <ng-template>
          Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon
          officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf
          moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim
          keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur
          butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably
          haven't heard of them accusamus labore sustainable VHS.
        </ng-template>
      </div>
    </div>
  </div>
  <div ngbAccordionItem>
    <h2 ngbAccordionHeader>
      <button ngbAccordionButton>
        <span>&#9733; <b>Fancy</b> title &#9733;</span>
      </button>
    </h2>
    <div ngbAccordionCollapse>
      <div ngbAccordionBody>
        <ng-template>
          Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon
          officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf
          moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim
          keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur
          butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably
          haven't heard of them accusamus labore sustainable VHS.
        </ng-template>
      </div>
    </div>
  </div>
  <div ngbAccordionItem [disabled]="true">
    <h2 ngbAccordionHeader>
      <button ngbAccordionButton>Disabled</button>
    </h2>
    <div ngbAccordionCollapse>
      <div ngbAccordionBody>
        <ng-template>
          Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. 3 wolf moon
          officia aute, non cupidatat skateboard dolor brunch. Food truck quinoa nesciunt laborum eiusmod. Brunch 3 wolf
          moon tempor, sunt aliqua put a bird on it squid single-origin coffee nulla assumenda shoreditch et. Nihil anim
          keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident. Ad vegan excepteur
          butcher vice lomo. Leggings occaecat craft beer farm-to-table, raw denim aesthetic synth nesciunt you probably
          haven't heard of them accusamus labore sustainable VHS.
        </ng-template>
      </div>
    </div>
  </div>
</div>`,
  tsCode: 
`import { Component } from '@angular/core';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-accordion',
  standalone: true,
  imports: [NgbAccordionModule],
  templateUrl: './accordion.component.html'
})
export class AccordionComponent {}`
}

// Extended SalesData interface with additional fields
interface ExtendedSalesData extends SalesData {
  subLocation?: string;
  contactName?: string;
  mobileNumber?: string;
  emailId?: string;
  professionTypeName?: string;
  connectWithId?: string;
  team?: string;
  loginPersonName?: string;
  handoverTo?: string;
  handoverEmployeeId?: string;
  // productType is already defined in SalesData as required
  subProductType?: string;
  createdBy?: string;
  createdDate?: string;
  lastUpdated?: string;
  people?: any[];
  priority?: string;
  expectedValue?: number;
  probability?: number;
  nextFollowUp?: string;
  notes?: string;
  formData?: any;
  productFormData?: any;
  lead_category_id?: string; // Add lead_category_id field
  contactInformation?: {
    name?: string;
    mobile?: string;
    email?: string;
  };
  locationObject?: {
    id: string;
    name: string;
    display_name?: string;
    code?: string;
    zone?: string;
  };
}

// Edit form data interface
interface EditFormData {
  leadId?: string;
  leadName?: string;
  leadCategory?: string;
  source?: string;
  sourceId?: string;
  companyName?: string;
  company?: string; // Added company property for associate data
  location?: string;
  locationId?: string;
  subLocation?: string;
  handoverTo?: string;
  handoverToId?: string;
  productType?: string;
  productTypeId?: string;
  subProductType?: string;
  subProductTypeId?: string;
  projectName?: string;
  connectWithName?: string;
  connectWithId?: string;
  connectWithMobile?: string;
  connectWithEmail?: string;
  // Associate Category fields
  associateNameCategory?: string;
  associateLocationCategory?: string;
  associateSubLocationCategory?: string;
  professionalCategory?: string;
  professionCategory?: string;
  leadNameCategory?: string;
  leadLocationCategory?: string;
  leadSubLocationCategory?: string;
}

// Location interface for dropdown
interface LocationOption {
  id: string;
  name: string;
  display_name?: string;
  code?: string;
}

// Employee interface for dropdown
interface EmployeeOption {
  id: string;
  name: string;
  display_name: string;
  employee_code?: string;
  first_name?: string;
  last_name?: string;
  user_active: boolean;
}

// Contact person interface for dropdown
interface ContactOption {
  id: string;
  name: string;
  mobile: string;
  email: string;
  is_primary_contact: boolean;
}

// Profession interface for dropdown
interface ProfessionOption {
  id: string;
  name: string;
  type?: string;
  status?: string;
}

// Extended PeopleInformation interface that includes API response fields
interface ExtendedPeopleInformation extends PeopleInformation {
  id?: string;
  sales_id?: string;
}



// Activity interface
interface Activity {
  id: number;
  type: string;
  title: string;
  description: string;
  date: string;
  createdBy?: string;
  followupDate?: string;
  followupTime?: string;
  alternativeNumber?: string;
}

@Component({
  selector: 'app-view-lead-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FeatherIconDirective,
    NgbNavModule,
    NgbTooltipModule,
    FormsModule,
    NgbAccordionModule,
    ProductOneComponent,
    ProductTwoComponent,
    CarComponent,
    LifeComponent,
    PropertyComponent
  ],
  templateUrl: './view-lead-details.component.html',
  styleUrl: './view-lead-details.component.scss'
})
export class ViewLeadDetailsComponent implements OnInit, AfterViewInit {
  leadId: string;
  leadData: ExtendedSalesData | undefined;
  isLoading: boolean = false;
  activeTab = 1;

  // ViewChild references for product form components
  @ViewChild('productOneRef') productOneComponent?: ProductOneComponent;
  @ViewChild('productTwoRef') productTwoComponent?: ProductTwoComponent;
  @ViewChild('carRef') carComponent?: CarComponent;
  @ViewChild('lifeRef') lifeComponent?: LifeComponent;
  @ViewChild('propertyRef') propertyComponent?: PropertyComponent;

  // Edit mode properties
  isEditMode = false;
  editFormData: EditFormData = {};

  // Component visibility properties
  showProductOne = false;
  showProductTwo = false;
  showCar = false;
  showLife = false;
  showProperty = false;

  // Dynamic dropdown properties
  productTypes: ProductType[] = [];
  subProductTypes: SubProductType[] = [];
  productTypesLoading = false;
  subProductTypesLoading = false;

  // Location dropdown properties
  locations: LocationOption[] = [];
  locationsLoading = false;

  // Employee dropdown properties
  employees: EmployeeOption[] = [];
  employeesLoading = false;

  // Contact dropdown properties
  contacts: ContactOption[] = [];
  contactsLoading = false;

  // Profession dropdown properties
  professions: ProfessionOption[] = [];
  professionsLoading = false;

  // Connect With dropdown properties
  connectWithList: {id: string, name: string, description: string}[] = [];
  connectWithLoading = false;

  // Sub-product type options based on product type (kept for backward compatibility)
  subProductTypeOptions: { [key: string]: { value: string; label: string }[] } = {
    'Corporate Syndication': [
      { value: 'CF', label: 'Construction Funding (CF)' },
      { value: 'IF', label: 'Inventory Funding (IF)' },
      { value: 'PF', label: 'Project Funding (PF)' },
      { value: 'HF', label: 'Hospital Funding (HF)' },
      { value: 'EF', label: 'Education funding (EF)' }
    ],
    'Retail Syndication': [
      { value: 'HL', label: 'Home Loan (HL)' },
      { value: 'LAP', label: 'Loan Against Property (LAP)' },
      { value: 'LRD', label: 'Lease Rental Discounting (LRD)' },
      { value: 'NRPL', label: 'Non Residential Property Loan (NRPL)' }
    ],
    'Property': [],
    'Insurance': [
      { value: 'CAR', label: 'Contractor All Risk (CAR)' },
      { value: 'LI', label: 'Life Insurance (LI)' }
    ]
  };

  // Update functionality properties
  isUpdating = false;

  // Product form data for pre-population and capture
  productFormData: any = null;

  // Lead categories for mapping UUID to name
  leadCategories: LeadCategory[] = [];
  leadCategoriesLoading = false;

  // Sources for mapping UUID to name
  sources: Source[] = [];
  sourcesLoading = false;

  // Follow-up form properties
  showFollowupForm = false;
  followupNotes = '';
  followupType = 'call';
  followupDate = '';
  followupTime = '';
  handoverToOps = false;
  handoverNotes = '';

  // Contact form properties
  showContactForm = false;
  contactConnectWith = '';
  contactName = '';
  contactMobileNo = '';
  contactEmailId = '';
  contactIsPrimary = false;

  // Associate dropdown properties
  associates: any[] = [];
  associatesLoading = false;
  filteredAssociates: any[] = [];
  associateSearchValue = '';
  selectedAssociate: any = null;
  
  // Profession types and professions for Associate category
  professionTypes: {value: string, label: string}[] = [];
  professionTypesLoading = false;
  filteredCategoryProfessions: any[] = [];

  // Contact management properties
  showContactsManager = false;
  editingContactIndex = -1;

  // Lead action properties
  selectedLeadAction: string = '';
  opsInstructions = '';

  // Business Call properties
  businessCallType = '';
  businessCallDate = '';
  businessCallTime = '';
  businessCallDescription = '';

  // Activities list
  activities: Activity[] = [
    {
      id: 1,
      type: 'call',
      title: 'Call with client',
      description: 'Discussed project requirements and timeline',
      date: 'Today, 10:30 AM',
      createdBy: 'John Doe',
      followupDate: new Date().toISOString().split('T')[0], // Today's date
      followupTime: '14:30' // 2:30 PM
    },
    {
      id: 2,
      type: 'email',
      title: 'Email sent',
      description: 'Sent project proposal and quotation',
      date: 'Yesterday, 2:30 PM',
      createdBy: 'Jane Smith',
      followupDate: (() => {
        const date = new Date();
        date.setDate(date.getDate() + 3); // 3 days from now
        return date.toISOString().split('T')[0];
      })(),
      followupTime: '11:00' // 11:00 AM
    },
    {
      id: 3,
      type: 'create',
      title: 'Lead created',
      description: 'Lead added to the system',
      date: '15 Jun 2023, 9:15 AM',
      createdBy: 'Admin'
      // No follow-up for lead creation
    }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private productDataService: ProductDataService,
    private salesService: SalesService,
    private productTypeService: ProductTypeService,
    private locationService: LocationService,
    private employeeService: EmployeeService,
    private masterService: MasterService,
    private leadCategoryService: LeadCategoryService,
    private cdr: ChangeDetectorRef
  ) {
    this.leadId = '';
    // Set default follow-up date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.followupDate = tomorrow.toISOString().split('T')[0];

    // Set default follow-up time to 10:00 AM
    this.followupTime = '10:00';
  }

  ngOnInit(): void {
    // Get the lead ID from the route parameters
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.leadId = id;
        // Load dropdown data first to ensure categories are available
        this.loadDropdownData();
        // Then load lead data
        setTimeout(() => {
          this.loadLeadData();
        }, 300);
      } else {
        // Redirect back to the list if no ID is provided
        this.router.navigate(['/sales-list']);
      }
    });

    // Associate search will be handled by onAssociateSearch method
  }

  ngAfterViewInit(): void {
    // ViewChild components are available here
    console.log('🔧 ViewChild components initialized');
  }



  /**
   * Handle product form submission from child components
   * This method can be used if product forms emit submitSales events
   */
  onProductFormSubmit(productFormData: any): void {
    console.log('🎯 Product form submitted in view-lead-details:', productFormData);

    // Store the product form data for later use in saveChanges
    this.productFormData = productFormData;

    // Optionally auto-save or show a notification
    console.log('✅ Product form data captured and stored');
  }

  loadLeadData(): void {
    this.isLoading = true;
    console.log('🔍 Loading lead data for ID:', this.leadId);
    
    // Ensure lead categories are loaded before proceeding
    if (this.leadCategories.length === 0) {
      console.log('⚠️ Lead categories not loaded yet, loading them first...');
      this.loadLeadCategoriesData().subscribe({
        next: (leadCategories) => {
          console.log('✅ Lead categories loaded successfully:', leadCategories);
          this.leadCategories = leadCategories || [];
          this.leadCategoriesLoading = false;
          // Now proceed with loading lead data
          this.fetchLeadData();
        },
        error: (error) => {
          console.error('❌ Failed to load lead categories:', error);
          this.leadCategoriesLoading = false;
          // Still try to load lead data even if categories failed
          this.fetchLeadData();
        }
      });
    } else {
      // Lead categories already loaded, proceed with loading lead data
      this.fetchLeadData();
    }
  }
  
  /**
   * Fetch lead data from API
   */
  private fetchLeadData(): void {
    this.salesService.getSalesById(this.leadId).subscribe({
      next: (response) => {
        console.log('✅ Lead data loaded successfully:', response);
        console.log('🔍 Raw API Response Email Field:', response.data?.email);

        if (response.success && response.data) {
          console.log('🔍 DEBUG: Raw API response data:', response.data);
          console.log('🔍 DEBUG: people_information in raw response:', response.data.people_information);

          // Map API response to component data structure
          this.leadData = this.mapSalesResponseToExtendedData(response.data);

          console.log('✅ Mapped lead data emailId:', this.leadData.emailId);
          console.log('🔍 DEBUG: Mapped lead data structure:', this.leadData);
          console.log('✅ Lead data mapping complete:', {
            emailId: this.leadData.emailId,
            leadId: this.leadData.leadId,
            leadName: this.leadData.leadName
          });

          // Debug email fields after data loading
          setTimeout(() => {
            this.debugEmailFields();
          }, 100);

          // Load sub product types for the current product type to enable proper mapping
          this.loadSubProductTypesForCurrentLead();

          // Determine component visibility based on current product type and populate people information
          setTimeout(() => {
            this.determineInitialComponentVisibility();
          }, 200);

          this.isLoading = false;
        } else {
          console.error('❌ Invalid response format:', response);
          this.handleLeadNotFound();
        }
      },
      error: (error) => {
        console.error('❌ Failed to load lead data:', error);
        this.isLoading = false;
        this.handleLeadNotFound();
      }
    });
  }

  private handleLeadNotFound(): void {
    console.log('🔄 Lead not found, redirecting to sales list');
    this.router.navigate(['/sales-list']);
  }

  /**
   * Load product types from API for dynamic dropdown
   */
  loadProductTypes(): void {
    this.productTypesLoading = true;
    console.log('🔍 Loading product types for dropdown...');

    this.productTypeService.getActiveProductTypes().subscribe({
      next: (productTypes) => {
        console.log('✅ Product types loaded successfully:', productTypes);
        this.productTypes = productTypes;
        this.productTypesLoading = false;
      },
      error: (error) => {
        console.error('❌ Failed to load product types:', error);
        this.productTypesLoading = false;
        // Fallback to hardcoded options if API fails
        this.productTypes = [];
      }
    });
  }

  /**
   * Load sub product types based on selected product type
   */
  loadSubProductTypes(productTypeId: string): void {
    if (!productTypeId) {
      this.subProductTypes = [];
      return;
    }

    this.subProductTypesLoading = true;
    console.log('🔍 Loading sub product types for product type:', productTypeId);

    this.productTypeService.getSubProductTypesByProductTypeId(productTypeId).subscribe({
      next: (subProductTypes) => {
        console.log('✅ Sub product types loaded successfully:', subProductTypes);
        this.subProductTypes = subProductTypes;
        this.subProductTypesLoading = false;
      },
      error: (error) => {
        console.error('❌ Failed to load sub product types:', error);
        this.subProductTypesLoading = false;
        this.subProductTypes = [];
      }
    });
  }

  /**
   * Load all dropdown data in parallel
   */
  loadDropdownData(): void {
    console.log('🔍 Loading dropdown data...');
    
    // Load lead categories first to ensure they're available for edit mode
    this.loadLeadCategoriesData().subscribe({
      next: (leadCategories) => {
        console.log('✅ Lead categories loaded successfully:', leadCategories);
        this.leadCategories = leadCategories || [];
        this.leadCategoriesLoading = false;
      },
      error: (error) => {
        console.error('❌ Failed to load lead categories:', error);
        this.leadCategoriesLoading = false;
      }
    });

    forkJoin({
      productTypes: this.loadProductTypesData(),
      locations: this.loadLocationsData(),
      employees: this.loadEmployeesData(),
      professions: this.loadProfessionsData(),
      connectWithList: this.loadConnectWithData(),
      leadCategories: this.loadLeadCategoriesData(),
      sources: this.loadSourcesData()
    }).subscribe({
      next: (results) => {
        console.log('✅ All dropdown data loaded successfully:', results);

        // Update component properties
        this.productTypes = results.productTypes || [];
        this.productTypesLoading = false;

        this.locations = results.locations || [];
        this.locationsLoading = false;

        // Process employees data
        this.employees = (results.employees || []).map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name || ''} ${emp.last_name || ''}`.trim(),
          display_name: `${emp.first_name || ''} ${emp.last_name || ''}`.trim() + (emp.employee_code ? ` (${emp.employee_code})` : ''),
          employee_code: emp.employee_code,
          first_name: emp.first_name,
          last_name: emp.last_name,
          user_active: emp.user_active !== false
        })).filter((emp: EmployeeOption) => emp.user_active); // Only show active employees
        this.employeesLoading = false;

        // Process professions data
        this.professions = (results.professions || []).map((prof: any) => ({
          id: prof.id,
          name: prof.name,
          type: prof.type,
          status: prof.status
        }));
        this.professionsLoading = false;
        
        // Initialize profession types from professions
        const professionTypesSet = new Set<string>();
        this.professions.forEach(prof => {
          if (prof.type) {
            professionTypesSet.add(prof.type);
          }
        });
        this.professionTypes = Array.from(professionTypesSet).map(type => ({
          value: type,
          label: type
        }));
        this.professionTypesLoading = false;

        // Process Connect With data
        this.connectWithList = (results.connectWithList || []).map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description
        }));
        this.connectWithLoading = false;

        console.log('✅ Connect With data loaded successfully:', {
          count: this.connectWithList.length,
          items: this.connectWithList.map(cw => ({ id: cw.id, name: cw.name }))
        });

        // Process lead categories data
        this.leadCategories = results.leadCategories || [];
        this.leadCategoriesLoading = false;

        // Process sources data
        this.sources = results.sources || [];
        this.sourcesLoading = false;

        console.log('📊 Processed dropdown data:', {
          productTypes: this.productTypes.length,
          locations: this.locations.length,
          employees: this.employees.length,
          professions: this.professions.length,
          connectWithList: this.connectWithList.length,
          leadCategories: this.leadCategories.length,
          sources: this.sources.length
        });
      },
      error: (error) => {
        console.error('❌ Failed to load some dropdown data:', error);
        this.productTypesLoading = false;
        this.locationsLoading = false;
        this.employeesLoading = false;
        this.professionsLoading = false;
        this.connectWithLoading = false;
        this.leadCategoriesLoading = false;
        this.sourcesLoading = false;
      }
    });
  }

  /**
   * Load product types data
   */
  private loadProductTypesData() {
    this.productTypesLoading = true;
    return this.productTypeService.getActiveProductTypes().pipe(
      catchError(error => {
        console.error('❌ Failed to load product types:', error);
        this.productTypesLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load locations data
   */
  private loadLocationsData() {
    this.locationsLoading = true;
    return this.locationService.getActiveLocations().pipe(
      catchError(error => {
        console.error('❌ Failed to load locations:', error);
        this.locationsLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load employees data
   */
  private loadEmployeesData() {
    this.employeesLoading = true;
    return this.employeeService.getAllEmployees().pipe(
      catchError(error => {
        console.error('❌ Failed to load employees:', error);
        this.employeesLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load professions data
   */
  private loadProfessionsData() {
    this.professionsLoading = true;
    return this.masterService.getActiveProfessions().pipe(
      catchError(error => {
        console.error('❌ Failed to load professions:', error);
        this.professionsLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load Connect With data
   */
  private loadConnectWithData() {
    this.connectWithLoading = true;
    return this.masterService.getConnectWithList().pipe(
      catchError(error => {
        console.error('❌ Failed to load connect with list:', error);
        this.connectWithLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load Lead Categories data
   */
  private loadLeadCategoriesData() {
    this.leadCategoriesLoading = true;
    return this.leadCategoryService.getActiveLeadCategories().pipe(
      catchError(error => {
        console.error('❌ Failed to load lead categories:', error);
        this.leadCategoriesLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load Sources data
   */
  private loadSourcesData() {
    this.sourcesLoading = true;
    return this.leadCategoryService.getActiveSources().pipe(
      catchError(error => {
        console.error('❌ Failed to load sources:', error);
        this.sourcesLoading = false;
        return of([]);
      })
    );
  }

  /**
   * Load sub product types for the current lead's product type
   * This enables proper UUID to name mapping in view mode
   */
  private loadSubProductTypesForCurrentLead(): void {
    // Check if we have a product type UUID in the lead data
    const productTypeId = this.getProductTypeIdFromLeadData();

    if (productTypeId) {
      console.log('🔍 Loading sub product types for current lead product type:', productTypeId);
      this.loadSubProductTypes(productTypeId);
    } else {
      console.log('⚠️ No product type ID found in lead data for sub product type loading');
    }
  }

  /**
   * Extract product type ID from lead data (handles both UUID and name formats)
   */
  private getProductTypeIdFromLeadData(): string | null {
    // Check if productType is a UUID
    if (this.leadData?.productType && this.isValidUUID(this.leadData.productType)) {
      return this.leadData.productType;
    }

    // If productType is a name, find the corresponding ID from loaded product types
    if (this.leadData?.productType && this.productTypes.length > 0) {
      const productType = this.productTypes.find(pt => pt.name === this.leadData.productType);
      return productType ? productType.id : null;
    }

    return null;
  }

  /**
   * Load contacts from people_information
   */
  loadContacts(): void {
    if (this.leadData?.people && this.leadData.people.length > 0) {
      this.contacts = this.leadData.people.map((person: any) => ({
        id: person.id || `temp_${Date.now()}_${Math.random()}`,
        name: person.name !== undefined ? person.name : 'N/A',
        mobile: person.mobile !== undefined ? person.mobile : 'N/A',
        email: person.email !== undefined ? person.email : '',
        is_primary_contact: Boolean(person.is_primary_contact)
      }));
      console.log('✅ Contacts loaded from people_information:', this.contacts);
    } else {
      this.contacts = [];
      console.log('⚠️ No contacts found in people_information');
    }
  }

  /**
   * Handle location dropdown change
   */
  onLocationChange(): void {
    console.log('🔄 Location changed:', this.editFormData.locationId);

    const selectedLocation = this.locations.find(loc => loc.id === this.editFormData.locationId);
    if (selectedLocation) {
      // Use name field for display (as per requirement)
      this.editFormData.location = selectedLocation.name || selectedLocation.display_name;
      console.log('✅ Location mapped:', {
        id: this.editFormData.locationId,
        name: selectedLocation.name,
        display_name: selectedLocation.display_name,
        mappedValue: this.editFormData.location,
        fullLocationObject: selectedLocation
      });
    } else {
      console.log('⚠️ No location found for ID:', this.editFormData.locationId);
      console.log('📋 Available locations:', this.locations);
    }
  }

  /**
   * Handle handover to dropdown change
   */
  onHandoverToChange(): void {
    console.log('🔄 Handover to changed:', this.editFormData.handoverToId);

    const selectedEmployee = this.employees.find(emp => emp.id === this.editFormData.handoverToId);
    if (selectedEmployee) {
      this.editFormData.handoverTo = selectedEmployee.display_name;
      console.log('✅ Handover to mapped:', {
        id: this.editFormData.handoverToId,
        name: this.editFormData.handoverTo
      });
    } else if (this.editFormData.handoverToId === '') {
      this.editFormData.handoverTo = 'Not Assigned';
    }
  }

  /**
   * Handle connect with dropdown change (connect with selection)
   */
  onConnectWithChange(): void {
    console.log('🔄 Connect with changed:', this.editFormData.connectWithId);

    const selectedConnectWith = this.connectWithList.find(item => item.id === this.editFormData.connectWithId);
    if (selectedConnectWith) {
      this.editFormData.connectWithName = selectedConnectWith.name;
      console.log('✅ Connect with item mapped:', {
        id: this.editFormData.connectWithId,
        name: this.editFormData.connectWithName,
        description: selectedConnectWith.description
      });
    } else if (this.editFormData.connectWithId === '') {
      this.editFormData.connectWithName = '';
    }
  }

  /**
   * Handle Lead Category dropdown change
   */
  onLeadCategoryChange(): void {
    console.log('🔄 Lead Category changed:', this.editFormData.leadCategory);

    // Update Lead Source based on Lead Category selection
    const isAssociateCategory = this.leadCategories.find(cat => cat.id === this.editFormData.leadCategory)?.name === 'Associate';
    
    if (isAssociateCategory) {
      // Find the Associate source ID
      const associateSource = this.sources.find(src => src.name === 'Associate');
      if (associateSource) {
        console.log('✅ Setting source to Associate based on Lead Category');
        this.editFormData.sourceId = associateSource.id;
        this.editFormData.source = associateSource.name;
        this.onSourceChange(); // Trigger source change handler
        
        // Update the ProductDataService with the lead category and source
        this.productDataService.setLeadCategory('Associate');
        this.productDataService.setLeadSource('Associate');
        
        // Immediately load associates when Lead Category is Associate
        console.log('🔍 Lead Category is Associate, loading associates...');
        this.loadAssociates();
      }
    } else {
      // Reset source when lead category changes to non-Associate
      this.editFormData.source = '';
      this.editFormData.sourceId = '';
      
      // Update the ProductDataService with the lead category
      const leadCategory = this.leadCategories.find(cat => cat.id === this.editFormData.leadCategory)?.name || 'Lead Data';
      this.productDataService.setLeadCategory(leadCategory);
    }

    // Reset associate selection
    this.selectedAssociate = null;
    this.associateSearchValue = '';
    this.filteredAssociates = [];

    // Load associates if needed (for other cases)
    this.checkAndLoadAssociates();
  }

  /**
   * Handle Source dropdown change
   */
  onSourceChange(): void {
    console.log('🔄 Source changed:', this.editFormData.sourceId);

    const selectedSource = this.sources.find(source => source.id === this.editFormData.sourceId);
    if (selectedSource) {
      this.editFormData.source = selectedSource.name;
      console.log('✅ Source mapped:', {
        id: this.editFormData.sourceId,
        name: this.editFormData.source
      });
      
      // Update the ProductDataService with the lead source
      this.productDataService.setLeadSource(selectedSource.name);
    } else if (this.editFormData.sourceId === '') {
      this.editFormData.source = '';
      this.productDataService.setLeadSource('');
    }

    // Reset associate selection when source changes
    this.selectedAssociate = null;
    this.associateSearchValue = '';
    this.filteredAssociates = [];

    // Load associates if needed
    this.checkAndLoadAssociates();
  }

  /**
   * Check if Source dropdown should be shown
   */
  shouldShowSourceDropdown(): boolean {
    if (!this.isEditMode) return false;

    // Show source dropdown when Lead Category is 'Lead Data'
    const leadDataCategoryId = 'c8ee56a9-3298-49fb-aefe-71e18c71fde7';
    return this.editFormData.leadCategory === leadDataCategoryId;
  }

  /**
   * Check if Associate dropdown should be shown
   */
  shouldShowAssociateDropdown(): boolean {
    if (!this.isEditMode) return false;

    // Show associate dropdown when Lead Category is 'Lead Data' AND Source is 'Associate'
    const leadDataCategoryId = 'c8ee56a9-3298-49fb-aefe-71e18c71fde7';
    const associateSourceId = '89f4f76b-2143-4e05-9370-7d7ca5fc526a';

    return this.editFormData.leadCategory === leadDataCategoryId &&
           this.editFormData.sourceId === associateSourceId;
  }
  
  /**
   * Check if Associate fields should be shown
   * This is used to show Associate fields for both Lead Category = Associate
   * and Lead Category = Lead Data with Source = Associate
   */
  shouldShowAssociateFields(): boolean {
    if (!this.isEditMode) return false;
    
    // Show Associate fields when either:
    // 1. Lead Category is 'Associate'
    // 2. Lead Category is 'Lead Data' AND Source is 'Associate'
    return this.isLeadCategorySelected('Associate') || this.shouldShowAssociateDropdown();
  }
  
  /**
   * Check if the selected Lead Category matches the provided name
   */
  isLeadCategorySelected(categoryName: string): boolean {
    if (!this.editFormData?.leadCategory || !this.leadCategories?.length) {
      return false;
    }
    
    // Find the category by ID and check if its name matches
    const selectedCategory = this.leadCategories.find(cat => cat.id === this.editFormData.leadCategory);
    return selectedCategory?.name === categoryName;
  }
  
  /**
   * Handle Professional Category Type change for Associate Lead Category
   */
  onProfessionalCategoryTypeChange(): void {
    console.log('🔄 Professional Category Type changed:', this.editFormData.professionalCategory);
    
    // Reset profession selection
    this.editFormData.professionCategory = '';
    
    // Filter professions based on selected professional type
    this.filterProfessionsByType(this.editFormData.professionalCategory);
  }
  
  /**
   * Filter professions based on selected professional type
   */
  filterProfessionsByType(professionalType: string): void {
    if (!professionalType) {
      this.filteredCategoryProfessions = [];
      return;
    }
    
    // Filter professions based on type
    this.filteredCategoryProfessions = this.professions.filter(profession => 
      profession.type === professionalType
    );
    
    console.log('✅ Filtered professions by type:', {
      type: professionalType,
      count: this.filteredCategoryProfessions.length,
      professions: this.filteredCategoryProfessions
    });
  }

  /**
   * Check if associates should be loaded and load them if needed
   */
  checkAndLoadAssociates(): void {
    if ((this.shouldShowAssociateFields() || this.shouldShowAssociateDropdown()) && this.associates.length === 0) {
      console.log('📥 Loading associates for Associate fields...');
      this.loadAssociates();
    }
  }

  /**
   * Load associates from API
   */
  loadAssociates(): void {
    this.associatesLoading = true;
    console.log('📥 Loading associates from API...');

    this.masterService.getRawAssociates().subscribe({
      next: (response) => {
        console.log('📥 Raw associates response:', response);
        if (response.success && response.data) {
          this.associates = response.data.map((associate: any) => ({
            id: associate.id,
            associate_name: associate.associate_name,
            company_name: associate.company_name,
            location_id: associate.location_id,
            location_name: associate.location_name,
            sub_location: associate.sub_location,
            profession_type: associate.profession_type,
            profession_id: associate.profession_id,
            profession_name: associate.profession_name,
            status: associate.status
          }));

          // Initialize filtered associates with all associates
          this.filteredAssociates = [];
          console.log('✅ Associates loaded successfully:', this.associates.length, this.associates);
        } else {
          console.log('⚠️ No associates data in response:', response);
          this.associates = [];
        }
      },
      error: (error) => {
        console.error('❌ Error loading associates:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          error: error.error
        });
        this.associates = [];
        this.filteredAssociates = [];
      },
      complete: () => {
        this.associatesLoading = false;
        console.log('🏁 Associates loading completed');
      }
    });
  }

  /**
   * Handle associate selection
   */
  onAssociateSelect(associate: any): void {
    this.selectedAssociate = associate;
    this.associateSearchValue = associate.associate_name;
    this.filteredAssociates = [];
    
    // Set the associateNameCategory field for form submission
    this.editFormData.associateNameCategory = associate.associate_name;
    
    // Also populate other associate fields
    this.editFormData.associateLocationCategory = associate.location_name || '';
    this.editFormData.associateSubLocationCategory = associate.sub_location || '';
    this.editFormData.professionalCategory = associate.profession_type || '';
    this.editFormData.professionCategory = associate.profession_name || '';
    this.editFormData.company = associate.company_name || '';
    
    // Filter professions based on selected professional type if available
    if (associate.profession_type) {
      this.filterProfessionsByType(associate.profession_type);
    }

    console.log('✅ Associate selected:', associate);
    console.log('✅ Form fields updated with associate data:', {
      associateNameCategory: this.editFormData.associateNameCategory,
      associateLocationCategory: this.editFormData.associateLocationCategory,
      associateSubLocationCategory: this.editFormData.associateSubLocationCategory,
      professionalCategory: this.editFormData.professionalCategory,
      professionCategory: this.editFormData.professionCategory,
      company: this.editFormData.company
    });
  }

  /**
   * Handle associate search input
   */
  onAssociateSearch(event: any): void {
    const searchTerm = event.target.value;
    this.associateSearchValue = searchTerm;

    if (searchTerm && searchTerm.length >= 2) {
      this.filterAssociates(searchTerm);
    } else {
      this.filteredAssociates = [];
    }
    
    // Always update the associateNameCategory field for form submission
    this.editFormData.associateNameCategory = searchTerm;
    
    // If Lead Category is Associate, also check and load associates if needed
    if (this.isLeadCategorySelected('Associate') && !this.associates.length && !this.associatesLoading) {
      console.log('🔍 Lead Category is Associate, loading associates for search...');
      this.loadAssociates();
    }
  }

  /**
   * Filter associates based on search term
   */
  filterAssociates(searchTerm: string): void {
    if (!this.associates || this.associates.length === 0) {
      // If associates aren't loaded yet, load them now
      if (!this.associatesLoading) {
        console.log('📥 Loading associates for search...');
        this.loadAssociates();
        
        // Set a timeout to retry filtering after associates are loaded
        setTimeout(() => {
          if (this.associates.length > 0 && searchTerm) {
            this.filterAssociates(searchTerm);
          }
        }, 1000);
      }
      this.filteredAssociates = [];
      return;
    }

    const term = searchTerm.toLowerCase();
    this.filteredAssociates = this.associates.filter(associate =>
      associate.associate_name?.toLowerCase().includes(term) ||
      associate.company_name?.toLowerCase().includes(term) ||
      associate.location_name?.toLowerCase().includes(term)
    );
    
    console.log(`✅ Found ${this.filteredAssociates.length} associates matching "${searchTerm}"`);
  }

  /**
   * Get Lead Category ID for edit form initialization
   */
  getLeadCategoryId(): string {
    // Try direct API response fields first
    if (this.leadData?.lead_category_id) {
      return this.leadData.lead_category_id;
    }
    
    // Try form data fields next
    if (this.leadData?.formData?.lead_category_id) {
      return this.leadData.formData.lead_category_id;
    }

    // Try source details category
    const sourceDetails = this.leadData?.formData?.source_details;
    if (sourceDetails?.category) {
      // If it's a UUID, use it directly
      if (this.isValidUUID(sourceDetails.category)) {
        return sourceDetails.category;
      }
      
      // Try to map category name to ID
      const category = this.leadCategories.find(cat => cat.name === sourceDetails.category);
      if (category) {
        return category.id;
      }
    }
    
    return '';
  }

  /**
   * Get Source ID for edit form initialization
   */
  getSourceId(): string {
    // Try to get from form data first
    if (this.leadData?.formData?.source_id) {
      return this.leadData.formData.source_id;
    }

    // Try to map from source name
    if (this.leadData?.source && this.sources.length > 0) {
      const source = this.sources.find(s => s.name === this.leadData.source);
      return source?.id || '';
    }

    return '';
  }

  private mapSalesResponseToExtendedData(salesData: any): ExtendedSalesData {
    // Debug logging for lead category
    console.log('🔍 API Response Lead Category:', {
      lead_category_id: salesData.lead_category_id,
      form_data_lead_category_id: salesData.form_data?.lead_category_id,
      source_details_category: salesData.form_data?.source_details?.category
    });
    // Debug logging for field mapping
    console.log('🔍 API Response Field Mapping:');
    console.log('📧 Root level email:', salesData.email);
    console.log('📧 People information emails:', salesData.people_information?.map((p: any) => p.email));
    console.log('👥 Full people_information from API:', salesData.people_information);
    console.log('👤 Profession type name:', salesData.profession_type_name);
    console.log('📍 Location data:', {
      name: salesData.location?.name,
      display_name: salesData.location?.display_name,
      id: salesData.location?.id
    });
    console.log('🔧 Product form data extraction:', {
      form_data_product_form_data: salesData.form_data?.product_form_data,
      direct_product_form_data: salesData.product_form_data,
      form_data_structure: salesData.form_data
    });
    console.log('👥 People information sources:', {
      form_data_people_information: salesData.form_data?.people_information,
      root_level_people_information: salesData.people_information,
      using_form_data: !!salesData.form_data?.people_information,
      form_data_count: salesData.form_data?.people_information?.length || 0,
      root_level_count: salesData.people_information?.length || 0
    });

    // Map the API response to the component's expected data structure
    return {      
      lead_category_id: salesData.lead_category_id || salesData.form_data?.lead_category_id || '',
      id: salesData.id,
      leadId: salesData.unique_id || 'N/A',
      leadName: salesData.lead_name || 'N/A',
      source: this.getSourceDisplayName(salesData),
      sourceType: 'API',
      location: salesData.location?.name || salesData.location?.display_name || 'N/A',
      locationObject: salesData.location ? {
        id: salesData.location.id,
        name: salesData.location.name,
        display_name: salesData.location.display_name,
        code: salesData.location.code,
        zone: salesData.location.zone
      } : undefined,
      subLocation: salesData.sub_location || 'N/A',
      companyName: salesData.company || 'N/A',
      projectName: salesData.lead_name || 'N/A',
      // Store UUID values for proper mapping - these will be mapped to names in display methods
      productType: salesData.product_type_id || salesData.product_type_name || 'N/A',
      subProductType: salesData.sub_product_type_id || salesData.sub_product_type_name || 'N/A',
      status: salesData.status || 'new',
      createdBy: salesData.created_by_name || 'N/A',
      handoverTo: salesData.handover_to_name || 'Not Assigned',
      emailId: salesData.email || '', // Main email field from API (preserve empty string instead of 'N/A')
      professionTypeName: salesData.profession_type_name || '', // Connect With Name field
      connectWithId: salesData.connect_with_id || '', // Connect With ID for UUID mapping
      // Map people information from form_data.people_information (source of truth)
      people: salesData.form_data?.people_information?.map((person: any, index: number) => {
        console.log(`🔍 Mapping person ${index + 1} from form_data.people_information:`, {
          personData: person,
          connect_with_id: person.connect_with_id,
          hasConnectWithId: !!person.connect_with_id
        });

        return {
          id: person.id, // Preserve the original ID from API
          name: person.name !== undefined ? person.name : '',
          email: person.email !== undefined ? person.email : '',
          mobile: person.mobile !== undefined ? person.mobile : '', // Use 'mobile' consistently
          role: person.notes !== undefined ? person.notes : '',
          is_primary_contact: Boolean(person.is_primary_contact), // Use consistent field name
          connect_with_id: person.connect_with_id || null, // Store connect_with_id for mapping
          // Store additional API fields for reference
          sales_id: person.sales_id,
          role_id: person.role_id,
          is_active: person.is_active,
          created_at: person.created_at,
          updated_at: person.updated_at
        };
      }) ||
      // Fallback to root-level people_information if form_data version doesn't exist
      salesData.people_information?.map((person: any, index: number) => {
        console.log(`🔍 Mapping person ${index + 1} from root-level people_information (fallback):`, {
          personData: person,
          connect_with_id: person.connect_with_id,
          hasConnectWithId: !!person.connect_with_id
        });

        return {
          id: person.id,
          name: person.name !== undefined ? person.name : '',
          email: person.email !== undefined ? person.email : '',
          mobile: person.mobile !== undefined ? person.mobile : '',
          role: person.notes !== undefined ? person.notes : '',
          is_primary_contact: Boolean(person.is_primary_contact),
          connect_with_id: person.connect_with_id || null, // Store connect_with_id for mapping
          sales_id: person.sales_id,
          role_id: person.role_id,
          is_active: person.is_active,
          created_at: person.created_at,
          updated_at: person.updated_at
        };
      }) || [],
      // Additional fields with default values
      priority: 'Medium',
      expectedValue: 0,
      probability: 50,
      nextFollowUp: '',
      notes: salesData.notes || '',
      createdDate: salesData.created_at ? new Date(salesData.created_at).toLocaleDateString() : '',
      lastUpdated: salesData.updated_at ? new Date(salesData.updated_at).toLocaleDateString() : '',
      // Map contact_information from API response
      contactInformation: salesData.form_data?.contact_information || salesData.contact_information || {
        name: '',
        mobile: '',
        email: ''
      },
      // Form data from product-specific components
      formData: {
        ...salesData.form_data,
        // Preserve original people_information for API payload
        people_information: salesData.people_information || []
      },
      // Extract product form data for pre-population (include coApplicantsDetailed)
      productFormData: {
        ...(salesData.form_data?.product_form_data || salesData.product_form_data || {}),
        // Include coApplicantsDetailed from the form_data level
        coApplicantsDetailed: salesData.form_data?.coApplicantsDetailed || null,
        // Include any other detailed data structures
        coApplicantDetailedData: salesData.form_data?.coApplicantDetailedData || null
      }
    };
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-success';
      case 'pending': return 'bg-warning';
      case 'completed': return 'bg-info';
      default: return 'bg-secondary';
    }
  }

  // Add a new follow-up
  addFollowup(): void {
    if (!this.followupNotes.trim()) {
      // Show validation error or alert
      alert('Please enter follow-up notes');
      return;
    }

    // Create a new activity for the follow-up
    const newActivity: Activity = {
      id: this.activities.length + 1,
      type: this.followupType,
      title: this.getFollowupTitle(),
      description: this.followupNotes,
      date: new Date().toLocaleString(),
      createdBy: 'Current User', // This would come from authentication service
      followupDate: this.followupDate,
      followupTime: this.followupTime,
      alternativeNumber: ''
    };

    // Add to the beginning of the activities array
    this.activities.unshift(newActivity);

    // Reset form
    this.followupNotes = '';
    this.showFollowupForm = false;

    // Reset date and time to defaults
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.followupDate = tomorrow.toISOString().split('T')[0];
    this.followupTime = '10:00';

    // Show success message
    alert('Follow-up added successfully!');
  }

  // Add a new contact
  addContact(): void {
    if (!this.contactName.trim() || !this.contactMobileNo.trim()) {
      alert('Please enter contact name and mobile number');
      return;
    }

    // Add contact to the people_information array
    const newContact = {
      id: `temp_${Date.now()}_${Math.random()}`,
      name: this.contactName.trim(),
      mobile: this.contactMobileNo.trim(),
      email: this.contactEmailId.trim() || '',
      is_primary_contact: this.contactIsPrimary,
      is_active: true,
      notes: this.contactConnectWith || '',
      role_id: null,
      sales_id: this.leadId
    };

    // Add to leadData people array for immediate UI update
    if (!this.leadData.people) {
      this.leadData.people = [];
    }

    // If this is set as primary, remove primary from others
    if (this.contactIsPrimary) {
      this.leadData.people.forEach(person => person.isPrimaryContact = false);
    }

    this.leadData.people.push({
      id: newContact.id,
      name: newContact.name,
      phone: newContact.mobile,
      email: newContact.email,
      role: newContact.notes,
      isPrimaryContact: newContact.is_primary_contact
    });

    // Also add to formData.people_information for API payload
    if (!this.leadData.formData.people_information) {
      this.leadData.formData.people_information = [];
    }
    this.leadData.formData.people_information.push(newContact);

    // Refresh contacts dropdown
    this.loadContacts();

    // Create activity log
    const newActivity: Activity = {
      id: this.activities.length + 1,
      type: 'contact',
      title: 'New Contact Added',
      description: `Contact: ${this.contactName}
      Connect With: ${this.contactConnectWith || 'N/A'}
      Mobile: ${this.contactMobileNo}
      Email: ${this.contactEmailId || 'N/A'}
      Primary Contact: ${this.contactIsPrimary ? 'Yes' : 'No'}`,
      date: new Date().toLocaleString(),
      createdBy: 'Current User',
    };

    this.activities.unshift(newActivity);

    // Reset form
    this.resetContactForm();
    this.showContactForm = false;

    console.log('✅ Contact added successfully:', newContact);
    alert('Contact added successfully!');
  }

  // Reset contact form
  resetContactForm(): void {
    this.contactConnectWith = '';
    this.contactName = '';
    this.contactMobileNo = '';
    this.contactEmailId = '';
    this.contactIsPrimary = false;
    this.editingContactIndex = -1;
  }

  // Edit existing contact
  editContact(index: number): void {
    if (this.leadData?.people && this.leadData.people[index]) {
      const contact = this.leadData.people[index];

      this.contactName = contact.name || '';
      this.contactMobileNo = contact.phone || '';
      this.contactEmailId = contact.email || '';
      this.contactConnectWith = contact.role || '';
      this.contactIsPrimary = contact.isPrimaryContact || false;
      this.editingContactIndex = index;
      this.showContactForm = true;

      console.log('📝 Editing contact:', contact);
    }
  }

  // Update existing contact
  updateContact(): void {
    if (this.editingContactIndex === -1 || !this.leadData?.people) {
      return;
    }

    if (!this.contactName.trim() || !this.contactMobileNo.trim()) {
      alert('Please enter contact name and mobile number');
      return;
    }

    // If this is set as primary, remove primary from others
    if (this.contactIsPrimary) {
      this.leadData.people.forEach((person, idx) => {
        if (idx !== this.editingContactIndex) {
          person.isPrimaryContact = false;
        }
      });
    }

    // Update the contact in people array
    this.leadData.people[this.editingContactIndex] = {
      ...this.leadData.people[this.editingContactIndex],
      name: this.contactName.trim(),
      phone: this.contactMobileNo.trim(),
      email: this.contactEmailId.trim() || '',
      role: this.contactConnectWith || '',
      isPrimaryContact: this.contactIsPrimary
    };

    // Update in formData.people_information as well
    if (this.leadData.formData.people_information && this.leadData.formData.people_information[this.editingContactIndex]) {
      this.leadData.formData.people_information[this.editingContactIndex] = {
        ...this.leadData.formData.people_information[this.editingContactIndex],
        name: this.contactName.trim(),
        mobile: this.contactMobileNo.trim(),
        email: this.contactEmailId.trim() || '',
        notes: this.contactConnectWith || '',
        is_primary_contact: this.contactIsPrimary
      };
    }

    // Refresh contacts dropdown
    this.loadContacts();

    // Create activity log
    const updateActivity: Activity = {
      id: this.activities.length + 1,
      type: 'contact',
      title: 'Contact Updated',
      description: `Updated Contact: ${this.contactName}
      Connect With: ${this.contactConnectWith || 'N/A'}
      Mobile: ${this.contactMobileNo}
      Email: ${this.contactEmailId || 'N/A'}
      Primary Contact: ${this.contactIsPrimary ? 'Yes' : 'No'}`,
      date: new Date().toLocaleString(),
      createdBy: 'Current User',
    };

    this.activities.unshift(updateActivity);

    // Reset form
    this.resetContactForm();
    this.showContactForm = false;

    console.log('✅ Contact updated successfully');
    alert('Contact updated successfully!');
  }

  // Delete contact
  deleteContact(index: number): void {
    if (!this.leadData?.people || index < 0 || index >= this.leadData.people.length) {
      return;
    }

    const contactToDelete = this.leadData.people[index];

    if (confirm(`Are you sure you want to delete contact "${contactToDelete.name}"?`)) {
      // Remove from people array
      this.leadData.people.splice(index, 1);

      // Remove from formData.people_information
      if (this.leadData.formData.people_information && this.leadData.formData.people_information[index]) {
        this.leadData.formData.people_information.splice(index, 1);
      }

      // Refresh contacts dropdown
      this.loadContacts();

      // Create activity log
      const deleteActivity: Activity = {
        id: this.activities.length + 1,
        type: 'contact',
        title: 'Contact Deleted',
        description: `Deleted Contact: ${contactToDelete.name}`,
        date: new Date().toLocaleString(),
        createdBy: 'Current User',
      };

      this.activities.unshift(deleteActivity);

      console.log('🗑️ Contact deleted successfully');
      alert('Contact deleted successfully!');
    }
  }

  // Get title based on follow-up type
  private getFollowupTitle(): string {
    const dateTimeStr = `${this.followupDate} at ${this.followupTime}`;
    switch (this.followupType) {
      case 'call': return `Phone call follow-up (${dateTimeStr})`;
      case 'meeting': return `Meeting scheduled (${dateTimeStr})`;
      case 'email': return `Email follow-up (${dateTimeStr})`;
      case 'site-visit': return `Site visit scheduled (${dateTimeStr})`;
      default: return `Follow-up added (${dateTimeStr})`;
    }
  }

  // Get badge class based on activity type
  getActivityBadgeClass(type: string): string {
    switch (type) {
      case 'call': return 'bg-primary';
      case 'meeting': return 'bg-warning';
      case 'email': return 'bg-info';
      case 'site-visit': return 'bg-success';
      case 'create': return 'bg-success';
      case 'handover': return 'bg-danger';
      case 'contact': return 'bg-info';
      case 'reject': return 'bg-danger';
      default: return 'bg-secondary';
    }
  }

  // Get icon based on activity type
  getActivityIcon(type: string): string {
    switch (type) {
      case 'call': return 'phone';
      case 'meeting': return 'users';
      case 'email': return 'mail';
      case 'site-visit': return 'map-pin';
      case 'create': return 'check-circle';
      case 'handover': return 'repeat';
      case 'contact': return 'user';
      case 'reject': return 'x-circle';
      default: return 'activity';
    }
  }



  // Submit lead action
  submitLeadAction(): void {
    // Validate if an option is selected
    if (!this.selectedLeadAction) {
      alert('Please select an action');
      return;
    }

    // Validate business call fields if business call is selected
    if (this.selectedLeadAction === 'businessCall') {
      if (!this.businessCallType) {
        alert('Please select a call type');
        return;
      }
      if (!this.businessCallDate) {
        alert('Please select next call date');
        return;
      }
      if (!this.businessCallTime) {
        alert('Please select call time');
        return;
      }
      if (!this.businessCallDescription.trim()) {
        alert('Please enter call description');
        return;
      }
    }

    // Validate handover instructions if handover is selected
    if (this.selectedLeadAction === 'handoverToOpsTeam' && !this.opsInstructions.trim()) {
      alert('Please enter instructions for the Ops Team');
      return;
    }

    // Create activity based on selected action
    let newActivity: Activity | null = null;

    switch (this.selectedLeadAction) {
      case 'businessCall':
        newActivity = {
          id: this.activities.length + 1,
          type: 'call',
          title: `Business Call - ${this.businessCallType}`,
          description: this.businessCallDescription,
          date: new Date().toLocaleString(),
          createdBy: 'Current User',
          followupDate: this.businessCallDate,
          followupTime: this.businessCallTime
        };
        break;

      case 'handoverToOpsTeam':
        newActivity = {
          id: this.activities.length + 1,
          type: 'handover',
          title: 'Handover to Ops Team',
          description: this.opsInstructions,
          date: new Date().toLocaleString(),
          createdBy: 'Current User'
        };
        break;

      case 'reject':
        newActivity = {
          id: this.activities.length + 1,
          type: 'reject',
          title: 'Lead Rejected',
          description: 'Lead has been rejected',
          date: new Date().toLocaleString(),
          createdBy: 'Current User'
        };
        break;
    }

    // Add the activity to the list if it was created
    if (newActivity) {
      this.activities.unshift(newActivity);
    }

    // Reset form
    this.resetLeadActionForm();

    // Show success message
    alert('Lead action submitted successfully!');
  }

  // Cancel lead action
  cancelLeadAction(): void {
    this.resetLeadActionForm();
  }

  // Reset lead action form
  private resetLeadActionForm(): void {
    this.selectedLeadAction = '';
    this.opsInstructions = '';
    this.businessCallType = '';
    this.businessCallDate = '';
    this.businessCallTime = '';
    this.businessCallDescription = '';
  }

  // Toggle edit mode
  toggleEditMode(): void {
    if (this.isEditMode) {
      // Cancel edit mode - reset form data and hide components
      this.isEditMode = false;
      this.editFormData = {};
      this.resetComponentVisibility();
    } else {
      // Check if product types are loaded before entering edit mode
      if (this.productTypes.length === 0) {
        console.log('⚠️ Product types not loaded yet, loading before entering edit mode...');
        this.loadProductTypes();
        // Wait for product types to load, then try again
        setTimeout(() => {
          if (this.productTypes.length > 0) {
            this.toggleEditMode();
          } else {
            alert('Please wait for data to load before editing.');
          }
        }, 1000);
        return;
      }

      // Enter edit mode - populate form with current data
      this.isEditMode = true;

      // Load contacts from people_information
      this.loadContacts();

      // Debug email field initialization
      const mainEmailValue = this.getMainEmailField();
      console.log('🔍 Edit Form Email Initialization:');
      console.log('📧 Main email value from getMainEmailField():', mainEmailValue);
      console.log('📧 Lead data emailId:', this.leadData?.emailId);
      console.log('📧 Primary contact email:', this.getPrimaryContactEmail());
      
      // Get lead category ID
      const leadCategoryId = this.getLeadCategoryId();
      
      // Debug log for lead category
      console.log('🔍 Lead Category for edit form:', {
        id: leadCategoryId,
        matchingCategory: this.leadCategories.find(cat => cat.id === leadCategoryId)?.name || 'Not found'
      });

      this.editFormData = {
        leadId: this.leadData?.leadId || '',
        leadName: this.leadData?.leadName || '',
        leadCategory: leadCategoryId, // Get the actual category ID for dropdown
        source: this.leadData?.source || '',
        sourceId: this.getSourceId(), // Get the actual source ID for dropdown
        companyName: this.leadData?.companyName || '',
        location: this.leadData?.location || '',
        locationId: '',
        subLocation: this.leadData?.subLocation || '',
        handoverTo: this.leadData?.handoverTo || '',
        handoverToId: '',
        productType: this.getProductTypeName(), // Use helper method to get proper name
        subProductType: this.getSubProductTypeName(), // Use helper method to get proper name
        projectName: this.leadData?.projectName || '',
        connectWithName: this.getProfessionTypeName(),
        connectWithId: this.getPrimaryContactConnectWithId(),
        connectWithMobile: this.getPrimaryContactMobile(),
        connectWithEmail: mainEmailValue,
        // Initialize IDs for API calls
        productTypeId: '',
        subProductTypeId: '',
        // Initialize Associate Category fields
        associateNameCategory: this.leadData?.formData?.associate_name || '',
        associateLocationCategory: this.leadData?.formData?.associate_location || '',
        associateSubLocationCategory: this.leadData?.formData?.associate_sub_location || '',
        professionalCategory: this.leadData?.formData?.professional_type || '',
        professionCategory: this.leadData?.formData?.profession || '',
        leadNameCategory: this.leadData?.formData?.lead_name || this.leadData?.leadName || '',
        leadLocationCategory: this.leadData?.formData?.lead_location || '',
        leadSubLocationCategory: this.leadData?.formData?.lead_sub_location || ''
      };

      console.log('✅ Edit form data initialized with email:', this.editFormData.connectWithEmail);

      // Find and set the location ID (prioritize name field matching)
      const selectedLocation = this.locations.find(loc =>
        loc.name === this.editFormData.location ||
        loc.display_name === this.editFormData.location
      );
      if (selectedLocation) {
        this.editFormData.locationId = selectedLocation.id;
        console.log('✅ Location ID mapped:', {
          locationName: this.editFormData.location,
          locationId: this.editFormData.locationId,
          selectedLocation: selectedLocation
        });
      }

      // Find and set the handover to ID
      const selectedEmployee = this.employees.find(emp =>
        emp.display_name === this.editFormData.handoverTo ||
        emp.name === this.editFormData.handoverTo
      );
      if (selectedEmployee) {
        this.editFormData.handoverToId = selectedEmployee.id;
      }

      // Find and set the connect with ID
      const selectedConnectWith = this.connectWithList.find(item =>
        item.name === this.editFormData.connectWithName
      );
      if (selectedConnectWith) {
        this.editFormData.connectWithId = selectedConnectWith.id;
      }

      // Find and set the product type ID
      let selectedProductType = this.productTypes.find(pt => pt.name === this.editFormData.productType);

      // If not found by name, try to find by ID (in case productType contains UUID)
      if (!selectedProductType && this.isValidUUID(this.leadData?.productType || '')) {
        selectedProductType = this.productTypes.find(pt => pt.id === this.leadData?.productType);
        if (selectedProductType) {
          // Update the form data with the correct name
          this.editFormData.productType = selectedProductType.name;
        } else {
          // Fallback to static mapping if dynamic data not available
          const productTypeMapping = {
            'f92a6dfa-3528-45ee-9a3a-4d3253d03813': 'Corporate Syndication',
            '1453aa41-bcc8-41a5-a46c-9ffc827d9be4': 'Retail Syndication',
            '82448c41-98c0-4382-abf5-88afb0bc3613': 'Property',
            '7dd37b35-0b3f-4643-adac-0ac566b6ad2b': 'Insurance'
          };
          const mappedName = productTypeMapping[this.leadData?.productType as keyof typeof productTypeMapping];
          if (mappedName) {
            this.editFormData.productType = mappedName;
            this.editFormData.productTypeId = this.leadData?.productType || '';
          }
        }
      }

      if (selectedProductType) {
        this.editFormData.productTypeId = selectedProductType.id;
        console.log('✅ Product type mapped:', {
          name: selectedProductType.name,
          id: selectedProductType.id,
          originalValue: this.leadData?.productType
        });

        // Load sub product types for the current product type
        this.loadSubProductTypes(selectedProductType.id);

        // Set sub product type ID after loading (with a small delay)
        setTimeout(() => {
          let selectedSubProductType = this.subProductTypes.find(spt => spt.name === this.editFormData.subProductType);

          // If not found by name, try to find by ID (in case subProductType contains UUID)
          if (!selectedSubProductType && this.isValidUUID(this.leadData?.subProductType || '')) {
            selectedSubProductType = this.subProductTypes.find(spt => spt.id === this.leadData?.subProductType);
            if (selectedSubProductType) {
              // Update the form data with the correct name
              this.editFormData.subProductType = selectedSubProductType.name;
            } else {
              // Fallback to static mapping if dynamic data not available
              const subProductTypeMapping = {
                // Corporate Syndication sub-types
                'bdd39bcf-8a14-49cb-8e71-d554a4b84a4b': 'Construction Funding(CF)',
                '07002e85-32cd-4018-8e87-596f74fbdce9': 'Inventory Funding(IF)',
                '9738264b-9a36-4d64-b5af-c8a31a3a8b42': 'Project Funding(PF)',
                'e0537f5e-597d-4052-ab87-e2a8eb7c57ff': 'Hospital Funding(HF)',
                'f4c6bd18-d760-4313-81e3-09d90f55fe6b': 'Education Funding(EF)',
                // Retail Syndication sub-types
                '6feaeea7-59fe-47a8-b967-3e02b1f208d7': 'Home Loan(HL)',
                '899008c6-6352-4338-894b-9c5305970f5b': 'Non Residential Property Loan(NRPL)',
                'c9c42d2a-69ef-42f0-8450-fdb3925a29af': 'Loan Against Property(LAP)',
                'a23fdba0-d565-45e1-aed6-0b233a89c612': 'Lease Rental Discounting(LRD)',
                // Property sub-types
                'ba1f7c8e-938e-4bb9-9d44-db878648630b': 'Property',
                // Insurance sub-types
                '97c00e7e-a05f-4939-8979-4656773d89c5': 'Contractor All Risk',
                'b5a1b463-e602-4baf-892b-c3275cc5bca2': 'Life Insurance'
              };
              const mappedName = subProductTypeMapping[this.leadData?.subProductType as keyof typeof subProductTypeMapping];
              if (mappedName) {
                this.editFormData.subProductType = mappedName;
                this.editFormData.subProductTypeId = this.leadData?.subProductType || '';
              }
            }
          }

          if (selectedSubProductType) {
            this.editFormData.subProductTypeId = selectedSubProductType.id;
            console.log('✅ Sub product type mapped:', {
              name: selectedSubProductType.name,
              id: selectedSubProductType.id,
              originalValue: this.leadData?.subProductType
            });
          }
        }, 500);
      } else {
        console.log('⚠️ Product type not found in loaded types:', {
          searchValue: this.editFormData.productType,
          originalValue: this.leadData?.productType,
          availableTypes: this.productTypes.map(pt => ({ id: pt.id, name: pt.name }))
        });
      }

      // Update component visibility based on current product type and sub-type
      this.updateComponentVisibility();

      // Pre-populate product form data after a short delay to ensure components are rendered
      setTimeout(() => {
        this.prePopulateProductFormData();
      }, 500);
    }
  }

  // Get available sub-product types based on selected product type
  getSubProductTypes(): { value: string; label: string }[] {
    // If we have dynamic data from API, use it
    if (this.subProductTypes.length > 0) {
      return this.subProductTypes.map(spt => ({
        value: spt.id,
        label: spt.name
      }));
    }

    // Fallback to hardcoded options
    const productType = this.editFormData.productType;
    return this.subProductTypeOptions[productType || ''] || [];
  }

  // Handle product type change
  onProductTypeChange(): void {
    console.log('🔄 Product type changed:', this.editFormData.productType);

    // Reset sub-product type when product type changes
    this.editFormData.subProductType = '';
    this.editFormData.subProductTypeId = '';

    // Find the selected product type ID
    const selectedProductType = this.productTypes.find(pt => pt.name === this.editFormData.productType);
    if (selectedProductType) {
      this.editFormData.productTypeId = selectedProductType.id;
      // Load sub product types for the selected product type
      this.loadSubProductTypes(selectedProductType.id);
    } else {
      // Fallback to hardcoded options if API data not available
      const subProductTypes = this.getSubProductTypes();
      if (subProductTypes.length > 0) {
        this.editFormData.subProductType = subProductTypes[0].value;
      }
    }

    // Update component visibility
    this.updateComponentVisibility();
  }

  /**
   * Capture current product form data from the active component
   */
  captureProductFormData(): any {
    console.log('🔧 Capturing product form data from active component...');

    try {
      let productFormData = null;

      if (this.showProductOne && this.productOneComponent) {
        console.log('🔧 Capturing data from ProductOne component');
        if (this.productOneComponent.getFormData) {
          productFormData = this.productOneComponent.getFormData();
        } else {
          console.warn('⚠️ ProductOne getFormData method not available');
        }
      } else if (this.showProductTwo && this.productTwoComponent) {
        console.log('🔧 Capturing data from ProductTwo component');
        if (this.productTwoComponent.getFormData) {
          productFormData = this.productTwoComponent.getFormData();
        } else {
          console.warn('⚠️ ProductTwo getFormData method not available');
        }
      } else if (this.showCar && this.carComponent) {
        console.log('🔧 Capturing data from Car component');
        if (this.carComponent.getFormData) {
          productFormData = this.carComponent.getFormData();
        } else {
          console.warn('⚠️ Car getFormData method not available');
        }
      } else if (this.showLife && this.lifeComponent) {
        console.log('🔧 Capturing data from Life component');
        if (this.lifeComponent.getFormData) {
          productFormData = this.lifeComponent.getFormData();
        } else {
          console.warn('⚠️ Life getFormData method not available');
        }
      } else if (this.showProperty && this.propertyComponent) {
        console.log('🔧 Capturing data from Property component');
        if (this.propertyComponent.getFormData) {
          productFormData = this.propertyComponent.getFormData();
        } else {
          console.warn('⚠️ Property getFormData method not available');
        }
      } else {
        console.log('ℹ️ No product form component is currently active');
      }

      console.log('✅ Captured product form data:', productFormData);
      return productFormData;
    } catch (error) {
      console.error('❌ Error capturing product form data:', error);
      return null;
    }
  }

  /**
   * Prepare updated people_information array from ProductOne component
   * Only deals with people_information array, ignoring people_details
   */
  prepareUpdatedPeopleInformation(): ExtendedPeopleInformation[] {
    console.log('🔧 Preparing updated people_information from ProductOne component...');

    // Get current people data directly from ProductOne component if it's active
    let updatedPeopleData: any[] = [];

    if (this.showProductOne && this.productOneComponent && this.productOneComponent.people) {
      console.log('🔧 Getting people data from ProductOne component:', this.productOneComponent.people);

      // Convert ProductOne people format to API people_information format
      updatedPeopleData = this.productOneComponent.people
        .filter((person: any) => {
          // Only include people with actual data
          return person.name?.trim() || person.mobile?.trim();
        })
        .map((person: any, index: number) => {
          // Try to find matching existing person to preserve ID and other API fields
          const existingPerson = this.leadData?.people?.find((existing: any) => {
            return (person.name && existing.name === person.name) ||
                   (person.mobile && existing.mobile === person.mobile);
          });

          return {
            // Preserve existing ID if found, otherwise let API assign new ID
            id: existingPerson?.id || undefined,
            name: person.name?.trim() || '',
            mobile: person.mobile?.trim() || '',
            email: person.email?.trim() || '',
            notes: person.connectWith?.trim() || '',
            is_primary_contact: existingPerson?.is_primary_contact || (index === 0),
            is_active: existingPerson?.is_active !== undefined ? existingPerson.is_active : true,
            role_id: existingPerson?.role_id || null,
            sales_id: existingPerson?.sales_id || this.leadId,
            created_at: existingPerson?.created_at,
            updated_at: existingPerson?.updated_at
          };
        });

      console.log('✅ Converted ProductOne people to people_information format:', updatedPeopleData);
    } else {
      // Fallback to existing people data if ProductOne component is not available
      console.log('ℹ️ ProductOne component not available, using existing people data');
      updatedPeopleData = this.leadData?.people || [];
    }

    // Convert to final API format
    let updatedPeopleInfo = updatedPeopleData.map((person: any) => {
      return {
        id: person.id,
        name: person.name || '',
        mobile: person.mobile || '',
        email: person.email || '',
        is_primary_contact: Boolean(person.is_primary_contact),
        is_active: Boolean(person.is_active),
        notes: person.notes || '',
        role_id: person.role_id || null,
        connect_with_id: person.connect_with_id || null, // Include connect_with_id for updates
        sales_id: person.sales_id || this.leadId
      };
    });

    console.log('📋 Converted people_information for API:', updatedPeopleInfo);

    // Note: Contact information is now managed entirely through the ProductOne component
    // No need for separate Connect With Name field processing

    console.log('✅ Final people_information with API format:', updatedPeopleInfo);
    console.log('📊 People information summary:', {
      totalContacts: updatedPeopleInfo.length,
      primaryContacts: updatedPeopleInfo.filter((p: any) => p.is_primary_contact).length,
      activeContacts: updatedPeopleInfo.filter((p: any) => p.is_active).length,
      contactsWithEmail: updatedPeopleInfo.filter((p: any) => p.email && p.email.trim() !== '').length,
      contactsWithMobile: updatedPeopleInfo.filter((p: any) => p.mobile && p.mobile.trim() !== '').length
    });

    return updatedPeopleInfo;
  }

  /**
   * Validate people_information array before sending to API
   * Only validates actual data without creating defaults
   */
  private validatePeopleInformation(peopleInfo: ExtendedPeopleInformation[]): boolean {
    console.log('🔍 Validating people_information structure (real data only)...');

    // Empty array is valid - no people_information to send
    if (!peopleInfo || peopleInfo.length === 0) {
      console.log('✅ Empty people_information array - validation passed');
      return true;
    }

    let isValid = true;
    const validationErrors: string[] = [];

    peopleInfo.forEach((person, index) => {
      // Check required fields for real contacts only
      if (!person.name || person.name.trim() === '') {
        validationErrors.push(`Contact ${index + 1}: Missing name`);
        isValid = false;
      }

      if (!person.mobile || person.mobile.trim() === '') {
        validationErrors.push(`Contact ${index + 1}: Missing mobile number`);
        isValid = false;
      }

      // Check field types
      if (typeof person.is_primary_contact !== 'boolean') {
        validationErrors.push(`Contact ${index + 1}: is_primary_contact must be boolean`);
        isValid = false;
      }

      if (typeof person.is_active !== 'boolean') {
        validationErrors.push(`Contact ${index + 1}: is_active must be boolean`);
        isValid = false;
      }

      // Check UUID format for id field if present (skip validation for new contacts without ID)
      if (person.id && person.id !== undefined && !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(person.id)) {
        validationErrors.push(`Contact ${index + 1}: Invalid UUID format for id`);
        isValid = false;
      }

      // Validate that this is real contact data (has actual content)
      if (!person.name && !person.mobile && !person.email) {
        validationErrors.push(`Contact ${index + 1}: Empty contact data - should not be included`);
        isValid = false;
      }
    });

    if (!isValid) {
      console.error('❌ People information validation failed:', validationErrors);
    } else {
      console.log('✅ People information validation passed');
    }

    return isValid;
  }

  // Save changes
  saveChanges(): void {
    if (!this.leadData || this.isUpdating) {
      return;
    }
    


    this.isUpdating = true;
    console.log('💾 Saving lead changes...', this.editFormData);

    // Prepare updated people_information array
    const updatedPeopleInformation = this.prepareUpdatedPeopleInformation();

    console.log('🔍 DEBUG: Prepared people_information for API:', {
      count: updatedPeopleInformation.length,
      data: updatedPeopleInformation,
      isEmpty: updatedPeopleInformation.length === 0
    });

    // Validate people_information structure before proceeding
    if (!this.validatePeopleInformation(updatedPeopleInformation)) {
      console.error('❌ People information validation failed, aborting save');
      this.isUpdating = false;
      alert('Error: Invalid contact information format. Please check the contact details and try again.');
      return;
    }

    // Capture current product form data
    const currentProductFormData = this.captureProductFormData();

    // Get selected location for complete location data
    const selectedLocation = this.locations.find(loc => loc.id === this.editFormData.locationId);

    // Determine the location_id to send (UUID)
    let locationIdToSend = '';

    if (this.editFormData.locationId && selectedLocation) {
      // User selected a new location from dropdown - use the selected location ID
      locationIdToSend = this.editFormData.locationId;
      console.log('🔄 Using selected location ID:', {
        locationId: this.editFormData.locationId,
        selectedLocation: selectedLocation,
        locationIdToSend: locationIdToSend
      });
    } else if (this.leadData?.locationObject?.id) {
      // No new location selected, use original location ID
      locationIdToSend = this.leadData.locationObject.id;
      console.log('🔄 Using original location ID:', locationIdToSend);
    }

    console.log('📍 Final location_id to send in payload:', locationIdToSend);

    // Prepare the complete update request data with ALL lead details fields
    const updateData: Partial<SalesCreateRequest> = {
      // Basic lead information
      lead_name: this.isLeadCategorySelected('Associate') ? this.editFormData.leadNameCategory : this.editFormData.leadName || '',
      company: this.editFormData.companyName || '',
      location_id: locationIdToSend, // Send the location_id (UUID) directly
      sub_location: this.editFormData.subLocation || '',

      // Main email field (top-level API field)
      email: this.editFormData.connectWithEmail || '',

      // Include people_information in the payload
      people_information: updatedPeopleInformation,

      // Map form data to API format with all fields
      form_data: {
        ...this.leadData.formData,
        // Include project name in form_data since it's not a direct API field
        project_name: this.editFormData.projectName || '',
        source_details: {
          ...this.leadData.formData?.source_details,
          category: this.editFormData.leadCategory || '',
          source: this.editFormData.source || '',
        },
        // Include contact information in form_data as well for compatibility
        contact_information: {
          name: this.editFormData.connectWithName || '',
          mobile: this.editFormData.connectWithMobile || '',
          email: this.editFormData.connectWithEmail || '',
        },
        // Include Associate Category fields if Lead Category is Associate
        ...(this.isLeadCategorySelected('Associate') ? {
          associate_name: this.editFormData.associateNameCategory || '',
          associate_location: this.editFormData.associateLocationCategory || '',
          associate_sub_location: this.editFormData.associateSubLocationCategory || '',
          professional_type: this.editFormData.professionalCategory || '',
          profession: this.editFormData.professionCategory || '',
          lead_name: this.editFormData.leadNameCategory || '',
          lead_location: this.editFormData.leadLocationCategory || '',
          lead_sub_location: this.editFormData.leadSubLocationCategory || ''
        } : {}),
        // IMPORTANT: Include updated people_information in form_data (API source of truth)
        people_information: updatedPeopleInformation,
        // Include captured product form data
        product_form_data: currentProductFormData || this.leadData.formData?.product_form_data || null
      }
    };

    // Add handover to ID if selected
    if (this.editFormData.handoverToId) {
      updateData.handover_to_id = this.editFormData.handoverToId;
    }

    // Add product type and sub product type IDs if selected
    if (this.editFormData.productTypeId) {
      updateData.product_type_id = this.editFormData.productTypeId;
    }
    if (this.editFormData.subProductTypeId) {
      updateData.sub_product_type_id = this.editFormData.subProductTypeId;
    }

    // If product type IDs are not set but we have product type names, try to derive the IDs
    if (!updateData.product_type_id && this.editFormData.productType) {
      const productType = this.productTypes.find(pt => pt.name === this.editFormData.productType);
      if (productType) {
        updateData.product_type_id = productType.id;
        console.log('🔄 Derived product_type_id from name:', {
          name: this.editFormData.productType,
          id: productType.id
        });
      }
    }

    if (!updateData.sub_product_type_id && this.editFormData.subProductType) {
      const subProductType = this.subProductTypes.find(spt => spt.name === this.editFormData.subProductType);
      if (subProductType) {
        updateData.sub_product_type_id = subProductType.id;
        console.log('🔄 Derived sub_product_type_id from name:', {
          name: this.editFormData.subProductType,
          id: subProductType.id
        });
      }
    }

    // If product type IDs are not set but we have product type names, try to derive the IDs
    if (!updateData.product_type_id && this.editFormData.productType) {
      const productType = this.productTypes.find(pt => pt.name === this.editFormData.productType);
      if (productType) {
        updateData.product_type_id = productType.id;
        console.log('🔄 Derived product_type_id from name:', {
          name: this.editFormData.productType,
          id: productType.id
        });
      }
    }

    if (!updateData.sub_product_type_id && this.editFormData.subProductType) {
      const subProductType = this.subProductTypes.find(spt => spt.name === this.editFormData.subProductType);
      if (subProductType) {
        updateData.sub_product_type_id = subProductType.id;
        console.log('🔄 Derived sub_product_type_id from name:', {
          name: this.editFormData.subProductType,
          id: subProductType.id
        });
      }
    }

    // Add connect with ID if selected (for Connect With Name field)
    if (this.editFormData.connectWithId) {
      (updateData as any).connect_with_id = this.editFormData.connectWithId;
    }

    // Comprehensive payload validation and logging
    console.log('📤 Complete update payload validation:');
    console.log('🔍 Basic fields:', {
      lead_name: updateData.lead_name,
      company: updateData.company,
      location: updateData.location,
      sub_location: updateData.sub_location,
      email: updateData.email // Main email field
    });
    console.log('🔍 Contact fields:', {
      connectWithName: this.editFormData.connectWithName,
      connectWithMobile: this.editFormData.connectWithMobile,
      connectWithEmail: this.editFormData.connectWithEmail,
      connectWithId: this.editFormData.connectWithId
    });
    console.log('🔍 People information (API format):', updatedPeopleInformation);
    console.log('🔍 People information in payload structure:', {
      rootLevel: !!updateData.people_information,
      formDataLevel: !!updateData.form_data?.people_information,
      rootLevelCount: updateData.people_information?.length || 0,
      formDataLevelCount: updateData.form_data?.people_information?.length || 0,
      bothMatch: JSON.stringify(updateData.people_information) === JSON.stringify(updateData.form_data?.people_information)
    });
    console.log('🔍 People information structure validation:', {
      totalContacts: updatedPeopleInformation.length,
      hasRequiredFields: updatedPeopleInformation.every(p => p.name && p.mobile),
      primaryContacts: updatedPeopleInformation.filter(p => p.is_primary_contact).length,
      activeContacts: updatedPeopleInformation.filter(p => p.is_active).length
    });
    console.log('🔍 Form data contact info:', updateData.form_data?.contact_information);
    console.log('🔍 Product form data:', {
      captured: currentProductFormData,
      existing: this.leadData.formData?.product_form_data,
      final: updateData.form_data?.product_form_data
    });
    console.log('🔍 Product fields:', {
      product_type_id: updateData.product_type_id,
      sub_product_type_id: updateData.sub_product_type_id,
      handover_to_id: updateData.handover_to_id,
      profession_type_id: (updateData as any).profession_type_id
    });
    console.log('📍 Location fields:', {
      selectedLocationId: this.editFormData.locationId,
      selectedLocationName: selectedLocation?.name,
      selectedLocationDisplayName: selectedLocation?.display_name,
      payloadLocationId: (updateData as any).location_id,
      editFormLocation: this.editFormData.location
    });
    console.log('�📤 Complete update request payload:', updateData);
    console.log('🔍 DEBUG: people_information in final payload:', {
      peopleInfo: updateData.people_information,
      count: updateData.people_information?.length || 0,
      isArray: Array.isArray(updateData.people_information)
    });

    // Validate that main email field is included
    if (updateData.email) {
      console.log('✅ Main email field validation passed:', updateData.email);
    } else {
      console.log('⚠️ Main email field is empty or undefined');
    }

    // Validate that contact email field is included
    if (this.editFormData.connectWithEmail) {
      console.log('✅ Contact email field validation passed:', this.editFormData.connectWithEmail);
    } else {
      console.log('⚠️ Contact email field is empty or undefined');
    }

    // Debug: Log the final update data being sent to API
    console.log('💾 Sending update request with data:', updateData);
    console.log('🔍 DETAILED DEBUG - People Information in Update Data:', {
      hasPeopleInformation: !!updateData.people_information,
      peopleInformationCount: updateData.people_information?.length || 0,
      peopleInformationContent: updateData.people_information
    });

    // Call the update API
    this.salesService.updateSales(this.leadId, updateData).subscribe({
      next: (response) => {
        console.log('✅ Lead updated successfully:', response);
        this.isUpdating = false;

        if (response.success && response.data) {
          console.log('🔄 Processing API response after save:', response.data);
          console.log('🔍 Root-level people_information in response:', response.data.people_information);
          console.log('🔍 Form-data people_information in response:', response.data.form_data?.people_information);

          // Check if API propagated form_data.people_information to root level
          const formDataPeople = response.data.form_data?.people_information;
          const rootLevelPeople = response.data.people_information;

          if (formDataPeople && formDataPeople.length > 0) {
            // If form_data has updated people but root level doesn't match, sync them
            const formDataUpdated = JSON.stringify(formDataPeople);
            const rootLevelUpdated = JSON.stringify(rootLevelPeople);

            if (formDataUpdated !== rootLevelUpdated) {
              console.log('⚠️ API did not propagate form_data.people_information to root level, syncing manually...');
              response.data.people_information = formDataPeople;
              console.log('✅ Manually synced root-level people_information with form_data.people_information');
            } else {
              console.log('✅ API properly propagated people_information from form_data to root level');
            }
          }

          // Instantly update people information first for immediate UI feedback
          this.updatePeopleInformationInstantly(response.data);

          // Instantly update location information for immediate UI feedback
          this.updateLocationInstantly(response.data);

          // Update local data with response
          this.leadData = this.mapSalesResponseToExtendedData(response.data);

          // Force immediate UI update for People Information section
          this.refreshPeopleInformationDisplay();

          // Re-populate ProductOne component with all updated data (people, RERA, etc.)
          setTimeout(() => {
            this.prePopulateProductFormData();

            // Explicitly update RERA component after a short delay to ensure it's initialized
            setTimeout(() => {
              this.updateReraComponent();
            }, 100);

            console.log('✅ ProductOne component re-populated with all updated data including RERA details');
          }, 100);

          // Exit edit mode
          this.isEditMode = false;
          this.editFormData = {};

          // Show success message
          alert('Lead details updated successfully!');

          // Update the product data service
          this.updateProductDataService();
        } else {
          console.error('❌ Update response indicates failure:', response);
          alert('Failed to update lead details. Please try again.');
        }
      },
      error: (error) => {
        console.error('❌ Failed to update lead:', error);
        this.isUpdating = false;

        // Show error message directly from backend
        const errorMessage = error.error?.error || error.message || 'Failed to update lead details';
        alert(`Error: ${errorMessage}`);
      }
    });
  }

  // Handle sub-product type change
  onSubProductTypeChange(): void {
    console.log('🔄 Sub product type changed:', this.editFormData.subProductType);

    // Find the selected sub product type ID
    const selectedSubProductType = this.subProductTypes.find(spt => spt.name === this.editFormData.subProductType);
    if (selectedSubProductType) {
      this.editFormData.subProductTypeId = selectedSubProductType.id;
    }

    this.updateComponentVisibility();

    // Force change detection to update the display
    this.cdr.detectChanges();
  }

  // Handle sub-product type selection by ID (for dynamic dropdown)
  onSubProductTypeIdChange(): void {
    console.log('🔄 Sub product type ID changed:', this.editFormData.subProductTypeId);

    // Find the selected sub product type name
    const selectedSubProductType = this.subProductTypes.find(spt => spt.id === this.editFormData.subProductTypeId);
    if (selectedSubProductType) {
      this.editFormData.subProductType = selectedSubProductType.name;
    }

    this.updateComponentVisibility();

    // Force change detection to update the display
    this.cdr.detectChanges();
  }

  /**
   * Pre-populate product form data when entering edit mode
   */
  prePopulateProductFormData(): void {
    if (!this.leadData?.productFormData) {
      console.log('ℹ️ No product form data available for pre-population');
      return;
    }

    const productFormData = this.leadData.productFormData;
    console.log('🔧 Pre-populating product form data:', productFormData);
    console.log('🔧 Product form data structure:', {
      hasComponentType: !!productFormData?.component_type,
      hasProductType: !!productFormData?.product_type,
      hasNestedProductFormData: !!productFormData?.product_form_data,
      componentType: productFormData?.component_type,
      productType: productFormData?.product_type
    });

    try {
      // Pre-populate based on which component is currently visible
      if (this.showProductOne && this.productOneComponent) {
        console.log('🔧 Pre-populating ProductOne component');
        if (this.productOneComponent.product1Form && productFormData) {
          this.productOneComponent.product1Form.patchValue(productFormData);

          // Handle RERA form visibility and data
          if (productFormData.rera === 'Yes') {
            this.productOneComponent.showReraForm = true;
            this.productOneComponent.reraFormData = productFormData.rera_details || {};

            // Update RERA component form if it exists
            setTimeout(() => {
              if (this.productOneComponent.reraComponent && productFormData.rera_details) {
                console.log('🔧 Populating RERA component with saved data:', productFormData.rera_details);
                this.productOneComponent.reraComponent.reraForm.patchValue(productFormData.rera_details);
                console.log('✅ RERA component form populated successfully');
              }
            }, 50);
          } else {
            this.productOneComponent.showReraForm = false;
            this.productOneComponent.reraFormData = {};
          }
        }

        // Pre-populate people information from root-level people_information
        this.prePopulateProductOnePeople();
      } else if (this.showProductTwo && this.productTwoComponent) {
        console.log('🔧 Pre-populating ProductTwo component');
        if (this.productTwoComponent.populateFormData && productFormData) {
          // Use the new populateFormData method for comprehensive data population
          this.productTwoComponent.populateFormData(productFormData);
        } else if (this.productTwoComponent.product2Form && productFormData) {
          // Fallback to basic form patching if populateFormData is not available
          this.productTwoComponent.product2Form.patchValue(productFormData);
          // Handle loan form if available
          if (this.productTwoComponent.loanForm && productFormData.loan_details) {
            this.productTwoComponent.loanForm.patchValue(productFormData.loan_details);
          }
        }

        // Pre-populate people information for ProductTwo as well
        this.prePopulateProductTwoPeople();
      } else if (this.showCar && this.carComponent) {
        console.log('🔧 Pre-populating Car component');

        // Extract the actual form data from nested structure
        let carFormData = null;
        if (productFormData?.product_form_data) {
          carFormData = productFormData.product_form_data;
        } else if (productFormData) {
          carFormData = productFormData;
        }

        console.log('🔧 Car form data to populate:', carFormData);

        if (carFormData && this.carComponent.populateFormData) {
          this.carComponent.populateFormData(carFormData);
        }
        // Pre-populate people information for Car component
        this.prePopulateCarPeople();
      } else if (this.showLife && this.lifeComponent) {
        console.log('🔧 Pre-populating Life component');

        // Extract the actual form data from nested structure
        let lifeFormData = null;
        if (productFormData?.product_form_data) {
          lifeFormData = productFormData.product_form_data;
        } else if (productFormData) {
          lifeFormData = productFormData;
        }

        console.log('🔧 Life form data to populate:', lifeFormData);

        if (lifeFormData && this.lifeComponent.populateFormData) {
          this.lifeComponent.populateFormData(lifeFormData);
        }
        // Pre-populate people information for Life component
        this.prePopulateLifePeople();
      } else if (this.showProperty && this.propertyComponent) {
        console.log('🔧 Pre-populating Property component');

        // Extract the actual form data from nested structure
        let propertyFormData = null;
        let customersData = null;

        if (productFormData?.product_form_data) {
          propertyFormData = productFormData.product_form_data;
          // Customers array is at the same level as product_form_data
          customersData = productFormData.customers;
        } else if (productFormData) {
          propertyFormData = productFormData;
          customersData = productFormData.customers;
        }

        console.log('🔧 Property form data to populate:', propertyFormData);
        console.log('🔧 Property customers data to populate:', customersData);

        if (propertyFormData && this.propertyComponent.populateFormData) {
          // Create combined data object with both form data and customers
          const combinedData = {
            ...propertyFormData,
            customers: customersData || []
          };
          this.propertyComponent.populateFormData(combinedData);
        }
        // Pre-populate people information for Property component (only if no customers data exists)
        if (!customersData || customersData.length === 0) {
          this.prePopulatePropertyPeople();
        }
      }

      console.log('✅ Product form data pre-population completed');
    } catch (error) {
      console.error('❌ Error pre-populating product form data:', error);
    }
  }

  // Update component visibility based on product type and sub-product type
  updateComponentVisibility(): void {
    // Hide all components first
    this.resetComponentVisibility();

    const productType = this.editFormData.productType;
    const subProductType = this.editFormData.subProductType;

    console.log('🔄 Updating component visibility for:', { productType, subProductType });

    if (!productType || !subProductType) {
      console.log('⚠️ Product type or sub-product type not selected, hiding all components');
      return;
    }

    // Convert to lowercase for case-insensitive matching
    const subProductTypeName = subProductType.toLowerCase();

    // Corporate Syndication - Show ProductOne component
    if (productType === 'Corporate Syndication') {
      // Check for Corporate Syndication sub-types using flexible matching
      const corporateSubTypes = ['cf', 'if', 'pf', 'hf', 'ef'];

      if (subProductTypeName.includes('construction') ||
          subProductTypeName.includes('inventory') ||
          subProductTypeName.includes('project') ||
          subProductTypeName.includes('hospital') ||
          subProductTypeName.includes('education') ||
          corporateSubTypes.some(type => subProductTypeName.includes(type))) {
        this.showProductOne = true;
        console.log('✅ Showing ProductOne component for Corporate Syndication');
        console.log('  🎯 Matched sub-type:', subProductType);
      } else {
        console.warn('⚠️ Corporate Syndication sub-type not recognized:', subProductType);
      }
    }

    // Retail Syndication - Show ProductTwo component
    else if (productType === 'Retail Syndication') {
      // Check for Retail Syndication sub-types using flexible matching
      const retailSubTypes = ['hl', 'lap', 'lrd', 'nrpl'];

      if (subProductTypeName.includes('home') ||
          subProductTypeName.includes('property') ||
          subProductTypeName.includes('lease') ||
          subProductTypeName.includes('residential') ||
          retailSubTypes.some(type => subProductTypeName.includes(type))) {
        this.showProductTwo = true;
        console.log('✅ Showing ProductTwo component for Retail Syndication');
        console.log('  🎯 Matched sub-type:', subProductType);
      } else {
        console.warn('⚠️ Retail Syndication sub-type not recognized:', subProductType);
      }
    }

    // Insurance - Show Car or Life components
    else if (productType === 'Insurance') {
      if (subProductTypeName.includes('car') ||
          subProductTypeName.includes('contractor') ||
          subProductTypeName.includes('risk')) {
        this.showCar = true;
        console.log('✅ Showing Car component for Insurance');
        console.log('  🎯 Matched sub-type:', subProductType);
      } else if (subProductTypeName.includes('life') ||
                 subProductTypeName.includes('li')) {
        this.showLife = true;
        console.log('✅ Showing Life component for Insurance');
        console.log('  🎯 Matched sub-type:', subProductType);
      } else {
        console.warn('⚠️ Insurance sub-type not recognized:', subProductType);
      }
    }

    // Property - Show Property component
    else if (productType === 'Property') {
      this.showProperty = true;
      console.log('✅ Showing Property component');
    }

    // Update the product data service
    this.updateProductDataService();

    // Pre-populate product form data after component visibility changes
    setTimeout(() => {
      this.prePopulateProductFormData();
    }, 100);
  }

  // Reset component visibility
  resetComponentVisibility(): void {
    this.showProductOne = false;
    this.showProductTwo = false;
    this.showCar = false;
    this.showLife = false;
    this.showProperty = false;
  }

  // Update product data service
  updateProductDataService(): void {
    if (this.editFormData.productType && this.editFormData.subProductType) {
      // Map the product type to the format expected by the service
      let serviceProductType = '';
      switch (this.editFormData.productType) {
        case 'Corporate Syndication':
          serviceProductType = 'Corporate_Syndication';
          break;
        case 'Retail Syndication':
          serviceProductType = 'Retail_Syndication';
          break;
        case 'Property':
          serviceProductType = 'Property';
          break;
        case 'Insurance':
          serviceProductType = 'Insurance';
          break;
      }

      // Extract short form from sub-product type (e.g., "HL" from "Home Loan(HL)")
      const subProductTypeShortForm = this.extractSubProductTypeShortForm(this.editFormData.subProductType);

      this.productDataService.setProductType(serviceProductType);
      this.productDataService.setProductSubType(subProductTypeShortForm);

      console.log('🔧 Updated ProductDataService:', {
        productType: serviceProductType,
        subProductType: subProductTypeShortForm,
        originalSubProductType: this.editFormData.subProductType
      });
    }
  }

  // Helper method to extract short form from sub-product type name
  private extractSubProductTypeShortForm(subProductTypeName: string): string {
    if (!subProductTypeName) return '';

    // Try to extract content within parentheses (e.g., "HL" from "Home Loan(HL)")
    const match = subProductTypeName.match(/\(([^)]+)\)/);
    if (match && match[1]) {
      return match[1];
    }

    // Fallback: return the original name if no parentheses found
    return subProductTypeName;
  }

  /**
   * Get the display name for the current sub-product type
   * This method returns the full name for display purposes
   */
  getSubProductTypeDisplayName(): string {
    // In edit mode, use the edit form data
    if (this.isEditMode && this.editFormData.subProductType) {
      return this.editFormData.subProductType;
    }

    // In view mode, use the helper method that handles UUID mapping
    return this.getSubProductTypeName();
  }

  /**
   * Check if sub-product type should be displayed
   */
  shouldShowSubProductType(): boolean {
    return !!(this.getSubProductTypeDisplayName());
  }

  // Helper methods to get primary contact information
  getPrimaryContactName(): string {
    if (this.leadData?.people && this.leadData.people.length > 0) {
      const primaryContact = this.leadData.people.find(person => person.is_primary_contact);
      const name = primaryContact?.name !== undefined ? primaryContact.name :
                  (this.leadData.people[0]?.name !== undefined ? this.leadData.people[0].name : 'N/A');
      return name;
    }
    return 'N/A';
  }

  /**
   * Get the connect_with_id from the primary contact in people information
   * Used for initializing the edit form with the correct Connect With ID
   */
  getPrimaryContactConnectWithId(): string {
    if (this.leadData?.people && this.leadData.people.length > 0) {
      const primaryContact = this.leadData.people.find(person => person.is_primary_contact);
      const connectWithId = primaryContact?.connect_with_id;

      console.log('🔍 getPrimaryContactConnectWithId:', {
        primaryContact: primaryContact?.name,
        connectWithId: connectWithId
      });

      return connectWithId || '';
    }

    // Fallback to root level connect_with_id (legacy support)
    return this.leadData?.connectWithId || '';
  }

  /**
   * Get the profession type name from API response (for Connect With Name field)
   * Maps connect_with_id UUID to Connect With name when available
   * Looks for connect_with_id in the primary contact's people information
   */
  getProfessionTypeName(): string {
    console.log('🔍 getProfessionTypeName called with:', {
      professionTypeName: this.leadData?.professionTypeName,
      connectWithId: this.leadData?.connectWithId,
      peopleCount: this.leadData?.people?.length || 0,
      availableConnectWithOptions: this.connectWithList.length
    });

    // First try to get connect_with_id from primary contact in people information
    if (this.leadData?.people && this.leadData.people.length > 0) {
      const primaryContact = this.leadData.people.find(person => person.is_primary_contact);
      const connectWithId = primaryContact?.connect_with_id;

      console.log('🔍 Primary contact connect_with_id:', {
        primaryContact: primaryContact?.name,
        connectWithId: connectWithId,
        hasPrimaryContact: !!primaryContact
      });

      if (connectWithId && this.connectWithList.length > 0) {
        const connectWithOption = this.connectWithList.find(cw => cw.id === connectWithId);
        if (connectWithOption) {
          console.log('✅ Mapped primary contact connect_with_id to name:', {
            uuid: connectWithId,
            mappedName: connectWithOption.name,
            primaryContactName: primaryContact?.name
          });
          return connectWithOption.name;
        }
      }
    }

    // Fallback: try root level connect_with_id (legacy support)
    if (this.leadData?.connectWithId && this.connectWithList.length > 0) {
      const connectWithOption = this.connectWithList.find(cw => cw.id === this.leadData.connectWithId);
      if (connectWithOption) {
        console.log('✅ Mapped root-level connect_with_id to name:', {
          uuid: this.leadData.connectWithId,
          mappedName: connectWithOption.name
        });
        return connectWithOption.name;
      }
    }

    // Final fallback to profession_type_name from API response
    const fallbackName = this.leadData?.professionTypeName || 'N/A';
    console.log('📋 Using fallback profession type name:', fallbackName);
    return fallbackName;
  }

  /**
   * Get source display name based on lead category
   * If lead_category_id is "Associate" (402c454c-b9c6-4419-a969-e440e7233239), display "Associate"
   * If lead_category_id is "Lead Data" (c8ee56a9-3298-49fb-aefe-71e18c71fde7), fetch source name from sources API
   */
  private getSourceDisplayName(salesData: any): string {
    const leadCategoryId = salesData.lead_category_id;
    const sourceId = salesData.source_id;

    console.log('🔍 Getting source display name for lead details:', {
      lead_category_id: leadCategoryId,
      source_id: sourceId,
      form_data_source: salesData.form_data?.source_details?.source,
      source_name: salesData.source_name
    });

    // Lead Category: Associate (402c454c-b9c6-4419-a969-e440e7233239)
    if (leadCategoryId === '402c454c-b9c6-4419-a969-e440e7233239') {
      console.log('✅ Lead Category is Associate, returning "Associate"');
      return 'Associate';
    }

    // Lead Category: Lead Data (c8ee56a9-3298-49fb-aefe-71e18c71fde7)
    if (leadCategoryId === 'c8ee56a9-3298-49fb-aefe-71e18c71fde7') {
      console.log('🔍 Lead Category is Lead Data, looking up source name');

      // Try to get source name from loaded sources data
      if (sourceId && this.sources.length > 0) {
        const source = this.sources.find(s => s.id === sourceId);
        if (source) {
          console.log('✅ Found source name from API data:', source.name);
          return source.name;
        }
      }

      // Fallback to form_data source or source_name
      if (salesData.form_data?.source_details?.source) {
        console.log('✅ Using source from form_data:', salesData.form_data.source_details.source);
        return salesData.form_data.source_details.source;
      }

      if (salesData.source_name) {
        console.log('✅ Using source_name from API:', salesData.source_name);
        return salesData.source_name;
      }

      console.log('⚠️ No source found for Lead Data category');
      return 'N/A';
    }

    // For other lead categories or unknown, use existing fallback logic
    console.log('🔄 Using fallback source logic for unknown lead category');
    return salesData.form_data?.source_details?.source || salesData.source_name || 'N/A';
  }

  /**
   * Get the lead category name from UUID
   */
  getLeadCategoryName(): string {
    // First try to get from lead_category_id in leadData
    const categoryUuid = this.leadData?.lead_category_id || this.leadData?.formData?.lead_category_id || this.leadData?.formData?.source_details?.category;
    
    console.log('🔍 Getting lead category name for:', {
      categoryUuid,
      lead_category_id: this.leadData?.lead_category_id,
      form_data_lead_category_id: this.leadData?.formData?.lead_category_id,
      source_details_category: this.leadData?.formData?.source_details?.category,
      availableCategories: this.leadCategories?.map(c => ({ id: c.id, name: c.name }))
    });
    
    if (!categoryUuid) {
      return 'N/A';
    }

    // Find the category by UUID
    const category = this.leadCategories.find(cat => cat.id === categoryUuid);
    return category ? category.name : categoryUuid; // Return UUID if name not found
  }

  /**
   * Get the source display name for Lead Details view
   * Maps source UUID to source name when Lead Category is "Lead Data"
   */
  getSourceNameForDisplay(): string {
    console.log('🔍 getSourceDisplayName called with leadData.source:', this.leadData?.source);

    if (!this.leadData?.source || this.leadData.source === 'N/A') {
      return 'N/A';
    }

    // Check if it's already a readable name (not a UUID)
    if (!this.isValidUUID(this.leadData.source)) {
      console.log('✅ Using source name directly:', this.leadData.source);
      return this.leadData.source;
    }

    // If it's a UUID, try to map it to a name using the provided mapping data
    const sourceMapping = {
      '798c4d0a-e1e9-4a02-ab24-05e8d4b89fe0': 'Self',
      '89f4f76b-2143-4e05-9370-7d7ca5fc526a': 'Associate'
    };

    // First try the static mapping
    if (sourceMapping[this.leadData.source as keyof typeof sourceMapping]) {
      const mappedName = sourceMapping[this.leadData.source as keyof typeof sourceMapping];
      console.log('✅ Mapped source UUID using static mapping:', {
        uuid: this.leadData.source,
        mappedName: mappedName
      });
      return mappedName;
    }

    // Fallback to dynamic mapping from loaded sources
    if (this.sources && this.sources.length > 0) {
      const source = this.sources.find(s => s.id === this.leadData.source);
      if (source) {
        console.log('✅ Mapped source UUID using dynamic data:', {
          uuid: this.leadData.source,
          mappedName: source.name
        });
        return source.name;
      }
    }

    console.log('⚠️ No source mapping found, returning UUID:', this.leadData.source);
    return this.leadData.source;
  }

  /**
   * Get Connect With name from UUID for people information
   * Maps connect_with_id to Connect With name using the Connect With API data
   */
  getConnectWithNameFromId(connectWithId: string | null): string {
    console.log('🔍 getConnectWithNameFromId called with:', {
      connectWithId,
      connectWithListLength: this.connectWithList?.length || 0,
      connectWithListSample: this.connectWithList?.slice(0, 3).map(cw => ({ id: cw.id, name: cw.name })) || []
    });

    if (!connectWithId || connectWithId === 'N/A') {
      console.log('📋 Returning N/A for empty/null connectWithId');
      return 'N/A';
    }

    // Check if it's already a readable name (not a UUID)
    if (!this.isValidUUID(connectWithId)) {
      console.log('✅ Using connect with name directly (not a UUID):', connectWithId);
      return connectWithId;
    }

    // Map UUID to name using loaded Connect With data
    if (this.connectWithList && this.connectWithList.length > 0) {
      console.log('🔍 Searching for UUID in connectWithList:', {
        searchingFor: connectWithId,
        availableIds: this.connectWithList.map(cw => cw.id)
      });

      const connectWithOption = this.connectWithList.find(cw => cw.id === connectWithId);
      if (connectWithOption) {
        console.log('✅ Successfully mapped connect_with_id to name:', {
          uuid: connectWithId,
          mappedName: connectWithOption.name,
          description: connectWithOption.description
        });
        return connectWithOption.name;
      } else {
        console.log('❌ UUID not found in connectWithList:', {
          searchedUuid: connectWithId,
          availableOptions: this.connectWithList.map(cw => ({ id: cw.id, name: cw.name }))
        });
      }
    } else {
      console.log('⚠️ connectWithList is empty or not loaded:', {
        listExists: !!this.connectWithList,
        listLength: this.connectWithList?.length || 0
      });
    }

    console.log('⚠️ No connect with mapping found, returning UUID as fallback:', connectWithId);
    return connectWithId;
  }

  /**
   * Get the product type name (either from API response or from UUID mapping)
   */
  getProductTypeName(): string {
    console.log('🔍 getProductTypeName called with:', {
      leadDataProductType: this.leadData?.productType,
      isUUID: this.leadData?.productType ? this.isValidUUID(this.leadData.productType) : false,
      availableProductTypes: this.productTypes.length
    });

    // First try to use the product_type_name from API response
    if (this.leadData?.productType && this.leadData.productType !== 'N/A') {
      // Check if it's already a readable name (not a UUID)
      if (!this.isValidUUID(this.leadData.productType)) {
        console.log('✅ Using product type name directly:', this.leadData.productType);
        return this.leadData.productType;
      }

      // If it's a UUID, try to map it to a name using the provided mapping data
      const productTypeMapping = {
        'f92a6dfa-3528-45ee-9a3a-4d3253d03813': 'Corporate Syndication',
        '1453aa41-bcc8-41a5-a46c-9ffc827d9be4': 'Retail Syndication',
        '82448c41-98c0-4382-abf5-88afb0bc3613': 'Property',
        '7dd37b35-0b3f-4643-adac-0ac566b6ad2b': 'Insurance'
      };

      // First try the static mapping
      if (productTypeMapping[this.leadData.productType as keyof typeof productTypeMapping]) {
        const mappedName = productTypeMapping[this.leadData.productType as keyof typeof productTypeMapping];
        console.log('✅ Mapped UUID using static mapping:', {
          uuid: this.leadData.productType,
          mappedName: mappedName
        });
        return mappedName;
      }

      // Fallback to dynamic mapping from loaded product types
      const productType = this.productTypes.find(pt => pt.id === this.leadData.productType);
      const result = productType ? productType.name : this.leadData.productType;
      console.log('🔄 Mapped UUID using dynamic data:', {
        uuid: this.leadData.productType,
        mappedName: result,
        found: !!productType
      });
      return result;
    }

    console.log('⚠️ No valid product type found, returning N/A');
    return 'N/A';
  }

  /**
   * Get the sub product type name (either from API response or from UUID mapping)
   */
  getSubProductTypeName(): string {
    console.log('🔍 getSubProductTypeName called with:', {
      leadDataSubProductType: this.leadData?.subProductType,
      isUUID: this.leadData?.subProductType ? this.isValidUUID(this.leadData.subProductType) : false,
      availableSubProductTypes: this.subProductTypes.length
    });

    // First try to use the sub_product_type_name from API response
    if (this.leadData?.subProductType && this.leadData.subProductType !== 'N/A') {
      // Check if it's already a readable name (not a UUID)
      if (!this.isValidUUID(this.leadData.subProductType)) {
        console.log('✅ Using sub product type name directly:', this.leadData.subProductType);
        return this.leadData.subProductType;
      }

      // If it's a UUID, try to map it to a name using the provided mapping data
      const subProductTypeMapping = {
        // Corporate Syndication sub-types
        'bdd39bcf-8a14-49cb-8e71-d554a4b84a4b': 'Construction Funding(CF)',
        '07002e85-32cd-4018-8e87-596f74fbdce9': 'Inventory Funding(IF)',
        '9738264b-9a36-4d64-b5af-c8a31a3a8b42': 'Project Funding(PF)',
        'e0537f5e-597d-4052-ab87-e2a8eb7c57ff': 'Hospital Funding(HF)',
        'f4c6bd18-d760-4313-81e3-09d90f55fe6b': 'Education Funding(EF)',
        // Retail Syndication sub-types
        '6feaeea7-59fe-47a8-b967-3e02b1f208d7': 'Home Loan(HL)',
        '899008c6-6352-4338-894b-9c5305970f5b': 'Non Residential Property Loan(NRPL)',
        'c9c42d2a-69ef-42f0-8450-fdb3925a29af': 'Loan Against Property(LAP)',
        'a23fdba0-d565-45e1-aed6-0b233a89c612': 'Lease Rental Discounting(LRD)',
        // Property sub-types
        'ba1f7c8e-938e-4bb9-9d44-db878648630b': 'Property',
        // Insurance sub-types
        '97c00e7e-a05f-4939-8979-4656773d89c5': 'Contractor All Risk',
        'b5a1b463-e602-4baf-892b-c3275cc5bca2': 'Life Insurance'
      };

      // First try the static mapping
      if (subProductTypeMapping[this.leadData.subProductType as keyof typeof subProductTypeMapping]) {
        const mappedName = subProductTypeMapping[this.leadData.subProductType as keyof typeof subProductTypeMapping];
        console.log('✅ Mapped UUID using static mapping:', {
          uuid: this.leadData.subProductType,
          mappedName: mappedName
        });
        return mappedName;
      }

      // Fallback to dynamic mapping from loaded sub product types
      const subProductType = this.subProductTypes.find(spt => spt.id === this.leadData.subProductType);
      const result = subProductType ? subProductType.name : this.leadData.subProductType;
      console.log('🔄 Mapped UUID using dynamic data:', {
        uuid: this.leadData.subProductType,
        mappedName: result,
        found: !!subProductType
      });
      return result;
    }

    console.log('⚠️ No valid sub product type found, returning N/A');
    return 'N/A';
  }

  /**
   * Helper method to check if a string is a valid UUID
   */
  private isValidUUID(str: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  getPrimaryContactMobile(): string {
    // First priority: Check contact_information from form_data
    if (this.leadData?.formData?.contact_information?.mobile) {
      return this.leadData.formData.contact_information.mobile;
    }

    // Second priority: Check root-level contact_information
    if (this.leadData?.contactInformation?.mobile) {
      return this.leadData.contactInformation.mobile;
    }

    // Third priority: Fallback to people_information array
    if (this.leadData?.people && this.leadData.people.length > 0) {
      const primaryContact = this.leadData.people.find(person => person.is_primary_contact);
      const mobile = primaryContact?.mobile !== undefined ? primaryContact.mobile :
                    (this.leadData.people[0]?.mobile !== undefined ? this.leadData.people[0].mobile : 'N/A');
      return mobile;
    }

    return 'N/A';
  }

  getPrimaryContactEmail(): string {
    // First priority: Check contact_information from form_data
    if (this.leadData?.formData?.contact_information?.email) {
      return this.leadData.formData.contact_information.email;
    }

    // Second priority: Check root-level contact_information
    if (this.leadData?.contactInformation?.email) {
      return this.leadData.contactInformation.email;
    }

    // Third priority: Fallback to people_information array
    if (this.leadData?.people && this.leadData.people.length > 0) {
      const primaryContact = this.leadData.people.find(person => person.is_primary_contact);
      const email = primaryContact?.email !== undefined ? primaryContact.email :
                   (this.leadData.people[0]?.email !== undefined ? this.leadData.people[0].email : '');
      return email;
    }

    return '';
  }

  /**
   * Get the main email field from the API response (top-level email field)
   * Prioritizes root-level email field over contact emails
   */
  getMainEmailField(): string {
    console.log('🔍 Getting main email field:');
    console.log('📧 Lead data emailId:', this.leadData?.emailId);
    console.log('📧 Lead data structure:', {
      emailId: this.leadData?.emailId,
      hasEmailId: !!this.leadData?.emailId,
      emailIdType: typeof this.leadData?.emailId
    });

    // First priority: Root-level email field from API response
    if (this.leadData?.emailId &&
        this.leadData.emailId !== 'N/A' &&
        this.leadData.emailId !== '' &&
        this.leadData.emailId !== null &&
        this.leadData.emailId !== undefined) {
      console.log('✅ Using root-level email field:', this.leadData.emailId);
      return this.leadData.emailId;
    }

    // Second priority: Primary contact email as fallback
    const contactEmail = this.getPrimaryContactEmail();
    console.log('⚠️ Root-level email not available, using contact email:', contactEmail);
    return contactEmail;
  }

  /**
   * Debug method to check field values
   */
  debugEmailFields(): void {
    console.log('🔍 Field Values Debug:');
    console.log('📧 Main email (getMainEmailField):', this.getMainEmailField());
    console.log('📧 Primary contact email (getPrimaryContactEmail):', this.getPrimaryContactEmail());
    console.log('📧 Lead data emailId:', this.leadData?.emailId);
    console.log('👤 Profession type name (getProfessionTypeName):', this.getProfessionTypeName());
    console.log('👤 Lead data professionTypeName:', this.leadData?.professionTypeName);
    console.log('📍 Location name:', this.leadData?.location);
    console.log('📧 People information:', this.leadData?.people);
  }

  /**
   * Determine initial component visibility based on loaded lead data
   * This is called when lead data is first loaded (not in edit mode)
   */
  determineInitialComponentVisibility(): void {
    if (!this.leadData) {
      console.log('ℹ️ No lead data available for initial component visibility');
      return;
    }

    console.log('🔧 Determining initial component visibility based on lead data:', {
      productType: this.leadData.productType,
      subProductType: this.leadData.subProductType
    });

    const productType = this.leadData.productType;
    const subProductType = this.leadData.subProductType;

    if (!productType || !subProductType) {
      console.log('⚠️ Product type or sub-product type not available in lead data');
      return;
    }

    // Set the component visibility based on the loaded data
    const subProductTypeName = subProductType.toLowerCase();

    // Corporate Syndication - Show ProductOne component
    if (productType === 'Corporate Syndication') {
      const corporateSubTypes = ['cf', 'if', 'pf', 'hf', 'ef'];

      if (subProductTypeName.includes('construction') ||
          subProductTypeName.includes('inventory') ||
          subProductTypeName.includes('project') ||
          subProductTypeName.includes('hospital') ||
          subProductTypeName.includes('education') ||
          corporateSubTypes.some(type => subProductTypeName.includes(type))) {
        this.showProductOne = true;
        console.log('✅ Showing ProductOne component for Corporate Syndication on initial load');

        // Populate people information after component is rendered
        setTimeout(() => {
          this.prePopulateProductOnePeople();
        }, 100);
      }
    }

    // Retail Syndication - Show ProductTwo component
    else if (productType === 'Retail Syndication') {
      const retailSubTypes = ['hl', 'lap', 'lrd', 'nrpl'];

      if (subProductTypeName.includes('home') ||
          subProductTypeName.includes('property') ||
          subProductTypeName.includes('lease') ||
          subProductTypeName.includes('residential') ||
          retailSubTypes.some(type => subProductTypeName.includes(type))) {
        this.showProductTwo = true;
        console.log('✅ Showing ProductTwo component for Retail Syndication on initial load');

        // Populate people information after component is rendered
        setTimeout(() => {
          this.prePopulateProductTwoPeople();
        }, 100);
      }
    }

    // Add other product types as needed...
  }

  /**
   * Force immediate refresh of People Information display
   * This ensures the UI shows updated contact data without requiring a reload
   */
  refreshPeopleInformationDisplay(): void {
    console.log('🔄 Forcing immediate refresh of People Information display...');

    // Trigger change detection to update the UI immediately
    if (this.leadData?.people) {
      console.log('✅ People Information updated with latest data:', {
        count: this.leadData.people.length,
        contacts: this.leadData.people.map(p => ({
          name: p.name,
          mobile: p.mobile,
          email: p.email,
          role: p.role,
          is_primary: p.is_primary_contact
        }))
      });

      // Update edit form fields if in edit mode
      if (this.isEditMode && this.editFormData) {
        this.updateEditFormWithLatestContactInfo();
      }

      // Force Angular change detection immediately
      this.cdr.detectChanges();

      // Also trigger a second change detection after a brief delay to ensure UI updates
      setTimeout(() => {
        this.cdr.detectChanges();
        console.log('🔄 Change detection triggered for People Information section');
      }, 50);
    }
  }

  /**
   * Immediately update location information from API response
   * This ensures the location object is properly refreshed after updates
   */
  updateLocationInstantly(apiResponse: any): void {
    console.log('📍 Instantly updating location from API response...');

    if (apiResponse.location) {
      // Update the leadData location fields
      this.leadData.location = apiResponse.location.name || apiResponse.location.display_name || 'N/A';

      // Store the complete location object for reference
      this.leadData.locationObject = {
        id: apiResponse.location.id,
        name: apiResponse.location.name,
        display_name: apiResponse.location.display_name,
        code: apiResponse.location.code,
        zone: apiResponse.location.zone
      };

      console.log('📍 Location instantly updated:', {
        locationName: this.leadData.location,
        locationObject: this.leadData.locationObject,
        originalApiLocation: apiResponse.location
      });

      // Force immediate UI update
      this.cdr.detectChanges();
    }
  }

  /**
   * Immediately update people information from API response
   * This bypasses the normal mapping flow for instant UI updates
   */
  updatePeopleInformationInstantly(apiResponse: any): void {
    console.log('⚡ Instantly updating people information from API response...');

    // Get the latest people information from form_data
    const latestPeopleInfo = apiResponse.form_data?.people_information || apiResponse.people_information || [];

    if (latestPeopleInfo.length > 0) {
      // Directly update the leadData.people array
      this.leadData.people = latestPeopleInfo.map((person: any) => ({
        id: person.id,
        name: person.name || '',
        email: person.email || '',
        mobile: person.mobile || '',
        role: person.notes || '',
        is_primary_contact: Boolean(person.is_primary_contact),
        sales_id: person.sales_id,
        role_id: person.role_id,
        is_active: person.is_active,
        created_at: person.created_at,
        updated_at: person.updated_at
      }));

      console.log('⚡ People information instantly updated:', this.leadData.people);

      // Update contact_information with primary contact data
      this.updateContactInformationFromPeople();

      // Update edit form fields if in edit mode
      if (this.isEditMode && this.editFormData) {
        this.updateEditFormWithLatestContactInfo();
      }

      // Force immediate UI update
      this.cdr.detectChanges();
    }
  }

  /**
   * Update contact_information object from people_information data
   * This ensures contact_information stays synchronized with people data
   */
  updateContactInformationFromPeople(): void {
    console.log('🔄 Updating contact_information from people data...');

    if (!this.leadData?.people || this.leadData.people.length === 0) {
      console.log('⚠️ No people data available');
      return;
    }

    // Find primary contact or use first contact
    const primaryContact = this.leadData.people.find(person => person.is_primary_contact) || this.leadData.people[0];

    if (primaryContact) {
      // Update contact_information in leadData
      if (!this.leadData.contactInformation) {
        this.leadData.contactInformation = {};
      }

      this.leadData.contactInformation.name = primaryContact.name || '';
      this.leadData.contactInformation.mobile = primaryContact.mobile || '';
      this.leadData.contactInformation.email = primaryContact.email || '';

      // Also update in formData for API consistency
      if (!this.leadData.formData) {
        this.leadData.formData = {};
      }
      if (!this.leadData.formData.contact_information) {
        this.leadData.formData.contact_information = {};
      }

      this.leadData.formData.contact_information.name = primaryContact.name || '';
      this.leadData.formData.contact_information.mobile = primaryContact.mobile || '';
      this.leadData.formData.contact_information.email = primaryContact.email || '';

      console.log('✅ Contact information updated:', {
        name: this.leadData.contactInformation.name,
        mobile: this.leadData.contactInformation.mobile,
        email: this.leadData.contactInformation.email
      });
    }
  }

  /**
   * Update edit form fields with latest contact information
   * This ensures the Mobile field in Lead Details section stays synchronized
   */
  updateEditFormWithLatestContactInfo(): void {
    console.log('🔄 Updating edit form with latest contact information...');

    if (!this.editFormData) {
      console.log('⚠️ Edit form data not available');
      return;
    }

    // Update mobile field with primary contact mobile (now uses contact_information)
    const primaryContactMobile = this.getPrimaryContactMobile();
    if (primaryContactMobile && primaryContactMobile !== 'N/A') {
      this.editFormData.connectWithMobile = primaryContactMobile;
      console.log('📱 Updated edit form mobile field to:', primaryContactMobile);
    }

    // Update email field with primary contact email
    const primaryContactEmail = this.getPrimaryContactEmail();
    if (primaryContactEmail) {
      this.editFormData.connectWithEmail = primaryContactEmail;
      console.log('📧 Updated edit form email field to:', primaryContactEmail);
    }

    console.log('✅ Edit form contact fields updated successfully');
  }

  /**
   * Update RERA component with data from product form data
   * This ensures the RERA form is properly populated after API response
   */
  updateReraComponent(): void {
    if (!this.showProductOne || !this.productOneComponent || !this.productOneComponent.reraComponent) {
      console.log('ℹ️ No ProductOne or RERA component available for update');
      return;
    }

    const productFormData = this.leadData?.productFormData;
    if (!productFormData || !productFormData.rera_details) {
      console.log('ℹ️ No RERA details available in product form data');
      return;
    }

    console.log('🔧 Updating RERA component with data:', productFormData.rera_details);

    // Set RERA form visibility based on RERA value
    this.productOneComponent.showReraForm = productFormData.rera === 'Yes';

    // Update RERA form data
    this.productOneComponent.reraFormData = productFormData.rera_details;

    // Update RERA component form
    this.productOneComponent.reraComponent.reraForm.patchValue(productFormData.rera_details);

    // Trigger RERA value check to ensure form visibility is correct
    this.productOneComponent.checkReraValue();

    console.log('✅ RERA component updated successfully');
  }

  /**
   * Pre-populate ProductOne component's people array from form_data.people_information
   * This ensures the product form uses actual contact data from the API response
   */
  prePopulateProductOnePeople(): void {
    if (!this.productOneComponent || !this.leadData?.people) {
      console.log('ℹ️ No ProductOne component or people data available for population');
      return;
    }

    console.log('🔧 Pre-populating ProductOne people from form_data.people_information:', this.leadData.people);

    try {
      // Convert form_data.people_information to ProductOne's people format
      const convertedPeople = this.leadData.people
        .filter((person: any) => {
          // Only include people with actual data
          return person.name?.trim() || person.mobile?.trim();
        })
        .map((person: any, index: number) => ({
          id: index + 1, // ProductOne uses simple numeric IDs
          connectWith: person.role || '', // Use role field as position
          name: person.name || '',
          mobile: person.mobile || '',
          email: person.email || ''
        }));

      // Update ProductOne component's people array
      this.productOneComponent.people = convertedPeople;

      console.log('✅ ProductOne people populated successfully:', {
        originalPeopleCount: this.leadData.people.length,
        convertedPeopleCount: convertedPeople.length,
        convertedPeople: convertedPeople
      });

    } catch (error) {
      console.error('❌ Error pre-populating ProductOne people:', error);
    }
  }

  /**
   * Pre-populate ProductTwo component with people information from the lead data
   * This ensures the salaried/self-employed components have access to contact data
   */
  prePopulateProductTwoPeople(): void {
    if (!this.productTwoComponent || !this.leadData?.people) {
      console.log('ℹ️ No ProductTwo component or people data available for population');
      return;
    }

    console.log('🔧 Pre-populating ProductTwo with people data:', this.leadData.people);

    try {
      // Extract primary contact information
      const primaryContact = this.leadData.people.find((person: any) => person.is_primary_contact);

      // If we have a primary contact, use it to populate the main form fields
      if (primaryContact && this.productTwoComponent.product2Form) {
        console.log('🔧 Found primary contact for ProductTwo:', primaryContact);

        // Update the main form with primary contact info
        this.productTwoComponent.product2Form.patchValue({
          nameOfCustomer: primaryContact.name || '',
          contactNo: primaryContact.mobile || '',
          mailId: primaryContact.email || ''
        });

        // Force status selection if not already set
        if (!this.productTwoComponent.selectedStatus && this.productTwoComponent.product2Form.get('status')) {
          // Default to Salaried (SL) status if not set
          const status = 'SL';
          this.productTwoComponent.product2Form.get('status')?.setValue(status);
          this.productTwoComponent.selectedStatus = status;
          this.productTwoComponent.updateFieldsBasedOnStatus(status);
          console.log('🔧 Set default status for ProductTwo:', status);
        }
      }

      // Create co-applicant data structure for salaried/self-employed components
      const coApplicants = this.leadData.people
        .filter((person: any) => !person.is_primary_contact)
        .map((person: any) => ({
          name: person.name || '',
          relationship: 'Other', // Default relationship
          age: 30, // Default age
          profession: 'Salaried', // Default profession
          monthlyIncome: 0, // Default income
          contactNo: person.mobile || '',
          address: '', // No address in people data
          role: person.role || '' // Additional field for reference
        }));

      // Store co-applicants data for later use in sub-components
      if (coApplicants.length > 0) {
        console.log('🔧 Prepared co-applicants for ProductTwo:', coApplicants);

        // Store in the component's data structure for later population
        if (!this.productTwoComponent.productFormData) {
          this.productTwoComponent.productFormData = {};
        }

        // Add co-applicants to the form data
        this.productTwoComponent.productFormData.coApplicants = coApplicants;

        console.log('✅ ProductTwo co-applicants data prepared for sub-components');
      }

    } catch (error) {
      console.error('❌ Error pre-populating ProductTwo people:', error);
    }
  }

  /**
   * Pre-populate Car component with people information
   */
  prePopulateCarPeople(): void {
    if (!this.leadData?.people || this.leadData.people.length === 0 || !this.carComponent) {
      console.log('⚠️ No people data or Car component not available for pre-population');
      return;
    }

    console.log('🔧 Pre-populating Car component with people data:', this.leadData.people);

    try {
      // Extract primary contact information
      const primaryContact = this.leadData.people.find((person: any) => person.is_primary_contact);

      // Car component is focused on construction/contractor details, not customer info
      // We can populate contractor name with primary contact name if needed
      if (primaryContact && this.carComponent.carForm) {
        console.log('🔧 Found primary contact for Car component:', primaryContact);

        // Update contractor name with primary contact info
        this.carComponent.carForm.patchValue({
          contractorName: primaryContact.name || '',
          // Note: Car component doesn't have typical customer fields
          // It's focused on construction project details
        });

        console.log('✅ Car component populated with primary contact as contractor');
      }

      console.log('✅ Car component pre-population completed');
    } catch (error) {
      console.error('❌ Error pre-populating Car component:', error);
    }
  }

  /**
   * Pre-populate Life component with people information
   */
  prePopulateLifePeople(): void {
    if (!this.leadData?.people || this.leadData.people.length === 0 || !this.lifeComponent) {
      console.log('⚠️ No people data or Life component not available for pre-population');
      return;
    }

    console.log('🔧 Pre-populating Life component with people data:', this.leadData.people);

    try {
      // Extract primary contact information
      const primaryContact = this.leadData.people.find((person: any) => person.is_primary_contact);

      // If we have a primary contact, use it to populate the main form fields
      if (primaryContact && this.lifeComponent.lifeForm) {
        console.log('🔧 Found primary contact for Life component:', primaryContact);

        // Update the main form with primary contact info
        this.lifeComponent.lifeForm.patchValue({
          customerName: primaryContact.name || '',
          insuredName: primaryContact.name || '', // Life insurance specific field
          // Note: Life component doesn't have contact number or email fields
          // It's focused on life insurance specific details
        });

        console.log('✅ Life component main form populated with primary contact');
      }

      console.log('✅ Life component pre-population completed');
    } catch (error) {
      console.error('❌ Error pre-populating Life component:', error);
    }
  }

  /**
   * Pre-populate Property component with people information
   * This is only called when no saved customers data exists
   */
  prePopulatePropertyPeople(): void {
    if (!this.leadData?.people || this.leadData.people.length === 0 || !this.propertyComponent) {
      console.log('⚠️ No people data or Property component not available for pre-population');
      return;
    }

    console.log('🔧 Pre-populating Property component with people data (fallback):', this.leadData.people);
    console.log('🔧 Current customers before people population:', this.propertyComponent.customers);

    try {
      // Only populate if no customers exist (to avoid overriding saved customer data)
      if (this.propertyComponent.customers.length === 0) {
        console.log('🔧 No existing customers found, populating from people data');

        // Convert people data to customer format
        this.leadData.people.forEach((person: any, index: number) => {
          const customer = {
            id: index + 1,
            customerName: person.name || '',
            contactNo: person.mobile || '',
            resiAddress: '' // Property component doesn't have address in people data
          };

          this.propertyComponent.customers.push(customer);
          console.log('🔧 Added customer from people data:', customer);
        });

        // Update next customer ID
        this.propertyComponent.nextCustomerId = this.leadData.people.length + 1;

        // Reset current customer for new entries
        this.propertyComponent.currentCustomer = this.propertyComponent.createEmptyCustomer();

        console.log('✅ Property component populated with customers from people data:', this.propertyComponent.customers);
      } else {
        console.log('ℹ️ Existing customers found, skipping people data population to preserve saved customer data');
        console.log('ℹ️ Existing customers:', this.propertyComponent.customers);
      }

      console.log('✅ Property component pre-population completed');
    } catch (error) {
      console.error('❌ Error pre-populating Property component:', error);
    }
  }
}
