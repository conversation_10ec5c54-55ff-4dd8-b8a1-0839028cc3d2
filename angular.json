{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"demo1": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/demo1", "allowedCommonJsDependencies": ["feather-icons", "sweetalert2", "apexcharts", "quill-delta", "dropzone"], "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/styles.scss"], "scripts": [], "define": {"process.env.NODE_ENV": "\"development\""}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "30kB", "maximumError": "50kB"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "sourceMap": false, "extractLicenses": true, "namedChunks": false}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"allowedHosts": ["localhost", "127.0.0.1", "bizzcorp.antllp.com", ".antllp.com"], "host": "0.0.0.0", "disableHostCheck": false, "verbose": false}, "configurations": {"production": {"buildTarget": "demo1:build:production"}, "development": {"buildTarget": "demo1:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "3e12d5f9-d50b-4e4d-887c-690513ab85f4"}}