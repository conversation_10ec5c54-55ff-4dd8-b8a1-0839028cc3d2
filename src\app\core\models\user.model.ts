/**
 * User Management Models
 * Comprehensive interfaces for user CRUD operations
 */

export interface User {
  id: string;
  email: string;
  is_active?: boolean;
  is_superuser?: boolean;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
  is_deleted: boolean;
  // Additional computed fields
  full_name?: string;
  roles?: any[];
  permissions?: string[];
}

export interface UserCreate {
  email: string;
  first_name: string;
  last_name: string;
  is_active?: boolean;
  is_superuser?: boolean;
  password?: string;
}

export interface UserUpdate {
  email?: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
  is_superuser?: boolean;
}

export interface UserFilter {
  search?: string;
  is_active?: boolean;
  is_superuser?: boolean;
  is_deleted?: boolean;
}

export interface UserResponse {
  items: User[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface PasswordResetRequest {
  email: string;
}

export interface UserStatistics {
  total: number;
  active: number;
  inactive: number;
  superusers: number;
  deleted: number;
}

export interface UserWithRoles extends User {
  roles: any[];
  permissions: string[];
}

// API Response wrapper
export interface UserAPIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: {
    message?: string;
    total?: number;
    page?: number;
    size?: number;
  };
}

// Form validation interfaces
export interface UserFormData {
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_superuser: boolean;
  password?: string;
  confirm_password?: string;
}

export interface UserFormErrors {
  email?: string;
  first_name?: string;
  last_name?: string;
  password?: string;
  confirm_password?: string;
  general?: string;
}

// Table column configuration
export interface UserTableColumn {
  key: keyof User | 'actions' | '';
  label: string;
  sortable?: boolean;
  width?: string;
  type?: 'text' | 'email' | 'date' | 'boolean' | 'actions';
}

// Sort configuration
export interface UserSortConfig {
  column: keyof User;
  direction: 'asc' | 'desc' | '';
}

// Pagination configuration
export interface UserPaginationConfig {
  page: number;
  pageSize: number;
  totalItems: number;
  pageSizeOptions: number[];
}

// User action types for components
export type UserAction = 'view' | 'edit' | 'delete' | 'restore' | 'activate' | 'deactivate';

export interface UserActionEvent {
  action: UserAction;
  user: User;
}

// User status enum
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted'
}

// User role enum (basic roles)
export enum UserRole {
  SUPERUSER = 'superuser',
  ADMIN = 'admin',
  USER = 'user'
}
