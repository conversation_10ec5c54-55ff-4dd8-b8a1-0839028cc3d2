import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, forwardRef, inject } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbDatepickerModule, NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { HolidayService } from '../../../core/services/holiday.service';

@Component({
  selector: 'app-restricted-datepicker',
  standalone: true,
  imports: [CommonModule, FormsModule, NgbDatepickerModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RestrictedDatepickerComponent),
      multi: true
    }
  ],
  template: `
    <div class="input-group">
      <input
        class="form-control"
        [placeholder]="placeholder"
        [ngModel]="dateString"
        (ngModelChange)="onDateStringChange($event)"
        ngbDatepicker
        #datePicker="ngbDatepicker"
        [markDisabled]="dateFilter"
        (dateSelect)="onDateSelect($event)"
        [disabled]="disabled"
        readonly
        [required]="required">
      <button 
        class="input-group-text" 
        type="button" 
        (click)="datePicker.toggle()"
        [disabled]="disabled">
        <i class="feather icon-calendar icon-md text-secondary"></i>
      </button>
    </div>
    
    <!-- Optional: Show restriction info -->
    <small class="text-muted mt-1" *ngIf="showRestrictionInfo">
      <i class="feather icon-info"></i>
      Weekends and holidays are disabled
    </small>
  `,
  styleUrls: ['./restricted-datepicker.component.scss']
})
export class RestrictedDatepickerComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() placeholder: string = 'yyyy-mm-dd';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() restrictWeekends: boolean = true;
  @Input() restrictHolidays: boolean = true;
  @Input() showRestrictionInfo: boolean = false;
  
  @Output() dateChange = new EventEmitter<string>();
  @Output() dateSelect = new EventEmitter<NgbDate>();

  // Angular services
  calendar = inject(NgbCalendar);
  formatter = inject(NgbDateParserFormatter);

  // Component state
  dateString: string = '';
  selectedDate: NgbDate | null = null;
  holidays: string[] = [];
  
  private holidaysSubscription?: Subscription;
  private onChange = (value: string) => {};
  private onTouched = () => {};

  constructor(private holidayService: HolidayService) {}

  ngOnInit(): void {
    console.log('🔒 RestrictedDatepicker - Initializing with restrictions:', {
      restrictWeekends: this.restrictWeekends,
      restrictHolidays: this.restrictHolidays
    });
    
    // Load holidays if restriction is enabled
    if (this.restrictHolidays) {
      this.loadHolidays();
    }
  }

  ngOnDestroy(): void {
    if (this.holidaysSubscription) {
      this.holidaysSubscription.unsubscribe();
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this.dateString = value || '';
    this.selectedDate = this.stringToNgbDate(value);
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Date handling methods
  onDateSelect(date: NgbDate | null): void {
    if (!date) return;

    this.selectedDate = date;
    this.dateString = this.formatter.format(date);
    
    // Emit events
    this.dateChange.emit(this.dateString);
    this.dateSelect.emit(date);
    
    // Update form control
    this.onChange(this.dateString);
    this.onTouched();
    
    console.log('📅 RestrictedDatepicker - Date selected:', this.dateString);
  }

  onDateStringChange(value: string): void {
    this.dateString = value;
    this.selectedDate = this.stringToNgbDate(value);
    
    // Emit events
    this.dateChange.emit(value);
    
    // Update form control
    this.onChange(value);
    this.onTouched();
  }

  // Date filter function
  dateFilter = (date: NgbDate | null): boolean => {
    if (!date) return false;

    // Convert NgbDate to JavaScript Date
    const jsDate = new Date(date.year, date.month - 1, date.day);
    
    // Check if it's a weekend
    if (this.restrictWeekends) {
      const dayOfWeek = jsDate.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday = 0, Saturday = 6
        return false;
      }
    }

    // Check if it's a holiday
    if (this.restrictHolidays && this.holidays.length > 0) {
      const dateStr = this.formatDate(jsDate);
      if (this.holidays.includes(dateStr)) {
        return false;
      }
    }

    return true;
  };

  private loadHolidays(): void {
    console.log('🎄 RestrictedDatepicker - Loading holidays for date restriction');
    
    this.holidaysSubscription = this.holidayService.getCachedHolidays().subscribe({
      next: (holidays) => {
        this.holidays = holidays.map(h => h.date); // Replace 'date' with the actual property name containing the holiday date
        console.log(`✅ RestrictedDatepicker - Loaded ${this.holidays.length} holidays for filtering`);
      },
      error: (error) => {
        console.error('❌ RestrictedDatepicker - Error loading holidays:', error);
        this.holidays = [];
      }
    });
  }

  private stringToNgbDate(dateString: string): NgbDate | null {
    if (!dateString) return null;
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return null;
    
    return this.calendar.getToday().constructor.call(
      this.calendar.getToday(),
      date.getFullYear(),
      date.getMonth() + 1,
      date.getDate()
    );
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
