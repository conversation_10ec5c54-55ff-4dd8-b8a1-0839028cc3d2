import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-upload me-2"></i>
        Bulk Upload Lead Categories
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Bulk Upload Component - Coming Soon</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced component will support bulk uploading of lead categories with:
        <ul class="mt-2 mb-0">
          <li><strong>Excel Template with Complex Rules</strong> - Template supporting qualification criteria and automation rules</li>
          <li><strong>Hierarchical Import</strong> - Support for parent-child category relationships</li>
          <li><strong>Scoring Rules Import</strong> - Bulk import of custom scoring rules and weights</li>
          <li><strong>Automation Rules Import</strong> - Import follow-up and automation rules with validation</li>
          <li><strong>Qualification Criteria Import</strong> - Budget ranges, timelines, and company requirements</li>
          <li><strong>Advanced Validation</strong> - Business rule validation and dependency checking</li>
          <li><strong>Preview Mode</strong> - Preview categories with scoring and automation before import</li>
          <li><strong>Rollback Support</strong> - Ability to rollback failed imports with detailed logs</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-settings me-2"></i>
        <strong>Advanced Import Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Hierarchical validation and circular dependency detection</li>
          <li>Scoring weight normalization and validation</li>
          <li>Currency conversion and multi-currency support</li>
          <li>Automation rule syntax validation</li>
          <li>Qualification criteria range validation</li>
          <li>Duplicate detection and merge strategies</li>
          <li>Performance impact analysis for large imports</li>
        </ul>
      </div>

      <div class="alert alert-success">
        <i class="feather icon-check-circle me-2"></i>
        <strong>Import Validation:</strong>
        <ul class="mt-2 mb-0">
          <li>Real-time validation during file upload</li>
          <li>Business rule compliance checking</li>
          <li>Data integrity and consistency validation</li>
          <li>Performance optimization suggestions</li>
          <li>Automated testing of scoring rules</li>
          <li>Integration testing with existing categories</li>
        </ul>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Close</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('uploaded')">Upload</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
  `]
})
export class BulkUploadComponent {
  constructor(public activeModal: NgbActiveModal) {}
}
