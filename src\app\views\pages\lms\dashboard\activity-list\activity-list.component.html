<!-- Breadcrumb Navigation -->
<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <div>
            <h6 class="card-title mb-0">View New Year Activity</h6>
            <!-- Activity Count Display -->
            <!-- <div *ngIf="activityCount && !loadingCount" class="mt-1">
              <small class="text-muted">
                Total: <strong>{{ activityCount.total }}</strong> |
                Planned: <strong class="text-primary">{{ activityCount.planned }}</strong> |
                Ongoing: <strong class="text-warning">{{ activityCount.ongoing }}</strong> |
                Completed: <strong class="text-success">{{ activityCount.completed }}</strong> |
                Cancelled: <strong class="text-danger">{{ activityCount.cancelled }}</strong>
              </small>
            </div> -->
            <!-- Loading indicator for count -->
            <div *ngIf="loadingCount" class="mt-1">
              <small class="text-muted">
                <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                Loading count...
              </small>
            </div>
          </div>
          <!-- <button class="btn btn-primary btn-sm" (click)="openCreateActivityModal(createActivityModal)">
            <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
            Create New Activity
          </button> -->
        </div>

        <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

        <!-- Controls row -->
        <div class="row mb-3">
          <div class="col-md-6 col-lg-4 mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Search:</span>
              <input
                type="text"
                class="form-control form-control-sm"
                [formControl]="searchTerm"
                placeholder="Search activities..."
              >
            </div>
          </div>

          <!-- <div class="col-md-6 col-lg-3 mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Filter:</span>
              <select class="form-select form-select-sm" [formControl]="filterType">
                <option value="all">All Status</option>
                <option value="planned">Planned</option>
                <option value="ongoing">Ongoing</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div> -->

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChange($event)">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>

          <div class="col-md-6 col-lg-3 d-flex align-items-center mb-3">
            <button class="btn btn-outline-secondary btn-sm me-2" (click)="resetFilter()">
              <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
              Reset
            </button>
            <button class="btn btn-primary btn-sm" (click)="refreshActivitiesList()">
              <i data-feather="refresh-ccw" class="icon-sm me-1" appFeatherIcon></i>
              Refresh
            </button>
          </div>
        </div>

        <!-- Loading indicator -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Activity Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">S.No.</th>
                <th scope="col">Actions</th>
                <th scope="col" sortable="activity_name" (sort)="onSort($event)">Holiday/Activity</th>
                <th scope="col" sortable="holiday_month" (sort)="onSort($event)">Month</th>
                <th scope="col" sortable="holiday_day" (sort)="onSort($event)">Day</th>
                <th scope="col" sortable="date" (sort)="onSort($event)">Date</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let activity of paginatedActivities; let i = index">
                <td>{{ (page - 1) * pageSize + i + 1 }}</td>
                <td class="action-icons">
                  <a
                    href="javascript:;"
                    class="action-icon me-2"
                    [class.disabled]="!canEditActivity(activity)"
                    [ngbTooltip]="canEditActivity(activity) ? 'Edit Activity' : 'Cannot edit this activity'"
                    (click)="canEditActivity(activity) && editActivity(activity, editActivityModal)"
                  >
                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                  </a>
                  <a
                    href="javascript:;"
                    class="action-icon text-danger"
                    [class.disabled]="!canDeleteActivity(activity)"
                    [ngbTooltip]="canDeleteActivity(activity) ? 'Delete Activity' : 'Cannot delete this activity'"
                    (click)="canDeleteActivity(activity) && deleteActivity(activity)"
                  >
                    <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                  </a>
                </td>
                <td>
                  <span class="fw-medium">{{ getActivityDisplayName(activity) }}</span>
                </td>
                <td>{{ activity.holiday_month || getMonthFromDate(activity.date) || 'N/A' }}</td>
                <td>{{ activity.holiday_day || getDayFromDate(activity.date) || 'N/A' }}</td>
                <td>{{ formatDate(activity.date || activity.holiday_date) }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty state message -->
        <div class="empty-state text-center py-5" *ngIf="!loading && paginatedActivities.length === 0">
          <i data-feather="calendar" class="icon-lg mb-3" appFeatherIcon></i>
          <p class="mb-0">{{ searchTerm.value || filterType.value !== 'all' ? 'No activities found matching your criteria' : 'No activities found' }}</p>
          <button *ngIf="searchTerm.value || filterType.value !== 'all'" class="btn btn-outline-primary btn-sm mt-2" (click)="resetFilter()">
            Clear Filters
          </button>
        </div>

        <!-- Pagination and Info -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!loading && totalItems > 0">
          <div class="d-flex align-items-center gap-3">
            <span class="text-muted">
              Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems) }} of {{ totalItems }} entries
              <span *ngIf="totalItems !== totalActivities" class="text-info">(filtered from {{ totalActivities }} total)</span>
            </span>
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChange($event)" style="width: auto;">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
          <ngb-pagination
            [collectionSize]="totalItems"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            (pageChange)="onPageChange($event)"
            class="pagination-sm"
          ></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Activity Modal -->
<ng-template #editActivityModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Edit Activity</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="editActivityForm" (ngSubmit)="saveActivityChanges()">

      <!-- Activity Information Section -->
      <div class="section-title">Activity Information</div>
      <div class="row">
        <div class="col-12 col-md-6 mb-3">
          <label for="activity_name" class="form-label">Activity/Holiday Name <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="activity_name"
            formControlName="activity_name"
            placeholder="Enter activity or holiday name"
            [class.is-invalid]="editActivityForm.get('activity_name')?.invalid && editActivityForm.get('activity_name')?.touched"
          >
          <div class="invalid-feedback" *ngIf="editActivityForm.get('activity_name')?.invalid && editActivityForm.get('activity_name')?.touched">
            Activity name is required.
          </div>
        </div>
        <div class="col-12 col-md-6 mb-3">
          <label for="date" class="form-label">Date <span class="text-danger">*</span></label>
          <input
            type="date"
            class="form-control"
            id="date"
            formControlName="date"
            [class.is-invalid]="editActivityForm.get('date')?.invalid && editActivityForm.get('date')?.touched"
          >
          <div class="invalid-feedback" *ngIf="editActivityForm.get('date')?.invalid && editActivityForm.get('date')?.touched">
            Date is required.
          </div>
        </div>
        <div class="col-12 col-md-4 mb-3">
          <label for="holiday_month" class="form-label">Month</label>
          <select class="form-select" id="holiday_month" formControlName="holiday_month">
            <option value="">Select Month</option>
            <option value="january">January</option>
            <option value="february">February</option>
            <option value="march">March</option>
            <option value="april">April</option>
            <option value="may">May</option>
            <option value="june">June</option>
            <option value="july">July</option>
            <option value="august">August</option>
            <option value="september">September</option>
            <option value="october">October</option>
            <option value="november">November</option>
            <option value="december">December</option>
          </select>
        </div>
        <div class="col-12 col-md-4 mb-3">
          <label for="holiday_day" class="form-label">Day</label>
          <select class="form-select" id="holiday_day" formControlName="holiday_day">
            <option value="">Select Day</option>
            <option value="monday">Monday</option>
            <option value="tuesday">Tuesday</option>
            <option value="wednesday">Wednesday</option>
            <option value="thursday">Thursday</option>
            <option value="friday">Friday</option>
            <option value="saturday">Saturday</option>
            <option value="sunday">Sunday</option>
          </select>
        </div>

      </div>

    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()" [disabled]="saving">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="saveActivityChanges()" [disabled]="saving">
      <span *ngIf="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ saving ? 'Saving...' : 'Save Changes' }}
    </button>
  </div>
</ng-template>

<!-- Create Activity Modal -->
<ng-template #createActivityModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Create New Activity</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="createActivityForm" (ngSubmit)="createActivity()">

      <!-- Activity Information Section -->
      <div class="section-title">Activity Information</div>
      <div class="row">
        <div class="col-12 col-md-6 mb-3">
          <label for="create_activity_name" class="form-label">Activity/Holiday Name <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="create_activity_name"
            formControlName="activity_name"
            placeholder="Enter activity or holiday name"
            [class.is-invalid]="createActivityForm.get('activity_name')?.invalid && createActivityForm.get('activity_name')?.touched"
          >
          <div class="invalid-feedback" *ngIf="createActivityForm.get('activity_name')?.invalid && createActivityForm.get('activity_name')?.touched">
            Activity name is required.
          </div>
        </div>
        <div class="col-12 col-md-6 mb-3">
          <label for="create_date" class="form-label">Date <span class="text-danger">*</span></label>
          <input
            type="date"
            class="form-control"
            id="create_date"
            formControlName="date"
            [class.is-invalid]="createActivityForm.get('date')?.invalid && createActivityForm.get('date')?.touched"
          >
          <div class="invalid-feedback" *ngIf="createActivityForm.get('date')?.invalid && createActivityForm.get('date')?.touched">
            Date is required.
          </div>
        </div>
        <div class="col-12 col-md-4 mb-3">
          <label for="create_holiday_month" class="form-label">Month</label>
          <select class="form-select" id="create_holiday_month" formControlName="holiday_month">
            <option value="">Select Month</option>
            <option value="january">January</option>
            <option value="february">February</option>
            <option value="march">March</option>
            <option value="april">April</option>
            <option value="may">May</option>
            <option value="june">June</option>
            <option value="july">July</option>
            <option value="august">August</option>
            <option value="september">September</option>
            <option value="october">October</option>
            <option value="november">November</option>
            <option value="december">December</option>
          </select>
        </div>
        <div class="col-12 col-md-4 mb-3">
          <label for="create_holiday_day" class="form-label">Day</label>
          <select class="form-select" id="create_holiday_day" formControlName="holiday_day">
            <option value="">Select Day</option>
            <option value="monday">Monday</option>
            <option value="tuesday">Tuesday</option>
            <option value="wednesday">Wednesday</option>
            <option value="thursday">Thursday</option>
            <option value="friday">Friday</option>
            <option value="saturday">Saturday</option>
            <option value="sunday">Sunday</option>
          </select>
        </div>

      </div>

    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()" [disabled]="creating">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="createActivity()" [disabled]="creating">
      <span *ngIf="creating" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ creating ? 'Creating...' : 'Create Activity' }}
    </button>
  </div>
</ng-template>
