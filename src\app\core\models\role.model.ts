export interface Permission {
  id: string;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: Permission[];
  users?: any[];
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface RoleCreate {
  name: string;
  description?: string;
}

export interface RoleUpdate {
  name?: string;
  description?: string;
}

export interface PermissionCreate {
  name: string;
  description?: string;
}

export interface PermissionUpdate {
  name?: string;
  description?: string;
}

export interface RolePermissionAssignment {
  role_id: string;
  permission_id: string;
}

// Enhanced interfaces for dynamic RBAC
export interface PermissionGroup {
  name: string;
  permissions: Permission[];
}

export interface UserPermissions {
  userId: string;
  roles: any[]; // Use any[] to match UserRole structure from API
  permissions: Permission[];
  permissionNames: string[];
  lastUpdated: Date;
}

export interface RoutePermission {
  path: string;
  requiredPermissions: string[];
  requiredRoles?: string[];
  allowedForAll?: boolean;
}

export interface MenuPermission {
  menuId: string;
  label: string;
  requiredPermissions: string[];
  requiredRoles?: string[];
  children?: MenuPermission[];
}

export interface PermissionCheck {
  hasPermission: boolean;
  missingPermissions: string[];
  reason?: string;
}

export interface RoleComment {
  id: string;
  role_id: string;
  user_id: string;
  user_name: string;
  comment: string;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface RoleWithUsers extends Role {
  users: any[];
}

export interface ApiResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ActualApiResponse<T> {
  data: T[];
  error: any;
  meta: any;
  success: boolean;
}
