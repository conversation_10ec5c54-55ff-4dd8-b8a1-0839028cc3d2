import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';

/**
 * Dynamic Auth Guard - Advanced permission checking with role-based access control
 *
 * Features:
 * - Role-based access control
 * - Fallback permission mapping
 * - Superuser privileges
 * - Wildcard permission support
 * - Detailed logging for debugging
 */
export const dynamicAuthGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  // 🚨 TEMPORARY BYPASS: Create mock user session for development
  console.log('🚨 AUTH GUARD - Creating mock user session for development:', state.url);

  // Create mock user if none exists
  if (!authService.currentUserValue) {
    const mockUser = {
      id: 'mock-user-123',
      email: '<EMAIL>',
      name: 'Development User',
      firstName: 'Development',
      lastName: 'User',
      role: 'admin',
      roles: ['admin', 'user'],
      permissions: ['*'], // Wildcard permission for all access
      is_superuser: true,
      access_token: 'mock-development-token-12345',
      refresh_token: 'mock-refresh-token-12345',
      token_expiry: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      is_active: true
    };

    // Store mock user in localStorage and set in service
    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    console.log('🔧 Mock user session created for development');
  }

  return true;

  // Original authentication logic (commented out for development)
  /*
  try {
    console.log('🛡️ Dynamic Auth Guard checking route:', state.url);
    const currentUser = authService.currentUserValue;
    console.log('👤 Current user:', currentUser ? `${currentUser.email} (${currentUser.role})` : 'Not logged in');

    if (!currentUser) {
      console.log('❌ Not logged in, redirecting to login');
      router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }
  */

    /*
    // Check if route requires specific permissions
    const requiredPermissions = route.data['permissions'] as string[];
    const requiredRoles = route.data['roles'] as string[];
    const allowedForAll = route.data['allowedForAll'] as boolean;

    console.log('🔍 Route requirements:', {
      permissions: requiredPermissions,
      roles: requiredRoles,
      allowedForAll: allowedForAll
    });

    // If route is allowed for all authenticated users
    if (allowedForAll === true || (!requiredPermissions?.length && !requiredRoles?.length)) {
      console.log('✅ Route allowed for all authenticated users');
      return true;
    }

    // Check role-based access first
    if (requiredRoles?.length > 0) {
      const hasRequiredRole = requiredRoles.some(role => {
        const hasRole = currentUser.roles?.includes(role) || currentUser.role === role;
        console.log(`${hasRole ? '✅' : '❌'} Role check: ${role}`);
        return hasRole;
      });

      if (hasRequiredRole) {
        console.log('✅ Access granted based on role');
        return true;
      }
    }

    // Check permission-based access
    if (requiredPermissions?.length > 0) {
      console.log('🔍 Checking permissions using dynamic system...');



      // Check for wildcard permission
      if (currentUser.permissions?.includes('*')) {
        console.log('✅ Access granted - User has wildcard permission (*)');
        return true;
      }

      // Check for any required permission using AuthService
      const hasAnyPermission = requiredPermissions.some(permission => {
        const hasPermission = authService.hasPermission(permission);
        console.log(`${hasPermission ? '✅' : '❌'} AuthService permission check: ${permission}`);
        return hasPermission;
      });

      if (hasAnyPermission) {
        console.log('✅ Access granted via AuthService permission check');
        return true;
      }

      // Fallback: Direct permission check for any remaining cases
      const hasDirectPermission = checkDirectPermissions(requiredPermissions, currentUser);

      if (hasDirectPermission) {
        console.log('✅ Access granted via direct permission check');
        return true;
      }

      // Access denied
      console.log('❌ Access denied - Missing required permissions');
      console.log('📋 Required permissions:', requiredPermissions);
      console.log('🔑 User permissions:', currentUser.permissions);

      const missingPermissions = requiredPermissions.filter(perm =>
        !currentUser.permissions?.includes(perm) && !authService.hasPermission(perm)
      );
      console.log('🚫 Missing permissions:', missingPermissions);

      router.navigate(['/lms/dashboard']);
      return false;
    }

    // Default: allow access if no specific requirements
    console.log('✅ Route authorized - No specific requirements');
    return true;

  } catch (error) {
    console.error('❌ Error in dynamic auth guard:', error);
    router.navigate(['/auth/login']);
    return false;
  }
  */
};

/**
 * Direct permission checking - no mappings, uses exact permission matches
 */
function checkDirectPermissions(requiredPermissions: string[], currentUser: any): boolean {
  const userPermissions = currentUser.permissions || [];

  console.log('🔍 Direct permission check - STRICT MODE');
  console.log('🔑 Required permissions:', requiredPermissions);
  console.log('🔑 User permissions:', userPermissions);

  // Check if user has any of the required permissions directly
  const hasDirectPermission = requiredPermissions.some(permission =>
    userPermissions.includes(permission)
  );

  if (hasDirectPermission) {
    console.log('✅ Direct permission match found');
    return true;
  }

  console.log('❌ No direct permission matches found');
  return false;
}


