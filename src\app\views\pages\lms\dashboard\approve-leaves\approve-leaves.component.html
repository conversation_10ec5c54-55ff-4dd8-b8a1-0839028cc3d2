<!-- Breadcrumb Navigation -->


<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Approve Leaves</h6>
          <button class="btn btn-outline-primary btn-sm" (click)="refreshData()" [disabled]="loading">
            <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
            Refresh
          </button>
        </div>
        <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

        <!-- Loading state -->
        <div class="text-center py-5" *ngIf="loading">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 text-muted">Loading leave applications...</p>
        </div>

        <!-- Error state -->
        <div class="alert alert-danger" *ngIf="error && !loading">
          <i data-feather="alert-circle" class="icon-sm me-2" appFeatherIcon></i>
          {{ error }}
          <button class="btn btn-outline-danger btn-sm ms-2" (click)="refreshData()">
            Try Again
          </button>
        </div>
        <!-- Main content (hidden when loading) -->
        <div *ngIf="!loading && !error">
          <!-- Search and filter section -->
          <div class="row mb-4">
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="statusFilter" class="form-label">Status Filter</label>
            <select
              id="statusFilter"
              class="form-select"
              [formControl]="statusFilter"
            >
              <option value="all">All</option>
              <option value="PENDING">Pending</option>
              <option value="APPROVED">Approved</option>
              <option value="REJECTED">Rejected</option>
            </select>
          </div>

          <div class="col-12 col-md-6 col-lg-4 mb-3">
            <label for="searchInput" class="form-label">Search</label>
            <input
              type="text"
              class="form-control"
              id="searchInput"
              [formControl]="searchTerm"
              placeholder="Search by employee name"
            >
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">Sr. No</th>
                <th scope="col">Employee Name</th>
                <th scope="col">Leave Type</th>
                <th scope="col">From Date</th>
                <th scope="col">To Date</th>
                <th scope="col">Days</th>
                <th scope="col">Reason</th>
                <th scope="col">Status</th>
                <th scope="col">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let leave of filteredLeaves; let i = index">
                <td><span class="fw-medium">{{ (page - 1) * pageSize + i + 1 }}</span></td>
                <td>{{ leave.employeeName || 'N/A' }}</td>
                <td>
                  <span class="badge" [ngClass]="getLeaveTypeBadgeClass(leave.leaveType)">
                    {{ leave.leaveType }}
                  </span>
                </td>
                <td>{{ leave.fromDate | date:'dd-MM-yyyy' }}</td>
                <td>{{ leave.toDate | date:'dd-MM-yyyy' }}</td>
                <td>{{ leave.days }}</td>
                <td>{{ leave.reason }}</td>
                <td>
                  <span class="badge rounded-pill " [ngClass]="getStatusBadgeClass(leave.status)">
                    {{ leave.status }}
                  </span>
                </td>
                <td>
                  <div class="action-icons">
                    <!-- Approve Button with Loading State -->
                    <ng-container *ngIf="leave.status === 'PENDING'">
                      <!-- Show loading spinner when approving -->
                      <a href="javascript:;"
                         class="action-icon text-success"
                         *ngIf="isApproving(leave.id)"
                         ngbTooltip="Approving...">
                        <div class="spinner-border spinner-border-sm" role="status">
                          <span class="visually-hidden">Approving...</span>
                        </div>
                      </a>

                      <!-- Show normal approve button when not processing -->
                      <a href="javascript:;"
                         class="action-icon"
                         (click)="approveLeave(leave)"
                         ngbTooltip="Approve"
                         *ngIf="!isProcessing(leave.id)">
                        <i data-feather="check" class="icon-sm" appFeatherIcon></i>
                      </a>
                    </ng-container>

                    <!-- Reject Button with Loading State -->
                    <ng-container *ngIf="leave.status === 'PENDING'">
                      <!-- Show loading spinner when rejecting -->
                      <a href="javascript:;"
                         class="action-icon text-danger"
                         *ngIf="isRejecting(leave.id)"
                         ngbTooltip="Rejecting...">
                        <div class="spinner-border spinner-border-sm" role="status">
                          <span class="visually-hidden">Rejecting...</span>
                        </div>
                      </a>

                      <!-- Show normal reject button when not processing -->
                      <a href="javascript:;"
                         class="action-icon"
                         (click)="rejectLeave(leave)"
                         ngbTooltip="Reject"
                         *ngIf="!isProcessing(leave.id)">
                        <i data-feather="x" class="icon-sm" appFeatherIcon></i>
                      </a>
                    </ng-container>

                    <!-- View Details Button (always available) -->
                    <a href="javascript:;"
                       class="action-icon"
                       (click)="viewLeaveDetails(leave)"
                       ngbTooltip="View Details">
                      <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                    </a>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty state message -->
        <div class="empty-state text-center py-5" *ngIf="filteredLeaves.length === 0">
          <i data-feather="calendar" class="icon-lg mb-3" appFeatherIcon></i>
          <p class="mb-0">No leave applications found</p>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-4" *ngIf="filteredLeaves.length > 0">
          <div>
            <span class="me-2">Page {{ page }} of {{ Math.ceil(collectionSize / pageSize) }}</span>
            <span>Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, collectionSize) }} of {{ collectionSize }} entries</span>
          </div>
          <ngb-pagination
            [collectionSize]="collectionSize"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            (pageChange)="onPageChange($event)"
          ></ngb-pagination>
        </div>
        </div> <!-- End main content conditional -->
      </div>
    </div>
  </div>
</div>