// Modern table card styling
.modern-table-card {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;

  .card-body {
    padding: 1.5rem;
  }

  .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #df5517;
  }
}

// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  thead {
    background-color: rgba(var(--bs-primary-rgb), 0.05);

    th {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 12px 10px;
      border-top: none;
      border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.08);
      }

      &.asc:after, &.desc:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      &.asc:after {
        border-bottom: 4px solid var(--bs-primary);
      }

      &.desc:after {
        border-top: 4px solid var(--bs-primary);
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.02);
      }

      td {
        vertical-align: middle;
        padding: 12px 10px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
      }
    }
  }
}

// Action icons styling
.action-icons {
  .action-icon {
    background: none;
    border: none;
    padding: 4px 6px;
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.2s;
    cursor: pointer;

    &:hover {
      background-color: rgba(var(--bs-primary-rgb), 0.1);
      transform: scale(1.1);
    }

    &.text-info:hover {
      background-color: rgba(var(--bs-info-rgb), 0.1);
    }

    &.text-success:hover {
      background-color: rgba(var(--bs-success-rgb), 0.1);
    }

    &.text-danger:hover {
      background-color: rgba(var(--bs-danger-rgb), 0.1);
    }
  }
}

// Badge styling
.badge {
  padding: 0.4em 0.8em;
  font-weight: 500;
  font-size: 0.75rem;

  &.rounded-pill {
    padding-left: 0.8em;
    padding-right: 0.8em;
  }
}

// Empty state styling
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #6c757d;

  i {
    color: #dee2e6;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  small {
    font-size: 0.875rem;
  }
}

// Data type selector styling
.dropdown {
  .dropdown-toggle {
    border-radius: 6px;
    font-weight: 500;
    
    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }

  .dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
    min-width: 280px;

    .dropdown-item {
      padding: 0.75rem 1rem;
      border-radius: 0;
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.05);
      }

      &.active {
        background-color: rgba(var(--bs-primary-rgb), 0.1);
        color: var(--bs-primary);
        font-weight: 500;
      }

      small {
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }
    }
  }
}

// Button group styling
.btn-group {
  .btn {
    border-radius: 6px;
  }
}

// Alert styling
.alert {
  border: none;
  border-radius: 8px;
  
  &.alert-info {
    background-color: rgba(var(--bs-info-rgb), 0.1);
    color: var(--bs-info);
    border-left: 4px solid var(--bs-info);
  }
}

// Modal styling
.modal {
  .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  }

  .modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;

    .modal-title {
      font-weight: 600;
      color: var(--bs-dark);
    }
  }

  .modal-body {
    padding: 1.5rem;
  }

  .modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
  }
}

// Form styling
.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid #e0e6ed;
  transition: all 0.2s;

  &:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
  }
}

.form-label {
  font-weight: 500;
  color: var(--bs-dark);
  margin-bottom: 0.5rem;
}

// Input group styling
.input-group {
  .input-group-text {
    border: 1px solid #e0e6ed;
    background-color: #f8f9fa;
    
    &.text-danger {
      cursor: pointer;
      
      &:hover {
        background-color: rgba(var(--bs-danger-rgb), 0.1);
      }
    }
  }
}

// Pagination styling
.pagination {
  .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #e0e6ed;
    color: var(--bs-dark);
    
    &:hover {
      background-color: rgba(var(--bs-primary-rgb), 0.1);
      border-color: var(--bs-primary);
    }
  }

  .page-item.active .page-link {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .modern-table-card .card-body {
    padding: 1rem;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-group {
    width: 100%;
    
    .btn {
      flex: 1;
    }
  }

  .dropdown .dropdown-menu {
    min-width: 100%;
  }
}

// Loading spinner
.spinner-border {
  &.spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}
