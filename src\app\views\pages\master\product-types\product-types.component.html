<!-- Product Types Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-package me-2"></i>
              Product Types Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_product_types }} total product types, 
              {{ statistics.active_product_types }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Product Type
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Product Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Product Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCategory" (change)="onCategoryFilter()">
                <option value="">All Categories</option>
                <option *ngFor="let category of productCategories" [value]="category.value">
                  {{ category.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedPricingModel" (change)="onPricingModelFilter()">
                <option value="">All Pricing Models</option>
                <option *ngFor="let model of pricingModels" [value]="model.value">
                  {{ model.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedSupportLevel" (change)="onSupportLevelFilter()">
                <option value="">All Support Levels</option>
                <option *ngFor="let level of supportLevels" [value]="level.value">
                  {{ level.label }}
                </option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading product types...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Product Details</th>
                  <th>Category & Pricing</th>
                  <th>Business Info</th>
                  <th>Support & Features</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let productType of getCurrentList(); trackBy: trackByProductTypeId">
                  <td>
                    <div>
                      <strong>{{ productType.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ productType.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="productType.description">
                        {{ productType.description }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <span [class]="getCategoryBadgeClass(productType.category)">
                      {{ getProductCategoryLabel(productType.category) }}
                    </span>
                    <small class="d-block text-muted mt-1">
                      <strong>Pricing:</strong> {{ getPricingModelLabel(productType.pricing_model) }}
                    </small>
                    <small class="d-block text-muted" *ngIf="productType.base_price">
                      <strong>Base Price:</strong> {{ formatPrice(productType.base_price, productType.currency) }}
                    </small>
                  </td>
                  <td>
                    <div class="business-info">
                      <small class="d-block text-muted" *ngIf="productType.lead_time_days">
                        <strong>Lead Time:</strong> {{ productType.lead_time_days }} days
                      </small>
                      <small class="d-block text-muted" *ngIf="productType.warranty_period_months">
                        <strong>Warranty:</strong> {{ productType.warranty_period_months }} months
                      </small>
                      <small class="d-block text-muted" *ngIf="productType.tax_category">
                        <strong>Tax:</strong> {{ productType.tax_category }}
                      </small>
                      <div class="mt-1" *ngIf="productType.inventory_tracking || productType.requires_approval || productType.is_customizable">
                        <span class="badge bg-info me-1" *ngIf="productType.inventory_tracking">Tracked</span>
                        <span class="badge bg-warning me-1" *ngIf="productType.requires_approval">Approval</span>
                        <span class="badge bg-secondary me-1" *ngIf="productType.is_customizable">Custom</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span [class]="getSupportLevelBadgeClass(productType.support_level)">
                      {{ getSupportLevelLabel(productType.support_level) }}
                    </span>
                    <small class="d-block text-muted mt-1" *ngIf="productType.specifications?.length">
                      <strong>Specs:</strong> {{ productType.specifications.length }} specifications
                    </small>
                    <small class="d-block text-muted" *ngIf="productType.sub_product_types_count">
                      <strong>Sub Types:</strong> {{ productType.sub_product_types_count }}
                    </small>
                    <div class="mt-1" *ngIf="productType.tags?.length">
                      <span class="badge bg-light text-dark me-1" *ngFor="let tag of productType.tags.slice(0, 2)">
                        {{ tag }}
                      </span>
                      <span class="badge bg-light text-dark" *ngIf="productType.tags.length > 2">
                        +{{ productType.tags.length - 2 }}
                      </span>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(productType.is_active)">
                      {{ getStatusText(productType.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ productType.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(productType)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteProductType(productType)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreProductType(productType)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-package text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Product Types' : 'No Product Types Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No product types have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No product types match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first product type.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Product Type
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} product types
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_product_types }}</h3>
                      <p class="mb-0">Total Product Types</p>
                    </div>
                    <i class="feather icon-package" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_product_types }}</h3>
                      <p class="mb-0">Active Product Types</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-secondary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_product_types }}</h3>
                      <p class="mb-0">Inactive Product Types</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
