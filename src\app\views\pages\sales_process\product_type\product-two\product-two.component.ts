import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges, ViewChild, AfterViewInit, ChangeDetectorRef, ViewChildren, QueryList } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgClass, CommonModule } from '@angular/common';
import {
  AddressType,
  JobProfileType,
  SalaryMode,
  LoanType,
  InvestmentMode,
  ProfessionType,
  BusinessType,
  RelationshipType
} from './product-two.model';
import { SalariedEmployeeComponent } from './salaried-employee/salaried-employee.component';
import { SelfEmployeeComponent } from './self-employee/self-employee.component';
import { ProductDataService } from '../../../../../core/services/product-data.service';

interface Product2 {
  dt: Date;
  leadSource: string;
  uniqueCode: string;
  productType: string; // HL/LAP/LRD
  nameOfCustomer: string;
  status: string; // SL/SE
  contactNo: string;
  mailId: string;
  loanAmount: number;
}

@Component({
  selector: 'app-product-two',
  templateUrl: './product-two.component.html',
  styleUrls: ['./product-two.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgClass,
    CommonModule,
    SalariedEmployeeComponent,
    SelfEmployeeComponent
  ]
})
export class ProductTwoComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() selectedProductSubType: string = '';

  // ViewChild references to access sub-component form data
  @ViewChild('salariedEmployeeRef', { static: false }) salariedEmployeeComponent?: SalariedEmployeeComponent;
  @ViewChild('selfEmployeeRef', { static: false }) selfEmployeeComponent?: SelfEmployeeComponent;

  // Alternative approach using ViewChildren
  @ViewChildren(SalariedEmployeeComponent) salariedEmployeeComponents!: QueryList<SalariedEmployeeComponent>;
  @ViewChildren(SelfEmployeeComponent) selfEmployeeComponents!: QueryList<SelfEmployeeComponent>;

  // Event emitter to communicate with parent component for sales submission
  @Output() submitSales = new EventEmitter<any>();

  product2List: Product2[] = [];
  product2Form: FormGroup;
  loanForm: FormGroup;
  isFormSubmitted: boolean = false;
  selectedStatus: string = ''; // No default selection

  // Property to detect if component is being used in edit mode (view-lead-details)
  isInEditMode: boolean = false;

  // Storage for additional form data (used by parent components)
  productFormData: any = {};

  // Flags to control alert visibility
  showExistingLoansAlert: boolean = true;
  showInvestmentsAlert: boolean = true;
  showCoApplicantsAlert: boolean = true;

  // Enums for dropdown options
  addressTypes = Object.values(AddressType);
  jobProfileTypes = Object.values(JobProfileType);
  salaryModes = Object.values(SalaryMode);
  loanTypes = Object.values(LoanType);
  investmentModes = Object.values(InvestmentMode);
  professionTypes = Object.values(ProfessionType);
  businessTypes = Object.values(BusinessType);
  relationshipTypes = Object.values(RelationshipType);

  constructor(
    private formBuilder: FormBuilder,
    private productDataService: ProductDataService,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Detect if component is being used in edit mode (view-lead-details context)
   * This is determined by checking if we're in a parent component that's using this for editing
   */
  private detectEditMode(): void {
    // Check if we're in the view-lead-details component context
    const currentUrl = window.location.href;
    const isViewLeadDetailsRoute = currentUrl.includes('/view-lead-details/');

    // Only set edit mode if we're actually in the view-lead-details route
    // Don't use selectedProductSubType as indicator since it's now passed in add sales context too
    this.isInEditMode = isViewLeadDetailsRoute;

    console.log('🔍 ProductTwo component edit mode detection:', {
      isInEditMode: this.isInEditMode,
      isViewLeadDetailsRoute: isViewLeadDetailsRoute,
      selectedProductSubType: this.selectedProductSubType,
      currentUrl: currentUrl
    });
  }

  /**
   * Update the product type field with the selected sub-product type
   */
  private updateProductTypeField(): void {
    if (!this.product2Form) {
      console.log('⚠️ ProductTwo form not initialized yet');
      return;
    }

    if (this.selectedProductSubType) {
      // Set the product type field to the selected sub-product type
      this.product2Form.get('productType')?.setValue(this.selectedProductSubType);

      // Make the field read-only when set from parent component
      this.product2Form.get('productType')?.disable();

      console.log('🔧 ProductTwo: Updated product type field to:', this.selectedProductSubType);
      console.log('🔧 ProductTwo: Form field value after update:', this.product2Form.get('productType')?.value);
    } else {
      // If no sub-product type is selected, enable the field for manual entry
      this.product2Form.get('productType')?.enable();
      console.log('🔧 ProductTwo: No sub-product type selected, enabling manual entry');
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedProductSubType']) {
      console.log('Product-two sub-type changed to:', this.selectedProductSubType);

      // Re-detect edit mode when selectedProductSubType changes
      this.detectEditMode();

      // Update the product type field with the selected sub-product type
      this.updateProductTypeField();
    }
    if (changes['selectedProductSubType'] && !changes['selectedProductSubType'].firstChange) {
      this.initializeFormBasedOnSubType();
    }
  }

  ngAfterViewInit(): void {
    // Ensure change detection runs after view initialization
    this.cdr.detectChanges();
    console.log('🔍 AfterViewInit - ViewChild components status:');
    console.log('🔍 Salaried component available (ViewChild):', !!this.salariedEmployeeComponent);
    console.log('🔍 Self-employed component available (ViewChild):', !!this.selfEmployeeComponent);
    console.log('🔍 Salaried components available (ViewChildren):', this.salariedEmployeeComponents?.length || 0);
    console.log('🔍 Self-employed components available (ViewChildren):', this.selfEmployeeComponents?.length || 0);
  }

  ngOnInit() {
    console.log('Product-two component initialized with sub-type:', this.selectedProductSubType);

    // Detect if component is being used in edit mode (view-lead-details context)
    this.detectEditMode();

    this.initializeFormBasedOnSubType();
    this.initForm();
    this.initLoanForm();

    // Update the product type field with the selected sub-product type
    this.updateProductTypeField();

    // Add status change listener to product2Form
    this.product2Form.get('status')?.valueChanges.subscribe(status => {
      this.selectedStatus = status;
      this.updateFieldsBasedOnStatus(status);
    });

    // Subscribe to product type changes from the service (fallback)
    this.productDataService.productType$.subscribe(productType => {
      // Only use service data if no selectedProductSubType is provided
      if (productType && !this.selectedProductSubType) {
        // Update the product type field in the form with the actual product type name
        this.product2Form.get('productType')?.setValue(productType);

        // Make the product type field read-only
        this.product2Form.get('productType')?.disable();

        console.log('🔧 ProductTwo: Set product type from service to:', productType);
        console.log('🔧 ProductTwo: Form field value after update:', this.product2Form.get('productType')?.value);
      } else if (!productType && !this.selectedProductSubType) {
        // If no product type is selected, enable the product type field
        this.product2Form.get('productType')?.enable();
        console.log('🔧 ProductTwo: No product type received, enabling field');
      }
    });
    
    // Subscribe to lead category and source changes
    this.productDataService.leadCategory$.subscribe(leadCategory => {
      if (leadCategory) {
        // If lead category is 'Associate', set lead source to 'Associate'
        if (leadCategory === 'Associate') {
          this.product2Form.get('leadSource')?.setValue('Associate');
        }
      }
    });
    
    this.productDataService.leadSource$.subscribe(leadSource => {
      if (leadSource) {
        // Update the lead source field in the form
        this.product2Form.get('leadSource')?.setValue(leadSource);
      }
    });

  }



  private initializeFormBasedOnSubType(): void {
    if (this.selectedProductSubType === 'Home Loan') {
      this.initHLSalariedForm();
    } else if (this.selectedProductSubType === 'LAP' || this.selectedProductSubType === 'LRD') {
      this.initLapLrdForm();
    }
  }

  private initHLSalariedForm(): void {
    // Initialize Home Loan form fields and validations
    // This method should be implemented with your specific HL form requirements
  }

  private initLapLrdForm(): void {
    // Initialize LAP/LRD form fields and validations
    // This method should be implemented with your specific LAP/LRD form requirements
  }

  initForm() {
    const currentDate = new Date();
    // Format date as DD/MM/YYYY
    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()}`;
    
    this.product2Form = this.formBuilder.group({
      dt: [{ value: formattedDate, disabled: true }],
      leadSource: [{ value: '', disabled: true }],
      uniqueCode: [{ value: '', disabled: true }],
      productType: ['', Validators.required], // Will be set by updateProductTypeField method
      nameOfCustomer: ['', Validators.required],
      status: ['', Validators.required], // No default value, user must select
      contactNo: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      mailId: ['', [Validators.required, Validators.email]],
      loanAmount: [0, [Validators.required, Validators.min(0)]]
    });
  }

  // Getter for easy access to form fields
  get form() {
    return this.product2Form.controls;
  }

  // Getter for loan form fields
  get loanFormControls() {
    return this.loanForm.controls;
  }

  // Getters for form arrays
  get existingLoans() {
    return this.loanForm.get('existingLoans') as FormArray;
  }

  get investments() {
    return this.loanForm.get('investments') as FormArray;
  }

  get coApplicants() {
    return this.loanForm.get('coApplicants') as FormArray;
  }

  onSubmit() {
    console.log('🔥 Product2 onSubmit triggered!');
    this.isFormSubmitted = true;

    // Check form validity
    console.log('🔍 Product2 form validity:', this.product2Form.valid);
    console.log('🔍 Product2 form errors:', this.product2Form.errors);
    console.log('🔍 Product2 form value:', this.product2Form.value);

    // Stop here if form is invalid
    if (this.product2Form.invalid) {
      console.log('❌ Product2 form is invalid, stopping submission');
      console.log('🔍 Invalid fields:', this.getInvalidFields());
      return;
    }

    console.log('✅ Product2 form is valid, proceeding with submission');

    // Ensure ViewChild components are detected before getting form data
    setTimeout(() => {
      // Get all form data including loan form data
      const productFormData = this.getFormData();

      console.log('✅ Product2 form submitted, emitting to parent:', productFormData);

      // Emit event to parent component to trigger sales API submission
      this.submitSales.emit(productFormData);

      // Note: Form reset and local list management will be handled by parent component
      // after successful API submission to maintain consistency with the overall flow
    }, 100); // Small delay to ensure ViewChild components are available
  }

  // Helper method to get invalid fields for debugging
  private getInvalidFields(): string[] {
    const invalidFields: string[] = [];
    Object.keys(this.product2Form.controls).forEach(key => {
      const control = this.product2Form.get(key);
      if (control && control.invalid) {
        invalidFields.push(key);
      }
    });
    return invalidFields;
  }

  onCancel() {
    // Reset the form
    this.product2Form.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values

    // Reset loan form
    this.resetLoanForm();

    console.log('✅ Product2 form cancelled and reset');
  }

  /**
   * Reset form after successful submission
   * Called by parent component after successful API submission
   */
  resetFormAfterSubmission(): void {
    console.log('🔄 Resetting Product2 form after successful submission');

    // Reset the form
    this.product2Form.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values

    // Update product type field with selected sub-product type
    this.updateProductTypeField();

    // Reset loan form
    this.resetLoanForm();

    // Reset selected status
    this.selectedStatus = '';

    console.log('✅ Product2 form reset completed');
  }

  /**
   * Populate form with existing data for editing
   * This method is called by the parent component when entering edit mode
   */
  populateFormData(formData: any): void {
    if (!formData || !this.product2Form) {
      console.warn('⚠️ No form data provided or form not initialized');
      return;
    }

    console.log('🔧 Populating ProductTwo form with data:', formData);
    console.log('🔍 Form data structure:', {
      hasStatus: !!formData.status,
      hasLoanDetails: !!formData.loan_details,
      hasSalariedDetails: !!formData.salaried_employee_details,
      hasSelfEmployedDetails: !!formData.self_employed_details,
      status: formData.status
    });

    try {
      // Populate main form
      this.product2Form.patchValue(formData);

      // Update product type field with selected sub-product type (override any form data)
      this.updateProductTypeField();

      // Set selected status to trigger sub-component visibility
      if (formData.status) {
        this.selectedStatus = formData.status;
        console.log('🔧 Setting selected status to:', this.selectedStatus);

        // Update form status field and trigger change detection
        this.product2Form.get('status')?.setValue(formData.status);
        this.updateFieldsBasedOnStatus(formData.status);

        // Force change detection to ensure sub-components are rendered
        this.cdr.detectChanges();
      }

      // Populate loan form if available
      if (formData.loan_details && this.loanForm) {
        console.log('🔧 Populating loan form with data');
        this.loanForm.patchValue(formData.loan_details);
      }

      // Populate sub-component data with multiple attempts to ensure components are available
      this.scheduleSubComponentPopulation(formData, 0);

      console.log('✅ ProductTwo form populated successfully');
    } catch (error) {
      console.error('❌ Error populating ProductTwo form:', error);
    }
  }

  /**
   * Schedule sub-component population with retry mechanism
   */
  private scheduleSubComponentPopulation(formData: any, attempt: number): void {
    const maxAttempts = 5;
    const delay = 100 + (attempt * 100); // Increasing delay

    setTimeout(() => {
      console.log(`🔧 Attempting sub-component population (attempt ${attempt + 1}/${maxAttempts})`);

      const success = this.populateSubComponentData(formData);

      if (success === false && attempt < maxAttempts - 1) {
        console.log(`⚠️ Sub-component not ready, retrying in ${delay}ms...`);
        this.scheduleSubComponentPopulation(formData, attempt + 1);
      } else if (success === false) {
        console.warn('❌ Failed to populate sub-component data after all attempts');
      }
    }, delay);
  }

  /**
   * Populate sub-component data (salaried/self-employed)
   * @returns boolean indicating whether population was successful
   */
  private populateSubComponentData(formData: any): boolean {
    console.log('🔍 Attempting to populate sub-component data with status:', this.selectedStatus);
    console.log('🔍 Sub-component availability:', {
      salariedComponent: !!this.salariedEmployeeComponent,
      salariedComponentsCount: this.salariedEmployeeComponents?.length || 0,
      selfEmployeeComponent: !!this.selfEmployeeComponent,
      selfEmployeeComponentsCount: this.selfEmployeeComponents?.length || 0
    });

    // Force change detection to ensure components are available
    this.cdr.detectChanges();

    // Check for salaried employee data
    if (this.selectedStatus === 'SL') {
      console.log('🔍 Checking for salaried employee data:', {
        hasSalariedDetails: !!formData.salaried_employee_details,
        mainApplicantData: !!formData.salaried_employee_details?.mainApplicant,
        coApplicantsData: !!formData.salaried_employee_details?.coApplicants
      });

      const salariedComponent = this.salariedEmployeeComponent || this.salariedEmployeeComponents?.first;
      if (salariedComponent && salariedComponent.populateFormData) {
        // If salaried_employee_details is missing but we have mainApplicant directly, create a wrapper
        let salariedData = formData.salaried_employee_details;
        if (!salariedData && formData.mainApplicant) {
          salariedData = { mainApplicant: formData.mainApplicant };
          if (formData.coApplicants) {
            salariedData.coApplicants = formData.coApplicants;
          }
          console.log('🔧 Created salaried data wrapper from direct fields');
        }

        // If we have co-applicants in the productFormData, include them
        if (!salariedData) {
          salariedData = {};
        }

        if (this.productFormData && this.productFormData.coApplicants && !salariedData.coApplicants) {
          salariedData.coApplicants = this.productFormData.coApplicants;
          console.log('🔧 Added co-applicants from productFormData to salaried data');
        }

        if (salariedData) {
          salariedComponent.populateFormData(salariedData);
          console.log('✅ Populated salaried employee data successfully');
          return true;
        } else {
          console.warn('⚠️ No salaried employee data available to populate');
        }
      } else {
        console.warn('⚠️ Salaried component not available yet');
        return false;
      }
    }
    // Check for self-employed data
    else if (this.selectedStatus === 'SE') {
      console.log('🔍 Checking for self-employed data:', {
        hasSelfEmployedDetails: !!formData.self_employed_details,
        mainApplicantData: !!formData.self_employed_details?.mainApplicant,
        coApplicantsData: !!formData.self_employed_details?.coApplicants
      });

      const selfEmployeeComponent = this.selfEmployeeComponent || this.selfEmployeeComponents?.first;
      if (selfEmployeeComponent && selfEmployeeComponent.populateFormData) {
        // If self_employed_details is missing but we have mainApplicant directly, create a wrapper
        let selfEmployedData = formData.self_employed_details;
        if (!selfEmployedData && formData.mainApplicant) {
          selfEmployedData = { mainApplicant: formData.mainApplicant };
          if (formData.coApplicants) {
            selfEmployedData.coApplicants = formData.coApplicants;
          }
          console.log('🔧 Created self-employed data wrapper from direct fields');
        }

        // If we have co-applicants in the productFormData, include them
        if (!selfEmployedData) {
          selfEmployedData = {};
        }

        if (this.productFormData && this.productFormData.coApplicants && !selfEmployedData.coApplicants) {
          selfEmployedData.coApplicants = this.productFormData.coApplicants;
          console.log('🔧 Added co-applicants from productFormData to self-employed data');
        }

        if (selfEmployedData) {
          selfEmployeeComponent.populateFormData(selfEmployedData);
          console.log('✅ Populated self-employed data successfully');
          return true;
        } else {
          console.warn('⚠️ No self-employed data available to populate');
        }
      } else {
        console.warn('⚠️ Self-employed component not available yet');
        return false;
      }
    } else {
      console.warn('⚠️ No status selected or unrecognized status:', this.selectedStatus);
    }

    return false;
  }

  editProduct(index: number) {
    // Find the product by index and populate the form
    const product = this.product2List[index];
    if (product) {
      this.product2Form.patchValue(product);
    }
  }

  deleteProduct(index: number) {
    this.product2List.splice(index, 1);
  }

  // Loan Form Methods
  initLoanForm() {
    // Reset alert visibility flags
    this.showExistingLoansAlert = true;
    this.showInvestmentsAlert = true;
    this.showCoApplicantsAlert = true;
    this.loanForm = this.formBuilder.group({
      // Personal Information
      age: ['', [Validators.required, Validators.min(18), Validators.max(100)]],
      address: ['', Validators.required],
      addressType: ['Owned', Validators.required],
      status: [''],

      // Employment/Business Information
      profession: ['Salaried', Validators.required],
      jobProfile: ['Private', Validators.required],
      typeOfBusiness: [''],
      companyName: ['', Validators.required],
      businessPlace: [''],
      currentJobExperience: ['', [Validators.min(0)]],
      totalPastJobExperience: ['', [Validators.min(0)]],
      currentBusinessExperience: ['', [Validators.min(0)]],
      totalPastBusinessExperience: ['', [Validators.min(0)]],

      // Business Financial Information
      monthlyTurnover: ['', [Validators.min(0)]],
      monthlyAvgExpense: ['', [Validators.min(0)]],
      monthlyNetProfit: ['', [Validators.min(0)]],
      itrStatus: [''],

      // Fiscal Year Information
      fyYear1: ['22-23'],
      fyYear1Gross: ['', [Validators.min(0)]],
      fyYear1Net: ['', [Validators.min(0)]],
      fyYear1Date: [''],

      fyYear2: ['23-24'],
      fyYear2Gross: ['', [Validators.min(0)]],
      fyYear2Net: ['', [Validators.min(0)]],
      fyYear2Date: [''],

      fyYear3: ['24-25'],
      fyYear3Gross: ['', [Validators.min(0)]],
      fyYear3Net: ['', [Validators.min(0)]],
      fyYear3Date: [''],

      otherIncomeDetails: [''],
      grossIncome: ['', [Validators.min(0)]],

      // Banking Information
      currentAccount: [''],
      currentAccountBankName: [''],
      savingAccount: [''],
      savingAccountBankName: [''],
      businessStatutoryDoc: [''],

      // Document Information
      appointmentLetter: [false],
      confirmationLetter: [false],
      salarySlip: [false],
      salaryCertificate: [false],

      // Salary Information
      totalGrossSalary: ['', [Validators.required, Validators.min(0)]],

      // Deductions
      deductionPF: ['', Validators.min(0)],
      deductionPT: ['', Validators.min(0)],
      deductionHRA: ['', Validators.min(0)],
      deductionESIC: ['', Validators.min(0)],
      deductionEmpLoan: ['', Validators.min(0)],
      deductionSocLoan: ['', Validators.min(0)],
      deductionOther: ['', Validators.min(0)],
      totalDeduction: [{value: 0, disabled: true}],
      netSalary: [{value: 0, disabled: true}],

      // Tax Information
      itrAvailable: [false],
      formNo16: [false],

      // Property Information
      propertySelected: [false],
      propertyLocation: [''],
      propertyType: [''],
      propertyDetails: [''],
      propertyArea: ['', Validators.min(0)],
      propertyConfiguration: [''],
      propertyStatus: [''],
      ucPercentage: ['', [Validators.min(0), Validators.max(100)]],
      propertyAge: ['', Validators.min(0)],
      societyStatus: [''],
      ccOc: [''],
      agreementValue: ['', Validators.min(0)],
      purchaseDate: [''],
      currentMarketValue: [5000000, Validators.min(0)],
      loanAmountRequired: [2500000, Validators.min(0)],
      ltvPercentage: [50, [Validators.min(0), Validators.max(100)]],
      ocr: ['', Validators.min(0)],
      ocrPercentage: ['', [Validators.min(0), Validators.max(100)]],
      bankLoanAmount: ['', Validators.min(0)],
      projectAPF: [''],

      // Existing Loans
      existingLoans: this.formBuilder.array([]),

      // Investments
      investments: this.formBuilder.array([]),

      // Co Applicants
      coApplicants: this.formBuilder.array([])
    });

    // Add watchers for calculating total deduction and net salary
    this.watchDeductionChanges();
  }

  // Method to add a new existing loan
  addExistingLoan() {
    // Hide the alert message
    this.showExistingLoansAlert = false;

    const loanForm = this.formBuilder.group({
      loanType: ['PL', Validators.required],
      bankName: ['', Validators.required],
      sanctionedAmount: ['', [Validators.required, Validators.min(0)]],
      disbursedAmount: ['', [Validators.required, Validators.min(0)]],
      outstandingAmount: ['', [Validators.required, Validators.min(0)]],
      startDate: ['', Validators.required],
      tenure: ['', [Validators.required, Validators.min(1)]],
      emiAmount: ['', [Validators.required, Validators.min(0)]],
      defaulted: [false]
    });

    this.existingLoans.push(loanForm);
  }

  // Method to remove an existing loan
  removeExistingLoan(index: number) {
    this.existingLoans.removeAt(index);

    // Show the alert again if all loans are removed
    if (this.existingLoans.length === 0) {
      this.showExistingLoansAlert = true;
    }
  }

  // Method to add a new investment
  addInvestment() {
    // Hide the alert message
    this.showInvestmentsAlert = false;

    const investmentForm = this.formBuilder.group({
      instituteName: ['', Validators.required],
      investmentProduct: ['', Validators.required],
      yearlyAmount: ['', [Validators.required, Validators.min(0)]],
      investmentMode: ['Monthly', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      currentSavingAmount: ['', [Validators.required, Validators.min(0)]]
    });

    this.investments.push(investmentForm);
  }

  // Method to remove an investment
  removeInvestment(index: number) {
    this.investments.removeAt(index);

    // Show the alert again if all investments are removed
    if (this.investments.length === 0) {
      this.showInvestmentsAlert = true;
    }
  }

  // Method to add a new co-applicant
  addCoApplicant() {
    // Hide the alert message
    this.showCoApplicantsAlert = false;

    const coApplicantForm = this.formBuilder.group({
      name: ['', Validators.required],
      relationship: ['Spouse', Validators.required],
      age: ['', [Validators.required, Validators.min(18), Validators.max(100)]],
      profession: ['Salaried', Validators.required],
      monthlyIncome: ['', [Validators.required, Validators.min(0)]],
      contactNo: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      address: ['', Validators.required]
    });

    this.coApplicants.push(coApplicantForm);
  }

  // Method to remove a co-applicant
  removeCoApplicant(index: number) {
    this.coApplicants.removeAt(index);

    // Show the alert again if all co-applicants are removed
    if (this.coApplicants.length === 0) {
      this.showCoApplicantsAlert = true;
    }
  }

  // Method to watch for changes in deduction fields and calculate totals
  watchDeductionChanges() {
    const deductionFields = [
      'deductionPF', 'deductionPT', 'deductionHRA', 'deductionESIC',
      'deductionEmpLoan', 'deductionSocLoan', 'deductionOther'
    ];

    // Watch each deduction field
    deductionFields.forEach(field => {
      this.loanForm.get(field)?.valueChanges.subscribe(() => {
        this.calculateTotalDeduction();
      });
    });

    // Watch gross salary for net salary calculation
    this.loanForm.get('totalGrossSalary')?.valueChanges.subscribe(() => {
      this.calculateNetSalary();
    });
  }

  // Calculate total deduction
  calculateTotalDeduction() {
    const deductionFields = [
      'deductionPF', 'deductionPT', 'deductionHRA', 'deductionESIC',
      'deductionEmpLoan', 'deductionSocLoan', 'deductionOther'
    ];

    let totalDeduction = 0;

    deductionFields.forEach(field => {
      const value = this.loanForm.get(field)?.value || 0;
      totalDeduction += parseFloat(value) || 0;
    });

    this.loanForm.get('totalDeduction')?.setValue(totalDeduction);
    this.calculateNetSalary();
  }

  // Calculate net salary
  calculateNetSalary() {
    const grossSalary = parseFloat(this.loanForm.get('totalGrossSalary')?.value) || 0;
    const totalDeduction = parseFloat(this.loanForm.get('totalDeduction')?.value) || 0;
    const netSalary = grossSalary - totalDeduction;

    this.loanForm.get('netSalary')?.setValue(netSalary);
  }

  // Update field visibility and validation based on status
  updateFieldsBasedOnStatus(status: string) {
    // Set initial status in loan form
    this.loanForm.get('status')?.setValue(status === 'SL' ? 'Salaried' : 'Self-Employed');

    if (status === 'SL') { // Salaried
      // Enable and set required validators for SL fields
      this.loanForm.get('totalGrossSalary')?.setValidators([Validators.required, Validators.min(0)]);
      this.loanForm.get('currentJobExperience')?.setValidators([Validators.required, Validators.min(0)]);
      this.loanForm.get('companyName')?.setValidators([Validators.required]);
      this.loanForm.get('jobProfile')?.setValidators([Validators.required]);

      // Disable SE-specific fields
      this.loanForm.get('typeOfBusiness')?.clearValidators();
      this.loanForm.get('monthlyTurnover')?.clearValidators();
      this.loanForm.get('monthlyAvgExpense')?.clearValidators();
      this.loanForm.get('monthlyNetProfit')?.clearValidators();
      this.loanForm.get('currentBusinessExperience')?.clearValidators();
      this.loanForm.get('businessPlace')?.clearValidators();
    }
    else if (status === 'SE') { // Self Employed
      // Enable and set required validators for SE fields
      this.loanForm.get('typeOfBusiness')?.setValidators([Validators.required]);
      this.loanForm.get('monthlyTurnover')?.setValidators([Validators.required, Validators.min(0)]);
      this.loanForm.get('monthlyAvgExpense')?.setValidators([Validators.required, Validators.min(0)]);
      this.loanForm.get('monthlyNetProfit')?.setValidators([Validators.required, Validators.min(0)]);
      this.loanForm.get('currentBusinessExperience')?.setValidators([Validators.required, Validators.min(0)]);
      this.loanForm.get('businessPlace')?.setValidators([Validators.required]);

      // Disable SL-specific fields
      this.loanForm.get('totalGrossSalary')?.clearValidators();
      this.loanForm.get('currentJobExperience')?.clearValidators();
      this.loanForm.get('jobProfile')?.clearValidators();
    }

    // Update all form controls validity
    this.loanForm.get('totalGrossSalary')?.updateValueAndValidity();
    this.loanForm.get('currentJobExperience')?.updateValueAndValidity();
    this.loanForm.get('companyName')?.updateValueAndValidity();
    this.loanForm.get('jobProfile')?.updateValueAndValidity();
    this.loanForm.get('typeOfBusiness')?.updateValueAndValidity();
    this.loanForm.get('monthlyTurnover')?.updateValueAndValidity();
    this.loanForm.get('monthlyAvgExpense')?.updateValueAndValidity();
    this.loanForm.get('monthlyNetProfit')?.updateValueAndValidity();
    this.loanForm.get('currentBusinessExperience')?.updateValueAndValidity();
    this.loanForm.get('businessPlace')?.updateValueAndValidity();
  }

  // Submit loan form
  onLoanFormSubmit() {
    this.isFormSubmitted = true;

    // Stop here if form is invalid
    if (this.loanForm.invalid) {
      return;
    }

    // Form submitted successfully

    // Reset the form
    this.loanForm.reset();
    this.isFormSubmitted = false;

    // Clear form arrays
    while (this.existingLoans.length) {
      this.existingLoans.removeAt(0);
    }

    while (this.investments.length) {
      this.investments.removeAt(0);
    }

    while (this.coApplicants.length) {
      this.coApplicants.removeAt(0);
    }

    this.initLoanForm();
  }

  // Reset loan form
  resetLoanForm() {
    // Reset the form
    this.loanForm.reset();

    // Clear form arrays
    while (this.existingLoans.length) {
      this.existingLoans.removeAt(0);
    }

    while (this.investments.length) {
      this.investments.removeAt(0);
    }

    while (this.coApplicants.length) {
      this.coApplicants.removeAt(0);
    }

    this.initLoanForm();
  }

  /**
   * Get all form data including loan form data
   * This method is called by the parent component to capture product-specific data
   */
  getFormData(): any {
    if (!this.product2Form) {
      console.warn('⚠️ Product2 form is not initialized');
      return {};
    }

    // Get all form values including disabled fields
    const formData = this.product2Form.getRawValue();

    // Add component metadata
    const productFormData = {
      ...formData,
      component_type: 'product-two',
      product_type: 'Retail Syndication',
      form_submission_date: new Date().toISOString()
    };

    // Include loan form data if available and valid
    if (this.loanForm && this.loanForm.valid) {
      productFormData.loan_details = this.loanForm.getRawValue();
      console.log('✅ Including loan form data in Product2 submission');
    } else if (this.loanForm) {
      console.warn('⚠️ Loan form is invalid, not including in submission');
    }

    // Include sub-component data based on selected status
    console.log('🔍 Checking sub-component data capture. Selected status:', this.selectedStatus);

    // Force change detection to ensure ViewChild components are available
    this.cdr.detectChanges();

    console.log('🔍 Salaried component available (ViewChild):', !!this.salariedEmployeeComponent);
    console.log('🔍 Self-employed component available (ViewChild):', !!this.selfEmployeeComponent);
    console.log('🔍 Salaried components available (ViewChildren):', this.salariedEmployeeComponents?.length || 0);
    console.log('🔍 Self-employed components available (ViewChildren):', this.selfEmployeeComponents?.length || 0);

    // Try ViewChild first, then fallback to ViewChildren
    const salariedComponent = this.salariedEmployeeComponent || this.salariedEmployeeComponents?.first;
    const selfEmployeeComponent = this.selfEmployeeComponent || this.selfEmployeeComponents?.first;

    if (this.selectedStatus === 'SL' && salariedComponent) {
      try {
        const salariedData = salariedComponent.getFormData();
        console.log('📊 Salaried employee data captured:', salariedData);
        if (salariedData && Object.keys(salariedData).length > 0) {
          productFormData.salaried_employee_details = salariedData;
          console.log('✅ Including salaried employee data in Product2 submission');
        } else {
          console.warn('⚠️ Salaried employee data is empty or invalid');
        }
      } catch (error) {
        console.warn('⚠️ Error capturing salaried employee data:', error);
      }
    } else if (this.selectedStatus === 'SE' && selfEmployeeComponent) {
      try {
        const selfEmployedData = selfEmployeeComponent.getFormData();
        console.log('📊 Self-employed data captured:', selfEmployedData);
        if (selfEmployedData && Object.keys(selfEmployedData).length > 0) {
          productFormData.self_employed_details = selfEmployedData;
          console.log('✅ Including self-employed data in Product2 submission');
        } else {
          console.warn('⚠️ Self-employed data is empty or invalid');
        }
      } catch (error) {
        console.warn('⚠️ Error capturing self-employed data:', error);
      }
    } else {
      console.log('ℹ️ No sub-component data to capture or component not available');
      console.log('🔍 Debug info - selectedStatus:', this.selectedStatus);
      console.log('🔍 Debug info - form status value:', this.product2Form.get('status')?.value);

      // Additional debugging: Check if the components exist but ViewChild failed
      if (this.selectedStatus === 'SL') {
        console.log('⚠️ Expected salaried component but ViewChild is null');
      } else if (this.selectedStatus === 'SE') {
        console.log('⚠️ Expected self-employed component but ViewChild is null');
      }
    }

    console.log('📋 Product2 form data captured:', productFormData);
    return productFormData;
  }
}