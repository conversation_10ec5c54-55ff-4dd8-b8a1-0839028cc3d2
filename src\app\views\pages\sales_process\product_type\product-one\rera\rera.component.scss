// RERA Component Styles

// Section headings
h6.text-primary {
  color: #3F828B !important;
  font-weight: 500;
  border-left: 3px solid #df5316;
  padding-left: 10px;
}

// Form controls
.form-control, .form-select {
  border-color: #e9ecef;

  &:focus {
    border-color: #df5316;
    box-shadow: 0 0 0 0.2rem rgba(223, 83, 22, 0.25);
  }
}



// Remove spinners from number inputs
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}