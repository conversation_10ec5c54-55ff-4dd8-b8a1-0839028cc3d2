import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

// New Year Activity interfaces
export interface NewYearActivity {
  id?: string;
  activity_name: string;
  date: string;
  description?: string;
  department?: string;
  status: 'planned' | 'ongoing' | 'completed' | 'cancelled';
  created_at?: string;
  updated_at?: string;
  // Excel/Holiday fields for compatibility
  holiday?: string;
  holiday_month?: string;
  holiday_day?: string;
  holiday_date?: string;
}

export interface NewYearActivityCreate {
  activity_name: string;
  date: string;
  description?: string;
  department?: string;
  status?: 'planned' | 'ongoing' | 'completed' | 'cancelled';
  // Excel/Holiday fields
  holiday?: string;
  holiday_month?: string;
  holiday_day?: string;
  holiday_date?: string;
}

export interface NewYearActivityUpdate {
  activity_name?: string;
  date?: string;
  description?: string;
  department?: string;
  status?: 'planned' | 'ongoing' | 'completed' | 'cancelled';
  // Excel/Holiday fields
  holiday?: string;
  holiday_month?: string;
  holiday_day?: string;
  holiday_date?: string;
}

export interface BulkUploadResponse {
  total: number;
  success: NewYearActivity[];
  errors: Array<{
    row: number;
    error: string;
    data?: any;
  }>;
}

export interface ActivityCount {
  total: number;
  planned: number;
  ongoing: number;
  completed: number;
  cancelled: number;
}

@Injectable({
  providedIn: 'root'
})
export class NewYearActivityService {
  private baseUrl = `${environment.apiUrl}/api/v1/new-year-activity`;

  constructor(private http: HttpClient) {}

  // Get all activities
  getAllActivities(page?: number, size?: number, search?: string): Observable<any> {
    let params = new HttpParams();
    
    if (page !== undefined) {
      params = params.set('page', page.toString());
    }
    if (size !== undefined) {
      params = params.set('size', size.toString());
    }
    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<any>(`${this.baseUrl}/`, { params });
  }

  // Create new activity
  createActivity(activity: NewYearActivityCreate): Observable<NewYearActivity> {
    return this.http.post<NewYearActivity>(`${this.baseUrl}/`, activity);
  }

  // Get activity by ID
  getActivity(activityId: string): Observable<NewYearActivity> {
    return this.http.get<NewYearActivity>(`${this.baseUrl}/${activityId}`);
  }

  // Update activity
  updateActivity(activityId: string, activity: NewYearActivityUpdate): Observable<NewYearActivity> {
    return this.http.put<NewYearActivity>(`${this.baseUrl}/${activityId}`, activity);
  }

  // Delete activity
  deleteActivity(activityId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${activityId}`);
  }

  // Bulk upload activities from file
  bulkUploadActivities(file: File): Observable<BulkUploadResponse> {
    console.log('NewYearActivityService: Starting bulk upload to:', `${this.baseUrl}/bulk-upload`);
    console.log('NewYearActivityService: File details:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/bulk-upload`, formData);
  }

  // Export activities to Excel
  exportActivities(format: 'excel' | 'csv' = 'excel'): Observable<Blob> {
    const params = new HttpParams().set('format', format);
    
    return this.http.get(`${this.baseUrl}/export`, {
      params,
      responseType: 'blob'
    });
  }

  // Get activities count
  getActivitiesCount(): Observable<ActivityCount> {
    return this.http.get<ActivityCount>(`${this.baseUrl}/count`);
  }

  // Helper method to download exported file
  downloadExportedFile(blob: Blob, filename: string = 'new_year_activities'): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Generate CSV template for bulk upload
  generateTemplate(): string {
    return [
      'Activity Name,Date,Description,Department,Status',
      'New Year Celebration,2024-01-01,Company new year celebration event,HR,planned',
      'Goal Setting Session,2024-01-02,Annual goal setting for all departments,Management,planned',
      'Team Building Activity,2024-01-15,Team building exercises for new year,HR,planned',
      'Performance Review,2024-01-20,Annual performance review sessions,HR,planned',
      'Training Workshop,2024-01-25,Skills development workshop,Training,planned'
    ].join('\n');
  }
}
