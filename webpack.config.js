const path = require('path');

/**
 * Advanced Webpack Configuration for Code Splitting Optimization
 * 
 * This configuration implements intelligent code splitting strategies
 * to optimize bundle loading and caching efficiency.
 */
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 500000,
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      automaticNameDelimiter: '-',
      cacheGroups: {
        // Angular Core Framework
        'angular-core': {
          test: /[\\/]node_modules[\\/]@angular[\\/](core|common|platform-browser|platform-browser-dynamic)[\\/]/,
          name: 'angular-core',
          priority: 30,
          chunks: 'all',
          minSize: 20000,
          maxSize: 500000,
          reuseExistingChunk: true
        },
        
        // Angular Forms
        'angular-forms': {
          test: /[\\/]node_modules[\\/]@angular[\\/](forms|reactive-forms)[\\/]/,
          name: 'angular-forms',
          priority: 25,
          chunks: 'all',
          minSize: 10000,
          maxSize: 200000,
          reuseExistingChunk: true
        },
        
        // Angular Router
        'angular-router': {
          test: /[\\/]node_modules[\\/]@angular[\\/]router[\\/]/,
          name: 'angular-router',
          priority: 25,
          chunks: 'all',
          minSize: 10000,
          maxSize: 200000,
          reuseExistingChunk: true
        },
        
        // Angular Animations & CDK
        'angular-animations': {
          test: /[\\/]node_modules[\\/]@angular[\\/](animations|cdk)[\\/]/,
          name: 'angular-animations',
          priority: 20,
          chunks: 'all',
          minSize: 10000,
          maxSize: 300000,
          reuseExistingChunk: true
        },
        
        // Angular Material (async loading)
        'angular-material': {
          test: /[\\/]node_modules[\\/]@angular[\\/]material[\\/]/,
          name: 'angular-material',
          priority: 15,
          chunks: 'async',
          minSize: 10000,
          maxSize: 400000,
          reuseExistingChunk: true
        },
        
        // Bootstrap & UI Framework
        'bootstrap': {
          test: /[\\/]node_modules[\\/](@ng-bootstrap|bootstrap)[\\/]/,
          name: 'bootstrap',
          priority: 20,
          chunks: 'all',
          minSize: 10000,
          maxSize: 200000,
          reuseExistingChunk: true
        },
        
        // RxJS
        'rxjs': {
          test: /[\\/]node_modules[\\/]rxjs[\\/]/,
          name: 'rxjs',
          priority: 25,
          chunks: 'all',
          minSize: 10000,
          maxSize: 150000,
          reuseExistingChunk: true
        },
        
        // Charts (async loading)
        'charts': {
          test: /[\\/]node_modules[\\/](apexcharts|ng-apexcharts)[\\/]/,
          name: 'charts',
          priority: 10,
          chunks: 'async',
          minSize: 10000,
          maxSize: 300000,
          reuseExistingChunk: true
        },
        
        // UI Libraries (async loading)
        'ui-libraries': {
          test: /[\\/]node_modules[\\/](sweetalert2|@ng-select|ngx-|@fullcalendar)[\\/]/,
          name: 'ui-libraries',
          priority: 15,
          chunks: 'async',
          minSize: 10000,
          maxSize: 250000,
          reuseExistingChunk: true
        },
        
        // Utilities
        'utilities': {
          test: /[\\/]node_modules[\\/](lodash|moment|date-fns|uuid)[\\/]/,
          name: 'utilities',
          priority: 10,
          chunks: 'all',
          minSize: 5000,
          maxSize: 100000,
          reuseExistingChunk: true
        },
        
        // Polyfills
        'polyfills': {
          test: /[\\/]node_modules[\\/](zone\.js|core-js|regenerator-runtime)[\\/]/,
          name: 'polyfills',
          priority: 30,
          chunks: 'all',
          minSize: 5000,
          maxSize: 100000,
          reuseExistingChunk: true
        },
        
        // Default vendor chunk for remaining vendor code
        'vendor': {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: 5,
          chunks: 'all',
          minSize: 10000,
          maxSize: 200000,
          reuseExistingChunk: true
        },
        
        // Common chunk for shared application code
        'common': {
          name: 'common',
          minChunks: 2,
          priority: 0,
          chunks: 'all',
          minSize: 5000,
          maxSize: 100000,
          reuseExistingChunk: true
        }
      }
    },
    
    // Runtime chunk for webpack runtime code
    runtimeChunk: {
      name: 'runtime'
    },
    
    // Enable tree shaking
    usedExports: true,
    sideEffects: false,
    
    // Module concatenation for better optimization
    concatenateModules: true,
    
    // Minimize code in production
    minimize: true
  },
  
  // Performance hints
  performance: {
    hints: 'warning',
    maxEntrypointSize: 500000, // 500KB
    maxAssetSize: 300000, // 300KB
    assetFilter: function(assetFilename) {
      return assetFilename.endsWith('.js');
    }
  },
  
  // Resolve configuration for better tree shaking
  resolve: {
    mainFields: ['es2015', 'browser', 'module', 'main'],
    alias: {
      // Add aliases for better tree shaking if needed
    }
  },
  
  // Module rules for optimization
  module: {
    rules: [
      // Enable tree shaking for specific libraries
      {
        test: /[\\/]node_modules[\\/]lodash[\\/]/,
        sideEffects: false
      },
      {
        test: /[\\/]node_modules[\\/]rxjs[\\/]/,
        sideEffects: false
      }
    ]
  }
};

// Export function for Angular CLI integration
module.exports.getWebpackConfig = function(config, options) {
  // Merge with existing Angular configuration
  if (config.optimization) {
    config.optimization = {
      ...config.optimization,
      ...module.exports.optimization
    };
  } else {
    config.optimization = module.exports.optimization;
  }
  
  if (config.performance) {
    config.performance = {
      ...config.performance,
      ...module.exports.performance
    };
  } else {
    config.performance = module.exports.performance;
  }
  
  return config;
};

// Development-specific optimizations
if (process.env.NODE_ENV === 'development') {
  module.exports.optimization.splitChunks.cacheGroups = {
    // Simplified cache groups for development
    vendor: {
      test: /[\\/]node_modules[\\/]/,
      name: 'vendors',
      chunks: 'all',
      priority: 10
    },
    common: {
      name: 'common',
      minChunks: 2,
      chunks: 'all',
      priority: 5
    }
  };
  
  // Disable minimization in development
  module.exports.optimization.minimize = false;
}

// Production-specific optimizations
if (process.env.NODE_ENV === 'production') {
  // Enable aggressive optimization in production
  module.exports.optimization.splitChunks.maxSize = 400000; // Smaller chunks in production
  module.exports.optimization.splitChunks.maxAsyncRequests = 20; // Fewer async requests
  module.exports.optimization.splitChunks.maxInitialRequests = 20; // Fewer initial requests
}

console.log('📦 Webpack: Advanced code splitting configuration loaded');
console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`🎯 Optimization level: ${process.env.NODE_ENV === 'production' ? 'Production (Aggressive)' : 'Development (Simplified)'}`);
