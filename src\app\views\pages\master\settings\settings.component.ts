import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import {
  SettingsService,
  Setting,
  SettingsStatistics
} from '../../../../core/services/settings.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { SettingFormComponent } from './setting-form/setting-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-settings',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {
  // Data properties
  settings: Setting[] = [];
  deletedSettings: Setting[] = [];
  statistics: SettingsStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedCategory = '';
  selectedDataType = '';
  selectedEncrypted: 'all' | 'encrypted' | 'not_encrypted' = 'all';
  selectedPublic: 'all' | 'public' | 'private' = 'all';
  selectedReadonly: 'all' | 'readonly' | 'editable' = 'all';
  selectedUserConfigurable: 'all' | 'user_configurable' | 'admin_only' = 'all';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedSettings: Set<string> = new Set();
  selectAll = false;

  // Filter options
  settingCategories: any[] = [];
  dataTypes: any[] = [];

  constructor(
    private settingsService: SettingsService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadSettings();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.settingCategories = this.settingsService.getSettingCategories();
    this.dataTypes = this.settingsService.getDataTypes();
  }

  /**
   * Load settings with current filters
   */
  loadSettings(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      category: this.selectedCategory || undefined,
      data_type: this.selectedDataType || undefined,
      is_encrypted: this.selectedEncrypted === 'all' ? undefined : this.selectedEncrypted === 'encrypted',
      is_public: this.selectedPublic === 'all' ? undefined : this.selectedPublic === 'public',
      is_readonly: this.selectedReadonly === 'all' ? undefined : this.selectedReadonly === 'readonly',
      user_configurable: this.selectedUserConfigurable === 'all' ? undefined : this.selectedUserConfigurable === 'user_configurable',
      include_deleted: this.viewMode === 'deleted'
    };

    this.settingsService.getSettingsWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedSettings = response.data.filter(s => s.deleted_at);
            this.settings = [];
          } else {
            this.settings = response.data.filter(s => !s.deleted_at);
            this.deletedSettings = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load settings';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load settings. Please try again.'
        });
      }
    });
  }

  /**
   * Load settings statistics
   */
  loadStatistics(): void {
    this.settingsService.getSettingsStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search settings
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Filter by category
   */
  onCategoryFilter(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Filter by data type
   */
  onDataTypeFilter(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Filter by encrypted status
   */
  onEncryptedFilter(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Filter by public status
   */
  onPublicFilter(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Filter by readonly status
   */
  onReadonlyFilter(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Filter by user configurable status
   */
  onUserConfigurableFilter(): void {
    this.currentPage = 1;
    this.loadSettings();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedSettings.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadSettings();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadSettings();
  }

  /**
   * Open create setting modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(SettingFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadSettings();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Setting created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit setting modal
   */
  openEditModal(setting: Setting): void {
    const modalRef = this.modalService.open(SettingFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.setting = { ...setting };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadSettings();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Setting updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete setting
   */
  deleteSetting(setting: Setting): void {
    this.popupService.showConfirmation({
      title: 'Delete Setting',
      message: `Are you sure you want to delete "${setting.key}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.settingsService.deleteSetting(setting.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadSettings();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Setting deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete setting.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Reset setting to default value
   */
  resetToDefault(setting: Setting): void {
    this.popupService.showConfirmation({
      title: 'Reset Setting',
      message: `Are you sure you want to reset "${setting.key}" to its default value?`,
      confirmText: 'Yes, Reset',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.settingsService.resetToDefault(setting.id).subscribe({
          next: () => {
            this.loadSettings();
            this.popupService.showSuccess({
              title: 'Reset!',
              message: 'Setting reset to default value successfully.',
              timer: 3000
            });
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Reset Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadSettings();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Settings uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.settingsService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'settings_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Export settings
   */
  exportSettings(): void {
    const selectedIds = Array.from(this.selectedSettings);
    const params = {
      category: this.selectedCategory || undefined,
      include_encrypted: false,
      format: 'xlsx' as const
    };

    this.settingsService.exportSettings(params).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'settings_export.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);

        this.popupService.showSuccess({
          title: 'Export Complete!',
          message: 'Settings exported successfully.',
          timer: 3000
        });
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Export Failed',
          message: 'Failed to export settings.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadSettings();
    this.loadStatistics();
  }

  /**
   * Get category label
   */
  getCategoryLabel(category: string): string {
    return this.settingsService.getCategoryLabel(category);
  }

  /**
   * Get data type label
   */
  getDataTypeLabel(dataType: string): string {
    return this.settingsService.getDataTypeLabel(dataType);
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    return this.settingsService.getCategoryBadgeClass(category);
  }

  /**
   * Get data type badge class
   */
  getDataTypeBadgeClass(dataType: string): string {
    return this.settingsService.getDataTypeBadgeClass(dataType);
  }

  /**
   * Format setting value
   */
  formatValue(setting: Setting): string {
    return this.settingsService.formatValue(setting);
  }

  /**
   * Toggle selection of a setting
   */
  toggleSelection(id: string): void {
    if (this.selectedSettings.has(id)) {
      this.selectedSettings.delete(id);
    } else {
      this.selectedSettings.add(id);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    if (this.selectAll) {
      // Select all
      this.getCurrentList().forEach(setting => {
        this.selectedSettings.add(setting.id);
      });
    } else {
      // Deselect all
      this.getCurrentList().forEach(setting => {
        this.selectedSettings.delete(setting.id);
      });
    }
  }

  /**
   * Update select all state based on current selections
   */
  updateSelectAllState(): void {
    const currentList = this.getCurrentList();
    this.selectAll = currentList.length > 0 &&
      currentList.every(setting => this.selectedSettings.has(setting.id));
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): Setting[] {
    return this.viewMode === 'deleted' ? this.deletedSettings : this.settings;
  }

  /**
   * Track by function for ngFor performance
   */
  trackBySettingId(index: number, setting: Setting): string {
    return setting.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
