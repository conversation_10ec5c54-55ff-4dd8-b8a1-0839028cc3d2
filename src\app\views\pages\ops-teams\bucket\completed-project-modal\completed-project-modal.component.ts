import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CompletedProjectRow } from '../ops-team.component';

@Component({
  selector: 'app-completed-project-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './completed-project-modal.component.html',
  styleUrls: ['./completed-project-modal.component.scss']
})
export class CompletedProjectModalComponent implements OnInit {
  @Input() projectId: number;
  @Input() projects: CompletedProjectRow[] = [];

  formData: CompletedProjectRow = {
    id: 0,
    projectName: '',
    entityName: '',
    promotersNames: '',
    profitSharing: '',
    promotersRole: '',
    developmentType: '',
    location: '',
    projectType: '',
    projectStructure: '',
    surveyNumbers: '',
    totalUnits: 0,
    constructionArea: '',
    constructionCost: 0,
    salesValue: 0,
    unsoldUnits: 0,
    startDate: '',
    endDate: '',
    occupancyReceived: '',
    reraNumber: '',
    remarks: ''
  };

  constructor(public activeModal: NgbActiveModal) { }

  // Define a type for month abbreviations
  private monthMap: { [key: string]: string } = {
    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
    'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
  };

  // Helper method to convert DD-MMM-YYYY to YYYY-MM-DD
  private convertToInputDateFormat(dateString: string): string {
    if (!dateString) return '';

    const dateParts = dateString.split('-');
    if (dateParts.length === 3) {
      const day = dateParts[0].padStart(2, '0');
      const monthAbbr = dateParts[1] as string;
      const month = this.monthMap[monthAbbr] || '01';
      const year = dateParts[2];
      return `${year}-${month}-${day}`;
    }
    return dateString;
  }

  ngOnInit(): void {
    // If editing an existing record, populate the form
    if (this.projectId) {
      const project = this.projects.find(p => p.id === this.projectId);
      if (project) {
        this.formData = { ...project };

        // Format dates for input fields
        if (this.formData.startDate) {
          this.formData.startDate = this.convertToInputDateFormat(this.formData.startDate);
        }

        if (this.formData.endDate) {
          this.formData.endDate = this.convertToInputDateFormat(this.formData.endDate);
        }
      }
    }
  }

  // Helper method to convert YYYY-MM-DD to DD-MMM-YYYY
  private convertToDisplayDateFormat(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const monthNames: string[] = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthIndex = date.getMonth();
      // Ensure the index is valid
      const month = monthIndex >= 0 && monthIndex < 12 ? monthNames[monthIndex] : 'Jan';
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Format dates for display in the table
    const formattedData: CompletedProjectRow = {
      ...this.formData
    };

    // Format dates from YYYY-MM-DD to DD-MMM-YYYY for display
    if (formattedData.startDate) {
      formattedData.startDate = this.convertToDisplayDateFormat(formattedData.startDate);
    }

    if (formattedData.endDate) {
      formattedData.endDate = this.convertToDisplayDateFormat(formattedData.endDate);
    }

    // Close the modal and pass the data back
    this.activeModal.close(formattedData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
