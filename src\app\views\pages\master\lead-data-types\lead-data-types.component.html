<!-- Lead Data Types Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-database me-2"></i>
              Lead Data Types Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_data_types }} total data types, 
              {{ statistics.active_data_types }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-warning" (click)="generateFormSchema()" 
                    [disabled]="selectedLeadDataTypes.size === 0">
              <i class="feather icon-code me-1"></i>
              Generate Schema
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Data Type
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Data Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Data Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedFieldType" (change)="onFieldTypeFilter()">
                <option value="">All Field Types</option>
                <option *ngFor="let fieldType of fieldTypes" [value]="fieldType.value">
                  {{ fieldType.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedDataCategory" (change)="onDataCategoryFilter()">
                <option value="">All Categories</option>
                <option *ngFor="let category of dataCategories" [value]="category.value">
                  {{ category.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedRequired" (change)="onRequiredFilter()">
                <option value="all">All Fields</option>
                <option value="required">Required Only</option>
                <option value="optional">Optional Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedPII" (change)="onPIIFilter()">
                <option value="all">All Data</option>
                <option value="pii">PII Only</option>
                <option value="non_pii">Non-PII Only</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading lead data types...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40">
                    <input type="checkbox" class="form-check-input" 
                           [(ngModel)]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Data Type Details</th>
                  <th>Field Configuration</th>
                  <th>Validation & Rules</th>
                  <th>Privacy & Security</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let leadDataType of getCurrentList(); trackBy: trackByLeadDataTypeId">
                  <td>
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedLeadDataTypes.has(leadDataType.id)"
                           (change)="toggleSelection(leadDataType.id)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ leadDataType.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ leadDataType.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="leadDataType.description">
                        {{ leadDataType.description }}
                      </small>
                      <div class="mt-1">
                        <span [class]="getCategoryBadgeClass(leadDataType.data_category)">
                          {{ getDataCategoryLabel(leadDataType.data_category) }}
                        </span>
                      </div>
                      <small class="d-block text-muted mt-1" *ngIf="leadDataType.group_name">
                        Group: {{ leadDataType.group_name }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div>
                      <span [class]="getFieldTypeBadgeClass(leadDataType.field_type)">
                        {{ getFieldTypeLabel(leadDataType.field_type) }}
                      </span>
                      <small class="d-block text-muted mt-1">
                        Order: {{ leadDataType.display_order }}
                      </small>
                      <div class="mt-1" *ngIf="leadDataType.field_options?.length">
                        <span class="badge bg-info me-1">
                          {{ leadDataType.field_options.length }} options
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="leadDataType.default_value">
                        <small class="text-muted">
                          <strong>Default:</strong> {{ leadDataType.default_value }}
                        </small>
                      </div>
                      <div class="mt-1" *ngIf="leadDataType.placeholder">
                        <small class="text-muted">
                          <strong>Placeholder:</strong> {{ leadDataType.placeholder }}
                        </small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="validation-info">
                      <div class="mb-1">
                        <span class="badge bg-danger me-1" *ngIf="leadDataType.is_required">Required</span>
                        <span class="badge bg-warning me-1" *ngIf="leadDataType.is_unique">Unique</span>
                        <span class="badge bg-info me-1" *ngIf="leadDataType.is_searchable">Searchable</span>
                        <span class="badge bg-secondary me-1" *ngIf="leadDataType.is_filterable">Filterable</span>
                      </div>
                      <small class="d-block text-muted">
                        {{ getValidationRulesSummary(leadDataType) }}
                      </small>
                      <div class="mt-1" *ngIf="leadDataType.conditional_logic?.length">
                        <span class="badge bg-success me-1">
                          {{ leadDataType.conditional_logic.length }} conditional rules
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="leadDataType.formatting_rules?.length">
                        <span class="badge bg-primary me-1">
                          {{ leadDataType.formatting_rules.length }} formatting rules
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="privacy-info">
                      <div class="mb-1">
                        <span class="badge bg-danger me-1" *ngIf="leadDataType.is_pii">PII</span>
                        <span class="badge bg-success me-1" *ngIf="leadDataType.is_exportable">Exportable</span>
                        <span class="badge bg-info me-1" *ngIf="leadDataType.is_sortable">Sortable</span>
                      </div>
                      <div *ngIf="leadDataType.privacy_settings">
                        <small class="d-block text-muted">
                          <strong>Access:</strong> {{ leadDataType.privacy_settings.access_level }}
                        </small>
                        <span class="badge bg-warning me-1" *ngIf="leadDataType.privacy_settings.encryption_required">
                          Encrypted
                        </span>
                        <span class="badge bg-info me-1" *ngIf="leadDataType.privacy_settings.consent_required">
                          Consent Required
                        </span>
                      </div>
                      <div *ngIf="leadDataType.audit_settings?.track_changes">
                        <span class="badge bg-secondary me-1">Audited</span>
                      </div>
                      <small class="d-block text-muted" *ngIf="leadDataType.usage_count">
                        Used {{ leadDataType.usage_count }} times
                      </small>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(leadDataType.is_active)">
                      {{ getStatusText(leadDataType.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ leadDataType.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(leadDataType)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteLeadDataType(leadDataType)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreLeadDataType(leadDataType)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-database text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Lead Data Types' : 'No Lead Data Types Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No lead data types have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No lead data types match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first lead data type.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Data Type
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} lead data types
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_data_types }}</h3>
                      <p class="mb-0">Total Data Types</p>
                    </div>
                    <i class="feather icon-database" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_data_types }}</h3>
                      <p class="mb-0">Active Data Types</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.required_fields_count }}</h3>
                      <p class="mb-0">Required Fields</p>
                    </div>
                    <i class="feather icon-alert-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-danger text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.pii_fields_count }}</h3>
                      <p class="mb-0">PII Fields</p>
                    </div>
                    <i class="feather icon-shield" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
