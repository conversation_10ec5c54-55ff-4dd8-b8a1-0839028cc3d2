import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

@Injectable({
  providedIn: 'root'
})
export class LoggingService {
  private logLevel: LogLevel;

  constructor() {
    // Set log level based on environment
    this.logLevel = environment.production ? LogLevel.ERROR : LogLevel.DEBUG;
  }

  /**
   * Debug logging - only in development
   */
  debug(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[DEBUG] ${message}`, ...args);
    }
  }

  /**
   * Info logging - development and staging
   */
  info(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.INFO) {
      console.log(`[INFO] ${message}`, ...args);
    }
  }

  /**
   * Warning logging - all environments except production
   */
  warn(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.WARN) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  }

  /**
   * Error logging - all environments
   */
  error(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.ERROR) {
      console.error(`[ERROR] ${message}`, ...args);
    }
  }

  /**
   * HTTP request logging - development only
   */
  logRequest(method: string, url: string, body?: any): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[HTTP] ${method} ${url}`, body ? '(has body)' : '(no body)');
    }
  }

  /**
   * HTTP response logging - development only
   */
  logResponse(method: string, url: string, status: number, timeElapsed: number): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[HTTP] ${method} ${url} - ${status} (${timeElapsed}ms)`);
    }
  }

  /**
   * Authentication logging - development only
   */
  logAuth(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[AUTH] ${message}`, ...args);
    }
  }

  /**
   * Performance logging - development only
   */
  logPerformance(operation: string, timeElapsed: number): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[PERF] ${operation} completed in ${timeElapsed}ms`);
    }
  }

  /**
   * Set log level programmatically
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Get current log level
   */
  getLogLevel(): LogLevel {
    return this.logLevel;
  }
}
