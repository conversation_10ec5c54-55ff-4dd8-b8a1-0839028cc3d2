<!-- Sub Product Types Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-layers me-2"></i>
              Sub Product Types Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_sub_product_types }} total sub product types, 
              {{ statistics.active_sub_product_types }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Sub Product Type
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Sub Product Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Sub Product Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCategory" (change)="onCategoryFilter()">
                <option value="">All Categories</option>
                <option *ngFor="let category of subProductTypeCategories" [value]="category.value">
                  {{ category.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedProductType" (change)="onProductTypeFilter()">
                <option value="">All Product Types</option>
                <option *ngFor="let productType of productTypes" [value]="productType.id">
                  {{ productType.name }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedFeatured" (change)="onFeaturedFilter()">
                <option value="all">All Items</option>
                <option value="featured">Featured Only</option>
                <option value="regular">Regular Only</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading sub product types...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Sub Product Details</th>
                  <th>Product Type & Category</th>
                  <th>Form Schema</th>
                  <th>Configuration</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let subProductType of getCurrentList(); trackBy: trackBySubProductTypeId">
                  <td>
                    <div>
                      <strong>{{ subProductType.name }}</strong>
                      <span class="badge bg-warning ms-2" *ngIf="subProductType.is_featured">Featured</span>
                      <small class="d-block text-muted">
                        Code: {{ subProductType.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="subProductType.description">
                        {{ subProductType.description }}
                      </small>
                      <small class="d-block text-muted">
                        Order: {{ subProductType.display_order }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div>
                      <strong class="d-block">{{ getProductTypeName(subProductType.product_type_id) }}</strong>
                      <span [class]="getCategoryBadgeClass(subProductType.category)">
                        {{ getSubProductTypeCategoryLabel(subProductType.category) }}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="form-schema-info">
                      <small class="d-block text-muted">
                        {{ getFormSchemaSummary(subProductType) }}
                      </small>
                      <div class="mt-1" *ngIf="subProductType.validation_rules?.length">
                        <span class="badge bg-info me-1">
                          {{ subProductType.validation_rules.length }} rules
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="subProductType.ui_config">
                        <span class="badge bg-secondary me-1">UI Config</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="configuration-info">
                      <div *ngIf="subProductType.pricing_override">
                        <small class="d-block text-muted">
                          <strong>Pricing:</strong> {{ subProductType.pricing_override.pricing_model || 'Override' }}
                        </small>
                      </div>
                      <div *ngIf="subProductType.availability_rules?.length">
                        <span class="badge bg-warning me-1">
                          {{ subProductType.availability_rules.length }} availability rules
                        </span>
                      </div>
                      <div *ngIf="subProductType.dependencies?.length">
                        <span class="badge bg-info me-1">
                          {{ subProductType.dependencies.length }} dependencies
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="subProductType.tags?.length">
                        <span class="badge bg-light text-dark me-1" *ngFor="let tag of subProductType.tags.slice(0, 2)">
                          {{ tag }}
                        </span>
                        <span class="badge bg-light text-dark" *ngIf="subProductType.tags.length > 2">
                          +{{ subProductType.tags.length - 2 }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(subProductType.is_active)">
                      {{ getStatusText(subProductType.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ subProductType.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(subProductType)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteSubProductType(subProductType)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreSubProductType(subProductType)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-layers text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Sub Product Types' : 'No Sub Product Types Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No sub product types have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No sub product types match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first sub product type.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Sub Product Type
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} sub product types
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_sub_product_types }}</h3>
                      <p class="mb-0">Total Sub Product Types</p>
                    </div>
                    <i class="feather icon-layers" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_sub_product_types }}</h3>
                      <p class="mb-0">Active Sub Product Types</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.featured_sub_product_types }}</h3>
                      <p class="mb-0">Featured Sub Product Types</p>
                    </div>
                    <i class="feather icon-star" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-secondary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_sub_product_types }}</h3>
                      <p class="mb-0">Inactive Sub Product Types</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
