import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { map, catchError, forkJoin, of, Subscription, Observable } from 'rxjs';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';
import { LeaveService, ManagerCompoffAssignment } from '../../../../../core/services/leave.service';
import { EmployeeService } from '../../../../../core/services/employee.service';
import Swal from 'sweetalert2';

// Removed sortable header directive - no column sorting needed

// Leave application data interface (mapped from API Leave interface)
export interface LeaveApplication {
  id: number;
  employeeName: string;
  employeeId: string; // Changed from number to string to handle UUIDs
  employeeCode:string;
  leaveType: string;
  leaveTypeId: number;
  fromDate: string;
  toDate: string;
  days: number;
  reason: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'DECLINED' | 'CANCELLED';
  appliedDate?: string;
  approvedBy?: number;
  approvedDate?: string;
  rejectionReason?: string;
}

// Helper function to format leave type names
function formatLeaveType(leaveType: string): string {
  const typeMap: { [key: string]: string } = {
    'privilege_leave': 'Privilege Leave',
    'sick_leave': 'Sick Leave',
    'casual_leave': 'Casual Leave',
    'maternity_leave': 'Maternity Leave',
    'paternity_leave': 'Paternity Leave',
    'comp_off': 'Comp Off',
    'lwp': 'Leave Without Pay'
  };
  return typeMap[leaveType] || leaveType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// Helper function to transform API response to LeaveApplication
function transformApiLeaveToApplication(apiLeave: any): LeaveApplication {
  // Try to get employee name from different possible fields
  let employeeName = 'Unknown Employee';

  if (apiLeave.employee_name) {
    employeeName = apiLeave.employee_name;
  } else if (apiLeave.employee?.first_name || apiLeave.employee?.last_name) {
    const firstName = apiLeave.employee.first_name || '';
    const lastName = apiLeave.employee.last_name || '';
    employeeName = `${firstName} ${lastName}`.trim();
  } else if (apiLeave.first_name || apiLeave.last_name) {
    const firstName = apiLeave.first_name || '';
    const lastName = apiLeave.last_name || '';
    employeeName = `${firstName} ${lastName}`.trim();
  } else if (apiLeave.employee_id) {
    // Fallback to employee ID if no name available
    employeeName = `Employee ${apiLeave.employee_id}`;
  }

  console.log('🔄 Transforming API Leave:', {
    employee_code: apiLeave.employee_code,
    employee_id: apiLeave.employee_id,
    allFields: Object.keys(apiLeave)
  });

  return {
    id: apiLeave.id || 0,
    employeeName: employeeName,
    employeeId: apiLeave.employee_id,
    employeeCode: apiLeave.employee_code,
    leaveType: formatLeaveType(apiLeave.leave_type || 'Unknown'),
    leaveTypeId: apiLeave.leave_type_id || 0,
    fromDate: apiLeave.start_date,
    toDate: apiLeave.end_date,
    days: apiLeave.days || 0,
    reason: apiLeave.reason,
    status: apiLeave.status,
    appliedDate: apiLeave.created_at,
    approvedBy: apiLeave.approved_by,
    approvedDate: apiLeave.approved_at,
    rejectionReason: apiLeave.rejected_reason
  };
}

// Helper function to transform comp-off request to LeaveApplication format
function transformCompoffToLeaveApplication(compoffRequest: any): LeaveApplication {
  console.log('🔄 Transforming Comp-off Request:', {
    id: compoffRequest.id,
    employee_id: compoffRequest.employee_id,
    working_date: compoffRequest.working_date,
    status: compoffRequest.status,
    allFields: Object.keys(compoffRequest)
  });

  // Try to get employee name from different possible fields
  let employeeName = 'Unknown Employee';
  if (compoffRequest.employee_name) {
    employeeName = compoffRequest.employee_name;
  } else if (compoffRequest.employee?.first_name || compoffRequest.employee?.last_name) {
    const firstName = compoffRequest.employee.first_name || '';
    const lastName = compoffRequest.employee.last_name || '';
    employeeName = `${firstName} ${lastName}`.trim();
  } else if (compoffRequest.first_name || compoffRequest.last_name) {
    const firstName = compoffRequest.first_name || '';
    const lastName = compoffRequest.last_name || '';
    employeeName = `${firstName} ${lastName}`.trim();
  } else if (compoffRequest.employee_id) {
    employeeName = `Employee ${compoffRequest.employee_id}`;
  }

  return {
    id: compoffRequest.id || 0,
    employeeName: employeeName,
    employeeId: compoffRequest.employee_id,
    employeeCode: compoffRequest.employee_code,
    leaveType: 'Comp-off Request', // Distinguish comp-off requests
    leaveTypeId: 999, // Special ID for comp-off requests
    fromDate: compoffRequest.working_date,
    toDate: compoffRequest.working_date, // Same date for comp-off
    days: 1, // Comp-off is always 1 day
    reason: compoffRequest.reason || 'Comp-off request for working on holiday/weekend',
    status: compoffRequest.status || 'PENDING',
    appliedDate: compoffRequest.created_at || compoffRequest.request_date,
    approvedBy: compoffRequest.approved_by,
    approvedDate: compoffRequest.approved_at,
    rejectionReason: compoffRequest.rejection_reason
  };
}

// Helper functions removed - no column sorting needed

// Helper function for filtering
function search(text: string, status: string, data: LeaveApplication[]): LeaveApplication[] {
  console.log('🔍 FILTER DEBUG - Filtering data:', {
    searchText: text,
    statusFilter: status,
    totalRecords: data.length,
    availableStatuses: [...new Set(data.map(leave => leave.status))]
  });

  const filtered = data.filter(leave => {
    const term = text.toLowerCase();

    // Status matching logic (handle both uppercase and lowercase)
    let statusMatch = false;
    if (status === 'all') {
      statusMatch = true;
    } else {
      // Handle different status variations (API returns UPPERCASE, UI might use lowercase)
      const leaveStatus = leave.status?.toUpperCase();
      const filterStatus = status.toUpperCase();

      // Direct match (case-insensitive)
      statusMatch = leaveStatus === filterStatus;

      // Handle status variations and API format differences
      if (!statusMatch) {
        switch (filterStatus) {
          case 'PENDING':
            statusMatch = leaveStatus === 'PENDING';
            break;
          case 'APPROVED':
            statusMatch = leaveStatus === 'APPROVED';
            break;
          case 'REJECTED':
            statusMatch = leaveStatus === 'REJECTED' || leaveStatus === 'DECLINED';
            break;
          case 'DECLINED':
            statusMatch = leaveStatus === 'DECLINED' || leaveStatus === 'REJECTED';
            break;
          case 'CANCELLED':
            statusMatch = leaveStatus === 'CANCELLED';
            break;
          case 'AUTO_APPROVED':
            statusMatch = leaveStatus === 'AUTO_APPROVED';
            break;
        }
      }
    }

    // Text matching logic
    const textMatch = text === '' || leave.employeeName.toLowerCase().includes(term);

    const matches = statusMatch && textMatch;

    // Debug individual record filtering (only for small datasets)
    if (data.length <= 10) {
      console.log(`🔍 Record ${leave.id}:`, {
        employeeName: leave.employeeName,
        status: leave.status,
        statusMatch,
        textMatch,
        finalMatch: matches
      });
    }

    return matches;
  });

  console.log('✅ FILTER RESULT:', {
    filteredCount: filtered.length,
    originalCount: data.length,
    filteredStatuses: [...new Set(filtered.map(leave => leave.status))]
  });

  return filtered;
}

@Component({
  selector: 'app-approve-leaves',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    BreadcrumbComponent
  ],
  templateUrl: './approve-leaves.component.html',
  styleUrl: './approve-leaves.component.scss'
})
export class ApproveLeavesComponent implements OnInit, OnDestroy {
  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'Approve Leaves' }
  ];

  // Data properties
  allLeaves: LeaveApplication[] = [];
  filteredLeaves: LeaveApplication[] = [];
  loading = false;
  error: string | null = null;

  // Loading states for individual leave actions
  approvingLeaves: Set<number> = new Set();
  rejectingLeaves: Set<number> = new Set();

  // Subscription for leave data reload events
  private reloadSubscription: Subscription = new Subscription();

  // Form controls
  searchTerm = new FormControl('', { nonNullable: true });
  statusFilter = new FormControl('pending', { nonNullable: true }); // Default to pending (lowercase for UI)

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Make Math available in template
  Math = Math;

  // Sorting (ViewChildren already defined above)

  constructor(
    private leaveService: LeaveService,
    private employeeService: EmployeeService
  ) {}

  ngOnInit(): void {
    // Load leaves based on initial filter
    this.loadLeavesByStatus();

    // Set up search filter
    this.searchTerm.valueChanges.subscribe(() => {
      this.page = 1;
      this.applyFiltersAndPagination();
    });

    // Set up status filter
    this.statusFilter.valueChanges.subscribe(() => {
      this.page = 1;
      this.loadLeavesByStatus();
    });

    // Subscribe to leave data reload events
    this.reloadSubscription.add(
      this.leaveService.reloadLeaveData$.subscribe(() => {
        console.log('🔄 Received reload event in ApproveLeavesComponent, reloading leave data...');
        this.loadLeavesByStatus();
      })
    );
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.reloadSubscription.unsubscribe();
  }

  // Load leaves based on status filter
  loadLeavesByStatus(): void {
    this.loading = true;
    this.error = null;

    const status = this.statusFilter.value;
    console.log('📋 Loading leaves for status:', status);

    if (status === 'all') {
      // Load all types of leaves when 'all' is selected
      this.loadAllLeaves();
      return;
    }

    let leaveApiCall: Observable<any>;
    let compoffApiCall: Observable<any[]> = of([]); // Default to empty array

    switch (status) {
      case 'pending':
        leaveApiCall = this.leaveService.getPendingApprovals();
        compoffApiCall = this.leaveService.getPendingCompoffApprovals();
        break;
      case 'approved':
        leaveApiCall = this.leaveService.getApprovedLeaves();
        // No comp-off for approved status (they would be in approved leaves already)
        break;
      case 'rejected':
        leaveApiCall = this.leaveService.getRejectedLeaves();
        // No comp-off for rejected status (they would be in rejected leaves already)
        break;
      default:
        // For any other status, load pending by default
        leaveApiCall = this.leaveService.getPendingApprovals();
        compoffApiCall = this.leaveService.getPendingCompoffApprovals();
        break;
    }

    // Combine regular leaves and comp-off requests
    forkJoin({
      leaves: leaveApiCall,
      compoffRequests: compoffApiCall
    }).subscribe({
      next: (responses: any) => {
        console.log('📥 API responses:', {
          leaves: responses.leaves,
          compoffRequests: responses.compoffRequests
        });

        // Process regular leaves
        let leavesData: any[] = [];
        if (responses.leaves && responses.leaves.success && Array.isArray(responses.leaves.data)) {
          leavesData = responses.leaves.data;
        } else if (Array.isArray(responses.leaves)) {
          leavesData = responses.leaves;
        } else {
          console.error('Unexpected leaves API response structure:', responses.leaves);
        }

        // Process comp-off requests (already handled in service)
        const compoffData = responses.compoffRequests || [];

        console.log('📊 Processing data:', {
          leavesCount: leavesData.length,
          compoffCount: compoffData.length
        });

        // Transform both types of data
        const transformedLeaves = leavesData.map((leave: any) => transformApiLeaveToApplication(leave));
        const transformedCompoffRequests = compoffData.map((compoff: any) => transformCompoffToLeaveApplication(compoff));

        // Combine both arrays
        this.allLeaves = [...transformedLeaves, ...transformedCompoffRequests];

        console.log('✅ Combined transformed data:', {
          totalItems: this.allLeaves.length,
          regularLeaves: transformedLeaves.length,
          compoffRequests: transformedCompoffRequests.length
        });

        // Fetch employee names BEFORE showing data to avoid UUID display
        this.fetchEmployeeNamesAndDisplay();
      },
      error: (error) => {
        console.error(`❌ Error loading ${status} leaves and comp-off requests:`, error);
        this.error = `Failed to load ${status} leaves. Please try again.`;
        this.loading = false;
      }
    });
  }

  // Load all types of leaves (pending, approved, rejected) including comp-off requests
  loadAllLeaves(): void {
    console.log('📋 Loading all types of leaves including comp-off requests...');

    // Make parallel requests to all endpoints including comp-off
    forkJoin({
      pending: this.leaveService.getPendingApprovals().pipe(catchError(() => of([]))),
      approved: this.leaveService.getApprovedLeaves().pipe(catchError(() => of([]))),
      rejected: this.leaveService.getRejectedLeaves().pipe(catchError(() => of([]))),
      compoffRequests: this.leaveService.getPendingCompoffApprovals().pipe(catchError(() => of([])))
    }).subscribe({
      next: (responses: any) => {
        console.log('📥 All API responses:', responses);

        let allLeavesData: any[] = [];

        // Process each leave response type (pending, approved, rejected)
        ['pending', 'approved', 'rejected'].forEach(type => {
          const response = responses[type];
          let leavesData: any[] = [];

          if (response && response.success && Array.isArray(response.data)) {
            leavesData = response.data;
          } else if (Array.isArray(response)) {
            leavesData = response;
          }

          allLeavesData = allLeavesData.concat(leavesData);
        });

        // Process comp-off requests separately
        const compoffData = responses.compoffRequests || [];

        console.log('📊 Combined data processing:', {
          regularLeavesCount: allLeavesData.length,
          compoffRequestsCount: compoffData.length
        });

        // Transform regular leaves
        const transformedLeaves = allLeavesData.map((leave: any) => transformApiLeaveToApplication(leave));

        // Transform comp-off requests
        const transformedCompoffRequests = compoffData.map((compoff: any) => transformCompoffToLeaveApplication(compoff));

        // Combine both arrays
        this.allLeaves = [...transformedLeaves, ...transformedCompoffRequests];

        console.log('✅ All transformed data:', {
          totalItems: this.allLeaves.length,
          regularLeaves: transformedLeaves.length,
          compoffRequests: transformedCompoffRequests.length
        });

        // Fetch employee names BEFORE showing data
        this.fetchEmployeeNamesAndDisplay();
      },
      error: (error) => {
        console.error('❌ Error loading all leaves and comp-off requests:', error);
        this.error = 'Failed to load leaves. Please try again.';
        this.loading = false;
      }
    });
  }

  // Load pending approvals from API (kept for backward compatibility)
  loadPendingApprovals(): void {
    this.statusFilter.setValue('pending');
    this.loadLeavesByStatus();
  }

  // Fetch employee names and display data only after names are loaded
  fetchEmployeeNamesAndDisplay(): void {
    console.log('🔍 Fetching employee names before displaying data...');

    // Get unique employee IDs
    const employeeIds = [...new Set(this.allLeaves.map(leave => leave.employeeId).filter(id => id))];
    console.log('📋 Unique employee IDs:', employeeIds);

    if (employeeIds.length === 0) {
      console.log('⚠️ No employee IDs found - displaying data as is');
      this.applyFiltersAndPagination();
      this.loading = false;
      return;
    }

    // Keep loading state until employee names are fetched
    console.log('⏳ Keeping loading state while fetching employee names...');

    // Fetch employee details for each ID
    const employeeRequests = employeeIds.map(employeeId =>
      this.employeeService.getEmployeeByUuid(employeeId).pipe(
        map((response: any) => {
          console.log(`👤 Employee data for ${employeeId}:`, response);

          // Handle different response structures
          let employeeData = null;
          if (response && response.success && response.data) {
            employeeData = response.data;
          } else if (response && response.first_name) {
            employeeData = response;
          }

          if (employeeData) {
            const firstName = employeeData.first_name || employeeData.employee_first_name || '';
            const lastName = employeeData.last_name || employeeData.employee_last_name || '';
            const fullName = `${firstName} ${lastName}`.trim();
            const employeeCode = employeeData.employee_code || employeeData.code || '';

            console.log(`🔍 Employee Code Debug for ${employeeId}:`, {
              employee_code: employeeData.employee_code,
              code: employeeData.code,
              finalEmployeeCode: employeeCode,
              allFields: Object.keys(employeeData)
            });

            return {
              employeeId: employeeId,
              employeeName: fullName || `Employee ${employeeId}`,
              employeeCode: employeeCode
            };
          }

          return {
            employeeId: employeeId,
            employeeName: `Employee ${employeeId}`,
            employeeCode: ''
          };
        }),
        catchError(error => {
          console.error(`❌ Error fetching employee ${employeeId}:`, error);
          return of({
            employeeId: employeeId,
            employeeName: `Employee ${employeeId}`,
            employeeCode: ''
          });
        })
      )
    );

    // Execute all requests and display data only after completion
    forkJoin(employeeRequests).subscribe({
      next: (employeeData) => {
        console.log('✅ All employee data fetched:', employeeData);

        // Update leave applications with employee names and codes
        this.allLeaves.forEach(leave => {
          const employee = employeeData.find(emp => emp.employeeId === leave.employeeId);
          if (employee) {
            leave.employeeName = employee.employeeName;
            if (employee.employeeCode) {
              leave.employeeCode = employee.employeeCode;
            }
          }
        });

        console.log('� Updated leaves with employee names:', this.allLeaves);

        // NOW display the data with proper employee names
        this.applyFiltersAndPagination();
        this.loading = false;
        console.log('✅ Data displayed with employee names loaded');
      },
      error: (error) => {
        console.error('❌ Error fetching employee names:', error);
        // Even if employee fetching fails, show the data
        this.applyFiltersAndPagination();
        this.loading = false;
        console.log('⚠️ Displayed data without employee names due to error');
      }
    });
  }

  // Legacy method for manual refresh (kept for compatibility)
  fetchEmployeeNames(): void {
    console.log('🔍 Fetching employee names for refresh...');

    // Get unique employee IDs
    const employeeIds = [...new Set(this.allLeaves.map(leave => leave.employeeId).filter(id => id))];
    console.log('📋 Unique employee IDs:', employeeIds);

    if (employeeIds.length === 0) {
      console.log('⚠️ No employee IDs found');
      return;
    }

    // Fetch employee details for each ID
    const employeeRequests = employeeIds.map(employeeId =>
      this.employeeService.getEmployeeByUuid(employeeId).pipe(
        map((response: any) => {
          console.log(`👤 Employee data for ${employeeId}:`, response);

          // Handle different response structures
          let employeeData = null;
          if (response && response.success && response.data) {
            employeeData = response.data;
          } else if (response && response.first_name) {
            employeeData = response;
          }

          if (employeeData) {
            const firstName = employeeData.first_name || employeeData.employee_first_name || '';
            const lastName = employeeData.last_name || employeeData.employee_last_name || '';
            const fullName = `${firstName} ${lastName}`.trim();
            const employeeCode = employeeData.employee_code || employeeData.code || '';

            return {
              employeeId: employeeId,
              employeeName: fullName || `Employee ${employeeId}`,
              employeeCode: employeeCode
            };
          }

          return {
            employeeId: employeeId,
            employeeName: `Employee ${employeeId}`,
            employeeCode: ''
          };
        }),
        catchError(error => {
          console.error(`❌ Error fetching employee ${employeeId}:`, error);
          return of({
            employeeId: employeeId,
            employeeName: `Employee ${employeeId}`,
            employeeCode: ''
          });
        })
      )
    );

    // Execute all requests
    forkJoin(employeeRequests).subscribe({
      next: (employeeData) => {
        console.log('✅ All employee data fetched:', employeeData);

        // Update leave applications with employee names and codes
        this.allLeaves.forEach(leave => {
          const employee = employeeData.find(emp => emp.employeeId === leave.employeeId);
          if (employee) {
            leave.employeeName = employee.employeeName;
            if (employee.employeeCode) {
              leave.employeeCode = employee.employeeCode;
            }
          }
        });

        console.log('🔄 Updated leaves with employee names:', this.allLeaves);

        // Refresh the filtered view
        this.applyFiltersAndPagination();
      },
      error: (error) => {
        console.error('❌ Error fetching employee names:', error);
      }
    });
  }

  // Apply filters and pagination with descending order (latest first)
  applyFiltersAndPagination(): void {
    const filtered = search(this.searchTerm.value, this.statusFilter.value, this.allLeaves);

    // Always sort by latest first (descending order)
    const sortedData = filtered.sort((a, b) => {
      // Primary sort: by applied date (most recent first)
      const dateA = new Date(a.appliedDate || a.fromDate || '1970-01-01').getTime();
      const dateB = new Date(b.appliedDate || b.fromDate || '1970-01-01').getTime();

      if (dateA !== dateB) {
        return dateB - dateA; // Descending order (latest first)
      }

      // Secondary sort: by ID (higher ID = more recent)
      return (b.id || 0) - (a.id || 0);
    });

    console.log('📅 Leaves sorted in descending order (latest first):', sortedData.slice(0, 3).map(l => ({
      id: l.id,
      employeeName: l.employeeName,
      appliedDate: l.appliedDate,
      fromDate: l.fromDate
    })));

    this.collectionSize = sortedData.length;

    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredLeaves = sortedData.slice(startIndex, endIndex);

    console.log(`📄 Showing page ${this.page}: items ${startIndex + 1}-${Math.min(endIndex, sortedData.length)} of ${sortedData.length}`);
  }

  // Sorting removed - leaves are always displayed in descending order (latest first)

  // Get badge class for leave type
  getLeaveTypeBadgeClass(leaveType: string): string {
    switch (leaveType) {
      case 'PL':
        return 'bg-primary text-white';
      case 'SL':
        return 'bg-warning text-dark';
      case 'CL':
        return 'bg-info text-white';
      case 'LWP':
        return 'bg-danger text-white';  // Red for Leave Without Pay (important)
      case 'OD':
        return 'bg-success text-white'; // Green for Outdoor Duty (official work)
      case 'COMP':
      case 'Comp Off':
      case 'Comp-off Request':
        return 'bg-dark text-white';
      default:
        return 'bg-light text-dark';
    }
  }

  // Get badge class for status (handle both uppercase and lowercase)
  getStatusBadgeClass(status: string): string {
    const upperStatus = status?.toUpperCase();
    switch (upperStatus) {
      case 'APPROVED':
        return 'bg-success';
      case 'PENDING':
        return 'bg-warning';
      case 'REJECTED':
      case 'DECLINED':
        return 'bg-danger';
      case 'CANCELLED':
        return 'bg-secondary';
      case 'AUTO_APPROVED':
        return 'bg-info';
      default:
        return 'bg-light';
    }
  }

  // Helper methods for loading states
  isApproving(leaveId: number): boolean {
    return this.approvingLeaves.has(leaveId);
  }

  isRejecting(leaveId: number): boolean {
    return this.rejectingLeaves.has(leaveId);
  }

  isProcessing(leaveId: number): boolean {
    return this.isApproving(leaveId) || this.isRejecting(leaveId);
  }

  // Helper method to format date in dd-MM-yyyy format
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}-${month}-${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  }

  // Helper method to extract error message from API response
  private extractErrorMessage(error: any, defaultMessage: string): string {
    console.error('🔍 Extracting error message from:', error);
    console.error('📋 Full error object:', JSON.stringify(error, null, 2));

    let errorMessage = defaultMessage;

    // Check various possible error response structures
    if (error.error) {
      if (error.error.message) {
        errorMessage = error.error.message;
      } else if (error.error.detail) {
        errorMessage = error.error.detail;
      } else if (error.error.error) {
        errorMessage = error.error.error;
      } else if (error.error.errors && Array.isArray(error.error.errors)) {
        // Handle validation errors array
        errorMessage = error.error.errors.join(', ');
      } else if (typeof error.error === 'string') {
        errorMessage = error.error;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    // Remove "Validation errors:" prefix if present
    if (errorMessage.toLowerCase().startsWith('validation errors:')) {
      errorMessage = errorMessage.replace(/^validation errors:\s*/i, '').trim();
    }

    // Handle specific validation errors with user-friendly messages
    if (errorMessage.toLowerCase().includes('already has approved or pending leaves')) {
      errorMessage = 'Employee already has approved or pending leaves during this period. Please check existing leave applications.';
    } else if (errorMessage.toLowerCase().includes('insufficient leave balance')) {
      errorMessage = 'Employee has insufficient leave balance for this request.';
    } else if (errorMessage.toLowerCase().includes('overlapping')) {
      errorMessage = 'This leave request overlaps with existing leave applications.';
    }

    console.log('✅ Final extracted error message:', errorMessage);
    return errorMessage;
  }

  // Approve a leave application or comp-off request
  approveLeave(leave: LeaveApplication): void {
    // Prevent multiple clicks while processing
    if (this.isProcessing(leave.id)) {
      console.log('⚠️ Leave is already being processed, ignoring click');
      return;
    }

    console.log('🔍 Approve Leave/Comp-off - Employee Object:', leave);
    console.log('📋 Employee Details:', {
      id: leave.id,
      employeeName: leave.employeeName,
      employeeId: leave.employeeId,
      leaveType: leave.leaveType,
      leaveTypeId: leave.leaveTypeId,
      fromDate: leave.fromDate,
      toDate: leave.toDate,
      days: leave.days,
      reason: leave.reason,
      status: leave.status
    });

    // Determine if this is a comp-off request
    const isCompoffRequest = leave.leaveType === 'Comp-off Request' || leave.leaveTypeId === 999;
    const requestType = isCompoffRequest ? 'comp-off request' : 'leave application';

    console.log('🔍 Comp-off Detection:', {
      leaveType: leave.leaveType,
      leaveTypeId: leave.leaveTypeId,
      isCompoffRequest: isCompoffRequest,
      reason: leave.reason,
      reasonLength: leave.reason ? leave.reason.length : 0
    });

    // Check if this should be treated as a manager comp-off assignment opportunity
    const isManagerAssignmentOpportunity = this.isManagerCompoffAssignmentOpportunity(leave);

    console.log('🎯 Assignment Opportunity Check Result:', {
      isCompoffRequest,
      isManagerAssignmentOpportunity,
      willShowAssignmentDialog: isManagerAssignmentOpportunity,
      willShowRegularDialog: !isManagerAssignmentOpportunity
    });

    if (isManagerAssignmentOpportunity) {
      console.log('✅ Showing Manager Comp-off Assignment Dialog');
      // Handle as manager comp-off assignment
      this.handleManagerCompoffAssignment(leave);
    } else {
      console.log('✅ Showing Regular Approval Dialog');
      // Handle as regular approval
      this.handleRegularApproval(leave, isCompoffRequest, requestType);
    }
  }

  // Check if this should be treated as a manager comp-off assignment opportunity
  private isManagerCompoffAssignmentOpportunity(leave: LeaveApplication): boolean {
    console.log('🔍 Starting Manager Assignment Opportunity Check...');

    // Logic to determine if this should be a manager assignment vs regular approval
    // This method can be extended based on specific business rules

    const isCompoffRequest = leave.leaveType === 'Comp-off Request' || leave.leaveTypeId === 999;

    console.log('📋 Comp-off Request Check:', {
      leaveType: leave.leaveType,
      leaveTypeId: leave.leaveTypeId,
      isCompoffRequest: isCompoffRequest
    });

    // Only comp-off requests can be manager assignments
    if (!isCompoffRequest) {
      console.log('❌ Not a comp-off request - skipping assignment check');
      return false;
    }

    // Check if reason field exists and is not empty
    if (!leave.reason || leave.reason.trim().length === 0) {
      console.log('❌ No reason provided - cannot check for assignment keywords');
      return false;
    }

    // Check if the reason suggests this should be a manager assignment
    const assignmentKeywords = [
      'holiday work', 'weekend work', 'overtime', 'extra hours',
      'manager assign', 'worked on holiday', 'worked on weekend',
      'emergency work', 'urgent work', 'after hours', 'special project',
      'holiday', 'weekend', 'overtime', 'extra', 'emergency', 'urgent',
      'worked', 'assign', 'special', 'project', 'hours'
    ];

    const reasonLower = leave.reason.toLowerCase().trim();
    console.log('📝 Reason Analysis:', {
      originalReason: leave.reason,
      reasonLower: reasonLower,
      reasonLength: reasonLower.length
    });

    const matchedKeywords: string[] = [];
    const hasAssignmentKeywords = assignmentKeywords.some(keyword => {
      const matches = reasonLower.includes(keyword);
      if (matches) {
        matchedKeywords.push(keyword);
      }
      return matches;
    });

    console.log('🔍 Keyword Matching Results:', {
      assignmentKeywords: assignmentKeywords,
      matchedKeywords: matchedKeywords,
      hasAssignmentKeywords: hasAssignmentKeywords,
      finalDecision: hasAssignmentKeywords
    });

    // Additional criteria can be added here:
    // - Check if the date falls on a weekend or holiday
    // - Check if the employee has specific roles
    // - Check if the request was made within a certain timeframe
    // - Check if there are specific approval workflows

    console.log('🎯 Final Manager Assignment Opportunity Decision:', {
      isCompoffRequest,
      reason: leave.reason,
      hasAssignmentKeywords,
      matchedKeywords,
      shouldAssign: hasAssignmentKeywords
    });

    return hasAssignmentKeywords;
  }

  // Handle manager comp-off assignment
  private handleManagerCompoffAssignment(leave: LeaveApplication): void {
    console.log('🎯 Handling automatic manager comp-off assignment:', leave);

    // Auto-generate assignment reason based on original request
    const autoReason = `Manager assigned comp-off for ${leave.reason}`;

    // Create manager assignment payload with proper date formatting
    const assignment: ManagerCompoffAssignment = {
      employee_id: leave.employeeId,
      working_date: this.formatDateForAPI(leave.fromDate), // Ensure YYYY-MM-DD format
      reason: autoReason,
      request_type: 'manager_assignment'
    };

    console.log('📤 Automatic manager comp-off assignment payload:', assignment);

    // Call the manager assignment API directly without confirmation dialog
    this.leaveService.assignManagerCompoff(assignment).subscribe({
      next: (response: any) => {
        console.log('✅ Automatic manager comp-off assignment successful:', response);

        // Show success message
        Swal.fire({
          title: 'Comp-off Assigned!',
          text: `Comp-off has been assigned to ${leave.employeeName} successfully.`,
          icon: 'success',
          timer: 2000,
          showConfirmButton: false
        });

        // Reload the leave data to reflect the new assignment
        this.loadLeavesByStatus();
      },
      error: (error: any) => {
        console.error('❌ Automatic manager comp-off assignment failed:', error);

        const errorMessage = this.extractErrorMessage(error, 'Failed to assign comp-off. Please try again.');

        Swal.fire({
          title: 'Assignment Failed',
          text: errorMessage,
          icon: 'error',
          confirmButtonText: 'OK',
          width: '500px'
        });
      }
    });
  }



  // Helper method to format date for API (YYYY-MM-DD)
  private formatDateForAPI(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.warn('Date API formatting error:', error);
      return dateString;
    }
  }

  // Handle regular approval (existing logic)
  private handleRegularApproval(leave: LeaveApplication, isCompoffRequest: boolean, requestType: string): void {
    Swal.fire({
      title: `Approve ${isCompoffRequest ? 'Comp-off Request' : 'Leave'}?`,
      html: `<strong style="color: black;">Are you sure you want to approve ${leave.employeeName}'s ${requestType}?</strong>`,
      icon: 'success',
      showCancelButton: true,
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Approve',
      cancelButtonText: 'Cancel',
      input: 'textarea',
      inputPlaceholder: 'Add comments (optional)...',
      inputAttributes: {
        'aria-label': 'Comments'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const comments = result.value || '';

        // Use appropriate service method based on request type
        let approvalObservable: Observable<any>;

        if (isCompoffRequest) {
          // For comp-off requests, use status-based payload (will be converted to action-based in service)
          const compoffPayload = { status: 'APPROVED' as const, comments };
          console.log('📤 Comp-off approval payload (status-based):', compoffPayload);
          console.log('📤 This will be converted to action-based payload in the service layer');
          approvalObservable = this.leaveService.approveCompoffRequest(leave.id.toString(), compoffPayload);
        } else {
          // For regular leaves, use existing method
          approvalObservable = this.leaveService.approveLeave(leave.id, comments);
        }

        console.log(`📤 ${isCompoffRequest ? 'Comp-off' : 'Leave'} approval request:`, {
          id: leave.id,
          comments,
          isCompoffRequest,
          payload: isCompoffRequest ? { status: 'APPROVED', comments } : { comments }
        });

        // Set loading state
        this.approvingLeaves.add(leave.id);

        approvalObservable.subscribe({
          next: (updatedLeave) => {
            console.log(`✅ ${requestType} approved successfully:`, updatedLeave);

            // Clear loading state
            this.approvingLeaves.delete(leave.id);

            // Note: Leave service will automatically trigger reload of all components
            // No need to manually update local data as fresh data will be loaded

            Swal.fire({
              title: 'Approved!',
              text: `${isCompoffRequest ? 'Comp-off request' : 'Leave application'} has been approved successfully.`,
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });
          },
          error: (error) => {
            // Clear loading state on error
            this.approvingLeaves.delete(leave.id);
            console.error(`❌ Error approving ${requestType}:`, error);

            // Extract specific error message using helper method
            const errorMessage = this.extractErrorMessage(error, `Failed to approve ${requestType}. Please try again.`);

            Swal.fire({
              title: `Cannot Approve ${isCompoffRequest ? 'Comp-off Request' : 'Leave'}`,
              text: errorMessage,
              icon: 'error',
              confirmButtonText: 'OK',
              width: '500px'
            });
          }
        });
      }
    });
  }

  // Reject a leave application or comp-off request
  rejectLeave(leave: LeaveApplication): void {
    // Prevent multiple clicks while processing
    if (this.isProcessing(leave.id)) {
      console.log('⚠️ Leave is already being processed, ignoring click');
      return;
    }

    console.log('❌ Reject Leave/Comp-off - Employee Object:', leave);
    console.log('📋 Employee Details for Rejection:', {
      id: leave.id,
      employeeName: leave.employeeName,
      employeeId: leave.employeeId,
      leaveType: leave.leaveType,
      leaveTypeId: leave.leaveTypeId,
      fromDate: leave.fromDate,
      toDate: leave.toDate,
      days: leave.days,
      reason: leave.reason,
      status: leave.status
    });

    // Determine if this is a comp-off request
    const isCompoffRequest = leave.leaveType === 'Comp-off Request' || leave.leaveTypeId === 999;
    const requestType = isCompoffRequest ? 'comp-off request' : 'leave application';

    Swal.fire({
      title: `Reject ${isCompoffRequest ? 'Comp-off Request' : 'Leave'}?`,
      html: `<strong style="color: black;">Are you sure you want to reject ${leave.employeeName}'s ${requestType}?</strong>`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Reject',
      cancelButtonText: 'Cancel',
      input: 'textarea',
      inputPlaceholder: 'Reason for rejection (optional)...',
      inputAttributes: {
        'aria-label': 'Rejection reason'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const reason = result.value ? result.value.trim() : '';

        // Use appropriate service method based on request type
        let rejectionObservable: Observable<any>;

        if (isCompoffRequest) {
          // For comp-off requests, use status-based payload (will be converted to action-based in service)
          const compoffPayload = { status: 'REJECTED' as const, comments: reason };
          console.log('📤 Comp-off rejection payload (status-based):', compoffPayload);
          console.log('📤 This will be converted to action-based payload in the service layer');
          rejectionObservable = this.leaveService.approveCompoffRequest(leave.id.toString(), compoffPayload);
        } else {
          // For regular leaves, use existing method
          rejectionObservable = this.leaveService.rejectLeave(leave.id, reason);
        }

        console.log(`📤 ${isCompoffRequest ? 'Comp-off' : 'Leave'} rejection request:`, {
          id: leave.id,
          reason,
          isCompoffRequest,
          payload: isCompoffRequest ? { status: 'REJECTED', comments: reason } : { reason }
        });

        // Set loading state
        this.rejectingLeaves.add(leave.id);

        rejectionObservable.subscribe({
          next: (updatedLeave: any) => {
            console.log(`✅ ${requestType} rejected successfully:`, updatedLeave);

            // Clear loading state
            this.rejectingLeaves.delete(leave.id);

            // Note: Leave service will automatically trigger reload of all components
            // No need to manually update local data as fresh data will be loaded

            Swal.fire({
              title: 'Rejected!',
              text: `${isCompoffRequest ? 'Comp-off request' : 'Leave application'} has been rejected.`,
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });
          },
          error: (error: any) => {
            // Clear loading state on error
            this.rejectingLeaves.delete(leave.id);
            console.error(`❌ Error rejecting ${requestType}:`, error);

            // Extract specific error message using helper method
            const errorMessage = this.extractErrorMessage(error, `Failed to reject ${requestType}. Please try again.`);

            Swal.fire({
              title: `Cannot Reject ${isCompoffRequest ? 'Comp-off Request' : 'Leave'}`,
              text: errorMessage,
              icon: 'error',
              confirmButtonText: 'OK',
              width: '500px'
            });
          }
        });
      }
    });
  }

  // Handle pagination changes
  onPageChange(page: number): void {
    this.page = page;
    this.applyFiltersAndPagination();
  }

  // View leave details
  viewLeaveDetails(leave: LeaveApplication): void {
    console.log('👁 View Leave Details - Employee Object:', leave);
    console.log('📋 Complete Employee Leave Data:', {
      id: leave.id,
      employeeName: leave.employeeName,
      employeeId: leave.employeeId,
      employeeCode: leave.employeeCode,
      leaveType: leave.leaveType,
      leaveTypeId: leave.leaveTypeId,
      fromDate: leave.fromDate,
      toDate: leave.toDate,
      days: leave.days,
      reason: leave.reason,
      status: leave.status,
      appliedDate: leave.appliedDate,
      approvedBy: leave.approvedBy,
      approvedDate: leave.approvedDate,
      rejectionReason: leave.rejectionReason
    });
    console.log('🔍 Employee Code Debug:', {
      employeeCode: leave.employeeCode,
      employeeCodeType: typeof leave.employeeCode,
      employeeCodeLength: leave.employeeCode ? leave.employeeCode.length : 0,
      employeeCodeValue: JSON.stringify(leave.employeeCode)
    });

    const details = `
      <div class="leave-details-container" style="text-align: left; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
        <!-- Employee Header -->
        <div class="employee-header" style="background: #f8f9fa;  padding: 16px; margin-bottom: 16px;">
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="background: #3F828B; color: white; padding: 10px; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
              <i class="fas fa-user" style="font-size: 16px;"></i>
            </div>
            <div>
              <h5 style="margin: 0; font-size: 16px; font-weight: 600; color: #212529;">${leave.employeeName}</h5>
              <p style="margin: 0; font-size: 13px; color: #6c757d;">Employee ID: ${leave.employeeCode || 'N/A'}</p>
            </div>
          </div>
        </div>

        <!-- Leave Details -->
        <div style="margin-bottom: 16px;">
          <!-- Row 1: Leave Type and Duration -->
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
            <div style="background: #fff; padding: 12px;">
              <p style="margin: 0; font-size: 14px; color: #000;">
                <span style="font-weight: 500; color: #000;">Leave Type :- </span>
                <span style="font-weight: 600; color: #000;">${leave.leaveType}</span>
              </p>
            </div>
            <div style="background: #fff; padding: 12px;">
              <p style="margin: 0; font-size: 14px; color: #000;">
                <span style="font-weight: 500; color: #000;">Duration :- </span>
                <span style="font-weight: 600; color: #000;">${leave.days} ${leave.days === 1 ? 'Day' : 'Days'}</span>
              </p>
            </div>
          </div>

          <!-- Row 2: From Date and To Date -->
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
            <div style="background: #fff; padding: 12px;">
              <p style="margin: 0; font-size: 14px; color: #000;">
                <span style="font-weight: 500; color: #000;">From Date :- </span>
                <span style="font-weight: 600; color: #000;">${this.formatDate(leave.fromDate)}</span>
              </p>
            </div>
            <div style="background: #fff; padding: 12px;">
              <p style="margin: 0; font-size: 14px; color: #000;">
                <span style="font-weight: 500; color: #000;">To Date :- </span>
                <span style="font-weight: 600; color: #000;">${this.formatDate(leave.toDate)}</span>
              </p>
            </div>
          </div>

          <!-- Row 3: Reason for Leave and Status -->
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
            <div style="background: #fff; padding: 12px;">
              <p style="margin: 0; font-size: 14px; color: #000;">
                <span style="font-weight: 500; color: #000;">Reason for Leave :- </span>
                <span style="font-weight: 600; color: #000;">${leave.reason}</span>
              </p>
            </div>
            <div style="background: #fff; padding: 12px;">
              <p style="margin: 0; font-size: 14px; color: #000;">
                <span style="font-weight: 500; color: #000;">Status :- </span>
                <span style="font-weight: 600; color: #000; text-transform: capitalize;">${leave.status}</span>
              </p>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 12px;">

          

          ${leave.approvedDate ? `
          <!-- Approved Date -->
          <div style="background: #fff;  padding: 12px;">
            <p style="margin: 0 0 6px 0; font-size: 12px; color: #6c757d; font-weight: 500;">Approved On</p>
            <p style="margin: 0; font-size: 13px; font-weight: 500; color: #28a745;">${this.formatDate(leave.approvedDate)}</p>
          </div>
          ` : ''}
        </div>

        ${leave.rejectionReason ? `
        <!-- Rejection Reason -->
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 12px; margin-top: 16px;">
        
          <p style="margin: 0 0 6px 0; font-size: 12px; color: #721c24; font-weight: 500;">Rejection Reason</p>
          <p style="margin: 0; font-size: 13px; color: #721c24; line-height: 1.4;">${leave.rejectionReason}</p>
        </div>
        ` : ''}
      </div>
    `;

    Swal.fire({
      title: 'Leave Application Details',
      html: details,
      icon: 'info',
      confirmButtonText: 'Close',
      width: '500px'
    });
  }

  // Refresh data
  refreshData(): void {
    this.loadLeavesByStatus();
  }

  // TEMPORARY: Test method to verify manager assignment functionality
  // This can be called from browser console: component.testManagerAssignment()
  testManagerAssignment(): void {
    console.log('🧪 Testing manager assignment functionality...');

    // Create a mock comp-off request with assignment keywords
    const mockLeave: LeaveApplication = {
      id: 999,
      employeeId: 'test-employee-123',
      employeeName: 'Test Employee',
      employeeCode: 'EMP123',
      leaveType: 'Comp-off Request',
      leaveTypeId: 999,
      fromDate: '2024-01-15',
      toDate: '2024-01-15',
      days: 1,
      reason: 'Worked on holiday during project deadline',
      status: 'PENDING',
      appliedDate: '2024-01-16',
      approvedBy: undefined,
      approvedDate: undefined,
      rejectionReason: undefined
    };

    console.log('🎯 Testing with mock comp-off request:', mockLeave);

    // Test the assignment opportunity detection
    const isAssignmentOpportunity = this.isManagerCompoffAssignmentOpportunity(mockLeave);
    console.log('✅ Assignment opportunity detected:', isAssignmentOpportunity);

    if (isAssignmentOpportunity) {
      console.log('🎉 Assignment detection working! Would trigger automatic assignment.');
      // Uncomment the line below to actually test the assignment API call
      // this.handleManagerCompoffAssignment(mockLeave);
    } else {
      console.log('❌ Assignment detection failed. Check keyword matching logic.');
    }
  }
}