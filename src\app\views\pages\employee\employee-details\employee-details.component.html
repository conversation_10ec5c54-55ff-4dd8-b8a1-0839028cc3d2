<!-- Employee Details Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h4 class="card-title">Employee Details</h4>
          <div>
            <a routerLink="/employees" class="btn btn-outline-secondary me-2">
              <i data-feather="list" appFeatherIcon class="me-2"></i>
              Back to List
            </a>
            <a *ngIf="isAdmin && employee" [routerLink]="['/employees/edit', employee.id]" class="btn btn-primary">
              <i data-feather="edit" appFeatherIcon class="me-2"></i>
              Edit Employee
            </a>
          </div>
        </div>

        <!-- Loading and Error States -->
        <div *ngIf="loading" class="text-center my-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Loading employee data...</p>
        </div>

        <div *ngIf="error" class="alert alert-danger mt-3" role="alert">
          {{ error }}
          <button type="button" class="btn btn-link" (click)="loadEmployeeData(employee?.id!)">Try again</button>
        </div>

        <!-- Employee Details -->
        <div *ngIf="employee && !loading" class="employee-details">
          <!-- Header Section -->
          <div class="employee-header">
            <div class="row align-items-center">
              <div class="col-md-8">
                <div class="d-flex align-items-center">
                  <div class="avatar bg-light rounded-circle me-3">
                    <span class="avatar-text display-6">{{ employee.name?.charAt(0) || 'N' }}</span>
                  </div>
                  <div>
                    <h3 class="mb-1">{{ employee.name }}</h3>
                    <p class="text-muted mb-0">
                      {{ employee.position || 'Employee' }}
                      <span *ngIf="employee.department">• {{ employee.department }}</span>
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-md-4 text-md-end mt-3 mt-md-0">
                <span [ngClass]="employee.user_active ? 'badge bg-success' : 'badge bg-danger'">
                  {{ employee.user_active ? 'Active' : 'Inactive' }}
                </span>
                <button *ngIf="isAdmin" class="btn btn-outline-primary ms-2" (click)="toggleStatus()">
                  {{ employee.user_active ? 'Deactivate' : 'Activate' }}
                </button>
              </div>
            </div>
          </div>

          <hr>

          <!-- Employee Information Sections -->
          <div class="row mt-4">
            <!-- Contact Information -->
            <div class="col-md-6 mb-4">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">
                    <i data-feather="user" appFeatherIcon class="me-2 text-primary"></i>
                    Contact Information
                  </h5>
                  <div class="mt-4">
                    <div class="info-item">
                      <span class="info-label">Email</span>
                      <span class="info-value">
                        <a href="mailto:{{ employee.email }}">{{ employee.email }}</a>
                      </span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Phone</span>
                      <span class="info-value">
                        {{ employee.phone || 'Not provided' }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Employment Information -->
            <div class="col-md-6 mb-4">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">
                    <i data-feather="briefcase" appFeatherIcon class="me-2 text-primary"></i>
                    Employment Information
                  </h5>
                  <div class="mt-4">
                    <div class="info-item">
                      <span class="info-label">Department</span>
                      <span class="info-value">{{ employee.department || 'Not assigned' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Position</span>
                      <span class="info-value">{{ employee.position || 'Not assigned' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Joining Date</span>
                      <span class="info-value">{{ formatDate(employee.joining_date) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Administrative Information -->
            <div *ngIf="isAdmin" class="col-md-6 mb-4">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">
                    <i data-feather="shield" appFeatherIcon class="me-2 text-primary"></i>
                    Administrative Details
                  </h5>
                  <div class="mt-4">
                    <div class="info-item">
                      <span class="info-label">Employee ID</span>
                      <span class="info-value">{{ employee.id }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Role</span>
                      <span class="info-value">
                        <span class="badge bg-info">{{ employee.role || 'Employee' }}</span>
                      </span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Salary</span>
                      <span class="info-value">${{ employee.salary?.toLocaleString() || 'Not set' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Permissions (placeholder for future implementation) -->
            <div *ngIf="isAdmin" class="col-md-6 mb-4">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title">
                    <i data-feather="key" appFeatherIcon class="me-2 text-primary"></i>
                    Permissions
                  </h5>
                  <div class="mt-4">
                    <p class="text-muted">Permission management will be available when the roles API is ready.</p>
                    <div class="placeholder-permissions">
                      <span class="badge bg-light text-secondary m-1">view_profile</span>
                      <span class="badge bg-light text-secondary m-1">view_salary</span>
                      <span class="badge bg-light text-secondary m-1">apply_leave</span>
                      <span *ngIf="employee.role === 'admin'" class="badge bg-light text-secondary m-1">manage_employees</span>
                      <span *ngIf="employee.role === 'admin'" class="badge bg-light text-secondary m-1">view_reports</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
