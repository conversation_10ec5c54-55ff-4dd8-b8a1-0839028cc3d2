import { Routes } from '@angular/router';
import { dynamicAuthGuard } from '../../../core/guards/dynamic-auth.guard';

export default [
  {
    path: '',
    redirectTo: 'users',
    pathMatch: 'full'
  },
  {
    path: 'users',
    loadComponent: () => import('./user-list/user-list.component').then(c => c.UserListComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      title: 'User Management',
      breadcrumb: 'Users',
      permissions: ['users:read', 'users:create', 'users:update', 'users:delete', '*']
    }
  },
  {
    path: 'users/create',
    loadComponent: () => import('./user-form/user-form.component').then(c => c.UserFormComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      title: 'Create User',
      breadcrumb: 'Create User',
      permissions: ['users:create', '*']
    }
  },
  {
    path: 'users/:id',
    loadComponent: () => import('./user-details/user-details.component').then(c => c.UserDetailsComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      title: 'User Details',
      breadcrumb: 'User Details',
      permissions: ['users:read', '*']
    }
  },
  {
    path: 'users/:id/edit',
    loadComponent: () => import('./user-form/user-form.component').then(c => c.UserFormComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      title: 'Edit User',
      breadcrumb: 'Edit User',
      permissions: ['users:update', '*']
    }
  }
] as Routes;
