import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class MasterPermissionGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {

    console.log('🛡️ MasterPermissionGuard checking access for:', state.url);

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check for master:read permission
    const hasPermission = this.authService.hasPermission('master:read');

    if (!hasPermission) {
      console.log('❌ Permission denied. Required: master:read');
      console.log('👤 User permissions:', currentUser.permissions);
      this.router.navigate(['/dashboard']);
      return false;
    }

    console.log('✅ Permission granted: master:read');
    console.log('✅ MASTER ACCESS GRANTED for:', state.url);
    return true;
  }
}
