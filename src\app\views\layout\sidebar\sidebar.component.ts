import { DOCUMENT, NgClass } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Inject, OnInit, OnDestroy, Renderer2, ViewChild, ChangeDetectorRef } from '@angular/core';
import { NavigationEnd, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { Subscription } from 'rxjs';

import { NgScrollbar } from 'ngx-scrollbar';

import { MENU } from './menu';
import { MenuItem } from './menu.model';

import { FeatherIconDirective } from '../../../core/feather-icon/feather-icon.directive';
import { AuthService } from '../../../core/services/auth.service';
import { PermissionService } from '../../../core/services/permission.service';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    RouterLink,
    RouterLinkActive,
    NgScrollbar,
    NgClass,
    FeatherIconDirective,
  ],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',

})
export class SidebarComponent implements OnInit, AfterViewInit, OnDestroy {
  currentLogo: string = 'images/brand-logo/brand-logo.png';

  private authSubscription: Subscription = new Subscription();
  private themeObserver: MutationObserver | null = null;

  updateLogoByTheme() {
    const theme = this.document.documentElement.getAttribute('data-bs-theme');
    this.currentLogo = theme === 'dark'
      ? 'images/brand-logo/brand-logo-white.png'
      : 'images/brand-logo/brand-logo.png';
  }

  @ViewChild('sidebarToggler') sidebarToggler: ElementRef;

  menuItems: MenuItem[] = [];
  @ViewChild('sidebarMenu') sidebarMenu: ElementRef;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    router: Router,
    private authService: AuthService,
    private permissionService: PermissionService,
    private cdr: ChangeDetectorRef
  ) {
    router.events.forEach((event) => {
      if (event instanceof NavigationEnd) {

        /**
         * Activating the current active item dropdown
         */
        this._activateMenuDropdown();

        /**
         * closing the sidebar
         */
        if (window.matchMedia('(max-width: 991px)').matches) {
          this.document.body.classList.remove('sidebar-open');
        }

      }
    });
  }

  /**
   * Manually refresh user permissions and update sidebar (for testing)
   */
  refreshPermissions(): void {
    console.log('🔄 Manually refreshing user permissions...');
    this.authService.refreshUserPermissions().subscribe({
      next: () => {
        console.log('✅ Permissions refreshed successfully');
      },
      error: (error) => {
        console.error('❌ Error refreshing permissions:', error);
      }
    });
  }

  /**
   * Toggle menu item expansion (Custom Accordion)
   */
  toggleMenuItem(item: MenuItem): void {
    if (this.hasItems(item)) {
      item.expanded = !item.expanded;
      console.log(`🔄 Toggled menu item '${item.label}': ${item.expanded ? 'expanded' : 'collapsed'}`);

      // Close other menu items at the same level (optional - for accordion behavior)
      // Uncomment the lines below if you want only one menu open at a time
      // this.menuItems.forEach(menuItem => {
      //   if (menuItem !== item && menuItem.subItems) {
      //     menuItem.expanded = false;
      //   }
      // });
    }
  }

  /**
   * Manual test method to refresh menu (for debugging)
   */
  testRefreshMenu(): void {
    console.log('🧪 Testing menu refresh...');
    console.log('📋 Current menu items:', this.menuItems.map(item => item.label));

    // Re-filter and update
    this.menuItems = this.filterMenuItemsByPermissions(MENU);
    this.cdr.detectChanges();

    console.log('✅ Test menu refresh completed');
  }

  /**
   * Filter menu items based on user permissions
   */
  private filterMenuItemsByPermissions(menuItems: MenuItem[]): MenuItem[] {
    return menuItems.filter(item => {
      console.log(`🔍 Processing menu item: '${item.label}'`);

      // Always show title items
      if (item.isTitle) {
        console.log(`📋 '${item.label}' is a title item - always showing`);
        return true;
      }

      // Check if item has required permissions
      if (item.requiredPermissions && item.requiredPermissions.length > 0) {
        console.log(`🔍 Checking permissions for '${item.label}':`, item.requiredPermissions);

        const hasPermission = this.permissionService.hasAnyPermission(item.requiredPermissions);
        console.log(`  - Permissions check result: ${hasPermission}`);

        console.log(`📊 Final permission result for '${item.label}': ${hasPermission}`);

        if (!hasPermission) {
          console.log(`🚫 Hiding menu item '${item.label}' - missing required permissions:`, item.requiredPermissions);
          return false;
        }
      }

      // Filter sub-items recursively if they exist
      if (item.subItems && item.subItems.length > 0) {
        const originalSubItemsLength = item.subItems.length;
        item.subItems = this.filterMenuItemsByPermissions(item.subItems);

        console.log(`📋 Sub-items for '${item.label}': ${originalSubItemsLength} → ${item.subItems.length}`);

        // Only hide parent item if it has permission requirements AND all sub-items are filtered out
        // If parent has no permission requirements, always show it even if sub-items are filtered
        if (item.subItems.length === 0 && item.requiredPermissions && item.requiredPermissions.length > 0) {
          console.log(`🚫 Hiding parent menu item '${item.label}' - has permission requirements and all sub-items filtered out`);
          return false;
        }
      }

      // If we reach here, item has no permission requirements or passed permission check
      if (!item.requiredPermissions || item.requiredPermissions.length === 0) {
        console.log(`✅ Showing menu item '${item.label}' - no permission requirements`);
      } else {
        console.log(`✅ Showing menu item '${item.label}' - permission check passed`);
      }
      return true;
    });
  }

  ngOnInit(): void {
    // Subscribe to real-time user changes for dynamic permission updates
    console.log('🧭 Sidebar: Setting up real-time permission filtering');

    this.authSubscription.add(
      this.authService.currentUser$.subscribe(user => {
        console.log('🔄 Sidebar: User permissions changed, re-filtering menu items');
        console.log('👤 Updated user:', user);
        console.log('🔑 Updated permissions:', user?.permissions || 'No permissions found');
        console.log('🔑 Updated role:', user?.role);
        console.log('🔑 Updated roles:', user?.roles);

        // Re-filter menu items with updated permissions
        const newMenuItems = this.filterMenuItemsByPermissions(MENU);
        console.log('📋 Filtered menu items:', newMenuItems.map(item => item.label));

        this.menuItems = newMenuItems;

        // Force change detection to update the UI
        this.cdr.detectChanges();

        console.log('✅ Menu updated with custom accordion functionality');
      })
    );

    this.updateLogoByTheme();

    // Setup theme observer
    this.themeObserver = new MutationObserver(() => this.updateLogoByTheme());
    this.themeObserver.observe(this.document.documentElement, {
      attributes: true,
      attributeFilter: ['data-bs-theme'],
    });

    const desktopMedium = window.matchMedia('(min-width:992px) and (max-width: 1199px)');
    desktopMedium.addEventListener('change', () => {
      this.iconSidebar;
    });
    this.iconSidebar(desktopMedium);
  }

  ngOnDestroy(): void {
    // Clean up subscriptions and observers
    this.authSubscription.unsubscribe();

    if (this.themeObserver) {
      this.themeObserver.disconnect();
    }
  }

  ngAfterViewInit() {
    // MetisMenu is already initialized in ngOnInit, just activate menu items
    this._activateMenuDropdown();
  }



  /**
   * Toggle the sidebar when the hamburger button is clicked
   */
  toggleSidebar(e: Event) {
    this.sidebarToggler.nativeElement.classList.toggle('active');
    if (window.matchMedia('(min-width: 992px)').matches) {
      e.preventDefault();
      this.document.body.classList.toggle('sidebar-folded');
    } else if (window.matchMedia('(max-width: 991px)').matches) {
      e.preventDefault();
      this.document.body.classList.toggle('sidebar-open');
    }
  }


  /**
   * Open the sidebar on hover when it is in a folded state
   */
  operSidebarFolded() {
    if (this.document.body.classList.contains('sidebar-folded')){
      this.document.body.classList.add("open-sidebar-folded");
    }
  }


  /**
   * Fold sidebar after mouse leave (in folded state)
   */
  closeSidebarFolded() {
    if (this.document.body.classList.contains('sidebar-folded')){
      this.document.body.classList.remove("open-sidebar-folded");
    }
  }

  /**
   * Sidebar folded on desktop screens with a width between 992px and 1199px
   */
  iconSidebar(mq: MediaQueryList) {
    if (mq.matches) {
      this.document.body.classList.add('sidebar-folded');
    } else {
      this.document.body.classList.remove('sidebar-folded');
    }
  }




  /**
   * Returns true or false depending on whether the given menu item has a child
   * @param item menuItem
   */
  hasItems(item: MenuItem) {
    return item.subItems !== undefined ? item.subItems.length > 0 : false;
  }




  /**
   * Reset the menus, then highlight the currently active menu item
   */
  _activateMenuDropdown() {
    this.resetMenuItems();
    this.activateMenuItems();
  }


  /**
   * Resets the menus
   */
  resetMenuItems() {

    const links = document.getElementsByClassName('nav-link-ref');

    for (let i = 0; i < links.length; i++) {
      const menuItemEl = links[i];
      menuItemEl.classList.remove('mm-active');
      const parentEl = menuItemEl.parentElement;

      if (parentEl) {
          parentEl.classList.remove('mm-active');
          const parent2El = parentEl.parentElement;

          if (parent2El) {
            parent2El.classList.remove('mm-show');
          }

          const parent3El = parent2El?.parentElement;
          if (parent3El) {
            parent3El.classList.remove('mm-active');

            if (parent3El.classList.contains('side-nav-item')) {
              const firstAnchor = parent3El.querySelector('.side-nav-link-a-ref');

              if (firstAnchor) {
                firstAnchor.classList.remove('mm-active');
              }
            }

            const parent4El = parent3El.parentElement;
            if (parent4El) {
              parent4El.classList.remove('mm-show');

              const parent5El = parent4El.parentElement;
              if (parent5El) {
                parent5El.classList.remove('mm-active');
              }
            }
          }
      }
    }
  };


  /**
   * Toggles the state of the menu items
   */
  activateMenuItems() {

    const links: any = document.getElementsByClassName('nav-link-ref');

    let menuItemEl = null;

    for (let i = 0; i < links.length; i++) {
      // tslint:disable-next-line: no-string-literal
        if (window.location.pathname === links[i]['pathname'] &&
            (!window.location.hash || window.location.hash.substring(1) === links[i]['hash'].substring(1))) {

            menuItemEl = links[i];

            break;
        }
    }

    if (menuItemEl) {
        menuItemEl.classList.add('mm-active');
        const parentEl = menuItemEl.parentElement;

        if (parentEl) {
            parentEl.classList.add('mm-active');

            const parent2El = parentEl.parentElement;
            if (parent2El) {
                parent2El.classList.add('mm-show');
            }

            const parent3El = parent2El.parentElement;
            if (parent3El) {
                parent3El.classList.add('mm-active');

                if (parent3El.classList.contains('side-nav-item')) {
                    const firstAnchor = parent3El.querySelector('.side-nav-link-a-ref');

                    if (firstAnchor) {
                        firstAnchor.classList.add('mm-active');
                    }
                }

                const parent4El = parent3El.parentElement;
                if (parent4El) {
                    parent4El.classList.add('mm-show');

                    const parent5El = parent4El.parentElement;
                    if (parent5El) {
                        parent5El.classList.add('mm-active');
                    }
                }
            }
        }
    }
  };


}
