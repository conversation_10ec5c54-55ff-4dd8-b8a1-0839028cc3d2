import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { Employee, EmployeeService } from '../../../../core/services/employee.service';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'app-employee-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FeatherIconDirective
  ],
  templateUrl: './employee-details.component.html',
  styleUrls: ['./employee-details.component.scss']
})
export class EmployeeDetailsComponent implements OnInit {
  employee: Employee | null = null;
  loading: boolean = true;
  error: string | null = null;
  isAdmin: boolean = false;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private employeeService: EmployeeService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    // Check if user has admin rights
    this.isAdmin = this.authService.isAdmin();
    
    // Get employee ID from route
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadEmployeeData(+id);
    } else {
      this.error = 'Employee ID not provided.';
      this.loading = false;
    }
  }

  loadEmployeeData(id: number): void {
    this.loading = true;
    this.error = null;
    
    this.employeeService.getEmployee(id).subscribe({
      next: (employee) => {
        this.employee = employee;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load employee data. Please try again.';
        this.loading = false;
        console.error('Error loading employee:', err);
      }
    });
  }

  toggleStatus(): void {
    if (!this.employee || !this.employee.id) return;

    const newStatus = !this.employee.user_active;
    const employeeId = this.employee.id;

    this.employeeService.setEmployeeStatus(employeeId, newStatus).subscribe({
      next: (updated) => {
        // Update the employee object
        this.employee = { ...this.employee!, user_active: newStatus };
      },
      error: (err) => {
        console.error('Error updating employee status:', err);
        // Show error message to user
      }
    });
  }

  formatDate(dateString: any): string {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
