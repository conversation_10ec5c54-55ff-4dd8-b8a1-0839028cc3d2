// Global type declarations for the application

// SweetAlert2 module declaration with proper types
declare module 'sweetalert2' {
  interface SweetAlertResult<T = any> {
    isConfirmed: boolean;
    isDenied: boolean;
    isDismissed: boolean;
    value?: T;
    dismiss?: any;
  }

  interface SweetAlertOptions {
    title?: string;
    text?: string;
    html?: string;
    icon?: 'success' | 'error' | 'warning' | 'info' | 'question';
    showCancelButton?: boolean;
    confirmButtonText?: string;
    cancelButtonText?: string;
    showConfirmButton?: boolean;
    timer?: number;
    timerProgressBar?: boolean;
    allowOutsideClick?: boolean;
    allowEscapeKey?: boolean;
    allowEnterKey?: boolean;
    input?: string;
    inputPlaceholder?: string;
    inputValue?: string;
    inputValidator?: (value: string) => Promise<string | null> | string | null;
    preConfirm?: (value: any) => Promise<any> | any;
    showLoaderOnConfirm?: boolean;
    backdrop?: boolean | string;
    toast?: boolean;
    position?: string;
    grow?: string;
    customClass?: any;
    buttonsStyling?: boolean;
    reverseButtons?: boolean;
    focusConfirm?: boolean;
    focusCancel?: boolean;
    showCloseButton?: boolean;
    closeButtonHtml?: string;
    imageUrl?: string;
    imageWidth?: number;
    imageHeight?: string;
    imageAlt?: string;
    animation?: boolean;
    heightAuto?: boolean;
    padding?: string;
    width?: string;
    background?: string;
    target?: string;
    scrollbarPadding?: boolean;
    [key: string]: any;
  }

  interface SweetAlert {
    fire(options: SweetAlertOptions): Promise<SweetAlertResult>;
    fire(title?: string, text?: string, icon?: string): Promise<SweetAlertResult>;
    close(): void;
    update(options: SweetAlertOptions): void;
    isVisible(): boolean;
    getTitle(): HTMLElement | null;
    getContent(): HTMLElement | null;
    getHtmlContainer(): HTMLElement | null;
    getImage(): HTMLElement | null;
    getIcon(): HTMLElement | null;
    getInputLabel(): HTMLElement | null;
    getValidationMessage(): HTMLElement | null;
    getActions(): HTMLElement | null;
    getConfirmButton(): HTMLElement | null;
    getDenyButton(): HTMLElement | null;
    getCancelButton(): HTMLElement | null;
    getLoader(): HTMLElement | null;
    getTimerLeft(): number | undefined;
    getTimerProgressBar(): HTMLElement | null;
    isTimerRunning(): boolean | undefined;
    increaseTimer(n: number): number | undefined;
    stopTimer(): number | undefined;
    resumeTimer(): number | undefined;
    toggleTimer(): number | undefined;
    isLoading(): boolean;
    clickConfirm(): void;
    clickDeny(): void;
    clickCancel(): void;
    showLoading(): void;
    hideLoading(): void;
    enableButtons(): void;
    disableButtons(): void;
    showValidationMessage(validationMessage: string): void;
    resetValidationMessage(): void;
    getInput(): HTMLInputElement | null;
    disableInput(): void;
    enableInput(): void;
    getFooter(): HTMLElement | null;
    getProgressSteps(): HTMLElement | null;
    setProgressSteps(progressSteps: string[]): void;
    showProgressSteps(): void;
    hideProgressSteps(): void;

    // DismissReason enum
    DismissReason: {
      cancel: string;
      backdrop: string;
      close: string;
      esc: string;
      timer: string;
    };
  }

  const Swal: SweetAlert;
  export default Swal;
}

// Additional global type declarations can be added here
