import { Routes } from '@angular/router';
import { dynamicAuthGuard } from '../../../core/guards/dynamic-auth.guard';

export default [
    {
        path: '',
        redirectTo: 'roles',
        pathMatch: 'full'
    },
    {
        path: 'roles',
        loadComponent: () => import('./roles/roles.component').then(c => c.RolesComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            permissions: ['roles:read', 'roles:create', 'roles:update', 'roles:delete', '*']
        }
    },
    {
        path: 'permissions',
        loadComponent: () => import('./permissions/permissions.component').then(c => c.PermissionsComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            permissions: ['permissions:read', 'permissions:create', 'permissions:update', 'permissions:delete', '*']
        }
    },
    {
        path: 'role-permissions',
        loadComponent: () => import('./role-permissions/role-permissions.component').then(c => c.RolePermissionsComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            permissions: ['roles:read', 'roles:update', '*']
        }
    },
    {
        path: 'analytics',
        loadComponent: () => import('./analytics/analytics.component').then(c => c.AnalyticsComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            permissions: ['roles:read', 'permissions:read', 'analytics:view', '*']
        }
    },
] as Routes;
