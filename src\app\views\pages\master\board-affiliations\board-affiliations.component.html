<!-- Board Affiliations Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">

        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-users me-2"></i>
              Board Affiliations Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_affiliations }} total affiliations,
              {{ statistics.active_affiliations }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Affiliation
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'"
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Affiliations
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'"
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Affiliations
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'"
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">

          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..."
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedType" (change)="onTypeFilter()">
                <option value="">All Types</option>
                <option *ngFor="let type of affiliationTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedMembershipType" (change)="onMembershipTypeFilter()">
                <option value="">All Memberships</option>
                <option *ngFor="let type of membershipTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCompensationType" (change)="onCompensationTypeFilter()">
                <option value="">All Compensation</option>
                <option *ngFor="let type of compensationTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCountry" (change)="onCountryFilter()">
                <option value="">All Countries</option>
                <option *ngFor="let country of countries" [value]="country">
                  {{ country }}
                </option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading board affiliations...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Organization</th>
                  <th>Type & Position</th>
                  <th>Contact</th>
                  <th>Location</th>
                  <th>Membership Details</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let affiliation of getCurrentList(); trackBy: trackByAffiliationId">
                  <td>
                    <div>
                      <strong>{{ affiliation.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ affiliation.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="affiliation.description">
                        {{ affiliation.description }}
                      </small>
                      <small class="d-block text-muted" *ngIf="affiliation.website">
                        <i class="feather icon-globe me-1"></i>
                        <a [href]="affiliation.website" target="_blank" class="text-decoration-none">
                          {{ affiliation.website }}
                        </a>
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ getAffiliationTypeLabel(affiliation.type) }}</span>
                    <small class="d-block text-muted mt-1" *ngIf="affiliation.position_title">
                      <strong>Position:</strong> {{ affiliation.position_title }}
                    </small>
                    <small class="d-block text-muted" *ngIf="affiliation.registration_number">
                      Reg: {{ affiliation.registration_number }}
                    </small>
                  </td>
                  <td>
                    <div *ngIf="affiliation.contact_email || affiliation.contact_phone">
                      <small class="d-block text-muted" *ngIf="affiliation.contact_email">
                        <i class="feather icon-mail me-1"></i>
                        {{ affiliation.contact_email }}
                      </small>
                      <small class="d-block text-muted" *ngIf="affiliation.contact_phone">
                        <i class="feather icon-phone me-1"></i>
                        {{ affiliation.contact_phone }}
                      </small>
                    </div>
                    <span *ngIf="!affiliation.contact_email && !affiliation.contact_phone" class="text-muted fst-italic">
                      No contact info
                    </span>
                  </td>
                  <td>
                    <div *ngIf="affiliation.city || affiliation.country">
                      <small class="d-block text-muted" *ngIf="affiliation.city">
                        <i class="feather icon-map-pin me-1"></i>
                        {{ affiliation.city }}
                      </small>
                      <small class="d-block text-muted" *ngIf="affiliation.country">
                        {{ affiliation.country }}
                      </small>
                    </div>
                    <span *ngIf="!affiliation.city && !affiliation.country" class="text-muted fst-italic">
                      Not specified
                    </span>
                  </td>
                  <td>
                    <div class="membership-details">
                      <span *ngIf="affiliation.membership_type" class="badge bg-primary">
                        {{ getMembershipTypeLabel(affiliation.membership_type) }}
                      </span>
                      <small class="d-block text-muted mt-1" *ngIf="affiliation.compensation_type">
                        <strong>Compensation:</strong> {{ getCompensationTypeLabel(affiliation.compensation_type) }}
                      </small>
                      <small class="d-block text-muted" *ngIf="affiliation.appointment_date">
                        <strong>Since:</strong> {{ affiliation.appointment_date | date:'MMM yyyy' }}
                      </small>
                      <small class="d-block text-muted" *ngIf="affiliation.term_end_date">
                        <strong>Until:</strong> {{ affiliation.term_end_date | date:'MMM yyyy' }}
                      </small>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(affiliation.is_active)">
                      {{ getStatusText(affiliation.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ affiliation.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(affiliation)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteAffiliation(affiliation)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreAffiliation(affiliation)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-users text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Affiliations' : 'No Board Affiliations Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No board affiliations have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No board affiliations match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first board affiliation.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Board Affiliation
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} affiliations
            </div>
            <ngb-pagination
              [(page)]="currentPage"
              [pageSize]="pageSize"
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_affiliations }}</h3>
                      <p class="mb-0">Total Affiliations</p>
                    </div>
                    <i class="feather icon-users" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_affiliations }}</h3>
                      <p class="mb-0">Active Affiliations</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-secondary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_affiliations }}</h3>
                      <p class="mb-0">Inactive Affiliations</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Affiliations by Type -->
          <div *ngIf="statistics.affiliations_by_type" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Affiliations by Type</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.affiliations_by_type)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getAffiliationTypeLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-primary fs-6">{{ statistics.affiliations_by_type[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Affiliations by Membership Type -->
          <div *ngIf="statistics.affiliations_by_membership_type" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Affiliations by Membership Type</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.affiliations_by_membership_type)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getMembershipTypeLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-info fs-6">{{ statistics.affiliations_by_membership_type[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Affiliations by Compensation -->
          <div *ngIf="statistics.affiliations_by_compensation" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Affiliations by Compensation Type</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.affiliations_by_compensation)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getCompensationTypeLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-warning fs-6">{{ statistics.affiliations_by_compensation[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Affiliations -->
          <div *ngIf="statistics.popular_affiliations?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Popular Board Affiliations</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Organization</th>
                      <th>Type</th>
                      <th>Membership</th>
                      <th>Location</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let affiliation of statistics.popular_affiliations">
                      <td>
                        <strong>{{ affiliation.name }}</strong>
                        <small class="d-block text-muted">{{ affiliation.code }}</small>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ getAffiliationTypeLabel(affiliation.type) }}</span>
                      </td>
                      <td>
                        <span *ngIf="affiliation.membership_type" class="badge bg-primary">
                          {{ getMembershipTypeLabel(affiliation.membership_type) }}
                        </span>
                        <span *ngIf="!affiliation.membership_type" class="text-muted">-</span>
                      </td>
                      <td>
                        <span *ngIf="affiliation.city">{{ affiliation.city }}, </span>{{ affiliation.country || 'Not specified' }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
