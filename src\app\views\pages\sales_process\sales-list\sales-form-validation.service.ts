import { Injectable } from '@angular/core';
import { 
  SALES_FORM_VALIDATION, 
  BUSINESS_VALIDATION_RULES,
  ValidationResult, 
  ValidationError, 
  ValidationSeverity,
  validateField,
  isFieldRequired
} from './sales-form-validation.config';

/**
 * Sales Form Validation Service
 * Provides comprehensive validation for the sales form using centralized configuration
 */
@Injectable({
  providedIn: 'root'
})
export class SalesFormValidationService {

  /**
   * Validate the entire sales form
   */
  validateSalesForm(formData: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Validate individual fields
    const fieldErrors = this.validateAllFields(formData);
    errors.push(...fieldErrors);

    // Validate business rules
    const businessErrors = this.validateBusinessRules(formData);
    errors.push(...businessErrors);

    // Validate people array
    const peopleErrors = this.validatePeopleArray(formData.people || []);
    errors.push(...peopleErrors);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate all configured fields
   */
  private validateAllFields(formData: any): ValidationError[] {
    const errors: ValidationError[] = [];

    // Core fields
    const coreFields = [
      'selectedLeadCategory',
      'selectedSource', 
      'selectedLeadDataType',
      'associateNameCategory',
      'company',
      'selectedConstitution',
      'selectedBoardAffiliation',
      'universityAffiliation'
    ];

    for (const fieldName of coreFields) {
      const fieldErrors = validateField(fieldName, formData[fieldName], formData);
      errors.push(...fieldErrors);
    }

    return errors;
  }

  /**
   * Validate business rules that span multiple fields
   */
  private validateBusinessRules(formData: any): ValidationError[] {
    const errors: ValidationError[] = [];

    // Check minimum people requirement
    if (!BUSINESS_VALIDATION_RULES.minimumPeopleRequired.validate(formData)) {
      errors.push({
        field: 'people',
        message: BUSINESS_VALIDATION_RULES.minimumPeopleRequired.errorMessage,
        severity: ValidationSeverity.ERROR
      });
    }

    // Check associate source validation
    if (!BUSINESS_VALIDATION_RULES.associateSourceValidation.validate(formData)) {
      errors.push({
        field: 'associateSource',
        message: BUSINESS_VALIDATION_RULES.associateSourceValidation.errorMessage,
        severity: ValidationSeverity.ERROR
      });
    }

    return errors;
  }

  /**
   * Validate people array
   */
  private validatePeopleArray(people: any[]): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!people || people.length === 0) {
      errors.push({
        field: 'people',
        message: 'At least one person contact is required',
        severity: ValidationSeverity.ERROR
      });
      return errors;
    }

    people.forEach((person, index) => {
      const personErrors = this.validatePerson(person, index);
      errors.push(...personErrors);
    });

    return errors;
  }

  /**
   * Validate a single person object
   */
  private validatePerson(person: any, index: number): ValidationError[] {
    const errors: ValidationError[] = [];

    // Validate connectWith
    if (!person.connectWith || person.connectWith.trim() === '') {
      errors.push({
        field: `people[${index}].connectWith`,
        message: `Person ${index + 1}: Connect With is required`,
        severity: ValidationSeverity.ERROR,
        value: person.connectWith
      });
    }

    // Validate name
    if (!person.name || person.name.trim() === '') {
      errors.push({
        field: `people[${index}].name`,
        message: `Person ${index + 1}: Name is required`,
        severity: ValidationSeverity.ERROR,
        value: person.name
      });
    } else if (person.name.trim().length < 2) {
      errors.push({
        field: `people[${index}].name`,
        message: `Person ${index + 1}: Name must be at least 2 characters long`,
        severity: ValidationSeverity.ERROR,
        value: person.name
      });
    }

    // Validate mobile
    if (!person.mobile || person.mobile.trim() === '') {
      errors.push({
        field: `people[${index}].mobile`,
        message: `Person ${index + 1}: Mobile number is required`,
        severity: ValidationSeverity.ERROR,
        value: person.mobile
      });
    } else if (!/^[0-9]{10}$/.test(person.mobile)) {
      errors.push({
        field: `people[${index}].mobile`,
        message: `Person ${index + 1}: Mobile number must be exactly 10 digits`,
        severity: ValidationSeverity.ERROR,
        value: person.mobile
      });
    }

    // Validate email (optional but must be valid if provided)
    if (person.email && person.email.trim() !== '') {
      if (!this.isValidEmail(person.email)) {
        errors.push({
          field: `people[${index}].email`,
          message: `Person ${index + 1}: Please enter a valid email address`,
          severity: ValidationSeverity.ERROR,
          value: person.email
        });
      }
    }

    return errors;
  }

  /**
   * Validate a specific field
   */
  validateSingleField(fieldName: string, value: any, formData: any): ValidationError[] {
    return validateField(fieldName, value, formData);
  }

  /**
   * Check if a field is required based on current form state
   */
  isFieldRequired(fieldName: string, formData: any): boolean {
    return isFieldRequired(fieldName, formData);
  }

  /**
   * Get user-friendly error messages for display
   */
  getFormattedErrors(validationResult: ValidationResult): string[] {
    return validationResult.errors.map(error => `• ${error.message}`);
  }

  /**
   * Get errors grouped by field
   */
  getErrorsByField(validationResult: ValidationResult): { [field: string]: string[] } {
    const errorsByField: { [field: string]: string[] } = {};

    validationResult.errors.forEach(error => {
      if (!errorsByField[error.field]) {
        errorsByField[error.field] = [];
      }
      errorsByField[error.field].push(error.message);
    });

    return errorsByField;
  }

  /**
   * Check if form has any validation errors
   */
  hasErrors(validationResult: ValidationResult): boolean {
    return validationResult.errors.length > 0;
  }

  /**
   * Get summary of validation results
   */
  getValidationSummary(validationResult: ValidationResult): string {
    const errorCount = validationResult.errors.length;
    const warningCount = validationResult.warnings.length;

    if (errorCount === 0 && warningCount === 0) {
      return 'All validations passed successfully';
    }

    let summary = '';
    if (errorCount > 0) {
      summary += `${errorCount} error${errorCount > 1 ? 's' : ''} found`;
    }
    if (warningCount > 0) {
      if (summary) summary += ', ';
      summary += `${warningCount} warning${warningCount > 1 ? 's' : ''} found`;
    }

    return summary;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate mobile number format
   */
  private isValidMobile(mobile: string): boolean {
    const mobileRegex = /^[0-9]{10}$/;
    return mobileRegex.test(mobile);
  }

  /**
   * Real-time validation for form fields as user types
   */
  validateFieldRealTime(fieldName: string, value: any, formData: any): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors = this.validateSingleField(fieldName, value, formData);
    
    return {
      isValid: errors.length === 0,
      errors: errors.filter(e => e.severity === ValidationSeverity.ERROR).map(e => e.message),
      warnings: errors.filter(e => e.severity === ValidationSeverity.WARNING).map(e => e.message)
    };
  }

  /**
   * Get validation rules for a specific field (for frontend display)
   */
  getFieldRules(fieldName: string): any {
    return SALES_FORM_VALIDATION[fieldName]?.rules || {};
  }

  /**
   * Check if field should show validation feedback
   */
  shouldShowValidation(fieldName: string, value: any, touched: boolean, formData: any): boolean {
    if (!touched) return false;
    
    const errors = this.validateSingleField(fieldName, value, formData);
    return errors.length > 0;
  }
}
