// Corporate Consultancies Component Styles

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  
  i {
    color: var(--bs-primary);
  }
}

// Navigation tabs styling
.nav-tabs {
  border-bottom: 2px solid var(--bs-gray-200);
  
  .nav-link {
    border: none;
    color: var(--bs-gray-600);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
    
    &:hover {
      color: var(--bs-primary);
      background: var(--bs-gray-50);
      border-radius: 8px 8px 0 0;
    }
    
    &.active {
      color: var(--bs-primary);
      background: var(--bs-white);
      border-bottom: 2px solid var(--bs-primary);
      font-weight: 600;
    }
    
    i {
      font-size: 0.875rem;
    }
  }
}

// Search and filter section
.input-group {
  .input-group-text {
    background: var(--bs-gray-50);
    border-color: var(--bs-gray-300);
    color: var(--bs-gray-600);
  }
  
  .form-control {
    border-color: var(--bs-gray-300);
    
    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }
}

.form-select {
  border-color: var(--bs-gray-300);
  
  &:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
  }
}

// Table styling
.table {
  th {
    background: var(--bs-gray-50);
    border-bottom: 2px solid var(--bs-gray-200);
    font-weight: 600;
    color: var(--bs-gray-700);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  td {
    vertical-align: middle;
    border-color: var(--bs-gray-200);
    
    .form-check-input {
      margin: 0;
    }
  }
  
  tbody tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background: var(--bs-gray-50);
    }
  }
}

// Consultancy details styling
.table td {
  strong {
    color: var(--bs-gray-800);
    font-weight: 600;
  }
  
  small {
    font-size: 0.8rem;
    line-height: 1.3;
    
    &.text-muted {
      color: var(--bs-gray-600) !important;
    }
    
    i {
      font-size: 0.75rem;
      opacity: 0.8;
    }
    
    a {
      color: var(--bs-primary);
      
      &:hover {
        color: var(--bs-primary);
        text-decoration: underline !important;
      }
    }
  }
}

// Performance metrics styling
.performance-metrics {
  small {
    font-size: 0.75rem;
    
    strong {
      font-weight: 600;
      color: var(--bs-gray-700);
    }
  }
}

// Status badges
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  
  &.bg-success {
    background-color: var(--bs-success) !important;
  }
  
  &.bg-secondary {
    background-color: var(--bs-gray-500) !important;
  }
  
  &.bg-info {
    background-color: var(--bs-info) !important;
  }
  
  &.bg-primary {
    background-color: var(--bs-primary) !important;
  }
  
  &.bg-warning {
    background-color: var(--bs-warning) !important;
    color: var(--bs-dark) !important;
  }
  
  &.bg-light {
    background-color: var(--bs-gray-100) !important;
    color: var(--bs-gray-700) !important;
    border: 1px solid var(--bs-gray-300);
  }
}

// Action buttons
.dropdown-toggle {
  &::after {
    display: none;
  }
}

.dropdown-menu {
  border: 1px solid var(--bs-gray-200);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  
  .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--bs-gray-50);
      color: var(--bs-primary);
    }
    
    &.text-danger:hover {
      background: var(--bs-danger);
      color: white;
    }
    
    &.text-success:hover {
      background: var(--bs-success);
      color: white;
    }
    
    i {
      width: 16px;
      font-size: 0.875rem;
    }
  }
}

// Statistics cards
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
  border: none;
  
  .card-body {
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transform: translate(30px, -30px);
    }
  }
  
  h3 {
    font-weight: 700;
    font-size: 2rem;
  }
  
  p {
    font-size: 0.875rem;
    opacity: 0.9;
  }
  
  i {
    opacity: 0.7;
  }
}

// Type and partnership level distribution
.border.rounded {
  border-color: var(--bs-gray-200) !important;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: var(--bs-primary) !important;
    background: rgba(var(--bs-primary-rgb), 0.05);
  }
  
  h6 {
    color: var(--bs-gray-700);
    font-weight: 600;
  }
  
  .badge {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
  }
}

// Empty state styling
.text-center {
  .feather {
    opacity: 0.5;
  }
  
  h5 {
    color: var(--bs-gray-600);
    font-weight: 500;
  }
  
  p {
    color: var(--bs-gray-500);
    font-size: 0.875rem;
  }
}

// Loading state
.spinner-border {
  width: 2rem;
  height: 2rem;
}

// Pagination styling
ngb-pagination {
  ::ng-deep {
    .page-link {
      color: var(--bs-gray-600);
      border-color: var(--bs-gray-300);
      
      &:hover {
        color: var(--bs-primary);
        background: var(--bs-gray-50);
        border-color: var(--bs-primary);
      }
    }
    
    .page-item.active .page-link {
      background: var(--bs-primary);
      border-color: var(--bs-primary);
    }
    
    .page-item.disabled .page-link {
      color: var(--bs-gray-400);
      background: var(--bs-gray-100);
      border-color: var(--bs-gray-300);
    }
  }
}

// Button styling enhancements
.btn {
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
  }
  
  &.btn-outline-info {
    border-color: var(--bs-info);
    color: var(--bs-info);
    
    &:hover {
      background: var(--bs-info);
      color: white;
    }
  }
  
  &.btn-outline-primary {
    border-color: var(--bs-primary);
    color: var(--bs-primary);
    
    &:hover {
      background: var(--bs-primary);
      color: white;
    }
  }
}

// Contact info styling
.table td div {
  small {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    i {
      margin-right: 0.25rem;
      width: 12px;
      flex-shrink: 0;
    }
  }
}

// Website link styling
a.text-decoration-none {
  transition: all 0.2s ease;
  
  &:hover {
    text-decoration: underline !important;
    color: var(--bs-primary) !important;
  }
}

// Responsive design
@media (max-width: 768px) {
  .card-title {
    font-size: 1.1rem;
    
    .btn {
      font-size: 0.875rem;
      padding: 0.375rem 0.75rem;
    }
  }
  
  .nav-tabs .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .dropdown-menu {
    font-size: 0.8rem;
  }
  
  // Stack filter controls on mobile
  .row.mb-3 > div {
    margin-bottom: 0.75rem;
  }
  
  // Adjust table for mobile
  .table {
    th, td {
      padding: 0.5rem 0.25rem;
      font-size: 0.8rem;
    }
    
    td {
      strong {
        font-size: 0.85rem;
      }
      
      small {
        font-size: 0.7rem;
      }
    }
  }
  
  // Hide less important columns on mobile
  @media (max-width: 576px) {
    .table {
      th:nth-child(6),
      td:nth-child(6),
      th:nth-child(7),
      td:nth-child(7) {
        display: none;
      }
    }
  }
  
  // Adjust statistics cards for mobile
  .card.bg-primary,
  .card.bg-success,
  .card.bg-warning,
  .card.bg-info {
    h3 {
      font-size: 1.5rem;
    }
    
    p {
      font-size: 0.8rem;
    }
  }
}

// Dark mode support
[data-theme="dark"] {
  .table {
    th {
      background: var(--bs-gray-800);
      color: var(--bs-gray-300);
      border-color: var(--bs-gray-700);
    }
    
    td {
      border-color: var(--bs-gray-700);
      color: var(--bs-gray-300);
      
      strong {
        color: var(--bs-gray-200);
      }
    }
    
    tbody tr:hover {
      background: var(--bs-gray-800);
    }
  }
  
  .nav-tabs {
    border-color: var(--bs-gray-700);
    
    .nav-link {
      color: var(--bs-gray-400);
      
      &:hover {
        background: var(--bs-gray-800);
        color: var(--bs-primary);
      }
      
      &.active {
        background: var(--bs-dark);
        color: var(--bs-primary);
      }
    }
  }
  
  .form-control,
  .form-select {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
    color: var(--bs-gray-300);
    
    &:focus {
      background: var(--bs-gray-800);
      border-color: var(--bs-primary);
      color: var(--bs-gray-300);
    }
  }
  
  .input-group-text {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
    color: var(--bs-gray-400);
  }
  
  .dropdown-menu {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
    
    .dropdown-item {
      color: var(--bs-gray-300);
      
      &:hover {
        background: var(--bs-gray-700);
        color: var(--bs-primary);
      }
    }
  }
  
  .border.rounded {
    border-color: var(--bs-gray-700) !important;
    background: var(--bs-gray-800);
    
    &:hover {
      background: rgba(var(--bs-primary-rgb), 0.1);
    }
    
    h6 {
      color: var(--bs-gray-300);
    }
  }
}
