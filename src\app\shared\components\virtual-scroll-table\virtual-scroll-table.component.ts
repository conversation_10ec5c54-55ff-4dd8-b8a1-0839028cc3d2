import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

export interface VirtualScrollColumn {
  key: string;
  label: string;
  width?: string;
  sortable?: boolean;
  template?: 'text' | 'link' | 'badge' | 'actions' | 'custom';
  customTemplate?: any;
}

export interface VirtualScrollConfig {
  itemSize: number;
  bufferSize?: number;
  enableSort?: boolean;
  enableSelection?: boolean;
}

/**
 * Virtual Scroll Table Component
 *
 * High-performance table component using CDK Virtual Scrolling
 * for handling large datasets efficiently.
 */
@Component({
  selector: 'app-virtual-scroll-table',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, ScrollingModule],
  template: `
    <div class="virtual-scroll-table">
      <!-- Table Header -->
      <div class="table-header" [style.width.px]="totalWidth">
        <div class="header-row">
          <div
            *ngFor="let column of columns; trackBy: trackByColumn"
            class="header-cell"
            [style.width]="column.width || 'auto'"
            [class.sortable]="column.sortable && config.enableSort"
            (click)="onSort(column)">
            {{ column.label }}
            <i *ngIf="column.sortable && config.enableSort"
               class="sort-icon"
               [class.asc]="sortColumn === column.key && sortDirection === 'asc'"
               [class.desc]="sortColumn === column.key && sortDirection === 'desc'">
            </i>
          </div>
        </div>
      </div>

      <!-- Virtual Scroll Container -->
      <cdk-virtual-scroll-viewport
        [itemSize]="config.itemSize"
        class="virtual-scroll-viewport">

        <div
          *cdkVirtualFor="let item of items; trackBy: trackByItem; let i = index"
          class="table-row"
          [class.selected]="isSelected(item)"
          (click)="onRowClick(item, i)">

          <div
            *ngFor="let column of columns; trackBy: trackByColumn"
            class="table-cell"
            [style.width]="column.width || 'auto'">

            <!-- Text Template -->
            <span *ngIf="!column.template || column.template === 'text'">
              {{ getColumnValue(item, column.key) }}
            </span>

            <!-- Link Template -->
            <a *ngIf="column.template === 'link'"
               [href]="getColumnValue(item, column.key)"
               class="table-link">
              {{ getColumnValue(item, column.key) }}
            </a>

            <!-- Badge Template -->
            <span *ngIf="column.template === 'badge'"
                  class="badge"
                  [class]="getBadgeClass(getColumnValue(item, column.key))">
              {{ getColumnValue(item, column.key) }}
            </span>

            <!-- Actions Template -->
            <div *ngIf="column.template === 'actions'" class="action-buttons">
              <button
                *ngFor="let action of getActions(item)"
                class="btn btn-sm"
                [class]="action.class"
                (click)="onAction(action.name, item, $event)">
                <i [class]="action.icon"></i>
                {{ action.label }}
              </button>
            </div>

            <!-- Custom Template -->
            <ng-container *ngIf="column.template === 'custom'">
              <ng-container *ngTemplateOutlet="column.customTemplate; context: { $implicit: item, column: column }">
              </ng-container>
            </ng-container>
          </div>
        </div>
      </cdk-virtual-scroll-viewport>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-overlay">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && items.length === 0" class="empty-state">
        <i class="empty-icon"></i>
        <h5>No data available</h5>
        <p>{{ emptyMessage || 'No items to display' }}</p>
      </div>
    </div>
  `,
  styles: [`
    .virtual-scroll-table {
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .table-header {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .header-row {
      display: flex;
      min-height: 48px;
    }

    .header-cell {
      padding: 12px 16px;
      font-weight: 600;
      border-right: 1px solid #dee2e6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      user-select: none;
    }

    .header-cell.sortable {
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .header-cell.sortable:hover {
      background-color: #e9ecef;
    }

    .sort-icon {
      margin-left: 8px;
      opacity: 0.5;
      transition: opacity 0.2s;
    }

    .sort-icon.asc::before {
      content: '↑';
      opacity: 1;
    }

    .sort-icon.desc::before {
      content: '↓';
      opacity: 1;
    }

    .virtual-scroll-viewport {
      flex: 1;
      height: 400px;
    }

    .table-row {
      display: flex;
      min-height: 48px;
      border-bottom: 1px solid #dee2e6;
      transition: background-color 0.2s;
    }

    .table-row:hover {
      background-color: #f8f9fa;
    }

    .table-row.selected {
      background-color: #e3f2fd;
    }

    .table-cell {
      padding: 12px 16px;
      border-right: 1px solid #dee2e6;
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .table-link {
      color: #0d6efd;
      text-decoration: none;
    }

    .table-link:hover {
      text-decoration: underline;
    }

    .badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      gap: 4px;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 20;
    }

    .empty-state {
      padding: 48px 24px;
      text-align: center;
      color: #6c757d;
    }

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
  `]
})
export class VirtualScrollTableComponent implements OnInit, OnDestroy {
  @Input() items: any[] = [];
  @Input() columns: VirtualScrollColumn[] = [];
  @Input() config: VirtualScrollConfig = { itemSize: 48 };
  @Input() loading = false;
  @Input() emptyMessage = '';
  @Input() selectedItems: any[] = [];

  @Output() rowClick = new EventEmitter<{ item: any, index: number }>();
  @Output() sort = new EventEmitter<{ column: string, direction: 'asc' | 'desc' }>();
  @Output() action = new EventEmitter<{ action: string, item: any }>();
  @Output() selectionChange = new EventEmitter<any[]>();

  sortColumn = '';
  sortDirection: 'asc' | 'desc' = 'asc';
  totalWidth = 0;

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.calculateTotalWidth();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  trackByColumn(index: number, column: VirtualScrollColumn): string {
    return column.key;
  }

  trackByItem(index: number, item: any): any {
    return item.id || item.uuid || index;
  }

  onSort(column: VirtualScrollColumn): void {
    if (!column.sortable || !this.config.enableSort) return;

    if (this.sortColumn === column.key) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column.key;
      this.sortDirection = 'asc';
    }

    this.sort.emit({ column: column.key, direction: this.sortDirection });
  }

  onRowClick(item: any, index: number): void {
    this.rowClick.emit({ item, index });

    if (this.config.enableSelection) {
      this.toggleSelection(item);
    }
  }

  onAction(action: string, item: any, event: Event): void {
    event.stopPropagation();
    this.action.emit({ action, item });
  }

  isSelected(item: any): boolean {
    return this.selectedItems.some(selected =>
      selected.id === item.id || selected.uuid === item.uuid
    );
  }

  toggleSelection(item: any): void {
    const isSelected = this.isSelected(item);

    if (isSelected) {
      this.selectedItems = this.selectedItems.filter(selected =>
        selected.id !== item.id && selected.uuid !== item.uuid
      );
    } else {
      this.selectedItems = [...this.selectedItems, item];
    }

    this.selectionChange.emit(this.selectedItems);
  }

  getColumnValue(item: any, key: string): any {
    return key.split('.').reduce((obj, prop) => obj?.[prop], item);
  }

  getBadgeClass(value: any): string {
    // Default badge classes based on common status values
    const statusClasses: { [key: string]: string } = {
      'active': 'bg-success',
      'inactive': 'bg-secondary',
      'pending': 'bg-warning',
      'completed': 'bg-success',
      'failed': 'bg-danger',
      'cancelled': 'bg-secondary'
    };

    return statusClasses[value?.toLowerCase()] || 'bg-primary';
  }

  getActions(item: any): any[] {
    // Override this method or pass actions through input
    return [
      { name: 'view', label: 'View', icon: 'fas fa-eye', class: 'btn-outline-primary' },
      { name: 'edit', label: 'Edit', icon: 'fas fa-edit', class: 'btn-outline-secondary' },
      { name: 'delete', label: 'Delete', icon: 'fas fa-trash', class: 'btn-outline-danger' }
    ];
  }

  private calculateTotalWidth(): void {
    this.totalWidth = this.columns.reduce((total, column) => {
      const width = column.width ? parseInt(column.width) : 150;
      return total + width;
    }, 0);
  }
}
