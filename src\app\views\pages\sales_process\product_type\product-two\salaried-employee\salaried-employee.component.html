<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card inner-card">
      <div class="card-body p-0">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">Salaried Employee Details</h4>
            <p class="text-secondary">Manage salaried employee information</p>
          </div>
        </div>

        <form [formGroup]="salariedForm" (ngSubmit)="onSubmit()">
          <!-- Personal Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Personal Information</h6>
            </div>

            <!-- Common Fields for All Product Types -->
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="age" class="form-label">Age</label>
              <input type="number" class="form-control" id="age" formControlName="age"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('age')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('age')?.errors?.required" class="invalid-feedback">Age is required</div>
              <div *ngIf="isFormSubmitted && (salariedForm.get('age')?.errors?.min || salariedForm.get('age')?.errors?.max)" class="invalid-feedback">Age must be between 18 and 100</div>
            </div>

            <!-- Home Loan Specific Fields -->
            <ng-container *ngIf="isHLForm">
              <!-- <h1>HL formmm</h1> -->
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="residentialStatus" class="form-label">Residential Status</label>
                <select class="form-select" id="residentialStatus" formControlName="residentialStatus"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('residentialStatus')?.errors}">
                  <option value="" selected>Select Residential Status</option>
                  <option *ngFor="let status of residentialStatuses" [value]="status">{{ status }}</option>
                </select>
                <div *ngIf="isFormSubmitted && salariedForm.get('residentialStatus')?.errors?.required" class="invalid-feedback">Residential status is required</div>
              </div>
            </ng-container>

            <!-- LAP/LRD Specific Fields -->
            <ng-container *ngIf="isLapLrdForm">
              <!-- <h1>LAP LRD formmm</h1> -->


              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="propertyOwnership" class="form-label">Property Ownership</label>
                <select class="form-select" id="propertyOwnership" formControlName="propertyOwnership"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('propertyOwnership')?.errors}">
                  <option value="" selected>Select Property Ownership</option>
                  <option *ngFor="let option of propertyOwnershipOptions" [value]="option">{{ option }}</option>
                </select>
                <div *ngIf="isFormSubmitted && salariedForm.get('propertyOwnership')?.errors?.required" class="invalid-feedback">Property ownership is required</div>
              </div>
            </ng-container>
          </div>

          <!-- Job Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Job Information</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="jobProfile" class="form-label">Employer Constitution</label>
              <select class="form-select" id="jobProfile" formControlName="jobProfile"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('jobProfile')?.errors}">
                <option value="" selected>Select Employer Constitution</option>
                <option *ngFor="let profile of isLapLrdForm ? jobProfileOptions : jobProfiles" [value]="profile">{{ profile }}</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('jobProfile')?.errors?.required" class="invalid-feedback">Employer constitution is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="companyName" class="form-label">Company/Office Name</label>
              <input type="text" class="form-control" id="companyName" formControlName="companyName"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('companyName')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('companyName')?.errors?.required" class="invalid-feedback">Company name is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="currentJobExperience" class="form-label">Current Job Experience (Years)</label>
              <input type="number" class="form-control" id="currentJobExperience" formControlName="currentJobExperience"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('currentJobExperience')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('currentJobExperience')?.errors?.required" class="invalid-feedback">Current job experience is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="totalPastJobExperience" class="form-label">Total Past Job Experience (Years)</label>
              <input type="number" class="form-control" id="totalPastJobExperience" formControlName="totalPastJobExperience"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('totalPastJobExperience')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('totalPastJobExperience')?.errors?.required" class="invalid-feedback">Total past job experience is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="officeDocument" class="form-label">Employment Documents</label>
              <select class="form-select" id="officeDocument" formControlName="officeDocument"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('officeDocument')?.errors}">
                <option value="" selected>Select Document</option>
                <option *ngFor="let doc of employmentDocumentOptions" [value]="doc">{{ doc }}</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('officeDocument')?.errors?.required" class="invalid-feedback">Employment document is required</div>
            </div>
          </div>

          <!-- Salary Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Salary Information</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="salaryDetails" class="form-label">Salary Documents</label>
              <select class="form-select" id="salaryDetails" formControlName="salaryDetails"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('salaryDetails')?.errors}">
                <option value="" selected>Select Salary Documents</option>
                <option *ngFor="let option of salaryDocumentOptions" [value]="option">{{ option }}</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('salaryDetails')?.errors?.required" class="invalid-feedback">Salary documents is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="totalGrossSalary" class="form-label">Total Gross Salary Amount</label>
              <input type="number" class="form-control" id="totalGrossSalary" formControlName="totalGrossSalary"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('totalGrossSalary')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('totalGrossSalary')?.errors?.required" class="invalid-feedback">Gross salary is required</div>
            </div>

            <!-- Deductions Section -->
            <div class="col-12 mb-3">
              <h6 class="text-secondary mb-2">Deductions</h6>
              <div class="row" formGroupName="deductions">
                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="pf" class="form-label">PF</label>
                  <input type="number" class="form-control" id="pf" formControlName="pf">
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="pt" class="form-label">PT</label>
                  <input type="number" class="form-control" id="pt" formControlName="pt">
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="hra" class="form-label">HRA</label>
                  <input type="number" class="form-control" id="hra" formControlName="hra">
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="esic" class="form-label">ESIC</label>
                  <input type="number" class="form-control" id="esic" formControlName="esic">
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="employeeLoan" class="form-label">Employee Loan</label>
                  <input type="number" class="form-control" id="employeeLoan" formControlName="employeeLoan">
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="societyLoan" class="form-label">Society Loan</label>
                  <input type="number" class="form-control" id="societyLoan" formControlName="societyLoan">
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-3">
                  <label for="other" class="form-label">Other</label>
                  <input type="number" class="form-control" id="other" formControlName="other">
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="totalDeduction" class="form-label">Total Deduction</label>
              <input type="number" class="form-control" id="totalDeduction" formControlName="totalDeduction" readonly>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="netSalary" class="form-label">Net Salary Amount</label>
              <input type="number" class="form-control" id="netSalary" formControlName="netSalary" readonly>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="salaryMode" class="form-label">Salary Mode</label>
              <select class="form-select" id="salaryMode" formControlName="salaryMode"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('salaryMode')?.errors}">
                <option value="" selected>Select Salary Mode</option>
                <option *ngFor="let mode of salaryModes" [value]="mode">{{ mode }}</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('salaryMode')?.errors?.required" class="invalid-feedback">Salary mode is required</div>
            </div>
          </div>

          <!-- ITR Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Annual Statutary Income Documents </h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="itrAvailable" class="form-label">ITR Documents</label>
              <select class="form-select" id="itrAvailable" formControlName="itrAvailable"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('itrAvailable')?.errors}">
                <option value="" selected>Select Option</option>
                <option value="ITR">ITR</option>
                <option value="Form 16">Form 16</option>
                <option value="Both">Both</option>
                <option value="NA">NA</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('itrAvailable')?.errors?.required" class="invalid-feedback">Please select an option</div>
            </div>


          </div>

          <!-- Banking Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary fw-semibold mb-0">Banking Information</h6>
              <small class="text-muted mb-3 d-block">Enter bank account details</small>
            </div>

            <div class="col-12">
              <div class="row">
                <div *ngFor="let bankAccount of bankAccountsArray.controls; let i = index" [formGroup]="$any(bankAccount)" class="col-md-6 col-lg-3 mb-3">
                  <label class="form-label">{{ bankAccount.get('accountType')?.value }} Account Bank Name</label>
                  <input type="text" class="form-control rounded-2" formControlName="bankName"
                    [ngClass]="{'is-invalid': isFormSubmitted && bankAccount.get('bankName')?.errors}"
                    placeholder="Enter bank name">
                  <input type="hidden" formControlName="accountType">
                  <div *ngIf="isFormSubmitted && bankAccount.get('bankName')?.errors?.required" class="invalid-feedback">Bank name is required</div>
                </div>
              </div>
            </div>
          </div>

        

          <!-- Loan Details Section -->
          <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="text-primary fw-semibold mb-0">Ongoing Loan Details</h6>
                <small class="text-muted">Excluding salary deduction</small>
              </div>
              <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addLoanDetail()">
                <i class="bi bi-plus-circle me-1"></i> Add Loan
              </button>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <div class="card shadow-sm rounded-3 border-0 mb-4">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead class="bg-light">
                        <tr>
                          <th style="width: 8%;" class="fw-medium text-center">Action</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '15%' : '15%'" class="fw-medium">Loan Type</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '16%' : '18%'" class="fw-medium">Bank Name</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '13%' : '15%'" class="fw-medium">Sanctioned Amt</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '13%' : '15%'" class="fw-medium">O/S Amt</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '10%' : '12%'" class="fw-medium">Tenure</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '10%' : '12%'" class="fw-medium">EMI Amt</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '8%' : '10%'" class="fw-medium">Default</th>
                          <th *ngIf="hasAnyLoanWithDefault()" style="width: 12%;" class="fw-medium">Example</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngIf="loanDetailsArray.length === 0">
                          <td [attr.colspan]="hasAnyLoanWithDefault() ? 9 : 8" class="text-center py-4 text-muted">
                            <div class="d-flex justify-content-center align-items-center">
                              <i class="bi bi-info-circle me-2"></i>
                              <span>No data found</span>
                            </div>
                          </td>
                        </tr>
                        <tr *ngFor="let loanDetail of loanDetailsArray.controls; let i = index" [formGroup]="$any(loanDetail)">
                          <td class="text-center">
                            <a href="javascript:void(0);" (click)="removeLoanDetail(i)" title="Delete">
                              <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                            </a>
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="loanType"
                              [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('loanType')?.errors}">
                              <option value="" selected>Select</option>
                              <option *ngFor="let type of loanTypes" [value]="type">{{ type }}</option>
                            </select>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="bankName"
                              [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('bankName')?.errors}">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="sanctionedAmount"
                              [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('sanctionedAmount')?.errors}">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="outstandingAmount"
                              [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('outstandingAmount')?.errors}">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="tenure"
                              [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('tenure')?.errors}">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="emiAmount"
                              [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('emiAmount')?.errors}">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="default">
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                            </select>
                          </td>
                          <td *ngIf="hasAnyLoanWithDefault()">
                            <input
                              *ngIf="loanDetail.get('default')?.value === 'Yes'"
                              type="text"
                              class="form-control form-control-sm rounded-2"
                              formControlName="example"
                              placeholder="Enter example">
                            <span *ngIf="loanDetail.get('default')?.value !== 'Yes'" class="text-muted">-</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Investment Details Section -->
          <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="text-primary fw-semibold mb-0">Investment Details</h6>
                <small class="text-muted">FD/RD/MIS/LIC/MF</small>
              </div>
              <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addInvestment()">
                <i class="bi bi-plus-circle me-1"></i> Add Investment
              </button>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <div class="card shadow-sm rounded-3 border-0 mb-4">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead class="bg-light">
                        <tr>
                          <th style="width: 5%;" class="fw-medium text-center">Action</th>
                          <th style="width: 15%;" class="fw-medium">Inv. Product</th>
                          <th style="width: 15%;" class="fw-medium">Institute Name</th>
                          <th style="width: 11%;" class="fw-medium">Yearly Amt.</th>
                          <th style="width: 13%;" class="fw-medium">Inv. Mode</th>
                          <th style="width: 11%;" class="fw-medium">Start Dt.</th>
                          <th style="width: 11%;" class="fw-medium">End Dt.</th>
                          <th style="width: 19%;" class="fw-medium">Current Saving Amt.</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngIf="investmentsArray.length === 0">
                          <td colspan="8" class="text-center py-4 text-muted">
                            <div class="d-flex justify-content-center align-items-center">
                              <i class="bi bi-info-circle me-2"></i>
                              <span>No data found</span>
                            </div>
                          </td>
                        </tr>
                        <tr *ngFor="let investment of investmentsArray.controls; let i = index" [formGroup]="$any(investment)">
                          <td class="text-center">
                            <a href="javascript:void(0);" (click)="removeInvestment(i)" title="Delete">
                              <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                            </a>
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="investmentProduct"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('investmentProduct')?.errors}">
                              <option value="" selected>Select</option>
                              <option *ngFor="let product of investmentProducts" [value]="product">{{ product }}</option>
                            </select>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="instituteName"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('instituteName')?.errors}">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="yearlyAmount"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('yearlyAmount')?.errors}">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="investmentMode"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('investmentMode')?.errors}">
                              <option value="" selected>Select</option>
                              <option *ngFor="let mode of investmentModes" [value]="mode">{{ mode }}</option>
                            </select>
                          </td>
                          <td>
                            <input type="date" class="form-control form-control-sm rounded-2" formControlName="startDate"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('startDate')?.errors}">
                          </td>
                          <td>
                            <input type="date" class="form-control form-control-sm rounded-2" formControlName="endDate"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('endDate')?.errors}">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="currentSavingAmount"
                              [ngClass]="{'is-invalid': isFormSubmitted && investment.get('currentSavingAmount')?.errors}">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Co-Applicants Section -->
          <div class="row mb-4" *ngIf="!isCoApplicantForm">
            <div class="col-12 d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="text-primary fw-semibold mb-0">Co-Applicants</h6>
                <small class="text-muted">Add all co-applicant information</small>
              </div>
              <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addCoApplicant()">
                <i class="bi bi-plus-circle me-1"></i> Add Co-Applicant
              </button>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <div class="card shadow-sm rounded-3 border-0 mb-4">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead class="bg-light">
                        <tr>
                          <th style="width: 5%;" class="fw-medium text-center">Action</th>
                          <th style="width: 17%;" class="fw-medium">Name</th>
                          <th style="width: 15%;" class="fw-medium">Relation</th>
                          <th style="width: 9%;" class="fw-medium">Age</th>
                          <th style="width: 17%;" class="fw-medium">Occupation</th>
                          <th style="width: 17%;" class="fw-medium">Income Source</th>
                          <th style="width: 20%;" class="fw-medium">Monthly Income</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngIf="coApplicantsArray?.length === 0">
                          <td colspan="7" class="text-center py-4 text-muted">
                            <div class="d-flex justify-content-center align-items-center">
                              <i class="bi bi-info-circle me-2"></i>
                              <span>No data found</span>
                            </div>
                          </td>
                        </tr>
                        <tr *ngFor="let coApplicant of coApplicantsArray?.controls; let i = index" [formGroup]="$any(coApplicant)">
                          <td class="text-center">
                            <div class="d-flex justify-content-center">
                              <a href="javascript:void(0);" class="action-icon me-2"
                                 (click)="editCoApplicantDetails(i)"
                                 title="Edit">
                                <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                              </a>
                              <a href="javascript:void(0);" class="action-icon" (click)="removeCoApplicant(i)" title="Delete">
                                <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                              </a>
                            </div>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="name"
                              [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('name')?.errors}">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="relation"
                              [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('relation')?.errors}">
                              <option value="" selected>Select</option>
                              <option *ngFor="let type of relationshipTypes" [value]="type">{{ type }}</option>
                            </select>
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="age"
                              [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('age')?.errors}">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="occupation"
                              [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('occupation')?.errors}"
                              (change)="coApplicant.get('occupation')?.value === 'Salaried Employee' ?
                                onCoApplicantOccupationChange(i, coApplicant.get('occupation')?.value, salariedModal) :
                                onCoApplicantOccupationChange(i, coApplicant.get('occupation')?.value, selfEmployedModal)">
                              <option value="" selected>Select</option>
                              <option *ngFor="let type of occupationTypes" [value]="type">{{ type }}</option>
                            </select>
                          </td>
                          <td>
                            <div class="d-flex align-items-center">
                              <input type="text" class="form-control form-control-sm rounded-2 me-2" formControlName="incomeSource"
                                [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('incomeSource')?.errors}">
                              <button type="button" class="btn btn-sm btn-outline-primary"
                                (click)="editCoApplicantDetails(i)">
                                <i class="bi bi-pencil-square"></i>
                              </button>
                            </div>
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="monthlyIncome"
                              [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('monthlyIncome')?.errors}">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
  <!-- Property Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Property Information</h6>
            </div>

            <!-- Common fields -->

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selectedProductSubType !== 'LAP'">
              <label for="propertyPurchased" class="form-label">Property Purchased</label>
              <select class="form-select" id="propertyPurchased" formControlName="propertyPurchased"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('propertyPurchased')?.errors}">
                <option value="" selected>Select Option</option>
                <option *ngFor="let option of propertyPurchasedOptions" [value]="option">{{ option }}</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('propertyPurchased')?.errors?.required" class="invalid-feedback">Please select an option</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="location" class="form-label">Location</label>
              <input type="text" class="form-control" id="location" formControlName="location"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('location')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('location')?.errors?.required" class="invalid-feedback">Location is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="typeOfProperty" class="form-label">Type Of Property</label>
              <select class="form-select" id="typeOfProperty" formControlName="typeOfProperty"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('typeOfProperty')?.errors}">
                <option value="" selected>Select Option</option>
                <option *ngFor="let option of filteredPropertyTypeOptions" [value]="option">{{ option }}</option>
              </select>
              <div *ngIf="isFormSubmitted && salariedForm.get('typeOfProperty')?.errors?.required" class="invalid-feedback">Please select a property type</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="isMainApplicantFlatSelected">
              <label for="configuration" class="form-label">Configuration (Only for Flat)</label>
              <select class="form-select" id="configuration" formControlName="configuration">
                <option value="" selected>Select Option</option>
                <option *ngFor="let option of configurationOptions" [value]="option">{{ option }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="areaSqFeet" class="form-label">Area (Sq.Feet)</label>
              <input type="number" class="form-control" id="areaSqFeet" formControlName="areaSqFeet"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('areaSqFeet')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('areaSqFeet')?.errors?.required" class="invalid-feedback">Area is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="salable" class="form-label">Salable</label>
              <input type="number" class="form-control" id="salable" formControlName="salable"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('salable')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('salable')?.errors?.required" class="invalid-feedback">Salable area is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="carpet" class="form-label">Carpet</label>
              <input type="number" class="form-control" id="carpet" formControlName="carpet"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('carpet')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('carpet')?.errors?.required" class="invalid-feedback">Carpet area is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ageOfProperty" class="form-label">Age of Property</label>
              <select class="form-select" id="ageOfProperty" formControlName="ageOfProperty">
                <option value="" selected>Select Years</option>
                <option *ngFor="let year of propertyAgeOptions" [value]="year">{{ year }} Year{{ year !== '1' ? 's' : '' }}</option>
              </select>
            </div>

            <!-- Developer Purchase Fields -->
            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="isMainApplicantDeveloperSelected">
              <label for="constructionStage" class="form-label">Construction Stage (%)</label>
              <input type="number" class="form-control" id="constructionStage" formControlName="constructionStage"
                min="0" max="100" placeholder="Enter percentage">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="approvalAuthority" class="form-label">Approval Authority</label>
              <select class="form-select" id="approvalAuthority" formControlName="approvalAuthority">
                <option value="" selected>Select Authority</option>
                <option *ngFor="let authority of approvalAuthorityOptions" [value]="authority">{{ authority }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label class="form-label">Documents Status</label>
              <ng-select
                [items]="documentsStatusOptions"
                [multiple]="true"
                bindLabel="name"
                bindValue="id"
                [closeOnSelect]="false"
                [searchable]="false"
                placeholder="Select Documents"
                formControlName="documentsStatus">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <input id="doc-{{index}}" type="checkbox" [ngModel]="item$.selected"/> {{item.name}}
                </ng-template>
              </ng-select>
            </div>

            <!-- LRD specific fields -->
            <ng-container *ngIf="selectedProductSubType === 'LRD'">
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="loiDetails" class="form-label">LOI Details</label>
                <input type="text" class="form-control" id="loiDetails" formControlName="loiDetails"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('loiDetails')?.errors}">
                <div *ngIf="isFormSubmitted && salariedForm.get('loiDetails')?.errors?.required" class="invalid-feedback">LOI Details is required</div>
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="companyNameLicensee" class="form-label">Company Name (licensee)</label>
                <input type="text" class="form-control" id="companyNameLicensee" formControlName="companyNameLicensee"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('companyNameLicensee')?.errors}">
                <div *ngIf="isFormSubmitted && salariedForm.get('companyNameLicensee')?.errors?.required" class="invalid-feedback">Company Name is required</div>
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="leaseTenor" class="form-label">Lease Tenor</label>
                <input type="text" class="form-control" id="leaseTenor" formControlName="leaseTenor"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('leaseTenor')?.errors}">
                <div *ngIf="isFormSubmitted && salariedForm.get('leaseTenor')?.errors?.required" class="invalid-feedback">Lease Tenor is required</div>
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="startDate" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="startDate" formControlName="startDate"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('startDate')?.errors}">
                <div *ngIf="isFormSubmitted && salariedForm.get('startDate')?.errors?.required" class="invalid-feedback">Start Date is required</div>
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="monthlyRent" class="form-label">Monthly Rent</label>
                <input type="number" class="form-control" id="monthlyRent" formControlName="monthlyRent"
                  [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('monthlyRent')?.errors}">
                <div *ngIf="isFormSubmitted && salariedForm.get('monthlyRent')?.errors?.required" class="invalid-feedback">Monthly Rent is required</div>
              </div>
            </ng-container>


            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="isMainApplicantDeveloperSelected">
              <label for="apfFrom" class="form-label">APF From</label>
              <input type="text" class="form-control" id="apfFrom" formControlName="apfFrom"
                placeholder="Enter APF From">
            </div>

            <!-- Additional Property Fields -->
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="noOfChainAgreement" class="form-label">No of Chain Agreement</label>
              <select class="form-select" id="noOfChainAgreement" formControlName="noOfChainAgreement">
                <option value="" selected>Select Number</option>
                <option *ngFor="let option of chainAgreementOptions" [value]="option">{{ option }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="agreementStatus" class="form-label">Agreement Status</label>
              <select class="form-select" id="agreementStatus" formControlName="agreementStatus">
                <option value="" selected>Select Status</option>
                <option *ngFor="let option of agreementStatusOptions" [value]="option">{{ option }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="currentMV" class="form-label">Current M.V.</label>
              <input type="number" class="form-control" id="currentMV" formControlName="currentMV"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('currentMV')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('currentMV')?.errors?.required" class="invalid-feedback">Current M.V. is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="agreementValue" class="form-label">Agreement Value</label>
              <input type="number" class="form-control" id="agreementValue" formControlName="agreementValue"
                [ngClass]="{'is-invalid': isFormSubmitted && salariedForm.get('agreementValue')?.errors}">
              <div *ngIf="isFormSubmitted && salariedForm.get('agreementValue')?.errors?.required" class="invalid-feedback">Agreement Value is required</div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="loanAmtRequired" class="form-label">Loan Amount</label>
              <input type="number" class="form-control" id="loanAmtRequired" formControlName="loanAmtRequired" readonly>
            </div>

            <!-- OCR Fields - Hidden for LAP/LRD -->
            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selectedProductSubType !== 'LAP' && selectedProductSubType !== 'LRD'">
              <label for="ocrRequired" class="form-label">OCR Required (Auto Calculated)</label>
              <input type="number" class="form-control" id="ocrRequired" formControlName="ocrRequired" readonly>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selectedProductSubType !== 'LAP' && selectedProductSubType !== 'LRD'">
              <label for="ocrPercentage" class="form-label">OCR % (Auto Calculated)</label>
              <input type="number" class="form-control" id="ocrPercentage" formControlName="ocrPercentage" readonly>
            </div>

            <!-- LTV Field - Shown for LAP/LRD -->
            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selectedProductSubType === 'LAP' || selectedProductSubType === 'LRD'">
              <label for="ltv" class="form-label">LTV</label>
              <input type="number" class="form-control" id="ltv" formControlName="ltv" placeholder="Enter LTV">
            </div>

            <!-- BT Fields -->
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ifBt" class="form-label">If BT</label>
              <select class="form-select" id="ifBt" formControlName="ifBt">
                <option value="" selected>Select Option</option>
                <option *ngFor="let option of btOptions" [value]="option">{{ option }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="bankName" class="form-label">Bank Name</label>
              <input type="text" class="form-control" id="bankName" formControlName="bankName">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="currentOutstandingLoanAmount" class="form-label">Current Outstanding Loan Amount</label>
              <input type="number" class="form-control" id="currentOutstandingLoanAmount" formControlName="currentOutstandingLoanAmount">
            </div>



            <!-- LAP/LRD specific property fields -->
            <ng-container *ngIf="isLapLrdForm">
              <!-- LAP/LRD specific fields -->



            </ng-container>
          </div>
          <!-- Submit and Cancel Buttons -->
          
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Co-Applicant Salaried Employee Form Modal -->
<ng-template #salariedModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Co-Applicant Salaried Employee Form</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss('Cross click')" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="coApplicantSalariedForm" *ngIf="coApplicantSalariedForm">
      <!-- Personal Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Personal Information</h6>
        </div>

        <!-- Common Fields for All Product Types -->
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppAge" class="form-label">Age</label>
          <input type="number" class="form-control" id="coAppAge" formControlName="age"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('age')?.errors}">
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('age')?.errors?.required" class="invalid-feedback">Age is required</div>
        </div>

        <!-- Home Loan Specific Fields -->
        <ng-container *ngIf="isHLForm">


          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppResidentialStatus" class="form-label">Residential Status</label>
            <select class="form-select" id="coAppResidentialStatus" formControlName="residentialStatus"
              [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('residentialStatus')?.errors}">
              <option value="" selected>Select Status</option>
              <option *ngFor="let status of residentialStatuses" [value]="status">{{ status }}</option>
            </select>
            <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('residentialStatus')?.errors?.required" class="invalid-feedback">Residential status is required</div>
          </div>
        </ng-container>

        <!-- LAP/LRD Specific Fields -->
        <ng-container *ngIf="isLapLrdForm">


          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppPropertyOwnership" class="form-label">Property Ownership</label>
            <select class="form-select" id="coAppPropertyOwnership" formControlName="propertyOwnership"
              [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('propertyOwnership')?.errors}">
              <option value="" selected>Select Property Ownership</option>
              <option *ngFor="let option of propertyOwnershipOptions" [value]="option">{{ option }}</option>
            </select>
            <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('propertyOwnership')?.errors?.required" class="invalid-feedback">Property ownership is required</div>
          </div>
        </ng-container>
      </div>

      <!-- Job Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Job Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppJobProfile" class="form-label">Employer Constitution</label>
          <select class="form-select" id="coAppJobProfile" formControlName="jobProfile"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('jobProfile')?.errors}">
            <option value="" selected>Select Employer Constitution</option>
            <option *ngFor="let profile of isLapLrdForm ? jobProfileOptions : jobProfiles" [value]="profile">{{ profile }}</option>
          </select>
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('jobProfile')?.errors?.required" class="invalid-feedback">Employer constitution is required</div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppCompanyName" class="form-label">Company/Office Name</label>
          <input type="text" class="form-control" id="coAppCompanyName" formControlName="companyName"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('companyName')?.errors}">
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('companyName')?.errors?.required" class="invalid-feedback">Company name is required</div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppCurrentJobExperience" class="form-label">Current Job Experience (Years)</label>
          <input type="number" class="form-control" id="coAppCurrentJobExperience" formControlName="currentJobExperience"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('currentJobExperience')?.errors}">
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('currentJobExperience')?.errors?.required" class="invalid-feedback">Current job experience is required</div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppTotalPastJobExperience" class="form-label">Total Past Job Experience (Years)</label>
          <input type="number" class="form-control" id="coAppTotalPastJobExperience" formControlName="totalPastJobExperience"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('totalPastJobExperience')?.errors}">
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('totalPastJobExperience')?.errors?.required" class="invalid-feedback">Past job experience is required</div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppOfficeDocument" class="form-label">Employment Documents</label>
          <select class="form-select" id="coAppOfficeDocument" formControlName="officeDocument"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('officeDocument')?.errors}">
            <option value="" selected>Select Document</option>
            <option *ngFor="let doc of employmentDocumentOptions" [value]="doc">{{ doc }}</option>
          </select>
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('officeDocument')?.errors?.required" class="invalid-feedback">Employment document is required</div>
        </div>
      </div>

      <!-- Salary Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Salary Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSalaryDetails" class="form-label">Salary Documents</label>
          <select class="form-select" id="coAppSalaryDetails" formControlName="salaryDetails"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('salaryDetails')?.errors}">
            <option value="" selected>Select Salary Documents</option>
            <option *ngFor="let option of salaryDocumentOptions" [value]="option">{{ option }}</option>
          </select>
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('salaryDetails')?.errors?.required" class="invalid-feedback">Salary documents is required</div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppTotalGrossSalary" class="form-label">Total Gross Salary Amount</label>
          <input type="number" class="form-control" id="coAppTotalGrossSalary" formControlName="totalGrossSalary"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('totalGrossSalary')?.errors}">
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('totalGrossSalary')?.errors?.required" class="invalid-feedback">Gross salary is required</div>
        </div>

        <!-- Deductions Section -->
        <div class="col-12 mb-3">
          <h6 class="text-secondary mb-2">Deductions</h6>
          <div class="row" formGroupName="deductions">
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppPf" class="form-label">PF</label>
              <input type="number" class="form-control" id="coAppPf" formControlName="pf">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppPt" class="form-label">PT</label>
              <input type="number" class="form-control" id="coAppPt" formControlName="pt">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppHra" class="form-label">HRA</label>
              <input type="number" class="form-control" id="coAppHra" formControlName="hra">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppEsic" class="form-label">ESIC</label>
              <input type="number" class="form-control" id="coAppEsic" formControlName="esic">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppEmployeeLoan" class="form-label">Employee Loan</label>
              <input type="number" class="form-control" id="coAppEmployeeLoan" formControlName="employeeLoan">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppSocietyLoan" class="form-label">Society Loan</label>
              <input type="number" class="form-control" id="coAppSocietyLoan" formControlName="societyLoan">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppOther" class="form-label">Other</label>
              <input type="number" class="form-control" id="coAppOther" formControlName="other">
            </div>
          </div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppTotalDeduction" class="form-label">Total Deduction</label>
          <input type="number" class="form-control" id="coAppTotalDeduction" formControlName="totalDeduction" readonly>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppNetSalary" class="form-label">Net Salary Amount</label>
          <input type="number" class="form-control" id="coAppNetSalary" formControlName="netSalary" readonly>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSalaryMode" class="form-label">Salary Mode</label>
          <select class="form-select" id="coAppSalaryMode" formControlName="salaryMode"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('salaryMode')?.errors}">
            <option value="" selected>Select Salary Mode</option>
            <option *ngFor="let mode of salaryModes" [value]="mode">{{ mode }}</option>
          </select>
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('salaryMode')?.errors?.required" class="invalid-feedback">Salary mode is required</div>
        </div>
      </div>

      <!-- ITR Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Annual Statutary Income Documents </h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppItrAvailable" class="form-label">ITR Documents</label>
          <select class="form-select" id="coAppItrAvailable" formControlName="itrAvailable"
            [ngClass]="{'is-invalid': isCoAppFormSubmitted && coApplicantSalariedForm.get('itrAvailable')?.errors}">
            <option value="" selected>Select Option</option>
            <option value="ITR">ITR</option>
            <option value="Form 16">Form 16</option>
            <option value="Both">Both</option>
            <option value="NA">NA</option>
          </select>
          <div *ngIf="isCoAppFormSubmitted && coApplicantSalariedForm.get('itrAvailable')?.errors?.required" class="invalid-feedback">Please select an option</div>
        </div>


      </div>

      <!-- Banking Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary fw-semibold mb-0">Banking Information</h6>
          <small class="text-muted mb-3 d-block">Enter bank account details</small>
        </div>

        <div class="col-12">
          <div class="row">
            <div *ngFor="let bankAccount of coAppBankAccountsArray.controls; let i = index" [formGroup]="$any(bankAccount)" class="col-md-6 mb-3">
              <label class="form-label">{{ bankAccount.get('accountType')?.value }} Account Bank Name</label>
              <input type="text" class="form-control rounded-2" formControlName="bankName"
                [ngClass]="{'is-invalid': isCoAppFormSubmitted && bankAccount.get('bankName')?.errors}"
                placeholder="Enter bank name">
              <input type="hidden" formControlName="accountType">
              <div *ngIf="isCoAppFormSubmitted && bankAccount.get('bankName')?.errors?.required" class="invalid-feedback">Bank name is required</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loan Details Section -->
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
          <div>
            <h6 class="text-primary fw-semibold mb-0">Ongoing Loan Details</h6>
            <small class="text-muted">Excluding salary deduction</small>
          </div>
          <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addCoAppLoanDetail()">
            <i class="bi bi-plus-circle me-1"></i> Add Loan
          </button>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <div class="card shadow-sm rounded-3 border-0 mb-4">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 8%;" class="fw-medium text-center">Action</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '15%' : '15%'" class="fw-medium">Loan Type</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '16%' : '18%'" class="fw-medium">Bank Name</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '13%' : '15%'" class="fw-medium">Sanctioned Amt</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '13%' : '15%'" class="fw-medium">O/S Amt</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '10%' : '12%'" class="fw-medium">Tenure</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '10%' : '12%'" class="fw-medium">EMI Amt</th>
                      <th [style.width]="hasAnyCoAppLoanWithDefault() ? '8%' : '10%'" class="fw-medium">Default</th>
                      <th *ngIf="hasAnyCoAppLoanWithDefault()" style="width: 12%;" class="fw-medium">Example</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngIf="coAppLoanDetailsArray.length === 0">
                      <td [attr.colspan]="hasAnyCoAppLoanWithDefault() ? 9 : 8" class="text-center py-4 text-muted">
                        <div class="d-flex justify-content-center align-items-center">
                          <i class="bi bi-info-circle me-2"></i>
                          <span>No data found</span>
                        </div>
                      </td>
                    </tr>
                    <tr *ngFor="let loanDetail of coAppLoanDetailsArray.controls; let i = index" [formGroup]="$any(loanDetail)">
                      <td class="text-center">
                        <a href="javascript:void(0);" (click)="removeCoAppLoanDetail(i)" title="Delete">
                          <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                        </a>
                      </td>
                      <td>
                        <select class="form-select form-select-sm rounded-2" formControlName="loanType"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && loanDetail.get('loanType')?.errors}">
                          <option value="" selected>Select</option>
                          <option *ngFor="let type of loanTypes" [value]="type">{{ type }}</option>
                        </select>
                      </td>
                      <td>
                        <input type="text" class="form-control form-control-sm rounded-2" formControlName="bankName"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && loanDetail.get('bankName')?.errors}">
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm rounded-2" formControlName="sanctionedAmount"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && loanDetail.get('sanctionedAmount')?.errors}">
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm rounded-2" formControlName="outstandingAmount"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && loanDetail.get('outstandingAmount')?.errors}">
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm rounded-2" formControlName="tenure"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && loanDetail.get('tenure')?.errors}">
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm rounded-2" formControlName="emiAmount"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && loanDetail.get('emiAmount')?.errors}">
                      </td>
                      <td>
                        <select class="form-select form-select-sm rounded-2" formControlName="default">
                          <option value="Yes">Yes</option>
                          <option value="No">No</option>
                        </select>
                      </td>
                      <td *ngIf="hasAnyCoAppLoanWithDefault()">
                        <input
                          *ngIf="loanDetail.get('default')?.value === 'Yes'"
                          type="text"
                          class="form-control form-control-sm rounded-2"
                          formControlName="example"
                          placeholder="Enter example">
                        <span *ngIf="loanDetail.get('default')?.value !== 'Yes'" class="text-muted">-</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Investment Details Section -->
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
          <div>
            <h6 class="text-primary fw-semibold mb-0">Investment Details</h6>
            <small class="text-muted">FD/RD/MIS/LIC/MF</small>
          </div>
          <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addCoAppInvestment()">
            <i class="bi bi-plus-circle me-1"></i> Add Investment
          </button>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <div class="card shadow-sm rounded-3 border-0 mb-4">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 5%;" class="fw-medium text-center">Action</th>
                      <th style="width: 15%;" class="fw-medium">Inv. Product</th>
                      <th style="width: 15%;" class="fw-medium">Institute Name</th>
                      <th style="width: 11%;" class="fw-medium">Yearly Amt.</th>
                      <th style="width: 13%;" class="fw-medium">Inv. Mode</th>
                      <th style="width: 11%;" class="fw-medium">Start Dt.</th>
                      <th style="width: 11%;" class="fw-medium">End Dt.</th>
                      <th style="width: 19%;" class="fw-medium">Current Saving Amt.</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngIf="coAppInvestmentsArray.length === 0">
                      <td colspan="5" class="text-center py-4 text-muted">
                        <div class="d-flex justify-content-center align-items-center">
                          <i class="bi bi-info-circle me-2"></i>
                          <span>No data found</span>
                        </div>
                      </td>
                    </tr>
                    <tr *ngFor="let investment of coAppInvestmentsArray.controls; let i = index" [formGroup]="$any(investment)">
                      <td class="text-center">
                        <a href="javascript:void(0);" (click)="removeCoAppInvestment(i)" title="Delete">
                          <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                        </a>
                      </td>
                      <td>
                        <select class="form-select form-select-sm rounded-2" formControlName="investmentProduct"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('investmentProduct')?.errors}">
                          <option value="" selected>Select</option>
                          <option *ngFor="let product of investmentProducts" [value]="product">{{ product }}</option>
                        </select>
                      </td>
                      <td>
                        <input type="text" class="form-control form-control-sm rounded-2" formControlName="instituteName"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('instituteName')?.errors}">
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm rounded-2" formControlName="yearlyAmount"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('yearlyAmount')?.errors}">
                      </td>
                      <td>
                        <select class="form-select form-select-sm rounded-2" formControlName="investmentMode"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('investmentMode')?.errors}">
                          <option value="" selected>Select</option>
                          <option *ngFor="let mode of investmentModes" [value]="mode">{{ mode }}</option>
                        </select>
                      </td>
                      <td>
                        <input type="date" class="form-control form-control-sm rounded-2" formControlName="startDate"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('startDate')?.errors}">
                      </td>
                      <td>
                        <input type="date" class="form-control form-control-sm rounded-2" formControlName="endDate"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('endDate')?.errors}">
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm rounded-2" formControlName="currentSavingAmount"
                          [ngClass]="{'is-invalid': isCoAppFormSubmitted && investment.get('currentSavingAmount')?.errors}">
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('Cancel')">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="saveCoApplicantSalariedForm(modal)">Save</button>
  </div>
</ng-template>

<!-- Self Employed Modal Template -->
<ng-template #selfEmployedModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Co-Applicant Self-Employed Details</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss('Cross click')" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <!-- Debug information - hidden in production -->
    <div class="alert alert-info mb-3">
      <small>Current Product Type: <strong>{{ currentProductType }}</strong></small>
      <br>
      <small>Is LAP/LRD Form: <strong>{{ isLapLrdForm }}</strong></small>
      <br>
      <small>Is HL Form: <strong>{{ isHLForm }}</strong></small>
    </div>

    <!-- Include the Self-Employed component with the current product type -->
    <app-self-employee #selfEmployeeComp [isCoApplicantForm]="true" (coApplicantSaved)="onCoApplicantSelfEmployedSaved($event)"></app-self-employee>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('Cancel')">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="selfEmployeeComp.saveCoApplicantSelfEmployedForm(); modal.close('Save')">Save</button>
  </div>
</ng-template>