import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  CorporateConsultancyService,
  CorporateConsultancy,
  CorporateConsultancyStatistics
} from '../../../../core/services/corporate-consultancy.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { CorporateConsultancyFormComponent } from './corporate-consultancy-form/corporate-consultancy-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-corporate-consultancies',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    CorporateConsultancyFormComponent,
    BulkUploadComponent
  ],
  templateUrl: './corporate-consultancies.component.html',
  styleUrls: ['./corporate-consultancies.component.scss']
})
export class CorporateConsultanciesComponent implements OnInit {
  // Data properties
  consultancies: CorporateConsultancy[] = [];
  deletedConsultancies: CorporateConsultancy[] = [];
  statistics: CorporateConsultancyStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedType = '';
  selectedPartnershipLevel = '';
  selectedCountry = '';
  selectedRegulatoryBody = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedConsultancies: Set<string> = new Set();
  selectAll = false;

  // Filter options
  consultancyTypes: any[] = [];
  partnershipLevels: any[] = [];
  countries: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private consultancyService: CorporateConsultancyService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadConsultancies();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.consultancyTypes = this.consultancyService.getConsultancyTypes();
    this.partnershipLevels = this.consultancyService.getPartnershipLevels();
    this.countries = this.consultancyService.getCountryList();
    this.regulatoryBodies = this.consultancyService.getRegulatoryBodies();
  }

  /**
   * Load consultancies with current filters
   */
  loadConsultancies(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      type: this.selectedType || undefined,
      partnership_level: this.selectedPartnershipLevel || undefined,
      country: this.selectedCountry || undefined,
      regulatory_body: this.selectedRegulatoryBody || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.consultancyService.getConsultancies(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedConsultancies = response.data.filter(c => c.deleted_at);
            this.consultancies = [];
          } else {
            this.consultancies = response.data.filter(c => !c.deleted_at);
            this.deletedConsultancies = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load consultancies';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load consultancies. Please try again.'
        });
      }
    });
  }

  /**
   * Load consultancy statistics
   */
  loadStatistics(): void {
    this.consultancyService.getConsultancyStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search consultancies
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadConsultancies();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadConsultancies();
  }

  /**
   * Filter by type
   */
  onTypeFilter(): void {
    this.currentPage = 1;
    this.loadConsultancies();
  }

  /**
   * Filter by partnership level
   */
  onPartnershipLevelFilter(): void {
    this.currentPage = 1;
    this.loadConsultancies();
  }

  /**
   * Filter by country
   */
  onCountryFilter(): void {
    this.currentPage = 1;
    this.loadConsultancies();
  }

  /**
   * Filter by regulatory body
   */
  onRegulatoryBodyFilter(): void {
    this.currentPage = 1;
    this.loadConsultancies();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedConsultancies.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadConsultancies();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadConsultancies();
  }

  /**
   * Open create consultancy modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(CorporateConsultancyFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadConsultancies();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Corporate consultancy created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit consultancy modal
   */
  openEditModal(consultancy: CorporateConsultancy): void {
    const modalRef = this.modalService.open(CorporateConsultancyFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.consultancy = { ...consultancy };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadConsultancies();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Corporate consultancy updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete consultancy
   */
  deleteConsultancy(consultancy: CorporateConsultancy): void {
    this.popupService.showConfirmation({
      title: 'Delete Corporate Consultancy',
      message: `Are you sure you want to delete "${consultancy.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.consultancyService.deleteConsultancy(consultancy.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadConsultancies();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Corporate consultancy deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete consultancy.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore consultancy
   */
  restoreConsultancy(consultancy: CorporateConsultancy): void {
    this.popupService.showConfirmation({
      title: 'Restore Corporate Consultancy',
      message: `Are you sure you want to restore "${consultancy.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.consultancyService.restoreConsultancy(consultancy.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadConsultancies();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Corporate consultancy restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore consultancy.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadConsultancies();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Corporate consultancies uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.consultancyService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'corporate_consultancies_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Toggle consultancy selection
   */
  toggleSelection(consultancyId: string): void {
    if (this.selectedConsultancies.has(consultancyId)) {
      this.selectedConsultancies.delete(consultancyId);
    } else {
      this.selectedConsultancies.add(consultancyId);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    const currentList = this.getCurrentList();

    if (this.selectAll) {
      this.selectedConsultancies.clear();
    } else {
      currentList.forEach(consultancy => this.selectedConsultancies.add(consultancy.id));
    }
    this.selectAll = !this.selectAll;
  }

  /**
   * Update select all state
   */
  private updateSelectAllState(): void {
    const currentList = this.getCurrentList();
    this.selectAll = currentList.length > 0 &&
      currentList.every(consultancy => this.selectedConsultancies.has(consultancy.id));
  }

  /**
   * Bulk delete selected consultancies
   */
  bulkDelete(): void {
    if (this.selectedConsultancies.size === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select consultancies to delete.'
      });
      return;
    }

    this.popupService.showConfirmation({
      title: 'Bulk Delete',
      message: `Are you sure you want to delete ${this.selectedConsultancies.size} selected consultancies?`,
      confirmText: 'Yes, Delete All',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.popupService.showInfo({
          title: 'Feature Coming Soon',
          message: 'Bulk delete functionality will be implemented in the next update.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadConsultancies();
    this.loadStatistics();
  }

  /**
   * Get consultancy type label
   */
  getConsultancyTypeLabel(type: string): string {
    return this.consultancyService.getConsultancyTypeLabel(type);
  }

  /**
   * Get partnership level label
   */
  getPartnershipLevelLabel(level: string): string {
    return this.consultancyService.getPartnershipLevelLabel(level);
  }

  /**
   * Format revenue
   */
  formatRevenue(amount?: number): string {
    return this.consultancyService.formatRevenue(amount);
  }

  /**
   * Get rating stars
   */
  getRatingStars(rating?: number): string {
    return this.consultancyService.getRatingStars(rating);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get partnership level badge class
   */
  getPartnershipLevelBadgeClass(level: string): string {
    switch (level) {
      case 'preferred': return 'badge bg-success';
      case 'standard': return 'badge bg-primary';
      case 'trial': return 'badge bg-warning';
      default: return 'badge bg-secondary';
    }
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): CorporateConsultancy[] {
    return this.viewMode === 'deleted' ? this.deletedConsultancies : this.consultancies;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByConsultancyId(index: number, consultancy: CorporateConsultancy): string {
    return consultancy.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
