import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Designation interfaces
export interface Designation {
  id: string;
  name: string;
  description?: string;
  level: number;
  department_id?: string;
  department_name?: string;
  is_active: boolean;
  employee_count?: number;
  salary_range_min?: number;
  salary_range_max?: number;
  created_at: string;
  updated_at: string;
}

export interface DesignationCreate {
  name: string;
  description?: string;
  level: number;
  department_id?: string;
  is_active?: boolean;
  salary_range_min?: number;
  salary_range_max?: number;
}

export interface DesignationUpdate {
  name?: string;
  description?: string;
  level?: number;
  department_id?: string;
  is_active?: boolean;
  salary_range_min?: number;
  salary_range_max?: number;
}

export interface DesignationLevel {
  level: number;
  name: string;
  description: string;
  designation_count: number;
  avg_salary?: number;
}

export interface DesignationStatistics {
  total_designations: number;
  active_designations: number;
  inactive_designations: number;
  levels_count: number;
  designations_by_level: { [level: number]: number };
  popular_designations: Designation[];
  avg_salary_by_level: { [level: number]: number };
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DesignationService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/designations/`;
  private designationsSubject = new BehaviorSubject<Designation[]>([]);
  public designations$ = this.designationsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all designations with optional filtering and pagination
   */
  getDesignations(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    level?: number;
    department_id?: string;
  }): Observable<APIResponse<Designation[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Designation[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.designationsSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get designation by ID
   */
  getDesignationById(id: string): Observable<APIResponse<Designation>> {
    return this.http.get<APIResponse<Designation>>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new designation
   */
  createDesignation(designation: DesignationCreate): Observable<APIResponse<Designation>> {
    return this.http.post<APIResponse<Designation>>(this.baseUrl, designation)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDesignations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update designation
   */
  updateDesignation(id: string, designation: DesignationUpdate): Observable<APIResponse<Designation>> {
    return this.http.put<APIResponse<Designation>>(`${this.baseUrl}/${id}`, designation)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDesignations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Delete designation
   */
  deleteDesignation(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDesignations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get designation levels
   */
  getDesignationLevels(): Observable<APIResponse<DesignationLevel[]>> {
    return this.http.get<APIResponse<DesignationLevel[]>>(`${this.baseUrl}/levels`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get popular designations
   */
  getPopularDesignations(): Observable<APIResponse<Designation[]>> {
    return this.http.get<APIResponse<Designation[]>>(`${this.baseUrl}/popular`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get designation statistics
   */
  getDesignationStatistics(): Observable<APIResponse<DesignationStatistics>> {
    return this.http.get<APIResponse<DesignationStatistics>>(`${this.baseUrl}/statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload designations
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}/bulk`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDesignations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Search designations
   */
  searchDesignations(query: string, filters?: {
    is_active?: boolean;
    level?: number;
    department_id?: string;
  }): Observable<APIResponse<Designation[]>> {
    let params = new HttpParams().set('search', query);

    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key as keyof typeof filters];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Designation[]>>(this.baseUrl, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get designations for dropdown (simplified data)
   */
  getDesignationsDropdown(): Observable<{ id: string; name: string; level: number }[]> {
    return this.getDesignations({ per_page: 1000 }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map(designation => ({
            id: designation.id,
            name: designation.name,
            level: designation.level
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get active designations only
   */
  getActiveDesignations(): Observable<Designation[]> {
    return this.getDesignations({ is_active: true }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get designations by level
   */
  getDesignationsByLevel(level: number): Observable<Designation[]> {
    return this.getDesignations({ level }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get designations by department
   */
  getDesignationsByDepartment(departmentId: string): Observable<Designation[]> {
    return this.getDesignations({ department_id: departmentId }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh designations data
   */
  refreshDesignations(): void {
    this.getDesignations().subscribe();
  }

  /**
   * Clear designations cache
   */
  clearCache(): void {
    this.designationsSubject.next([]);
  }

  /**
   * Get level name from level number
   */
  getLevelName(level: number): string {
    const levelNames: { [key: number]: string } = {
      1: 'Entry Level',
      2: 'Junior Level',
      3: 'Mid Level',
      4: 'Senior Level',
      5: 'Lead Level',
      6: 'Manager Level',
      7: 'Senior Manager',
      8: 'Director Level',
      9: 'VP Level',
      10: 'Executive Level'
    };
    return levelNames[level] || `Level ${level}`;
  }

  /**
   * Format salary range
   */
  formatSalaryRange(min?: number, max?: number): string {
    if (!min && !max) return 'Not specified';
    if (min && max) return `₹${min.toLocaleString()} - ₹${max.toLocaleString()}`;
    if (min) return `₹${min.toLocaleString()}+`;
    if (max) return `Up to ₹${max.toLocaleString()}`;
    return 'Not specified';
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Designation service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
