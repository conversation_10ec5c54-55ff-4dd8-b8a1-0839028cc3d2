import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Constitution interfaces
export interface Constitution {
  id: string;
  name: string;
  code: string;
  type: 'company' | 'partnership' | 'llp' | 'trust' | 'society' | 'cooperative' | 'other';
  description?: string;
  legal_framework?: string;
  jurisdiction?: string;
  registration_number?: string;
  registration_date?: string;
  regulatory_body?: string;
  governing_law?: string;
  is_active: boolean;
  document_url?: string;
  last_amended_date?: string;
  amendment_count?: number;
  compliance_status?: 'compliant' | 'non_compliant' | 'under_review' | 'pending';
  next_review_date?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface ConstitutionCreate {
  name: string;
  code: string;
  type: 'company' | 'partnership' | 'llp' | 'trust' | 'society' | 'cooperative' | 'other';
  description?: string;
  legal_framework?: string;
  jurisdiction?: string;
  registration_number?: string;
  registration_date?: string;
  regulatory_body?: string;
  governing_law?: string;
  is_active?: boolean;
  document_url?: string;
  last_amended_date?: string;
  amendment_count?: number;
  compliance_status?: 'compliant' | 'non_compliant' | 'under_review' | 'pending';
  next_review_date?: string;
}

export interface ConstitutionUpdate {
  name?: string;
  code?: string;
  type?: 'company' | 'partnership' | 'llp' | 'trust' | 'society' | 'cooperative' | 'other';
  description?: string;
  legal_framework?: string;
  jurisdiction?: string;
  registration_number?: string;
  registration_date?: string;
  regulatory_body?: string;
  governing_law?: string;
  is_active?: boolean;
  document_url?: string;
  last_amended_date?: string;
  amendment_count?: number;
  compliance_status?: 'compliant' | 'non_compliant' | 'under_review' | 'pending';
  next_review_date?: string;
}

export interface ConstitutionStatistics {
  total_constitutions: number;
  active_constitutions: number;
  inactive_constitutions: number;
  constitutions_by_type: { [type: string]: number };
  constitutions_by_compliance: { [status: string]: number };
  constitutions_by_jurisdiction: { [jurisdiction: string]: number };
  popular_constitutions: Constitution[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ConstitutionService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/constitutions/`;
  private constitutionsSubject = new BehaviorSubject<Constitution[]>([]);
  public constitutions$ = this.constitutionsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all constitutions with optional filtering and pagination (returns APIResponse)
   */
  getConstitutionsWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    type?: string;
    compliance_status?: string;
    jurisdiction?: string;
    regulatory_body?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<Constitution[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Constitution[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.constitutionsSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all constitutions (backward compatibility) - returns Constitution[] directly
   */
  getConstitutions(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    type?: string;
    compliance_status?: string;
    jurisdiction?: string;
    regulatory_body?: string;
    include_deleted?: boolean;
  }): Observable<Constitution[]> {
    return this.getConstitutionsWithResponse(params).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get constitution by ID
   */
  getConstitutionById(id: string): Observable<APIResponse<Constitution>> {
    return this.http.get<APIResponse<Constitution>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new constitution
   */
  createConstitution(constitution: ConstitutionCreate): Observable<APIResponse<Constitution>> {
    return this.http.post<APIResponse<Constitution>>(this.baseUrl, constitution)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConstitutions();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update constitution
   */
  updateConstitution(id: string, constitution: ConstitutionUpdate): Observable<APIResponse<Constitution>> {
    return this.http.put<APIResponse<Constitution>>(`${this.baseUrl}${id}`, constitution)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConstitutions();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete constitution
   */
  deleteConstitution(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConstitutions();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted constitution
   */
  restoreConstitution(id: string): Observable<APIResponse<Constitution>> {
    return this.http.post<APIResponse<Constitution>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConstitutions();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get constitution statistics
   */
  getConstitutionStatistics(): Observable<APIResponse<ConstitutionStatistics>> {
    return this.http.get<APIResponse<ConstitutionStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload constitutions
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConstitutions();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Get constitutions for dropdown (simplified data)
   */
  getConstitutionsDropdown(): Observable<{ id: string; name: string; code: string; type: string }[]> {
    return this.getConstitutions({ per_page: 1000, is_active: true }).pipe(
      map(constitutions => {
        return constitutions.map(constitution => ({
          id: constitution.id,
          name: constitution.name,
          code: constitution.code,
          type: constitution.type
        }));
      })
    );
  }

  /**
   * Get constitutions (backward compatibility) - returns Constitution[] directly
   */
  getConstitutionsLegacy(
    filter: any = {},
    skip: number = 0,
    limit: number = 100,
    includeDeleted: boolean = false
  ): Observable<Constitution[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search,
      is_active: filter.is_active,
      include_deleted: includeDeleted
    };

    return this.getConstitutionsWithResponse(params).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh constitutions data
   */
  refreshConstitutions(): void {
    this.getConstitutionsWithResponse().subscribe();
  }

  /**
   * Clear constitutions cache
   */
  clearCache(): void {
    this.constitutionsSubject.next([]);
  }

  /**
   * Get constitution types
   */
  getConstitutionTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'company', label: 'Company', description: 'Private or public limited company' },
      { value: 'partnership', label: 'Partnership', description: 'General or limited partnership' },
      { value: 'llp', label: 'LLP', description: 'Limited Liability Partnership' },
      { value: 'trust', label: 'Trust', description: 'Private or public trust' },
      { value: 'society', label: 'Society', description: 'Registered society or association' },
      { value: 'cooperative', label: 'Cooperative', description: 'Cooperative society' },
      { value: 'other', label: 'Other', description: 'Other legal entity types' }
    ];
  }

  /**
   * Get compliance statuses
   */
  getComplianceStatuses(): { value: string; label: string; description: string }[] {
    return [
      { value: 'compliant', label: 'Compliant', description: 'Fully compliant with regulations' },
      { value: 'non_compliant', label: 'Non-Compliant', description: 'Not meeting regulatory requirements' },
      { value: 'under_review', label: 'Under Review', description: 'Currently being reviewed for compliance' },
      { value: 'pending', label: 'Pending', description: 'Compliance status pending determination' }
    ];
  }

  /**
   * Get jurisdiction list
   */
  getJurisdictionList(): string[] {
    return [
      'India',
      'United States',
      'United Kingdom',
      'Canada',
      'Australia',
      'Singapore',
      'Hong Kong',
      'Japan',
      'Germany',
      'France',
      'Switzerland',
      'Netherlands',
      'Luxembourg',
      'UAE',
      'Malaysia'
    ];
  }

  /**
   * Get regulatory bodies
   */
  getRegulatoryBodies(): string[] {
    return [
      'Ministry of Corporate Affairs (India)',
      'SEBI (India)',
      'RBI (India)',
      'SEC (USA)',
      'Companies House (UK)',
      'ASIC (Australia)',
      'ACRA (Singapore)',
      'FSA (Japan)',
      'BaFin (Germany)',
      'AMF (France)',
      'FINMA (Switzerland)',
      'AFM (Netherlands)',
      'CSSF (Luxembourg)',
      'SCA (UAE)',
      'SSM (Malaysia)'
    ];
  }

  /**
   * Get constitution type label
   */
  getConstitutionTypeLabel(type: string): string {
    const types = this.getConstitutionTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Get compliance status label
   */
  getComplianceStatusLabel(status: string): string {
    const statuses = this.getComplianceStatuses();
    const statusObj = statuses.find(s => s.value === status);
    return statusObj ? statusObj.label : status;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Constitution service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
