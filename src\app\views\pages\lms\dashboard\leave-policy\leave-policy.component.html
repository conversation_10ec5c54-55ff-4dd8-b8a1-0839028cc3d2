<!-- Breadcrumb Navigation -->


<div class="container-fluid">
  <div class="row">
    <div class="col-md-12 grid-margin stretch-card">
      <div class="card">
        <div class="card-body">
          <!-- Header with title -->
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h6 class="card-title mb-0">Leave Policy</h6>
          </div>
          <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
          <!-- First Table - Leave Types -->
          <div class="card modern-table-card mb-4">
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th>Leave Code</th>
                      <th>Leave Description</th>
                      <th>Leave Details</th>
                      <th>No Of Leaves</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><span class="badge bg-primary text-white">PL</span></td>
                      <td><strong>Privilege Leave</strong></td>
                      <td>PL can apply by employee when leaves required, max 10 days leaves allowed at a time.</td>
                      <td><span class="badge bg-success text-white">18</span></td>
                    </tr>
                    <tr>
                      <td><span class="badge bg-warning text-dark">LWP</span></td>
                      <td><strong>Leave Without Pay</strong></td>
                      <td>LWP can be applied by an employee when no other leave is available. During the period of LWP, the employee is not entitled for any pay or allowance.</td>
                    </tr>
                    <tr>
                      <td><span class="badge bg-info text-white">OD</span></td>
                      <td><strong>Outdoor</strong></td>
                      <td>If employee going out for duty (Client side) that time employee has to apply OD.</td>
                    </tr>
                    <tr>
                      <td><span class="badge bg-success text-white">WFH</span></td>
                      <td><strong>Work From Home</strong></td>
                      <td>If employee wants to work from home (depend on situation), employee will apply the WFH. If Employer allows then only this is applicable.</td>
                    </tr>
                    <tr>
                      <td><span class="badge bg-secondary text-white">CompOff</span></td>
                      <td><strong>Compensatory Off</strong></td>
                      <td>If employee worked on holidays or weekend then employee can send request for Comp Off.</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Second Table - Leave Policies -->
          <div class="card modern-table-card">
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th>Leave Policies</th>
                      <th>PL</th>
                      <th>LWP</th>
                      <th>OD</th>
                      <th>WFH</th>
                      <th>Comp Off</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><strong>Balance Required</strong></td>
                      <td>YES</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>YES</td>
                    </tr>
                    <tr>
                      <td><strong>Cancellation allowed after approval</strong></td>
                      <td>YES</td>
                      <td>YES</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>YES</td>
                    </tr>
                    <tr>
                      <td><strong>Count Holidays</strong></td>
                      <td>YES</td>
                      <td>YES</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>YES</td>
                    </tr>
                    <tr>
                      <td><strong>Count Weekend</strong></td>
                      <td>YES</td>
                      <td>YES</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>YES</td>
                    </tr>
                    <tr>
                      <td><strong>Max days</strong></td>
                      <td>YES</td>
                      <td>YES</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>YES</td>
                    </tr>
                    <tr>
                      <td><strong>Min days</strong></td>
                      <td>YES</td>
                      <td>YES</td>
                      <td>NO</td>
                      <td>NO</td>
                      <td>YES</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
