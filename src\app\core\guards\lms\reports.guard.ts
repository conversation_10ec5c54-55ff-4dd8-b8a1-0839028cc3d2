// hi

import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class ReportsGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    console.log('🛡️ REPORTS GUARD - Checking access for Reports');

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ REPORTS GUARD - No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check multiple possible permissions for reports access
    const possiblePermissions = [
      'leave_report:read',     // Primary permission
      'view_reports',          // Admin permission
      'manage_reports',        // Admin permission
      'report:read',           // Alternative format
      'reports:read',          // Alternative format
      '*'                      // Wildcard permission
    ];

    console.log('🔑 Checking permissions for Reports:', possiblePermissions);
    console.log('👤 User permissions:', currentUser.permissions || []);

    // Check if user has any of the required permissions
    const hasPermission = possiblePermissions.some(permission =>
      this.authService.hasPermission(permission)
    );

    // Additional check for admin/superuser roles
    const isAdmin = currentUser.role === 'admin' || currentUser.is_superuser === true;
    const hasAdminAccess = isAdmin && (
      currentUser.permissions?.includes('*') ||
      currentUser.permissions?.includes('view_reports') ||
      currentUser.permissions?.includes('manage_reports')
    );

    const finalAccess = hasPermission || hasAdminAccess;

    if (!finalAccess) {
      console.log('❌ REPORTS GUARD - Permission DENIED');
      console.log('🔑 Required any of:', possiblePermissions);
      console.log('👤 User permissions:', currentUser.permissions || []);
      console.log('📧 User email:', currentUser.email);
      console.log('🎭 User role:', currentUser.role);
      console.log('🔑 Is admin:', isAdmin);
      console.log('🚫 Redirecting to dashboard');
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log('✅ REPORTS GUARD - Permission GRANTED');
    console.log('🎯 User can access Reports functionality');
    console.log('👤 User permissions:', currentUser.permissions || []);
    console.log('🔑 Access granted via:', hasPermission ? 'permission check' : 'admin access');
    return true;
  }
}
