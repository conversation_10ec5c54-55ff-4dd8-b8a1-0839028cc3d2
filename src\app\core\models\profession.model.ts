export interface Profession {
  id?: number;
  name: string;
  type: 'Profession' | 'Non Profession';
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
  deleted_at?: string | null;
}

export interface ProfessionResponse {
  items: Profession[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ProfessionCreate {
  name: string;
  type: 'Profession' | 'Non Profession';
  status?: 'active' | 'inactive';
}

export interface ProfessionUpdate {
  name?: string;
  type?: 'Profession' | 'Non Profession';
  status?: 'active' | 'inactive';
}
