import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError, tap, timeout, retry } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * User Role interface based on API response
 */
export interface UserRole {
  id: string;
  name: string;
  description?: string;
  permissions?: string[];
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
}

/**
 * API Response wrapper interface for user roles
 */
export interface UserRolesApiResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: {
    message?: string;
    total?: number;
    page?: number;
    size?: number;
  };
}

/**
 * User Roles Service
 * Handles fetching user roles and permissions from the API
 */
@Injectable({
  providedIn: 'root'
})
export class UserRolesService {
  private baseUrl = environment.apiUrl;
  private readonly REQUEST_TIMEOUT = 10000; // 10 seconds
  private readonly MAX_RETRIES = 2;

  constructor(
    private http: HttpClient
  ) {}

  /**
   * Get current user's roles from User Roles API
   * GET /api/v1/user-roles/me/roles
   * @returns Observable of user roles array
   */
  getCurrentUserRoles(): Observable<UserRole[]> {
    console.log('UserRolesService: Fetching current user roles from User Roles API');

    const userRolesUrl = `${this.baseUrl}/api/v1/user-roles/me/roles`;

    return this.http.get<UserRolesApiResponse<UserRole[]>>(userRolesUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      tap(response => {
        console.log('UserRolesService: Raw API response:', response);
      }),
      map(response => {
        console.log('UserRolesService: Processing user roles response:', response);

        if (response && response.success && response.data && Array.isArray(response.data)) {
          console.log('UserRolesService: Successfully extracted roles:', response.data);
          return response.data;
        } else if (Array.isArray(response)) {
          // Handle direct array response (fallback)
          console.log('UserRolesService: Direct array response detected:', response);
          return response;
        } else {
          console.warn('UserRolesService: Unexpected user roles response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.error('UserRolesService: Error fetching user roles:', error);
        // Return empty array instead of throwing error to maintain graceful degradation
        return of([]);
      })
    );
  }

  /**
   * Get user roles for a specific user (admin only)
   * GET /api/v1/user-roles/users/{user_id}/roles
   * @param userId User ID to fetch roles for
   * @returns Observable of user roles array
   */
  getUserRoles(userId: string): Observable<UserRole[]> {
    if (!userId) {
      console.error('UserRolesService: No user ID provided');
      return of([]);
    }

    console.log('UserRolesService: Fetching roles for user:', userId);

    const userRolesUrl = `${this.baseUrl}/api/v1/user-roles/users/${userId}/roles`;

    return this.http.get<UserRolesApiResponse<UserRole[]>>(userRolesUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      tap(response => {
        console.log('UserRolesService: Raw API response for user roles:', response);
      }),
      map(response => {
        console.log('UserRolesService: Processing user roles response for user:', userId, response);

        if (response && response.success && response.data && Array.isArray(response.data)) {
          console.log('UserRolesService: Successfully extracted user roles:', response.data);
          return response.data;
        } else if (Array.isArray(response)) {
          // Handle direct array response (fallback)
          console.log('UserRolesService: Direct array response detected for user roles:', response);
          return response;
        } else {
          console.warn('UserRolesService: Unexpected user roles response format for user:', userId, response);
          return [];
        }
      }),
      catchError(error => {
        console.error('UserRolesService: Error fetching user roles for user:', userId, error);
        // Return empty array instead of throwing error to maintain graceful degradation
        return of([]);
      })
    );
  }

  /**
   * Extract permissions from roles array
   * @param roles Array of user roles
   * @returns Array of permission strings
   */
  extractPermissionsFromRoles(roles: UserRole[]): string[] {
    if (!roles || !Array.isArray(roles)) {
      return [];
    }

    const permissions: string[] = [];

    roles.forEach(role => {
      if (role.permissions && Array.isArray(role.permissions)) {
        permissions.push(...role.permissions);
      }
    });

    // Remove duplicates and return
    return [...new Set(permissions)];
  }

  /**
   * Check if user has a specific role
   * @param roles Array of user roles
   * @param roleName Role name to check
   * @returns Boolean indicating if user has the role
   */
  hasRole(roles: UserRole[], roleName: string): boolean {
    if (!roles || !Array.isArray(roles) || !roleName) {
      return false;
    }

    return roles.some(role =>
      role.name && role.name.toLowerCase() === roleName.toLowerCase()
    );
  }

  /**
   * Get primary role (first active role)
   * @param roles Array of user roles
   * @returns Primary role or null
   */
  getPrimaryRole(roles: UserRole[]): UserRole | null {
    if (!roles || !Array.isArray(roles) || roles.length === 0) {
      return null;
    }

    // Find first active role
    const activeRole = roles.find(role => role.is_active !== false);
    return activeRole || roles[0]; // Fallback to first role if no active role found
  }

  /**
   * Find role by name
   * @param roles Array of user roles
   * @param roleName Role name to find
   * @returns Found role or null
   */
  findRoleByName(roles: UserRole[], roleName: string): UserRole | null {
    if (!roles || !Array.isArray(roles) || !roleName) {
      return null;
    }

    return roles.find(role =>
      role.name && role.name.toLowerCase() === roleName.toLowerCase()
    ) || null;
  }

  /**
   * Get role names as array
   * @param roles Array of user roles
   * @returns Array of role names
   */
  getRoleNames(roles: UserRole[]): string[] {
    if (!roles || !Array.isArray(roles)) {
      return [];
    }

    return roles
      .filter(role => role.name)
      .map(role => role.name);
  }

  /**
   * Check if user has any of the specified roles
   * @param roles Array of user roles
   * @param roleNames Array of role names to check
   * @returns Boolean indicating if user has any of the roles
   */
  hasAnyRole(roles: UserRole[], roleNames: string[]): boolean {
    if (!roles || !Array.isArray(roles) || !roleNames || !Array.isArray(roleNames)) {
      return false;
    }

    const userRoleNames = this.getRoleNames(roles).map(name => name.toLowerCase());
    return roleNames.some(roleName =>
      userRoleNames.includes(roleName.toLowerCase())
    );
  }

  /**
   * Check if user has all of the specified roles
   * @param roles Array of user roles
   * @param roleNames Array of role names to check
   * @returns Boolean indicating if user has all of the roles
   */
  hasAllRoles(roles: UserRole[], roleNames: string[]): boolean {
    if (!roles || !Array.isArray(roles) || !roleNames || !Array.isArray(roleNames)) {
      return false;
    }

    const userRoleNames = this.getRoleNames(roles).map(name => name.toLowerCase());
    return roleNames.every(roleName =>
      userRoleNames.includes(roleName.toLowerCase())
    );
  }

  /**
   * Debug method to compare both API endpoints
   * This helps identify discrepancies between /me/roles and /users/{id}/roles
   */
  debugCompareEndpoints(userId: string): void {
    console.log('🔍 DEBUGGING: Comparing both user roles endpoints...');

    // Call both endpoints simultaneously
    const currentUserRoles$ = this.getCurrentUserRoles();
    const specificUserRoles$ = this.getUserRoles(userId);

    // Compare responses
    currentUserRoles$.subscribe({
      next: (currentRoles) => {
        console.log('📋 /me/roles response:', currentRoles);

        specificUserRoles$.subscribe({
          next: (specificRoles) => {
            console.log('📋 /users/{id}/roles response:', specificRoles);

            // Compare the responses
            this.compareRoleResponses(currentRoles, specificRoles);
          },
          error: (error) => {
            console.error('❌ Error from /users/{id}/roles:', error);
          }
        });
      },
      error: (error) => {
        console.error('❌ Error from /me/roles:', error);
      }
    });
  }

  /**
   * Compare two role responses and highlight differences
   */
  private compareRoleResponses(currentRoles: UserRole[], specificRoles: UserRole[]): void {
    console.log('🔍 COMPARISON ANALYSIS:');
    console.log('Current user roles count:', currentRoles.length);
    console.log('Specific user roles count:', specificRoles.length);

    // Extract permissions from both
    const currentPermissions = this.extractPermissionsFromRoles(currentRoles);
    const specificPermissions = this.extractPermissionsFromRoles(specificRoles);

    console.log('📋 Current user permissions:', currentPermissions);
    console.log('📋 Specific user permissions:', specificPermissions);

    // Check for ops:access specifically
    const currentHasOpsAccess = currentPermissions.includes('ops:access');
    const specificHasOpsAccess = specificPermissions.includes('ops:access');

    console.log('🎯 Current user has ops:access:', currentHasOpsAccess);
    console.log('🎯 Specific user has ops:access:', specificHasOpsAccess);

    if (currentHasOpsAccess !== specificHasOpsAccess) {
      console.log('⚠️ DISCREPANCY FOUND: Different ops:access permission between endpoints!');
    }

    // Find missing permissions
    const missingInCurrent = specificPermissions.filter(p => !currentPermissions.includes(p));
    const missingInSpecific = currentPermissions.filter(p => !specificPermissions.includes(p));

    if (missingInCurrent.length > 0) {
      console.log('❌ Permissions missing in /me/roles:', missingInCurrent);
    }

    if (missingInSpecific.length > 0) {
      console.log('❌ Permissions missing in /users/{id}/roles:', missingInSpecific);
    }

    // Compare role structures
    console.log('🔍 ROLE STRUCTURE COMPARISON:');
    currentRoles.forEach((role) => {
      const matchingRole = specificRoles.find(r => r.id === role.id);
      if (matchingRole) {
        console.log(`Role ${role.name}:`);
        console.log('  Current permissions:', role.permissions);
        console.log('  Specific permissions:', matchingRole.permissions);

        if (JSON.stringify(role.permissions) !== JSON.stringify(matchingRole.permissions)) {
          console.log('  ⚠️ PERMISSION MISMATCH DETECTED!');
        }
      } else {
        console.log(`❌ Role ${role.name} not found in specific user response`);
      }
    });
  }
}
