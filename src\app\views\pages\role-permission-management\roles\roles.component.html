<!-- Enhanced Header Section -->
<div class="roles-header-section">
  <div class="container-fluid">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <div class="page-header">
          <h4 class="page-title mb-1">
            <i data-feather="shield" class="icon-md me-2" appFeatherIcon></i>
            Role Management
          </h4>
          <p class="page-subtitle text-muted mb-0">
            Manage user roles and their permissions across the system
          </p>
        </div>
      </div>
      <div class="col-md-4 text-end">
        <div class="header-actions">
          <button class="btn btn-outline-light me-2" routerLink="../analytics">
            <i data-feather="bar-chart-2" class="icon-sm me-1" appFeatherIcon></i>
            Analytics
          </button>
          <button class="btn btn-outline-secondary me-2" (click)="toggleViewMode()">
            <i [attr.data-feather]="viewMode === 'cards' ? 'list' : 'grid'" class="icon-sm me-1" appFeatherIcon></i>
            {{ viewMode === 'cards' ? 'Table View' : 'Card View' }}
          </button>
          <button class="btn btn-primary" (click)="openRoleModal(roleModal)">
            <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
            Add New Role
          </button>
        </div>
      </div>
    </div>

    <!-- Enhanced Search and Filter Section -->
    <div class="search-filter-section">
      <div class="row g-3 mb-4">
        <div class="col-md-4">
          <div class="search-box">
            <div class="input-group">
              <span class="input-group-text">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input
                type="text"
                class="form-control"
                [formControl]="searchTerm"
                placeholder="Search roles by name, description, or permissions..."
              >
              <button
                *ngIf="searchTerm.value"
                class="btn btn-outline-secondary"
                type="button"
                (click)="searchTerm.setValue(''); loadRoles()">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
          </div>
        </div>

        <div class="col-md-2">
          <select class="form-select" [(ngModel)]="statusFilter" (ngModelChange)="loadRoles()">
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="deleted">Deleted</option>
          </select>
        </div>

        <div class="col-md-2">
          <select class="form-select" [(ngModel)]="sortBy" (ngModelChange)="loadRoles()">
            <option value="name">Sort by Name</option>
            <option value="created_at">Sort by Date</option>
            <option value="users_count">Sort by Users</option>
            <option value="permissions_count">Sort by Permissions</option>
          </select>
        </div>

        <div class="col-md-2">
          <select class="form-select" [(ngModel)]="pageSize" (ngModelChange)="loadRoles()">
            <option [ngValue]="6">6 per page</option>
            <option [ngValue]="12">12 per page</option>
            <option [ngValue]="24">24 per page</option>
            <option [ngValue]="48">48 per page</option>
          </select>
        </div>

        <div class="col-md-2">
          <div class="d-flex gap-1">
            <button class="btn btn-outline-primary" (click)="toggleAdvancedFilters()" ngbTooltip="Advanced Filters">
              <i data-feather="filter" class="icon-sm" appFeatherIcon></i>
            </button>
            <button class="btn btn-outline-secondary" (click)="resetFilters()" ngbTooltip="Reset Filters">
              <i data-feather="refresh-cw" class="icon-sm" appFeatherIcon></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Advanced Filters Panel -->
      <div class="advanced-filters-panel" [class.show]="showAdvancedFilters">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i data-feather="sliders" class="icon-sm me-2" appFeatherIcon></i>
              Advanced Filters
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-3">

              <!-- User Count Filter -->
              <div class="col-md-3">
                <label class="form-label">User Count</label>
                <div class="d-flex gap-2">
                  <input
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Min"
                    [(ngModel)]="advancedFilters.userCountMin"
                    (ngModelChange)="applyAdvancedFilters()">
                  <input
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Max"
                    [(ngModel)]="advancedFilters.userCountMax"
                    (ngModelChange)="applyAdvancedFilters()">
                </div>
              </div>

              <!-- Permission Count Filter -->
              <div class="col-md-3">
                <label class="form-label">Permission Count</label>
                <div class="d-flex gap-2">
                  <input
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Min"
                    [(ngModel)]="advancedFilters.permissionCountMin"
                    (ngModelChange)="applyAdvancedFilters()">
                  <input
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Max"
                    [(ngModel)]="advancedFilters.permissionCountMax"
                    (ngModelChange)="applyAdvancedFilters()">
                </div>
              </div>

              <!-- Date Range Filter -->
              <div class="col-md-3">
                <label class="form-label">Created Date</label>
                <div class="d-flex gap-2">
                  <input
                    type="date"
                    class="form-control form-control-sm"
                    [(ngModel)]="advancedFilters.createdAfter"
                    (ngModelChange)="applyAdvancedFilters()">
                  <input
                    type="date"
                    class="form-control form-control-sm"
                    [(ngModel)]="advancedFilters.createdBefore"
                    (ngModelChange)="applyAdvancedFilters()">
                </div>
              </div>

              <!-- Role Type Filter -->
              <div class="col-md-3">
                <label class="form-label">Role Type</label>
                <select class="form-select form-select-sm" [(ngModel)]="advancedFilters.roleType" (ngModelChange)="applyAdvancedFilters()">
                  <option value="">All Types</option>
                  <option value="admin">Admin Roles</option>
                  <option value="user">User Roles</option>
                  <option value="system">System Roles</option>
                  <option value="custom">Custom Roles</option>
                </select>
              </div>

              <!-- Has Users Filter -->
              <div class="col-md-3">
                <label class="form-label">Assignment Status</label>
                <select class="form-select form-select-sm" [(ngModel)]="advancedFilters.hasUsers" (ngModelChange)="applyAdvancedFilters()">
                  <option value="">All Roles</option>
                  <option value="true">Has Users</option>
                  <option value="false">No Users</option>
                </select>
              </div>

              <!-- Has Permissions Filter -->
              <div class="col-md-3">
                <label class="form-label">Permission Status</label>
                <select class="form-select form-select-sm" [(ngModel)]="advancedFilters.hasPermissions" (ngModelChange)="applyAdvancedFilters()">
                  <option value="">All Roles</option>
                  <option value="true">Has Permissions</option>
                  <option value="false">No Permissions</option>
                </select>
              </div>

              <!-- Search in Description -->
              <div class="col-md-3">
                <label class="form-label">Description Contains</label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  placeholder="Search in description..."
                  [(ngModel)]="advancedFilters.descriptionContains"
                  (ngModelChange)="applyAdvancedFilters()">
              </div>

              <!-- Sort Order -->
              <div class="col-md-3">
                <label class="form-label">Sort Order</label>
                <select class="form-select form-select-sm" [(ngModel)]="advancedFilters.sortOrder" (ngModelChange)="applyAdvancedFilters()">
                  <option value="asc">Ascending</option>
                  <option value="desc">Descending</option>
                </select>
              </div>

            </div>

            <!-- Filter Actions -->
            <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
              <div class="filter-summary">
                <small class="text-muted">
                  <span *ngIf="getActiveFiltersCount() > 0">
                    {{ getActiveFiltersCount() }} filter(s) active
                  </span>
                  <span *ngIf="getActiveFiltersCount() === 0">
                    No filters applied
                  </span>
                </small>
              </div>
              <div class="filter-actions">
                <button class="btn btn-sm btn-outline-secondary me-2" (click)="clearAdvancedFilters()">
                  Clear All
                </button>
                <button class="btn btn-sm btn-primary" (click)="saveFilterPreset()">
                  Save Preset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="quick-stats-row">
        <div class="row g-3 mb-4">
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon bg-primary">
                <i data-feather="shield" appFeatherIcon></i>
              </div>
              <div class="stat-content">
                <h6 class="stat-number">{{ totalItems }}</h6>
                <p class="stat-label">Total Roles</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon bg-success">
                <i data-feather="check-circle" appFeatherIcon></i>
              </div>
              <div class="stat-content">
                <h6 class="stat-number">{{ getActiveRolesCount() }}</h6>
                <p class="stat-label">Active Roles</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon bg-info">
                <i data-feather="users" appFeatherIcon></i>
              </div>
              <div class="stat-content">
                <h6 class="stat-number">{{ getTotalUsersCount() }}</h6>
                <p class="stat-label">Users Assigned</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon bg-warning">
                <i data-feather="key" appFeatherIcon></i>
              </div>
              <div class="stat-content">
                <h6 class="stat-number">{{ getTotalPermissionsCount() }}</h6>
                <p class="stat-label">Total Permissions</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Content Area -->
<div class="roles-content-section">
  <div class="container-fluid">

    <!-- Loading indicator -->
    <div *ngIf="loading" class="loading-section">
      <div class="d-flex justify-content-center align-items-center" style="min-height: 300px;">
        <div class="text-center">
          <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="text-muted">Loading roles...</p>
        </div>
      </div>
    </div>

    <!-- Card View -->
    <div *ngIf="!loading && viewMode === 'cards'" class="roles-grid">
      <div class="row g-4">
        <div class="col-xl-4 col-lg-6 col-md-6" *ngFor="let role of roles">
          <div class="role-card" [class.deleted]="role.deleted_at">
            <div class="role-card-header">
              <div class="role-info">
                <h5 class="role-name">{{ role.name }}</h5>
                <div class="role-status">
                  <span *ngIf="!role.deleted_at" class="badge bg-success-subtle text-success">
                    <i data-feather="check-circle" class="icon-xs me-1" appFeatherIcon></i>
                    Active
                  </span>
                  <span *ngIf="role.deleted_at" class="badge bg-danger-subtle text-danger">
                    <i data-feather="x-circle" class="icon-xs me-1" appFeatherIcon></i>
                    Deleted
                  </span>
                </div>
              </div>
              <div class="role-actions">
                <div class="dropdown">
                  <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i data-feather="more-vertical" class="icon-sm" appFeatherIcon></i>
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="javascript:;" (click)="viewRole(role, viewRoleModal)">
                      <i data-feather="eye" class="icon-sm me-2" appFeatherIcon></i>View Details
                    </a></li>
                    <li><a class="dropdown-item" href="javascript:;" (click)="openRoleModal(roleModal, role)">
                      <i data-feather="edit" class="icon-sm me-2" appFeatherIcon></i>Edit Role
                    </a></li>
                    <li><a class="dropdown-item" href="javascript:;" (click)="managePermissions(role)">
                      <i data-feather="lock" class="icon-sm me-2" appFeatherIcon></i>Manage Permissions
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li *ngIf="!role.deleted_at"><a class="dropdown-item text-danger" href="javascript:;" (click)="deleteRole(role)">
                      <i data-feather="trash" class="icon-sm me-2" appFeatherIcon></i>Delete Role
                    </a></li>
                    <li *ngIf="role.deleted_at"><a class="dropdown-item text-success" href="javascript:;" (click)="restoreRole(role)">
                      <i data-feather="refresh-cw" class="icon-sm me-2" appFeatherIcon></i>Restore Role
                    </a></li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="role-card-body">
              <p class="role-description">
                {{ role.description || 'No description provided' }}
              </p>

              <div class="role-stats">
                <div class="stat-item">
                  <i data-feather="users" class="icon-sm text-primary" appFeatherIcon></i>
                  <span class="stat-value">{{ role.users?.length || 0 }}</span>
                  <span class="stat-label">Users</span>
                </div>
                <div class="stat-item">
                  <i data-feather="key" class="icon-sm text-warning" appFeatherIcon></i>
                  <span class="stat-value">{{ role.permissions?.length || 0 }}</span>
                  <span class="stat-label">Permissions</span>
                </div>
                <div class="stat-item">
                  <i data-feather="calendar" class="icon-sm text-info" appFeatherIcon></i>
                  <span class="stat-value">{{ role.created_at | date:'MMM d' }}</span>
                  <span class="stat-label">Created</span>
                </div>
              </div>
            </div>

            <div class="role-card-footer">
              <div class="quick-actions">
                <button class="btn btn-sm btn-outline-primary" (click)="viewRole(role, viewRoleModal)">
                  <i data-feather="eye" class="icon-xs me-1" appFeatherIcon></i>
                  View
                </button>
                <button class="btn btn-sm btn-outline-secondary" (click)="openRoleModal(roleModal, role)">
                  <i data-feather="edit" class="icon-xs me-1" appFeatherIcon></i>
                  Edit
                </button>
                <button class="btn btn-sm btn-outline-warning" (click)="managePermissions(role)">
                  <i data-feather="lock" class="icon-xs me-1" appFeatherIcon></i>
                  Permissions
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table View -->
    <div *ngIf="!loading && viewMode === 'table'" class="roles-table">
      <div class="card modern-table-card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover modern-table">
              <thead>
                <tr>
                  <th scope="col" sortable="name" (sort)="onSort($event)">
                    <i data-feather="shield" class="icon-sm me-2" appFeatherIcon></i>
                    Role Name
                  </th>
                  <th scope="col">Description</th>
                  <th scope="col" class="text-center">Users</th>
                  <th scope="col" class="text-center">Permissions</th>
                  <th scope="col" class="text-center">Status</th>
                  <th scope="col" class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let role of roles" [class.table-danger]="role.deleted_at">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="role-avatar me-3">
                        <i data-feather="shield" class="icon-sm" appFeatherIcon></i>
                      </div>
                      <div>
                        <h6 class="mb-0">{{ role.name }}</h6>
                        <small class="text-muted">ID: {{ getShortId(role.id) }}</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="text-truncate" style="max-width: 200px; display: inline-block;"
                          [ngbTooltip]="role.description"
                          *ngIf="role.description">
                      {{ role.description }}
                    </span>
                    <span class="text-muted fst-italic" *ngIf="!role.description">No description</span>
                  </td>
                  <td class="text-center">
                    <span class="badge bg-primary-subtle text-primary">
                      {{ role.users?.length || 0 }}
                    </span>
                  </td>
                  <td class="text-center">
                    <span class="badge bg-warning-subtle text-warning">
                      {{ role.permissions?.length || 0 }}
                    </span>
                  </td>
                  <td class="text-center">
                    <span *ngIf="!role.deleted_at" class="badge bg-success">Active</span>
                    <span *ngIf="role.deleted_at" class="badge bg-danger">Deleted</span>
                  </td>
                  <td class="text-center">
                    <div class="action-buttons">
                      <button class="btn btn-sm btn-outline-info me-1" ngbTooltip="View Details" (click)="viewRole(role, viewRoleModal)">
                        <i data-feather="eye" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <button class="btn btn-sm btn-outline-primary me-1" ngbTooltip="Edit Role" (click)="openRoleModal(roleModal, role)">
                        <i data-feather="edit" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <button class="btn btn-sm btn-outline-warning me-1" ngbTooltip="Manage Permissions" (click)="managePermissions(role)">
                        <i data-feather="lock" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <button *ngIf="!role.deleted_at" class="btn btn-sm btn-outline-danger" ngbTooltip="Delete Role" (click)="deleteRole(role)">
                        <i data-feather="trash" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <button *ngIf="role.deleted_at" class="btn btn-sm btn-outline-success" ngbTooltip="Restore Role" (click)="restoreRole(role)">
                        <i data-feather="refresh-cw" class="icon-xs" appFeatherIcon></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && roles && roles.length === 0" class="empty-state-section">
      <div class="text-center py-5">
        <div class="empty-state-icon mb-4">
          <i data-feather="shield-off" class="icon-xxl text-muted" appFeatherIcon></i>
        </div>
        <h5 class="text-muted mb-3">No roles found</h5>
        <p class="text-muted mb-4" *ngIf="searchTerm.value || statusFilter !== 'all'">
          Try adjusting your search criteria or filters
        </p>
        <p class="text-muted mb-4" *ngIf="!searchTerm.value && statusFilter === 'all'">
          Get started by creating your first role
        </p>
        <button class="btn btn-primary" (click)="openRoleModal(roleModal)">
          <i data-feather="plus" class="icon-sm me-2" appFeatherIcon></i>
          Create First Role
        </button>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-section" *ngIf="!loading && totalItems > 0">
      <div class="d-flex justify-content-between align-items-center mt-4">
        <div class="pagination-info">
          <span class="text-muted">
            Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems) }} of {{ totalItems }} roles
          </span>
        </div>
        <div class="pagination-controls">
          <ngb-pagination
            [collectionSize]="totalItems"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            (pageChange)="onPageChange($event)"
            class="pagination-sm"
          ></ngb-pagination>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Role Modal -->
<ng-template #roleModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">{{ formMode === 'create' ? 'Add New Role' : 'Edit Role' }}</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="roleForm">
      <div class="mb-3">
        <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          id="name"
          formControlName="name"
          placeholder="Enter role name"
          [class.is-invalid]="roleForm.get('name')?.invalid && roleForm.get('name')?.touched">
        <div *ngIf="roleForm.get('name')?.invalid && roleForm.get('name')?.touched" class="invalid-feedback">
          <div *ngIf="roleForm.get('name')?.errors?.['required']">Role name is required</div>
        </div>
      </div>

      <div class="mb-3">
        <label for="description" class="form-label">Description</label>
        <textarea class="form-control" id="description" formControlName="description" rows="3" placeholder="Enter role description (optional)"></textarea>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <!-- <button type="button" class="btn btn-outline-info btn-sm" (click)="debugPermissionState()">
      Debug State
    </button> -->
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    <button
      type="button"
      class="btn btn-primary"
      [disabled]="roleForm.invalid || submitting"
      (click)="saveRole()">
      <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
      {{ formMode === 'create' ? 'Create Role' : 'Update Role' }}
    </button>
  </div>
</ng-template>

<!-- View Role Modal -->
<ng-template #viewRoleModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">View Role Details</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body" *ngIf="selectedRole">
    <div class="card">
      <div class="card-body">
        <div class="row mb-3">
          <!-- <div class="col-md-4 fw-bold">ID:</div>
          <div class="col-md-8">
            <code class="text-muted" style="cursor: pointer;" (click)="copyIdToClipboard(selectedRole.id)">{{ selectedRole.id }}</code>
            <button class="btn btn-sm btn-outline-secondary ms-2" (click)="copyIdToClipboard(selectedRole.id)">
              <i data-feather="copy" class="icon-sm" appFeatherIcon></i>
            </button>
          </div> -->
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Name:</div>
          <div class="col-md-8">{{ selectedRole.name }}</div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Description:</div>
          <div class="col-md-8">
            <span *ngIf="selectedRole.description">{{ selectedRole.description }}</span>
            <span *ngIf="!selectedRole.description" class="text-muted fst-italic">No description provided</span>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Status:</div>
          <div class="col-md-8">
            <span *ngIf="!selectedRole.deleted_at" class="badge bg-success">Active</span>
            <span *ngIf="selectedRole.deleted_at" class="badge bg-danger">Deleted</span>
          </div>
        </div>
        <div class="row mb-3" *ngIf="selectedRole.users">
          <div class="col-md-4 fw-bold">Users:</div>
          <div class="col-md-8">
            <span class="badge bg-info">{{ selectedRole.users.length }} user(s) assigned</span>
          </div>
        </div>
        <!-- <div class="row mb-3">
          <div class="col-md-4 fw-bold">Permissions:</div>
          <div class="col-md-8">
            <div *ngIf="selectedRole.permissions && selectedRole.permissions.length > 0">
              <div class="mb-2">
                <span class="badge bg-primary">{{ selectedRole.permissions.length }} permission(s) assigned</span>
              </div>
              <div class="list-group">
                <div
                  *ngFor="let permission of selectedRole.permissions"
                  class="list-group-item list-group-item-action">
                  <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">{{ permission.name }}</h6>
                    <small class="text-muted">ID: {{ getShortId(permission.id) }}</small>
                  </div>
                  <p class="mb-1" *ngIf="permission.description">{{ permission.description }}</p>
                  <small class="text-muted" *ngIf="!permission.description">No description available</small>
                </div>
              </div>
            </div>
            <div *ngIf="!selectedRole.permissions || selectedRole.permissions.length === 0" class="alert alert-info">
              <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
              No permissions assigned to this role
            </div>
          </div>
        </div> -->
        <!-- Timestamps Section -->
        <!-- <div class="row mb-3">
          <div class="col-12">
            <h6 class="text-muted border-bottom pb-2">Timestamps</h6>
          </div>
        </div> -->
        <!-- <div class="row mb-2" *ngIf="selectedRole.created_at">
          <div class="col-md-4 fw-bold">Created:</div>
          <div class="col-md-8">
            <span>{{ selectedRole.created_at | date:'full' }}</span>
            <br>
            <small class="text-muted">{{ selectedRole.created_at | date:'short' }}</small>
          </div>
        </div> -->
        <div class="row mb-2" *ngIf="selectedRole.updated_at">
          <div class="col-md-4 fw-bold">Last Updated:</div>
          <div class="col-md-8">
            <span>{{ selectedRole.updated_at | date:'full' }}</span>
            <br>
            <small class="text-muted">{{ selectedRole.updated_at | date:'short' }}</small>
          </div>
        </div>
        <div class="row mb-2" *ngIf="selectedRole.deleted_at">
          <div class="col-md-4 fw-bold">Deleted:</div>
          <div class="col-md-8">
            <span class="text-danger">{{ selectedRole.deleted_at | date:'full' }}</span>
            <br>
            <small class="text-muted">{{ selectedRole.deleted_at | date:'short' }}</small>
          </div>
        </div>


      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Close</button>
    <button type="button" class="btn btn-primary" (click)="editFromViewModal(roleModal, modal)">
      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
      Edit Role
    </button>
  </div>
</ng-template>
