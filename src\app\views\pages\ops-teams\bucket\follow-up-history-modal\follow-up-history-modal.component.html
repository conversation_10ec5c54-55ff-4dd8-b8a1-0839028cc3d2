<div class="modal-header">
  <h5 class="modal-title">Follow-up History</h5>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss('Cross click')"></button>
</div>
<div class="modal-body">
  <div class="follow-up-history-container">
    <div class="document-info mb-4">
      <h6 class="mb-2">Document:</h6>
      <div class="document-name p-3 bg-light rounded border">
        {{ documentName }}
      </div>
    </div>
    <div *ngIf="followUps.length === 0" class="text-center py-4">
      <p class="text-muted">No follow-up history available for this document.</p>
    </div>

    <div *ngIf="followUps.length > 0">
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>#</th>
              <th>Date</th>
              <th>Time</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let followUp of paginatedFollowUps; let i = index">
              <td>{{ (page - 1) * pageSize + i + 1 }}</td>
              <td>{{ followUp.date }}</td>
              <td>{{ followUp.time }}</td>
              <td>{{ followUp.notes }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="d-flex justify-content-center mt-3" *ngIf="sortedFollowUps.length > pageSize">
        <ngb-pagination
          [collectionSize]="sortedFollowUps.length"
          [(page)]="page"
          [pageSize]="pageSize"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          aria-label="Default pagination">
        </ngb-pagination>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="activeModal.close('Close click')">Close</button>
</div>
