<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title and add button -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Permission Management</h6>
          <button class="btn btn-primary" (click)="openPermissionModal(permissionModal)">
            <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
            Add New Permission
          </button>
        </div>

        <!-- Search and filter -->
        <div class="row mb-3">
          <div class="col-12 col-md-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input
                type="text"
                class="form-control"
                [formControl]="searchTerm"
                placeholder="Search by permission name or description..."
              >
              <button
                *ngIf="searchTerm.value"
                class="input-group-text bg-light text-danger"
                (click)="searchTerm.setValue(''); loadPermissions()">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
            <small class="text-muted" *ngIf="searchTerm.value">
              Searching for: "{{ searchTerm.value }}"
            </small>
          </div>

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="loadPermissions()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col" sortable="name" (sort)="onSort($event)">Permission Name</th>
                <th scope="col">Description</th>
                <th scope="col">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let permission of permissions">
                <td>
                  <span class="badge bg-primary">{{ permission.name }}</span>
                </td>
                <td>{{ permission.description || 'No description' }}</td>
                <td class="action-icons">
  <button type="button" class="action-icon text-info" ngbTooltip="View" (click)="viewPermission(permission, viewPermissionModal)">
    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
  </button>

  <button type="button" class="action-icon" ngbTooltip="Edit" (click)="openPermissionModal(permissionModal, permission)">
    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
  </button>

  <button type="button" class="action-icon" ngbTooltip="Delete" (click)="deletePermission(permission)">
    <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
  </button>

  <button *ngIf="permission.deleted_at" type="button" class="action-icon text-success" ngbTooltip="Restore" (click)="restorePermission(permission)">
    <i data-feather="refresh-cw" class="icon-sm" appFeatherIcon></i>
  </button>
</td>

              </tr>
              <tr *ngIf="permissions && permissions.length === 0">
                <td colspan="3" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="key" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No permissions found</p>
                    <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!loading && totalItems > 0">
          <div>
            <span class="text-muted">Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems) }} of {{ totalItems }} entries</span>
          </div>
          <ngb-pagination
            [collectionSize]="totalItems"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            (pageChange)="onPageChange($event)"
            class="pagination-sm"
          ></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Permission Modal -->
<ng-template #permissionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">{{ formMode === 'create' ? 'Add New Permission' : 'Edit Permission' }}</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="permissionForm">
      <div class="mb-3">
        <label for="name" class="form-label">Permission Name</label>
        <input type="text" class="form-control" id="name" formControlName="name" placeholder="Enter permission name (e.g., manage_users)">
        <div *ngIf="permissionForm.get('name')?.invalid && permissionForm.get('name')?.touched" class="text-danger">
          Permission name is required
        </div>
        <small class="text-muted">Use lowercase with underscores (e.g., view_reports, manage_employees)</small>
      </div>

      <div class="mb-3">
        <label for="description" class="form-label">Description</label>
        <textarea class="form-control" id="description" formControlName="description" rows="3" placeholder="Enter permission description (optional)"></textarea>
        <small class="text-muted">Describe what this permission allows users to do</small>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    <button
      type="button"
      class="btn btn-primary"
      [disabled]="permissionForm.invalid || submitting"
      (click)="savePermission()">
      <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
      {{ formMode === 'create' ? 'Create Permission' : 'Update Permission' }}
    </button>
  </div>
</ng-template>

<!-- View Permission Modal -->
<ng-template #viewPermissionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">View Permission Details</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body" *ngIf="selectedPermission">
    <div class="card">
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Name:</div>
          <div class="col-md-8">
            <span class="badge bg-primary">{{ selectedPermission.name }}</span>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Description:</div>
          <div class="col-md-8">
            <span *ngIf="selectedPermission.description">{{ selectedPermission.description }}</span>
            <span *ngIf="!selectedPermission.description" class="text-muted fst-italic">No description provided</span>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Status:</div>
          <div class="col-md-8">
            <span *ngIf="!selectedPermission.deleted_at" class="badge bg-success">Active</span>
            <span *ngIf="selectedPermission.deleted_at" class="badge bg-danger">Deleted</span>
          </div>
        </div>

        <div class="row mb-2" *ngIf="selectedPermission.updated_at">
          <div class="col-md-4 fw-bold">Last Updated:</div>
          <div class="col-md-8">
            <span>{{ selectedPermission.updated_at | date:'full' }}</span>
            <br>
            <small class="text-muted">{{ selectedPermission.updated_at | date:'short' }}</small>
          </div>
        </div>
        <div class="row mb-2" *ngIf="selectedPermission.deleted_at">
          <div class="col-md-4 fw-bold">Deleted:</div>
          <div class="col-md-8">
            <span class="text-danger">{{ selectedPermission.deleted_at | date:'full' }}</span>
            <br>
            <small class="text-muted">{{ selectedPermission.deleted_at | date:'short' }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Close</button>
    <button type="button" class="btn btn-primary" (click)="editFromViewModal(permissionModal, modal)">
      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
      Edit Permission
    </button>
  </div>
</ng-template>
