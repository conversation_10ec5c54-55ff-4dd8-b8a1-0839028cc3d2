import { Routes } from '@angular/router';
import { dynamicAuthGuard } from '../../../../core/guards/dynamic-auth.guard';

export default [
    {
        path: '',
        loadComponent: () => import('./ops-team.component').then(c => c.OpsTeamComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            permissions: ['ops:access']
        }
    },
    {
        path: 'add-details',
        loadComponent: () => import('./ops-team.component').then(c => c.OpsTeamComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            showDetailsForm: true,
            permissions: ['ops:access']
        }
    },
    {
        path: 'edit/:id',
        loadComponent: () => import('./ops-team.component').then(c => c.OpsTeamComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            showDetailsForm: true,
            permissions: ['ops:access']
        }
    }
] as Routes;
