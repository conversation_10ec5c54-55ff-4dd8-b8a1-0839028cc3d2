<!-- Lead Categories Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-target me-2"></i>
              Lead Categories Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_categories }} total categories, 
              {{ statistics.active_categories }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Lead Category
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Categories
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Categories
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedPriority" (change)="onPriorityFilter()">
                <option value="">All Priorities</option>
                <option *ngFor="let priority of priorityLevels" [value]="priority.value">
                  {{ priority.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedLevel" (change)="onLevelFilter()">
                <option [ngValue]="null">All Levels</option>
                <option [ngValue]="0">Level 0 (Root)</option>
                <option [ngValue]="1">Level 1</option>
                <option [ngValue]="2">Level 2</option>
                <option [ngValue]="3">Level 3</option>
                <option [ngValue]="4">Level 4</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading lead categories...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Category Details</th>
                  <th>Priority & Scoring</th>
                  <th>Qualification Criteria</th>
                  <th>Automation & Rules</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let leadCategory of getCurrentList(); trackBy: trackByLeadCategoryId">
                  <td>
                    <div>
                      <strong>{{ leadCategory.name }}</strong>
                      <span class="badge bg-info ms-2" *ngIf="leadCategory.is_default">Default</span>
                      <small class="d-block text-muted">
                        Code: {{ leadCategory.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="leadCategory.description">
                        {{ leadCategory.description }}
                      </small>
                      <small class="d-block text-muted">
                        Level: {{ leadCategory.level }} | Order: {{ leadCategory.display_order }}
                      </small>
                      <div class="mt-1" *ngIf="leadCategory.tags?.length">
                        <span class="badge bg-light text-dark me-1" *ngFor="let tag of leadCategory.tags.slice(0, 2)">
                          {{ tag }}
                        </span>
                        <span class="badge bg-light text-dark" *ngIf="leadCategory.tags.length > 2">
                          +{{ leadCategory.tags.length - 2 }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span [class]="getPriorityBadgeClass(leadCategory.priority)">
                      {{ getPriorityLabel(leadCategory.priority) }}
                    </span>
                    <small class="d-block text-muted mt-1">
                      <strong>Weight:</strong> {{ leadCategory.scoring_weight }}
                    </small>
                    <small class="d-block text-muted" *ngIf="leadCategory.expected_value">
                      <strong>Expected:</strong> {{ formatExpectedValue(leadCategory.expected_value, leadCategory.currency) }}
                    </small>
                    <small class="d-block text-muted" *ngIf="leadCategory.conversion_rate">
                      <strong>Conversion:</strong> {{ (leadCategory.conversion_rate * 100).toFixed(1) }}%
                    </small>
                    <small class="d-block text-muted" *ngIf="leadCategory.leads_count">
                      <strong>Leads:</strong> {{ leadCategory.leads_count }}
                    </small>
                  </td>
                  <td>
                    <div class="qualification-info">
                      <small class="d-block text-muted">
                        {{ getQualificationSummary(leadCategory) }}
                      </small>
                      <div class="mt-1" *ngIf="leadCategory.qualification_criteria?.scoring_rules?.length">
                        <span class="badge bg-info me-1">
                          {{ leadCategory.qualification_criteria.scoring_rules.length }} scoring rules
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="leadCategory.qualification_criteria?.required_fields?.length">
                        <span class="badge bg-warning me-1">
                          {{ leadCategory.qualification_criteria.required_fields.length }} required fields
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="automation-info">
                      <div *ngIf="getAutomationRulesCount(leadCategory) > 0">
                        <span class="badge bg-success me-1">
                          {{ getAutomationRulesCount(leadCategory) }} automation rules
                        </span>
                      </div>
                      <div *ngIf="leadCategory.follow_up_rules?.length">
                        <small class="d-block text-muted">
                          <strong>Follow-up:</strong> {{ leadCategory.follow_up_rules.length }} rules
                        </small>
                      </div>
                      <div *ngIf="leadCategory.automation_rules?.length">
                        <small class="d-block text-muted">
                          <strong>Automation:</strong> {{ leadCategory.automation_rules.length }} rules
                        </small>
                      </div>
                      <div *ngIf="getAutomationRulesCount(leadCategory) === 0">
                        <small class="text-muted">No automation</small>
                      </div>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(leadCategory.is_active)">
                      {{ getStatusText(leadCategory.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ leadCategory.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(leadCategory)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteLeadCategory(leadCategory)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreLeadCategory(leadCategory)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-target text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Lead Categories' : 'No Lead Categories Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No lead categories have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No lead categories match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first lead category.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Lead Category
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} lead categories
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_categories }}</h3>
                      <p class="mb-0">Total Categories</p>
                    </div>
                    <i class="feather icon-target" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_categories }}</h3>
                      <p class="mb-0">Active Categories</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.average_scoring_weight.toFixed(1) }}</h3>
                      <p class="mb-0">Avg Scoring Weight</p>
                    </div>
                    <i class="feather icon-trending-up" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-secondary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_categories }}</h3>
                      <p class="mb-0">Inactive Categories</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
