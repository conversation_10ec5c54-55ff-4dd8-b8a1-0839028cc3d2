import { 
  Directive, 
  Input, 
  ElementRef, 
  OnInit, 
  OnDestroy, 
  ViewContainerRef,
  TemplateRef,
  Output,
  EventEmitter
} from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { ProgressiveLoadingService, ComponentLoadConfig } from '../services/progressive-loading.service';

/**
 * Lazy Load Directive
 * 
 * Provides declarative lazy loading for components and content.
 * Supports intersection-based loading, delayed loading, and priority-based loading.
 * 
 * Usage:
 * <div *appLazyLoad="'component-id'; config: loadConfig; onLoad: handleLoad">
 *   <!-- Content to show while loading -->
 *   <div class="loading-placeholder">Loading...</div>
 * </div>
 */
@Directive({
  selector: '[appLazyLoad]',
  standalone: true
})
export class LazyLoadDirective implements OnInit, OnDestroy {
  @Input('appLazyLoad') componentId!: string;
  @Input('appLazyLoadConfig') config: ComponentLoadConfig = { priority: 'medium' };
  @Input('appLazyLoadLoader') loader?: () => Promise<any>;
  @Input('appLazyLoadStrategy') strategy: 'intersection' | 'delay' | 'immediate' = 'intersection';
  @Input('appLazyLoadDelay') delay: number = 2000;
  @Input('appLazyLoadThreshold') threshold: number = 0.1;
  @Input('appLazyLoadRootMargin') rootMargin: string = '50px';

  @Output() lazyLoadStart = new EventEmitter<string>();
  @Output() lazyLoadComplete = new EventEmitter<any>();
  @Output() lazyLoadError = new EventEmitter<Error>();

  private destroy$ = new Subject<void>();
  private intersectionObserver?: IntersectionObserver;
  private isLoaded = false;

  constructor(
    private elementRef: ElementRef,
    private viewContainer: ViewContainerRef,
    private templateRef: TemplateRef<any>,
    private progressiveLoadingService: ProgressiveLoadingService
  ) {}

  ngOnInit(): void {
    if (!this.componentId) {
      console.warn('LazyLoadDirective: componentId is required');
      return;
    }

    // Check if component is already loaded
    if (this.progressiveLoadingService.isComponentLoaded(this.componentId)) {
      this.handleLoadComplete(null);
      return;
    }

    // Initialize based on strategy
    switch (this.strategy) {
      case 'intersection':
        this.initIntersectionLoading();
        break;
      case 'delay':
        this.initDelayedLoading();
        break;
      case 'immediate':
        this.initImmediateLoading();
        break;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  /**
   * Initialize intersection-based loading
   */
  private initIntersectionLoading(): void {
    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.isLoaded) {
            console.log(`👁️ LazyLoadDirective: ${this.componentId} entered viewport`);
            this.loadComponent();
          }
        });
      },
      {
        rootMargin: this.rootMargin,
        threshold: this.threshold
      }
    );

    this.intersectionObserver.observe(this.elementRef.nativeElement);
  }

  /**
   * Initialize delayed loading
   */
  private initDelayedLoading(): void {
    if (!this.loader) {
      console.warn(`LazyLoadDirective: loader function required for delayed loading of ${this.componentId}`);
      return;
    }

    this.progressiveLoadingService
      .loadAfterDelay(this.componentId, this.loader, this.delay)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => this.handleLoadComplete(result),
        error: (error) => this.handleLoadError(error)
      });
  }

  /**
   * Initialize immediate loading
   */
  private initImmediateLoading(): void {
    this.loadComponent();
  }

  /**
   * Load the component
   */
  private loadComponent(): void {
    if (this.isLoaded || !this.loader) {
      return;
    }

    this.isLoaded = true;
    this.lazyLoadStart.emit(this.componentId);

    // Disconnect intersection observer since we're loading now
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }

    this.progressiveLoadingService
      .loadComponentImmediate(this.componentId, this.loader)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => this.handleLoadComplete(result),
        error: (error) => this.handleLoadError(error)
      });
  }

  /**
   * Handle successful load completion
   */
  private handleLoadComplete(result: any): void {
    console.log(`✅ LazyLoadDirective: ${this.componentId} loaded successfully`);
    
    // Create the view from template
    this.viewContainer.createEmbeddedView(this.templateRef);
    
    this.lazyLoadComplete.emit(result);
  }

  /**
   * Handle load error
   */
  private handleLoadError(error: Error): void {
    console.error(`❌ LazyLoadDirective: Failed to load ${this.componentId}:`, error);
    
    // Still create the view to show error state
    this.viewContainer.createEmbeddedView(this.templateRef);
    
    this.lazyLoadError.emit(error);
  }

  /**
   * Manually trigger loading (for programmatic control)
   */
  public triggerLoad(): void {
    if (!this.isLoaded) {
      this.loadComponent();
    }
  }

  /**
   * Check if component is loaded
   */
  public get loaded(): boolean {
    return this.isLoaded;
  }
}

/**
 * Lazy Load Container Directive
 * 
 * Provides a container for lazy-loaded content with loading states.
 * 
 * Usage:
 * <div appLazyLoadContainer>
 *   <ng-template #loading>
 *     <div class="spinner">Loading...</div>
 *   </ng-template>
 *   <ng-template #error>
 *     <div class="error">Failed to load</div>
 *   </ng-template>
 *   <ng-template #content>
 *     <!-- Actual content -->
 *   </ng-template>
 * </div>
 */
@Directive({
  selector: '[appLazyLoadContainer]',
  standalone: true
})
export class LazyLoadContainerDirective implements OnInit {
  @Input() showLoadingState: boolean = true;
  @Input() showErrorState: boolean = true;

  constructor(
    private viewContainer: ViewContainerRef,
    private progressiveLoadingService: ProgressiveLoadingService
  ) {}

  ngOnInit(): void {
    // Subscribe to loading state changes
    this.progressiveLoadingService.loadingState$.subscribe(state => {
      this.updateContainerState(state);
    });
  }

  private updateContainerState(state: any): void {
    // Add CSS classes based on loading state
    const element = this.viewContainer.element.nativeElement;
    
    if (state.isLoading && this.showLoadingState) {
      element.classList.add('lazy-loading');
      element.classList.remove('lazy-error', 'lazy-loaded');
    } else if (state.error && this.showErrorState) {
      element.classList.add('lazy-error');
      element.classList.remove('lazy-loading', 'lazy-loaded');
    } else {
      element.classList.add('lazy-loaded');
      element.classList.remove('lazy-loading', 'lazy-error');
    }
  }
}
