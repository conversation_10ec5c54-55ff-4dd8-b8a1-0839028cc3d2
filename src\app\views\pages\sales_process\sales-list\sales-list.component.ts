import { Component, Directive, EventEmitter, Input, OnInit, OnDestroy, Output, QueryList, ViewChildren, ViewChild, ChangeDetectorRef, NgZone, ChangeDetectionStrategy } from '@angular/core';
import { MemorySafeBaseComponent } from '../../../../core/components/memory-safe-base.component';
import { CommonModule } from '@angular/common';
import { RouterLink, Router, ActivatedRoute } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { FormControl, FormGroup, FormBuilder, FormArray, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { SalesFormValidationService } from './sales-form-validation.service';
import { ProductTwoComponent } from '../product_type/product-two/product-two.component';
import { LeadDataTypeService, LeadDataType } from '../../../../core/services/lead-data-type.service';
import { BoardAffiliationService, BoardAffiliation } from '../../../../core/services/board-affiliation.service';
import { ProductOneComponent } from '../product_type/product-one/product-one.component';

import { CarComponent } from '../product_type/product-four/car/car.component';
import { LifeComponent } from '../product_type/product-four/life/life.component';
import { PropertyComponent } from '../product_type/product-three/property/property.component';

import { ProductDataService } from '../../../../core/services/product-data.service';
import { LocationService } from '../../../../core/services/location.service';
import { MasterService } from '../../../../core/services/master.service';
import { ProductTypeService, ProductType, SubProductType } from '../../../../core/services/product-type.service';
import { SubProductTypeService } from '../../../../core/services/sub-product-type.service';
import { AuthService } from '../../../../core/services/auth.service';
import { SalesService, SalesCreateRequest, PeopleInformation } from '../../../../core/services/sales.service';
import { EmployeeCacheService, CachedEmployee } from '../../../../core/services/employee-cache.service';
// Removed EmployeeService - no longer needed
import { ConstitutionService, Constitution } from '../../../../core/services/constitution.service';
import { LeadCategoryService, LeadCategory, Source } from '../../../../core/services/lead-category.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import Swal from 'sweetalert2';
import { forkJoin, of, Subscription } from 'rxjs';
import { catchError, finalize, filter, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';

// Define product type and sub-type interfaces
interface ProductTypeOption {
  value: string;
  label: string;
  code?: string; // Add code field for sub product types
}

// Location and Profession dropdown interfaces
interface LocationOption {
  id: string;
  uuid: string;
  name: string;
}

interface ProfessionOption {
  id: string;
  name: string;
  type: string;
  status: string;
  description?: string;
}

interface ProfessionTypeOption {
  id: string;
  value: string;
  label: string;
}

interface AssociateOption {
  id: string;
  associate_name: string;
  company_name: string;
  location_id: string;
  location_name: string;
  sub_location: string;
  profession_type: string;
  profession_id: string;
  profession_name: string;
  status: boolean;
}

// Sortable directive
export type SortColumn = keyof SalesData | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();
  @Input() selectedProductSubType: any;

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Person interface for multiple people
export interface Person {
  id: number;
  name: string;
  mobile: string;
  email: string;
  connectWith: string; // This will now store employee ID instead of profession name
  connectWithName?: string; // Display name for the selected employee
  employeeId?: string; // UUID of the selected employee
}

// Sales data interface
export interface SalesData {
  id: number;
  leadId: string;
  leadName: string;
  source: string;
  sourceType: string;
  location: string;
  companyName: string;
  projectName: string;
  productType: string;
  status: string; // Allow any string status from API (e.g., "new", "in_progress", "completed")
}



// Helper function for sorting
function compare(v1: string | number, v2: string | number) {
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-sales-list',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    RouterLink,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbdSortableHeader,
    NgbPaginationModule,
    NgbTooltipModule,
    NgbModalModule,
    ProductOneComponent,
    ProductTwoComponent,
    CarComponent,
    LifeComponent,
    PropertyComponent
  ],
  templateUrl: './sales-list.component.html',
  styleUrl: './sales-list.component.scss'
})
export class SalesListComponent extends MemorySafeBaseComponent implements OnInit, OnDestroy {
  // For debugging in template
  console = console;

  // Original data
  salesData: SalesData[] = [];

  // Filtered data
  filteredSales: SalesData[] = [];

  // Subscription for sales updates
  private salesUpdateSubscription: Subscription = new Subscription();

  // Search filter
  searchTerm = new FormControl('', { nonNullable: true });
  associateSearchTerm = new FormControl('', { nonNullable: true });
  selectedAssociate: AssociateOption | null = null;
  associateSearchValue: string = '';

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;
  totalPages = 0;

  // Separate loading states
  paginationLoading = false; // For pagination-specific loading

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // Product form component references
  @ViewChild('productOneRef') productOneComponent: ProductOneComponent;
  @ViewChild('productTwoRef') productTwoComponent: ProductTwoComponent;
  @ViewChild('carRef') carComponent: CarComponent;
  @ViewChild('lifeRef') lifeComponent: LifeComponent;
  @ViewChild('propertyRef') propertyComponent: PropertyComponent;

  // Sales form visibility
  showSalesForm = false;

  // Product type options - now using API data with fallback
  productTypes: ProductTypeOption[] = [
    { value: 'Corporate_Syndication', label: 'Corporate Syndication' },
    { value: 'Retail_Syndication', label: 'Retail Syndication' },
    { value: 'Property', label: 'Property' },
    { value: 'Insurance', label: 'Insurance' }
  ]; // Fallback values - will be replaced by API data

  // Dropdown data
  locations: LocationOption[] = [];
  professions: ProfessionOption[] = [];
  connectWithList: {id: string, name: string, description: string}[] = []; // Connect With dropdown data
  professionTypes: ProfessionTypeOption[] = [];
  productTypesFromApi: ProductType[] = [];
  subProductTypesFromApi: SubProductType[] = [];
  // Removed employeesForPeople - not used in template
  employees: { value: string, label: string, employee_code?: string, id?: string, first_name?: string, last_name?: string }[] = []; // Employees for handover mapping
  associates: AssociateOption[] = [];
  filteredAssociates: AssociateOption[] = [];
  leadCategoriesFromApi: LeadCategory[] = []; // Lead categories from API
  sourcesFromApi: Source[] = []; // Sources from API

  // Filtered professions based on profession type selection
  filteredAssociateProfessions: ProfessionOption[] = [];
  filteredCategoryProfessions: ProfessionOption[] = [];

  // Loading states
  salesLoading = false; // Loading state for sales list
  locationsLoading = false;
  professionsLoading = false;
  connectWithLoading = false; // Loading state for Connect With dropdown
  professionTypesLoading = false;
  productTypesLoading = false;
  subProductTypesLoading = false;
  associatesLoading = false;
  // Removed employeesForPeopleLoading - not needed
  employeesLoading = false;
  leadCategoriesLoading = false; // Loading state for lead categories
  sourcesLoading = false; // Loading state for sources

  // Selected values
  selectedProductType: string = '';
  selectedProductSubType: string = '';
  selectedHandover: string = '';
  selectedStatus: string = 'Hot'; // Default to Hot
  selectedLeadCategory: string = '';
  selectedLeadDataType: string = '';
  selectedProfession: string = '';
  selectedLocation: string = '';
  selectedSource: string = '';
  leadId: string = '';
  company: string = '';
  subLocation: string = '';

  // Fields for when Source is "Self"
  leadName: string = '';
  leadLocation: string = '';
  leadSublocation: string = '';

  // Fields for when Source is "Associate"
  associateName: string = '';
  associateCompanyName: string = '';
  associateLocation: string = '';
  associateSubLocation: string = '';
  associateProfessionType: string = '';
  associateProfession: string = '';
  associateLeadName: string = '';
  associateLeadLocation: string = '';
  associateLeadSublocation: string = '';

  // Fields for when Lead Category is "Associate"
  associateNameCategory: string = '';
  professionalCategory: string = '';
  professionCategory: string = '';
  associateLocationCategory: string = '';
  associateSubLocationCategory: string = '';
  leadNameCategory: string = '';
  leadLocationCategory: string = '';
  leadSubLocationCategory: string = '';

  availableSubTypes: ProductTypeOption[] = [];

  // Form submission state
  isSubmitting: boolean = false;

  // Temporary storage for product form data when submitted from child components
  tempProductFormData: any = null;

  // ID mapping for categorical fields
  // NOTE: These UUIDs are based on the example payload provided
  // In a real implementation, you should fetch these mappings from the API
  leadCategoryMap: Map<string, string> = new Map([
    ['Associate', '402c454c-b9c6-4419-a969-e440e7233239'],
    ['Lead data', '402c454c-b9c6-4419-a969-e440e7233239'] // Using same UUID from example
  ]);

  sourceMap: Map<string, string> = new Map([
    ['Self', '798c4d0a-e1e9-4a02-ab24-05e8d4b89fe0'],
    ['Associate', '798c4d0a-e1e9-4a02-ab24-05e8d4b89fe0'], // Using same UUID from example
    ['BCS', '798c4d0a-e1e9-4a02-ab24-05e8d4b89fe0']
  ]);

  leadDataTypeMap: Map<string, string> = new Map([
    ['Construction Funding', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Inventory Funding', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Project Funding', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Hospital Funding', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Education Funding', '7a56e4df-37a9-4321-8dea-21a123729fba'], // Using same UUID from example
    ['Home Loan', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Loan Against Property', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Lease Rental Discounting', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Non Residential Property Loan', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Property', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Contractor All Risk', '7a56e4df-37a9-4321-8dea-21a123729fba'],
    ['Life Insurance', '7a56e4df-37a9-4321-8dea-21a123729fba']
  ]);

  // Product type and handover mappings based on example
  productTypeMap: Map<string, string> = new Map([
    // Add your product type mappings here based on actual data
  ]);

  handoverMap: Map<string, string> = new Map([
    // Add your handover mappings here based on actual data
    // Example: ['John Doe', '20b677f6-ba72-43be-aeff-99cb9de0b929']
  ]);

  constitutionMap: Map<string, string> = new Map([
    // Add constitution mappings here based on actual data
    ['Private Limited', 'private-limited-uuid'],
    ['Public Limited', 'public-limited-uuid'],
    ['Partnership', 'partnership-uuid'],
    ['Sole Proprietorship', 'sole-proprietorship-uuid'],
    ['Trust', 'trust-uuid'],
    ['Society', 'society-uuid']
  ]);

  boardAffiliationMap: Map<string, string> = new Map([
    // Add board affiliation mappings here based on actual data
    ['CBSE', 'cbse-uuid'],
    ['ICSE', 'icse-uuid'],
    ['State Board', 'state-board-uuid'],
    ['IB', 'ib-uuid'],
    ['IGCSE', 'igcse-uuid']
  ]);

  // Education Funding specific fields
  selectedConstitution: string = '';
  selectedBoardAffiliation: string = '';
  universityAffiliation: string = '';

  // Constitution dropdown data from API
  constitutions: Constitution[] = [];
  constitutionsLoading: boolean = false;

  // Lead Data Types from API
  leadDataTypesFromApi: LeadDataType[] = [];
  leadDataTypesLoading: boolean = false;

  // Board Affiliations from API
  boardAffiliationsFromApi: BoardAffiliation[] = [];
  boardAffiliationsLoading: boolean = false;

  // Multiple people functionality
  people: Person[] = [];
  nextPersonId: number = 1;

  // Reactive form for people information
  peopleForm: FormGroup;
  peopleFormArray: FormArray;

  // Component visibility flags
  showProductOne: boolean = false;
  showProductTwo: boolean = false;
  showCar: boolean = false;
  showLife: boolean = false;
  showProperty: boolean = false;

  // Product sub-type options based on selected product type
  productSubTypes: { [key: string]: ProductTypeOption[] } = {
    'Corporate_Syndication': [
      { value: 'CF', label: 'Construction Funding (CF)' },
      { value: 'IF', label: 'Inventory Funding (IF)' },
      { value: 'PF', label: 'Project Funding (PF)' },
      { value: 'HF', label: 'Hospital Funding (HF)' },
      { value: 'EF', label: 'Education Funding (EF)' }
    ],
    'Retail_Syndication': [
      { value: 'HL', label: 'Home Loan (HL)' },
      { value: 'NRPL', label: 'Non Residential Property Loan (NRPL)' },
      { value: 'LAP', label: 'Loan Against Property (LAP)' },
      { value: 'LRD', label: 'Lease Rental Discounting (LRD)' }
    ],
    'Insurance': [
      { value: 'CAR', label: 'Contractor All Risk' },
      { value: 'LI', label: 'Life Insurance' }
    ],
    'Property': [
      { value: 'Property', label: 'Property' }
    ]
  };

  constructor(
    private productDataService: ProductDataService,
    private router: Router,
    private route: ActivatedRoute,
    private locationService: LocationService,
    private masterService: MasterService,
    private productTypeService: ProductTypeService,
    private subProductTypeService: SubProductTypeService,
    private authService: AuthService,
    private salesService: SalesService,
    private employeeCacheService: EmployeeCacheService,
    private constitutionService: ConstitutionService,
    private leadCategoryService: LeadCategoryService,
    private popupDesignService: PopupDesignService,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private fb: FormBuilder,
    private validationService: SalesFormValidationService,
    private leadDataTypeService: LeadDataTypeService,
    private boardAffiliationService: BoardAffiliationService
  ) {
    super(); // Call MemorySafeBaseComponent constructor

    try {
      // Initialize empty data - will be loaded from API in ngOnInit
      this.salesData = [];
      this.refreshSales();

      // Initialize people form
      this.initPeopleForm();
    } catch (error) {
      console.error('❌ Error in SalesListComponent constructor:', error);
    }
  }

  ngOnInit(): void {

    // Set up search filter with memory-safe subscription
    this.searchTerm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.page = 1;
        this.refreshSales();
      });

    // Set up associate search filter with memory-safe subscription
    this.associateSearchTerm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((searchTerm) => {
        this.filterAssociates(searchTerm);
      });

    // Subscribe to sales updates from the service with memory-safe subscription
    this.salesUpdateSubscription = this.salesService.salesUpdated$
      .pipe(
        filter(updated => updated === true), // Only react to true updates
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        // Add a small delay to ensure API transaction is complete
        setTimeout(() => {
          this.forceRefreshSalesList();
        }, 500); // 500ms delay to ensure API transaction is complete

        // Reset the notification
        this.salesService.resetSalesUpdateNotification();
      });

    // Set up search functionality with debouncing
    this.salesUpdateSubscription.add(
      this.searchTerm.valueChanges
        .pipe(
          debounceTime(500), // Wait 500ms after user stops typing
          distinctUntilChanged(), // Only emit when the value actually changes
          takeUntil(this.destroy$)
        )
        .subscribe(() => {
          this.onSearchChange();
        })
    );

    // Load dropdown data first, then sales data (sales data will be loaded automatically after dropdown data)
    // Lead ID will be generated after sales data is loaded
    this.loadDropdownData();

    // Load lead data types and board affiliations from API
    this.loadLeadDataTypes();
    this.loadBoardAffiliations();

    // Check if we should show the add sales form based on route data
    this.route.data
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data['showAddSalesForm']) {
          this.showSalesForm = true;
          this.resetForm();
        } else {
          this.showSalesForm = false;
        }
      });

    // Initialize with one person entry only if people array is empty
    // Note: We now only initialize with one row, not two
    if (this.people.length === 0) {
      this.addPerson();
    }

    // Check user permissions for debugging
    this.checkUserPermissions();




  }



  /**
   * Check current user permissions
   */
  checkUserPermissions(): void {
    const salesPermissions = ['sales:read', 'sales:manage', 'sales:create'];
    const canAccessSales = salesPermissions.some(permission => this.authService.hasPermission(permission));

    if (!canAccessSales) {
      console.warn('⚠️ User does not have sales permissions. Required permissions:', salesPermissions);
    }
  }

  refreshSales() {
    // For server-side pagination, reload data from API
    this.loadSalesFromAPIWithCallback();
  }

  /**
   * Load sales data from API
   */
  loadSalesFromAPI(): void {
    this.loadSalesFromAPIWithCallback();
  }

  /**
   * Force refresh the entire sales list (used by BehaviorSubject)
   */
  forceRefreshSalesList(): void {
    // Clear current data
    this.salesData = [];
    this.filteredSales = [];
    this.collectionSize = 0;
    this.totalPages = 0;

    // Force immediate UI update to show empty state
    this.cdr.detectChanges();

    // Load fresh data from API with cache busting
    this.loadSalesFromAPIWithCallback(() => {
      // Multiple change detection cycles to ensure UI updates
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          this.ngZone.run(() => {
            this.cdr.detectChanges();
          });
        }, i * 100);
      }
    }, true); // Enable cache busting
  }

  /**
   * Force immediate refresh after sales creation with multiple retry attempts
   */
  forceImmediateRefresh(): void {
    console.log('🚀 Starting force immediate refresh...');

    // Clear existing data immediately
    this.salesData = [];
    this.filteredSales = [];
    this.collectionSize = 0;
    this.cdr.detectChanges();

    // Attempt 1: Immediate refresh with cache busting
    setTimeout(() => {
      console.log('🔄 Attempt 1: Immediate refresh with cache busting');
      this.loadSalesFromAPIWithCallback(() => {
        console.log('✅ Attempt 1 completed');
        this.ngZone.run(() => this.cdr.detectChanges());
      }, true);
    }, 100);

    // Attempt 2: After 500ms delay with cache busting
    setTimeout(() => {
      console.log('🔄 Attempt 2: 500ms delayed refresh with cache busting');
      this.loadSalesFromAPIWithCallback(() => {
        console.log('✅ Attempt 2 completed');
        this.ngZone.run(() => this.cdr.detectChanges());
      }, true);
    }, 500);

    // Attempt 3: After 1000ms delay (final attempt) with cache busting
    setTimeout(() => {
      console.log('🔄 Attempt 3: 1000ms delayed refresh (final) with cache busting');
      this.loadSalesFromAPIWithCallback(() => {
        console.log('✅ Attempt 3 completed - final refresh done');

        // Force multiple change detection cycles
        for (let i = 0; i < 5; i++) {
          setTimeout(() => {
            this.ngZone.run(() => {
              this.cdr.detectChanges();
              console.log(`🔄 Final change detection cycle ${i + 1}`);
            });
          }, i * 50);
        }
      }, true);
    }, 1000);
  }

  /**
   * Load sales data from API with optional callback
   */
  loadSalesFromAPIWithCallback(callback?: () => void, bustCache: boolean = false, isPagination: boolean = false): void {
    console.log('🔄 Loading sales data from API...', bustCache ? '(with cache busting)' : '');
    console.log('📄 Loading page:', this.page, 'pageSize:', this.pageSize);
    console.log('🔍 Search term:', this.searchTerm.value);

    // Set appropriate loading state
    if (isPagination) {
      this.paginationLoading = true;
    } else {
      this.salesLoading = true;
    }

    // Get current search term
    const searchTerm = this.searchTerm.value?.trim() || undefined;

    // Convert page/size to skip/limit for new API format
    const skip = (this.page - 1) * this.pageSize;

    this.salesService.getAllSales(skip, this.pageSize, searchTerm, bustCache).subscribe({
      next: (response) => {
        console.log('✅ Sales data loaded from API:', response);

        if (response.success && response.data) {
          // Convert API response to local SalesData format with proper field mapping
          const salesData = response.data.map((apiSale: any) => ({
            id: apiSale.id || Math.random().toString(36).substring(2, 11),
            leadId: apiSale.unique_id || 'N/A',
            leadName: apiSale.lead_name || 'N/A',
            source: this.getSourceDisplayName(apiSale),
            sourceType: 'API',
            location: this.getLocationDisplayName(apiSale),
            companyName: apiSale.company || 'N/A',
            projectName: apiSale.lead_name || 'N/A',
            // Map product_type_id to product type name using loaded product types
            productType: this.getProductTypeName(apiSale.product_type_id) || 'N/A',
            status: this.mapApiStatusToDisplay(apiSale.status),
            createdDate: apiSale.created_at ? new Date(apiSale.created_at).toLocaleDateString() : new Date().toLocaleDateString(),
            amount: apiSale.amount || 0,
            priority: apiSale.priority || 'medium'
          }));

          // For server-side pagination, set the data directly
          this.salesData = salesData;
          this.filteredSales = salesData;

          // Handle pagination metadata from API response (new structure)
          if (response.meta && response.meta.pagination) {
            const pagination = response.meta.pagination;
            this.collectionSize = pagination.total_count || salesData.length;
            this.totalPages = pagination.total_pages || Math.ceil(this.collectionSize / this.pageSize);
            console.log('📊 Pagination meta (new structure):', {
              total: this.collectionSize,
              totalPages: this.totalPages,
              currentPage: pagination.current_page || this.page,
              pageSize: this.pageSize,
              hasNext: pagination.has_next,
              hasPrevious: pagination.has_previous
            });
          } else if (response.meta) {
            // Fallback to old structure
            this.collectionSize = response.meta.total || response.meta.totalItems || salesData.length;
            this.totalPages = response.meta.totalPages || Math.ceil(this.collectionSize / this.pageSize);
            console.log('📊 Pagination meta (old structure):', {
              total: this.collectionSize,
              totalPages: this.totalPages,
              currentPage: this.page,
              pageSize: this.pageSize
            });
          } else {
            // Fallback if no meta data
            this.collectionSize = salesData.length;
            this.totalPages = 1;
            console.log('⚠️ No pagination meta data found, using fallback');
          }

          console.log('📊 Converted sales data:', this.salesData);
          console.log('📊 Total sales records on current page:', this.salesData.length);
          console.log('📊 Total sales records overall:', this.collectionSize);

          // Generate new Lead ID based on loaded sales data
          this.generateleadId();

          // Reset loading states immediately after data processing
          this.salesLoading = false;
          this.paginationLoading = false;

          // Force multiple change detection cycles to ensure UI updates
          this.cdr.detectChanges();
          console.log('🔄 Initial change detection completed');

          // Additional change detection after a short delay using NgZone
          setTimeout(() => {
            this.ngZone.run(() => {
              this.cdr.detectChanges();
              console.log('🔄 Secondary change detection completed with NgZone');

              // Execute callback after UI is fully updated
              if (callback) {
                callback();
              }
            });
          }, 50);

        } else {
          console.warn('⚠️ API response indicates failure or no data');
          this.salesLoading = false;
          this.paginationLoading = false;
          if (callback) {
            callback();
          }
        }
      },
      error: (error) => {
        console.error('❌ Failed to load sales data from API:', error);
        console.log('📋 No sales data available - API call failed');
        // Initialize with empty array if API fails
        this.salesData = [];
        this.filteredSales = [];
        this.collectionSize = 0;
        this.totalPages = 0;

        // Reset loading states
        this.salesLoading = false;
        this.paginationLoading = false;

        // Force change detection even on error
        this.cdr.detectChanges();

        // Execute callback even on error
        if (callback) {
          callback();
        }
      }
    });
  }

  /**
   * Add newly created sales lead to local list for immediate feedback
   */
  addNewSalesLeadToList(salesRequest: SalesCreateRequest, apiResponse: any): void {
    console.log('➕ Adding new sales lead to local list:', salesRequest);
    console.log('📋 API Response data:', apiResponse.data);
    console.log('🔄 Status from API:', apiResponse.data?.status);
    console.log('🔄 Status from request:', salesRequest.status);

    const newSalesLead = {
      id: apiResponse.data?.id || Math.random().toString(36).substring(2, 11),
      leadId: apiResponse.data?.unique_id || this.leadId, // Use unique_id from API response (e.g., "BCS-2025-012")
      leadName: salesRequest.lead_name,
      source: this.getSourceDisplayName(apiResponse.data),
      sourceType: 'New',
      location: this.getLocationDisplayName(apiResponse.data) || salesRequest.location || 'N/A',
      companyName: salesRequest.company || 'N/A',
      projectName: salesRequest.lead_name,
      productType: this.getProductTypeName(salesRequest.product_type_id) || 'N/A',
      status: this.mapApiStatusToDisplay(apiResponse.data?.status || salesRequest.status), // Use status from API response
      createdDate: new Date().toLocaleDateString(),
      amount: apiResponse.data?.amount || 0,
      priority: apiResponse.data?.priority || 'medium'
    };

    // Add to beginning of the list for immediate visibility
    this.salesData.unshift(newSalesLead);
    console.log('✅ New sales lead added to local list with Lead ID:', newSalesLead.leadId);

    // Refresh the display to show the new lead
    this.refreshSales();
  }



  /**
   * Helper method to check if selected source matches a specific name
   */
  isSourceSelected(sourceName: string): boolean {
    if (!this.selectedSource) return false;

    // If selectedSource is a UUID, find the corresponding source name
    if (this.isValidUUID(this.selectedSource)) {
      const source = this.sourcesFromApi.find(src => src.id === this.selectedSource);
      return source ? source.name === sourceName : false;
    }

    // If selectedSource is already a name, compare directly
    return this.selectedSource === sourceName;
  }

  /**
   * Helper method to get product type name from product type ID
   */
  private getProductTypeName(productTypeId: string): string {
    if (!productTypeId) return 'N/A';

    // Try to find the product type name from loaded data
    const productType = this.productTypesFromApi.find(type => type.id === productTypeId);
    return productType ? productType.name : productTypeId;
  }

  /**
   * Helper method to display API status as-is with proper formatting
   */
  private mapApiStatusToDisplay(apiStatus: string): any {
    if (!apiStatus) return 'new';

    console.log(`🔄 Status display: "${apiStatus}" → "${apiStatus}"`);

    // Return the API status as-is (e.g., "new", "in_progress", "completed")
    return apiStatus;
  }

  /**
   * Helper method to get status class for badge styling
   */
  getStatusClass(status: string): string {
    const statusClassMap: { [key: string]: string } = {
      // New status values
      'Hot': 'bg-danger',
      'Cold': 'bg-info',
      'Warm': 'bg-warning',
      // Legacy status values (keep for backward compatibility)
      'new': 'bg-warning',
      'pending': 'bg-warning',
      'in_progress': 'bg-success',
      'active': 'bg-success',
      'processing': 'bg-success',
      'under_review': 'bg-info',
      'completed': 'bg-info',
      'closed': 'bg-secondary',
      'cancelled': 'bg-danger',
      'rejected': 'bg-danger'
    };

    return statusClassMap[status] || 'bg-secondary';
  }

  /**
   * Handle page change for pagination
   */
  handlePageChange(newPage: number): void {
    console.log('📄 Page change requested:', newPage);
    this.page = newPage;
    // Load new page data from API with pagination loading state
    this.loadSalesFromAPIWithCallback(undefined, false, true);
  }

  /**
   * Handle search term changes
   */
  onSearchChange(): void {
    console.log('🔍 Search term changed:', this.searchTerm.value);
    // Reset to first page when searching
    this.page = 1;
    // Load data with new search term
    this.loadSalesFromAPIWithCallback();
  }

  /**
   * Clear search and reload data
   */
  clearSearch(): void {
    console.log('🔍 Clearing search...');
    this.searchTerm.setValue('');
    this.page = 1;
    this.loadSalesFromAPIWithCallback();
  }

  /**
   * Handle page size changes
   */
  onPageSizeChange(): void {
    console.log('📏 Page size changed:', this.pageSize);
    // Reset to first page when changing page size
    this.page = 1;
    // Load data with new page size
    this.loadSalesFromAPIWithCallback();
  }



  // Note: Client-side search method removed - now using server-side search via API

  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data (client-side sorting for current page only)
    // TODO: Implement server-side sorting for better performance with large datasets
    if (direction === '' || column === '') {
      this.refreshSales();
    } else {
      this.filteredSales = [...this.filteredSales].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }



  // Toggle sales form visibility
  toggleSalesForm() {
    if (!this.showSalesForm) {
      // Navigate to add-sales route
      this.router.navigate(['/sales-list/add-sales']);
    } else {
      // Navigate back to sales list
      this.router.navigate(['/sales-list']);
    }
  }



  // Generate a unique ID for new sales
  generateleadId(): void {
    const currentYear = new Date().getFullYear();

    // Find the highest existing ID number from loaded sales data
    let maxIdNumber = 0;

    if (this.salesData && this.salesData.length > 0) {
      this.salesData.forEach(sale => {
        if (sale.leadId && sale.leadId.startsWith(`BCS-${currentYear}-`)) {
          const idParts = sale.leadId.split('-');
          if (idParts.length === 3) {
            const idNumber = parseInt(idParts[2], 10);
            if (!isNaN(idNumber) && idNumber > maxIdNumber) {
              maxIdNumber = idNumber;
            }
          }
        }
      });
    }

    // Generate next ID by incrementing the highest found ID
    const nextId = maxIdNumber + 1;
    this.leadId = `BCS-${currentYear}-${nextId}`;
    console.log('🆔 Generated new Lead ID:', this.leadId, {
      salesDataLength: this.salesData?.length || 0,
      maxIdFound: maxIdNumber,
      nextId: nextId
    });
  }



  // Handle lead data type selection change
  onLeadDataTypeChange(): void {
    // Reset funding specific fields when lead data type changes
    this.resetFundingFields();

    // Auto-select product type and sub product type based on lead data type
    // Note: This method now uses API data to find the correct IDs
    let productTypeName = '';
    let subProductTypeName = '';

    switch (this.selectedLeadDataType) {
      case 'Construction Funding':
        productTypeName = 'Corporate Syndication';
        subProductTypeName = 'CF';
        break;
      case 'Inventory Funding':
        productTypeName = 'Corporate Syndication';
        subProductTypeName = 'IF';
        break;
      case 'Project Funding':
        productTypeName = 'Corporate Syndication';
        subProductTypeName = 'PF';
        break;
      case 'Hospital Funding':
        productTypeName = 'Corporate Syndication';
        subProductTypeName = 'HF';
        break;
      case 'Education Funding':
        productTypeName = 'Corporate Syndication';
        subProductTypeName = 'EF';
        break;
      case 'Home Loan':
        productTypeName = 'Retail Syndication';
        subProductTypeName = 'HL';
        break;
      case 'Loan Against Property':
        productTypeName = 'Retail Syndication';
        subProductTypeName = 'LAP';
        break;
      case 'Lease Rental Discounting':
        productTypeName = 'Retail Syndication';
        subProductTypeName = 'LRD';
        break;
      case 'Non Residential Property Loan':
        productTypeName = 'Retail Syndication';
        subProductTypeName = 'NRPL';
        break;
      case 'Property':
        productTypeName = 'Property';
        subProductTypeName = 'Property';
        break;
      case 'Contractor All Risk':
        productTypeName = 'Insurance';
        subProductTypeName = 'CAR';
        break;
      case 'Life Insurance':
        productTypeName = 'Insurance';
        subProductTypeName = 'LI';
        break;
      default:
        // Reset if no matching lead data type
        this.selectedProductType = '';
        this.selectedProductSubType = '';
        this.updateProductTypeSelection(true);
        return;
    }

    // Find the product type ID from API data
    const productType = this.productTypesFromApi.find(type =>
      type.name.toLowerCase().includes(productTypeName.toLowerCase())
    );

    if (productType) {
      this.selectedProductType = productType.id;

      // Load sub-product types for this product type and then select the appropriate one
      this.subProductTypeService.getSubProductTypesByProductTypeId(productType.id).subscribe({
        next: (subTypes) => {
          this.availableSubTypes = subTypes.map(subType => ({
            value: subType.id,
            label: subType.name
          }));

          // Find and select the appropriate sub-product type
          const subProductType = subTypes.find(subType =>
            subType.name.toLowerCase().includes(subProductTypeName.toLowerCase())
          );

          if (subProductType) {
            this.selectedProductSubType = subProductType.id;
            // Trigger the product sub type change to show the appropriate form
            this.onProductSubTypeChange();
          }

          // Update component visibility
          this.updateProductTypeSelection(true);
        },
        error: (error) => {
          console.error('Error loading sub product types for lead data type change:', error);
          this.updateProductTypeSelection(true);
        }
      });
    } else {
      // Fallback if product type not found
      this.selectedProductType = '';
      this.selectedProductSubType = '';
      this.updateProductTypeSelection(true);
    }
  }

  // Reset funding specific fields (Education Funding and Hospital Funding)
  resetFundingFields(): void {
    this.selectedConstitution = '';
    this.selectedBoardAffiliation = '';
    this.universityAffiliation = '';
  }



  // Update product type selection with option to preserve sub type
  updateProductTypeSelection(preserveSubType: boolean = false): void {
    // Reset sub-type and component visibility only if not preserving
    if (!preserveSubType) {
      this.selectedProductSubType = '';
    }
    this.resetComponentVisibility();

    // Note: Sub-types are now loaded via API in onProductTypeChange()
    // This method now only handles component visibility and service updates

    // Update the product type in the service with the actual product type name
    if (this.selectedProductType) {
      const selectedProductType = this.productTypesFromApi.find(type => type.id === this.selectedProductType);
      if (selectedProductType) {
        this.productDataService.setProductType(selectedProductType.name);
      }
    }
  }

  // Handle product sub-type selection change
  onProductSubTypeChange(): void {
    console.log('🔄 onProductSubTypeChange triggered');

    // Hide all components first
    this.resetComponentVisibility();

    // Update the product sub-type in the service
    this.productDataService.setProductSubType(this.selectedProductSubType);

    // Get the actual product type and sub product type names from API data
    const selectedProductType = this.productTypesFromApi.find(type => type.id === this.selectedProductType);
    const selectedSubProductType = this.availableSubTypes.find(subType => subType.value === this.selectedProductSubType);

    if (!selectedProductType || !selectedSubProductType) {
      console.warn('Product type or sub product type not found');
      // Force change detection even when no component is shown
      this.cdr.detectChanges();
      return;
    }

    // Update the product type in the service with the specific product type based on sub-product type
    // Map sub-product type code to its corresponding product type name (more reliable)
    let specificProductType = '';
    const subProductTypeCode = selectedSubProductType.code; // Get the code field

    if (subProductTypeCode) {
      switch (subProductTypeCode) {
        case 'HL':
          specificProductType = 'Home Loan';
          break;
        case 'NRPL':
          specificProductType = 'Non Residential Property Loan';
          break;
        case 'LAP':
          specificProductType = 'Loan Against Property';
          break;
        case 'LRD':
          specificProductType = 'Lease Rental Discounting';
          break;
        case 'CF':
          specificProductType = 'Construction Funding';
          break;
        case 'IF':
          specificProductType = 'Inventory Funding';
          break;
        case 'PF':
          specificProductType = 'Project Funding';
          break;
        case 'HF':
          specificProductType = 'Hospital Funding';
          break;
        case 'EF':
          specificProductType = 'Education Funding';
          break;
        case 'CAR':
          specificProductType = 'Contractor All Risk';
          break;
        case 'LI':
          specificProductType = 'Life Insurance';
          break;
        default:
          // Fallback to the sub-product type label without parentheses
          specificProductType = selectedSubProductType.label.replace(/\s*\([^)]*\)/g, '').trim();
          console.warn('⚠️ Unknown sub-product type code:', subProductTypeCode, 'using fallback:', specificProductType);
          break;
      }
    } else {
      // Fallback if no code is available
      specificProductType = selectedSubProductType.label.replace(/\s*\([^)]*\)/g, '').trim();
      console.warn('⚠️ No sub-product type code available, using fallback:', specificProductType);
    }

    this.productDataService.setProductType(specificProductType);
    console.log('🔧 Setting specific product type:', specificProductType, 'for sub-type:', selectedSubProductType.label);

    const productTypeName = selectedProductType.name.toLowerCase();
    const subProductTypeName = selectedSubProductType.label.toLowerCase();
    const subProductTypeValue = selectedSubProductType.value;
    // subProductTypeCode is already declared above

    console.log('🔍 Product Form Selection Debug:');
    console.log('  📋 Product Type:', productTypeName);
    console.log('  📋 Sub Product Type:', subProductTypeName);
    console.log('  🔑 Sub Product Type Value:', subProductTypeValue);
    console.log('  🏷️ Sub Product Type Code:', subProductTypeCode);

    // Show the appropriate component based on product type and sub-type codes (more reliable)
    if (productTypeName.includes('corporate') && productTypeName.includes('syndication')) {
      // Corporate Syndication products: CF, IF, PF, HF, EF
      const corporateSubTypes = ['CF', 'IF', 'PF', 'HF', 'EF'];

      if (subProductTypeCode && corporateSubTypes.includes(subProductTypeCode)) {
        this.showProductOne = true;
        console.log('✅ Showing form for:', selectedSubProductType.label);
        console.log('  🎯 Product Type: Corporate Syndication');
        console.log('  🎯 Sub-type code:', subProductTypeCode);
      } else {
        console.warn('⚠️ Corporate Syndication sub-type not recognized:', subProductTypeCode, '(', selectedSubProductType.label, ')');
      }
    } else if (productTypeName.includes('retail') && productTypeName.includes('syndication')) {
      // Retail Syndication products: HL, LAP, LRD, NRPL
      const retailSubTypes = ['HL', 'LAP', 'LRD', 'NRPL'];

      if (subProductTypeCode && retailSubTypes.includes(subProductTypeCode)) {
        this.showProductTwo = true;
        console.log('✅ Showing form for:', selectedSubProductType.label);
        console.log('  🎯 Product Type: Retail Syndication');
        console.log('  🎯 Sub-type code:', subProductTypeCode);
      } else {
        console.warn('⚠️ Retail Syndication sub-type not recognized:', subProductTypeCode, '(', selectedSubProductType.label, ')');
      }
    } else if (productTypeName.includes('insurance')) {
      // Insurance products: CAR, LI
      if (subProductTypeCode === 'CAR') {
        this.showCar = true;
        console.log('✅ Showing form for:', selectedSubProductType.label);
        console.log('  🎯 Product Type: Insurance - Contractor All Risk');
      } else if (subProductTypeCode === 'LI') {
        this.showLife = true;
        console.log('✅ Showing form for:', selectedSubProductType.label);
        console.log('  🎯 Product Type: Insurance - Life Insurance');
      } else {
        console.warn('⚠️ Insurance sub-type not recognized:', subProductTypeCode, '(', selectedSubProductType.label, ')');
      }
    } else if (productTypeName.includes('property')) {
      // Property products
      this.showProperty = true;
      console.log('✅ Showing form for:', selectedSubProductType.label);
      console.log('  🎯 Product Type: Property');
    } else {
      // Fallback: try to match by code regardless of product type
      console.log('🔄 Fallback: Trying to match by code regardless of product type...');
      console.log('  🔍 Product:', selectedSubProductType.label);

      const corporateSubTypes = ['CF', 'IF', 'PF', 'HF', 'EF'];
      const retailSubTypes = ['HL', 'LAP', 'LRD', 'NRPL'];
      const insuranceSubTypes = ['CAR', 'LI'];

      if (subProductTypeCode && corporateSubTypes.includes(subProductTypeCode)) {
        this.showProductOne = true;
        console.log('✅ Fallback: Showing form for:', selectedSubProductType.label);
        console.log('  🎯 Detected as: Corporate Syndication product');
      } else if (subProductTypeCode && retailSubTypes.includes(subProductTypeCode)) {
        this.showProductTwo = true;
        console.log('✅ Fallback: Showing form for:', selectedSubProductType.label);
        console.log('  🎯 Detected as: Retail Syndication product');
      } else if (subProductTypeCode && insuranceSubTypes.includes(subProductTypeCode)) {
        if (subProductTypeCode === 'CAR') {
          this.showCar = true;
          console.log('✅ Fallback: Showing form for:', selectedSubProductType.label);
          console.log('  🎯 Detected as: Insurance - Contractor All Risk');
        } else if (subProductTypeCode === 'LI') {
          this.showLife = true;
          console.log('✅ Fallback: Showing form for:', selectedSubProductType.label);
          console.log('  🎯 Detected as: Insurance - Life Insurance');
        }
      } else {
        console.warn('⚠️ No form found for product:', selectedSubProductType.label, '(code:', subProductTypeCode, ')');
      }
    }

    // Force change detection to ensure immediate UI update
    this.cdr.detectChanges();

    // Additional timeout to ensure DOM updates are complete
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 0);

    console.log('🎯 Component visibility after change:');
    console.log('  showProductOne:', this.showProductOne);
    console.log('  showProductTwo:', this.showProductTwo);
    console.log('  showCar:', this.showCar);
    console.log('  showLife:', this.showLife);
    console.log('  showProperty:', this.showProperty);
  }

  // Reset all component visibility flags
  resetComponentVisibility(): void {
    console.log('🔄 Resetting all component visibility flags');
    const previousState = {
      showProductOne: this.showProductOne,
      showProductTwo: this.showProductTwo,
      showCar: this.showCar,
      showLife: this.showLife,
      showProperty: this.showProperty
    };

    this.showProductOne = false;
    this.showProductTwo = false;
    this.showCar = false;
    this.showLife = false;
    this.showProperty = false;

    console.log('🔄 Component visibility reset:', {
      previous: previousState,
      current: {
        showProductOne: this.showProductOne,
        showProductTwo: this.showProductTwo,
        showCar: this.showCar,
        showLife: this.showLife,
        showProperty: this.showProperty
      }
    });

    // Force change detection to ensure immediate UI update
    this.cdr.detectChanges();
  }

  // Save sales data
  saveSalesData(): void {
    console.log('🚀🚀🚀 SALES-LIST: saveSalesData() called!');

    // Validate form using enhanced validation
    console.log('🔍 SALES-LIST: About to validate form...');
    try {
      if (!this.validateFormEnhanced()) {
        console.log('❌ SALES-LIST: Enhanced form validation failed!');
        return;
      }
      console.log('✅ SALES-LIST: Enhanced form validation passed!');
    } catch (error) {
      console.error('❌ SALES-LIST: Error during form validation:', error);
      this.isSubmitting = false; // Reset flag on error

      // Show error alert with modern popup design
      this.popupDesignService.showError({
        title: 'Validation Error',
        message: 'An error occurred during form validation. Please try again.',
        confirmText: 'I Understand'
      });
      return;
    }

    // Prevent multiple submissions
    if (this.isSubmitting) {
      console.log('⚠️ SALES-LIST: Already submitting, preventing duplicate submission');
      return;
    }

    console.log('🚀 SALES-LIST: Starting sales submission...');
    this.isSubmitting = true;

    try {

    // Prepare API payload with proper field mapping
    console.log('🔍 Selected location (should be location name):', this.selectedLocation);

    // Only include product_type_id and sub_product_type_id if selected
    const salesRequest: SalesCreateRequest = {
      lead_name: this.getLeadNameForAPI() || 'Lead Name', // Get appropriate lead name based on source
      company: this.company || undefined,
      location: this.getLocationForAPI(), // Get location ID for API (UUID)
      location_id: this.getLocationIdForAPI(), // Get location ID for API (UUID) - same as location field
      sub_location: this.getSubLocationForAPI(),
      in_house_team: this.getInHouseTeam(),
      email: this.getEmailForAPI(),
      university_affiliation: this.universityAffiliation || undefined,
      status: this.selectedStatus, // Use selected status from dropdown
      lead_category_id: this.mapLeadCategoryId(this.selectedLeadCategory),
      source_id: this.mapSourceId(this.selectedSource),
      lead_data_type_id: this.mapLeadDataTypeId(this.selectedLeadDataType),
      product_type_id: this.selectedProductType || undefined, // Include product type ID
      sub_product_type_id: this.selectedProductSubType || undefined, // Include sub product type ID
      created_by_id: this.getCurrentUserId(),
      handover_to_id: this.mapHandoverToId(this.selectedHandover),
      connect_with_id: this.mapConnectWithId(this.professionalCategory),
      constitution_id: this.mapConstitutionId(this.selectedConstitution),
      board_affiliation_id: this.mapBoardAffiliationId(this.selectedBoardAffiliation),
      form_data: this.buildFormData(),
      people_information: this.mapPeopleInformation()
    };

    // Add associate fields to top-level when Lead Category is 'Associate' OR when Lead Category is 'Lead Data' and Source is 'Associate'
    if (this.isLeadCategorySelected('Associate')) {
      // For Lead Category Associate, use manual input fields
      salesRequest.associate_name = this.associateNameCategory;
      salesRequest.associate_company_name = this.company || '';
      salesRequest.associate_location_id = this.getLocationIdByName(this.associateLocationCategory);
      salesRequest.associate_sub_location = this.associateSubLocationCategory;
      salesRequest.associate_profession_type = this.professionalCategory;
      salesRequest.associate_profession_id = this.getProfessionIdByName(this.professionCategory);
    } else if (this.isLeadCategorySelected('Lead Data') && this.isSourceSelected('Associate') && this.selectedAssociate) {
      // For Lead Data + Associate source, use selected associate data
      salesRequest.associate_id = this.selectedAssociate.id;
      salesRequest.associate_name = this.selectedAssociate.associate_name;
      salesRequest.associate_company_name = this.selectedAssociate.company_name;
      salesRequest.associate_location_id = this.selectedAssociate.location_id;
      salesRequest.associate_sub_location = this.selectedAssociate.sub_location;
      salesRequest.associate_profession_type = this.selectedAssociate.profession_type;
      salesRequest.associate_profession_id = this.selectedAssociate.profession_id;
    }

    console.log('🚀 Submitting sales data:', salesRequest);
    console.log('📋 Form field mappings:', {
      selectedLocation: this.selectedLocation,
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      selectedLeadDataType: this.selectedLeadDataType,
      selectedProductType: this.selectedProductType,
      selectedProductSubType: this.selectedProductSubType,
      selectedHandover: this.selectedHandover,
      selectedStatus: this.selectedStatus,
      selectedProfession: this.selectedProfession,
      selectedConstitution: this.selectedConstitution,
      selectedBoardAffiliation: this.selectedBoardAffiliation
    });

    // Validate critical IDs before submission to prevent foreign key errors
    console.log('🔍 Critical ID validation:', {
      lead_category_id: salesRequest.lead_category_id,
      source_id: salesRequest.source_id,
      lead_data_type_id: salesRequest.lead_data_type_id,
      product_type_id: salesRequest.product_type_id,
      sub_product_type_id: salesRequest.sub_product_type_id,
      handover_to_id: salesRequest.handover_to_id,
      profession_type_id: salesRequest.profession_type_id,
      constitution_id: salesRequest.constitution_id,
      board_affiliation_id: salesRequest.board_affiliation_id,
      location_id: salesRequest.location_id
    });

    // Submit to API
    this.salesService.createSalesWithCustomErrorHandling(salesRequest).subscribe({
      next: (response) => {
        console.log('✅ Sales lead created successfully:', response);

        if (response.success) {
          // Reset form immediately
          this.resetForm();

          // Show success alert with modern design
          this.popupDesignService.showSuccess({
            title: 'Success!',
            message: 'Sales lead has been created successfully. The page will refresh to show the updated list.',
            confirmText: 'Great!',
            timer: 3000,
            showProgressBar: true
          }).then(() => {
            // After alert, reload the page to get fresh data (like F5)
            console.log('🔄 Reloading page to show updated sales list...');
            window.location.reload();
          });
        } else {
          console.error('❌ API returned success=false:', response);

          // Show error alert for API success=false with modern design
          this.popupDesignService.showError({
            title: 'Submission Failed',
            message: 'Failed to create sales lead. Please try again.',
            confirmText: 'Try Again'
          });
        }
      },
      error: (error) => {
        console.error('❌ Failed to create sales lead:', error);

        // Handle different error types
        let errorMessage = 'Failed to create sales lead. Please try again.';

        if (error.status === 422) {
          // Validation errors - try to extract specific field errors
          if (error.error && error.error.detail && Array.isArray(error.error.detail)) {
            // Handle the new error format with detail array in a user-friendly way
            const fieldErrors = error.error.detail.map(item => {
              // Extract just the field name from the location path (last item in loc array)
              const fieldName = item.loc[item.loc.length - 1];
              // Make field name more readable (capitalize and add spaces)
              const readableFieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1).replace(/_/g, ' ');
              return `• ${readableFieldName}: ${item.msg}`;
            }).join('\n');
            errorMessage = `Please correct the following errors:\n${fieldErrors}`;
          } else if (error.error && error.error.error && typeof error.error.error === 'object') {
            // Legacy error format handling with user-friendly formatting
            const fieldErrors = Object.keys(error.error.error).map(field => {
              // Make field name more readable (capitalize and add spaces)
              const readableField = field.charAt(0).toUpperCase() + field.slice(1).replace(/_/g, ' ');
              return `• ${readableField}: ${error.error.error[field]}`;
            }).join('\n');
            errorMessage = `Please correct the following errors:\n${fieldErrors}`;
          } else {
            errorMessage = 'Please check your form data and try again.';
          }
        } else if (error.status === 400) {
          errorMessage = 'Invalid data provided. Please check your inputs.';
        } else if (error.status === 401) {
          errorMessage = 'You are not authorized to perform this action.';
        } else if (error.status === 403) {
          errorMessage = 'You do not have permission to create sales leads.';
        } else if (error.status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        } else if (error.status === 0) {
          errorMessage = 'Network error. Please check your internet connection.';
        }

        // Show error alert with modern design
        this.popupDesignService.showWarning({
          title: 'Form Validation Error',
          html: errorMessage.replace(/\n/g, '<br>'),
          confirmText: 'Fix Issues',
          width: '600px',
          customClass: 'validation-error-popup'
        }).then(() => {
          // Ensure isSubmitting is reset after the alert is closed
          this.isSubmitting = false;
        });
      },
      complete: () => {
        this.isSubmitting = false;
      }
    });

    } catch (error) {
      console.error('❌ SALES-LIST: Error during submission preparation:', error);
      this.isSubmitting = false; // Reset flag on error

      // Show error alert with modern design
      this.popupDesignService.showError({
        title: 'Submission Error',
        message: 'We encountered an issue while processing your form. Please try again.',
        confirmText: 'Try Again'
      });
    }
  }

  /**
   * Reset submission state - useful for debugging stuck submission states
   */
  resetSubmissionState(): void {
    console.log('🔧 SALES-LIST: Manually resetting submission state');
    this.isSubmitting = false;
  }

  // ID mapping helper methods
  private mapLeadCategoryId(value: string): string | undefined {
    if (!value) return undefined;

    // If value is already a UUID (from API dropdown), return it directly
    if (this.isValidUUID(value)) {
      return value;
    }

    // Try to find by name in API data
    const category = this.leadCategoriesFromApi.find(cat => cat.name === value);
    if (category) {
      return category.id;
    }

    // Fallback to hardcoded map for backward compatibility
    const mappedId = this.leadCategoryMap.get(value);
    if (!mappedId) {
      console.warn(`No ID mapping found for lead category: ${value}`);
      return undefined; // Return undefined for unmapped values to avoid sending invalid data
    }
    return mappedId;
  }

  private mapSourceId(value: string): string | undefined {
    if (!value) return undefined;

    // If value is already a UUID (from API dropdown), return it directly
    if (this.isValidUUID(value)) {
      return value;
    }

    // Try to find by name in API data first
    const source = this.sourcesFromApi.find(src => src.name === value);
    if (source) {
      return source.id;
    }

    // Fallback to hardcoded map for backward compatibility
    const mappedId = this.sourceMap.get(value);
    if (!mappedId) {
      console.warn(`No ID mapping found for source: ${value}`);
      return undefined;
    }
    return mappedId;
  }

  private mapLeadDataTypeId(value: string): string | undefined {
    if (!value) return undefined;
    const mappedId = this.leadDataTypeMap.get(value);
    if (!mappedId) {
      console.warn(`No ID mapping found for lead data type: ${value}`);
      return undefined;
    }
    return mappedId;
  }

  private mapHandoverToId(value: string): string | undefined {
    if (!value) return undefined;

    // Check if the value is already a UUID
    if (this.isValidUUID(value)) {
      return value; // Already a UUID
    }

    // Try to find employee by id
    const employee = this.employees.find(emp => emp.value === value || emp.id === value);
    if (employee) {
      return employee.id || employee.value;
    }

    // Try to find employee by employee_code
    const employeeByCode = this.employees.find(emp => emp.employee_code === value);
    if (employeeByCode) {
      return employeeByCode.id || employeeByCode.value;
    }

    // Try to find employee by name
    const employeeByName = this.employees.find(emp =>
      emp.first_name === value ||
      emp.last_name === value ||
      `${emp.first_name} ${emp.last_name}` === value
    );
    if (employeeByName) {
      return employeeByName.id || employeeByName.value;
    }

    console.warn(`❌ No employee found for handover: ${value}`);
    return undefined;
  }

  private isValidUUID(str: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  private getCurrentUserId(): string | undefined {
    // Get current user ID from auth service or user context
    try {
      // Try to get current user from auth service (synchronous)
      const currentUser = this.authService.currentUserValue;
      if (currentUser && currentUser.id) {
        console.log(`✅ Found current user ID: ${currentUser.id}`);
        return currentUser.id;
      }

      // Try to get from localStorage or session storage
      const userDataStr = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
      if (userDataStr) {
        const userData = JSON.parse(userDataStr);
        if (userData.id) {
          console.log(`✅ Found user ID from storage: ${userData.id}`);
          return userData.id;
        }
      }

      console.warn('❌ Could not get current user ID from auth service or storage');
      return '3fa85f64-5717-4562-b3fc-2c963f66afa6'; // Fallback to example UUID
    } catch (error) {
      console.warn('❌ Error getting current user ID:', error);
      return '3fa85f64-5717-4562-b3fc-2c963f66afa6'; // Fallback to example UUID
    }
  }

  // Helper method to map people information to API format
  private mapPeopleInformation(): PeopleInformation[] {
    // Use the form values for people information
    return this.peopleControls.map((control, index) => {
      const personValue = control.value;
      return {
        name: personValue.name || '',
        mobile: personValue.mobile || '',
        email: personValue.email || '',
        is_primary_contact: index === 0, // First person is primary contact
        is_active: true,
        notes: personValue.connectWithName || personValue.connectWith || '', // Use employee name or ID as notes
        role_id: null, // Set to null as per API structure
        connect_with_id: this.mapPersonConnectWithId(personValue.connectWith) // Map connect with name to UUID
      };
    });
  }

  // Helper method to capture product form data
  private getProductFormData(): any {
    let productFormData: any = {};

    try {
      if (this.showProductOne && this.productOneComponent) {
        // Get data from ProductOneComponent using the new getFormData method
        if (this.productOneComponent.getFormData && this.productOneComponent.isFormValid()) {
          productFormData = this.productOneComponent.getFormData();
          console.log('✅ Captured ProductOne form data:', productFormData);
        } else {
          console.warn('⚠️ ProductOne form is invalid or not available');
        }
      } else if (this.showProductTwo && this.productTwoComponent) {
        // Get data from ProductTwoComponent using the new getFormData method
        if (this.productTwoComponent.getFormData) {
          productFormData = this.productTwoComponent.getFormData();
          console.log('✅ Captured ProductTwo form data:', productFormData);
        } else {
          console.warn('⚠️ ProductTwo getFormData method is not available');
        }
      } else if (this.showCar && this.carComponent) {
        // Get data from CarComponent (Insurance - CAR)
        if (this.carComponent.getFormData) {
          productFormData = this.carComponent.getFormData();
          console.log('✅ Captured Car form data:', productFormData);
        } else {
          console.warn('⚠️ Car getFormData method is not available');
        }
      } else if (this.showLife && this.lifeComponent) {
        // Get data from LifeComponent (Insurance - Life)
        if (this.lifeComponent.getFormData) {
          productFormData = this.lifeComponent.getFormData();
          console.log('✅ Captured Life form data:', productFormData);
        } else {
          console.warn('⚠️ Life getFormData method is not available');
        }
      } else if (this.showProperty && this.propertyComponent) {
        // Get data from PropertyComponent
        productFormData = this.propertyComponent.getFormData();
        console.log('✅ Captured Property form data:', productFormData);
      } else {
        console.log('ℹ️ No product form component is currently displayed');
        return null;
      }

      return productFormData;
    } catch (error) {
      console.error('❌ Error capturing product form data:', error);
      return null;
    }
  }



  // Helper method to get in-house team information
  private getInHouseTeam(): string {
    // This could be determined based on the selected product type, lead category, etc.
    // For now, return a default value or derive from form data
    if (this.selectedLeadDataType) {
      if (this.selectedLeadDataType.includes('Education')) {
        return 'EdTech Solutions Team';
      } else if (this.selectedLeadDataType.includes('Hospital')) {
        return 'Healthcare Solutions Team';
      } else if (this.selectedLeadDataType.includes('Property') || this.selectedLeadDataType.includes('Loan')) {
        return 'Financial Services Team';
      } else if (this.selectedLeadDataType.includes('Insurance')) {
        return 'Insurance Solutions Team';
      }
    }
    return 'General Sales Team'; // Default team
  }

  // Helper method to build form data object
  private buildFormData(): any {
    const formData: any = {};

    // Add specific data based on lead data type
    if (this.selectedLeadDataType === 'Education Funding') {
      formData.course_type = 'Live + Recorded'; // Default or from additional form fields
      formData.student_capacity = '500+ users'; // Default or from additional form fields
      formData.integration_needed = 'Zoom, Moodle, Payment Gateway'; // Default or from additional form fields

      // Add education specific fields
      if (this.selectedConstitution) {
        formData.constitution = this.selectedConstitution;
      }
      if (this.selectedBoardAffiliation) {
        // Use board affiliation ID if available from API, otherwise use name
        const boardAffiliationId = this.getBoardAffiliationId(this.selectedBoardAffiliation);
        formData.board_affiliation = boardAffiliationId || this.selectedBoardAffiliation;
        formData.board_affiliation_name = this.selectedBoardAffiliation; // Keep name for reference
      }
      if (this.universityAffiliation) {
        formData.university_affiliation = this.universityAffiliation;
      }
    } else if (this.selectedLeadDataType === 'Hospital Funding') {
      formData.facility_type = 'Multi-specialty Hospital'; // Default or from additional form fields
      formData.bed_capacity = '100+ beds'; // Default or from additional form fields
      formData.specializations = 'Cardiology, Orthopedics, Neurology'; // Default or from additional form fields

      // Add hospital specific fields
      if (this.selectedConstitution) {
        formData.constitution = this.selectedConstitution;
      }
    } else if (this.selectedLeadDataType && this.selectedLeadDataType.includes('Loan')) {
      formData.loan_amount_required = 'To be determined'; // Default or from additional form fields
      formData.loan_purpose = this.selectedLeadDataType; // Use the lead data type as purpose
      formData.property_type = 'Residential/Commercial'; // Default or from additional form fields
    } else if (this.selectedLeadDataType && this.selectedLeadDataType.includes('Insurance')) {
      formData.insurance_type = this.selectedLeadDataType; // Use the lead data type
      formData.coverage_amount = 'To be determined'; // Default or from additional form fields
      formData.policy_duration = '1-5 years'; // Default or from additional form fields
    } else {
      // Generic form data for other types
      formData.requirements = 'General business requirements';
      formData.timeline = 'To be discussed';
      formData.budget_range = 'To be determined';
    }

    // Add common metadata
    formData.lead_id = this.leadId;
    formData.form_submission_date = new Date().toISOString();

    // Use lead data type ID if available from API, otherwise use name
    const leadDataTypeId = this.getLeadDataTypeId(this.selectedLeadDataType);
    formData.source_details = {
      category: this.selectedLeadCategory,
      source: this.selectedSource,
      data_type: leadDataTypeId || this.selectedLeadDataType,
      data_type_name: this.selectedLeadDataType // Keep name for reference
    };

    // Add Self-specific fields to form data when source is Self
    if (this.isSourceSelected('Self')) {
      formData.self_source_details = {
        lead_name: this.leadName,
        lead_location: this.leadLocation,
        lead_sublocation: this.leadSublocation
      };
    }

    // Add Associate-specific fields to form data when source is Associate
    if (this.isSourceSelected('Associate')) {
      formData.associate_source_details = {
        associate_name: this.associateName,
        company_name: this.associateCompanyName,
        location: this.associateLocation,
        sub_location: this.associateSubLocation,
        profession_type: this.associateProfessionType,
        profession: this.associateProfession,
        lead_name: this.associateLeadName,
        lead_location: this.associateLeadLocation,
        lead_sublocation: this.associateLeadSublocation
      };
    }

    // Add Lead Category Associate-specific fields to form data when lead category is Associate
    if (this.isLeadCategorySelected('Associate')) {
      formData.lead_category_associate_details = {
        associate_name: this.associateNameCategory,
        professional_category: this.professionalCategory,
        profession_category: this.professionCategory,
        associate_location: this.associateLocationCategory,
        associate_sub_location: this.associateSubLocationCategory,
        lead_name: this.leadNameCategory,
        lead_location: this.leadLocationCategory,
        lead_sub_location: this.leadSubLocationCategory
      };
      console.log('✅ Lead Category Associate details added to form_data:', formData.lead_category_associate_details);
    }

    // Add Lead Category Associate-specific fields to form data when lead category is 'Lead Data' and source is 'Associate'
    if (this.isLeadCategorySelected('Lead Data') && this.isSourceSelected('Associate') && this.selectedAssociate) {
      formData.lead_category_associate_details = {
        associate_name: this.selectedAssociate.associate_name,
        professional_category: this.selectedAssociate.profession_type,
        profession_category: this.selectedAssociate.profession_name,
        associate_location: this.selectedAssociate.location_name,
        associate_sub_location: this.selectedAssociate.sub_location || 'Associate Sub Location',
        lead_name: this.getLeadNameForAPI() || '',
        lead_location: this.getLocationForAPI() || '',
        lead_sub_location: this.getSubLocationForAPI() || ''
      };
      console.log('✅ Lead Data + Associate source details added to form_data:', formData.lead_category_associate_details);
    }

    // Capture and include product form data if available
    // Use temporary product form data if available (from child component submission)
    // Otherwise, get it from the current component state
    const productFormData = this.tempProductFormData || this.getProductFormData();
    if (productFormData) {
      formData.product_form_data = productFormData;
      console.log('✅ Product form data included in form_data:', productFormData);
      console.log('🔍 Final customers data in payload:', productFormData.customers);
      console.log('🔍 Final customers count in payload:', productFormData.customers?.length || 0);
    } else {
      console.log('ℹ️ No product form data to include');
    }

    return formData;
  }

  // Validate form
  validateForm(): boolean {
    console.log('🔍 VALIDATION: Starting form validation...');
    console.log('🔍 VALIDATION: Current form state:', {
      selectedLocation: this.selectedLocation,
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      selectedLeadDataType: this.selectedLeadDataType,
      selectedProfession: this.selectedProfession,
      company: this.company,
      peopleCount: this.people.length,
      selectedConstitution: this.selectedConstitution,
      selectedBoardAffiliation: this.selectedBoardAffiliation,
      universityAffiliation: this.universityAffiliation
    });

    // Reset isSubmitting flag if validation fails
    const resetSubmittingOnError = () => {
      this.isSubmitting = false;
      return false;
    };

    // Location validation is handled per source type below
    // No need for general selectedLocation validation

    // Product Type and Sub Product Type are now optional
    // if (!this.selectedProductType) {
    //   alert('Please select a product type');
    //   return false;
    // }

    // if (!this.selectedProductSubType) {
    //   alert('Please select a sub product type');
    //   return false;
    // }

    // Validate lead category specific fields - now optional
    // if (this.selectedLeadCategory === 'Associate') {
    //   if (!this.selectedProfession) {
    //     alert('Please select a profession type for Associate category');
    //     return false;
    //   }
    // }

    // Validate source specific fields
    if (this.isSourceSelected('Associate')) {
      // Associate name and company fields are now optional
      // if (!this.associateName || this.associateName.trim() === '') {
      //   alert('Please enter associate name when source is Associate');
      //   return false;
      // }
      // if (!this.associateCompanyName || this.associateCompanyName.trim() === '') {
      //   alert('Please enter company name when source is Associate');
      //   return false;
      // }
      // Associate location fields are now optional
      // if (!this.associateLocation || this.associateLocation.trim() === '') {
      //   alert('Please select location when source is Associate');
      //   return false;
      // }
      // if (!this.associateSubLocation || this.associateSubLocation.trim() === '') {
      //   alert('Please enter sub location when source is Associate');
      //   return false;
      // }
      // Associate profession fields are now optional
      // if (!this.associateProfessionType || this.associateProfessionType.trim() === '') {
      //   alert('Please select profession type when source is Associate');
      //   return false;
      // }
      // if (!this.associateProfession || this.associateProfession.trim() === '') {
      //   alert('Please select profession when source is Associate');
      //   return false;
      // }
      // Associate lead name is now optional
      // if (!this.associateLeadName || this.associateLeadName.trim() === '') {
      //   alert('Please enter lead name when source is Associate');
      //   return false;
      // }
      // Associate lead location fields are now optional
      // if (!this.associateLeadLocation || this.associateLeadLocation.trim() === '') {
      //   alert('Please select lead location when source is Associate');
      //   return false;
      // }
      // if (!this.associateLeadSublocation || this.associateLeadSublocation.trim() === '') {
      //   alert('Please enter lead sublocation when source is Associate');
      //   return false;
      // }
    }

    // Validate Self source specific fields
    if (this.isSourceSelected('Self')) {
      // Lead name is now optional
      // if (!this.leadName || this.leadName.trim() === '') {
      //   alert('Please enter lead name when source is Self');
      //   return false;
      // }
      // Lead location fields are now optional
      // if (!this.leadLocation || this.leadLocation.trim() === '') {
      //   alert('Please select lead location when source is Self');
      //   return false;
      // }
      // if (!this.leadSublocation || this.leadSublocation.trim() === '') {
      //   alert('Please enter lead sublocation when source is Self');
      //   return false;
      // }
    }

    // Validate Constitution field for both Education Funding and Hospital Funding
    if (this.selectedLeadDataType === 'Education Funding' || this.selectedLeadDataType === 'Hospital Funding') {
      if (!this.selectedConstitution) {
        alert(`Please select a constitution for ${this.selectedLeadDataType}`);
        return resetSubmittingOnError();
      }
    }

    // Validate Education Funding specific fields (Board Affiliation and University Affiliation)
    if (this.selectedLeadDataType === 'Education Funding') {
      if (!this.selectedBoardAffiliation) {
        alert('Please select a board affiliation for Education Funding');
        return resetSubmittingOnError();
      }

      if (!this.universityAffiliation || this.universityAffiliation.trim() === '') {
        alert('Please enter university affiliation for Education Funding');
        return resetSubmittingOnError();
      }
    }

    // People information validation using reactive forms
    if (this.peopleForm.invalid) {
      console.log('❌ VALIDATION: People form is invalid');
      console.log('Form errors:', this.peopleForm.errors);

      // Mark all fields as touched to show validation errors
      this.peopleControls.forEach(control => {
        control.markAllAsTouched();
      });

      // Check which fields are invalid
      this.peopleControls.forEach((control, index) => {
        if (control.get('connectWith')?.invalid) {
          console.log(`❌ VALIDATION: Person ${index + 1} missing Connect With`);
        }
        if (control.get('name')?.invalid) {
          console.log(`❌ VALIDATION: Person ${index + 1} missing Name`);
        }
        if (control.get('mobile')?.invalid) {
          console.log(`❌ VALIDATION: Person ${index + 1} missing Mobile`);
        }
      });

      // Show alert for validation errors
      alert('Please fill in all required fields in People Information section');
      return resetSubmittingOnError();
    }

    console.log('✅ VALIDATION: All validations passed!');
    return true;
  }

  /**
   * Enhanced validation method using the validation service
   * This method provides comprehensive validation with centralized rules
   */
  validateFormEnhanced(): boolean {
    console.log('🔍 ENHANCED VALIDATION: Starting comprehensive form validation...');

    // Reset isSubmitting flag if validation fails
    const resetSubmittingOnError = () => {
      this.isSubmitting = false;
      return false;
    };

    // Prepare form data for validation
    const formData = {
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      selectedLeadDataType: this.selectedLeadDataType,
      associateNameCategory: this.associateNameCategory,
      company: this.company,
      selectedConstitution: this.selectedConstitution,
      selectedBoardAffiliation: this.selectedBoardAffiliation,
      universityAffiliation: this.universityAffiliation,
      associateName: this.associateName,
      associateCompanyName: this.associateCompanyName,
      associateLocation: this.associateLocation,
      associateSubLocation: this.associateSubLocation,
      people: this.people
    };

    // Validate using the validation service
    const validationResult = this.validationService.validateSalesForm(formData);

    // If validation fails, show errors
    if (!validationResult.isValid) {
      const errorMessages = this.validationService.getFormattedErrors(validationResult);
      const errorMessage = 'Please fix the following errors:\n\n' + errorMessages.join('\n');

      // Use modern popup design for better error display
      this.popupDesignService.showWarning({
        title: 'Form Validation Errors',
        html: errorMessage.replace(/\n/g, '<br>'),
        confirmText: 'Fix Issues',
        width: '600px',
        customClass: 'validation-error-popup'
      });

      console.log('❌ ENHANCED VALIDATION: Form validation failed with errors:', validationResult.errors);

      // Mark people form fields as touched to show validation errors
      if (this.peopleForm.invalid) {
        this.peopleControls.forEach(control => {
          control.markAllAsTouched();
        });
      }

      return resetSubmittingOnError();
    }

    console.log('✅ ENHANCED VALIDATION: All validations passed!');
    return true;
  }

  // Helper methods for validation service integration

  /**
   * Get current form data for validation
   */
  private getCurrentFormData(): any {
    return {
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      selectedLeadDataType: this.selectedLeadDataType,
      associateNameCategory: this.associateNameCategory,
      company: this.company,
      selectedConstitution: this.selectedConstitution,
      selectedBoardAffiliation: this.selectedBoardAffiliation,
      universityAffiliation: this.universityAffiliation,
      associateName: this.associateName,
      associateCompanyName: this.associateCompanyName,
      associateLocation: this.associateLocation,
      associateSubLocation: this.associateSubLocation,
      people: this.people
    };
  }

  /**
   * Check if a field should show validation feedback
   */
  shouldShowFieldValidation(fieldName: string, value: any, touched: boolean = true): boolean {
    return this.validationService.shouldShowValidation(fieldName, value, touched, this.getCurrentFormData());
  }

  /**
   * Get validation errors for a specific field
   */
  getFieldValidationErrors(fieldName: string, value: any): string[] {
    const result = this.validationService.validateFieldRealTime(fieldName, value, this.getCurrentFormData());
    return result.errors;
  }

  // Reset form to initial state
  resetForm(): void {
    this.company = '';
    this.subLocation = '';
    this.selectedProductType = '';
    this.selectedProductSubType = '';
    this.selectedHandover = '';
    this.selectedStatus = 'Hot'; // Reset to default status
    this.selectedLeadCategory = '';
    this.selectedLeadDataType = '';
    this.selectedProfession = '';
    this.selectedLocation = '';
    this.selectedSource = '';
    this.selectedConstitution = '';
    this.selectedBoardAffiliation = '';
    this.universityAffiliation = '';

    // Reset Self source fields
    this.leadName = '';
    this.leadLocation = '';
    this.leadSublocation = '';

    // Reset Associate source fields
    this.associateName = '';
    this.associateCompanyName = '';
    this.associateLocation = '';
    this.associateSubLocation = '';
    this.associateProfessionType = '';
    this.associateProfession = '';
    this.associateLeadName = '';
    this.associateLeadLocation = '';
    this.associateLeadSublocation = '';

    // Reset Associate Category fields
    this.associateNameCategory = '';
    this.associateSearchValue = '';
    this.filteredAssociates = [];

    // Reset people array and form
    this.people = [];
    this.nextPersonId = 1;

    // Reset the form array
    this.peopleFormArray.clear();

    // Add only one person - this will add both form group and people array entry
    this.addPerson();

    // Reset component visibility flags
    this.showProductOne = false;
    this.showProductTwo = false;
    this.showCar = false;
    this.showLife = false;
    this.showProperty = false;

    // Reset available sub types
    this.availableSubTypes = [];

    // Generate new lead ID
    this.generateleadId();

    // Clear temporary product form data
    this.tempProductFormData = null;

    // Reset product components if they exist
    if (this.productOneComponent) {
      this.productOneComponent.resetFormAfterSubmission();
    }
    if (this.productTwoComponent) {
      this.productTwoComponent.resetFormAfterSubmission();
    }
    if (this.propertyComponent) {
      this.propertyComponent.resetForm();
    }

    console.log('✅ Form reset successfully');
  }

  // Get product type label from value - now uses API data with fallback
  getProductTypeLabel(value: string): string {
    // First try to get from API data
    if (this.productTypesFromApi && this.productTypesFromApi.length > 0) {
      const productType = this.productTypesFromApi.find(type => type.id === value || type.name === value);
      if (productType) {
        return productType.name;
      }
    }

    // Fallback to hardcoded values
    const productType = this.productTypes.find(type => type.value === value);
    return productType ? productType.label : value;
  }

  /**
   * Get the selected sub product type name instead of UUID
   */
  getSelectedSubProductTypeName(): string {
    if (!this.selectedProductSubType) {
      return '';
    }

    const selectedSubProductType = this.availableSubTypes.find(subType => subType.value === this.selectedProductSubType);
    return selectedSubProductType?.label || this.selectedProductSubType;
  }

  /**
   * Handle product form submission from child components
   * This method is triggered when Product 1, Product 2, etc. emit submitSales event
   */
  onProductFormSubmit(productFormData: any): void {
    console.log('🎯🎯🎯 SALES-LIST: Product form submitted event received!');
    console.log('🎯 Product form submitted, triggering sales API:', productFormData);
    console.log('🔍 Received customers data:', productFormData?.customers);
    console.log('🔍 Number of customers received:', productFormData?.customers?.length || 0);

    // Store the product form data temporarily
    this.tempProductFormData = productFormData;

    console.log('🚀 SALES-LIST: About to call saveSalesData()');
    // Trigger the main sales submission
    this.saveSalesData();
  }
  /**
   * Deletes a sale record after confirmation using API
   */
  deleteSale(sale: any): void {
    // Show confirmation dialog
    if (confirm(`Are you sure you want to delete the sales record for ${sale.leadName}?`)) {
      console.log('🗑️ Deleting sales record:', sale);

      // Show loading state
      this.salesLoading = true;

      // Call API to delete the sales record
      this.salesService.deleteSales(sale.id).subscribe({
        next: (response) => {
          console.log('✅ Sales record deleted successfully:', response);

          if (response.success) {
            // Remove from local array after successful deletion
            this.salesData = this.salesData.filter(s => s.id !== sale.id);

            // Update collection size
            this.collectionSize = this.salesData.length;

            // Check if current page is now empty and adjust if needed
            const maxPage = Math.ceil(this.collectionSize / this.pageSize);
            if (this.page > maxPage && maxPage > 0) {
              this.page = maxPage;
            }

            // Recalculate the filtered sales
            this.refreshSales();

            // Show success message
            alert('Sales record deleted successfully');
          } else {
            console.error('❌ Delete operation failed:', response);
            alert('Failed to delete sales record. Please try again.');
          }

          this.salesLoading = false;
        },
        error: (error) => {
          console.error('❌ Failed to delete sales record:', error);
          this.salesLoading = false;

          // Show user-friendly error message
          let errorMessage = 'Failed to delete sales record. Please try again.';

          if (error.status === 404) {
            errorMessage = 'Sales record not found. It may have already been deleted.';
          } else if (error.status === 403) {
            errorMessage = 'You do not have permission to delete this sales record.';
          } else if (error.status === 0) {
            errorMessage = 'Unable to connect to server. Please check your internet connection.';
          }

          alert(errorMessage);
        }
      });
    }
  }

  /**
   * Initialize the people form with FormArray
   */
  initPeopleForm(): void {
    // Create the form array
    this.peopleFormArray = this.fb.array([]);

    // Create the main form group containing the form array
    this.peopleForm = this.fb.group({
      people: this.peopleFormArray
    });

    // Don't add any initial person here - will be added in ngOnInit
  }

  /**
   * Create a form group for a person
   */
  createPersonFormGroup(person?: Person): FormGroup {
    return this.fb.group({
      id: [person?.id || this.nextPersonId++],
      connectWith: [person?.connectWith || '', Validators.required],
      name: [person?.name || '', Validators.required],
      mobile: [person?.mobile || '', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      email: [person?.email || '', [Validators.email]],
      connectWithName: [person?.connectWithName || ''],
      employeeId: [person?.employeeId || '']
    });
  }

  /**
   * Get the people form array
   */
  get peopleControls() {
    return (this.peopleForm.get('people') as FormArray).controls;
  }

  // Multiple people management methods
  addPerson(): void {
    // Add to the form array
    this.addPersonFormGroup();

    // Also maintain the people array for backward compatibility
    const newPerson: Person = {
      id: this.nextPersonId,
      name: '',
      mobile: '',
      email: '',
      connectWith: ''
    };
    this.people.push(newPerson);
  }

  /**
   * Add a new person form group to the form array
   */
  addPersonFormGroup(person?: Person): void {
    this.peopleFormArray.push(this.createPersonFormGroup(person));
  }

  removePerson(personId: number): void {
    if (this.peopleControls.length > 1) {
      // Find the index of the person in the form array
      const index = this.peopleControls.findIndex(control => control.get('id')?.value === personId);
      if (index !== -1) {
        // Remove from form array
        this.peopleFormArray.removeAt(index);

        // Also maintain the people array for backward compatibility
        this.people = this.people.filter(person => person.id !== personId);
      }
    }
  }

  // Removed onEmployeeSelected method - not used in template

  /**
   * Load associates data from API
   */
  loadAssociates(): void {
    console.log('🚀 Starting to load associates...');
    this.associatesLoading = true;

    this.masterService.getRawAssociates().subscribe({
      next: (response) => {
        console.log('📥 Raw associates response:', response);
        if (response.success && response.data) {
          this.associates = response.data.map((associate: any) => ({
            id: associate.id,
            associate_name: associate.associate_name,
            company_name: associate.company_name,
            location_id: associate.location_id,
            location_name: associate.location_name,
            sub_location: associate.sub_location,
            profession_type: associate.profession_type,
            profession_id: associate.profession_id,
            profession_name: associate.profession_name,
            status: associate.status
          }));

          // Initialize filtered associates with all associates
          this.filteredAssociates = [...this.associates];
          console.log('✅ Associates loaded successfully:', this.associates.length, this.associates);
        } else {
          console.log('⚠️ No associates data in response:', response);
        }
      },
      error: (error) => {
        console.error('❌ Error loading associates:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          error: error.error
        });
        this.associates = [];
        this.filteredAssociates = [];
      },
      complete: () => {
        this.associatesLoading = false;
        console.log('🏁 Associates loading completed');
      }
    });
  }

  /**
   * Filter associates based on search term
   */
  filterAssociates(searchTerm: string): void {
    if (!searchTerm || searchTerm.trim() === '') {
      this.filteredAssociates = [...this.associates];
    } else {
      const term = searchTerm.toLowerCase().trim();
      this.filteredAssociates = this.associates.filter(associate =>
        associate.associate_name.toLowerCase().includes(term) ||
        associate.company_name.toLowerCase().includes(term) ||
        (associate.location_name && associate.location_name.toLowerCase().includes(term))
      );
    }
  }

  /**
   * Handle input in Associate Name Category field for Lead Category = Associate
   */
  onAssociateNameCategoryInput(event: any): void {
    const searchTerm = event.target.value;
    this.associateNameCategory = searchTerm;

    if (searchTerm && searchTerm.length >= 2) {
      this.filterAssociates(searchTerm);
    } else {
      this.filteredAssociates = [];
    }
  }

  /**
   * Handle focus on Associate Name Category field for Lead Category = Associate
   */
  onAssociateNameCategoryFocus(): void {
    console.log('🎯 Associate Name Category field focused');

    // Load associates if not already loaded
    if (this.associates.length === 0) {
      console.log('📥 Loading associates for Associate Name Category field...');
      this.loadAssociates();
    } else if (this.associateNameCategory && this.associateNameCategory.length >= 2) {
      // If there's already text in the field, filter associates
      this.filterAssociates(this.associateNameCategory);
    } else {
      // Show existing associates
      this.filteredAssociates = [...this.associates];
    }
  }

  /**
   * Handle associate selection for Lead Category = Associate
   */
  onAssociateSelectForCategory(associate: any): void {
    // Set the associateNameCategory field
    this.associateNameCategory = associate.associate_name;

    // Also populate other associate fields
    this.associateLocationCategory = associate.location_name || '';
    this.associateSubLocationCategory = associate.sub_location || '';
    this.professionalCategory = associate.profession_type || '';
    this.professionCategory = associate.profession_name || '';
    this.company = associate.company_name || '';

    // Filter professions based on selected professional type if available
    if (associate.profession_type) {
      this.filterCategoryProfessions();
    }

    // Hide dropdown
    this.filteredAssociates = [];

    console.log('✅ Associate selected for category:', associate);
  }

  /**
   * Handle focus on associate field
   */
  onAssociateFieldFocus(): void {
    console.log('🎯 Associate field focused');

    // Load associates if not already loaded
    if (this.associates.length === 0 && this.shouldLoadAssociates()) {
      console.log('📥 Loading associates on focus...');
      this.loadAssociates();
    } else {
      // Show existing associates
      this.filteredAssociates = [...this.associates];
    }
  }

  /**
   * Handle associate selection
   */
  onAssociateSelect(associate: AssociateOption): void {
    this.selectedAssociate = associate;

    // Populate fields based on current form context
    // Note: Lead Category Associate fields are now manual input only (no auto-population)

    if (this.isSourceSelected('Associate')) {
      // For Source Associate
      this.associateName = associate.associate_name;
      this.associateLocation = associate.location_name;
      this.associateSubLocation = associate.sub_location || '';

      // Auto-populate profession type and profession for Source Associate
      if (associate.profession_type) {
        this.associateProfessionType = associate.profession_type;
        console.log('🔧 Set associate profession type:', this.associateProfessionType);

        // Filter professions based on the selected profession type
        this.filterAssociateProfessions();
      }

      if (associate.profession_name) {
        this.associateProfession = associate.profession_name;
        console.log('🔧 Set associate profession:', this.associateProfession);
      }
    }

    if (this.isLeadCategorySelected('Associate')) {
      // Auto-populate profession type and profession for Lead Category Associate
      if (associate.profession_type) {
        this.professionalCategory = associate.profession_type;
        console.log('🔧 Set professional category:', this.professionalCategory);

        // Filter professions based on the selected profession type
        this.filterCategoryProfessions();
      }

      if (associate.profession_name) {
        this.professionCategory = associate.profession_name;
        console.log('🔧 Set profession category:', this.professionCategory);
      }
    }

    // Common fields
    this.company = associate.company_name;

    // Update search term with selected associate name and hide dropdown
    this.associateSearchTerm.setValue(associate.associate_name);
    this.filteredAssociates = [];

    console.log('✅ Associate selected:', associate);
  }

  /**
   * Check if associates should be loaded based on form state
   */
  shouldLoadAssociates(): boolean {
    // Only load associates for Lead Data + Associate source combination
    // Lead Category Associate now uses manual input (no search functionality)
    return ((this.isLeadCategorySelected('Lead Data') || this.isLeadCategorySelected('Lead data')) && this.isSourceSelected('Associate'));
  }

  /**
   * Get location ID by name for Lead Category Associate fields
   */
  getLocationIdByName(locationName: string): string | null {
    if (!locationName) return null;
    const location = this.locations.find(loc => loc.name === locationName);
    return location ? location.id || location.uuid : null;
  }

  /**
   * Get profession ID by name for Lead Category Associate fields
   */
  getProfessionIdByName(professionName: string): string | null {
    if (!professionName) return null;
    const profession = this.professions.find(prof => prof.name === professionName);
    return profession ? profession.id : null;
  }

  /**
   * Handle changes in Lead Category or Source to load associates when needed
   * Also update Lead Source based on Lead Category selection
   */
  onLeadCategoryOrSourceChange(): void {
    console.log('🔄 Lead Category or Source changed:', {
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      shouldLoad: this.shouldLoadAssociates(),
      associatesLength: this.associates.length
    });

    // Update Lead Source based on Lead Category selection
    if (this.isLeadCategorySelected('Associate')) {
      // Find the Associate source ID
      const associateSource = this.sourcesFromApi.find(src => src.name === 'Associate');
      if (associateSource) {
        console.log('✅ Setting source to Associate based on Lead Category');
        this.selectedSource = associateSource.id;

        // Update the ProductDataService with the lead category and source
        this.productDataService.setLeadCategory('Associate');
        this.productDataService.setLeadSource('Associate');
      }
    } else if (this.isLeadCategorySelected('Lead Data') || this.isLeadCategorySelected('Lead data')) {
      // For Lead Data category, keep the current source or let user select
      console.log('✅ Lead Data category selected, user can select source');

      // Update the ProductDataService with the lead category
      this.productDataService.setLeadCategory('Lead Data');

      // If source is selected, update the ProductDataService
      if (this.selectedSource) {
        const selectedSource = this.sourcesFromApi.find(src => src.id === this.selectedSource);
        if (selectedSource) {
          this.productDataService.setLeadSource(selectedSource.name);
        }
      }
    }

    if (this.shouldLoadAssociates() && this.associates.length === 0) {
      console.log('📥 Loading associates...');
      this.loadAssociates();
    }
  }

  /**
   * Check if the selected lead category should show the Source dropdown
   * This method checks both ID and name for backward compatibility
   */
  shouldShowSourceDropdown(): boolean {
    return this.isLeadCategorySelected('Lead Data') || this.isLeadCategorySelected('Lead data');
  }

  /**
   * Helper method to check if a specific lead category is selected
   * Works with both UUID (from API) and name (for backward compatibility)
   */
  isLeadCategorySelected(categoryName: string): boolean {
    if (!this.selectedLeadCategory) return false;

    // If selectedLeadCategory is a UUID, find the category name
    if (this.isValidUUID(this.selectedLeadCategory)) {
      const category = this.leadCategoriesFromApi.find(cat => cat.id === this.selectedLeadCategory);
      return category?.name === categoryName;
    }

    // For backward compatibility, check the name directly
    return this.selectedLeadCategory === categoryName;
  }

  /**
   * Load dropdown data for locations, professions, profession types, product types, and sub product types
   */
  loadDropdownData(): void {
    // Set loading states first
    this.locationsLoading = true;
    this.professionsLoading = true;
    this.connectWithLoading = true;
    this.professionTypesLoading = true;
    this.productTypesLoading = true;
    this.subProductTypesLoading = true;
    // Removed employeesForPeopleLoading
    this.employeesLoading = true;
    this.constitutionsLoading = true;
    this.leadCategoriesLoading = true;
    this.sourcesLoading = true;

    // Load all dropdown data in parallel
    forkJoin({
      locations: this.locationService.getActiveLocations().pipe(
        catchError(error => {
          console.error('Error loading locations:', error);
          return of([]);
        })
      ),
      professions: this.masterService.getActiveProfessions().pipe(
        catchError(error => {
          console.error('Error loading professions:', error);
          return of([]);
        })
      ),
      connectWithList: this.masterService.getConnectWithList().pipe(
        catchError(error => {
          console.error('Error loading connect with list:', error);
          return of([]);
        })
      ),
      professionTypes: this.masterService.getProfessionTypesForDropdown().pipe(
        catchError(error => {
          console.error('Error loading profession types:', error);
          return of([]);
        })
      ),
      productTypes: this.productTypeService.getActiveProductTypes().pipe(
        catchError(error => {
          console.error('Error loading product types:', error);
          return of([]);
        })
      ),
      subProductTypes: this.productTypeService.getActiveSubProductTypes().pipe(
        catchError(error => {
          console.error('Error loading sub product types:', error);
          return of([]);
        })
      ),
      employees: this.employeeCacheService.getEmployees().pipe(
        catchError(error => {
          console.error('Error loading employees:', error);
          return of([]);
        })
      ),
      // Removed employeesForPeople - not used in template
      constitutions: this.constitutionService.getConstitutions({ is_active: true }).pipe(
        catchError(error => {
          console.error('Error loading constitutions:', error);
          return of([]);
        })
      ),
      leadCategories: this.leadCategoryService.getActiveLeadCategories().pipe(
        catchError(error => {
          console.error('Error loading lead categories:', error);
          return of([]);
        })
      ),
      sources: this.leadCategoryService.getActiveSources().pipe(
        catchError(error => {
          console.error('Error loading sources:', error);
          return of([]);
        })
      )
    }).pipe(
      finalize(() => {
        this.locationsLoading = false;
        this.professionsLoading = false;
        this.connectWithLoading = false;
        this.professionTypesLoading = false;
        this.productTypesLoading = false;
        this.subProductTypesLoading = false;
        // Removed employeesForPeopleLoading
        this.employeesLoading = false;
        this.constitutionsLoading = false;
        this.leadCategoriesLoading = false;
        this.sourcesLoading = false;

        // Force change detection to update UI
        this.cdr.detectChanges();
      }),
      takeUntil(this.destroy$)
    ).subscribe({
      next: (data: any) => {
        // Transform locations data
        this.locations = data.locations.map(location => ({
          id: location.id || location.uuid, // Support both id and uuid
          uuid: location.uuid,
          name: location.name
        }));

        // Transform professions data
        this.professions = data.professions.map(profession => ({
          id: profession.id,
          name: profession.name,
          type: profession.type,
          status: profession.status,
          description: profession.description
        }));

        // Transform Connect With data
        this.connectWithList = data.connectWithList.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description
        }));

        // Transform profession types data
        this.professionTypes = data.professionTypes.map(professionType => ({
          id: professionType.id,
          value: professionType.value,
          label: professionType.label
        }));

        // Store product types and sub product types from API
        this.productTypesFromApi = data.productTypes;
        this.subProductTypesFromApi = data.subProductTypes;

        console.log('🔍 Raw product types from API:', data.productTypes);
        console.log('🔍 Raw sub product types from API:', data.subProductTypes);

        // Replace hardcoded productTypes with API data
        if (this.productTypesFromApi && this.productTypesFromApi.length > 0) {
          this.productTypes = this.productTypesFromApi.map(type => ({
            value: type.id, // Use ID as value for form submission
            label: type.name // Use name as display label
          }));
          console.log('✅ Product types updated from API:', this.productTypes);
          console.log('✅ Total product types loaded:', this.productTypes.length);
        } else {
          console.warn('⚠️ No product types received from API');
        }

        // Transform employees for handover dropdown
        this.employees = (data.employees || []).map((emp: any) => ({
          value: emp.id,
          label: `${emp.first_name || ''} ${emp.last_name || ''}${emp.employee_code ? ' (' + emp.employee_code + ')' : ''}`.trim(),
          employee_code: emp.employee_code,
          id: emp.id,
          first_name: emp.first_name,
          last_name: emp.last_name
        }));

        // Removed employeesForPeople transformation - not used in template

        // Store constitutions data
        this.constitutions = data.constitutions || [];

        // Store lead categories and sources data from API
        this.leadCategoriesFromApi = data.leadCategories || [];
        this.sourcesFromApi = data.sources || [];

        console.log('✅ Loaded locations:', this.locations.length, this.locations);
        console.log('✅ Loaded professions:', this.professions.length, this.professions);
        console.log('✅ Loaded Connect With list:', this.connectWithList.length, this.connectWithList);
        console.log('✅ Loaded profession types:', this.professionTypes.length, this.professionTypes);
        console.log('✅ Loaded product types:', this.productTypesFromApi.length, this.productTypesFromApi);
        console.log('✅ Loaded sub product types:', this.subProductTypesFromApi.length, this.subProductTypesFromApi);
        console.log('✅ Loaded employees for handover:', this.employees.length, this.employees);
        // Removed employeesForPeople logging
        console.log('✅ Loaded constitutions:', this.constitutions.length, this.constitutions);
        console.log('✅ Loaded lead categories:', this.leadCategoriesFromApi.length, this.leadCategoriesFromApi);
        console.log('✅ Loaded sources:', this.sourcesFromApi.length, this.sourcesFromApi);

        // Now that dropdown data (including product types) is loaded, load sales data
        console.log('🔄 Loading sales data now that product types are available...');
        this.loadSalesFromAPIWithCallback();
      },
      error: (error) => {
        console.error('Error loading dropdown data:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          error: error.error
        });
      }
    });
  }

  /**
   * Load lead data types from API
   */
  loadLeadDataTypes(): void {
    this.leadDataTypesLoading = true;
    console.log('🔍 Loading lead data types from API...');

    this.leadDataTypeService.getActiveLeadDataTypes().subscribe({
      next: (leadDataTypes) => {
        console.log('✅ Lead data types loaded successfully:', leadDataTypes);
        this.leadDataTypesFromApi = leadDataTypes;
        this.leadDataTypesLoading = false;
      },
      error: (error) => {
        console.error('❌ Failed to load lead data types:', error);
        this.leadDataTypesLoading = false;
        // Keep hardcoded fallback for now
        this.leadDataTypesFromApi = [];
      }
    });
  }

  /**
   * Load board affiliations from API
   */
  loadBoardAffiliations(): void {
    this.boardAffiliationsLoading = true;
    console.log('🔍 Loading board affiliations from API...');

    this.boardAffiliationService.getActiveBoardAffiliations().subscribe({
      next: (boardAffiliations) => {
        console.log('✅ Board affiliations loaded successfully:', boardAffiliations);
        this.boardAffiliationsFromApi = boardAffiliations;
        this.boardAffiliationsLoading = false;
      },
      error: (error) => {
        console.error('❌ Failed to load board affiliations:', error);
        this.boardAffiliationsLoading = false;
        // Keep hardcoded fallback for now
        this.boardAffiliationsFromApi = [];
      }
    });
  }

  /**
   * Helper method to get lead data type ID from name
   */
  getLeadDataTypeId(name: string): string | null {
    if (!name) return null;

    const leadDataType = this.leadDataTypesFromApi.find(type => type.name === name);
    return leadDataType ? leadDataType.id : null;
  }

  /**
   * Helper method to get board affiliation ID from name
   */
  getBoardAffiliationId(name: string): string | null {
    if (!name) return null;

    const boardAffiliation = this.boardAffiliationsFromApi.find(affiliation => affiliation.name === name);
    return boardAffiliation ? boardAffiliation.id : null;
  }

  /**
   * Helper method to get lead data type name from ID
   */
  getLeadDataTypeName(id: string): string | null {
    if (!id) return null;

    const leadDataType = this.leadDataTypesFromApi.find(type => type.id === id);
    return leadDataType ? leadDataType.name : null;
  }

  /**
   * Helper method to get board affiliation name from ID
   */
  getBoardAffiliationName(id: string): string | null {
    if (!id) return null;

    const boardAffiliation = this.boardAffiliationsFromApi.find(affiliation => affiliation.id === id);
    return boardAffiliation ? boardAffiliation.name : null;
  }

  trackByPersonId(_index: number, person: Person): number {
    return person.id;
  }

  /**
   * Handle profession type change for Associate source
   */
  onAssociateProfessionTypeChange(): void {
    console.log('🔄 Associate profession type changed:', this.associateProfessionType);

    // Clear the selected profession when profession type changes
    this.associateProfession = '';

    // Filter professions based on selected profession type
    this.filterAssociateProfessions();
  }

  /**
   * Handle profession type change for Lead Category
   */
  onProfessionalCategoryTypeChange(): void {
    console.log('🔄 Professional category type changed:', this.professionalCategory);

    // Clear the selected profession when profession type changes
    this.professionCategory = '';

    // Filter professions based on selected profession type
    this.filterCategoryProfessions();
  }

  /**
   * Filter professions for Associate source based on selected profession type
   */
  private filterAssociateProfessions(): void {
    if (!this.associateProfessionType) {
      this.filteredAssociateProfessions = [];
      return;
    }

    this.filteredAssociateProfessions = this.professions.filter(profession =>
      profession.type === this.associateProfessionType && profession.status === 'active'
    );

    console.log('✅ Filtered associate professions:', this.filteredAssociateProfessions);
  }

  /**
   * Filter professions for Lead Category based on selected profession type
   */
  private filterCategoryProfessions(): void {
    if (!this.professionalCategory) {
      this.filteredCategoryProfessions = [];
      return;
    }

    this.filteredCategoryProfessions = this.professions.filter(profession =>
      profession.type === this.professionalCategory && profession.status === 'active'
    );

    console.log('✅ Filtered category professions:', this.filteredCategoryProfessions);
  }

  /**
   * Get location ID for API based on lead category and source type
   */
  private getLocationForAPI(): string | undefined {
    let locationName = '';

    console.log('🔍 Getting location for API:', {
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      leadLocationCategory: this.leadLocationCategory,
      leadLocation: this.leadLocation,
      associateLeadLocation: this.associateLeadLocation,
      associateLocation: this.associateLocation
    });

    // Check Lead Category first (this is what the UI shows)
    if (this.isLeadCategorySelected('Associate')) {
      locationName = this.leadLocationCategory || '';
      console.log('✅ Using leadLocationCategory for Lead Category Associate:', locationName);
    }
    // Then check Source type for backward compatibility
    else if (this.isSourceSelected('Self')) {
      locationName = this.leadLocation || '';
      console.log('✅ Using leadLocation for Source Self:', locationName);
    } else if (this.isSourceSelected('Associate')) {
      locationName = this.associateLeadLocation || this.associateLocation || '';
      console.log('✅ Using associateLeadLocation for Source Associate:', locationName);
    }

    if (!locationName) {
      console.log('❌ No location name found, returning undefined');
      return undefined;
    }

    // Find the location object by name and return its ID
    const location = this.locations.find(loc => loc.name === locationName);
    const locationId = location?.id || location?.uuid;

    console.log('🎯 Found location:', { locationName, location, locationId });

    if (locationId) {
      console.log('✅ Returning location ID for API:', locationId);
      return locationId;
    } else {
      console.log('❌ Location ID not found, returning undefined');
      return undefined;
    }
  }

  /**
   * Get location ID for API based on source type (same as getLocationForAPI now)
   */
  private getLocationIdForAPI(): string | undefined {
    // Since getLocationForAPI now returns location ID, we can just call it
    return this.getLocationForAPI();
  }

  /**
   * Get source display name based on lead category
   * If lead_category_id is "Associate" (402c454c-b9c6-4419-a969-e440e7233239), display "Associate"
   * If lead_category_id is "Lead Data" (c8ee56a9-3298-49fb-aefe-71e18c71fde7), fetch source name from sources API
   */
  private getSourceDisplayName(apiSale: any): string {
    const leadCategoryId = apiSale.lead_category_id;
    const sourceId = apiSale.source_id;

    console.log('🔍 Getting source display name for:', {
      lead_category_id: leadCategoryId,
      source_id: sourceId,
      form_data_source: apiSale.form_data?.source_details?.source,
      source_name: apiSale.source_name
    });

    // Lead Category: Associate (402c454c-b9c6-4419-a969-e440e7233239)
    if (leadCategoryId === '402c454c-b9c6-4419-a969-e440e7233239') {
      console.log('✅ Lead Category is Associate, returning "Associate"');
      return 'Associate';
    }

    // Lead Category: Lead Data (c8ee56a9-3298-49fb-aefe-71e18c71fde7)
    if (leadCategoryId === 'c8ee56a9-3298-49fb-aefe-71e18c71fde7') {
      console.log('🔍 Lead Category is Lead Data, looking up source name');

      // Try to get source name from loaded sources data
      if (sourceId && this.sourcesFromApi.length > 0) {
        const source = this.sourcesFromApi.find(s => s.id === sourceId);
        if (source) {
          console.log('✅ Found source name from API data:', source.name);
          return source.name;
        }
      }

      // Fallback to form_data source or source_name
      if (apiSale.form_data?.source_details?.source) {
        console.log('✅ Using source from form_data:', apiSale.form_data.source_details.source);
        return apiSale.form_data.source_details.source;
      }

      if (apiSale.source_name) {
        console.log('✅ Using source_name from API:', apiSale.source_name);
        return apiSale.source_name;
      }

      console.log('⚠️ No source found for Lead Data category');
      return 'N/A';
    }

    // For other lead categories or unknown, use existing fallback logic
    console.log('🔄 Using fallback source logic for unknown lead category');
    return apiSale.form_data?.source_details?.source || apiSale.source_name || 'N/A';
  }

  /**
   * Get location display name from API response
   */
  private getLocationDisplayName(apiSale: any): string {
    console.log('🔍 Getting location display name for:', {
      location: apiSale.location,
      location_id: apiSale.location_id,
      location_name: apiSale.location_name,
      associate_location_name: apiSale.associate_location_name,
      associate_location_id: apiSale.associate_location_id,
      form_data_location: apiSale.form_data?.location,
      availableLocations: this.locations.length
    });

    // Try multiple ways to get location name
    if (apiSale.location?.name) {
      console.log('✅ Found location from location.name:', apiSale.location.name);
      return apiSale.location.name;
    }
    if (typeof apiSale.location === 'string' && apiSale.location) {
      console.log('✅ Found location from location string:', apiSale.location);
      return apiSale.location;
    }
    if (apiSale.location_name) {
      console.log('✅ Found location from location_name:', apiSale.location_name);
      return apiSale.location_name;
    }

    // Try to resolve location from location_id
    if (apiSale.location_id && this.locations.length > 0) {
      const location = this.locations.find(loc => loc.id === apiSale.location_id || loc.uuid === apiSale.location_id);
      if (location) {
        console.log('✅ Found location from location_id:', location.name);
        return location.name;
      }
    }

    // Try form data locations
    if (apiSale.form_data?.location) {
      console.log('✅ Found location from form_data.location:', apiSale.form_data.location);
      return apiSale.form_data.location;
    }
    if (apiSale.form_data?.source_details?.location) {
      console.log('✅ Found location from form_data.source_details.location:', apiSale.form_data.source_details.location);
      return apiSale.form_data.source_details.location;
    }

    // Try associate location fields
    if (apiSale.associate_location_name) {
      console.log('✅ Found location from associate_location_name:', apiSale.associate_location_name);
      return apiSale.associate_location_name;
    }
    if (apiSale.associate_location_id && this.locations.length > 0) {
      const location = this.locations.find(loc => loc.id === apiSale.associate_location_id || loc.uuid === apiSale.associate_location_id);
      if (location) {
        console.log('✅ Found location from associate_location_id:', location.name);
        return location.name;
      }
    }

    console.log('❌ No location found, returning N/A');
    return 'N/A';
  }

  /**
   * Get lead name for API based on lead category and source type
   */
  private getLeadNameForAPI(): string {
    console.log('🔍 Getting lead name for API:', {
      selectedLeadCategory: this.selectedLeadCategory,
      selectedSource: this.selectedSource,
      leadNameCategory: this.leadNameCategory,
      leadName: this.leadName,
      associateLeadName: this.associateLeadName
    });

    // Check Lead Category first (this is what the UI shows)
    if (this.isLeadCategorySelected('Associate')) {
      const leadName = this.leadNameCategory || '';
      console.log('✅ Using leadNameCategory for Lead Category Associate:', leadName);
      return leadName;
    }

    // Then check Source type for backward compatibility
    if (this.isSourceSelected('Self')) {
      const leadName = this.leadName || '';
      console.log('✅ Using leadName for Source Self:', leadName);
      return leadName;
    } else if (this.isSourceSelected('Associate')) {
      const leadName = this.associateLeadName || '';
      console.log('✅ Using associateLeadName for Source Associate:', leadName);
      return leadName;
    }

    console.log('❌ No lead name found, returning empty string');
    return '';
  }

  /**
   * Get email for API based on source type
   */
  private getEmailForAPI(): string | undefined {
    // For now, return undefined as email is not captured in the current form
    // This can be enhanced later to capture email from people information
    return undefined;
  }

  /**
   * Map profession type ID
   */
  private mapProfessionTypeId(professionType: string): string | undefined {
    if (!professionType) {
      console.log('🔍 No profession type provided, returning undefined');
      return undefined;
    }

    console.log('🔍 Mapping profession type ID:', {
      professionType,
      availableProfessionTypes: this.professionTypes.map(pt => ({ id: pt.id, value: pt.value, label: pt.label }))
    });

    const professionTypeObj = this.professionTypes.find(pt => pt.value === professionType);

    if (professionTypeObj) {
      console.log('✅ Found profession type:', professionTypeObj);
      return professionTypeObj.id;
    } else {
      console.log('❌ Profession type not found, returning undefined to avoid foreign key error');
      return undefined;
    }
  }

  /**
   * Map connect with ID from connect with name
   */
  private mapConnectWithId(connectWithName: string): string | undefined {
    if (!connectWithName) {
      console.log('🔍 No connect with name provided, returning undefined');
      return undefined;
    }

    console.log('🔍 Mapping connect with ID:', {
      connectWithName,
      availableConnectWithOptions: this.connectWithList.map(cw => ({ id: cw.id, name: cw.name }))
    });

    const connectWithOption = this.connectWithList.find(cw => cw.name === connectWithName);
    if (connectWithOption) {
      console.log('✅ Mapped connect with:', {
        name: connectWithName,
        id: connectWithOption.id
      });
      return connectWithOption.id;
    }

    console.log('⚠️ Connect with option not found:', connectWithName);
    return undefined;
  }

  /**
   * Map person's connect with name to UUID for people_information array
   */
  private mapPersonConnectWithId(connectWithName: string): string | null {
    if (!connectWithName) {
      console.log('🔍 No person connect with name provided, returning null');
      return null;
    }

    console.log('🔍 Mapping person connect with ID:', {
      connectWithName,
      availableConnectWithOptions: this.connectWithList.map(cw => ({ id: cw.id, name: cw.name }))
    });

    const connectWithOption = this.connectWithList.find(cw => cw.name === connectWithName);
    if (connectWithOption) {
      console.log('✅ Mapped person connect with:', {
        name: connectWithName,
        id: connectWithOption.id
      });
      return connectWithOption.id;
    }

    console.log('⚠️ Person connect with option not found:', connectWithName);
    return null;
  }

  /**
   * Map constitution ID
   */
  private mapConstitutionId(constitution: string): string | undefined {
    if (!constitution) {
      console.log('🔍 No constitution provided, returning undefined');
      return undefined;
    }

    console.log('🔍 Mapping constitution ID:', {
      constitution,
      availableConstitutions: this.constitutions.map((c: any) => ({ id: c.id, name: c.name }))
    });

    const constitutionObj = this.constitutions.find((c: any) => c.name === constitution);

    if (constitutionObj) {
      console.log('✅ Found constitution:', constitutionObj);
      return constitutionObj.id;
    } else {
      console.log('❌ Constitution not found, returning undefined to avoid foreign key error');
      return undefined;
    }
  }

  /**
   * Map board affiliation ID
   */
  private mapBoardAffiliationId(boardAffiliation: string): string | undefined {
    if (!boardAffiliation) {
      console.log('🔍 No board affiliation provided, returning undefined');
      return undefined;
    }

    console.log('🔍 Mapping board affiliation ID:', {
      boardAffiliation,
      availableBoardAffiliations: Array.from(this.boardAffiliationMap.entries())
    });

    const boardAffiliationId = this.boardAffiliationMap.get(boardAffiliation);

    if (boardAffiliationId) {
      console.log('✅ Found board affiliation ID:', boardAffiliationId);
      return boardAffiliationId;
    } else {
      console.log('❌ Board affiliation not found, returning undefined to avoid foreign key error');
      return undefined;
    }
  }

  /**
   * Get sub location for API based on source type
   */
  private getSubLocationForAPI(): string | undefined {
    // Check Lead Category first (this takes priority)
    if (this.isLeadCategorySelected('Associate')) {
      return this.leadSubLocationCategory || this.associateSubLocationCategory || undefined;
    }

    // Then check Source type for backward compatibility
    if (this.isSourceSelected('Self')) {
      return this.leadSublocation || undefined;
    } else if (this.isSourceSelected('Associate')) {
      return this.associateLeadSublocation || this.associateSubLocation || undefined;
    }
    return undefined;
  }













  /**
   * Get filtered sub product types based on selected product type
   * Note: This method is now deprecated in favor of the API-based approach in onProductTypeChange()
   */
  getFilteredSubProductTypes(): SubProductType[] {
    if (!this.selectedProductType || !this.subProductTypesFromApi.length) {
      return [];
    }

    // Since selectedProductType now contains the ID, use it directly
    return this.subProductTypesFromApi.filter(
      subType => subType.product_type_id === this.selectedProductType
    );
  }



  /**
   * Handle product type selection change
   */
  onProductTypeChange(): void {
    console.log('🔄 onProductTypeChange triggered');

    // Reset sub product type selection when product type changes
    this.selectedProductSubType = '';
    this.availableSubTypes = [];

    // Reset component visibility when product type changes
    this.resetComponentVisibility();

    // Since selectedProductType now contains the ID, use it directly
    if (this.selectedProductType) {
      // Set loading state
      this.subProductTypesLoading = true;

      // Fetch sub product types using the correct service
      console.log('🔍 Loading sub product types for product type ID:', this.selectedProductType);
      this.subProductTypeService.getSubProductTypesByProductTypeId(this.selectedProductType).pipe(
        finalize(() => {
          this.subProductTypesLoading = false;
          // Force change detection after loading is complete
          this.cdr.detectChanges();
        })
      ).subscribe({
        next: (subTypes) => {
          console.log('✅ Raw sub product types response:', subTypes);
          console.log('✅ Sub product types type:', typeof subTypes);
          console.log('✅ Is array:', Array.isArray(subTypes));

          if (Array.isArray(subTypes) && subTypes.length > 0) {
            console.log('✅ First sub product type sample:', subTypes[0]);

            // Update available sub types from API response
            this.availableSubTypes = subTypes.map(subType => ({
              value: subType.id,
              label: subType.name,
              code: subType.code // Add code for easier form logic
            }));

            // Find the product type name for logging
            const selectedProductType = this.productTypesFromApi.find(type => type.id === this.selectedProductType);
            console.log('✅ Mapped available sub types:', this.availableSubTypes);
            console.log('✅ Loaded', subTypes.length, 'sub product types for product type:', selectedProductType?.name);
          } else {
            console.warn('⚠️ No sub product types found or invalid response format');
            this.availableSubTypes = [];
          }

          // Force change detection after data is loaded
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('❌ Error loading sub product types:', error);
          console.error('❌ Error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            error: error.error,
            url: error.url
          });

          // Reset available sub types on error
          this.availableSubTypes = [];

          // Show user-friendly error message
          this.popupDesignService.showError({
            title: 'Loading Error',
            message: 'Failed to load sub product types. Please try again or contact support if the issue persists.',
            confirmText: 'OK'
          });

          // Force change detection even on error
          this.cdr.detectChanges();
        }
      });
    } else {
      // Force change detection when no product type is selected
      this.cdr.detectChanges();
    }
  }

  /**
   * Handle source selection change
   */
  onSourceChange(): void {
    console.log('🔄 Source changed:', this.selectedSource);

    // Update the ProductDataService with the lead source
    if (this.selectedSource) {
      const selectedSource = this.sourcesFromApi.find(src => src.id === this.selectedSource);
      if (selectedSource) {
        this.productDataService.setLeadSource(selectedSource.name);
      }
    } else {
      this.productDataService.setLeadSource('');
    }
  }

  /**
   * Component cleanup
   */
  override ngOnDestroy(): void {
    if (this.salesUpdateSubscription) {
      this.salesUpdateSubscription.unsubscribe();
    }
  }

  // TrackBy functions for performance optimization
  trackBySaleId(index: number, sale: any): any {
    return sale.id || index;
  }

  trackByLocationName(index: number, location: any): any {
    return location.name || index;
  }

  trackByAssociateId(index: number, associate: any): any {
    return associate.id || index;
  }

  trackByProfessionName(index: number, profession: any): any {
    return profession.name || index;
  }

  trackByEmployeeValue(index: number, employee: any): any {
    return employee.value || index;
  }

  trackByConnectWithName(index: number, connectWith: any): any {
    return connectWith.name || index;
  }
}
