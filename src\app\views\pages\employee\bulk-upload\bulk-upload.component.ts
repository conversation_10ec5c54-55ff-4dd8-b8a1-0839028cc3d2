import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { EmployeeService, BulkUploadResponse } from '../../../../core/services/employee.service';
import { TemplateDownloadService } from '../../../../core/services/template-download.service';
import Swal from 'sweetalert2';



@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    FeatherIconDirective
  ],
  templateUrl: './bulk-upload.component.html',
  styleUrls: ['./bulk-upload.component.scss']
})
export class BulkUploadComponent implements OnInit {
  selectedFile: File | null = null;
  uploading = false;
  uploadResult: BulkUploadResponse | null = null;
  error: string | null = null;
  dragOver = false;
  hasValidationErrors = false;
  downloadingErrors = false;

  constructor(
    private employeeService: EmployeeService,
    private templateDownloadService: TemplateDownloadService
  ) {}

  ngOnInit(): void {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFileSelection(input.files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;

    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      this.handleFileSelection(event.dataTransfer.files[0]);
    }
  }

  private handleFileSelection(file: File): void {
    // Validate file type - accept Excel and CSV files
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
      'application/csv' // .csv (alternative MIME type)
    ];

    // Also check file extension as a fallback
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!allowedTypes.includes(file.type) && !hasValidExtension) {
      this.error = 'Please select a valid Excel (.xlsx, .xls) or CSV (.csv) file';
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.error = 'File size must be less than 10MB';
      return;
    }

    // Validate file is not empty
    if (file.size === 0) {
      this.error = 'Please select a non-empty file';
      return;
    }

    this.selectedFile = file;
    this.error = null;
    this.uploadResult = null;
    this.hasValidationErrors = false;
    this.downloadingErrors = false;

    // Clear any previous validation errors
    this.clearValidationErrors();
  }

  uploadFile(): void {
    // Safety check - if somehow the button is clicked while uploading, ignore
    if (this.uploading) {
      console.log('⚠️ Upload already in progress, ignoring click');
      return;
    }

    if (!this.selectedFile) {
      this.error = 'Please select a file first';
      return;
    }

    console.log('Starting file upload...', {
      fileName: this.selectedFile.name,
      fileSize: this.selectedFile.size,
      fileType: this.selectedFile.type
    });

    // Set uploading state to disable button and clear any previous errors
    this.uploading = true;
    this.error = null;
    this.uploadResult = null;
    this.hasValidationErrors = false;
    this.downloadingErrors = false;
    console.log('🔄 Upload started - Button disabled (uploading = true)');

    this.employeeService.bulkUploadEmployees(this.selectedFile).subscribe({
      next: (response) => {
        console.log('✅ 200 Status - Upload API Response Received');
        console.log('Response:', response);

        // Always reset uploading state on success
        this.uploading = false;
        console.log('✅ Upload successful - Button re-enabled (uploading = false)');

        // Reset form on successful upload
        this.selectedFile = null;
        this.clearValidationErrors();
        this.resetFileInput();

        // Show success alert immediately after 200 status
        console.log('🎉 Showing success alert after 200 status');
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Employees uploaded successfully!',
          timer: 3000,
          showConfirmButton: false
        }).then(() => {
          console.log('✅ Success alert completed');
        });
      },
      error: (err) => {
        console.error('Upload error details:', err);
        console.error('Error response structure:', JSON.stringify(err, null, 2));
        console.log('🔍 Error status:', err.status);
        console.log('🔍 Error message:', err.error?.error || err.error?.message || err.message);

        // CRITICAL: Always reset uploading state on error to re-enable the upload button
        this.resetUploadState();
        console.log('❌ Upload failed - Button re-enabled');

        // Check for validation errors FIRST before setting error message
        this.checkForValidationErrors(err);

        // Set error message for display
        this.setErrorMessage(err);

        // Handle bulk upload validation errors with custom display
        this.handleBulkUploadError(err);

        // Final safety check - ensure button is enabled
        this.resetUploadState();
        console.log('🔍 Final uploading state after error handling:', this.uploading);
        console.log('🔍 Final hasValidationErrors state:', this.hasValidationErrors);
      }
    });
  }

  downloadTemplate(): void {
    console.log('📥 Downloading employee bulk upload template...');

    // Use the template download service with promise handling
    this.templateDownloadService.downloadEmployeeBulkUploadTemplate().then(() => {
      // Show success message only after successful download
      Swal.fire({
        icon: 'success',
        title: 'Template Downloaded',
        text: 'Employee bulk upload template has been downloaded successfully.',
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
      });
    }).catch((error) => {
      console.error('Template download failed:', error);
      Swal.fire({
        icon: 'error',
        title: 'Download Failed',
        text: 'Failed to download template. Please check if the file exists and try again.',
        confirmButtonText: 'OK'
      });
    });
  }

  downloadErrorReport(): void {
    if (!this.selectedFile) {
      console.error('No file selected for error report download');
      return;
    }

    console.log('📥 Downloading bulk upload error report...');
    this.downloadingErrors = true;

    this.employeeService.downloadBulkUploadErrorReport(this.selectedFile).subscribe({
      next: (blob) => {
        console.log('✅ Error report downloaded successfully');

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `employee_upload_errors_${new Date().toISOString().split('T')[0]}.xlsx`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);

        this.downloadingErrors = false;

        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Error Report Downloaded',
          text: 'The error report has been downloaded successfully.',
          timer: 3000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end'
        });
      },
      error: (error) => {
        console.error('Error downloading error report:', error);
        this.downloadingErrors = false;

        Swal.fire({
          icon: 'error',
          title: 'Download Failed',
          text: 'Failed to download error report. Please try again.',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  clearFile(): void {
    this.selectedFile = null;
    this.error = null;
    this.uploadResult = null;
    this.uploading = false; // Ensure uploading state is reset when clearing file
    this.hasValidationErrors = false;
    this.downloadingErrors = false;
    this.clearValidationErrors();
    this.resetFileInput();
  }

  // Clear validation errors (kept for compatibility with existing calls)
  private clearValidationErrors(): void {
    // This method is kept to avoid breaking existing code that calls it
  }

  private resetFileInput(): void {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  getFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Set error message for display in the UI
  private setErrorMessage(err: any): void {
    // Extract a user-friendly error message
    let errorMessage = 'Upload failed. Please check your file and try again.';

    if (err.error && err.error.error) {
      errorMessage = this.getFormattedErrorMessage(err.error.error);
    } else if (err.error && err.error.message) {
      errorMessage = err.error.message;
    } else if (err.message) {
      errorMessage = err.message;
    }

    // Set the error for display in the template
    this.error = errorMessage;
  }

  // Reset upload state to ensure button is re-enabled
  private resetUploadState(): void {
    this.uploading = false;
    console.log('🔄 Upload state reset - Button should be enabled (uploading = false)');
    console.log('🔍 Current state - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile);
  }

  // Check if upload button should be disabled
  isUploadButtonDisabled(): boolean {
    const disabled = this.uploading || !this.selectedFile;
    console.log('🔍 Button disabled check - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile, 'disabled:', disabled);
    return disabled;
  }

  // Force enable button for testing (can be called from browser console)
  forceEnableButton(): void {
    this.uploading = false;
    console.log('🔧 Button force enabled - uploading set to false');
    console.log('🔍 Current state - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile);
  }

  // Test method to trigger validation error state (can be called from browser console)
  testValidationError(): void {
    this.hasValidationErrors = true;
    this.error = 'Test validation error - Download Error Report button should now be visible';
    console.log('🧪 Test validation error triggered - hasValidationErrors:', this.hasValidationErrors);
  }

  // Check for validation errors in the error response
  private checkForValidationErrors(err: any): void {
    console.log('🔍 Checking for validation errors in response...');

    // Check error message content for validation keywords
    const errorMessage = (err.error?.error || err.error?.message || err.message || '').toLowerCase();

    const validationKeywords = [
      'already registered',
      'already exists',
      'duplicate',
      'invalid',
      'validation',
      'required',
      'format'
    ];

    const hasValidationKeyword = validationKeywords.some(keyword => errorMessage.includes(keyword));

    if (hasValidationKeyword || err.status === 400) {
      console.log('✅ Validation error detected! Setting hasValidationErrors = true');
      this.hasValidationErrors = true;
    } else {
      console.log('❌ No validation error detected');
      this.hasValidationErrors = false;
    }

    console.log('🔍 Error message:', errorMessage);
    console.log('🔍 Has validation keyword:', hasValidationKeyword);
    console.log('🔍 Status code:', err.status);
    console.log('🔍 Final hasValidationErrors:', this.hasValidationErrors);
  }



  // Handle bulk upload errors with custom display
  private handleBulkUploadError(err: any): void {
    console.log('🔍 Processing bulk upload error:', err);
    console.log('🔍 Error structure:', JSON.stringify(err, null, 2));
    console.log('🔍 Uploading state at start of error handler:', this.uploading);

    // CRITICAL: Ensure uploading state is always reset to re-enable the upload button
    // This is a safety net in case the main error handler didn't reset it
    this.resetUploadState();

    // Handle 403 Authorization errors specifically
    if (err.status === 403) {
      console.log('🚫 Handling 403 Authorization error');
      this.show403AuthorizationError(err);
      return;
    }

    // Handle 401 Authentication errors
    if (err.status === 401) {
      console.log('🔑 Handling 401 Authentication error');
      this.show401AuthenticationError(err);
      return;
    }

    // Check for the specific API response format: { success: false, error: "..." }
    if (err.error && err.error.success === false && err.error.error) {
      const errorMessage = err.error.error;

      // Check for validation-related errors that should show download button
      if (errorMessage.includes('already registered') ||
          errorMessage.includes('already exists') ||
          errorMessage.includes('duplicate') ||
          errorMessage.includes('invalid') ||
          errorMessage.includes('validation')) {
        console.log('✅ Found validation error in success=false format');
        this.hasValidationErrors = true;
        // Don't show popup, let it fall through to show error in main UI
      }
      // Check for "Holiday already exists in database" error (specific case)
      else if (errorMessage.includes('Holiday already exists in database for this date')) {
        Swal.fire({
          icon: 'warning',
          title: 'Upload Error',
          text: 'Holiday already exists !',
          confirmButtonText: 'OK',
          confirmButtonColor: '#3085d6'
        });
        return;
      }
    }

    // Check if this is a bulk upload validation error - Multiple formats
    console.log('🔍 Checking for validation errors...');

    // Format 1: err.error.error with meta.error_details
    if (err.error && err.error.error && err.error.meta && err.error.meta.error_details) {
      console.log('✅ Found validation errors - Format 1 (meta.error_details)');
      this.hasValidationErrors = true;
      this.showBulkUploadValidationErrors(err.error);
      return;
    }

    // Format 2: err.error.errors array
    if (err.error && err.error.errors && Array.isArray(err.error.errors)) {
      console.log('✅ Found validation errors - Format 2 (errors array)');
      this.hasValidationErrors = true;
      this.showBulkUploadValidationErrors(err.error);
      return;
    }

    // Format 3: Check for any validation-related error messages
    if (err.error && err.error.error && typeof err.error.error === 'string') {
      const errorMessage = err.error.error.toLowerCase();
      if (errorMessage.includes('validation') ||
          errorMessage.includes('invalid') ||
          errorMessage.includes('duplicate') ||
          errorMessage.includes('required') ||
          errorMessage.includes('format')) {
        console.log('✅ Found validation errors - Format 3 (validation keywords)');
        this.hasValidationErrors = true;
        this.showBulkUploadValidationErrors(err.error);
        return;
      }
    }

    // Format 4: Check status code 400 (Bad Request) which often indicates validation errors
    if (err.status === 400) {
      console.log('✅ Found validation errors - Format 4 (400 status code)');
      this.hasValidationErrors = true;
      this.showBulkUploadValidationErrors(err.error || err);
      return;
    }

    // Fallback to generic error display
    this.showGenericUploadError(err);
  }

  // Show detailed validation errors for bulk upload
  private showBulkUploadValidationErrors(errorData: any): void {
    console.log('📋 Showing bulk upload validation errors:', errorData);
    console.log('🔍 Uploading state in validation error handler:', this.uploading);
    console.log('🔍 hasValidationErrors flag:', this.hasValidationErrors);

    const errorDetails = errorData.meta?.error_details || errorData;
    const errors = errorDetails.errors || [];

    console.log('📊 Processing errors:', errors);

    // Re-enable upload button immediately when validation errors occur
    this.uploading = false;
    console.log('✅ Upload button re-enabled after validation errors (uploading = false)');

    // Count total errors
    const totalErrors = errors.length;

    if (totalErrors > 0) {
      // Don't show popup for validation errors - let the main UI handle it with download button
      console.log('🔍 Validation errors found, showing in main UI instead of popup');
      // The error message will be displayed in the main UI where the download button can appear
    } else {
      // Fallback if no specific errors found - still show in main UI if validation errors detected
      if (!this.hasValidationErrors) {
        Swal.fire({
          icon: 'error',
          title: 'Upload Failed',
          text: 'There was an issue with your upload. Please check your file and try again.',
          confirmButtonText: 'OK'
        });
      }
    }
  }



  // Show 403 Authorization error
  private show403AuthorizationError(err: any): void {
    console.log('🚫 403 Authorization Error:', err);

    Swal.fire({
      icon: 'warning',
      title: 'Access Denied',
      text: 'You do not have permission to upload employee data.',
      confirmButtonText: 'Close',
      confirmButtonColor: '#6c757d'
    });
  }

  // Show 401 Authentication error
  private show401AuthenticationError(err: any): void {
    console.log('🔑 401 Authentication Error:', err);

    Swal.fire({
      icon: 'warning',
      title: 'Session Expired',
      html: `
        <div style="text-align: left;">
          <p><strong>Your session has expired or you are not authenticated.</strong></p>
          <br>
          <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;">
            <strong>🔑 Authentication Required:</strong><br>
            • Your login session has expired<br>
            • You need to log in again to continue<br>
            • Your authentication token is invalid
          </div>
          <br>
          <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #17a2b8;">
            <strong>💡 Next Steps:</strong><br>
            • Click "Login Again" to be redirected to the login page<br>
            • Enter your credentials to get a new session<br>
            • Try the upload again after logging in
          </div>
        </div>
      `,
      width: '500px',
      confirmButtonText: 'Login Again',
      confirmButtonColor: '#dc3545',
      showCancelButton: true,
      cancelButtonText: 'Close',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {
        // Redirect to login page or trigger logout
        window.location.href = '/auth/login';
      }
    });
  }

  // Show generic upload error
  private showGenericUploadError(err: any): void {
    const errorMessage = err.error?.message || err.error?.error || err.message || 'Upload failed. Please try again.';

    // If we already detected validation errors, don't show popup - let the main UI handle it
    if (this.hasValidationErrors) {
      console.log('🔍 Validation errors detected, skipping popup and showing in main UI');
      return;
    }

    // Handle other HTTP status codes
    let title = 'Upload Error';
    let icon: 'error' | 'warning' = 'error';

    switch (err.status) {
      case 400:
        title = 'Invalid Request';
        icon = 'warning';
        break;
      case 413:
        title = 'File Too Large';
        icon = 'warning';
        break;
      case 415:
        title = 'Unsupported File Type';
        icon = 'warning';
        break;
      case 500:
        title = 'Server Error';
        break;
      case 502:
      case 503:
      case 504:
        title = 'Service Unavailable';
        break;
    }

    Swal.fire({
      icon: icon,
      title: title,
      text: errorMessage,
      confirmButtonText: 'OK',
      confirmButtonColor: '#dc3545'
    });
  }

  // Format error messages to be more user-friendly
  getFormattedErrorMessage(errorMessage: string): string {
    if (!errorMessage) return 'Unknown error';

    // Handle holiday/activity specific errors
    if (errorMessage.toLowerCase().includes('holiday already exists in database for this date')) {
      return 'Holiday already exists for this date. Please choose a different date.';
    }

    if (errorMessage.toLowerCase().includes('duplicate holiday dates') ||
        errorMessage.toLowerCase().includes('holiday already exists')) {
      return 'Holiday already exists for this date. Please choose a different date.';
    }

    // Handle duplicate email errors
    if (errorMessage.toLowerCase().includes('already exists') && errorMessage.toLowerCase().includes('email')) {
      const emailMatch = errorMessage.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      const email = emailMatch ? emailMatch[1] : 'this email';
      return `Email address ${email} is already registered in the system`;
    }

    // Handle IFSC code validation errors
    if (errorMessage.includes('IFSC code must be in valid format')) {
      return 'Invalid IFSC Code: Must be exactly 4 CAPITAL letters + 0 + 6 alphanumeric characters (e.g., HDFC0000001, SBIN0000002)';
    }

    // Handle date parsing errors
    if (errorMessage.includes('Input should be a valid date') || errorMessage.includes('date_from_datetime_parsing')) {
      return 'Invalid Date Format: Use YYYY-MM-DD format (e.g., 1990-01-15)';
    }

    // Handle email validation errors
    if (errorMessage.includes('value is not a valid email address')) {
      return 'Invalid Email: Please provide a valid email address';
    }

    // Handle phone number validation errors
    if (errorMessage.includes('phone') && errorMessage.includes('validation error')) {
      return 'Invalid Phone Number: Please provide a valid phone number';
    }

    // Handle PAN validation errors
    if (errorMessage.includes('pan_no') || errorMessage.includes('PAN')) {
      return 'Invalid PAN Number: Must be in format **********';
    }

    // Handle Aadhar validation errors
    if (errorMessage.includes('aadhar') || errorMessage.includes('Aadhar')) {
      return 'Invalid Aadhar Number: Must be 12 digits';
    }

    // Handle required field errors
    if (errorMessage.includes('Field required') || errorMessage.includes('This field is required')) {
      return 'Required Field Missing: Please fill all mandatory fields';
    }

    // Handle general validation errors
    if (errorMessage.includes('validation error')) {
      // Extract the field name if possible
      const fieldMatch = errorMessage.match(/(\w+)\s*\n\s*(.+)/);
      if (fieldMatch) {
        return `${fieldMatch[1]}: ${fieldMatch[2]}`;
      }
    }

    // Clean up the message by removing technical prefixes
    let cleanMessage = errorMessage;
    const prefixesToRemove = [
      'Employee with office email',
      'Employee with',
      'Validation error:',
      'Error:',
      'Exception:'
    ];

    prefixesToRemove.forEach(prefix => {
      if (cleanMessage.startsWith(prefix)) {
        cleanMessage = cleanMessage.substring(prefix.length).trim();
      }
    });

    // Capitalize first letter
    if (cleanMessage && cleanMessage.length > 0) {
      cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
    }

    return cleanMessage || errorMessage;
  }


}
