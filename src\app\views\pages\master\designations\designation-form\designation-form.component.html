<!-- Designation Form Modal -->
<div class="modal-header">
  <h5 class="modal-title">
    <i class="feather icon-award me-2"></i>
    {{ getModalTitle() }}
  </h5>
  <button type="button" class="btn-close" (click)="cancel()" aria-label="Close"></button>
</div>

<div class="modal-body">
  <!-- Error <PERSON>ert -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="feather icon-alert-circle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null" aria-label="Close"></button>
  </div>

  <!-- Designation Form -->
  <form [formGroup]="designationForm" (ngSubmit)="save()">
    
    <div class="row">
      <!-- Designation Name -->
      <div class="col-md-8 mb-3">
        <label for="designationName" class="form-label">
          Designation Name <span class="text-danger">*</span>
        </label>
        <input 
          type="text" 
          id="designationName"
          class="form-control"
          [class.is-invalid]="hasError('name')"
          formControlName="name"
          placeholder="Enter designation name"
          maxlength="100">
        
        <div *ngIf="hasError('name')" class="invalid-feedback">
          {{ getErrorMessage('name') }}
        </div>
        
        <div class="form-text">
          Enter a unique name for the designation (2-100 characters)
        </div>
      </div>

      <!-- Level -->
      <div class="col-md-4 mb-3">
        <label for="designationLevel" class="form-label">
          Level <span class="text-danger">*</span>
        </label>
        <select 
          id="designationLevel"
          class="form-select"
          [class.is-invalid]="hasError('level')"
          formControlName="level">
          
          <option *ngFor="let option of levelOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        
        <div *ngIf="hasError('level')" class="invalid-feedback">
          {{ getErrorMessage('level') }}
        </div>
        
        <div class="form-text">
          Select the organizational level
        </div>
      </div>
    </div>

    <!-- Description -->
    <div class="mb-3">
      <label for="designationDescription" class="form-label">
        Description
      </label>
      <textarea 
        id="designationDescription"
        class="form-control"
        [class.is-invalid]="hasError('description')"
        formControlName="description"
        placeholder="Enter designation description (optional)"
        rows="3"
        maxlength="500"></textarea>
      
      <div *ngIf="hasError('description')" class="invalid-feedback">
        {{ getErrorMessage('description') }}
      </div>
      
      <div class="form-text">
        Optional description of the designation's responsibilities and requirements
      </div>
    </div>

    <!-- Department -->
    <div class="mb-3">
      <label for="designationDepartment" class="form-label">
        Department
      </label>
      <select 
        id="designationDepartment"
        class="form-select"
        [class.is-invalid]="hasError('department_id')"
        formControlName="department_id">
        
        <option value="">No Department (General)</option>
        <option *ngFor="let dept of departments" [value]="dept.id">
          {{ dept.name }}
        </option>
      </select>
      
      <div *ngIf="hasError('department_id')" class="invalid-feedback">
        {{ getErrorMessage('department_id') }}
      </div>
      
      <div class="form-text">
        Select the department this designation belongs to (optional)
      </div>
    </div>

    <!-- Salary Range -->
    <div class="row">
      <div class="col-md-6 mb-3">
        <label for="salaryMin" class="form-label">
          Minimum Salary (₹)
        </label>
        <input 
          type="number" 
          id="salaryMin"
          class="form-control"
          [class.is-invalid]="hasError('salary_range_min') || hasSalaryRangeError()"
          formControlName="salary_range_min"
          placeholder="Enter minimum salary"
          min="0"
          max="10000000">
        
        <div *ngIf="hasError('salary_range_min')" class="invalid-feedback">
          {{ getErrorMessage('salary_range_min') }}
        </div>
      </div>

      <div class="col-md-6 mb-3">
        <label for="salaryMax" class="form-label">
          Maximum Salary (₹)
        </label>
        <input 
          type="number" 
          id="salaryMax"
          class="form-control"
          [class.is-invalid]="hasError('salary_range_max') || hasSalaryRangeError()"
          formControlName="salary_range_max"
          placeholder="Enter maximum salary"
          min="0"
          max="10000000">
        
        <div *ngIf="hasError('salary_range_max')" class="invalid-feedback">
          {{ getErrorMessage('salary_range_max') }}
        </div>
      </div>
    </div>

    <!-- Salary Range Error -->
    <div *ngIf="hasSalaryRangeError()" class="alert alert-warning mb-3">
      <i class="feather icon-alert-triangle me-2"></i>
      Maximum salary must be greater than minimum salary
    </div>

    <!-- Salary Range Preview -->
    <div *ngIf="designationForm.get('salary_range_min')?.value || designationForm.get('salary_range_max')?.value" class="mb-3">
      <div class="card bg-light">
        <div class="card-body py-2">
          <small class="text-muted">
            <i class="feather icon-info me-1"></i>
            <strong>Salary Range Preview:</strong> 
            {{ getPreviewSalaryRange() }}
          </small>
        </div>
      </div>
    </div>

    <!-- Status -->
    <div class="mb-3">
      <label class="form-label">
        Status <span class="text-danger">*</span>
      </label>
      
      <div class="form-check form-switch">
        <input 
          class="form-check-input" 
          type="checkbox" 
          id="designationStatus"
          formControlName="is_active">
        <label class="form-check-label" for="designationStatus">
          <span *ngIf="designationForm.get('is_active')?.value" class="text-success">
            <i class="feather icon-check-circle me-1"></i>
            Active
          </span>
          <span *ngIf="!designationForm.get('is_active')?.value" class="text-muted">
            <i class="feather icon-pause-circle me-1"></i>
            Inactive
          </span>
        </label>
      </div>
      
      <div class="form-text">
        Active designations are available for employee assignment and appear in dropdowns
      </div>
    </div>

    <!-- Form Summary (for edit mode) -->
    <div *ngIf="isEditMode && designation" class="mb-3">
      <div class="card border-info">
        <div class="card-header bg-info text-white py-2">
          <h6 class="mb-0">
            <i class="feather icon-info me-1"></i>
            Current Designation Information
          </h6>
        </div>
        <div class="card-body py-2">
          <div class="row">
            <div class="col-md-4">
              <small class="text-muted">Current Level:</small>
              <div class="fw-bold">{{ getLevelName(designation.level) }}</div>
            </div>
            <div class="col-md-4">
              <small class="text-muted">Employee Count:</small>
              <div class="fw-bold">{{ designation.employee_count || 0 }} employees</div>
            </div>
            <div class="col-md-4">
              <small class="text-muted">Department:</small>
              <div class="fw-bold">
                {{ designation.department_name || 'No Department' }}
              </div>
            </div>
            <div class="col-12 mt-2" *ngIf="designation.created_at">
              <small class="text-muted">Created:</small>
              <div class="fw-bold">{{ designation.created_at | date:'medium' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Validation Summary -->
    <div *ngIf="designationForm.invalid && (designationForm.dirty || designationForm.touched)" 
         class="alert alert-warning">
      <h6 class="alert-heading">
        <i class="feather icon-alert-triangle me-1"></i>
        Please fix the following issues:
      </h6>
      <ul class="mb-0">
        <li *ngIf="hasError('name')">{{ getErrorMessage('name') }}</li>
        <li *ngIf="hasError('description')">{{ getErrorMessage('description') }}</li>
        <li *ngIf="hasError('level')">{{ getErrorMessage('level') }}</li>
        <li *ngIf="hasError('department_id')">{{ getErrorMessage('department_id') }}</li>
        <li *ngIf="hasError('salary_range_min')">{{ getErrorMessage('salary_range_min') }}</li>
        <li *ngIf="hasError('salary_range_max')">{{ getErrorMessage('salary_range_max') }}</li>
        <li *ngIf="hasSalaryRangeError()">Maximum salary must be greater than minimum salary</li>
        <li *ngIf="hasError('is_active')">{{ getErrorMessage('is_active') }}</li>
      </ul>
    </div>

  </form>
</div>

<div class="modal-footer">
  <div class="d-flex justify-content-between w-100">
    <!-- Reset Button (for edit mode) -->
    <div>
      <button 
        *ngIf="isEditMode" 
        type="button" 
        class="btn btn-outline-warning"
        (click)="reset()"
        [disabled]="saving">
        <i class="feather icon-refresh-cw me-1"></i>
        Reset
      </button>
    </div>
    
    <!-- Action Buttons -->
    <div class="d-flex gap-2">
      <button 
        type="button" 
        class="btn btn-secondary" 
        (click)="cancel()"
        [disabled]="saving">
        <i class="feather icon-x me-1"></i>
        Cancel
      </button>
      
      <button 
        type="button" 
        class="btn btn-primary" 
        (click)="save()"
        [disabled]="designationForm.invalid || saving">
        
        <!-- Loading Spinner -->
        <span *ngIf="saving" class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </span>
        
        <!-- Save Icon -->
        <i *ngIf="!saving" class="feather icon-save me-1"></i>
        
        {{ getSaveButtonText() }}
      </button>
    </div>
  </div>
</div>
