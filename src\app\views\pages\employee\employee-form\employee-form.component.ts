import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { Employee, EmployeeService } from '../../../../core/services/employee.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-employee-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  templateUrl: './employee-form.component.html',
  styleUrls: ['./employee-form.component.scss']
})
export class EmployeeFormComponent implements OnInit {
  employeeForm: FormGroup;
  isEditMode: boolean = false;
  employeeId: number | null = null;
  pageTitle: string = 'Create Employee';
  submitBtnText: string = 'Create Employee';

  loading: boolean = false;
  submitLoading: boolean = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  // For role selection
  roles: any[] = [];
  rolesLoading: boolean = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private employeeService: EmployeeService
  ) {
    // Initialize form
    this.employeeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.pattern(/^\+?[0-9\s-()]{10,15}$/)]],
      department: [''],
      position: [''],
      joining_date: [''],
      salary: [null, [Validators.min(0)]],
      user_active: [true],
      role: ['employee']
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.employeeId = +params['id'];
        this.pageTitle = 'Edit Employee';
        this.submitBtnText = 'Update Employee';
        this.loadEmployeeData(this.employeeId);
      }
    });

    // Load available roles
    this.loadRoles();
  }

  loadEmployeeData(id: number): void {
    this.loading = true;
    this.errorMessage = null;

    this.employeeService.getEmployee(id).subscribe({
      next: (employee) => {
        // Format joining date for the form if it exists
        if (employee.joining_date) {
          const date = new Date(employee.joining_date);
          employee.joining_date = date.toISOString().split('T')[0] as any;
        }

        this.employeeForm.patchValue(employee);
        this.loading = false;
      },
      error: (err) => {
        this.errorMessage = 'Failed to load employee data. Please try again.';
        this.loading = false;
        console.error('Error loading employee:', err);
      }
    });
  }

  loadRoles(): void {
    this.rolesLoading = true;

    this.employeeService.getRoles().subscribe({
      next: (roles) => {
        this.roles = roles;
        this.rolesLoading = false;
      },
      error: (err) => {
        console.error('Error loading roles - roles are required:', err);
        this.roles = []; // No fallback roles
        this.rolesLoading = false;
        // Show error message to user
        alert('Unable to load roles. Please contact your administrator or try again later.');
      }
    });
  }

  onSubmit(): void {
    if (this.employeeForm.invalid) {
      // Mark all fields as touched to show validation errors
      Object.keys(this.employeeForm.controls).forEach(key => {
        this.employeeForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.submitLoading = true;
    this.errorMessage = null;
    this.successMessage = null;

    const employeeData = this.employeeForm.value;

    if (this.isEditMode && this.employeeId) {
      // Update existing employee
      this.employeeService.updateEmployee(this.employeeId, employeeData).subscribe({
        next: () => {
          this.submitLoading = false;

          // Show animated success notification
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Employee updated successfully. Redirecting to employee list with updated data.',
            timer: 2000,
            showConfirmButton: false
          }).then(() => {
            // Navigate back to employee list and reload to show fresh data
            this.router.navigate(['/employees']).then(() => {
              console.log('🔄 Reloading page to show updated employee data...');
              window.location.reload();
            });
          });
        },
        error: (err) => {
          this.submitLoading = false;
          console.error('Error updating employee:', err);

          // Show animated error notification
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: err.error?.detail || 'Failed to update employee. Please try again.',
            confirmButtonText: 'OK'
          });
        }
      });
    } else {
      // Create new employee
      this.employeeService.createEmployee(employeeData).subscribe({
        next: () => {
          this.submitLoading = false;

          // Show animated success notification
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Employee created successfully.',
            timer: 2000,
            showConfirmButton: false
          }).then(() => {
            // Navigate back to employee list
            this.router.navigate(['/employees']);
          });
        },
        error: (err) => {
          this.submitLoading = false;
          console.error('Error creating employee:', err);

          // Show animated error notification
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: err.error?.detail || 'Failed to create employee. Please try again.',
            confirmButtonText: 'OK'
          });
        }
      });
    }
  }

  // Helper method to check if a field is invalid and touched
  isInvalid(controlName: string): boolean {
    const control = this.employeeForm.get(controlName);
    return control !== null && control.invalid && (control.dirty || control.touched);
  }

  // Helper to get error message for a field
  getErrorMessage(controlName: string): string {
    const control = this.employeeForm.get(controlName);

    if (!control) return '';

    if (control.hasError('required')) {
      return 'This field is required';
    }

    if (control.hasError('email')) {
      return 'Please enter a valid email address';
    }

    if (control.hasError('minlength')) {
      return `Minimum length is ${control.getError('minlength').requiredLength} characters`;
    }

    if (control.hasError('pattern')) {
      return 'Please enter a valid format';
    }

    if (control.hasError('min')) {
      return `Value must be at least ${control.getError('min').min}`;
    }

    return 'Invalid value';
  }
}
