import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-sathbara-entry-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './sathbara-entry-modal.component.html',
  styleUrl: './sathbara-entry-modal.component.scss'
})
export class SathbaraEntryModalComponent implements OnInit {
  @Input() rowId?: number;

  // Form data
  formData = {
    id: 0,
    surveyNumber: '',
    area: '',
    mutationEntries: '',
    landOwnerName: '',
    remarks: ''
  };

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      surveyNumber: '',
      area: '',
      mutationEntries: '',
      landOwnerName: '',
      remarks: ''
    };

    // If editing an existing record, populate the form
    if (this.rowId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll use mock data based on the ID
      if (this.rowId === 1) {
        this.formData = {
          id: 1,
          surveyNumber: '123/4/A',
          area: '5000 Sq. Mt.',
          mutationEntries: 'Entry No. 1234 dated 15-Jun-2022',
          landOwnerName: 'Rajesh Kumar Sharma',
          remarks: 'Mutation completed and registered'
        };
      } else if (this.rowId === 2) {
        this.formData = {
          id: 2,
          surveyNumber: '456/7/B',
          area: '7500 Sq. Mt.',
          mutationEntries: 'Entry No. 6789 dated 22-Jul-2022',
          landOwnerName: 'Sunil Patel',
          remarks: 'Mutation application submitted, awaiting approval'
        };
      } else if (this.rowId === 3) {
        this.formData = {
          id: 3,
          surveyNumber: '789/10/C',
          area: '3200 Sq. Mt.',
          mutationEntries: 'Entry No. 5432 dated 05-Aug-2022',
          landOwnerName: 'Priya Desai',
          remarks: 'Mutation completed with NA conversion'
        };
      } else if (this.rowId === 4) {
        this.formData = {
          id: 4,
          surveyNumber: '234/5/D',
          area: '10000 Sq. Mt.',
          mutationEntries: 'Entry No. 9876 dated 15-Sep-2022',
          landOwnerName: 'Amit Joshi',
          remarks: 'Mutation in process, verification pending'
        };
      } else if (this.rowId === 5) {
        this.formData = {
          id: 5,
          surveyNumber: '567/8/E',
          area: '8500 Sq. Mt.',
          mutationEntries: 'Entry No. 1357 dated 30-Oct-2022',
          landOwnerName: 'Neha Singh',
          remarks: 'Mutation rejected due to incomplete documentation'
        };
      }
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Close the modal and pass the data back
    this.activeModal.close(this.formData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
