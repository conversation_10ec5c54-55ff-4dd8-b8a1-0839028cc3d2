// Calendar component styles
.calendar-container {
  .fc {
    // Ensure calendar takes full width
    width: 100%;

    // Style the header toolbar
    .fc-header-toolbar {
      margin-bottom: 1rem;

      .fc-toolbar-chunk {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      // Style navigation buttons
      .fc-button {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease-in-out;

        &:hover:not(:disabled) {
          background-color: #0056b3;
          border-color: #0056b3;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        &.fc-button-active {
          background-color: #0056b3;
          border-color: #0056b3;
        }

        &:disabled,
        &.fc-button-disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none !important;
          box-shadow: none !important;
          pointer-events: none;

          &:hover {
            background-color: #007bff !important;
            border-color: #007bff !important;
            transform: none !important;
            box-shadow: none !important;
          }
        }

        // Custom navigation button styles
        &.fc-customPrev-button,
        &.fc-customNext-button {
          font-size: 1.2rem;
          font-weight: bold;
          padding: 0.5rem 0.75rem;
          min-width: 40px;
        }

        &.fc-customToday-button {
          font-weight: 600;
          padding: 0.5rem 1rem;
        }
      }

      // Style the title
      .fc-toolbar-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
      }
    }

    // Style the calendar grid
    .fc-daygrid-day {
      &:hover {
        background-color: #f8f9fa;
      }
    }

    // Style events
    .fc-event {
      border-radius: 3px;
      padding: 2px 4px;
      font-size: 0.75rem;

      &:hover {
        opacity: 0.8;
        cursor: pointer;
      }
    }

    // Style today's date
    .fc-day-today {
      background-color: rgba(255, 193, 7, 0.1) !important;
    }

    // Ensure proper height
    .fc-view-harness {
      min-height: 500px;
    }
  }
}

// Loading and error states
.calendar-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

.calendar-error {
  padding: 2rem;
  text-align: center;

  .alert {
    margin-bottom: 1rem;
  }
}

// Employee info header
.employee-info-header {
  .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;

    .card-body {
      padding: 1rem;
    }

    .card-title {
      margin-bottom: 0.5rem;
      color: #495057;
    }

    .badge {
      font-size: 0.75rem;
    }
  }
}

// Calendar loading overlay (injected dynamically)
.calendar-loading-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(255, 255, 255, 0.9) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
  border-radius: 8px !important;
  backdrop-filter: blur(2px) !important;

  .calendar-loading-content {
    text-align: center !important;
    padding: 20px !important;

    .spinner-border {
      width: 2.5rem !important;
      height: 2.5rem !important;
    }

    p {
      margin-top: 0.75rem !important;
      margin-bottom: 0 !important;
      font-size: 0.9rem !important;
      color: #6c757d !important;
    }
  }
}

// Enhanced navigation buttons
.btn-group {
  .btn {
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.btn-outline-secondary {
      &:hover:not(:disabled) {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
      }
    }
  }
}

// Calendar navigation improvements
.calendar-container {
  .fc {
    // Enhanced button group styling for FullCalendar
    .fc-button-group {
      .fc-button {
        transition: all 0.2s ease-in-out;

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none !important;
          box-shadow: none !important;
        }
      }
    }

    // Loading state for calendar
    &.fc-loading {
      opacity: 0.7;
      pointer-events: none;
    }

    // Improved event styling
    .fc-event {
      transition: all 0.2s ease-in-out;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      // Holiday events styling
      &.holiday-event {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        color: #ffffff !important;
        font-weight: 600;

        &::before {
          content: "🎉 ";
          margin-right: 2px;
        }

        &:hover {
          background-color: #c82333 !important;
          border-color: #c82333 !important;
        }
      }

      // Leave events styling
      &.leave-event {
        color: #ffffff !important;
        font-weight: 500;

        &::before {
          content: "🏖️ ";
          margin-right: 2px;
        }

        // Different colors based on leave status
        &[data-status="APPROVED"] {
          background-color: #28a745 !important;
          border-color: #28a745 !important;

          &:hover {
            background-color: #218838 !important;
            border-color: #218838 !important;
          }
        }

        &[data-status="PENDING"] {
          background-color: #ffc107 !important;
          border-color: #ffc107 !important;
          color: #212529 !important;

          &:hover {
            background-color: #e0a800 !important;
            border-color: #e0a800 !important;
          }
        }

        &[data-status="REJECTED"] {
          background-color: #dc3545 !important;
          border-color: #dc3545 !important;

          &:hover {
            background-color: #c82333 !important;
            border-color: #c82333 !important;
          }
        }

        &[data-status="CANCELLED"] {
          background-color: #6c757d !important;
          border-color: #6c757d !important;

          &:hover {
            background-color: #5a6268 !important;
            border-color: #5a6268 !important;
          }
        }
      }

      // Attendance/calendar events (default styling)
      &:not(.holiday-event):not(.leave-event) {
        &::before {
          content: "📅 ";
          margin-right: 2px;
        }
      }
    }
  }
}

// Event legend styling
.legend-color-box {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  flex-shrink: 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

// Responsive improvements
@media (max-width: 768px) {
  .employee-info-header {
    .d-flex {
      flex-direction: column;
      gap: 1rem;

      .text-end {
        text-align: left !important;
      }
    }

    .btn-group {
      margin-bottom: 0.5rem;
    }
  }

  .calendar-container {
    .fc {
      .fc-header-toolbar {
        flex-direction: column;
        gap: 0.5rem;

        .fc-toolbar-chunk {
          justify-content: center;
        }
      }
    }
  }
}