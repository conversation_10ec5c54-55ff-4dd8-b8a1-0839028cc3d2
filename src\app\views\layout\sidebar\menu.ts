import { MenuItem } from './menu.model';

export const MENU: MenuItem[] = [
  {
    label: 'Main',
    isTitle: true
  },
  {
    label: 'Dashboard',
    icon: 'home',
    link: '/dashboard'
  },
  {
    label: 'Sales Data List',
    icon: 'users',
    link: '/sales-list'
  },



  {
    label: 'Admin',
    icon: 'shield',
    requiredPermissions: ["admin:access"],
    link: '/admin-lead-list'
  },
  {
    label: 'Ops Team',
    icon: 'shield',
    // Temporarily disable permission check to allow menu visibility for testing
    // requiredPermissions: ["ops:access"],
    subItems: [
      {
        label: 'Bucket',
        link: '/bucket',
        // Permission will be checked at route level by dynamicAuthGuard
        // requiredPermissions: ["ops:access"]
      },
      {
        label: 'Login at Institute',
        link: '/login-at-institute',
        // Permission will be checked at route level by dynamicAuthGuard
        // requiredPermissions: ["ops:access"]
      },
      {
        label: 'Disbursement',
        link: '/disbursement',
        // Permission will be checked at route level by dynamicAuthGuard
        // requiredPermissions: ["ops:access"]
      },
    ]
  },
  {
    label: 'LMS',
    icon: 'book',

    subItems: [
      {
        label: 'Apply Leave',
        link: '/lms/dashboard',

      },
      // {
      //   label: 'Apply Leave',
      //   link: '/lms/apply-leave',
      // },

      // {
      //   label: 'Approve Leaves',
      //   link: '/lms/approve-leaves',
      // },
      // {
      //   label: 'Leave Policy',
      //   link: '/lms/leave-policy',
      // },
      // {
      //   label: 'Assign Comp-off',
      //   link: '/lms/assign-comp-off',
      // },
      // {
      //   label: 'Comp-off Request',
      //   link: '/lms/comp-off-request',
      // },
      {
        label: 'Calendar',
        link: '/lms/calendar',
      },
      {
        label: 'Salary Slip',
        link: '/lms/salary-slip',
        requiredPermissions: ["salary:read"]

      },
      {
        label: 'Generate Salary Slip',
        link: '/lms/generate-salary-slip',
        requiredPermissions: ["salary:generate"]
      },

    ]
  },



  {
    label: 'Roles & Permissions',
    icon: 'shield',
    requiredPermissions: ["roles:read"],

    subItems: [
      {
        label: 'Roles',
        link: '/role-permission-management/roles',
      },
      // {
      //   label: 'Permissions',
      //   link: '/role-permission-management/permissions',
      // },
    ]
  },


  // {
  //   label: 'Role & Permission Management',
  //   icon: 'shield',
  //   subItems: [
  //     {
  //       label: 'Roles',
  //       link: '/role-permission-management/roles',
  //     },
  //     {
  //       label: 'Permissions',
  //       link: '/role-permission-management/permissions',
  //     },
  //   ]
  // },


  {
    label: 'Master',
    icon: 'settings',
    requiredPermissions: ['master:read'],

    subItems: [
      // High Priority Components
      {
        label: 'Departments',
        link: '/master/departments',
        icon: 'users'
      },
      {
        label: 'Designations',
        link: '/master/designations',
        icon: 'award'
      },
      {
        label: 'Fund Houses',
        link: '/master/fund-houses',
        icon: 'trending-up'
      },
      {
        label: 'Institutes',
        link: '/master/institutes',
        icon: 'home'
      },
      {
        label: 'Corporate Consultancies',
        link: '/master/corporate-consultancies',
        icon: 'briefcase'
      },
      {
        label: 'Board Affiliations',
        link: '/master/board-affiliations',
        icon: 'users'
      },
      {
        label: 'Constitutions',
        link: '/master/constitutions',
        icon: 'file-text'
      },
      {
        label: 'Profession Types',
        link: '/master/profession-types',
        icon: 'briefcase'
      },
      {
        label: 'Locations',
        link: '/master/locations',
        icon: 'map-pin'
      },
      {
        label: 'Product Types',
        link: '/master/product-types',
        icon: 'package'
      },
      {
        label: 'Sub Product Types',
        link: '/master/sub-product-types',
        icon: 'layers'
      },
      {
        label: 'Lead Categories',
        link: '/master/lead-categories',
        icon: 'target'
      },
      {
        label: 'Lead Data Types',
        link: '/master/lead-data-types',
        icon: 'database'
      },
      {
        label: 'Sources',
        link: '/master/sources',
        icon: 'trending-up'
      },
      {
        label: 'Settings',
        link: '/master/settings',
        icon: 'settings'
      },
      // Existing Components
      {
        label: 'Profession',
        link: '/master/profession',
        icon: 'tool'
      },
      {
        label: 'Associate',
        link: '/master/associate',
        icon: 'user-plus'
      },
      {
        label: 'Connect with',
        link: '/master/connect-with',
        icon: 'link'
      },
    ]
  },
  // {
  //   label: 'Web Apps',
  //   isTitle: true
  // },
  // {
  //   label: 'Email',
  //   icon: 'mail',
  //   subItems: [

  //     {
  //       label: 'Inbox',
  //       link: '/apps/email/inbox',
  //     },
  //     {
  //       label: 'Read',
  //       link: '/apps/email/read'
  //     },
  //     {
  //       label: 'Compose',
  //       link: '/apps/email/compose'
  //     },
  //   ]
  // },
  // {
  //   label: 'Chat',
  //   icon: 'message-square',
  //   link: '/apps/chat',
  // },
  // {
  //   label: 'Calendar',
  //   icon: 'calendar',
  //   link: '/apps/calendar',
  //   badge: {
  //     variant: 'primary',
  //     text: 'Event',
  //   }
  // },
  // {
  //   label: 'Components',
  //   isTitle: true
  // },
  // {
  //   label: 'UI Kit',
  //   icon: 'feather',
  //   subItems: [
  //     {
  //       label: 'Accordion',
  //       link: '/ui-components/accordion',
  //     },
  //     {
  //       label: 'Alerts',
  //       link: '/ui-components/alerts',
  //     },
  //     {
  //       label: 'Badges',
  //       link: '/ui-components/badges',
  //     },
  //     {
  //       label: 'Breadcrumbs',
  //       link: '/ui-components/breadcrumbs',
  //     },
  //     {
  //       label: 'Buttons',
  //       link: '/ui-components/buttons',
  //     },
  //     {
  //       label: 'Button group',
  //       link: '/ui-components/button-group',
  //     },
  //     {
  //       label: 'Cards',
  //       link: '/ui-components/cards',
  //     },
  //     {
  //       label: 'Carousel',
  //       link: '/ui-components/carousel',
  //     },
  //     {
  //       label: 'Collapse',
  //       link: '/ui-components/collapse',
  //     },
  //     {
  //       label: 'Datepicker',
  //       link: '/ui-components/datepicker',
  //     },
  //     {
  //       label: 'Dropdowns',
  //       link: '/ui-components/dropdowns',
  //     },
  //     {
  //       label: 'List group',
  //       link: '/ui-components/list-group',
  //     },
  //     {
  //       label: 'Media object',
  //       link: '/ui-components/media-object',
  //     },
  //     {
  //       label: 'Modal',
  //       link: '/ui-components/modal',
  //     },
  //     {
  //       label: 'Navs',
  //       link: '/ui-components/navs',
  //     },
  //     {
  //       label: 'Offcanvas',
  //       link: '/ui-components/offcanvas',
  //     },
  //     {
  //       label: 'Pagination',
  //       link: '/ui-components/pagination',
  //     },
  //     {
  //       label: 'Popovers',
  //       link: '/ui-components/popovers',
  //     },
  //     {
  //       label: 'Progress',
  //       link: '/ui-components/progress',
  //     },
  //     {
  //       label: 'Rating',
  //       link: '/ui-components/rating',
  //     },
  //     {
  //       label: 'Scrollbar',
  //       link: '/ui-components/scrollbar',
  //     },
  //     {
  //       label: 'Spinners',
  //       link: '/ui-components/spinners',
  //     },
  //     {
  //       label: 'Table',
  //       link: '/ui-components/table',
  //     },
  //     {
  //       label: 'Timepicker',
  //       link: '/ui-components/timepicker',
  //     },
  //     {
  //       label: 'Tooltips',
  //       link: '/ui-components/tooltips',
  //     },
  //     {
  //       label: 'Typeadhed',
  //       link: '/ui-components/typeahead',
  //     },
  //   ]
  // },
  // {
  //   label: 'Advanced UI',
  //   icon: 'anchor',
  //   subItems: [
  //     {
  //       label: 'Cropper',
  //       link: '/advanced-ui/cropper',
  //     },
  //     {
  //       label: 'Owl carousel',
  //       link: '/advanced-ui/owl-carousel',
  //     },
  //     {
  //       label: 'SortableJs',
  //       link: '/advanced-ui/sortablejs',
  //     },
  //     {
  //       label: 'Sweet alert',
  //       link: '/advanced-ui/sweet-alert',
  //     },
  //   ]
  // },
  // {
  //   label: 'Forms',
  //   icon: 'file-text',
  //   subItems: [
  //     {
  //       label: 'Basic elements',
  //       link: '/forms/basic-elements'
  //     },
  //     {
  //       label: 'Advanced elements',
  //       subItems: [
  //         {
  //           label: 'Ngx-custom-validators',
  //           link: '/forms/advanced/ngx-custom-validators'
  //         },
  //         {
  //           label: 'Ngx-mask',
  //           link: '/forms/advanced/ngx-mask'
  //         },
  //         {
  //           label: 'Ng-select',
  //           link: '/forms/advanced/ng-select'
  //         },
  //         {
  //           label: 'Ngx-chips',
  //           link: '/forms/advanced/ngx-chips'
  //         },
  //         {
  //           label: 'Ngx-color-picker',
  //           link: '/forms/advanced/ngx-color-picker'
  //         },
  //         {
  //           label: 'Ngx-dropzone',
  //           link: '/forms/advanced/ngx-dropzone-wrapper'
  //         },
  //       ]
  //     },
  //     {
  //       label: 'Editors',
  //       link: '/forms/editors'
  //     },
  //     {
  //       label: 'Wizard',
  //       link: '/forms/wizard'
  //     },
  //   ]
  // },
  // {
  //   label: 'Charts',
  //   icon: 'pie-chart',
  //   subItems: [
  //     {
  //       label: 'ApexCharts',
  //       link: '/charts/apexcharts',
  //     },
  //     {
  //       label: 'ChartJs',
  //       link: '/charts/chartjs',
  //     },
  //   ]
  // },
  // {
  //   label: 'Tables',
  //   icon: 'layout',
  //   subItems: [
  //     {
  //       label: 'Basic tables',
  //       link: '/tables/basic-tables',
  //     },
  //     {
  //       label: 'Ngx-datatable',
  //       link: '/tables/ngx-datatable'
  //     }
  //   ]
  // },
  // {
  //   label: 'Icons',
  //   icon: 'smile',
  //   subItems: [
  //     {
  //       label: 'Feather icons',
  //       link: '/icons/feather-icons',
  //     },
  //   ]
  // },
  // {
  //   label: 'Pages',
  //   isTitle: true
  // },
  // {
  //   label: 'Special pages',
  //   icon: 'book',
  //   subItems: [
  //     {
  //       label: 'Blank page',
  //       link: '/general/blank-page',
  //     },
  //     {
  //       label: 'Faq',
  //       link: '/general/faq',
  //     },
  //     {
  //       label: 'Invoice',
  //       link: '/general/invoice',
  //     },
  //     {
  //       label: 'Profile',
  //       link: '/general/profile',
  //     },
  //     {
  //       label: 'Pricing',
  //       link: '/general/pricing',
  //     },
  //     {
  //       label: 'Timeline',
  //       link: '/general/timeline',
  //     }
  //   ]
  // },
  // {
  //   label: 'Authentication',
  //   icon: 'unlock',
  //   subItems: [
  //     {
  //       label: 'Login',
  //       link: '/auth/login',
  //     },
  //     {
  //       label: 'Register',
  //       link: '/auth/register',
  //     },
  //   ]
  // },
  // {
  //   label: 'Error',
  //   icon: 'cloud-off',
  //   subItems: [
  //     {
  //       label: '404',
  //       link: '/error/404',
  //     },
  //     {
  //       label: '500',
  //       link: '/error/500',
  //     },
  //   ]
  // },
];
