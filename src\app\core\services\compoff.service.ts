import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, tap, map, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';
import { EmployeeService } from './employee.service';

/**
 * Dedicated Comp-off Service for handling comp-off specific operations
 * This service provides methods for the new dedicated comp-off API endpoints
 */

// Comp-off specific interfaces
export interface CompoffRequest {
  working_date: string; // Date when employee worked (YYYY-MM-DD format)
  reason: string; // Reason for comp-off request
  employee_id: string; // Required - UUID of the employee
  request_type: 'employee_request' | 'manager_assignment'; // Required - Type of comp-off request
}

// Interface for the input data (before processing)
export interface CompoffRequestInput {
  working_date: string; // Date when employee worked (YYYY-MM-DD format)
  reason: string; // Reason for comp-off request
}

export interface CompoffRequestResponse {
  id: string; // UUID of the comp-off request
  employee_id: string; // UUID of the employee
  employee_name?: string; // Employee name (if populated by API)
  employee_code?: string; // Employee code (if populated by API)
  working_date: string; // Date when employee worked
  reason: string; // Reason for comp-off request
  status: 'pending' | 'approved' | 'rejected'; // Request status
  requested_at: string; // When the request was made
  approved_by?: string; // UUID of approver (if approved)
  approved_by_name?: string; // Name of approver (if populated by API)
  approved_at?: string; // When it was approved
  rejection_reason?: string; // Reason for rejection (if rejected)
  created_at: string;
  updated_at?: string;
}

export interface CompoffApprovalRequest {
  action: 'approve' | 'reject';
  comments?: string; // Optional comments for approval/rejection
}

@Injectable({
  providedIn: 'root'
})
export class CompoffService {
  private baseUrl = `${environment.apiUrl}/api/v1/leave/comp-off`;

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private employeeService: EmployeeService
  ) {}

  /**
   * Request comp-off for current user using dedicated API endpoint
   * Uses proper employee_id from employee API (not user ID)
   * @param compoffRequestInput Comp-off request input data (working_date, reason) OR full CompoffRequest
   * @returns Observable of comp-off request response
   */
  requestCompoff(compoffRequestInput: CompoffRequestInput | CompoffRequest): Observable<CompoffRequestResponse> {
    console.log('🎯 COMP-OFF: Starting comp-off request...');
    console.log('🎯 COMP-OFF: Input data:', compoffRequestInput);

    // Check if this is already a full CompoffRequest (manager assignment)
    if ('employee_id' in compoffRequestInput && 'request_type' in compoffRequestInput) {
      console.log('🎯 COMP-OFF: Full CompoffRequest detected (manager assignment)');

      // Validate the employee_id format
      if (!this.isValidUUID(compoffRequestInput.employee_id)) {
        console.error('❌ COMP-OFF: Invalid employee UUID format:', compoffRequestInput.employee_id);
        return throwError(() => new Error('Invalid employee identifier format. Please contact support.'));
      }

      // Submit directly to comp-off API
      const url = `${this.baseUrl}/request`;
      console.log('📤 COMP-OFF: Submitting manager assignment to:', url);
      console.log('📤 COMP-OFF: Manager assignment payload:', compoffRequestInput);

      // Add header to skip error interceptor so component can handle errors manually
      const headers = new HttpHeaders({
        'X-Skip-Error-Interceptor': 'true'
      });

      return this.http.post<CompoffRequestResponse>(url, compoffRequestInput, { headers }).pipe(
        tap(response => {
          console.log('✅ COMP-OFF: Manager assignment submitted successfully:', response);
        }),
        catchError(error => {
          console.error('❌ COMP-OFF: Manager assignment failed:', error);
          console.error('❌ COMP-OFF: Error details:', {
            status: error.status,
            statusText: error.statusText,
            error: error.error,
            message: error.message
          });
          return throwError(() => error);
        })
      );
    }

    // Handle regular employee request (CompoffRequestInput)
    console.log('🎯 COMP-OFF: Regular employee request detected');

    // Step 1: Get current user's email from authentication
    const currentUser = this.authService.currentUserValue;
    if (!currentUser || !currentUser.email) {
      console.error('❌ COMP-OFF: No authenticated user or email found');
      return throwError(() => new Error('User authentication required. Please log in again.'));
    }

    console.log('🔍 COMP-OFF: Current user email:', currentUser.email);

    // Step 2: Get employee_id using EXACT same method as apply-leave (direct employee lookup)
    return this.getEmployeeIdFromEmployeeLookup().pipe(
      switchMap((employeeId: string | null) => {
        if (!employeeId) {
          console.error('❌ COMP-OFF: No employee record found for user email:', currentUser.email);
          return throwError(() => new Error('Employee record not found. Please ensure your account is properly set up in the employee system.'));
        }

        console.log('✅ COMP-OFF: Found employee_id from employee API:', employeeId);
        console.log('✅ COMP-OFF: This employee_id should exist in the backend database');

        // Step 3: Validate UUID format
        if (!this.isValidUUID(employeeId)) {
          console.error('❌ COMP-OFF: Invalid employee UUID format:', employeeId);
          return throwError(() => new Error('Invalid employee identifier format. Please contact support.'));
        }

        // Step 4: Prepare comp-off request payload
        const compoffRequest: CompoffRequest = {
          working_date: compoffRequestInput.working_date,
          reason: compoffRequestInput.reason,
          employee_id: employeeId, // Using proper employee_id from employee API
          request_type: 'employee_request'
        };

        console.log('📤 COMP-OFF: Final request payload:', compoffRequest);

        // Step 5: Submit to comp-off API
        const url = `${this.baseUrl}/request`;
        console.log('📤 COMP-OFF: Submitting to:', url);

        // Add header to skip error interceptor so component can handle errors manually
        const headers = new HttpHeaders({
          'X-Skip-Error-Interceptor': 'true'
        });

        return this.http.post<CompoffRequestResponse>(url, compoffRequest, { headers }).pipe(
          tap(response => {
            console.log('✅ COMP-OFF: Request submitted successfully:', response);
          }),
          catchError(error => {
            console.error('❌ COMP-OFF: Request failed:', error);
            console.error('❌ COMP-OFF: Failed with employee_id:', employeeId);
            console.error('❌ COMP-OFF: Error details:', {
              status: error.status,
              statusText: error.statusText,
              error: error.error,
              message: error.message
            });
            return throwError(() => error);
          })
        );
      })
    );
  }

  /**
   * Get employee_id using EXACT same method as apply-leave component
   * First try from balance API, then fallback to employee lookup
   * @returns Observable of employee ID
   */
  private getEmployeeIdFromBalanceApi(): Observable<string | null> {
    console.log('CompoffService: Getting employee_id from balance API (same as apply-leave)...');

    // First try to get employee_id from leave balance API (same as apply-leave)
    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });
    return this.http.get<any>('/api/v1/leave/balance', { headers }).pipe(
      map((response: any) => {
        console.log('CompoffService: Balance API response:', response);

        if (response && response.length > 0 && response[0] && response[0].employee_id) {
          const employeeId = response[0].employee_id;
          console.log('CompoffService: ✅ Employee UUID extracted from balance API:', employeeId);
          return employeeId;
        }

        console.log('CompoffService: No employee_id found in balance API, trying fallback...');
        return null;
      }),
      switchMap((employeeId: string | null) => {
        if (employeeId) {
          return of(employeeId);
        }

        // Fallback: Use the same employee lookup method as apply-leave
        return this.getEmployeeIdFromEmployeeLookup();
      }),
      catchError((error) => {
        console.error('CompoffService: Error getting employee_id from balance API, trying fallback:', error);
        return this.getEmployeeIdFromEmployeeLookup();
      })
    );
  }

  /**
   * Get employee_id using EXACT same method as apply-leave component
   * This matches apply-leave.component.ts lines 252-316
   */
  private getEmployeeIdFromEmployeeLookup(): Observable<string | null> {
    console.log('🔍 COMP-OFF: Using EXACT same employee lookup method as apply-leave...');

    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ COMP-OFF: No current user found');
      return of(null);
    }

    console.log('🔍 COMP-OFF: Current user:', currentUser);

    // Get all employees and find the current user by email or employee code (EXACT same as apply-leave)
    return this.employeeService.getAllEmployees().pipe(
      map((response: any) => {
        console.log('🔍 COMP-OFF: Employee API response:', response);

        let employees: any[] = [];
        if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
          employees = response.data;
        } else if (Array.isArray(response)) {
          employees = response;
        }

        console.log('🔍 COMP-OFF: Searching for current user in employees:', employees);

        // Try to find employee by email first, then by employee code (EXACT same logic as apply-leave)
        let currentEmployee = employees.find(emp =>
          emp.office_email === currentUser.email ||
          emp.personal_email === currentUser.email
        );

        // If not found by email, try by employee code (if user ID matches employee code)
        if (!currentEmployee) {
          currentEmployee = employees.find(emp =>
            emp.employee_code === currentUser.id.toString()
          );
        }

        if (currentEmployee) {
          // Ensure we have a proper UUID string (EXACT same as apply-leave)
          const employeeUuid = currentEmployee.id ? currentEmployee.id.toString() : null;
          console.log('✅ COMP-OFF: Found current employee UUID:', employeeUuid);
          console.log('✅ COMP-OFF: Employee UUID type:', typeof employeeUuid);
          console.log('✅ COMP-OFF: Employee details:', currentEmployee);
          return employeeUuid;
        } else {
          console.error('❌ COMP-OFF: Could not find current employee in employee list');
          console.log('❌ COMP-OFF: Current user email:', currentUser.email);
          console.log('❌ COMP-OFF: Current user ID:', currentUser.id);
          console.log('❌ COMP-OFF: Available employees:', employees.map(emp => ({
            id: emp.id,
            employee_code: emp.employee_code,
            office_email: emp.office_email,
            personal_email: emp.personal_email,
            name: `${emp.first_name} ${emp.last_name}`
          })));

          // Fallback: Try to use the current user's ID if it's a valid UUID (EXACT same as apply-leave)
          const userUuid = currentUser.id;
          if (userUuid && this.isValidUUID(userUuid)) {
            console.log('🔄 COMP-OFF: Using current user UUID as fallback:', userUuid);
            return userUuid;
          } else {
            console.error('❌ COMP-OFF: Current user ID is not a valid UUID:', userUuid);
            return null;
          }
        }
      }),
      catchError((error) => {
        console.error('❌ COMP-OFF: Error in employee lookup:', error);

        // Error fallback: Try to use current user's ID if it's a valid UUID (same as apply-leave)
        const userUuid = currentUser.id;
        if (userUuid && this.isValidUUID(userUuid)) {
          console.log('🔄 COMP-OFF: Using current user UUID as error fallback:', userUuid);
          return of(userUuid);
        } else {
          console.error('❌ COMP-OFF: Current user ID is not a valid UUID:', userUuid);
          return of(null);
        }
      })
    );
  }

  /**
   * Get current user's employee ID using the EXACT same method as PL/SL applications
   * Step 1: Get current user's email from authentication
   * Step 2: Find employee record where office_email matches user's email
   * Step 3: Extract employee.id from the matched record
   * @returns Observable of employee ID
   */
  private getCurrentEmployeeId(): Observable<string | null> {
    console.log('🔍 COMP-OFF: Starting employee_id retrieval (same method as PL/SL)...');

    // Step 1: Get current authenticated user
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ COMP-OFF: No current user found in authentication');
      return of(null);
    }

    console.log('🔍 COMP-OFF: Current authenticated user:', {
      id: currentUser.id,
      email: currentUser.email,
      name: currentUser.name
    });

    // Step 2: Get all employees from /api/v1/employees/ endpoint (same as PL/SL)
    console.log('🔍 COMP-OFF: Fetching employees from /api/v1/employees/ endpoint...');
    return this.employeeService.getAllEmployees().pipe(
      map((response: any) => {
        console.log('🔍 COMP-OFF: Employee API response received:', response);

        // Parse employee data (same logic as apply-leave component)
        let employees: any[] = [];
        if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
          employees = response.data;
        } else if (Array.isArray(response)) {
          employees = response;
        }

        console.log(`🔍 COMP-OFF: Found ${employees.length} employees in database`);
        console.log('🔍 COMP-OFF: Looking for employee with office_email:', currentUser.email);

        // Step 3: Find employee by office_email (PRIMARY method - same as PL/SL)
        let currentEmployee = employees.find(emp =>
          emp.office_email === currentUser.email
        );

        if (currentEmployee) {
          console.log('✅ COMP-OFF: Found employee by office_email:', {
            id: currentEmployee.id,
            office_email: currentEmployee.office_email,
            employee_code: currentEmployee.employee_code,
            name: `${currentEmployee.first_name} ${currentEmployee.last_name}`
          });
        } else {
          console.log('⚠️ COMP-OFF: No employee found by office_email, trying personal_email...');

          // Fallback 1: Try personal_email (same as apply-leave)
          currentEmployee = employees.find(emp =>
            emp.personal_email === currentUser.email
          );

          if (currentEmployee) {
            console.log('✅ COMP-OFF: Found employee by personal_email:', {
              id: currentEmployee.id,
              personal_email: currentEmployee.personal_email,
              employee_code: currentEmployee.employee_code,
              name: `${currentEmployee.first_name} ${currentEmployee.last_name}`
            });
          } else {
            console.log('⚠️ COMP-OFF: No employee found by personal_email, trying employee_code...');

            // Fallback 2: Try employee_code (same as apply-leave)
            currentEmployee = employees.find(emp =>
              emp.employee_code === currentUser.id.toString()
            );

            if (currentEmployee) {
              console.log('✅ COMP-OFF: Found employee by employee_code:', {
                id: currentEmployee.id,
                employee_code: currentEmployee.employee_code,
                office_email: currentEmployee.office_email,
                name: `${currentEmployee.first_name} ${currentEmployee.last_name}`
              });
            }
          }
        }

        if (currentEmployee) {
          // Step 4: Extract employee.id and convert to string (same as apply-leave)
          const employeeId = currentEmployee.id ? currentEmployee.id.toString() : null;
          console.log('✅ COMP-OFF: Extracted employee_id:', employeeId);
          console.log('✅ COMP-OFF: Employee_id type:', typeof employeeId);

          // Step 5: Validate UUID format (same as apply-leave)
          if (employeeId && !this.isValidUUID(employeeId)) {
            console.warn('⚠️ COMP-OFF: Employee ID is not a valid UUID format:', employeeId);
            // Try to use current user's ID if it's a valid UUID
            const userUuid = currentUser.id;
            if (userUuid && this.isValidUUID(userUuid)) {
              console.log('🔄 COMP-OFF: Using current user UUID as fallback:', userUuid);
              return userUuid;
            } else {
              console.error('❌ COMP-OFF: Current user ID is also not a valid UUID:', userUuid);
              return null;
            }
          }

          console.log('✅ COMP-OFF: Final employee_id to use:', employeeId);
          console.log('✅ COMP-OFF: This should match the employee_id used by PL/SL applications');
          return employeeId;
        } else {
          console.error('❌ COMP-OFF: Could not find employee record for current user');
          console.error('❌ COMP-OFF: Searched for:', {
            office_email: currentUser.email,
            personal_email: currentUser.email,
            employee_code: currentUser.id.toString()
          });
          console.error('❌ COMP-OFF: Available employees:', employees.map(emp => ({
            id: emp.id,
            employee_code: emp.employee_code,
            office_email: emp.office_email,
            personal_email: emp.personal_email,
            name: `${emp.first_name} ${emp.last_name}`
          })));

          // Final fallback: Use current user's ID if it's a valid UUID (same as apply-leave)
          const userUuid = currentUser.id;
          if (userUuid && this.isValidUUID(userUuid)) {
            console.log('🔄 COMP-OFF: Using current user UUID as final fallback:', userUuid);
            console.warn('⚠️ COMP-OFF: This fallback might cause "Employee not found" errors if user ID doesn\'t exist in employee table');
            return userUuid;
          } else {
            console.error('❌ COMP-OFF: Current user ID is not a valid UUID:', userUuid);
            return null;
          }
        }
      }),
      catchError(error => {
        console.error('❌ COMP-OFF: Error fetching employees from API:', error);

        // Error fallback: Try to use current user's ID if it's a valid UUID
        const currentUser = this.authService.currentUserValue;
        const userUuid = currentUser?.id;
        if (userUuid && this.isValidUUID(userUuid)) {
          console.log('🔄 COMP-OFF: Using current user UUID as error fallback:', userUuid);
          console.warn('⚠️ COMP-OFF: This fallback might cause "Employee not found" errors');
          return of(userUuid);
        } else {
          console.error('❌ COMP-OFF: Current user ID is not a valid UUID:', userUuid);
          return of(null);
        }
      })
    );
  }

  /**
   * Get employee_id by matching user's email with employee records
   * This is the correct method that should match PL/SL applications
   * @param userEmail Current user's email address
   * @returns Observable of employee_id or null if not found
   */
  private getEmployeeIdByEmail(userEmail: string): Observable<string | null> {
    console.log('🔍 COMP-OFF: Looking up employee_id for email:', userEmail);

    return this.employeeService.getAllEmployees().pipe(
      map((response: any) => {
        console.log('🔍 COMP-OFF: Employee API response:', response);

        // Parse employee data
        let employees: any[] = [];
        if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
          employees = response.data;
        } else if (Array.isArray(response)) {
          employees = response;
        }

        console.log(`🔍 COMP-OFF: Searching ${employees.length} employees for email: ${userEmail}`);

        // Find employee by office_email (primary method)
        const employee = employees.find(emp => emp.office_email === userEmail);

        if (employee) {
          const employeeId = employee.id ? employee.id.toString() : null;
          console.log('✅ COMP-OFF: Found employee by office_email:', {
            id: employeeId,
            office_email: employee.office_email,
            employee_code: employee.employee_code,
            name: `${employee.first_name} ${employee.last_name}`
          });
          return employeeId;
        } else {
          console.error('❌ COMP-OFF: No employee found with office_email:', userEmail);
          console.error('❌ COMP-OFF: Available employees:', employees.map(emp => ({
            id: emp.id,
            office_email: emp.office_email,
            employee_code: emp.employee_code,
            name: `${emp.first_name} ${emp.last_name}`
          })));
          return null;
        }
      }),
      catchError(error => {
        console.error('❌ COMP-OFF: Error fetching employees from getAllEmployees:', error);
        console.log('🔄 COMP-OFF: Trying fallback approach with current user ID...');

        // Fallback: Use current user's ID if it's a valid UUID
        const currentUser = this.authService.currentUserValue;
        const userUuid = currentUser?.id;

        if (userUuid && this.isValidUUID(userUuid)) {
          console.log('🔄 COMP-OFF: Using current user UUID as fallback:', userUuid);
          console.warn('⚠️ COMP-OFF: This fallback might cause "Employee not found" errors if user ID doesn\'t exist in employee table');
          return of(userUuid);
        } else {
          console.error('❌ COMP-OFF: Current user ID is not a valid UUID:', userUuid);
          return of(null);
        }
      })
    );
  }

  /**
   * Get my comp-off requests using dedicated API endpoint
   * @returns Observable of comp-off request responses
   */
  getMyCompoffRequests(): Observable<CompoffRequestResponse[]> {
    console.log('📋 Fetching my comp-off requests via dedicated API');

    const url = `${this.baseUrl}/my-requests`;
    console.log('📤 GET URL:', url);

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<CompoffRequestResponse[]>(url, { headers }).pipe(
      tap(response => console.log('📋 My comp-off requests response:', response)),
      catchError(error => {
        console.error('❌ Error fetching comp-off requests:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get pending comp-off requests for admin/manager review
   * @returns Observable of pending comp-off requests
   */
  getPendingCompoffRequests(): Observable<CompoffRequestResponse[]> {
    console.log('📋 Fetching pending comp-off requests for admin review');

    const url = `${this.baseUrl}/pending`;
    console.log('📤 GET URL:', url);

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<CompoffRequestResponse[]>(url, { headers }).pipe(
      tap(response => console.log('📋 Pending comp-off requests response:', response)),
      catchError(error => {
        console.error('❌ Error fetching pending comp-off requests:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Approve or reject a comp-off request
   * @param requestId UUID of the comp-off request
   * @param approval Approval/rejection data
   * @returns Observable of updated comp-off request
   */
  approveCompoffRequest(requestId: string, approval: CompoffApprovalRequest): Observable<CompoffRequestResponse> {
    console.log('🔄 Processing comp-off approval:', { requestId, approval });

    const url = `${this.baseUrl}/${requestId}/approve`;
    console.log('📤 POST URL:', url);

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.post<CompoffRequestResponse>(url, approval, { headers }).pipe(
      tap(response => {
        console.log('✅ Comp-off approval processed successfully:', response);
      }),
      catchError(error => {
        console.error('❌ Comp-off approval failed:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get comp-off request details by ID
   * @param requestId UUID of the comp-off request
   * @returns Observable of comp-off request details
   */
  getCompoffRequestDetails(requestId: string): Observable<CompoffRequestResponse> {
    console.log('🔍 Fetching comp-off request details for ID:', requestId);

    const url = `${this.baseUrl}/${requestId}`;
    console.log('📤 GET URL:', url);

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<CompoffRequestResponse>(url, { headers }).pipe(
      tap(response => console.log('📋 Comp-off request details:', response)),
      catchError(error => {
        console.error('❌ Error fetching comp-off request details:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get all comp-off requests (admin/manager view)
   * @param status Optional status filter
   * @returns Observable of comp-off requests
   */
  getAllCompoffRequests(status?: string): Observable<CompoffRequestResponse[]> {
    console.log('📋 Fetching all comp-off requests for admin view');

    let url = `${this.baseUrl}/all`;
    if (status) {
      url += `?status=${status}`;
    }
    console.log('📤 GET URL:', url);

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<CompoffRequestResponse[]>(url, { headers }).pipe(
      tap(response => console.log('📋 All comp-off requests response:', response)),
      catchError(error => {
        console.error('❌ Error fetching all comp-off requests:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get comp-off statistics for dashboard
   * @returns Observable of comp-off statistics
   */
  getCompoffStatistics(): Observable<any> {
    console.log('📊 Fetching comp-off statistics');

    const url = `${this.baseUrl}/statistics`;
    console.log('📤 GET URL:', url);

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<any>(url, { headers }).pipe(
      tap(response => console.log('📊 Comp-off statistics response:', response)),
      catchError(error => {
        console.error('❌ Error fetching comp-off statistics:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Bulk approve comp-off requests
   * @param requestIds Array of request IDs to approve
   * @param comments Optional comments for bulk approval
   * @returns Observable of bulk approval result
   */
  bulkApproveCompoffRequests(requestIds: string[], comments?: string): Observable<any> {
    console.log('🔄 Bulk approving comp-off requests:', requestIds);

    const url = `${this.baseUrl}/bulk-approve`;
    const payload = {
      request_ids: requestIds,
      comments: comments
    };

    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.post<any>(url, payload, { headers }).pipe(
      tap(response => {
        console.log('✅ Bulk comp-off approval successful:', response);
      }),
      catchError(error => {
        console.error('❌ Bulk comp-off approval failed:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Export comp-off requests to Excel
   * @param filters Optional filters for export
   * @returns Observable of blob data
   */
  exportCompoffRequests(filters?: any): Observable<Blob> {
    console.log('📊 Exporting comp-off requests to Excel');

    const url = `${this.baseUrl}/export`;

    return this.http.post(url, filters || {}, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'X-Skip-Error-Interceptor': 'true'
      }
    }).pipe(
      tap(() => console.log('✅ Comp-off export successful')),
      catchError(error => {
        console.error('❌ Comp-off export failed:', error);
        return throwError(() => error);
      })
    );
  }

  // ==========================================
  // PRIVATE HELPER METHODS
  // ==========================================



  /**
   * Test employee API endpoint to see what's causing the failures
   * This helps debug the employee API connection issues
   */
  testEmployeeApiEndpoint(): Observable<any> {
    console.log('🔍 TESTING: Employee API endpoint directly...');

    return this.employeeService.getAllEmployees().pipe(
      map(response => {
        console.log('✅ TESTING: Employee API SUCCESS:', response);
        return {
          success: true,
          message: 'Employee API is working',
          response: response
        };
      }),
      catchError(error => {
        console.error('❌ TESTING: Employee API FAILED:', error);
        return of({
          success: false,
          message: 'Employee API failed',
          error: error.message,
          status: error.status,
          statusText: error.statusText
        });
      })
    );
  }

  /**
   * Debug method to verify employee_id retrieval from balance API (same as apply-leave)
   * This helps ensure comp-off uses proper employee_id from the correct API flow
   */
  debugEmployeeIdComparison(): Observable<any> {
    console.log('🔍 DEBUG: Testing employee_id lookup via balance API (same as apply-leave)...');

    return this.getEmployeeIdFromBalanceApi().pipe(
      map(employeeId => {
        const currentUser = this.authService.currentUserValue;

        console.log('🔍 DEBUG RESULTS:');
        console.log('  📧 User email from balance API (same as apply-leave)');
        console.log('  👤 Employee_id from balance API lookup:', employeeId);
        console.log('  ✅ Is valid UUID:', employeeId ? this.isValidUUID(employeeId) : false);
        console.log('  🎯 This employee_id should exist in backend database');

        if (!employeeId) {
          console.error('❌ DEBUG: No employee_id found from balance API - this will cause "Employee not found" errors');
        } else if (!this.isValidUUID(employeeId)) {
          console.error('❌ DEBUG: Invalid UUID format - this will cause API errors');
        } else {
          console.log('✅ DEBUG: Employee_id from balance API looks valid and should work for comp-off requests');
        }

        return {
          success: !!employeeId,
          compoffEmployeeId: employeeId,
          currentUserId: null, // Not using auth service user ID anymore
          currentUserEmail: null, // Email comes from balance API
          isValidUUID: employeeId ? this.isValidUUID(employeeId) : false,
          message: employeeId ? 'Employee ID found from balance API (same as apply-leave)' : 'Employee ID lookup failed from balance API'
        };
      }),
      catchError(error => {
        console.error('❌ DEBUG: Employee_id retrieval from balance API failed:', error);
        return of({
          success: false,
          compoffEmployeeId: null,
          currentUserId: null,
          currentUserEmail: null,
          isValidUUID: false,
          message: 'Employee ID retrieval failed from balance API',
          error: error.message
        });
      })
    );
  }

  /**
   * Check if a string is a valid UUID (same validation as apply-leave component)
   * @param uuid String to validate (can be null)
   * @returns True if valid UUID
   */
  private isValidUUID(uuid: string | null): boolean {
    if (!uuid) return false;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}
