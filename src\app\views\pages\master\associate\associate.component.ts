import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { MasterService } from '../../../../core/services/master.service';
import { Associate, AssociateCreate, AssociateUpdate } from '../../../../core/models/associate.model';
import { AuthService } from '../../../../core/services/auth.service';
import { LocationService } from '../../../../core/services/location.service';
import { catchError, debounceTime, finalize, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';
import Swal from 'sweetalert2';

// Sortable directive
export type SortColumn = 'name' | 'category' | 'status' | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number | null | undefined, v2: string | number | null | undefined) {
  // Handle null/undefined values
  if (v1 === null || v1 === undefined) return -1;
  if (v2 === null || v2 === undefined) return 1;

  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-associate',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbdSortableHeader,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './associate.component.html',
  styleUrl: './associate.component.scss'
})
export class AssociateComponent implements OnInit {
  // Data
  associates: Associate[] = [];
  selectedAssociate: Associate | null = null;

  // Loading state
  loading = false;
  submitting = false;
  deleting = false;
  restoring = false;
  deletingAssociateId: number | null = null;
  restoringAssociateId: number | null = null;

  // Form
  associateForm: FormGroup;
  formMode: 'create' | 'edit' = 'create';
  currentAssociateId?: number;
  modalError: string | null = null; // Add modal-specific error handling

  // Search
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 10;
  totalItems = 0;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // Add a list of allowed categories for the dropdown and mapping
  categoryOptions = [
    { value: 'financial', label: 'Financial' },
    { value: 'legal', label: 'Legal' },
    { value: 'real_estate', label: 'Real Estate' },
    { value: 'taxation', label: 'Taxation' },
    { value: 'other', label: 'Other' }
  ];

  // Dropdown options
  locations: any[] = [];
  professions: any[] = [];
  professionTypes: any[] = []; // Load from API like sales list
  filteredProfessions: any[] = [];

  constructor(
    private masterService: MasterService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private authService: AuthService,
    private locationService: LocationService,
    private http: HttpClient
  ) {
    // Initialize form with all required fields
    this.associateForm = this.fb.group({
      associate_name: ['', [Validators.required]],
      company_name: ['', [Validators.required]],
      location_id: ['', [Validators.required]],
      sub_location: ['', [Validators.required]],
      profession_type: ['', [Validators.required]],
      profession_id: ['', [Validators.required]],
      status: ['active', [Validators.required]]
    });

    // Listen for profession type changes to filter professions
    this.associateForm.get('profession_type')?.valueChanges.subscribe(professionType => {
      this.filterProfessionsByType(professionType);
      // Reset profession selection when type changes
      this.associateForm.get('profession_id')?.setValue('');
    });
  }

  ngOnInit(): void {
    console.log('🔍 Associate Component - ngOnInit called');
    console.log('🔍 API Base URL:', this.masterService['baseUrl']);
    console.log('🔍 User authenticated:', this.authService.isLoggedIn());
    console.log('🔍 Current user:', this.authService.currentUserValue);

    // Add global test method
    (window as any).testAssociateAPI = () => {
      console.log('🧪 Testing Associate API...');
      this.loadAssociates();
    };

    // Add permission check method
    (window as any).checkMasterPermissions = () => {
      console.log('🔍 Checking Master Permissions...');
      console.log('🔍 User has master:read permission:', this.authService.hasPermission('master:read'));
      console.log('🔍 User permissions:', this.authService.currentUserValue?.permissions);
      console.log('🔍 User role:', this.authService.currentUserValue?.role);
    };

    // Add method to temporarily grant master permission for testing
    (window as any).grantMasterPermission = () => {
      console.log('🔧 Temporarily granting master:read permission...');
      const currentUser = this.authService.currentUserValue;
      if (currentUser) {
        const updatedPermissions = [...(currentUser.permissions || [])];
        if (!updatedPermissions.includes('master:read')) {
          updatedPermissions.push('master:read');
          const updatedUser = { ...currentUser, permissions: updatedPermissions };
          this.authService.updateCurrentUser(updatedUser);
          console.log('✅ master:read permission granted temporarily');
          console.log('🔄 Reloading associates...');
          this.loadAssociates();
        } else {
          console.log('✅ User already has master:read permission');
        }
      }
    };

    // Add method to test API endpoint directly
    (window as any).testAPIEndpoint = () => {
      console.log('🧪 Testing API endpoint directly...');
      const url = `${environment.apiUrl}/api/v1/master/associates?page=1&size=10`;
      console.log('🌐 Testing URL:', url);

      this.http.get(url).subscribe({
        next: (response) => {
          console.log('✅ API endpoint is reachable:', response);
        },
        error: (error) => {
          console.error('❌ API endpoint error:', error);
          console.error('❌ Error status:', error.status);
          console.error('❌ Error message:', error.message);
        }
      });
    };

    this.loadAssociates();
    this.loadLocations();
    this.loadProfessions();
    this.loadProfessionTypes();
    this.searchTerm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      this.page = 1;
      this.loadAssociates();
    });
  }

  loadAssociates() {
    console.log('🔍 Loading associates...', {
      page: this.page,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm.value,
      timestamp: new Date().toISOString()
    });
    this.loading = true;
    const shouldUseClientSideFiltering = this.searchTerm.value && this.searchTerm.value.trim() !== '';
    console.log('🔍 Should use client side filtering:', shouldUseClientSideFiltering);
    console.log('🔍 Search term:', this.searchTerm.value);

    // Convert page/size to skip/limit for new API format
    const skip = (this.page - 1) * this.pageSize;
    this.masterService.getAssociates(skip, this.pageSize, shouldUseClientSideFiltering ? '' : this.searchTerm.value)
      .pipe(
        catchError(error => {
          console.error('❌ Error loading associates:', error);
          console.error('❌ Error status:', error.status);
          console.error('❌ Error message:', error.message);
          console.error('❌ Full error object:', error);
          this.showErrorMessage('Failed to load associates. Please try again.');
          return of({ items: [], total: 0, page: 1, size: this.pageSize, pages: 0 });
        }),
        finalize(() => {
          console.log('🔍 Loading associates completed, setting loading to false');
          this.loading = false;
        })
      )
      .subscribe(response => {
        console.log('✅ Associates loaded successfully:', response);
        console.log('✅ Number of associates:', response.items?.length || 0);

        if (shouldUseClientSideFiltering) {
          const filteredAssociates = this.filterAssociates(response.items, this.searchTerm.value);
          this.associates = filteredAssociates;
          this.totalItems = filteredAssociates.length;
          console.log('✅ Filtered associates:', filteredAssociates.length);
        } else {
          this.associates = response.items;
          this.totalItems = response.total;
          console.log('✅ All associates:', response.items?.length || 0);
        }
      });
  }

  loadLocations() {
    this.locationService.getActiveLocations().subscribe({
      next: (locations) => {
        this.locations = locations;
        console.log('✅ Locations loaded:', locations);
      },
      error: (error) => {
        console.error('❌ Error loading locations:', error);
        // Fallback to dummy data if API fails
        this.locations = [
          { id: '49d7e0f7-4ac3-4c73-afd3-e6bb317a4aba', name: 'Borivali' },
          { id: '619aacf8-9be1-462c-8866-9df2ae799b38', name: 'Pune' },
          { id: '123e4567-e89b-12d3-a456-426614174000', name: 'Mumbai' },
          { id: '987fcdeb-51a2-43d1-9c4e-123456789abc', name: 'Delhi' }
        ];
      }
    });
  }

  loadProfessions() {
    this.masterService.getActiveProfessions().subscribe({
      next: (professions) => {
        this.professions = professions;
        this.filteredProfessions = professions; // Initialize filtered list
        console.log('✅ Professions loaded:', professions);
        console.log('✅ Profession types found in data:', [...new Set(professions.map(p => p.type))]);
      },
      error: (error) => {
        console.error('❌ Error loading professions:', error);
        this.professions = [];
        this.filteredProfessions = [];
      }
    });
  }

  loadProfessionTypes() {
    // Load profession types from API (same as sales list)
    this.masterService.getProfessionTypesForDropdown().subscribe({
      next: (professionTypes) => {
        this.professionTypes = professionTypes.map(professionType => ({
          id: professionType.id,
          value: professionType.value,
          label: professionType.label
        }));
        console.log('✅ Profession types loaded:', this.professionTypes);
        console.log('🔍 Profession type values:', this.professionTypes.map(pt => pt.value));
      },
      error: (error) => {
        console.error('❌ Error loading profession types:', error);
        // Fallback to hardcoded values if API fails
        this.professionTypes = [
          { value: 'professional', label: 'Professional' },
          { value: 'non_professional', label: 'Non Professional' }
        ];
      }
    });
  }

  /**
   * Force reload data after CRUD operations
   */
  forceReloadData(): void {
    console.log('🔄 Force reloading associate data...');

    // Clear service cache first
    this.masterService.refreshAssociateCache();

    // Reset pagination to first page
    this.page = 1;

    // Reload data
    this.loadAssociates();
  }

  /**
   * Reset form state and variables
   */
  resetFormState(): void {
    console.log('🧹 Resetting form state...');
    this.formMode = 'create';
    this.currentAssociateId = null;
    this.submitting = false;
    this.modalError = '';
    this.associateForm.reset();
  }

  refreshDropdownData() {
    // Refresh all dropdown data to get latest information
    this.loadLocations();
    this.loadProfessions();
    this.loadProfessionTypes();

    // Clear the service cache to ensure fresh data
    this.masterService.refreshAssociateCache();
  }

  filterProfessionsByType(professionType: string) {
    console.log('🔍 Filtering professions by type:', professionType);
    console.log('🔍 Available professions:', this.professions);
    console.log('🔍 Unique profession types in data:', [...new Set(this.professions.map(p => p.type))]);

    if (!professionType) {
      this.filteredProfessions = this.professions;
      console.log('🔍 No profession type selected, showing all professions');
      return;
    }

    // Use direct comparison - the profession type values should match exactly
    // If there's a mismatch, it will be caught by the validation logic
    let typeToMatch = professionType;

    console.log('🔍 Mapping profession type:', professionType, '→', typeToMatch);

    this.filteredProfessions = this.professions.filter(profession => {
      const matches = profession.type === typeToMatch && profession.status === 'active';
      console.log('🔍 Checking profession:', profession.name, 'type:', profession.type, 'expected:', typeToMatch, 'matches:', matches);
      return matches;
    });

    console.log('✅ Filtered professions for type', professionType, '(mapped to:', typeToMatch, '):', this.filteredProfessions);

    // If no professions match, log a warning
    if (this.filteredProfessions.length === 0) {
      console.warn('⚠️ No professions found for type:', typeToMatch, 'Available types:', [...new Set(this.professions.map(p => p.type))]);
    }

    // Reset profession selection if current selection doesn't match the new filter
    const currentProfessionId = this.associateForm.get('profession_id')?.value;
    if (currentProfessionId) {
      const currentProfessionStillValid = this.filteredProfessions.some(p => p.id === currentProfessionId);
      if (!currentProfessionStillValid) {
        console.log('🔄 Resetting profession selection as current selection no longer matches filter');
        this.associateForm.get('profession_id')?.setValue('');
      }
    }
  }

  onPageChange(page: number) {
    this.page = page;
    this.loadAssociates();
  }

  onSort({ column, direction }: SortEvent) {
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });
    if (direction === '' || column === '') {
      this.loadAssociates();
    } else {
      this.associates = [...this.associates].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  openAssociateModal(modal: TemplateRef<any>, associate?: Associate) {
    // Clear any previous modal errors
    this.modalError = null;

    if (associate) {
      this.formMode = 'edit';
      this.currentAssociateId = associate.id;
      this.associateForm.patchValue({
        associate_name: associate.name,
        company_name: associate.company_name || associate.category,
        location_id: associate.location_id || '',
        sub_location: associate.sub_location || '',
        profession_type: associate.profession_type || '',
        profession_id: associate.profession_id || '',
        status: this.normalizeStatus(associate.status)
      });
      // Filter professions based on the current profession type
      if (associate.profession_type) {
        this.filterProfessionsByType(associate.profession_type);
      }
    } else {
      this.formMode = 'create';
      this.currentAssociateId = undefined;
      this.associateForm.reset({
        associate_name: '',
        company_name: '',
        location_id: '',
        sub_location: '',
        profession_type: '',
        profession_id: '',
        status: 'active'
      });
      // Reset filtered professions to show all
      this.filteredProfessions = this.professions;
    }
    this.modalService.open(modal, { centered: true });
  }

  viewAssociate(associate: Associate, modal: TemplateRef<any>) {
    if (associate.id) {
      this.loading = true;
      this.masterService.getAssociate(associate.id)
        .pipe(
          catchError(error => {
            console.error('Error fetching associate details:', error);
            let errorMessage = 'Failed to fetch associate details. Please try again.';
            if (error && error.message && error.message.includes('No UUID mapping found')) {
              errorMessage = 'The data has changed. Please refresh the page and try again.';
            }
            this.showErrorMessage(errorMessage);
            return of(null);
          }),
          finalize(() => {
            this.loading = false;
          })
        )
        .subscribe(result => {
          if (result) {
            console.log('📋 Associate data for view modal:', result);
            console.log('📋 Associate name:', result.name);
            console.log('📋 Associate status:', result.status, typeof result.status);
            this.selectedAssociate = result;
            this.modalService.open(modal, { centered: true, size: 'lg' });
          }
        });
    }
  }

  saveAssociate() {
    if (this.associateForm.invalid) {
      // Mark all fields as touched to show validation errors
      this.associateForm.markAllAsTouched();
      return;
    }

    // Clear any previous modal errors
    this.clearModalError();
    this.submitting = true;

    if (this.formMode === 'create') {
      // Transform form data to match API requirements
      const formValue = this.associateForm.value;

      // Validate profession type and profession match
      if (formValue.profession_id && formValue.profession_type) {
        const selectedProfession = this.professions.find(p => p.id === formValue.profession_id);
        if (selectedProfession) {
          // Check if the profession type matches - handle both formats
          const professionTypeMatches = selectedProfession.type === formValue.profession_type ||
            (formValue.profession_type === 'professional' && selectedProfession.type === 'Profession') ||
            (formValue.profession_type === 'non_professional' && selectedProfession.type === 'Non Profession');

          if (!professionTypeMatches) {
            this.showModalError(`The selected profession "${selectedProfession.name}" is of type "${selectedProfession.type}" but you selected profession type "${formValue.profession_type}". Please select a matching profession or change the profession type.`);
            this.submitting = false;
            return;
          }
        }
      }

      const newAssociate = {
        associate_name: formValue.associate_name,
        company_name: formValue.company_name,
        location_id: formValue.location_id,
        sub_location: formValue.sub_location || null,
        profession_type: formValue.profession_type,
        profession_id: formValue.profession_id || null,
        status: formValue.status === 'active' // Convert string to boolean
      };
      this.masterService.createAssociate(newAssociate)
        .pipe(
          catchError(error => {
            console.error('Error creating associate:', error);
            // Try to extract backend validation errors
            let errorMsg = 'Failed to create associate. Please try again.';

            if (error.status === 422 && error.error && error.error.detail) {
              // Check for profession type mismatch error
              if (typeof error.error.detail === 'string' &&
                  error.error.detail.includes('Profession type mismatch')) {
                errorMsg = 'The selected profession does not match the chosen profession type. Please select a profession that matches your profession type or change the profession type.';
              } else if (Array.isArray(error.error.detail)) {
                // FastAPI/Pydantic style: error.error.detail is an array of error objects
                const errors = error.error.detail.map((d: any) => {
                  if (d.loc && d.loc.includes('profession_type') && d.msg && d.msg.includes("Input should be 'professional' or 'non_professional'")) {
                    return 'Please select a valid profession type (Professional or Non Professional).';
                  }
                  return d.msg || JSON.stringify(d);
                });
                errorMsg = errors.join(' | ');
              } else {
                errorMsg = error.error.detail;
              }
            } else if (error.error && error.error.message) {
              errorMsg = error.error.message;
            }
            this.showModalError(errorMsg); // Show error in modal instead of SweetAlert
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            // Close modal and reset state
            this.modalService.dismissAll();
            this.resetFormState();
            this.showSuccessMessage('Associate created successfully!');

            // Force reload with a slight delay to ensure modal is closed
            setTimeout(() => {
              console.log('🔄 Forcing reload after create...');
              this.forceReloadData();
            }, 200);
          }
        });
    } else if (this.formMode === 'edit' && this.currentAssociateId) {
      // Transform form data to match API requirements
      const formValue = this.associateForm.value;

      // Validate profession type and profession match for edit mode too
      if (formValue.profession_id && formValue.profession_type) {
        const selectedProfession = this.professions.find(p => p.id === formValue.profession_id);
        if (selectedProfession) {
          // Check if the profession type matches - handle both formats
          const professionTypeMatches = selectedProfession.type === formValue.profession_type ||
            (formValue.profession_type === 'professional' && selectedProfession.type === 'Profession') ||
            (formValue.profession_type === 'non_professional' && selectedProfession.type === 'Non Profession');

          if (!professionTypeMatches) {
            this.showModalError(`The selected profession "${selectedProfession.name}" is of type "${selectedProfession.type}" but you selected profession type "${formValue.profession_type}". Please select a matching profession or change the profession type.`);
            this.submitting = false;
            return;
          }
        }
      }

      const updatedAssociate: AssociateUpdate = {
        associate_name: formValue.associate_name,
        company_name: formValue.company_name,
        location_id: formValue.location_id,
        sub_location: formValue.sub_location || null,
        profession_type: formValue.profession_type,
        profession_id: formValue.profession_id || null,
        status: formValue.status === 'active' // Convert string to boolean
      };
      this.masterService.updateAssociate(this.currentAssociateId, updatedAssociate)
        .pipe(
          catchError(error => {
            console.error('Error updating associate:', error);
            let errorMessage = 'Failed to update associate. Please try again.';

            if (error.status === 422 && error.error && error.error.detail) {
              // Check for profession type mismatch error
              if (typeof error.error.detail === 'string' &&
                  error.error.detail.includes('Profession type mismatch')) {
                errorMessage = 'The selected profession does not match the chosen profession type. Please select a profession that matches your profession type or change the profession type.';
              } else if (Array.isArray(error.error.detail)) {
                const errors = error.error.detail.map((d: any) => {
                  if (d.loc && d.loc.includes('profession_type') && d.msg && d.msg.includes("Input should be 'professional' or 'non_professional'")) {
                    return 'Please select a valid profession type (Professional or Non Professional).';
                  }
                  return d.msg || JSON.stringify(d);
                });
                errorMessage = errors.join(' | ');
              } else {
                errorMessage = error.error.detail;
              }
            } else if (error.error && error.error.message) {
              errorMessage = error.error.message;
            }
            this.showModalError(errorMessage); // Show error in modal instead of SweetAlert
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            // Close modal and reset state
            this.modalService.dismissAll();
            this.resetFormState();
            this.showSuccessMessage('Associate updated successfully!');

            // Force reload with a slight delay to ensure modal is closed
            setTimeout(() => {
              console.log('🔄 Forcing reload after update...');
              this.forceReloadData();
            }, 200);
          }
        });
    }
  }

  deleteAssociate(associate: Associate) {
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete associate "${associate.name}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed && associate.id) {
        // Set loading state for this specific associate
        this.deleting = true;
        this.deletingAssociateId = associate.id;

        this.masterService.deleteAssociate(associate.id)
          .pipe(
            catchError(error => {
              console.error('Error deleting associate:', error);
              let errorMessage = 'Failed to delete associate. Please try again.';
              if (error.message && error.message.includes('No UUID mapping found')) {
                errorMessage = 'The data has changed. Please refresh the page and try again.';
              }
              this.showErrorMessage(errorMessage);
              return of(null);
            }),
            finalize(() => {
              // Reset loading state
              this.deleting = false;
              this.deletingAssociateId = null;
            })
          )
          .subscribe(result => {
            if (result !== null) {
              this.showSuccessMessage('Associate deleted successfully!');
              this.forceReloadData();
            }
          });
      }
    });
  }

  restoreAssociate(associate: Associate) {
    if (!associate.id) return;

    // Set loading state for this specific associate
    this.restoring = true;
    this.restoringAssociateId = associate.id;

    this.masterService.restoreAssociate(associate.id)
      .pipe(
        catchError(error => {
          console.error('Error restoring associate:', error);
          let errorMessage = 'Failed to restore associate. Please try again.';
          if (error.message && error.message.includes('No UUID mapping found')) {
            errorMessage = 'The data has changed. Please refresh the page and try again.';
          }
          this.showErrorMessage(errorMessage);
          return of(null);
        }),
        finalize(() => {
          // Reset loading state
          this.restoring = false;
          this.restoringAssociateId = null;
        })
      )
      .subscribe(result => {
        if (result) {
          this.showSuccessMessage('Associate restored successfully!');
          this.forceReloadData();
        }
      });
  }



  filterAssociates(associates: Associate[], searchTerm: string): Associate[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return associates;
    }
    searchTerm = searchTerm.toLowerCase().trim();
    return associates.filter(associate => {
      return (
        (associate.name && associate.name.toLowerCase().includes(searchTerm)) ||
        (associate.category && associate.category.toLowerCase().includes(searchTerm)) ||
        (associate.company_name && associate.company_name.toLowerCase().includes(searchTerm)) ||
        (associate.location_name && associate.location_name.toLowerCase().includes(searchTerm)) ||
        (associate.status && associate.status.toLowerCase().includes(searchTerm))
      );
    });
  }

  getStatusClass(status: string): string {
    console.log('🎨 Getting status class for:', status, typeof status);
    const normalizedStatus = this.normalizeStatus(status);
    console.log('🎨 Normalized status:', normalizedStatus);

    switch (normalizedStatus) {
      case 'active': return 'bg-success';
      case 'inactive': return 'bg-secondary';
      default:
        console.warn('🎨 Unknown status, defaulting to bg-secondary:', status);
        return 'bg-secondary';
    }
  }

  normalizeStatus(status: any): string {
    console.log('🔄 Normalizing status:', status, typeof status);
    // Handle different status formats from API
    if (status === true || status === 'active' || status === 'Active') {
      console.log('🔄 Status normalized to: active');
      return 'active';
    } else if (status === false || status === 'inactive' || status === 'Inactive') {
      console.log('🔄 Status normalized to: inactive');
      return 'inactive';
    }
    console.warn('🔄 Unknown status, defaulting to inactive:', status);
    return 'inactive'; // Default to inactive if unknown
  }

  editFromViewModal(associateModal: TemplateRef<any>, currentModal: any) {
    if (this.selectedAssociate) {
      currentModal.dismiss();
      this.openAssociateModal(associateModal, this.selectedAssociate);
    }
  }

  showSuccessMessage(message: string) {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
      timer: 2000,
      showConfirmButton: false
    });
  }

  showErrorMessage(message: string) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message
    });
  }

  showModalError(message: string) {
    this.modalError = message;
  }

  clearModalError() {
    this.modalError = null;
  }
}
