// Ngb DatePicker

ngb-datepicker {
  @extend .border;
  .ngb-dp-header {
    padding: .5rem 0;
    direction: ltr;
    .ngb-dp-arrow {
      button {
        .ngb-dp-navigation-chevron {
          border-width: .11em .11em 0 0;
          width: .44rem;
          height: .44rem;
        }
      }
    }
    .ngb-dp-navigation-select {
      select {
        border-color: var(--#{$prefix}border-color);
      }
      &:focus {
        outline: none;
      }
    }
  }
  .ngb-dp-content {
    .ngb-dp-month {
      .ngb-dp-week {
        &.ngb-dp-weekdays {
          margin-bottom: 4px;
          border-top: 1px solid var(--#{$prefix}border-color);
          border-color: var(--#{$prefix}border-color);
          .ngb-dp-weekday {
            color: $primary;
            font-style: normal;
            font-size: 95%;
          }
        }
        .ngb-dp-day {
          &:focus {
            outline: none;
          }
          .btn-light {
            &:hover,
            &.active {
              background-color: $component-hover-bg;
            }
          }
        }
      }
    }
  }
}

.range-selection {
  .form-group.hidden {
    width: 0;
    margin: 0;
    border: none;
    padding: 0;
  }
  .custom-day {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    width: 2rem;
  }
  .custom-day.range, .custom-day:hover {
    background-color: $primary;
    color: $white;
  }
  .custom-day.faded {
    background-color: rgba($primary, .2);
    color: var(--#{$prefix}body-color);
  }
}