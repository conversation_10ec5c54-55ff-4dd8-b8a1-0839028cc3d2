import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Salary Slip interfaces based on OpenAPI specification
 */
export interface SalaryComponent {
  id: string;
  salary_slip_id: string;
  component_name: string;
  component_type: 'earning' | 'deduction' | 'employer_contribution';
  amount: number;
  is_percentage: boolean;
  percentage_base?: number;
  is_taxable: boolean;
  is_statutory: boolean;
  display_order: number;
  description?: string;
  created_at: string;
  updated_at?: string;
}

export interface SalaryComponentCreate {
  component_name: string;
  component_type: 'earning' | 'deduction' | 'employer_contribution';
  amount: number;
  is_percentage?: boolean;
  percentage_base?: number;
  is_taxable?: boolean;
  is_statutory?: boolean;
  display_order?: number;
  description?: string;
}

export interface SalarySlip {
  id: string;
  employee_id: string;
  salary_id?: string;
  pay_period_month: number;
  pay_period_year: number;
  pay_date?: string;
  status: 'draft' | 'generated' | 'approved' | 'paid' | 'cancelled';
  total_working_days?: number;
  actual_working_days?: number;
  remarks?: string;
  gross_salary: number;
  total_deductions: number;
  net_salary: number;
  generated_at?: string;
  approved_by?: string;
  approved_at?: string;
  created_at: string;
  updated_at?: string;
  salary_components: SalaryComponent[];
}

export interface SalarySlipCreate {
  employee_id: string;
  salary_id?: string;
  pay_period_month: number;
  pay_period_year: number;
  pay_date?: string;
  status?: 'draft' | 'generated' | 'approved' | 'paid' | 'cancelled';
  total_working_days?: number;
  actual_working_days?: number;
  remarks?: string;
  salary_components: SalaryComponentCreate[];
}

export interface SalarySlipUpdate {
  pay_date?: string;
  status?: 'draft' | 'generated' | 'approved' | 'paid' | 'cancelled';
  total_working_days?: number;
  actual_working_days?: number;
  remarks?: string;
}

export interface SalarySlipFilter {
  employee_id?: string;
  pay_period_month?: number;
  pay_period_year?: number;
  status?: string;
  from_date?: string;
  to_date?: string;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * Salary Slip Service
 * Handles salary slip management including generation, approval,
 * and salary component management.
 */
@Injectable({
  providedIn: 'root'
})
export class SalarySlipService {
  private baseUrl = `${environment.apiUrl}/api/v1/salary-slips`;

  constructor(private http: HttpClient) {}

  /**
   * Get all salary slips with filtering
   * GET /api/v1/salary-slips/
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @returns Observable of salary slips list
   */
  getSalarySlips(
    filter: Partial<SalarySlipFilter> = {},
    skip: number = 0,
    limit: number = 100
  ): Observable<SalarySlip[]> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    // Add filter parameters
    if (filter.employee_id) params = params.set('employee_id', filter.employee_id);
    if (filter.pay_period_month) params = params.set('pay_period_month', filter.pay_period_month.toString());
    if (filter.pay_period_year) params = params.set('pay_period_year', filter.pay_period_year.toString());
    if (filter.status) params = params.set('status', filter.status);
    if (filter.from_date) params = params.set('from_date', filter.from_date);
    if (filter.to_date) params = params.set('to_date', filter.to_date);

    return this.http.get<APIResponse<SalarySlip[]>>(`${this.baseUrl}/`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getSalarySlips', []))
    );
  }

  /**
   * Get a specific salary slip by ID
   * GET /api/v1/salary-slips/{salary_slip_id}
   * @param salarySlipId Salary slip ID
   * @returns Observable of salary slip
   */
  getSalarySlip(salarySlipId: string): Observable<SalarySlip> {
    return this.http.get<APIResponse<SalarySlip>>(`${this.baseUrl}/${salarySlipId}`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Salary slip not found');
      }),
      catchError(this.handleError('getSalarySlip'))
    );
  }

  /**
   * Create a new salary slip
   * POST /api/v1/salary-slips/
   * @param salarySlip Salary slip data
   * @returns Observable of created salary slip
   */
  createSalarySlip(salarySlip: SalarySlipCreate): Observable<SalarySlip> {
    return this.http.post<APIResponse<SalarySlip>>(`${this.baseUrl}/`, salarySlip).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create salary slip');
      }),
      catchError(this.handleError('createSalarySlip'))
    );
  }

  /**
   * Update an existing salary slip
   * PUT /api/v1/salary-slips/{salary_slip_id}
   * @param salarySlipId Salary slip ID
   * @param salarySlip Updated salary slip data
   * @returns Observable of updated salary slip
   */
  updateSalarySlip(salarySlipId: string, salarySlip: SalarySlipUpdate): Observable<SalarySlip> {
    return this.http.put<APIResponse<SalarySlip>>(`${this.baseUrl}/${salarySlipId}`, salarySlip).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to update salary slip');
      }),
      catchError(this.handleError('updateSalarySlip'))
    );
  }

  /**
   * Delete a salary slip
   * DELETE /api/v1/salary-slips/{salary_slip_id}
   * @param salarySlipId Salary slip ID
   * @param hardDelete Whether to permanently delete
   * @returns Observable of operation result
   */
  deleteSalarySlip(salarySlipId: string, hardDelete: boolean = false): Observable<boolean> {
    const params = new HttpParams().set('hard_delete', hardDelete.toString());

    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${salarySlipId}`, { params }).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to delete salary slip');
      }),
      catchError(this.handleError('deleteSalarySlip'))
    );
  }

  /**
   * Generate salary slip for an employee
   * POST /api/v1/salary-slips/{salary_slip_id}/generate
   * @param salarySlipId Salary slip ID
   * @returns Observable of generated salary slip
   */
  generateSalarySlip(salarySlipId: string): Observable<SalarySlip> {
    return this.http.post<APIResponse<SalarySlip>>(`${this.baseUrl}/${salarySlipId}/generate`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to generate salary slip');
      }),
      catchError(this.handleError('generateSalarySlip'))
    );
  }

  /**
   * Approve a salary slip
   * POST /api/v1/salary-slips/{salary_slip_id}/approve
   * @param salarySlipId Salary slip ID
   * @returns Observable of approved salary slip
   */
  approveSalarySlip(salarySlipId: string): Observable<SalarySlip> {
    return this.http.post<APIResponse<SalarySlip>>(`${this.baseUrl}/${salarySlipId}/approve`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to approve salary slip');
      }),
      catchError(this.handleError('approveSalarySlip'))
    );
  }

  /**
   * Download salary slip as PDF
   * GET /api/v1/salary-slips/{salary_slip_id}/download
   * @param salarySlipId Salary slip ID
   * @returns Observable of PDF blob
   */
  downloadSalarySlip(salarySlipId: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/${salarySlipId}/download`, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/pdf'
      }
    }).pipe(
      catchError(this.handleError('downloadSalarySlip'))
    );
  }

  /**
   * Get salary slips for a specific employee
   * @param employeeId Employee ID
   * @param year Optional year filter
   * @param month Optional month filter
   * @returns Observable of employee salary slips
   */
  getEmployeeSalarySlips(employeeId: string, year?: number, month?: number): Observable<SalarySlip[]> {
    const filter: Partial<SalarySlipFilter> = { employee_id: employeeId };
    if (year) filter.pay_period_year = year;
    if (month) filter.pay_period_month = month;

    return this.getSalarySlips(filter);
  }

  // Salary Component Management Methods

  /**
   * Get salary components for a salary slip
   * GET /api/v1/salary-slips/{salary_slip_id}/components
   * @param salarySlipId Salary slip ID
   * @returns Observable of salary components
   */
  getSalaryComponents(salarySlipId: string): Observable<SalaryComponent[]> {
    return this.http.get<APIResponse<SalaryComponent[]>>(`${this.baseUrl}/${salarySlipId}/components`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getSalaryComponents', []))
    );
  }

  /**
   * Add a salary component to a salary slip
   * POST /api/v1/salary-slips/{salary_slip_id}/components
   * @param salarySlipId Salary slip ID
   * @param component Salary component data
   * @returns Observable of created salary component
   */
  addSalaryComponent(salarySlipId: string, component: SalaryComponentCreate): Observable<SalaryComponent> {
    return this.http.post<APIResponse<SalaryComponent>>(`${this.baseUrl}/${salarySlipId}/components`, component).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to add salary component');
      }),
      catchError(this.handleError('addSalaryComponent'))
    );
  }

  /**
   * Update a salary component
   * PUT /api/v1/salary-slips/{salary_slip_id}/components/{component_id}
   * @param salarySlipId Salary slip ID
   * @param componentId Component ID
   * @param component Updated component data
   * @returns Observable of updated salary component
   */
  updateSalaryComponent(
    salarySlipId: string,
    componentId: string,
    component: Partial<SalaryComponentCreate>
  ): Observable<SalaryComponent> {
    return this.http.put<APIResponse<SalaryComponent>>(
      `${this.baseUrl}/${salarySlipId}/components/${componentId}`,
      component
    ).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to update salary component');
      }),
      catchError(this.handleError('updateSalaryComponent'))
    );
  }

  /**
   * Delete a salary component
   * DELETE /api/v1/salary-slips/{salary_slip_id}/components/{component_id}
   * @param salarySlipId Salary slip ID
   * @param componentId Component ID
   * @returns Observable of operation result
   */
  deleteSalaryComponent(salarySlipId: string, componentId: string): Observable<boolean> {
    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${salarySlipId}/components/${componentId}`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to delete salary component');
      }),
      catchError(this.handleError('deleteSalaryComponent'))
    );
  }

  /**
   * Bulk update salary components for a salary slip
   * PUT /api/v1/salary-slips/{salary_slip_id}/components/bulk
   * @param salarySlipId Salary slip ID
   * @param components Array of salary components
   * @returns Observable of updated salary slip
   */
  bulkUpdateSalaryComponents(salarySlipId: string, components: SalaryComponentCreate[]): Observable<SalarySlip> {
    return this.http.put<APIResponse<SalarySlip>>(`${this.baseUrl}/${salarySlipId}/components/bulk`, {
      components
    }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to bulk update salary components');
      }),
      catchError(this.handleError('bulkUpdateSalaryComponents'))
    );
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
