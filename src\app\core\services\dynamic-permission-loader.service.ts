import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, timer, of, throwError } from 'rxjs';
import { catchError, switchMap, tap, retry, shareReplay } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';
import { PermissionService } from './permission.service';

export interface PermissionRefreshConfig {
  autoRefreshInterval: number; // in milliseconds
  maxRetries: number;
  retryDelay: number; // in milliseconds
  enableAutoRefresh: boolean;
}

export interface PermissionLoadResult {
  success: boolean;
  permissions: string[];
  roles: any[];
  timestamp: Date;
  source: 'cache' | 'api' | 'fallback';
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DynamicPermissionLoaderService {
  private readonly baseUrl = environment.apiUrl;
  private refreshSubject = new BehaviorSubject<PermissionLoadResult | null>(null);
  public permissionRefresh$ = this.refreshSubject.asObservable();

  private config: PermissionRefreshConfig = {
    autoRefreshInterval: 5 * 60 * 1000, // 5 minutes
    maxRetries: 3,
    retryDelay: 2000, // 2 seconds
    enableAutoRefresh: true
  };

  private autoRefreshSubscription: any;
  private lastSuccessfulLoad: Date | null = null;
  private isLoading = false;

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private permissionService: PermissionService
  ) {
    this.initializeAutoRefresh();
  }

  /**
   * Load user permissions dynamically from API
   */
  loadUserPermissions(forceRefresh: boolean = false): Observable<PermissionLoadResult> {
    const currentUser = this.authService.currentUserValue;

    if (!currentUser?.id) {
      return of({
        success: false,
        permissions: [],
        roles: [],
        timestamp: new Date(),
        source: 'fallback',
        error: 'No authenticated user'
      });
    }

    // Check if we should use cached data
    if (!forceRefresh && this.shouldUseCachedData()) {
      return of({
        success: true,
        permissions: currentUser.permissions || [],
        roles: currentUser.roles || [],
        timestamp: this.lastSuccessfulLoad || new Date(),
        source: 'cache'
      });
    }

    if (this.isLoading && !forceRefresh) {
      // Return current permissions while loading
      return of({
        success: true,
        permissions: currentUser.permissions || [],
        roles: currentUser.roles || [],
        timestamp: new Date(),
        source: 'cache'
      });
    }

    this.isLoading = true;

    return this.fetchUserRolesAndPermissions(currentUser.id).pipe(
      retry(this.config.maxRetries),
      tap(result => {
        this.isLoading = false;
        if (result.success) {
          this.lastSuccessfulLoad = new Date();
          this.updateUserPermissions(result.permissions, result.roles);
        }
        this.refreshSubject.next(result);
      }),
      catchError(error => {
        this.isLoading = false;
        const errorResult: PermissionLoadResult = {
          success: false,
          permissions: currentUser.permissions || [],
          roles: currentUser.roles || [],
          timestamp: new Date(),
          source: 'fallback',
          error: error.message || 'Failed to load permissions'
        };
        this.refreshSubject.next(errorResult);
        return of(errorResult);
      }),
      shareReplay(1)
    );
  }

  /**
   * Fetch user roles and permissions from API
   * Uses the current user endpoint /me/roles instead of admin endpoint
   */
  private fetchUserRolesAndPermissions(userId: string): Observable<PermissionLoadResult> {
    const apiUrl = `${this.baseUrl}/api/v1/user-roles/me/roles`;

    return this.http.get<any>(apiUrl).pipe(
      switchMap(response => {
        if (response.success && response.data && Array.isArray(response.data)) {
          const roles = response.data;
          const permissions = this.extractPermissionsFromRoles(roles);

          return of({
            success: true,
            permissions,
            roles,
            timestamp: new Date(),
            source: 'api' as const
          });
        } else {
          return throwError(() => new Error('Invalid API response format'));
        }
      }),
      catchError(error => {
        console.error('❌ Failed to fetch user permissions:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Extract permissions from roles array
   */
  private extractPermissionsFromRoles(roles: any[]): string[] {
    const permissions: string[] = [];

    roles.forEach(role => {
      if (role.permissions && Array.isArray(role.permissions)) {
        role.permissions.forEach((permission: any) => {
          if (permission.name && !permissions.includes(permission.name)) {
            permissions.push(permission.name);
          }
        });
      }
    });

    return permissions;
  }

  /**
   * Update user permissions in AuthService
   */
  private updateUserPermissions(permissions: string[], roles: any[]): void {
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      const updatedUser = {
        ...currentUser,
        permissions,
        roles: roles.map(r => r.name)
      };

      // Update user in AuthService
      this.authService.updateCurrentUser(updatedUser);

      console.log('✅ Permissions updated dynamically:', {
        permissionCount: permissions.length,
        roleCount: roles.length,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Check if cached data should be used
   */
  private shouldUseCachedData(): boolean {
    if (!this.lastSuccessfulLoad) return false;

    const cacheAge = Date.now() - this.lastSuccessfulLoad.getTime();
    const maxCacheAge = this.config.autoRefreshInterval / 2; // Use cache for half the refresh interval

    return cacheAge < maxCacheAge;
  }

  /**
   * Initialize auto-refresh mechanism
   */
  private initializeAutoRefresh(): void {
    if (!this.config.enableAutoRefresh) return;

    // Start auto-refresh timer
    this.autoRefreshSubscription = timer(
      this.config.autoRefreshInterval,
      this.config.autoRefreshInterval
    ).subscribe(() => {
      if (this.authService.isLoggedIn()) {
        console.log('🔄 Auto-refreshing user permissions...');
        this.loadUserPermissions(true).subscribe();
      }
    });
  }

  /**
   * Manually refresh permissions
   */
  refreshPermissions(): Observable<PermissionLoadResult> {
    console.log('🔄 Manually refreshing permissions...');
    return this.loadUserPermissions(true);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PermissionRefreshConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Restart auto-refresh with new config
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
    }
    this.initializeAutoRefresh();
  }

  /**
   * Get current configuration
   */
  getConfig(): PermissionRefreshConfig {
    return { ...this.config };
  }

  /**
   * Stop auto-refresh
   */
  stopAutoRefresh(): void {
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
      this.autoRefreshSubscription = null;
    }
  }

  /**
   * Start auto-refresh
   */
  startAutoRefresh(): void {
    this.stopAutoRefresh();
    this.initializeAutoRefresh();
  }

  /**
   * Get last successful load timestamp
   */
  getLastRefreshTime(): Date | null {
    return this.lastSuccessfulLoad;
  }

  /**
   * Check if currently loading
   */
  isCurrentlyLoading(): boolean {
    return this.isLoading;
  }

  /**
   * Cleanup on service destruction
   */
  ngOnDestroy(): void {
    this.stopAutoRefresh();
  }
}
