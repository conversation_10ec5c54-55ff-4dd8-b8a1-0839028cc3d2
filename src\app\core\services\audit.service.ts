import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Audit interfaces based on OpenAPI specification
 */
export interface AuditEvent {
  id: string;
  event_type: string;
  entity_type: string;
  entity_id: string;
  user_id?: string;
  trace_id?: string;
  timestamp: string;
  severity: string;
  message: string;
  metadata?: any;
  is_archived: boolean;
}

export interface AuditEventFilter {
  event_types?: string[];
  entity_types?: string[];
  entity_ids?: string[];
  user_ids?: string[];
  trace_ids?: string[];
  start_date?: string;
  end_date?: string;
  severities?: string[];
  include_archived?: boolean;
  search_term?: string;
}

export interface AuditEventUpdate {
  severity?: string;
  message?: string;
  metadata?: any;
  is_archived?: boolean;
}

export interface AuditEventStats {
  total_events: number;
  recent_events: number;
  events_by_type: { [key: string]: number };
  events_by_severity: { [key: string]: number };
  archived_events: number;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * Audit Service
 * Handles audit log management including filtering, statistics,
 * archiving, and entity-specific audit trails.
 */
@Injectable({
  providedIn: 'root'
})
export class AuditService {
  private baseUrl = `${environment.apiUrl}/api/v1/audit`;

  constructor(private http: HttpClient) {}

  /**
   * Filter audit logs with various criteria (admin only)
   * GET /api/v1/audit/
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param sortBy Field to sort by
   * @param sortOrder Sort order (asc or desc)
   * @returns Observable of filtered audit events
   */
  filterAuditLogs(
    filter: Partial<AuditEventFilter> = {},
    skip: number = 0,
    limit: number = 10,
    sortBy: string = 'timestamp',
    sortOrder: string = 'desc'
  ): Observable<{ events: AuditEvent[], total: number }> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('sort_by', sortBy)
      .set('sort_order', sortOrder);

    // Add filter parameters
    if (filter.event_types?.length) {
      filter.event_types.forEach(type => params = params.append('event_types', type));
    }
    if (filter.entity_types?.length) {
      filter.entity_types.forEach(type => params = params.append('entity_types', type));
    }
    if (filter.entity_ids?.length) {
      filter.entity_ids.forEach(id => params = params.append('entity_ids', id));
    }
    if (filter.user_ids?.length) {
      filter.user_ids.forEach(id => params = params.append('user_ids', id));
    }
    if (filter.trace_ids?.length) {
      filter.trace_ids.forEach(id => params = params.append('trace_ids', id));
    }
    if (filter.start_date) {
      params = params.set('start_date', filter.start_date);
    }
    if (filter.end_date) {
      params = params.set('end_date', filter.end_date);
    }
    if (filter.severities?.length) {
      filter.severities.forEach(severity => params = params.append('severities', severity));
    }
    if (filter.include_archived !== undefined) {
      params = params.set('include_archived', filter.include_archived.toString());
    }
    if (filter.search_term) {
      params = params.set('search_term', filter.search_term);
    }

    return this.http.get<APIResponse<any>>(`${this.baseUrl}/`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return {
            events: response.data.events || [],
            total: response.data.total || 0
          };
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('filterAuditLogs', { events: [], total: 0 }))
    );
  }

  /**
   * Filter audit logs using POST with complex filter (admin only)
   * POST /api/v1/audit/filter
   * @param filterParams Complex filter parameters
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param sortBy Field to sort by
   * @param sortOrder Sort order (asc or desc)
   * @returns Observable of filtered audit events
   */
  filterAuditLogsPost(
    filterParams: AuditEventFilter,
    skip: number = 0,
    limit: number = 10,
    sortBy: string = 'timestamp',
    sortOrder: string = 'desc'
  ): Observable<{ events: AuditEvent[], total: number }> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('sort_by', sortBy)
      .set('sort_order', sortOrder);

    return this.http.post<APIResponse<any>>(`${this.baseUrl}/filter`, filterParams, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return {
            events: response.data.events || [],
            total: response.data.total || 0
          };
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('filterAuditLogsPost', { events: [], total: 0 }))
    );
  }

  /**
   * Get audit statistics (admin only)
   * GET /api/v1/audit/stats
   * @param days Number of days to include in recent events count
   * @returns Observable of audit statistics
   */
  getAuditStats(days: number = 30): Observable<AuditEventStats> {
    const params = new HttpParams().set('days', days.toString());

    return this.http.get<APIResponse<AuditEventStats>>(`${this.baseUrl}/stats`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('getAuditStats'))
    );
  }

  /**
   * Update an audit event (admin only)
   * PUT /api/v1/audit/{event_id}
   * @param eventId Event ID to update
   * @param updateData Data to update
   * @returns Observable of updated audit event
   */
  updateAuditEvent(eventId: string, updateData: AuditEventUpdate): Observable<AuditEvent> {
    return this.http.put<APIResponse<AuditEvent>>(`${this.baseUrl}/${eventId}`, updateData).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to update audit event');
      }),
      catchError(this.handleError('updateAuditEvent'))
    );
  }

  /**
   * Archive audit events (admin only)
   * POST /api/v1/audit/archive
   * @param olderThanDays Archive events older than this many days
   * @param entityType Archive events for this entity type
   * @param entityId Archive events for this entity ID
   * @returns Observable of number of events archived
   */
  archiveAuditEvents(
    olderThanDays?: number,
    entityType?: string,
    entityId?: string
  ): Observable<{ archived_count: number }> {
    const body: any = {};
    if (olderThanDays !== undefined) body.older_than_days = olderThanDays;
    if (entityType) body.entity_type = entityType;
    if (entityId) body.entity_id = entityId;

    return this.http.post<APIResponse<{ archived_count: number }>>(`${this.baseUrl}/archive`, body).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to archive audit events');
      }),
      catchError(this.handleError('archiveAuditEvents'))
    );
  }

  /**
   * Delete archived audit events (admin only)
   * POST /api/v1/audit/delete-archived
   * @param olderThanDays Delete events older than this many days
   * @returns Observable of number of events deleted
   */
  deleteArchivedAuditEvents(olderThanDays?: number): Observable<{ deleted_count: number }> {
    return this.http.post<APIResponse<{ deleted_count: number }>>(`${this.baseUrl}/delete-archived`, olderThanDays).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to delete archived audit events');
      }),
      catchError(this.handleError('deleteArchivedAuditEvents'))
    );
  }

  /**
   * Get audit logs for an entity (admin only)
   * GET /api/v1/audit/entity/{entity_type}/{entity_id}
   * @param entityType Type of entity
   * @param entityId ID of the entity
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeArchived Whether to include archived events
   * @returns Observable of entity audit events
   */
  getEntityAuditLogs(
    entityType: string,
    entityId: string,
    skip: number = 0,
    limit: number = 10,
    includeArchived: boolean = false
  ): Observable<AuditEvent[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_archived', includeArchived.toString());

    return this.http.get<APIResponse<AuditEvent[]>>(`${this.baseUrl}/entity/${entityType}/${entityId}`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getEntityAuditLogs', []))
    );
  }

  /**
   * Get audit logs for a user (admin only)
   * GET /api/v1/audit/user/{user_id}
   * @param userId ID of the user
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeArchived Whether to include archived events
   * @returns Observable of user audit events
   */
  getUserAuditLogs(
    userId: string,
    skip: number = 0,
    limit: number = 10,
    includeArchived: boolean = false
  ): Observable<AuditEvent[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_archived', includeArchived.toString());

    return this.http.get<APIResponse<AuditEvent[]>>(`${this.baseUrl}/user/${userId}`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getUserAuditLogs', []))
    );
  }

  /**
   * Get audit logs for a trace (admin only)
   * GET /api/v1/audit/trace/{trace_id}
   * @param traceId Trace ID
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeArchived Whether to include archived events
   * @returns Observable of trace audit events
   */
  getTraceAuditLogs(
    traceId: string,
    skip: number = 0,
    limit: number = 10,
    includeArchived: boolean = false
  ): Observable<AuditEvent[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_archived', includeArchived.toString());

    return this.http.get<APIResponse<AuditEvent[]>>(`${this.baseUrl}/trace/${traceId}`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getTraceAuditLogs', []))
    );
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
