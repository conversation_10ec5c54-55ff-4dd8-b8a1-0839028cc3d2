import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of, Subject, BehaviorSubject } from 'rxjs';
import { catchError, tap, map, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { HttpErrorHandlerService } from './http-error-handler.service';
import { AuthService } from './auth.service';
import { EmployeeService } from './employee.service';
import { CompoffService, CompoffRequestInput } from './compoff.service';

// Leave Type interfaces
export interface LeaveType {
  id?: number;
  name: string;
  description?: string;
  max_days_per_year?: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface LeaveTypeCreate {
  name: string;
  description?: string;
  max_days_per_year?: number;
  is_active?: boolean;
}

export interface LeaveTypeUpdate {
  name?: string;
  description?: string;
  max_days_per_year?: number;
  is_active?: boolean;
}

// Leave Balance interfaces
export interface LeaveBalance {
  id?: number;
  employee_id: number;
  leave_type_id: number;
  leave_type?: LeaveType;
  total_allocated: number;
  used: number;
  remaining: number;
  year: number;
  created_at?: string;
  updated_at?: string;
}

export interface LeaveBalanceCreate {
  employee_id: number;
  leave_type_id: number;
  total_allocated: number;
  year: number;
}

export interface LeaveBalanceUpdate {
  total_allocated?: number;
  used?: number;
  year?: number;
}

export interface LeaveBalanceSummary {
  employee_id: number;
  year: number;
  balances: LeaveBalance[];
  total_allocated: number;
  total_used: number;
  total_remaining: number;
}

// Leave Application interfaces
export interface Leave {
  id?: string; // UUID format from API
  employee_id: string; // UUID format from API
  leave_type_id?: number;
  leave_type: string; // enum string like "privilege_leave"
  start_date: string;
  end_date: string;
  days: number; // Changed from days_requested to days
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'declined' | 'cancelled';
  rejected_reason?: string | null;
  approved_by?: string; // UUID format from API
  approved_by_name?: string; // Approver name from API
  approved_at?: string; // Date string from API
  document_url?: string | null;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string | null;
}

export interface LeaveCreate {
  leave_type_id: number;
  leave_type: string;  // API expects enum string value
  employee_id: string; // API expects UUID string
  start_date: string;
  end_date: string;
  reason: string;
}

export interface LeaveUpdate {
  leave_type_id?: number;
  start_date?: string;
  end_date?: string;
  reason?: string;
  status?: 'pending' | 'approved' | 'rejected' | 'declined' | 'cancelled';
}

// Comp-off specific interfaces - Updated for dedicated API endpoints
export interface CompoffRequest {
  working_date: string; // Date when employee worked (YYYY-MM-DD format)
  reason: string; // Reason for comp-off request
  employee_id?: string; // Optional - defaults to current user
}

export interface CompoffRequestResponse {
  id: string; // UUID of the comp-off request
  employee_id: string; // UUID of the employee
  working_date: string; // Date when employee worked
  reason: string; // Reason for comp-off request
  status: 'pending' | 'approved' | 'rejected'; // Request status
  requested_at: string; // When the request was made
  approved_by?: string; // UUID of approver (if approved)
  approved_at?: string; // When it was approved
  rejection_reason?: string; // Reason for rejection (if rejected)
  created_at: string;
  updated_at?: string;
}

export interface CompoffApprovalRequest {
  action: 'approve' | 'reject';
  comments?: string; // Optional comments for approval/rejection
}

// Alternative interface for status-based comp-off approval (used by approve-leaves component)
export interface CompoffStatusApprovalRequest {
  status: 'APPROVED' | 'REJECTED';
  comments?: string; // Optional comments for approval/rejection
}

// Legacy interface - will be deprecated
export interface CompoffAssignment {
  employee_ids: string[]; // Array of employee UUIDs to assign comp-off to
  working_date: string; // Date when employees worked (YYYY-MM-DD format)
  remark: string; // Admin remark for the assignment
  days?: number; // Number of comp-off days to assign (default: 1)
}

// Manager comp-off assignment interface (for approve-leaves component)
export interface ManagerCompoffAssignment {
  employee_id: string; // UUID of the employee to assign comp-off to
  working_date: string; // Date when employee worked (YYYY-MM-DD format)
  reason: string; // Reason for the comp-off assignment
  request_type: 'manager_assignment'; // Type of comp-off request
}

export interface LeaveFilter {
  employee_id?: number;
  leave_type_id?: number;
  status?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  size?: number;
}

export interface LeaveApproval {
  leave_id: number;
  action: 'approve' | 'reject';
  comments?: string;
}

// Leave Type Configuration interfaces (from OpenAPI spec)
export interface LeaveTypeConfig {
  id: string;
  code: string;
  type: string; // enum from LeaveType
  name: string;
  description?: string;
  max_days_per_year?: number;
  max_days_per_application?: number;
  carry_forward: boolean;
  max_carry_forward?: number;
  requires_approval: boolean;
  min_days_notice: number;
  documents_required: boolean;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface LeaveTypeConfigCreate {
  code: string;
  type: string; // enum from LeaveType
  name: string;
  description?: string;
  max_days_per_year?: number;
  max_days_per_application?: number;
  carry_forward?: boolean;
  max_carry_forward?: number;
  requires_approval?: boolean;
  min_days_notice?: number;
  documents_required?: boolean;
}

export interface LeaveTypeConfigUpdate {
  name?: string;
  description?: string;
  max_days_per_year?: number;
  max_days_per_application?: number;
  carry_forward?: boolean;
  max_carry_forward?: number;
  requires_approval?: boolean;
  min_days_notice?: number;
  documents_required?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LeaveService {
  private baseUrl = `${environment.apiUrl}/api/v1/leave`;

  // Subject to notify components when leave data needs to be reloaded
  private reloadLeaveDataSubject = new Subject<void>();
  public reloadLeaveData$ = this.reloadLeaveDataSubject.asObservable();

  // BehaviorSubject to track loading state
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(
    private http: HttpClient,
    private httpErrorHandler: HttpErrorHandlerService,
    private authService: AuthService,
    private employeeService: EmployeeService,
    private compoffService: CompoffService
  ) { }

  /**
   * Trigger reload of all leave-related data across components
   * This method should be called after any leave action (create, approve, reject, cancel)
   */
  triggerLeaveDataReload(): void {
    console.log('🔄 Triggering leave data reload across all components...');
    this.reloadLeaveDataSubject.next();
  }

  /**
   * Set loading state for all components
   */
  setLoadingState(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  // Map leave type names to API enum values
  private mapLeaveTypeToEnum(leaveTypeName: string): string {
    const leaveTypeMap: { [key: string]: string } = {
      'PL': 'privilege_leave',
      'Privilege Leave': 'privilege_leave',
      'CL': 'casual_leave',
      'Casual Leave': 'casual_leave',
      'SL': 'sick_leave',
      'Sick Leave': 'sick_leave',
      'ML': 'maternity_leave',
      'Maternity Leave': 'maternity_leave',
      'PaL': 'paternity_leave',
      'Paternity Leave': 'paternity_leave',
      'BL': 'bereavement_leave',
      'Bereavement Leave': 'bereavement_leave',
      'OD': 'outdoor_duty',
      'Outdoor Duty': 'outdoor_duty',
      'LWP': 'leave_without_pay',
      'Leave Without Pay': 'leave_without_pay',
      'COMP': 'compensatory_off',
      'Compensatory Off': 'compensatory_off',
      'Comp Off': 'compensatory_off',
      'ITL': 'intern_trainee_leave',
      'Intern Trainee Leave': 'intern_trainee_leave'
    };

    return leaveTypeMap[leaveTypeName] || 'privilege_leave'; // Default fallback
  }

  // Public method to get leave type enum value
  getLeaveTypeEnum(leaveTypeName: string): string {
    return this.mapLeaveTypeToEnum(leaveTypeName);
  }

  // Error handling method
  private handleError(operation = 'operation', result?: any) {
    return (error: HttpErrorResponse): Observable<any> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return empty result to let the app continue
      if (result !== undefined) {
        return of(result);
      }

      return throwError(() => error);
    };
  }

  // Test API endpoint availability
  testApiEndpoint(endpoint: string): Observable<boolean> {
    return this.http.get(`${this.baseUrl}${endpoint}`).pipe(
      map(() => true),
      catchError(() => of(false)),
      tap(available => console.log(`API endpoint ${endpoint} available:`, available))
    );
  }

  // Test all leave API endpoints
  testAllEndpoints(): Observable<{[key: string]: boolean}> {
    const endpoints = ['/types', '/balance', '/', '/balance/summary'];
    const tests = endpoints.map(endpoint =>
      this.testApiEndpoint(endpoint).pipe(
        map(available => ({ [endpoint]: available }))
      )
    );

    return of({}).pipe(
      tap(() => console.log('Testing all leave API endpoints...')),
      map(() => {
        const results: {[key: string]: boolean} = {};
        return results;
      })
    );
  }

  // Leave Types Management
  getLeaveTypes(): Observable<any> {
    console.log('Fetching leave types from:', `${this.baseUrl}/types`);
    return this.http.get<any>(`${this.baseUrl}/types`).pipe(
      tap(response => console.log('Leave types response:', response)),
      catchError(this.handleError('getLeaveTypes', []))
    );
  }

  createLeaveType(leaveType: LeaveTypeCreate): Observable<LeaveType> {
    return this.http.post<LeaveType>(`${this.baseUrl}/types`, leaveType);
  }

  getLeaveType(leaveTypeId: number): Observable<LeaveType> {
    return this.http.get<LeaveType>(`${this.baseUrl}/types/${leaveTypeId}`);
  }

  updateLeaveType(leaveTypeId: number, leaveType: LeaveTypeUpdate): Observable<LeaveType> {
    return this.http.put<LeaveType>(`${this.baseUrl}/types/${leaveTypeId}`, leaveType);
  }

  deleteLeaveType(leaveTypeId: number): Observable<any> {
    return this.http.delete(`${this.baseUrl}/types/${leaveTypeId}`);
  }

  // Leave Balance Management
  getMyLeaveBalance(): Observable<LeaveBalance[]> {
    console.log('Fetching leave balance from:', `${this.baseUrl}/balance`);

    // Add header to skip error interceptor for comp-off service compatibility
    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<LeaveBalance[]>(`${this.baseUrl}/balance`, { headers }).pipe(
      tap(response => console.log('Leave balance response:', response)),
      catchError(error => {
        console.error('Leave balance API failed, returning mock data:', error);
        // Return mock data if API fails
        return of(this.getMockLeaveBalances());
      })
    );
  }

  // Mock data for testing when API is not available
  private getMockLeaveBalances(): LeaveBalance[] {
    return [
      {
        id: 1,
        employee_id: 1,
        leave_type_id: 1,
        leave_type: { id: 1, name: 'PL', description: 'Privilege Leave' },
        total_allocated: 21,
        used: 5,
        remaining: 16,
        year: new Date().getFullYear()
      },
      {
        id: 2,
        employee_id: 1,
        leave_type_id: 2,
        leave_type: { id: 2, name: 'SL', description: 'Sick Leave' },
        total_allocated: 12,
        used: 2,
        remaining: 10,
        year: new Date().getFullYear()
      },
      {
        id: 3,
        employee_id: 1,
        leave_type_id: 3,
        leave_type: { id: 3, name: 'CL', description: 'Casual Leave' },
        total_allocated: 12,
        used: 3,
        remaining: 9,
        year: new Date().getFullYear()
      }
    ];
  }

  createLeaveBalance(leaveBalance: LeaveBalanceCreate): Observable<LeaveBalance> {
    return this.http.post<LeaveBalance>(`${this.baseUrl}/balance`, leaveBalance);
  }

  getMyLeaveBalanceSummary(): Observable<LeaveBalanceSummary> {
    return this.http.get<LeaveBalanceSummary>(`${this.baseUrl}/balance/summary`);
  }

  getEmployeeLeaveBalance(employeeId: number): Observable<LeaveBalance[]> {
    return this.http.get<LeaveBalance[]>(`${this.baseUrl}/balance/${employeeId}`);
  }

  updateLeaveBalance(balanceId: number, leaveBalance: LeaveBalanceUpdate): Observable<LeaveBalance> {
    return this.http.put<LeaveBalance>(`${this.baseUrl}/balance/${balanceId}`, leaveBalance);
  }

  // Leave Applications Management
  getMyLeaves(skip: number = 0, limit: number = 10): Observable<any> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    console.log('Fetching my leaves from:', `${this.baseUrl}/`, 'with params:', params.toString());
    return this.http.get<any>(`${this.baseUrl}/`, { params }).pipe(
      tap(response => console.log('My leaves response:', response)),
      catchError(this.handleError('getMyLeaves', []))
    );
  }

  /**
   * Get my leaves without pagination (backward compatibility)
   */
  getMyLeavesLegacy(): Observable<any> {
    return this.getMyLeaves(0, 1000); // Get up to 1000 leaves
  }

  createLeave(leave: LeaveCreate): Observable<Leave> {
    console.log('Creating leave application:', leave);
    console.log('POST URL:', `${this.baseUrl}/`);
    return this.http.post<Leave>(`${this.baseUrl}/`, leave).pipe(
      tap(response => console.log('Create leave response:', response)),
      catchError(error => {
        console.error('Create leave failed:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create leave with custom error handling (skips automatic error interceptor)
   */
  createLeaveWithCustomErrorHandling(leave: LeaveCreate): Observable<Leave> {
    console.log('Creating leave application with custom error handling:', leave);
    console.log('POST URL:', `${this.baseUrl}/`);

    return this.httpErrorHandler.post<Leave>(`${this.baseUrl}/`, leave, {
      errorHandling: { skipErrorInterceptor: true }
    }).pipe(
      tap(response => {
        console.log('Create leave response:', response);
        // Trigger reload of all leave data after successful creation
        this.triggerLeaveDataReload();
      }),
      catchError(error => {
        console.error('Create leave failed:', error);
        return throwError(() => error);
      })
    );
  }

  filterLeaves(filter: LeaveFilter): Observable<Leave[]> {
    return this.http.post<Leave[]>(`${this.baseUrl}/filter`, filter);
  }

  getLeave(leaveId: number): Observable<Leave> {
    return this.http.get<Leave>(`${this.baseUrl}/${leaveId}`);
  }

  updateLeave(leaveId: number, leave: LeaveUpdate): Observable<Leave> {
    return this.http.put<Leave>(`${this.baseUrl}/${leaveId}`, leave);
  }

  approveLeave(leaveId: string | number, comments?: string): Observable<Leave> {
    console.log('✅ APPROVE LEAVE - Sending approval request with UPPERCASE status');

    const payload = {
      status: "APPROVED",  // API expects UPPERCASE status
      comments: comments || ""
    };

    console.log('📤 Approval payload:', JSON.stringify(payload, null, 2));
    console.log('🌐 Request URL:', `${this.baseUrl}/${leaveId}/approve`);

    return this.http.post<Leave>(`${this.baseUrl}/${leaveId}/approve`, payload).pipe(
      tap(response => {
        console.log('✅ Leave approved successfully:', response);
        // Trigger reload of all leave data after successful approval
        this.triggerLeaveDataReload();
      }),
      catchError(error => {
        console.error('❌ Approve leave failed:', error);
        return throwError(() => error);
      })
    );
  }

  rejectLeave(leaveId: string | number, reason: string): Observable<Leave> {
    console.log('❌ REJECT LEAVE - Sending rejection request with UPPERCASE status');
    console.log('🔄 Rejecting leave:', { leaveId, reason });

    // API expects UPPERCASE "REJECTED" status
    const payload = {
      status: "REJECTED" as const,  // API expects UPPERCASE status
      rejection_reason: reason
    };

    console.log('📤 FINAL REJECTION PAYLOAD:', JSON.stringify(payload, null, 2));
    console.log('🌐 Request URL:', `${this.baseUrl}/${leaveId}/approve`);
    console.log('🔍 Payload status field:', payload.status);
    console.log('✅ Confirming status is "REJECTED":', payload.status === "REJECTED");

    return this.http.post<Leave>(`${this.baseUrl}/${leaveId}/approve`, payload).pipe(
      tap(response => {
        console.log('✅ Leave rejected successfully:', response);
        // Trigger reload of all leave data after successful rejection
        this.triggerLeaveDataReload();
      }),
      catchError(error => {
        console.error('❌ Reject leave API Error:', error);
        return throwError(() => error);
      })
    );
  }

  cancelLeave(leaveId: string | number, reason?: string): Observable<Leave> {
    console.log('🗑️ Cancelling leave with DELETE request:', { leaveId, reason });

    // Use DELETE request for leave cancellation
    const options = {
      body: {
        reason: reason || "Cancelled by user"
      }
    };

    console.log('📤 DELETE request URL:', `${this.baseUrl}/${leaveId}`);
    console.log('📤 DELETE request body:', options.body);

    return this.http.delete<Leave>(`${this.baseUrl}/${leaveId}/cancel`, options).pipe(
      tap(response => {
        console.log('📥 DELETE response received:', response);
        // Trigger reload of all leave data after successful cancellation
        this.triggerLeaveDataReload();
      }),
      catchError(error => {
        console.error('❌ DELETE request failed:', error);
        return throwError(() => error);
      })
    );
  }

  getPendingApprovals(skip: number = 0, limit: number = 10): Observable<Leave[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    return this.http.get<Leave[]>(`${this.baseUrl}/approvals/pending`, { params });
  }

  getApprovedLeaves(skip: number = 0, limit: number = 10): Observable<Leave[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    return this.http.get<Leave[]>(`${this.baseUrl}/approvals/approved`, { params });
  }

  getRejectedLeaves(skip: number = 0, limit: number = 10): Observable<Leave[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    return this.http.get<Leave[]>(`${this.baseUrl}/approvals/rejected`, { params });
  }

  /**
   * Backward compatibility methods for approval lists
   */
  getPendingApprovalsLegacy(): Observable<Leave[]> {
    return this.getPendingApprovals(0, 1000);
  }

  getApprovedLeavesLegacy(): Observable<Leave[]> {
    return this.getApprovedLeaves(0, 1000);
  }

  getRejectedLeavesLegacy(): Observable<Leave[]> {
    return this.getRejectedLeaves(0, 1000);
  }

  /**
   * Get pending comp-off requests for approval
   * GET /api/v1/leave/comp-off/pending
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @returns Observable of pending comp-off requests
   */
  getPendingCompoffApprovals(skip: number = 0, limit: number = 10): Observable<any[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    console.log('📋 Fetching pending comp-off requests for approval');
    console.log('🌐 Request URL:', `${this.baseUrl}/comp-off/pending`, 'with params:', params.toString());

    return this.http.get<any>(`${this.baseUrl}/comp-off/pending`, { params }).pipe(
      map((response: any) => {
        console.log('📥 Pending comp-off API response:', response);

        // Handle different response structures
        let compoffData: any[] = [];
        if (response && response.success && Array.isArray(response.data)) {
          // Structured response: { success: true, data: [...] }
          compoffData = response.data;
        } else if (Array.isArray(response)) {
          // Direct array response
          compoffData = response;
        } else {
          console.warn('⚠️ Unexpected comp-off API response structure:', response);
          compoffData = [];
        }

        console.log('✅ Processed pending comp-off requests:', compoffData.length, 'requests');
        return compoffData;
      }),
      catchError(error => {
        console.error('❌ Error fetching pending comp-off requests:', error);
        return of([]); // Return empty array on error to prevent breaking the UI
      })
    );
  }

  /**
   * Get pending comp-off requests without pagination (backward compatibility)
   */
  getPendingCompoffApprovalsLegacy(): Observable<any[]> {
    return this.getPendingCompoffApprovals(0, 1000);
  }

  // Helper methods
  calculateDaysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const timeDifference = end.getTime() - start.getTime();
    return Math.ceil(timeDifference / (1000 * 3600 * 24)) + 1;
  }

  formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  getWorkingDaysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let workingDays = 0;

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      if (!this.isWeekend(date)) {
        workingDays++;
      }
    }

    return workingDays;
  }

  // Leave Type Configuration Management (from OpenAPI spec)

  /**
   * Get all leave type configurations
   * GET /api/v1/leave/types/config
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted configurations
   * @returns Observable of leave type configurations
   */
  getLeaveTypeConfigs(skip: number = 0, limit: number = 100, includeDeleted: boolean = false): Observable<LeaveTypeConfig[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<any>(`${this.baseUrl}/types/config`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return response || [];
      }),
      catchError(this.handleError('getLeaveTypeConfigs', []))
    );
  }

  /**
   * Get a specific leave type configuration by ID
   * GET /api/v1/leave/types/config/{config_id}
   * @param configId Configuration ID
   * @param includeDeleted Whether to include soft-deleted configurations
   * @returns Observable of leave type configuration
   */
  getLeaveTypeConfig(configId: string, includeDeleted: boolean = false): Observable<LeaveTypeConfig> {
    const params = new HttpParams().set('include_deleted', includeDeleted.toString());

    return this.http.get<any>(`${this.baseUrl}/types/config/${configId}`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return response;
      }),
      catchError(this.handleError('getLeaveTypeConfig'))
    );
  }

  /**
   * Create a new leave type configuration
   * POST /api/v1/leave/types/config
   * @param config Leave type configuration data
   * @returns Observable of created configuration
   */
  createLeaveTypeConfig(config: LeaveTypeConfigCreate): Observable<LeaveTypeConfig> {
    return this.http.post<any>(`${this.baseUrl}/types/config`, config).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return response;
      }),
      catchError(this.handleError('createLeaveTypeConfig'))
    );
  }

  /**
   * Update an existing leave type configuration
   * PUT /api/v1/leave/types/config/{config_id}
   * @param configId Configuration ID
   * @param config Updated configuration data
   * @returns Observable of updated configuration
   */
  updateLeaveTypeConfig(configId: string, config: LeaveTypeConfigUpdate): Observable<LeaveTypeConfig> {
    return this.http.put<any>(`${this.baseUrl}/types/config/${configId}`, config).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return response;
      }),
      catchError(this.handleError('updateLeaveTypeConfig'))
    );
  }

  /**
   * Delete a leave type configuration (soft delete)
   * DELETE /api/v1/leave/types/config/{config_id}
   * @param configId Configuration ID
   * @param hardDelete Whether to permanently delete
   * @returns Observable of operation result
   */
  deleteLeaveTypeConfig(configId: string, hardDelete: boolean = false): Observable<boolean> {
    const params = new HttpParams().set('hard_delete', hardDelete.toString());

    return this.http.delete<any>(`${this.baseUrl}/types/config/${configId}`, { params }).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        return true;
      }),
      catchError(this.handleError('deleteLeaveTypeConfig'))
    );
  }

  /**
   * Restore a soft-deleted leave type configuration
   * POST /api/v1/leave/types/config/{config_id}/restore
   * @param configId Configuration ID
   * @returns Observable of restored configuration
   */
  restoreLeaveTypeConfig(configId: string): Observable<LeaveTypeConfig> {
    return this.http.post<any>(`${this.baseUrl}/types/config/${configId}/restore`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return response;
      }),
      catchError(this.handleError('restoreLeaveTypeConfig'))
    );
  }

  /**
   * Get leave type configuration by leave type enum
   * @param leaveType Leave type enum (e.g., 'privilege_leave')
   * @returns Observable of leave type configuration
   */
  getLeaveTypeConfigByType(leaveType: string): Observable<LeaveTypeConfig | null> {
    return this.getLeaveTypeConfigs().pipe(
      map(configs => {
        const config = configs.find(c => c.type === leaveType);
        return config || null;
      })
    );
  }

  // ==========================================
  // COMP-OFF SPECIFIC METHODS - NEW DEDICATED API ENDPOINTS
  // ==========================================

  /**
   * Request comp-off for current user using dedicated API endpoint
   * Delegates to the dedicated CompoffService
   * @param compoffRequest Comp-off request input data (working_date, reason)
   * @returns Observable of comp-off request response
   */
  requestCompoff(compoffRequest: CompoffRequestInput): Observable<CompoffRequestResponse> {
    console.log('🎯 Delegating comp-off request to dedicated service:', compoffRequest);

    return this.compoffService.requestCompoff(compoffRequest).pipe(
      tap(response => {
        console.log('✅ Comp-off request successful, triggering data reload');
        // Trigger reload of all leave data after successful request
        this.triggerLeaveDataReload();
      }),
      catchError(error => {
        console.error('❌ Comp-off request failed in leave service:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get my comp-off requests using dedicated API endpoint
   * Delegates to the dedicated CompoffService
   * @returns Observable of comp-off request responses
   */
  getMyCompoffRequests(): Observable<CompoffRequestResponse[]> {
    console.log('📋 Delegating to dedicated comp-off service for my requests');
    return this.compoffService.getMyCompoffRequests();
  }

  /**
   * Get pending comp-off requests for admin/manager review
   * Delegates to the dedicated CompoffService
   * @returns Observable of pending comp-off requests
   */
  getPendingCompoffRequests(): Observable<CompoffRequestResponse[]> {
    console.log('📋 Delegating to dedicated comp-off service for pending requests');
    return this.compoffService.getPendingCompoffRequests();
  }

  /**
   * Approve or reject a comp-off request (action-based payload)
   * Delegates to the dedicated CompoffService
   * @param requestId UUID of the comp-off request
   * @param approval Approval/rejection data with action field
   * @returns Observable of updated comp-off request
   */
  approveCompoffRequest(requestId: string, approval: CompoffApprovalRequest | any): Observable<CompoffRequestResponse> {
    console.log('🔄 Processing comp-off approval request:', { requestId, approval });

    // Check if this is a status-based payload (from approve-leaves component)
    if ('status' in approval) {
      console.log('📤 Status-based payload detected, making direct API call');
      console.log('🌐 Request URL:', `${this.baseUrl}/comp-off/${requestId}/approve`);
      console.log('📤 Payload:', approval);

      // Make direct API call with status-based payload
      return this.http.post<CompoffRequestResponse>(`${this.baseUrl}/comp-off/${requestId}/approve`, approval).pipe(
        tap(response => {
          console.log('✅ Comp-off approval successful (direct API):', response);
          // Trigger reload of all leave data after successful approval
          this.triggerLeaveDataReload();
        }),
        catchError(error => {
          console.error('❌ Comp-off approval failed (direct API):', error);
          console.error('❌ Error details:', {
            status: error.status,
            statusText: error.statusText,
            error: error.error,
            url: `${this.baseUrl}/comp-off/${requestId}/approve`,
            payload: approval
          });
          return throwError(() => error);
        })
      );
    } else {
      // Use existing service for action-based payloads
      console.log('🔄 Action-based payload, delegating to CompoffService');
      return this.compoffService.approveCompoffRequest(requestId, approval).pipe(
        tap(response => {
          console.log('✅ Comp-off approval successful, triggering data reload');
          // Trigger reload of all leave data after successful approval
          this.triggerLeaveDataReload();
        }),
        catchError(error => {
          console.error('❌ Comp-off approval failed in leave service:', error);
          return throwError(() => error);
        })
      );
    }
  }

  /**
   * Get comp-off request details by ID
   * Delegates to the dedicated CompoffService
   * @param requestId UUID of the comp-off request
   * @returns Observable of comp-off request details
   */
  getCompoffRequestDetails(requestId: string): Observable<CompoffRequestResponse> {
    console.log('🔍 Delegating to dedicated comp-off service for request details:', requestId);
    return this.compoffService.getCompoffRequestDetails(requestId);
  }

  /**
   * Assign comp-off to an employee (Manager function)
   * Creates a comp-off request with manager_assignment type
   * @param assignment Manager comp-off assignment data
   * @returns Observable of comp-off request response
   */
  assignManagerCompoff(assignment: ManagerCompoffAssignment): Observable<CompoffRequestResponse> {
    console.log('🎯 Manager comp-off assignment:', assignment);

    // Create comp-off request with manager assignment type
    const compoffRequest = {
      working_date: assignment.working_date,
      reason: assignment.reason,
      employee_id: assignment.employee_id,
      request_type: assignment.request_type
    };

    console.log('📤 Delegating manager comp-off assignment to CompoffService:', compoffRequest);

    // Use the CompoffService to create the comp-off request
    return this.compoffService.requestCompoff(compoffRequest).pipe(
      tap(response => {
        console.log('✅ Manager comp-off assignment successful, triggering data reload');
        // Trigger reload of all leave data after successful assignment
        this.triggerLeaveDataReload();
      }),
      catchError(error => {
        console.error('❌ Manager comp-off assignment failed in leave service:', error);
        return throwError(() => error);
      })
    );
  }

  // ==========================================
  // LEGACY COMP-OFF METHODS - DEPRECATED
  // ==========================================

  /**
   * @deprecated Use getMyCompoffRequests() instead
   * Get comp-off applications for current user (legacy method)
   * @returns Observable of comp-off leave applications
   */
  getMyCompoffs(): Observable<Leave[]> {
    console.warn('⚠️ getMyCompoffs() is deprecated. Use getMyCompoffRequests() instead.');
    console.log('📋 Fetching my comp-off applications (legacy method)');

    return this.getMyLeaves().pipe(
      map((response: any) => {
        // Handle different API response structures
        let leaves: Leave[] = [];
        if (response && typeof response === 'object' && response.success && Array.isArray(response.data)) {
          leaves = response.data;
        } else if (Array.isArray(response)) {
          leaves = response;
        }

        // Filter for compensatory_off leaves only
        const compoffs = leaves.filter((leave: Leave) =>
          leave.leave_type === 'compensatory_off' ||
          leave.leave_type === 'Compensatory Off' ||
          leave.leave_type === 'COMP'
        );

        console.log('📋 Filtered comp-off applications:', compoffs);
        return compoffs;
      }),
      catchError((error: any) => {
        console.error('❌ Error fetching comp-off applications:', error);
        return of([]);
      })
    );
  }

  /**
   * @deprecated This functionality is replaced by the approval workflow
   * Assign comp-off to multiple employees (Admin function) - DEPRECATED
   * Creates compensatory_off leave applications for specified employees
   * @param compoffAssignment Comp-off assignment data
   * @returns Observable of assignment results
   */
  assignCompoff(compoffAssignment: CompoffAssignment): Observable<any> {
    console.warn('⚠️ assignCompoff() is deprecated. Comp-off assignment is now handled through the approval workflow.');
    console.log('🎯 Assigning comp-off to employees (legacy method):', compoffAssignment);

    if (!compoffAssignment.employee_ids || compoffAssignment.employee_ids.length === 0) {
      return throwError(() => new Error('No employees selected for comp-off assignment'));
    }

    // Find compensatory_off leave type ID
    const compoffLeaveType = this.getCompoffLeaveType();
    if (!compoffLeaveType) {
      console.error('❌ Compensatory off leave type not found');
      return throwError(() => new Error('Compensatory off leave type not configured'));
    }

    // Create leave applications for each employee
    const assignmentPromises = compoffAssignment.employee_ids.map(employeeId => {
      const leaveData: LeaveCreate = {
        leave_type_id: compoffLeaveType.id || 4,
        leave_type: 'compensatory_off',
        employee_id: employeeId,
        start_date: compoffAssignment.working_date,
        end_date: compoffAssignment.working_date,
        reason: `Comp-off assigned by admin for working on ${compoffAssignment.working_date}. ${compoffAssignment.remark}`
      };

      console.log(`📤 Creating comp-off for employee ${employeeId}:`, leaveData);

      return this.createLeaveWithCustomErrorHandling(leaveData).toPromise();
    });

    // Execute all assignments
    return new Observable(observer => {
      Promise.all(assignmentPromises)
        .then(results => {
          console.log('✅ All comp-off assignments completed:', results);
          observer.next({
            success: true,
            assigned_count: results.length,
            results: results
          });
          observer.complete();
        })
        .catch(error => {
          console.error('❌ Comp-off assignment failed:', error);
          observer.error(error);
        });
    });
  }

  /**
   * Get compensatory off leave type configuration
   * @returns Leave type configuration for compensatory_off
   */
  private getCompoffLeaveType(): LeaveType | null {
    // Return default configuration since we don't have cached leave types
    // This will be used for comp-off requests
    return {
      id: 4, // Default ID for compensatory_off
      name: 'compensatory_off',
      description: 'Compensatory Off',
      max_days_per_year: undefined, // Usually unlimited
      is_active: true
    };
  }



  /**
   * Get current user's employee ID from the employee API
   * Uses the same method as apply-leave component for consistency
   * @returns Observable of employee ID
   */
  private getCurrentEmployeeId(): Observable<string | null> {
    const currentUser = this.authService.currentUserValue;
    console.log('🔍 LeaveService getCurrentEmployeeId() - Full currentUser object:', currentUser);

    if (!currentUser) {
      console.warn('⚠️ No current user found in AuthService');
      return of(null);
    }

    console.log('🔍 Getting employee ID from employee API for user:', currentUser.email);
    console.log('Current user ID:', currentUser?.id);
    console.log('Current user ID type:', typeof currentUser?.id);
    console.log('Current user ID is valid UUID:', currentUser?.id ? this.isValidUUID(currentUser.id) : false);
    console.log('Current user email:', currentUser?.email);
    console.log('Current user employee_code:', (currentUser as any)?.employee_code);

    // Get all employees and find the current user by email (same as apply-leave component)
    return this.employeeService.getAllEmployees().pipe(
      map((response: any) => {
        console.log('🔍 Employee API response:', response);

        let employees: any[] = [];
        if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
          employees = response.data;
        } else if (Array.isArray(response)) {
          employees = response;
        }

        console.log('🔍 Searching for current user in employees:', employees.length, 'employees');

        // Try to find employee by email first, then by employee code (same logic as apply-leave)
        let currentEmployee = employees.find(emp =>
          emp.office_email === currentUser.email ||
          emp.personal_email === currentUser.email
        );

        // If not found by email, try by employee code (if user ID matches employee code)
        if (!currentEmployee) {
          currentEmployee = employees.find(emp =>
            emp.employee_code === currentUser.id.toString()
          );
        }

        if (currentEmployee) {
          // Ensure we have a proper UUID string (same as apply-leave component)
          const currentEmployeeUuid = currentEmployee.id ? currentEmployee.id.toString() : null;
          console.log('✅ Found current employee UUID:', currentEmployeeUuid);
          console.log('Employee UUID type:', typeof currentEmployeeUuid);
          console.log('Employee details:', currentEmployee);

          // Validate that we have a proper UUID format (same as apply-leave)
          if (currentEmployeeUuid && !this.isValidUUID(currentEmployeeUuid)) {
            console.warn('⚠️ Employee ID is not a valid UUID format:', currentEmployeeUuid);
            // Try to get UUID from the current user if available
            const userUuid = currentUser.id;
            if (userUuid && this.isValidUUID(userUuid)) {
              console.log('🔄 Using user UUID instead:', userUuid);
              return userUuid;
            }
          }

          return currentEmployeeUuid;
        } else {
          console.error('❌ Could not find current employee in employee list');
          console.log('❌ Current user email:', currentUser.email);
          console.log('❌ Current user ID:', currentUser.id);
          console.log('❌ Available employees:', employees.map(emp => ({
            id: emp.id,
            employee_code: emp.employee_code,
            office_email: emp.office_email,
            personal_email: emp.personal_email,
            name: `${emp.first_name} ${emp.last_name}`
          })));

          // Fallback: Try to use the current user's ID if it's a valid UUID (same as apply-leave)
          const userUuid = currentUser.id;
          if (userUuid && this.isValidUUID(userUuid)) {
            console.log('🔄 Using current user UUID as fallback:', userUuid);
            return userUuid;
          } else {
            console.error('❌ Current user ID is not a valid UUID:', userUuid);
            return null;
          }
        }
      }),
      catchError(error => {
        console.error('❌ Error getting employee ID from API:', error);
        // Fallback: Try to use the current user's ID if it's a valid UUID
        const userUuid = currentUser.id;
        if (userUuid && this.isValidUUID(userUuid)) {
          console.log('🔄 Using current user UUID as fallback after API error:', userUuid);
          return of(userUuid);
        } else {
          console.error('❌ Current user ID is not a valid UUID:', userUuid);
          return of(null);
        }
      })
    );
  }

  /**
   * Check if a string is a valid UUID format
   * @param uuid String to check
   * @returns True if valid UUID format
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Get current user information (legacy method for backward compatibility)
   * @returns Current user data
   */
  private getCurrentUser(): any {
    const currentUser = this.authService.currentUserValue;
    console.log('🔍 getCurrentUser() - Full currentUser object:', currentUser);

    if (currentUser) {
      const userData = {
        employee_id: currentUser.id, // Use id as employee_id (fallback)
        name: currentUser.name || `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim(),
        email: currentUser.email
      };

      console.log('🔍 getCurrentUser() - Extracted employee_id (fallback):', userData.employee_id);
      console.log('🔍 getCurrentUser() - employee_id type:', typeof userData.employee_id);
      console.log('🔍 getCurrentUser() - Returning userData:', userData);

      return userData;
    }

    console.warn('⚠️ No current user found in AuthService');
    return null;
  }



}
