import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, ViewChild, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbModal, NgbModalModule, NgbDatepickerModule, NgbDate, NgbCalendar, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';

import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { EmployeeService, BulkUploadResponse } from '../../../../core/services/employee.service';
import { NewYearActivityService } from '../../../../core/services/new-year-activity.service';
import { TemplateDownloadService } from '../../../../core/services/template-download.service';
import { AuthService } from '../../../../core/services/auth.service';
import { PermissionService } from '../../../../core/services/permission.service';
import { SessionExpirationService } from '../../../../core/services/session-expiration.service';
import { CalendarService, MyCalendarResponse, CalendarData, Holiday } from '../../../../core/services/calendar.service';
import { LeaveService, CompoffAssignment } from '../../../../core/services/leave.service';
import { CompoffService, CompoffRequestInput, CompoffRequestResponse } from '../../../../core/services/compoff.service';
import {
  HrAdminGuard,
  ViewEmployeeGuard,
  MarkAttendanceGuard,
  ReportsGuard,
  ApplyLeaveGuard,
  ApproveLeaveGuard,
  LeavePolicyGuard,
  AssignCompoffGuard,
  CompoffRequestGuard
} from '../../../../core/guards/lms';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';

import Swal from 'sweetalert2';


@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NgbModalModule,
    NgbDatepickerModule,
    FeatherIconDirective
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit, OnDestroy {
  // Modal references
  @ViewChild('hrAdminModal') hrAdminModal: TemplateRef<any>;
  @ViewChild('assignCompoffModal') assignCompoffModal: TemplateRef<any>;
  @ViewChild('compoffRequestModal') compoffRequestModal: TemplateRef<any>;

  // File upload properties
  uploadType: string = '';
  selectedFile: File | null = null;
  uploading: boolean = false;
  uploadResult: BulkUploadResponse | null = null;
  uploadError: string | null = null;
  hasValidationErrors: boolean = false;
  downloadingErrors: boolean = false;
  lastUploadErrorData: any = null;



  // New Year Activity properties
  activityCount: any = null;
  loadingCount: boolean = false;
  exporting: boolean = false;

  // Assign Compoff properties - DEPRECATED: Now handled through approval workflow
  assignCompoffForm: FormGroup;
  assigningCompoff: boolean = false;
  assignCompoffError: string | null = null;
  workingDateError: string | null = null;
  employees: any[] = []; // List of employees for comp-off assignment
  loadingEmployees: boolean = false;

  // Compoff Request properties
  compoffRequestForm: FormGroup;
  submittingRequest: boolean = false;
  compoffRequestError: string | null = null;

  // Comp off calendar properties
  compoffWorkingDatePicker: NgbDate | null = null;
  holidays: Holiday[] = [];
  holidayDates: string[] = [];
  holidaysLoaded: boolean = false;

  // Loading state to prevent component flickering
  userDataLoaded: boolean = false;

  // Calendar properties
  currentCalendarData: CalendarData | null = null;
  calendarLoading: boolean = false;
  calendarError: string | null = null;
  currentMonth: number = new Date().getMonth() + 1;
  currentYear: number = new Date().getFullYear();
  calendarEvents: any[] = [];

  // Real-time component permission mapping (no caching)
  private componentPermissionMap = new Map<string, string[]>();

  /**
   * Initialize component permission mapping for optimized checking
   */
  private initializeComponentPermissionMap(): void {
    this.componentPermissionMap.set('ApplyLeave', ['leave:create']);
    this.componentPermissionMap.set('HRAdmin', ['new_year_activity:read', 'new_year_activity:create', 'employees:create']);
    this.componentPermissionMap.set('Reports', ['leave_report:read']);
    this.componentPermissionMap.set('ViewEmployee', ['employees:read']);
    this.componentPermissionMap.set('LeavePolicy', ['leave:read']);
    this.componentPermissionMap.set('ApproveLeaves', ['leave:approve']);
    this.componentPermissionMap.set('MarkAttendance', ['attendance:create']);
    this.componentPermissionMap.set('AssignCompoff', ['leave:assign_compoff']);
    this.componentPermissionMap.set('CompoffRequest', ['leave:request_compoff']);
    this.componentPermissionMap.set('Calendar', ['calendar:read', 'calendar:employee']);
  }

  /**
   * Real-time permission checking using centralized service
   * @param permissions Array of permissions to check
   * @returns boolean indicating if user has any of the permissions
   */
  private hasAnyPermission(permissions: string[]): boolean {
    return this.permissionService.hasAnyPermission(permissions);
  }

  /**
   * Real-time component visibility check (no caching)
   * @param componentName Name of the component
   * @returns boolean indicating if component should be visible
   */
  private checkComponentVisibility(componentName: string): boolean {
    if (!this.userDataLoaded) return false;

    // Get required permissions for component
    const requiredPermissions = this.componentPermissionMap.get(componentName);
    if (!requiredPermissions) return false;

    // Real-time permission check
    const hasAccess = this.hasAnyPermission(requiredPermissions);

    return hasAccess;
  }

  /**
   * Clear template cache when user data changes (removed other caches for real-time)
   */
  private clearTemplateCache(): void {
    // Clear template cache for real-time updates
    this._cachedComponentVisibility = null;
    this._lastVisibilityCheck = 0;
  }

  /**
   * 🔄 REAL-TIME COMPONENT VISIBILITY CHECK
   * Get all component visibility states with real-time permission checking
   * No caching - always fresh results
   */
  getAllComponentVisibility(): { [key: string]: boolean } {
    if (!this.userDataLoaded) {
      return {
        applyLeave: false,
        hrAdmin: false,
        reports: false,
        viewEmployee: false,
        leavePolicy: false,
        approveLeaves: false,
        markAttendance: false,
        assignCompoff: false,
        compoffRequest: false,
        calendar: false
      };
    }

    const user = this.authService.currentUserValue;
    if (!user || !user.permissions) {
      return {
        applyLeave: false,
        hrAdmin: false,
        reports: false,
        viewEmployee: false,
        leavePolicy: false,
        approveLeaves: false,
        markAttendance: false,
        assignCompoff: false,
        compoffRequest: false,
        calendar: false
      };
    }

    // Real-time permission check - no caching
    const userPermissions = new Set(user.permissions || []);

    return {
      applyLeave: userPermissions.has('leave:create'),
      hrAdmin: userPermissions.has('new_year_activity:read') ||
               userPermissions.has('new_year_activity:create') ||
               userPermissions.has('employees:create'),
      reports: userPermissions.has('leave_report:read'),
      viewEmployee: userPermissions.has('employees:read'),
      leavePolicy: userPermissions.has('leave:read'),
      approveLeaves: userPermissions.has('leave:approve'),
      markAttendance: userPermissions.has('attendance:create'),
      assignCompoff: userPermissions.has('leave:assign_compoff'),
      compoffRequest: userPermissions.has('leave:request_compoff'),
      calendar: userPermissions.has('calendar:read') || userPermissions.has('calendar:employee')
    };
  }

  /**
   * 🔄 REAL-TIME ROLE-BASED ACCESS
   * Real-time component visibility based on current user permissions
   * Always checks actual permissions instead of role assumptions
   */
  getComponentVisibilityByRole(): { [key: string]: boolean } | null {
    // Always return null to force real-time permission checking
    // This ensures components are shown based on actual permissions, not role assumptions
    return null;
  }

  // Public getter for template access
  get currentUser() {
    return this.authService.currentUserValue;
  }

  // 🔄 REAL-TIME TEMPLATE ACCESS
  // No caching - always fresh permission checks
  private _cachedComponentVisibility: { [key: string]: boolean } | null = null;
  private _lastVisibilityCheck = 0;

  get componentVisibility(): { [key: string]: boolean } {
    // Always get fresh component visibility - no caching
    return this.getAllComponentVisibility();
  }

  constructor(
    private modalService: NgbModal,
    private employeeService: EmployeeService,
    private newYearActivityService: NewYearActivityService,
    private fb: FormBuilder,
    private templateDownloadService: TemplateDownloadService,
    private authService: AuthService,
    private permissionService: PermissionService,
    private sessionExpirationService: SessionExpirationService,
    private route: ActivatedRoute,
    private router: Router,
    private calendarService: CalendarService,
    private leaveService: LeaveService,
    private compoffService: CompoffService,
    private calendar: NgbCalendar,
    private cdr: ChangeDetectorRef,
    // Inject all the guards for dashboard visibility
    private hrAdminGuard: HrAdminGuard,
    private viewEmployeeGuard: ViewEmployeeGuard,
    private markAttendanceGuard: MarkAttendanceGuard,
    private reportsGuard: ReportsGuard,
    private applyLeaveGuard: ApplyLeaveGuard,
    private approveLeaveGuard: ApproveLeaveGuard,
    private leavePolicyGuard: LeavePolicyGuard,
    private assignCompoffGuard: AssignCompoffGuard,
    private compoffRequestGuard: CompoffRequestGuard
  ) {
    // Initialize optimized component permission mapping
    this.initializeComponentPermissionMap();

    // Initialize Assign Compoff form
    this.assignCompoffForm = this.fb.group({
      employeeList: ['', [Validators.required]],
      workingDate: ['', [Validators.required]],
      remark: ['', [Validators.required]]
    });

    // Initialize Compoff Request form
    this.compoffRequestForm = this.fb.group({
      workingDate: ['', [Validators.required]],
      reason: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    // Initialize session monitoring for session extension functionality
    this.initializeSessionMonitoring();

    // Load activity count on component initialization
    this.loadActivityCount();

    // Load holidays for comp off calendar
    this.loadHolidaysForCompoff();

    // Wait for user data to be fully loaded before showing components
    this.waitForUserData().then(() => {
      this.userDataLoaded = true;
      // Clear template cache when user data is loaded to ensure fresh permission checks
      this.clearTemplateCache();
      console.log('✅ USER DATA LOADED - Dashboard components should now check permissions');
      console.log('🔄 REAL-TIME: Using real-time permission-based component fetching (no caching)');
      this.logUserDebugInfo();

      // Check if we need to open a modal based on route data
      this.handleRouteModalAction();
    });
  }

  /**
   * Initialize session monitoring for session extension functionality
   */
  private initializeSessionMonitoring(): void {
    console.log('🔐 Session extension functionality available');

    // Session monitoring is automatically started by SessionExpirationService constructor
    if (this.authService.isLoggedIn()) {
      console.log('✅ Session monitoring active - session extension popup will appear when needed');
      console.log('🔄 Session extension logic restored - users can extend their session when it expires');

      // Log current session status
      const sessionStatus = this.sessionExpirationService.getSessionStatus();
      console.log('📊 Current session status:', sessionStatus);
    } else {
      console.log('⚠️ User not logged in - session monitoring will start after login');
    }
  }

  private handleRouteModalAction(): void {
    const modalAction = this.route.snapshot.data['modalAction'];
    if (modalAction) {
      // Small delay to ensure the component is fully rendered
      setTimeout(() => {
        switch (modalAction) {
          case 'assign-comp-off':
            this.openAssignCompoffModal();
            break;
          case 'comp-off-request':
            this.openCompoffRequestModal();
            break;
        }
      }, 100);
    }
  }

  private async waitForUserData(): Promise<void> {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 20; // Reduced from 50 to 20 (2 seconds max wait time)

      const checkUserData = () => {
        const user = this.authService.currentUserValue;
        attempts++;

        // User is loaded if we have a user object (permissions can be empty)
        if (user && user.id) {
          // Reduced logging for better performance
          if (attempts > 5) {
            console.log('✅ User data loaded after', attempts, 'attempts');
          }
          resolve();
        } else if (attempts >= maxAttempts) {
          console.warn('⚠️ Timeout waiting for user data, proceeding anyway');
          resolve();
        } else {
          // Only log every 5 attempts to reduce console spam
          if (attempts % 5 === 0) {
            console.log(`⏳ Waiting for user data... (${attempts}/${maxAttempts})`);
          }
          setTimeout(checkUserData, 100);
        }
      };
      checkUserData();
    });
  }

  private logUserDebugInfo(): void {
    // Count visible components
    const visibleCount = [
      this.showApplyLeave(),
      this.showHRAdmin(),
      this.showReports(),
      this.showViewEmployee(),
      this.showLeavePolicy(),
      this.showApproveLeaves(),
      this.showMarkAttendance(),
      this.showAssignCompoff(),
      this.showCompoffRequest(),
      this.showCalendar()
    ].filter(Boolean).length;





    // Add test methods to window for debugging
    (window as any).testUserDataFetching = () => {
      this.authService.testUserDataFetching();
    };

    // Add method to enable guard debugging
    (window as any).enableGuardDebug = () => {
      (window as any).debugGuards = true;
      console.log('🔧 Guard debugging enabled - guards will now log detailed information');
    };

    // Add method to disable guard debugging
    (window as any).disableGuardDebug = () => {
      (window as any).debugGuards = false;
      console.log('🔧 Guard debugging disabled - guards will run silently for better performance');
    };

    // Add method to clear template cache (no other caches in real-time mode)
    (window as any).clearGuardCache = () => {
      this.clearTemplateCache();

    };

    // Permission test methods removed for production build

    // Display current user role and permissions for testing
    this.displayUserRoleInfo();

    // Add temporary manager role override for testing
    (window as any).forceManagerRole = () => {
      const user = this.authService.currentUserValue;
      if (user) {
        // Temporarily override user role to manager
        user.role = 'manager';
        window.location.reload();
      }
    };







    (window as any).refreshNavbarRole = () => {
      // Find navbar component and refresh role display
      const navbarElement = document.querySelector('app-navbar');
      if (navbarElement && (navbarElement as any).refreshRoleDisplay) {
        (navbarElement as any).refreshRoleDisplay();
      } else {

      }
    };

    (window as any).setManagerRole = () => {
      // Manually set manager role for testing
      const user = this.authService.currentUserValue;
      if (user) {
        user.role = 'manager';
        user.roles = ['role_manager'];
        console.log('✅ Manager role set manually');
      }

      // Refresh navbar
      setTimeout(() => {
        (window as any).refreshNavbarRole();
      }, 100);
    };

    (window as any).testUserDataFetching2 = () => {
      this.authService.testUserDataFetching();
    };

    (window as any).autoFetchRoles = () => {
      console.log('🔄 Auto-fetching roles with proper UUID...');
      this.authService.getCurrentUser().subscribe({
        next: () => {
          console.log('✅ User data updated from /users/me');
          setTimeout(() => {
            // User roles are loaded from login response
            console.log('✅ User roles loaded from login response');
            setTimeout(() => {
              (window as any).refreshNavbarRole();
            }, 100);
          }, 100);
        },
        error: (error) => {
          console.error('❌ Failed to get current user:', error);
        }
      });
    };

    // Add guard-based component visibility test
    (window as any).testComponentVisibility = () => {
      console.log('🛡️ GUARD-BASED COMPONENT VISIBILITY TEST');
      console.log('='.repeat(60));

      const user = this.authService.currentUserValue;
      console.log(`👤 User: ${user?.email}`);
      console.log(`🎭 Role: ${user?.role}`);
      console.log(`🔑 Permissions: ${user?.permissions?.length || 0} total`);

      console.log('\n🧪 TESTING EACH COMPONENT GUARD:');
      console.log('-'.repeat(40));

      // Test each component with detailed guard information
      const components = [
        { name: 'Apply Leave', method: () => this.showApplyLeave(), guard: 'ApplyLeaveGuard', permission: 'leave:create' },
        { name: 'HR Admin', method: () => this.showHRAdmin(), guard: 'HrAdminGuard', permission: 'new_year_activity:read OR new_year_activity:create OR employees:create' },
        { name: 'Reports', method: () => this.showReports(), guard: 'ReportsGuard', permission: 'leave_report:read' },
        { name: 'View Employee', method: () => this.showViewEmployee(), guard: 'ViewEmployeeGuard', permission: 'employees:read' },
        { name: 'Leave Policy', method: () => this.showLeavePolicy(), guard: 'LeavePolicyGuard', permission: 'leave:read' },
        { name: 'Approve Leaves', method: () => this.showApproveLeaves(), guard: 'ApproveLeaveGuard', permission: 'leave:approve' },
        { name: 'Mark Attendance', method: () => this.showMarkAttendance(), guard: 'MarkAttendanceGuard', permission: 'attendance:create' },
        { name: 'Assign Comp-off', method: () => this.showAssignCompoff(), guard: 'AssignCompoffGuard', permission: 'leave:assign_compoff' },
        { name: 'Comp-off Request', method: () => this.showCompoffRequest(), guard: 'CompoffRequestGuard', permission: 'leave:request_compoff' },
        { name: 'Calendar', method: () => this.showCalendar(), guard: 'Permission Check', permission: 'calendar:read OR calendar:employee' }
      ];

      let visibleCount = 0;
      components.forEach((comp, index) => {
        const isVisible = comp.method();
        if (isVisible) visibleCount++;

        console.log(`${index + 1}. ${isVisible ? '✅' : '❌'} ${comp.name}`);
        console.log(`   Guard: ${comp.guard}`);
        console.log(`   Required: ${comp.permission}`);

        // Check specific permissions for this component
        if (comp.name === 'HR Admin') {
          const hasNewYearRead = this.authService.hasPermission('new_year_activity:read');
          const hasNewYearCreate = this.authService.hasPermission('new_year_activity:create');
          const hasEmployeesCreate = this.authService.hasPermission('employees:create');
          console.log(`   Has new_year_activity:read: ${hasNewYearRead}`);
          console.log(`   Has new_year_activity:create: ${hasNewYearCreate}`);
          console.log(`   Has employees:create: ${hasEmployeesCreate}`);
        } else if (comp.name === 'Calendar') {
          const hasCalendarRead = this.authService.hasPermission('calendar:read');
          const hasCalendarEmployee = this.authService.hasPermission('calendar:employee');
          console.log(`   Has calendar:read: ${hasCalendarRead}`);
          console.log(`   Has calendar:employee: ${hasCalendarEmployee}`);
        } else {
          const hasPermission = this.authService.hasPermission(comp.permission.split(' ')[0]);
          console.log(`   Has permission: ${hasPermission}`);
        }
        console.log('');
      });

      console.log(`📊 SUMMARY: ${visibleCount}/10 components visible`);

      // Role-based expectations
      console.log('\n🎯 ROLE-BASED EXPECTATIONS:');
      if (user?.role === 'employee') {
        console.log('👤 Employee should see: Apply Leave, Leave Policy, Calendar (3 components)');
        if (visibleCount === 3) {
          console.log('✅ CORRECT: Employee seeing expected number of components');
        } else {
          console.log(`❌ ISSUE: Employee seeing ${visibleCount} components instead of 3`);
        }
      } else if (user?.role === 'manager') {
        console.log('👔 Manager should see: Apply Leave, Approve Leaves, Leave Policy, Assign Comp-off, Comp-off Request, Calendar (6 components)');
        if (visibleCount === 6) {
          console.log('✅ CORRECT: Manager seeing expected number of components');
        } else {
          console.log(`❌ ISSUE: Manager seeing ${visibleCount} components instead of 6`);
        }
      } else if (user?.role === 'admin') {
        console.log('👑 Admin should see: All 10 components');
        if (visibleCount === 10) {
          console.log('✅ CORRECT: Admin seeing all components');
        } else {
          console.log(`❌ ISSUE: Admin seeing ${visibleCount} components instead of 10`);
        }
      }

      console.log('='.repeat(60));
    };

    // Add specific manager role test
    (window as any).testManagerRole = () => {
      console.log('🎯 Testing Manager Role Visibility:');
      console.log('==================================');

      const user = this.authService.currentUserValue;
      console.log('👤 Current User Role:', user?.role);
      console.log('🔍 Is Manager:', this.isManager());

      console.log('✅ SHOULD SHOW for Manager (6 components):');
      console.log('  1. Apply Leave:', this.showApplyLeave());
      console.log('  2. Approve Leaves:', this.showApproveLeaves());
      console.log('  3. Leave Policy:', this.showLeavePolicy());
      console.log('  4. Assign Comp-off:', this.showAssignCompoff());
      console.log('  5. Comp-off Request:', this.showCompoffRequest());
      console.log('  6. Calendar:', this.showCalendar());

      console.log('❌ SHOULD NOT SHOW for Manager:');
      console.log('  - HR Admin:', this.showHRAdmin());
      console.log('  - Reports:', this.showReports());
      console.log('  - View Employee:', this.showViewEmployee());
      console.log('  - Mark Attendance:', this.showMarkAttendance());

      const managerComponents = [
        this.showApplyLeave(),
        this.showApproveLeaves(),
        this.showLeavePolicy(),
        this.showAssignCompoff(),
        this.showCompoffRequest(),
        this.showCalendar()
      ];

      const hiddenComponents = [
        this.showHRAdmin(),
        this.showReports(),
        this.showViewEmployee(),
        this.showMarkAttendance()
      ];

      const allManagerComponentsVisible = managerComponents.every(visible => visible);
      const allHiddenComponentsHidden = hiddenComponents.every(visible => !visible);

      console.log('🎯 Manager Role Test Results:');
      console.log('  All 6 manager components visible:', allManagerComponentsVisible);
      console.log('  All non-manager components hidden:', allHiddenComponentsHidden);
      console.log('  Manager role working correctly:', allManagerComponentsVisible && allHiddenComponentsHidden);
    };

    // Add method to check View Employee specifically
    (window as any).checkViewEmployee = () => {
      const user = this.authService.currentUserValue;
      console.log('🔍 VIEW EMPLOYEE DEBUG CHECK:');
      console.log('👤 Current User:', {
        email: user?.email,
        role: user?.role,
        permissions: user?.permissions || []
      });
      console.log('🔑 Required Permission: employees:read');
      console.log('✅ Has Permission:', this.authService.hasPermission('employees:read'));
      console.log('👁️ Component Visible:', this.showViewEmployee());

      // Test the guard directly
      console.log('🛡️ Testing ViewEmployeeGuard directly...');
      const mockRoute = {} as ActivatedRouteSnapshot;
      const mockState = {} as RouterStateSnapshot;
      const guardResult = this.viewEmployeeGuard.canActivate(mockRoute, mockState);
      console.log('🛡️ Guard Result:', guardResult);
    };

    // Add method to check Reports specifically
    (window as any).checkReports = () => {
      const user = this.authService.currentUserValue;
      console.log('🔍 REPORTS DEBUG CHECK:');
      console.log('👤 Current User:', {
        email: user?.email,
        role: user?.role,
        permissions: user?.permissions || []
      });
      console.log('🔑 Required Permission: leave_report:read');
      console.log('✅ Has Permission:', this.authService.hasPermission('leave_report:read'));
      console.log('📊 Component Visible:', this.showReports());

      // Test the guard directly
      console.log('🛡️ Testing ReportsGuard directly...');
      const mockRoute = {} as ActivatedRouteSnapshot;
      const mockState = {} as RouterStateSnapshot;
      const guardResult = this.reportsGuard.canActivate(mockRoute, mockState);
      console.log('🛡️ Guard Result:', guardResult);
    };

    console.log('🧪 Added debugging methods to window:');
    console.log('  - testRoleVisibility() - Test role-based component visibility');
    console.log('  - testManagerRole() - Test manager role specifically');
    console.log('  - checkViewEmployee() - Debug View Employee permission issue');
    console.log('  - checkReports() - Debug Reports permission issue');
    console.log('  - clearGuardCache() - Clear guard cache and re-evaluate');
    console.log('🧪 Use testManagerRole() to verify manager role shows exactly 6 components');

    // Add role visibility testing method
    (window as any).testRoleVisibility = () => {
      console.log('🎭 Testing Role-Based Visibility (user.role):');
      console.log('=============================================');

      const user = this.authService.currentUserValue;

      console.log('👤 Current User:', user?.email);
      console.log('🎭 User Role Property:', user?.role);
      console.log('🔍 Role Detection:', {
        'user.role': user?.role,
        isEmployee: this.isEmployee(),
        isManager: this.isManager(),
        isAdmin: this.isAdmin(),
        isHR: this.isHR()
      });

      console.log('👁️ Component Visibility:');
      console.log('  Apply Leave:', this.showApplyLeave());
      console.log('  HR Admin:', this.showHRAdmin());
      console.log('  Reports:', this.showReports());
      console.log('  View Employee:', this.showViewEmployee());
      console.log('  Leave Policy:', this.showLeavePolicy());
      console.log('  Approve Leaves:', this.showApproveLeaves());
      console.log('  Mark Attendance:', this.showMarkAttendance());
      console.log('  Assign Comp-off:', this.showAssignCompoff());
      console.log('  Comp-off Request:', this.showCompoffRequest());
      console.log('  Calendar:', this.showCalendar());

      console.log('🎯 Expected for Current Role:');
      if (user?.role === 'employee') {
        console.log('  EMPLOYEE (4 components): Apply Leave, Leave Policy, Comp-off Request, Calendar');
      } else if (user?.role === 'manager' || this.authService.hasRole('role_manager')) {
        console.log('  MANAGER (6 components): Apply Leave, Approve Leaves, Leave Policy, Assign Comp-off, Comp-off Request, Calendar');
      } else if (user?.role === 'admin' || user?.role === 'hr') {
        console.log('  ADMIN/HR (10 components): Apply Leave, HR Admin, Reports, View Employee, Mark Attendance, Assign Comp-off, Approve Leaves, Leave Policy, Comp-off Request, Calendar');
      } else {
        console.log('  UNKNOWN ROLE: No components should be visible');
      }
    };

    console.log('🧪 Added testRoleVisibility() method for testing role-based visibility');

    // Add real-time performance testing method
    (window as any).testRealTimePerformance = () => {
      console.log('🔄 TESTING REAL-TIME COMPONENT FETCHING PERFORMANCE');
      console.log('='.repeat(60));

      const startTime = performance.now();

      // Test real-time batch component visibility
      const batchResults = this.getAllComponentVisibility();
      const batchTime = performance.now() - startTime;

      console.log('📊 Real-time Batch Component Visibility Results:', batchResults);
      console.log(`⚡ Real-time batch fetch time: ${batchTime.toFixed(2)}ms`);

      // Test individual method calls for comparison
      const individualStartTime = performance.now();
      const individualResults = {
        applyLeave: this.showApplyLeave(),
        hrAdmin: this.showHRAdmin(),
        reports: this.showReports(),
        viewEmployee: this.showViewEmployee(),
        leavePolicy: this.showLeavePolicy(),
        approveLeaves: this.showApproveLeaves(),
        markAttendance: this.showMarkAttendance(),
        assignCompoff: this.showAssignCompoff(),
        compoffRequest: this.showCompoffRequest(),
        calendar: this.showCalendar()
      };
      const individualTime = performance.now() - individualStartTime;

      console.log('🔄 Individual Method Results:', individualResults);
      console.log(`⚡ Individual fetch time: ${individualTime.toFixed(2)}ms`);

      console.log('\n📈 REAL-TIME PERFORMANCE:');
      console.log(`Real-time batch method: ${batchTime.toFixed(2)}ms`);
      console.log(`Real-time individual methods: ${individualTime.toFixed(2)}ms`);
      console.log('🔄 Note: All checks are performed in real-time without caching');

      console.log('='.repeat(60));
    };

    // Add session extension testing methods
    (window as any).testSessionExtension = () => {
      console.log('🔐 TESTING SESSION EXTENSION FUNCTIONALITY');
      console.log('='.repeat(50));

      const sessionStatus = this.sessionExpirationService.getSessionStatus();
      console.log('📊 Current Session Status:', sessionStatus);

      console.log('\n🧪 Available Session Extension Commands:');
      console.log('  - sessionDebug.forceWarning() - Force show session expiration warning');
      console.log('  - sessionDebug.manualExtend() - Manually extend session');
      console.log('  - sessionDebug.getStatus() - Get current session status');
      console.log('='.repeat(50));
    };

    // Add session debugging methods to window
    (window as any).sessionDebug = {
      forceWarning: () => {
        console.log('⚠️ Forcing session expiration warning...');
        this.sessionExpirationService.forceShowWarning();
      },
      manualExtend: () => {
        console.log('🔄 Manually extending session...');
        this.sessionExpirationService.manualExtendSession();
      },
      getStatus: () => {
        const status = this.sessionExpirationService.getSessionStatus();
        console.log('📊 Session Status:', status);
        return status;
      },
      resetWarning: () => {
        console.log('🔄 Resetting session warning state...');
        this.sessionExpirationService.resetWarningState();
      },
      resetCooldown: () => {
        console.log('🔄 Resetting extension cooldown...');
        this.sessionExpirationService.resetExtensionCooldown();
      }
    };

    // Role determination test removed - using real-time permission checking instead
    console.log('🧪 Role determination now handled by real-time permission checking');
    console.log('🧪 Debugging methods removed - using real-time LMS component fetching');
    console.log('🎯 Access control is now handled by real-time permission checking (no caching)');
    console.log('🔄 Use testRealTimePerformance() to test the new real-time system');
    console.log('🔐 Use testSessionExtension() to test session extension functionality');
  }









  // Role detection methods based purely on API roles (no superuser logic)
  isEmployee(): boolean {
    const user = this.authService.currentUserValue;
    const result = user?.role === 'employee';
    console.log('🔍 isEmployee():', { userRole: user?.role, result });
    return result;
  }

  isManager(): boolean {
    const user = this.authService.currentUserValue;
    const hasManagerRole = user?.role === 'manager';
    const hasRoleManager = this.authService.hasRole('role_manager');
    const result = hasManagerRole || hasRoleManager;
    console.log('🔍 isManager():', {
      userRole: user?.role,
      hasManagerRole,
      hasRoleManager,
      result
    });
    return result;
  }

  isAdmin(): boolean {
    const user = this.authService.currentUserValue;
    const isAdminRole = user?.role === 'admin';
    const hasAdminRole = this.authService.hasRole('role_admin') || this.authService.hasRole('role_superuser');
    const result = isAdminRole || hasAdminRole;
    console.log('🔍 isAdmin():', {
      userRole: user?.role,
      isAdminRole,
      hasAdminRole,
      result
    });
    return result;
  }

  isHR(): boolean {
    const user = this.authService.currentUserValue;
    const isHRRole = user?.role === 'hr';
    const hasHRRole = this.authService.hasRole('role_hr');
    const result = isHRRole || hasHRRole;
    console.log('🔍 isHR():', {
      userRole: user?.role,
      isHRRole,
      hasHRRole,
      result
    });
    return result;
  }

  // 🔐 PERMISSION-BASED VISIBILITY METHODS
  // These methods check specific permissions for each component based on the new route structure

  /**
   * Check if user has permission to create leaves (apply leave only)
   */
  hasLeaveCreatePermission(): boolean {
    return this.permissionService.hasPermission('leave:create');
  }

  /**
   * Check if user has permission to request comp-off
   */
  hasLeaveRequestCompoffPermission(): boolean {
    return this.permissionService.hasPermission('leave:request_compoff');
  }

  /**
   * Check if user has permission to approve leaves
   */
  hasLeaveApprovePermission(): boolean {
    return this.permissionService.hasPermission('leave:approve');
  }

  /**
   * Check if user has permission to read leave policies
   */
  hasLeaveReadPermission(): boolean {
    return this.permissionService.hasPermission('leave:read');
  }

  /**
   * Check if user has permission to assign comp-off
   */
  hasAssignCompoffPermission(): boolean {
    return this.permissionService.hasPermission('leave:assign_compoff');
  }



  /**
   * Check if user has permission to read attendance
   */
  hasAttendanceReadPermission(): boolean {
    return this.permissionService.hasPermission('attendance:read');
  }



  // 🎯 UPDATED COMPONENT VISIBILITY METHODS - PERMISSION-BASED ACCESS CONTROL
  // These methods now align with the new route permission structure



  // 🔄 REAL-TIME COMPONENT VISIBILITY METHODS
  // Using direct permission checking without caching for real-time updates

  showApplyLeave(): boolean {
    return this.checkComponentVisibility('ApplyLeave');
  }

  showHRAdmin(): boolean {
    return this.checkComponentVisibility('HRAdmin');
  }

  showReports(): boolean {
    return this.checkComponentVisibility('Reports');
  }

  showViewEmployee(): boolean {
    return this.checkComponentVisibility('ViewEmployee');
  }

  showLeavePolicy(): boolean {
    return this.checkComponentVisibility('LeavePolicy');
  }

  showApproveLeaves(): boolean {
    return this.checkComponentVisibility('ApproveLeaves');
  }

  showMarkAttendance(): boolean {
    return this.checkComponentVisibility('MarkAttendance');
  }

  showAssignCompoff(): boolean {
    return this.checkComponentVisibility('AssignCompoff');
  }

  showCompoffRequest(): boolean {
    return this.checkComponentVisibility('CompoffRequest');
  }

  showCalendar(): boolean {
    const hasAccess = this.checkComponentVisibility('Calendar');

    // Keep logging for calendar component as requested
    if (hasAccess) {
      console.log('✅ CALENDAR - Permission GRANTED');
      console.log('🎯 User can access Calendar functionality');
      const user = this.authService.currentUserValue;
      if (user?.permissions?.includes('calendar:read')) console.log('🔑 Permission: calendar:read');
      if (user?.permissions?.includes('calendar:employee')) console.log('🔑 Permission: calendar:employee');
    } else {
      console.log('❌ CALENDAR - Permission DENIED');
      console.log('🔑 Required: calendar:read OR calendar:employee');
    }

    return hasAccess;
  }



  /**
   * 🔄 REAL-TIME PERMISSION-BASED VISIBILITY SUMMARY:
   *
   * ✅ Components are shown using REAL-TIME PERMISSION CHECKING - Always Fresh!
   *
   * DASHBOARD VISIBILITY USES REAL-TIME METHODS:
   * - Direct permission checking without caching
   * - Real-time permission evaluation on every call
   * - No caching for immediate permission updates
   * - Set-based permission lookup for O(1) complexity
   * - Always current user permission state
   *
   * REAL-TIME FEATURES:
   * - Real-time component visibility checking: getAllComponentVisibility()
   * - No caching - always fresh permission checks
   * - Immediate response to permission changes
   * - Set-based permission lookup for performance
   * - Direct user permission evaluation
   *
   * BENEFITS:
   * - Always up-to-date component visibility
   * - Immediate response to permission changes
   * - No stale cached data
   * - Perfect consistency with current user permissions
   * - Real-time security enforcement
   *
   * IMPLEMENTATION:
   * - Each showXXX() method uses checkComponentVisibility() without caching
   * - Component permissions mapped in initializeComponentPermissionMap()
   * - Real-time processing via getAllComponentVisibility()
   * - No role-based shortcuts - always check actual permissions
   * - Direct permission evaluation for maximum accuracy
   */

  ngOnDestroy(): void {
    // Close all modals when component is destroyed
    this.closeAllModals();
  }

  // Open HR Admin modal
  openHrAdminModal(): void {
    this.modalService.open(this.hrAdminModal, {
      size: 'md',
      centered: true,
      backdrop: 'static'
    });

    // Reset form values
    this.uploadType = '';
    this.selectedFile = null;
    this.uploading = false;
    this.uploadResult = null;
    this.uploadError = null;
    this.hasValidationErrors = false;
    this.downloadingErrors = false;
    this.lastUploadErrorData = null;
  }

  // Force enable upload button for testing (can be called from browser console)
  forceEnableUploadButton(): void {
    this.uploading = false;
    console.log('🔧 Upload button force enabled - uploading set to false');
    console.log('🔍 Current state - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile, 'uploadType:', this.uploadType);
  }

  // Handle file selection with validation
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv' // .csv
      ];

      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: 'Invalid File Type',
          html: `
            <div class="text-start">
              <p>Please select a valid Excel or CSV file.</p>
              <p class="text-muted small mt-2">Supported formats:</p>
              <ul class="text-start small">
                <li>Excel files (.xlsx, .xls)</li>
                <li>CSV files (.csv)</li>
              </ul>
            </div>
          `,
          confirmButtonText: 'OK'
        });
        input.value = ''; // Clear the input
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        Swal.fire({
          icon: 'error',
          title: 'File Too Large',
          html: `
            <div class="text-start">
              <p>File size: <strong>${(file.size / 1024 / 1024).toFixed(2)} MB</strong></p>
              <p>Maximum allowed: <strong>10 MB</strong></p>
              <p class="text-muted small mt-2">Please reduce the file size by:</p>
              <ul class="text-start small">
                <li>Removing unnecessary columns or rows</li>
                <li>Splitting data into smaller batches</li>
                <li>Saving as .xlsx instead of .xls</li>
              </ul>
            </div>
          `,
          confirmButtonText: 'OK'
        });
        input.value = ''; // Clear the input
        return;
      }

      this.selectedFile = file;

      // Reset error states when new file is selected
      this.uploadError = null;
      this.hasValidationErrors = false;
      this.downloadingErrors = false;
      this.lastUploadErrorData = null;

      console.log('✅ File selected and validated:', {
        name: file.name,
        type: file.type,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`
      });
    }
  }

  // Download template based on upload type
  downloadTemplate(): void {
    if (!this.uploadType) {
      Swal.fire({
        icon: 'warning',
        title: 'Upload Type Required',
        text: 'Please select an upload type first.',
        confirmButtonText: 'OK'
      });
      return;
    }

    console.log('📥 Downloading template for upload type:', this.uploadType);

    let downloadPromise: Promise<boolean>;

    switch (this.uploadType) {
      case 'new_employee':
        downloadPromise = this.templateDownloadService.downloadEmployeeBulkUploadTemplate();
        break;

      case 'new_year_activity':
        // Use the template download service for new year activities
        downloadPromise = this.templateDownloadService.downloadNewYearActivityTemplate();
        break;

      default:
        Swal.fire({
          icon: 'info',
          title: 'Template Not Available',
          text: `Template for "${this.getUploadTypeLabel(this.uploadType)}" is not yet available.`,
          confirmButtonText: 'OK'
        });
        return;
    }

    // Show success message only after successful download
    downloadPromise.then(() => {
      Swal.fire({
        icon: 'success',
        title: 'Template Downloaded',
        text: 'Template has been downloaded successfully.',
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
      });
    }).catch((error) => {
      console.error('Template download failed:', error);
      Swal.fire({
        icon: 'error',
        title: 'Download Failed',
        text: 'Failed to download template. Please check if the file exists and try again.',
        confirmButtonText: 'OK'
      });
    });
  }



  // Upload file
  uploadFile(): void {
    // Safety check - if somehow the button is clicked while uploading, ignore
    if (this.uploading) {
      console.log('⚠️ Upload already in progress, ignoring click');
      return;
    }

    // Validate upload type
    if (!this.uploadType) {
      Swal.fire({
        icon: 'warning',
        title: 'Upload Type Required',
        text: 'Please select an upload type before proceeding.',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Validate file selection
    if (!this.selectedFile) {
      Swal.fire({
        icon: 'warning',
        title: 'File Required',
        text: 'Please select a file to upload.',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Proceed directly to upload without confirmation
    this.performUpload();
  }

  // Perform the actual upload
  private performUpload(): void {
    // Additional safety check (should not be needed due to validation above)
    if (!this.selectedFile || !this.uploadType) {
      console.error('Upload called without file or type');
      return;
    }

    // Reset previous results
    this.uploadError = null;
    this.uploadResult = null;
    this.uploading = true;

    console.log('Starting file upload...', {
      fileName: this.selectedFile.name,
      uploadType: this.uploadType,
      fileSize: this.selectedFile.size
    });

    // Handle different upload types
    if (this.uploadType === 'new_employee') {
      this.employeeService.bulkUploadEmployees(this.selectedFile).subscribe({
        next: (response) => {
          console.log('✅ 200 Status - Dashboard Upload API Response Received');
          console.log('Response:', response);

          this.uploading = false;



          // Show success alert immediately after 200 status
          console.log('🎉 Dashboard showing success alert after 200 status');
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Employees uploaded successfully!',
            timer: 3000,
            showConfirmButton: false
          }).then(() => {
            console.log('✅ Dashboard success alert completed');
            // Close modal after success notification
            this.modalService.dismissAll();
          });
        },
        error: (err) => {
          console.error('Upload error details:', err);

          // CRITICAL: Reset uploading state to re-enable upload button
          this.uploading = false;
          console.log('❌ Upload failed - Button should be re-enabled (uploading = false)');
          console.log('🔍 Current state - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile, 'uploadType:', this.uploadType);

          // Handle bulk upload validation errors with custom display
          this.handleBulkUploadError(err);

          // DO NOT reset selectedFile - keep it so user can retry with same file
          // this.selectedFile = null;  // REMOVED - this was causing button to stay disabled
          // const fileInput = document.getElementById('fileInput') as HTMLInputElement;
          // if (fileInput) fileInput.value = '';  // REMOVED - keep file selection for retry

          console.log('🔍 After error handling - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile, 'uploadType:', this.uploadType);
        }
      });
    } else if (this.uploadType === 'new_year_activity') {
      this.newYearActivityService.bulkUploadActivities(this.selectedFile).subscribe({
        next: (response) => {
          console.log('✅ 200 Status - New Year Activity Upload API Response Received');
          console.log('Response:', response);

          this.uploading = false;



          // Show success alert immediately after 200 status
          console.log('🎉 Dashboard showing success alert for new year activities');
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'New year activities uploaded successfully!',
            timer: 3000,
            showConfirmButton: false
          }).then(() => {
            console.log('✅ Dashboard success alert completed');
            // Refresh activity count after successful upload
            this.loadActivityCount();
            // Close modal after success notification
            this.modalService.dismissAll();
          });
        },
        error: (err) => {
          console.error('New Year Activity Upload error details:', err);

          // CRITICAL: Reset uploading state to re-enable upload button
          this.uploading = false;
          console.log('❌ New Year Activity upload failed - Button should be re-enabled (uploading = false)');
          console.log('🔍 Current state - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile, 'uploadType:', this.uploadType);

          // Handle bulk upload validation errors with custom display
          this.handleBulkUploadError(err);

          // DO NOT reset selectedFile - keep it so user can retry with same file
          // this.selectedFile = null;  // REMOVED - this was causing button to stay disabled
          // const fileInput = document.getElementById('fileInput') as HTMLInputElement;
          // if (fileInput) fileInput.value = '';  // REMOVED - keep file selection for retry

          console.log('🔍 After error handling - uploading:', this.uploading, 'selectedFile:', !!this.selectedFile, 'uploadType:', this.uploadType);
        }
      });
    } else {
      // For other upload types, show placeholder message
      this.uploading = false;
      this.uploadError = `Upload functionality for "${this.getUploadTypeLabel(this.uploadType)}" is not yet implemented.`;
    }
  }




  // Get readable label for upload type
  private getUploadTypeLabel(type: string): string {
    switch (type) {
      case 'new_year_activity':
        return 'New Year Activity';
      case 'new_employee':
        return 'New Employee Added';
      case 'bulk_employee':
        return 'Upload Bulk Employee';
      default:
        return type;
    }
  }

  // Open Assign Compoff modal
  openAssignCompoffModal(): void {
    // Load employees before opening modal
    this.loadEmployeesForCompoff();

    this.modalService.open(this.assignCompoffModal, {
      size: 'md',
      centered: true,
      backdrop: 'static'
    });

    // Reset form values
    this.assignCompoffForm.reset({
      employeeList: '',
      workingDate: '',
      remark: ''
    });
    this.assigningCompoff = false;
    this.assignCompoffError = null;
    this.workingDateError = null;
  }

  // Open Compoff Request modal
  openCompoffRequestModal(): void {
    this.modalService.open(this.compoffRequestModal, {
      size: 'md',
      centered: true,
      backdrop: 'static'
    });

    // Reset form values and date picker
    this.compoffRequestForm.reset({
      workingDate: '',
      reason: ''
    });
    this.compoffWorkingDatePicker = null;
    this.submittingRequest = false;
    this.compoffRequestError = null;
  }

  // Assign Compoff - Manager assignment functionality
  assignCompoff(): void {
    if (this.assignCompoffForm.invalid) {
      this.assignCompoffError = 'Please fill all required fields.';
      return;
    }

    this.assigningCompoff = true;
    this.assignCompoffError = null;

    const formValue = this.assignCompoffForm.value;
    console.log('🎯 Assigning comp-off with form data:', formValue);

    // Prepare manager comp-off assignment data
    const assignment = {
      employee_id: formValue.employeeList,
      working_date: formValue.workingDate,
      reason: formValue.remark,
      request_type: 'manager_assignment' as const
    };

    console.log('📤 Manager comp-off assignment payload:', assignment);

    // Call the comp-off service to create the assignment
    this.compoffService.requestCompoff(assignment).subscribe({
      next: (response: any) => {
        console.log('✅ Comp-off assignment successful:', response);
        this.assigningCompoff = false;

        // Close modal and reset form
        this.modalService.dismissAll();
        this.assignCompoffForm.reset();

        // Show success message like other leave operations
        Swal.fire({
          title: 'Success!',
          text: 'Comp-off has been assigned successfully.',
          icon: 'success',
          timer: 3000,
          showConfirmButton: false
        });
      },
      error: (error: any) => {
        console.error('❌ Comp-off assignment failed:', error);
        this.assigningCompoff = false;

        // Extract error message from API response - check multiple possible fields
        let errorMessage = 'Failed to assign comp-off. Please try again.';
        if (error?.error?.detail) {
          // Backend returns error in 'detail' field
          errorMessage = error.error.detail;
        } else if (error?.error?.message) {
          // Some APIs return error in 'message' field
          errorMessage = error.error.message;
        } else if (error?.detail) {
          // Direct detail field
          errorMessage = error.detail;
        } else if (error?.message) {
          // Direct message field
          errorMessage = error.message;
        }

        this.assignCompoffError = errorMessage;
      }
    });
  }

  // Test Employee API endpoint to debug the issue
  testEmployeeApi(): void {
    console.log('🔍 TESTING: Employee API endpoint...');
    this.compoffService.testEmployeeApiEndpoint().subscribe({
      next: (result) => {
        console.log('🔍 TESTING: Employee API test result:', result);
        if (result.success) {
          alert('✅ Employee API is working! Check console for details.');
        } else {
          alert(`❌ Employee API failed: ${result.message}\nStatus: ${result.status}\nError: ${result.error}`);
        }
      },
      error: (error) => {
        console.error('🔍 TESTING: Employee API test error:', error);
        alert(`❌ Employee API test failed: ${error.message}`);
      }
    });
  }

  // Submit Compoff Request
  submitCompoffRequest(): void {
    if (this.compoffRequestForm.invalid) {
      this.compoffRequestError = 'Please fill all required fields.';
      return;
    }

    this.submittingRequest = true;
    this.compoffRequestError = null;

    const formValue = this.compoffRequestForm.value;
    console.log('🎯 Submitting comp-off request with form data:', formValue);

    // Prepare comp-off request data
    const compoffRequest: CompoffRequestInput = {
      working_date: formValue.workingDate,
      reason: formValue.reason
    };

    // Debug: Check what employee_id would be used
    console.log('🔍 DEBUG: Checking employee_id before comp-off request...');
    this.compoffService.debugEmployeeIdComparison().subscribe({
      next: (debugInfo) => {
        console.log('🔍 DEBUG: Employee ID comparison result:', debugInfo);
      },
      error: (debugError) => {
        console.error('🔍 DEBUG: Employee ID comparison failed:', debugError);
      }
    });

    // Call the dedicated comp-off service directly
    this.compoffService.requestCompoff(compoffRequest).subscribe({
      next: (response: CompoffRequestResponse) => {
        console.log('✅ Comp-off request successful:', response);
        this.submittingRequest = false;

        // Show success message with request ID
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: `Comp-off request submitted successfully!`,
          timer: 4000,
          showConfirmButton: false
        }).then(() => {
          this.modalService.dismissAll();
          // Reset form
          this.compoffRequestForm.reset();
        });
      },
      error: (error) => {
        console.error('❌ Comp-off request failed:', error);
        this.submittingRequest = false;

        // Extract error message from API response
        let errorMessage = 'Failed to submit comp-off request. Please try again.';
        if (error?.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        this.compoffRequestError = errorMessage;
      }
    });
  }

  // Load employees for comp-off assignment
  loadEmployeesForCompoff(): void {
    if (this.employees.length > 0) {
      // Employees already loaded
      return;
    }

    this.loadingEmployees = true;
    console.log('📋 Loading employees for comp-off assignment...');

    this.employeeService.getEmployees().subscribe({
      next: (response) => {
        console.log('✅ Employees loaded for comp-off:', response);

        // Handle different API response structures
        let employees: any[] = [];
        if (response && typeof response === 'object' && response.success && Array.isArray(response.data)) {
          employees = response.data;
        } else if (Array.isArray(response)) {
          employees = response;
        }

        // Transform employees for dropdown display
        this.employees = employees.map(emp => ({
          id: emp.id || emp.employee_id,
          name: `${emp.first_name || emp.employee_first_name || ''} ${emp.last_name || emp.employee_last_name || ''}`.trim(),
          employee_code: emp.employee_code || emp.code || '',
          display: `${emp.employee_code || emp.code || 'N/A'} - ${emp.first_name || emp.employee_first_name || ''} ${emp.last_name || emp.employee_last_name || ''}`.trim()
        })).filter(emp => emp.id && emp.name); // Filter out invalid entries

        console.log('📋 Transformed employees for comp-off:', this.employees);
        this.loadingEmployees = false;
      },
      error: (error) => {
        console.error('❌ Failed to load employees for comp-off:', error);
        this.loadingEmployees = false;
        this.employees = [];
      }
    });
  }

  // Validate Working Date - Only allow Saturdays and Sundays
  validateWorkingDate(event: any): void {
    const selectedDate = event.target.value;
    this.workingDateError = null;

    if (selectedDate) {
      const date = new Date(selectedDate);
      const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday

      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        this.workingDateError = 'Please select only Saturday or Sunday. Weekdays are not allowed.';
        // Clear the invalid date
        this.assignCompoffForm.patchValue({
          workingDate: ''
        });
      }
    }
  }

  // Open date picker when calendar box is clicked
  openDatePicker(inputType: string): void {
    let inputElement: HTMLInputElement | null = null;

    if (inputType === 'workingDate') {
      inputElement = document.getElementById('workingDate') as HTMLInputElement;
    } else if (inputType === 'compoffWorkingDate') {
      inputElement = document.getElementById('workingDate') as HTMLInputElement;
    }

    if (inputElement) {
      // Focus the input first to ensure it's active
      inputElement.focus();
      // Trigger the date picker
      inputElement.showPicker();
    }
  }

  // Handle bulk upload errors with custom display
  private handleBulkUploadError(err: any): void {
    console.log('🔍 Dashboard processing bulk upload error:', err);

    // Handle 403 Authorization errors specifically
    if (err.status === 403) {
      this.show403AuthorizationError(err);
      return;
    }

    // Handle 401 Authentication errors
    if (err.status === 401) {
      this.show401AuthenticationError(err);
      return;
    }

    // Check for the specific API response format: { success: false, error: "..." }
    if (err.error && err.error.success === false && err.error.error) {
      const errorMessage = err.error.error;

      // Check for "Holiday already exists in database" error
      if (errorMessage.includes('Holiday already exists in database for this date')) {
        Swal.fire({
          icon: 'error',
          title: 'Upload Error',
          text: 'Holiday already exists in database',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
        return;
      }

      // Check for empty file error
      if (errorMessage.includes('File contains') && errorMessage.includes('rows but all are empty')) {
        Swal.fire({
          icon: 'error',
          title: 'Upload Failed',
          text: 'The file is empty or contains no valid data. Please add holiday activities to your file and try again.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
        return;
      }

      // Check for other bulk upload errors
      if (errorMessage.includes('Bulk upload failed:')) {
        this.showBulkUploadSummaryError(errorMessage);
        return;
      }

      // Handle employee upload validation errors (like duplicate email)
      if (this.uploadType === 'new_employee') {
        this.hasValidationErrors = true;
        this.lastUploadErrorData = err.error; // Store error data for download
        this.showEmployeeUploadErrorWithDownload(errorMessage);
        return;
      }

      // For other upload types, show regular error
      Swal.fire({
        icon: 'error',
        title: 'Upload Error',
        text: errorMessage,
        confirmButtonText: 'OK',
        confirmButtonColor: '#dc3545'
      });
      return;
    }

    // Check if this is a bulk upload validation error
    if (err.error && err.error.error && err.error.meta && err.error.meta.error_details) {
      this.hasValidationErrors = true;
      this.lastUploadErrorData = err.error; // Store error data for download
      this.showBulkUploadValidationErrors(err.error);
      return;
    }

    // Check for other error formats
    if (err.error && err.error.errors && Array.isArray(err.error.errors)) {
      this.hasValidationErrors = true;
      this.lastUploadErrorData = err.error; // Store error data for download
      this.showBulkUploadValidationErrors(err.error);
      return;
    }

    // Fallback to generic error display
    this.showGenericUploadError(err);
  }

  // Show detailed validation errors for bulk upload
  private showBulkUploadValidationErrors(errorData: any): void {
    console.log('📋 Dashboard showing bulk upload validation errors:', errorData);

    const errorDetails = errorData.meta?.error_details || errorData;
    const errors = errorDetails.errors || [];

    // Check if this is a bulk upload summary error (like the one in the screenshot)
    if (errorData.error && typeof errorData.error === 'string') {
      const errorText = errorData.error;

      // Check for specific "Holiday already exists" error first
      if (errorText.includes('Holiday already exists in database for this date')) {
        Swal.fire({
          icon: 'error',
          title: 'Upload Error',
          text: 'Holiday already exists in database',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
        return;
      }

      // Parse bulk upload summary errors
      if (errorText.includes('Bulk upload failed:')) {
        this.showBulkUploadSummaryError(errorText);
        return;
      }
    }

    if (errors.length > 0) {
      // Get the first error message and clean it up
      const firstError = errors[0];
      const errorMessage = this.getFormattedErrorMessage(firstError.error || firstError.error_message || 'Unknown error');

      // Show error with download button for employee uploads
      if (this.uploadType === 'new_employee') {
        this.showEmployeeUploadErrorWithDownload(errorMessage);
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Upload Error',
          text: errorMessage,
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
      }
    } else {
      // Fallback if no specific errors found
      const fallbackMessage = 'Upload failed due to validation errors. Please check your data and try again.';

      if (this.uploadType === 'new_employee') {
        this.showEmployeeUploadErrorWithDownload(fallbackMessage);
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Upload Error',
          text: fallbackMessage,
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
      }
    }
  }

  // Handle bulk upload summary errors (like "Bulk upload failed: 1 duplicate holiday dates, 1 validation errors")
  private showBulkUploadSummaryError(errorText: string): void {
    console.log('📋 Processing bulk upload summary error:', errorText);

    // Check for specific "Holiday already exists" error first
    if (errorText.includes('Holiday already exists in database for this date')) {
      Swal.fire({
        icon: 'error',
        title: 'Upload Error',
        text: 'Holiday already exists in database',
        confirmButtonText: 'OK',
        confirmButtonColor: '#dc3545'
      });
      return;
    }

    let title = 'Upload Issues Found';
    let message = 'Please review and fix the following issues in your file:';
    let issues: string[] = [];

    // Parse different types of errors from the summary
    if (errorText.includes('duplicate holiday dates')) {
      const duplicateMatch = errorText.match(/(\d+)\s+duplicate holiday dates/);
      const count = duplicateMatch ? duplicateMatch[1] : '1';

      // Check if it's specifically about existing holidays in database
      if (errorText.includes('Holiday already exists in database')) {
        issues.push(`• Holiday already exists for this date - Choose a different date or update the existing entry`);
      } else {
        issues.push(`• ${count} duplicate date${count !== '1' ? 's' : ''} found - Each activity must have a unique date`);
      }
    }

    if (errorText.includes('validation errors')) {
      const validationMatch = errorText.match(/(\d+)\s+validation errors/);
      const count = validationMatch ? validationMatch[1] : '1';
      issues.push(`• ${count} validation error${count !== '1' ? 's' : ''} - Check required fields and data formats`);
    }

    if (errorText.includes('invalid date format')) {
      issues.push('• Invalid date format - Use YYYY-MM-DD format (e.g., 2024-01-01)');
    }

    if (errorText.includes('missing required fields')) {
      issues.push('• Missing required fields - Activity name and date are required');
    }

    // If no specific issues were parsed, provide general guidance
    if (issues.length === 0) {
      issues.push('• Please check your data format and ensure all required fields are filled');
      issues.push('• Make sure dates are unique and in YYYY-MM-DD format');
      issues.push('• Verify that activity names are provided for all entries');
    }

    const fullMessage = message + '\n\n' + issues.join('\n') + '\n\nNo records were uploaded. Please fix these issues and try again.';

    Swal.fire({
      icon: 'error',
      title: title,
      html: fullMessage.replace(/\n/g, '<br>'),
      confirmButtonText: 'OK, I\'ll Fix These',
      confirmButtonColor: '#dc3545',
      width: '500px'
    });
  }



  // Show 403 Authorization error
  private show403AuthorizationError(err: any): void {
    console.log('🚫 403 Authorization Error:', err);

    Swal.fire({
      icon: 'warning',
      title: 'Access Denied',
      text: 'You do not have permission to upload employee data.',
      confirmButtonText: 'Close',
      confirmButtonColor: '#6c757d'
    });
  }

  // Show 401 Authentication error
  private show401AuthenticationError(err: any): void {
    console.log('🔑 401 Authentication Error:', err);

    // Close any open modals first
    this.closeAllModals();

    Swal.fire({
      icon: 'warning',
      title: 'Session Expired',
      html: `
        <div style="text-align: left;">
          <p><strong>Your session has expired or you are not authenticated.</strong></p>
          <br>
          <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;">
            <strong>🔑 Authentication Required:</strong><br>
            • Your login session has expired<br>
            • You need to log in again to continue<br>
            • Your authentication token is invalid
          </div>
          <br>
          <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #17a2b8;">
            <strong>💡 Next Steps:</strong><br>
            • Click "Login Again" to be redirected to the login page<br>
            • Enter your credentials to get a new session<br>
            • Try the upload again after logging in
          </div>
        </div>
      `,
      width: '500px',
      confirmButtonText: 'Login Again',
      confirmButtonColor: '#dc3545',
      showCancelButton: true,
      cancelButtonText: 'Close',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {
        // Close any open modals before redirecting
        this.closeAllModals();
        // Use AuthService logout but preserve remembered credentials for auto-login
        // Pass false to preserve "Remember Me" credentials even on session expiry
        this.authService.logout(false, 'Session expired - please log in again');
      }
    });
  }

  // Show generic upload error
  private showGenericUploadError(err: any): void {
    let errorMessage = err.error?.message || err.error?.error || err.message || 'Upload failed. Please try again.';

    // Handle other HTTP status codes
    let title = 'Upload Error';
    let icon: 'error' | 'warning' = 'error';

    switch (err.status) {
      case 400:
        title = 'Upload Error';
        icon = 'error';
        // For 400 errors, provide more helpful messaging
        if (this.uploadType === 'new_year_activity') {
          errorMessage = 'There are issues with your activity data. Please check:\n\n• All dates are in YYYY-MM-DD format\n• No duplicate dates\n• All required fields are filled\n• Activity names are provided';
        } else {
          errorMessage = 'There are issues with your data format. Please check the template and try again.';
        }
        break;
      case 413:
        title = 'Upload Error';
        icon = 'error';
        errorMessage = 'The selected file is too large. Please reduce the file size and try again.';
        break;
      case 415:
        title = 'Upload Error';
        icon = 'error';
        errorMessage = 'Please upload a valid Excel (.xlsx) or CSV (.csv) file.';
        break;
      case 500:
        title = 'Server Error';
        errorMessage = 'A server error occurred. Please try again later or contact support.';
        break;
      case 502:
      case 503:
      case 504:
        title = 'Service Unavailable';
        errorMessage = 'The service is temporarily unavailable. Please try again in a few minutes.';
        break;
    }

    Swal.fire({
      icon: icon,
      title: title,
      html: errorMessage.replace(/\n/g, '<br>'),
      confirmButtonText: 'OK',
      confirmButtonColor: '#dc3545',
      width: '450px'
    });
  }

  // Format error messages to be more user-friendly
  private getFormattedErrorMessage(errorMessage: string): string {
    if (!errorMessage) return 'Unknown error';

    // Handle duplicate email errors
    if (errorMessage.toLowerCase().includes('already exists') && errorMessage.toLowerCase().includes('email')) {
      const emailMatch = errorMessage.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      const email = emailMatch ? emailMatch[1] : 'this email';
      return `Email address ${email} is already registered in the system`;
    }

    // Handle holiday/activity specific errors
    if (errorMessage.toLowerCase().includes('holiday already exists in database for this date')) {
      return 'Holiday already exists for this date. Please choose a different date.';
    }

    if (errorMessage.toLowerCase().includes('file contains') &&
        errorMessage.toLowerCase().includes('rows but all are empty')) {
      return 'The file is empty or contains no valid data. Please add holiday activities to your file and try again.';
    }

    if (errorMessage.toLowerCase().includes('duplicate holiday dates') ||
        errorMessage.toLowerCase().includes('holiday already exists')) {
      return 'Holiday already exists for this date. Please choose a different date.';
    }

    if (errorMessage.toLowerCase().includes('holiday_date') &&
        errorMessage.toLowerCase().includes('already exists')) {
      return 'Holiday already exists for this date. Please choose a different date.';
    }

    if (errorMessage.toLowerCase().includes('activity_name') &&
        errorMessage.toLowerCase().includes('required')) {
      return 'Activity name is required for all entries.';
    }

    if (errorMessage.toLowerCase().includes('date') &&
        errorMessage.toLowerCase().includes('required')) {
      return 'Date is required for all activities.';
    }

    if (errorMessage.toLowerCase().includes('invalid date format')) {
      return 'Invalid date format. Please use YYYY-MM-DD format (e.g., 2024-01-01).';
    }

    if (errorMessage.toLowerCase().includes('status') &&
        errorMessage.toLowerCase().includes('invalid')) {
      return 'Invalid status. Use: planned, ongoing, completed, or cancelled.';
    }

    // Handle other validation errors
    if (errorMessage.includes('IFSC code must be in valid format')) {
      return 'Invalid IFSC Code: Must be exactly 4 CAPITAL letters + 0 + 6 alphanumeric characters';
    }

    if (errorMessage.includes('Input should be a valid date')) {
      return 'Invalid Date Format: Use YYYY-MM-DD format (e.g., 1990-01-15)';
    }

    if (errorMessage.includes('value is not a valid email address')) {
      return 'Invalid Email: Please provide a valid email address';
    }

    // Clean up the message by removing technical prefixes
    let cleanMessage = errorMessage;
    const prefixesToRemove = [
      'Employee with office email',
      'Employee with',
      'Validation error:',
      'Error:',
      'Exception:'
    ];

    prefixesToRemove.forEach(prefix => {
      if (cleanMessage.startsWith(prefix)) {
        cleanMessage = cleanMessage.substring(prefix.length).trim();
      }
    });

    // Capitalize first letter
    if (cleanMessage && cleanMessage.length > 0) {
      cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
    }

    return cleanMessage || errorMessage;
  }

  // Load New Year Activity Count
  loadActivityCount(): void {
    this.loadingCount = true;
    this.newYearActivityService.getActivitiesCount().subscribe({
      next: (count) => {
        this.activityCount = count;
        this.loadingCount = false;
        console.log('✅ Activity count loaded:', count);
      },
      error: (error) => {
        console.error('❌ Failed to load activity count:', error);
        this.loadingCount = false;
      }
    });
  }

  // Export New Year Activities
  exportNewYearActivities(): void {
    this.exporting = true;

    this.newYearActivityService.exportActivities('excel').subscribe({
      next: (blob) => {
        // Generate filename with current date
        const currentDate = new Date().toISOString().split('T')[0];
        const filename = `new_year_activities_${currentDate}`;

        // Download the file
        this.newYearActivityService.downloadExportedFile(blob, filename);

        this.exporting = false;

        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Export Successful!',
          text: 'New Year Activities have been exported successfully.',
          timer: 3000,
          showConfirmButton: false
        });

        console.log('✅ New Year Activities exported successfully');
      },
      error: (error) => {
        console.error('❌ Failed to export activities:', error);
        this.exporting = false;

        // Show error message
        Swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'Failed to export New Year Activities. Please try again.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
      }
    });
  }

  // Close all open modals
  private closeAllModals(): void {
    try {
      // Close NgBootstrap modals
      this.modalService.dismissAll();

      // Close any SweetAlert2 dialogs
      Swal.close();

      console.log('✅ All modals closed before logout');
    } catch (error) {
      console.error('❌ Error closing modals:', error);
    }
  }

  /**
   * Display current user role and permissions for testing
   */
  private displayUserRoleInfo(): void {
    const user = this.authService.currentUserValue;
    if (user) {
      console.log('👤 Current User Role Info:');
      console.log('📧 Email:', user.email);
      console.log('🎭 Role:', user.role);
      console.log('🔑 Permissions:', user.permissions || []);

      // Test specific permissions
      const testPermissions = ['leave:create', 'leave:approve', 'attendance:read'];
      console.log('🧪 Permission Tests:');
      testPermissions.forEach(permission => {
        const hasPermission = this.authService.hasPermission(permission);
        console.log(`${hasPermission ? '✅' : '❌'} ${permission}`);
      });
    }
  }

  // Show employee upload error with download button
  private showEmployeeUploadErrorWithDownload(errorMessage: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Upload Error',
      text: errorMessage,
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Download Error Report',
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      reverseButtons: true
    }).then((result) => {
      if (result.dismiss === Swal.DismissReason.cancel) {
        // User clicked "Download Error Report"
        this.downloadErrorReport();
      }
    });
  }

  // Download error report for bulk upload validation errors
  downloadErrorReport(): void {
    // Check if we have validation error data from the API response
    if (this.lastUploadErrorData && this.hasValidationErrors) {
      console.log('📥 Downloading error report using API error data...');
      this.downloadErrorReportFromAPI();
      return;
    }

    // Fallback to file-based download for HR Admin modal
    if (!this.selectedFile) {
      console.error('No file available for error report');
      return;
    }

    console.log('📥 Downloading bulk upload error report from HR Admin modal...');
    this.downloadingErrors = true;

    this.employeeService.downloadBulkUploadErrorReport(this.selectedFile).subscribe({
      next: (blob) => {
        console.log('✅ Error report downloaded successfully');

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `employee_upload_errors_${new Date().toISOString().split('T')[0]}.xlsx`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);

        this.downloadingErrors = false;

        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Error Report Downloaded',
          text: 'The error report has been downloaded successfully.',
          timer: 3000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end'
        });
      },
      error: (err) => {
        console.error('❌ Error downloading error report:', err);
        this.downloadingErrors = false;

        Swal.fire({
          icon: 'error',
          title: 'Download Failed',
          text: 'Failed to download error report. Please try again.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
      }
    });
  }








  // Download error report using API error data
  private downloadErrorReportFromAPI(): void {
    if (!this.lastUploadErrorData) {
      console.error('❌ No error data available for download');
      return;
    }

    console.log('📥 Downloading error report using API error data...');
    this.downloadingErrors = true;

    // Extract error details from the API response and send the entire object structure
    const downloadPayload = this.lastUploadErrorData.meta?.error_details || this.lastUploadErrorData;

    console.log('📤 Sending download request with payload:', downloadPayload);

    // Use the server-side error download API
    this.employeeService.downloadBulkUploadErrors(downloadPayload).subscribe({
      next: (response: Blob) => {
        console.log('✅ Error report downloaded successfully');
        this.downloadingErrors = false;

        // Create download link
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `employee_bulk_upload_errors_${new Date().getTime()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Error Report Downloaded',
          text: 'The error report has been downloaded successfully.',
          timer: 3000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end'
        });
      },
      error: (error: any) => {
        console.error('❌ Error downloading error report:', error);
        this.downloadingErrors = false;

        Swal.fire({
          icon: 'error',
          title: 'Download Failed',
          text: 'Failed to download error report. Please try again.',
          confirmButtonText: 'OK',
          confirmButtonColor: '#dc3545'
        });
      }
    });
  }

  // ===== COMP OFF CALENDAR METHODS =====

  // Comp off date filter function - OPPOSITE logic to apply leave
  // Returns TRUE for DISABLED dates, FALSE for ENABLED dates (ng-bootstrap markDisabled expects this)
  // For comp off: ENABLE weekends and holidays, DISABLE weekdays
  compoffDateFilter = (date: NgbDate | null): boolean => {
    if (!date) return true; // Disable null dates

    // Convert NgbDate to JavaScript Date
    const jsDate = new Date(date.year, date.month - 1, date.day);

    // Check if it's a weekend - ENABLE weekends (return FALSE to enable)
    const dayOfWeek = jsDate.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday = 0, Saturday = 6
      return false; // Return FALSE to ENABLE weekends
    }

    // Check if it's a holiday - ENABLE holidays (return FALSE to enable)
    if (this.holidayDates.length > 0) {
      const dateStr = this.formatDate(jsDate);
      if (this.holidayDates.includes(dateStr)) {
        return false; // Return FALSE to ENABLE holidays
      }
    }

    // Return TRUE to DISABLE regular weekdays that are not holidays
    return true;
  };

  // Load holidays for comp off datepicker filtering
  loadHolidaysForCompoff(): void {
    this.calendarService.getHolidays().subscribe({
      next: (holidays) => {
        this.holidays = holidays;
        this.holidayDates = holidays.map(h => h.date);
        this.holidaysLoaded = true;

        console.log(`✅ Loaded ${this.holidayDates.length} holidays for comp off datepicker`);
      },
      error: (error) => {
        console.error('❌ Error loading holidays for comp off datepicker:', error);
        this.holidays = [];
        this.holidayDates = [];
        this.holidaysLoaded = false;
      }
    });
  }

  // Handle comp off date picker selection
  onCompoffDatePickerSelect(date: NgbDate): void {
    this.compoffWorkingDatePicker = date;

    // Convert NgbDate to YYYY-MM-DD format for the form
    if (date) {
      const formattedDate = `${date.year}-${date.month.toString().padStart(2, '0')}-${date.day.toString().padStart(2, '0')}`;
      this.compoffRequestForm.patchValue({ workingDate: formattedDate });
      console.log('📅 Comp off date selected:', formattedDate);
    }
  }

  // Format date to YYYY-MM-DD string
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

}
