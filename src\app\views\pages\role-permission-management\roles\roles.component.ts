import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { RoleService } from '../../../../core/services/role.service';
import { Role, Permission, RoleCreate, RoleUpdate } from '../../../../core/models/role.model';
import { catchError, debounceTime, finalize, of, forkJoin } from 'rxjs';
import Swal from 'sweetalert2';

// Sortable directive
export type SortColumn = keyof Role | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number | null | undefined, v2: string | number | null | undefined) {
  if (v1 === null || v1 === undefined) return -1;
  if (v2 === null || v2 === undefined) return 1;
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-roles',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbdSortableHeader,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './roles.component.html',
  styleUrl: './roles.component.scss'
})
export class RolesComponent implements OnInit {
  // Data
  roles: Role[] = [];
  permissions: Permission[] = [];
  selectedRole: Role | null = null;

  // Loading state
  loading = false;
  submitting = false;
  permissionsLoading = false;

  // Form
  roleForm: FormGroup;
  formMode: 'create' | 'edit' = 'create';
  currentRoleId?: string;

  // Permission assignment
  selectedPermissions: Set<string> = new Set();

  // Search and Filters
  searchTerm = new FormControl('', { nonNullable: true });
  statusFilter = 'all';
  sortBy = 'name';

  // Advanced Filters
  showAdvancedFilters = false;
  advancedFilters = {
    userCountMin: null as number | null,
    userCountMax: null as number | null,
    permissionCountMin: null as number | null,
    permissionCountMax: null as number | null,
    createdAfter: '',
    createdBefore: '',
    roleType: '',
    hasUsers: '',
    hasPermissions: '',
    descriptionContains: '',
    sortOrder: 'asc' as 'asc' | 'desc'
  };

  // View Mode
  viewMode: 'cards' | 'table' = 'cards';

  // Pagination
  page = 1;
  pageSize = 12;
  totalItems = 0;

  // Make Math available in template
  Math = Math;

  // Helper method to get short ID
  getShortId(id: string | number): string {
    if (!id) return '';
    const idStr = id.toString();
    return idStr.length > 8 ? idStr.substring(0, 8) : idStr;
  }

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  constructor(
    private roleService: RoleService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private router: Router
  ) {
    // Initialize form
    this.roleForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['']
    });
  }

  ngOnInit(): void {
    // Load filter presets if available
    this.loadFilterPreset();

    // Load initial data
    this.loadRoles();
    this.loadPermissions();

    // Set up search
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.page = 1;
      this.loadRoles();
    });
  }

  loadRoles() {
    this.loading = true;
    console.log('Loading roles - page:', this.page, 'pageSize:', this.pageSize, 'search:', this.searchTerm.value);

    // Convert page/size to skip/limit for new API format
    const skip = (this.page - 1) * this.pageSize;
    this.roleService.getRoles(skip, this.pageSize, this.searchTerm.value)
      .pipe(
        catchError(error => {
          console.error('Error loading roles:', error);
          this.showErrorMessage('Failed to load roles. Please try again.');
          return of({ data: [], error: null, meta: null, success: false });
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        console.log('Roles loaded successfully:', response);
        // Handle the actual API response structure
        if (response.success && response.data && Array.isArray(response.data)) {
          let roles = response.data;

          // Apply status filter
          if (this.statusFilter !== 'all') {
            if (this.statusFilter === 'active') {
              roles = roles.filter(role => !role.deleted_at);
            } else if (this.statusFilter === 'deleted') {
              roles = roles.filter(role => role.deleted_at);
            }
          }

          // Apply advanced filters
          if (this.getActiveFiltersCount() > 0) {
            roles = this.applyAdvancedFiltersToRoles(roles);
          }

          // Apply sorting
          if (this.sortBy && this.sortBy !== 'name') {
            roles = this.applySorting(roles);
          }

          this.roles = roles;

          // Handle new pagination structure
          if (response.meta && response.meta.pagination) {
            this.totalItems = response.meta.pagination.total_count || response.data.length;
            console.log('📊 Pagination info (new structure):', {
              totalItems: this.totalItems,
              currentPage: response.meta.pagination.current_page,
              totalPages: response.meta.pagination.total_pages,
              hasNext: response.meta.pagination.has_next
            });
          } else {
            // Fallback to old structure or data length
            this.totalItems = response.meta?.total || response.data.length;
            console.log('📊 Pagination info (fallback):', { totalItems: this.totalItems });
          }

          console.log('Roles set:', this.roles.length, 'roles');
          console.log('First role permissions:', this.roles[0]?.permissions);
          console.log('Sample role data:', this.roles[0]);

          // If permissions are not included, load them separately
          if (this.roles.length > 0 && !this.roles[0].permissions) {
            console.log('Permissions not included in roles response, loading separately...');
            this.loadPermissionsForRoles();
          }
        } else {
          console.error('API response indicates failure or unexpected structure:', response);
          this.roles = [];
          this.totalItems = 0;
        }
      });
  }

  // Load permissions for each role separately
  loadPermissionsForRoles() {
    if (!this.roles || this.roles.length === 0) return;

    // Create an array of permission loading requests, filtering out roles with invalid IDs
    const permissionRequests = this.roles
      .filter(role => role && role.id) // Only include roles with valid IDs
      .map(role =>
        this.roleService.getRolePermissions(role.id).pipe(
          catchError(error => {
            console.error(`Error loading permissions for role ${role.id}:`, error);
            return of([]); // Return empty array on error
          })
        )
      );

    if (permissionRequests.length === 0) {
      console.warn('No valid roles found for permission loading');
      return;
    }

    // Execute all requests in parallel
    forkJoin(permissionRequests).subscribe(permissionsArrays => {
      // Assign permissions to each role (only for roles with valid IDs)
      let requestIndex = 0;
      this.roles.forEach((role) => {
        if (role && role.id) {
          role.permissions = permissionsArrays[requestIndex];
          requestIndex++;
        } else {
          role.permissions = []; // Set empty array for invalid roles
        }
      });
      console.log('Permissions loaded for all roles:', this.roles);
    });
  }

  loadPermissions() {
    this.permissionsLoading = true;
    console.log('Loading permissions...');

    this.roleService.getAllPermissions()
      .pipe(
        catchError(error => {
          console.error('Error loading permissions:', error);
          return of([]);
        }),
        finalize(() => {
          this.permissionsLoading = false;
        })
      )
      .subscribe(permissions => {
        console.log('Permissions loaded:', permissions);
        this.permissions = permissions;
      });
  }

  // Refresh when pagination changes
  onPageChange(page: number) {
    this.page = page;
    this.loadRoles();
  }

  // Handle sorting
  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.loadRoles();
    } else {
      this.roles = [...this.roles].sort((a, b) => {
        const aValue = Array.isArray(a[column]) ? a[column].length : a[column];
        const bValue = Array.isArray(b[column]) ? b[column].length : b[column];
        const res = compare(aValue, bValue);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  // Open modal for creating/editing
  openRoleModal(modal: TemplateRef<any>, role?: Role) {
    if (role) {
      // Edit mode
      this.formMode = 'edit';
      this.currentRoleId = role.id;
      this.roleForm.patchValue({
        name: role.name,
        description: role.description || ''
      });

      // Load current permissions for the role
      this.selectedPermissions.clear();
      console.log('Opening role modal for editing. Role:', role);

      // Validate role.id before making API call
      if (role.id) {
        this.roleService.getRolePermissions(role.id).subscribe({
          next: (permissions) => {
            console.log('Loaded permissions for role:', permissions);
            permissions.forEach(permission => {
              this.selectedPermissions.add(permission.id);
              console.log('Added permission to selected set:', permission.id, permission.name);
            });
            console.log('Final selected permissions set:', Array.from(this.selectedPermissions));
          },
          error: (error) => {
            console.error('Error loading permissions for editing:', error);
          }
        });
      } else {
        console.error('Invalid role ID for editing:', role);
      }
    } else {
      // Create mode
      this.formMode = 'create';
      this.currentRoleId = undefined;
      this.roleForm.reset({
        name: '',
        description: ''
      });
      this.selectedPermissions.clear();
      console.log('Opening role modal for creation');
    }

    this.modalService.open(modal, { centered: true, size: 'lg' });
  }

  // Permission checkbox handling
  onPermissionChange(permissionId: string, event: Event) {
    const checkbox = event.target as HTMLInputElement;

    console.log('Permission change event:', {
      permissionId,
      checked: checkbox.checked,
      beforeChange: Array.from(this.selectedPermissions)
    });

    if (checkbox.checked) {
      this.selectedPermissions.add(permissionId);
      console.log('Permission added:', permissionId, 'Total selected:', this.selectedPermissions.size);
    } else {
      this.selectedPermissions.delete(permissionId);
      console.log('Permission removed:', permissionId, 'Total selected:', this.selectedPermissions.size);
    }

    // Log current selected permissions for debugging
    console.log('Current selected permissions after change:', Array.from(this.selectedPermissions));

    // Force change detection to ensure UI updates
    setTimeout(() => {
      console.log('Delayed check - selected permissions:', Array.from(this.selectedPermissions));
    }, 100);
  }

  isPermissionSelected(permissionId: string): boolean {
    const isSelected = this.selectedPermissions.has(permissionId);
    // Uncomment for debugging checkbox state issues
    // console.log(`Checking if permission ${permissionId} is selected:`, isSelected);
    return isSelected;
  }

  // Debug method to check current state
  debugPermissionState() {
    console.log('=== PERMISSION STATE DEBUG ===');
    console.log('Selected permissions:', Array.from(this.selectedPermissions));
    console.log('Available permissions:', this.permissions?.map(p => ({ id: p.id, name: p.name })));
    console.log('Form mode:', this.formMode);
    console.log('Current role ID:', this.currentRoleId);
  }

  // Select/Deselect all permissions
  // toggleAllPermissions(event: Event) {
  //   const checkbox = event.target as HTMLInputElement;

  //   if (checkbox.checked) {
  //     if (this.permissions && Array.isArray(this.permissions)) {
  //       this.permissions.forEach(permission => {
  //         this.selectedPermissions.add(permission.id);
  //       });
  //       console.log('All permissions selected:', this.selectedPermissions.size);
  //     }
  //   } else {
  //     this.selectedPermissions.clear();
  //     console.log('All permissions deselected');
  //   }

  //   console.log('Current selected permissions after toggle all:', Array.from(this.selectedPermissions));
  // }

  areAllPermissionsSelected(): boolean {
    return this.permissions && this.permissions.length > 0 && this.selectedPermissions.size === this.permissions.length;
  }

  areSomePermissionsSelected(): boolean {
    return this.permissions && this.selectedPermissions.size > 0 && this.selectedPermissions.size < this.permissions.length;
  }

  // Save role (create or update)
  saveRole() {
    if (this.roleForm.invalid) {
      // Mark all fields as touched to show validation errors
      this.roleForm.markAllAsTouched();
      return;
    }

    console.log('=== SAVING ROLE ===');
    console.log('Form mode:', this.formMode);
    console.log('Current role ID:', this.currentRoleId);
    console.log('Selected permissions before save:', Array.from(this.selectedPermissions));
    console.log('Number of selected permissions:', this.selectedPermissions.size);

    this.submitting = true;

    if (this.formMode === 'create') {
      // Create new role
      const newRole: RoleCreate = this.roleForm.value;
      console.log('Creating role with data:', newRole);
      console.log('Selected permissions:', Array.from(this.selectedPermissions));

      this.roleService.createRole(newRole)
        .pipe(
          catchError(error => {
            console.error('Error creating role:', error);
            // Enhanced error handling
            let errorMsg = 'Failed to create role. Please try again.';
            if (error.status === 422 && error.error && error.error.detail) {
              // FastAPI/Pydantic style validation errors
              if (Array.isArray(error.error.detail)) {
                errorMsg = error.error.detail.map((d: any) => d.msg || JSON.stringify(d)).join(' | ');
              } else {
                errorMsg = error.error.detail;
              }
            } else if (error.error && error.error.message) {
              errorMsg = error.error.message;
            } else if (error.message) {
              errorMsg = error.message;
            }
            this.showErrorMessage(errorMsg);
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            console.log('🎉 Role created successfully!');
            console.log('📋 Full API response:', JSON.stringify(result, null, 2));
            console.log('📋 Response type:', typeof result);
            console.log('📋 Response keys:', Object.keys(result || {}));

            // Extract role ID from response (handle different response formats)
            let createdRoleId: string | null = null;
            if (result.id) {
              createdRoleId = result.id;
              console.log('✅ Found role ID in result.id:', createdRoleId);
            } else if (result.data && result.data.id) {
              createdRoleId = result.data.id;
              console.log('✅ Found role ID in result.data.id:', createdRoleId);
            } else if (typeof result === 'string') {
              createdRoleId = result; // In case API returns just the ID
              console.log('✅ Found role ID as string:', createdRoleId);
            } else {
              console.log('❌ Could not find role ID in response');
              console.log('Available properties:', Object.keys(result || {}));
            }

            console.log('🆔 Final extracted role ID:', createdRoleId);

            // Always close modal and refresh list first
            this.modalService.dismissAll();
            this.showSuccessMessage('Role created successfully!');

            // Reset to first page to see the new role
            this.page = 1;
            this.loadRoles();

            // Assign permissions if any selected and we have a valid role ID
            if (this.selectedPermissions.size > 0 && createdRoleId) {
              console.log('Assigning permissions to newly created role:', createdRoleId);
              // Add a small delay to ensure the role is fully created before assigning permissions
              setTimeout(() => {
                this.assignPermissionsToRole(createdRoleId!, Array.from(this.selectedPermissions));
              }, 500);
            } else if (this.selectedPermissions.size > 0 && !createdRoleId) {
              console.error('Cannot assign permissions: No role ID found in response');
              console.error('Full API response:', result);
              this.showErrorMessage('Role created but failed to assign permissions. Please edit the role to add permissions.');
            }
          } else {
            console.log('Role creation failed - no result returned');
          }
        });
    } else if (this.formMode === 'edit' && this.currentRoleId) {
      // Update existing role
      const updatedRole: RoleUpdate = this.roleForm.value;

      this.roleService.updateRole(this.currentRoleId, updatedRole)
        .pipe(
          catchError(error => {
            console.error('Error updating role:', error);
            // Enhanced error handling
            let errorMsg = 'Failed to update role. Please try again.';
            if (error.status === 422 && error.error && error.error.detail) {
              // FastAPI/Pydantic style validation errors
              if (Array.isArray(error.error.detail)) {
                errorMsg = error.error.detail.map((d: any) => d.msg || JSON.stringify(d)).join(' | ');
              } else {
                errorMsg = error.error.detail;
              }
            } else if (error.error && error.error.message) {
              errorMsg = error.error.message;
            } else if (error.message) {
              errorMsg = error.message;
            }
            this.showErrorMessage(errorMsg);
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            // Update permissions
            console.log('Role updated, now assigning permissions:', Array.from(this.selectedPermissions));
            this.assignPermissionsToRole(this.currentRoleId!, Array.from(this.selectedPermissions));
          }
        });
    }
  }

  private assignPermissionsToRole(roleId: string, permissionIds: string[]) {
    console.log('🎯 Assigning permissions to role:', roleId, permissionIds);

    // Always call the API, even with empty array to clear permissions
    this.roleService.assignPermissionsToRole(roleId, permissionIds)
      .pipe(
        catchError(error => {
          console.error('❌ Permission assignment failed:', error);
          console.error('Error details:', error.error);
          this.showErrorMessage('Role saved but failed to assign permissions. Please try again.');
          return of(null);
        })
      )
      .subscribe((result) => {
        if (result) {
          console.log('✅ Permissions assigned successfully:', result);

          // Verify permissions were assigned by fetching them
          this.roleService.getRolePermissions(roleId).subscribe(permissions => {
            console.log('🔍 Verification - permissions after assignment:', permissions);
            console.log('🔍 Expected permissions:', permissionIds);
            console.log('🔍 Actual permissions:', permissions.map(p => p.id));

            // Check if the assignment worked
            const expectedSet = new Set(permissionIds);
            const actualSet = new Set(permissions.map(p => p.id));
            const isMatch = expectedSet.size === actualSet.size &&
                           [...expectedSet].every(id => actualSet.has(id));

            if (isMatch) {
              console.log('✅ Permission verification PASSED');
            } else {
              console.log('❌ Permission verification FAILED');
              console.log('Missing permissions:', [...expectedSet].filter(id => !actualSet.has(id)));
              console.log('Extra permissions:', [...actualSet].filter(id => !expectedSet.has(id)));
            }
          });

          // For edit mode, we still need to close modal and refresh
          if (this.formMode === 'edit') {
            this.modalService.dismissAll();
            this.showSuccessMessage('Role updated successfully!');
            this.page = 1;
            this.loadRoles();
          } else {
            // For create mode, just refresh the list to show updated permissions
            this.loadRoles();
          }
        }
      });
  }

  // View role details
  viewRole(role: Role, modal: TemplateRef<any>) {
    // Validate role and role.id
    if (!role || !role.id) {
      console.error('Invalid role provided to viewRole:', role);
      this.showErrorMessage('Invalid role selected. Please try again.');
      return;
    }

    this.roleService.getRole(role.id)
      .pipe(
        catchError(error => {
          console.error('Error fetching role details:', error);
          this.showErrorMessage('Failed to fetch role details. Please try again.');
          return of(null);
        })
      )
      .subscribe(result => {
        if (result && result.id) {
          this.selectedRole = result;

          // Always load permissions separately to ensure we have the latest data
          this.roleService.getRolePermissions(result.id).subscribe({
            next: (permissions) => {
              this.selectedRole!.permissions = permissions;
            },
            error: (error) => {
              console.error('Error loading permissions for role:', error);
              // Set empty array if permission loading fails
              this.selectedRole!.permissions = [];
            }
          });

          this.modalService.open(modal, { centered: true, size: 'lg' });
        } else {
          this.showErrorMessage('Failed to fetch role details. Please try again.');
        }
      });
  }

  // Delete role
  deleteRole(role: Role) {
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete role "${role.name}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.roleService.deleteRole(role.id)
          .pipe(
            catchError(error => {
              console.error('Error deleting role:', error);
              this.showErrorMessage('Failed to delete role. Please try again.');
              return of(null);
            })
          )
          .subscribe(result => {
            if (result !== null) {
              this.showSuccessMessage('Role deleted successfully!');
              this.loadRoles();
            }
          });
      }
    });
  }

  // Restore role
  restoreRole(role: Role) {
    this.roleService.restoreRole(role.id)
      .pipe(
        catchError(error => {
          console.error('Error restoring role:', error);
          this.showErrorMessage('Failed to restore role. Please try again.');
          return of(null);
        })
      )
      .subscribe(result => {
        if (result) {
          this.showSuccessMessage('Role restored successfully!');
          this.loadRoles();
        }
      });
  }

  // Navigate to permissions management page for a role
  managePermissions(role: Role) {
    if (!role || !role.id) {
      console.error('Invalid role provided to managePermissions:', role);
      this.showErrorMessage('Invalid role selected. Please try again.');
      return;
    }

    console.log('Navigating to permissions management for role:', role.name, 'ID:', role.id);

    // Navigate to the role-permissions page with role information
    this.router.navigate(['/role-permission-management/role-permissions'], {
      queryParams: {
        roleId: role.id,
        roleName: role.name
      }
    });
  }

  // Helper method to handle editing from view modal
  editFromViewModal(roleModal: TemplateRef<any>, currentModal: any) {
    if (this.selectedRole) {
      currentModal.dismiss();
      this.openRoleModal(roleModal, this.selectedRole);
    }
  }

  // Copy ID to clipboard
  copyIdToClipboard(id: string | number): void {
    const idStr = id.toString();
    navigator.clipboard.writeText(idStr).then(() => {
      this.showSuccessMessage('ID copied to clipboard!');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = idStr;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.showSuccessMessage('ID copied to clipboard!');
    });
  }

  // Alerts
  showSuccessMessage(message: string) {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
      timer: 2000,
      showConfirmButton: false
    });
  }

  showErrorMessage(message: string) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message
    });
  }

  // New methods for enhanced UI
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
  }

  resetFilters(): void {
    this.searchTerm.setValue('');
    this.statusFilter = 'all';
    this.sortBy = 'name';
    this.page = 1;
    this.loadRoles();
  }

  getActiveRolesCount(): number {
    return this.roles.filter(role => !role.deleted_at).length;
  }

  getTotalUsersCount(): number {
    return this.roles.reduce((total, role) => {
      return total + (role.users ? role.users.length : 0);
    }, 0);
  }

  getTotalPermissionsCount(): number {
    const uniquePermissions = new Set();
    this.roles.forEach(role => {
      if (role.permissions) {
        role.permissions.forEach(permission => {
          uniquePermissions.add(permission.id);
        });
      }
    });
    return uniquePermissions.size;
  }

  // Advanced Filter Methods
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  applyAdvancedFilters(): void {
    // Apply filters and reload roles
    this.page = 1; // Reset to first page
    this.loadRoles();
  }

  clearAdvancedFilters(): void {
    this.advancedFilters = {
      userCountMin: null,
      userCountMax: null,
      permissionCountMin: null,
      permissionCountMax: null,
      createdAfter: '',
      createdBefore: '',
      roleType: '',
      hasUsers: '',
      hasPermissions: '',
      descriptionContains: '',
      sortOrder: 'asc'
    };
    this.applyAdvancedFilters();
  }

  getActiveFiltersCount(): number {
    let count = 0;

    if (this.advancedFilters.userCountMin !== null) count++;
    if (this.advancedFilters.userCountMax !== null) count++;
    if (this.advancedFilters.permissionCountMin !== null) count++;
    if (this.advancedFilters.permissionCountMax !== null) count++;
    if (this.advancedFilters.createdAfter) count++;
    if (this.advancedFilters.createdBefore) count++;
    if (this.advancedFilters.roleType) count++;
    if (this.advancedFilters.hasUsers) count++;
    if (this.advancedFilters.hasPermissions) count++;
    if (this.advancedFilters.descriptionContains) count++;

    return count;
  }

  saveFilterPreset(): void {
    // Save current filter settings to localStorage
    const preset = {
      ...this.advancedFilters,
      statusFilter: this.statusFilter,
      sortBy: this.sortBy,
      searchTerm: this.searchTerm.value
    };

    localStorage.setItem('roleFilterPreset', JSON.stringify(preset));
    this.showSuccessMessage('Filter preset saved successfully');
  }

  loadFilterPreset(): void {
    const saved = localStorage.getItem('roleFilterPreset');
    if (saved) {
      try {
        const preset = JSON.parse(saved);
        this.advancedFilters = { ...preset };
        this.statusFilter = preset.statusFilter || 'all';
        this.sortBy = preset.sortBy || 'name';
        this.searchTerm.setValue(preset.searchTerm || '');
        this.applyAdvancedFilters();
      } catch (error) {
        console.error('Error loading filter preset:', error);
      }
    }
  }

  private applyAdvancedFiltersToRoles(roles: Role[]): Role[] {
    let filteredRoles = [...roles];

    // Apply user count filter
    if (this.advancedFilters.userCountMin !== null) {
      filteredRoles = filteredRoles.filter(role =>
        (role.users?.length || 0) >= this.advancedFilters.userCountMin!
      );
    }
    if (this.advancedFilters.userCountMax !== null) {
      filteredRoles = filteredRoles.filter(role =>
        (role.users?.length || 0) <= this.advancedFilters.userCountMax!
      );
    }

    // Apply permission count filter
    if (this.advancedFilters.permissionCountMin !== null) {
      filteredRoles = filteredRoles.filter(role =>
        (role.permissions?.length || 0) >= this.advancedFilters.permissionCountMin!
      );
    }
    if (this.advancedFilters.permissionCountMax !== null) {
      filteredRoles = filteredRoles.filter(role =>
        (role.permissions?.length || 0) <= this.advancedFilters.permissionCountMax!
      );
    }

    // Apply date filters
    if (this.advancedFilters.createdAfter) {
      const afterDate = new Date(this.advancedFilters.createdAfter);
      filteredRoles = filteredRoles.filter(role =>
        new Date(role.created_at) >= afterDate
      );
    }
    if (this.advancedFilters.createdBefore) {
      const beforeDate = new Date(this.advancedFilters.createdBefore);
      filteredRoles = filteredRoles.filter(role =>
        new Date(role.created_at) <= beforeDate
      );
    }

    // Apply role type filter
    if (this.advancedFilters.roleType) {
      filteredRoles = filteredRoles.filter(role => {
        const roleName = role.name.toLowerCase();
        switch (this.advancedFilters.roleType) {
          case 'admin': return roleName.includes('admin');
          case 'user': return roleName.includes('user');
          case 'system': return roleName.includes('system');
          case 'custom': return !roleName.includes('admin') && !roleName.includes('user') && !roleName.includes('system');
          default: return true;
        }
      });
    }

    // Apply has users filter
    if (this.advancedFilters.hasUsers) {
      const hasUsers = this.advancedFilters.hasUsers === 'true';
      filteredRoles = filteredRoles.filter(role =>
        hasUsers ? (role.users?.length || 0) > 0 : (role.users?.length || 0) === 0
      );
    }

    // Apply has permissions filter
    if (this.advancedFilters.hasPermissions) {
      const hasPermissions = this.advancedFilters.hasPermissions === 'true';
      filteredRoles = filteredRoles.filter(role =>
        hasPermissions ? (role.permissions?.length || 0) > 0 : (role.permissions?.length || 0) === 0
      );
    }

    // Apply description filter
    if (this.advancedFilters.descriptionContains) {
      const searchText = this.advancedFilters.descriptionContains.toLowerCase();
      filteredRoles = filteredRoles.filter(role =>
        role.description?.toLowerCase().includes(searchText)
      );
    }

    return filteredRoles;
  }

  private applySorting(roles: Role[]): Role[] {
    const sortedRoles = [...roles];
    const isAsc = this.advancedFilters.sortOrder === 'asc';

    return sortedRoles.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (this.sortBy) {
        case 'name':
          aValue = a.name?.toLowerCase() || '';
          bValue = b.name?.toLowerCase() || '';
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        case 'users_count':
          aValue = a.users?.length || 0;
          bValue = b.users?.length || 0;
          break;
        case 'permissions_count':
          aValue = a.permissions?.length || 0;
          bValue = b.permissions?.length || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return isAsc ? -1 : 1;
      if (aValue > bValue) return isAsc ? 1 : -1;
      return 0;
    });
  }
}

