import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { MasterService } from '../../../../core/services/master.service';
import { ConnectWith } from '../../../../core/models/connect-with.model';

@Component({
  selector: 'app-connect-with',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    DatePipe
  ],
  templateUrl: './connect-with.component.html',
  styleUrls: ['./connect-with.component.scss']
})
export class ConnectWithComponent implements OnInit {
  // Data properties
  connectWithItems: ConnectWith[] = [];
  filteredConnectWithItems: ConnectWith[] = [];
  selectedConnectWith: ConnectWith | null = null;

  // Form properties
  connectWithForm: FormGroup;

  // UI state properties
  loading = false;
  submitting = false;
  isEditMode = false;
  searchTerm = '';

  // Pagination properties
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  totalPages = 0;

  // Modal error handling
  modalError = '';

  // Loading states for actions
  deleting = false;
  deletingItemId: number | null = null;
  restoring = false;
  restoringItemId: number | null = null;

  // Utility properties
  Math = Math;

  constructor(
    private fb: FormBuilder,
    private modalService: NgbModal,
    private masterService: MasterService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    console.log('🚀 ConnectWithComponent initialized');
    this.loadConnectWithItems();
  }

  private initializeForm(): void {
    // Initialize form with all required fields
    this.connectWithForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', [Validators.required]]
    });
  }

  loadConnectWithItems(): void {
    this.loading = true;
    console.log('🔄 Loading connect with items...', {
      currentPage: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      searchTerm: this.searchTerm,
      timestamp: new Date().toISOString()
    });

    // Convert page/size to skip/limit for new API format
    const skip = (this.currentPage - 1) * this.itemsPerPage;
    this.masterService.getConnectWithItems(skip, this.itemsPerPage, this.searchTerm).subscribe({
      next: (response) => {
        console.log('📥 Raw API response:', response);
        this.connectWithItems = response.items || [];
        this.filteredConnectWithItems = [...this.connectWithItems];
        this.totalItems = response.total || 0;
        this.totalPages = response.pages || 0;
        this.loading = false;
        console.log('✅ Connect with items loaded:', {
          itemsCount: this.connectWithItems.length,
          totalItems: this.totalItems,
          items: this.connectWithItems,
          timestamp: new Date().toISOString()
        });
      },
      error: (error) => {
        console.error('❌ Error loading connect with items:', error);
        this.connectWithItems = [];
        this.filteredConnectWithItems = [];
        this.loading = false;
      }
    });
  }

  // Search functionality
  onSearch(): void {
    console.log('🔍 Searching connect with items with term:', this.searchTerm);
    this.currentPage = 1; // Reset to first page when searching
    this.loadConnectWithItems();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.currentPage = 1;
    this.loadConnectWithItems();
  }

  // Pagination
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadConnectWithItems();
  }

  // Modal operations
  openCreateModal(content: any): void {
    this.isEditMode = false;
    this.selectedConnectWith = null;
    this.clearModalError();

    this.connectWithForm.reset({
      name: '',
      description: ''
    });

    this.modalService.open(content, { size: 'lg', backdrop: 'static' });
  }

  openEditModal(content: any, connectWith: ConnectWith): void {
    this.isEditMode = true;
    this.selectedConnectWith = connectWith;
    this.clearModalError();
    this.submitting = false; // Reset submitting state

    console.log('📝 Opening edit modal for connect with:', connectWith);

    // Reset form first
    this.connectWithForm.reset();

    // Load the full connect with data with fresh API call
    console.log('🔄 Loading fresh data for edit modal...');
    this.masterService.getConnectWithItem(connectWith.id!).subscribe({
      next: (fullConnectWith) => {
        console.log('📥 Received data for edit modal:', fullConnectWith);
        if (fullConnectWith) {
          // Update the selected item with fresh data
          this.selectedConnectWith = fullConnectWith;

          // Populate form with fresh data
          this.connectWithForm.patchValue({
            name: fullConnectWith.name,
            description: fullConnectWith.description
          });

          console.log('✅ Form populated with data:', {
            name: fullConnectWith.name,
            description: fullConnectWith.description,
            formValue: this.connectWithForm.value
          });
        } else {
          console.error('❌ No data received for connect with');
          this.showModalError('No data found for this connect with item');
        }
      },
      error: (error) => {
        console.error('❌ Error loading connect with for edit:', error);
        this.showModalError('Error loading connect with data for editing');
      }
    });

    this.modalService.open(content, { size: 'lg', backdrop: 'static' });
  }

  openViewModal(content: any, connectWith: ConnectWith): void {
    this.selectedConnectWith = connectWith;
    console.log('👁️ Opening view modal for connect with:', connectWith);

    // Load the full connect with data
    this.masterService.getConnectWithItem(connectWith.id!).subscribe({
      next: (fullConnectWith) => {
        if (fullConnectWith) {
          this.selectedConnectWith = fullConnectWith;
          console.log('✅ Connect with data loaded for viewing:', fullConnectWith);
        }
      },
      error: (error) => {
        console.error('❌ Error loading connect with for view:', error);
      }
    });

    this.modalService.open(content, { size: 'lg' });
  }

  // CRUD operations
  saveConnectWith(): void {
    if (this.connectWithForm.invalid) {
      // Mark all fields as touched to show validation errors
      this.connectWithForm.markAllAsTouched();
      return;
    }

    // Clear any previous modal errors
    this.clearModalError();
    this.submitting = true;

    const formValue = this.connectWithForm.value;
    console.log('💾 Saving connect with:', formValue);

    if (this.isEditMode && this.selectedConnectWith) {
      // Update existing connect with
      this.masterService.updateConnectWithItem(this.selectedConnectWith.id!, formValue).subscribe({
        next: (response) => {
          console.log('📝 Update response received:', response);
          if (response && response.success) {
            console.log('✅ Connect with updated successfully:', response);
            this.submitting = false;

            // Close modal first
            this.modalService.dismissAll();

            // Reset form and state
            this.isEditMode = false;
            this.selectedConnectWith = null;
            this.connectWithForm.reset();

            // Show success message
            this.showSuccessMessage('Connect with updated successfully!');

            // Force reload with a slight delay to ensure modal is fully closed
            setTimeout(() => {
              console.log('🔄 Forcing reload after update...');
              this.forceReloadData();
            }, 200);
          } else {
            console.error('❌ Update failed:', response);
            this.showModalError(response?.error || 'Failed to update connect with');
            this.submitting = false;
          }
        },
        error: (error) => {
          console.error('❌ Error updating connect with:', error);
          this.showModalError(error.error?.error || 'Error updating connect with');
          this.submitting = false;
        }
      });
    } else {
      // Create new connect with
      this.masterService.createConnectWithItem(formValue).subscribe({
        next: (response) => {
          if (response && response.success) {
            console.log('✅ Connect with created successfully:', response);
            this.submitting = false;
            this.modalService.dismissAll();
            this.showSuccessMessage('Connect with created successfully!');
            this.forceReloadData();
          } else {
            console.error('❌ Creation failed:', response);
            this.showModalError(response?.error || 'Failed to create connect with');
            this.submitting = false;
          }
        },
        error: (error) => {
          console.error('❌ Error creating connect with:', error);
          this.showModalError(error.error?.error || 'Error creating connect with');
          this.submitting = false;
        }
      });
    }
  }

  deleteConnectWith(connectWith: ConnectWith): void {
    if (confirm(`Are you sure you want to delete "${connectWith.name}"?`)) {
      console.log('🗑️ Deleting connect with:', connectWith);

      this.deleting = true;
      this.deletingItemId = connectWith.id!;

      this.masterService.deleteConnectWithItem(connectWith.id!).subscribe({
        next: (response) => {
          this.deleting = false;
          this.deletingItemId = null;

          if (response && response.success) {
            console.log('✅ Connect with deleted successfully:', response);
            this.showSuccessMessage('Connect with deleted successfully!');
            this.forceReloadData();
          } else {
            console.error('❌ Delete failed:', response);
            this.showErrorMessage(response?.error || 'Failed to delete connect with');
          }
        },
        error: (error) => {
          this.deleting = false;
          this.deletingItemId = null;
          console.error('❌ Error deleting connect with:', error);
          this.showErrorMessage(error.error?.error || 'Error deleting connect with');
        }
      });
    }
  }

  restoreConnectWith(connectWith: ConnectWith): void {
    if (confirm(`Are you sure you want to restore "${connectWith.name}"?`)) {
      console.log('♻️ Restoring connect with:', connectWith);

      this.restoring = true;
      this.restoringItemId = connectWith.id!;

      this.masterService.restoreConnectWithItem(connectWith.id!).subscribe({
        next: (response) => {
          this.restoring = false;
          this.restoringItemId = null;

          if (response && response.success) {
            console.log('✅ Connect with restored successfully:', response);
            this.showSuccessMessage('Connect with restored successfully!');
            this.forceReloadData();
          } else {
            console.error('❌ Restore failed:', response);
            this.showErrorMessage(response?.error || 'Failed to restore connect with');
          }
        },
        error: (error) => {
          this.restoring = false;
          this.restoringItemId = null;
          console.error('❌ Error restoring connect with:', error);
          this.showErrorMessage(error.error?.error || 'Error restoring connect with');
        }
      });
    }
  }

  /**
   * Force reload data after CRUD operations
   */
  forceReloadData(): void {
    console.log('🔄 Force reloading connect with data...');

    // Clear service cache first
    this.masterService.refreshConnectWithCache();

    // Reset pagination to first page
    this.currentPage = 1;

    // Reload data
    this.loadConnectWithItems();
  }

  refreshDropdownData() {
    // Refresh any dropdown data that might use connect with items
    // This can be extended if other components depend on connect with data
    console.log('🔄 Refreshing dropdown data...');

    // Clear the service cache to ensure fresh data
    this.masterService.refreshConnectWithCache();

    // Example: If you need to notify other components, you could use a subject
    // this.masterService.connectWithUpdated.next(true);
  }

  // Pagination methods
  getStartIndex(): number {
    return (this.currentPage - 1) * this.itemsPerPage;
  }

  getEndIndex(): number {
    const endIndex = this.currentPage * this.itemsPerPage;
    return Math.min(endIndex, this.filteredConnectWithItems.length);
  }

  // Utility methods
  isDeleted(connectWith: ConnectWith): boolean {
    return connectWith.deleted_at !== null && connectWith.deleted_at !== undefined;
  }

  // Message handling
  private showSuccessMessage(message: string): void {
    // You can implement toast notifications here
    console.log('✅ Success:', message);
  }

  private showErrorMessage(message: string): void {
    // You can implement toast notifications here
    console.error('❌ Error:', message);
  }

  private showModalError(message: string): void {
    this.modalError = message;
  }

  private clearModalError(): void {
    this.modalError = '';
  }

  /**
   * Handle modal close/cancel events
   */
  onModalClose(): void {
    console.log('🚪 Modal closing - cleaning up state...');
    this.isEditMode = false;
    this.selectedConnectWith = null;
    this.submitting = false;
    this.clearModalError();
    this.connectWithForm.reset({
      name: '',
      description: ''
    });
  }
}
