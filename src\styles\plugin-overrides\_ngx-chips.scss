// Plugin: Ngx-chips
// github: https://github.com/Gbuomprisco/ngx-chips

tag-input {
  $tag-height: 26px;
  .ng2-tag-input {
    @extend .border;
    padding: 5px 6px 0 !important;
    border-radius: $input-border-radius;
    min-height: 38px;
    tag {
      height: $tag-height;
      line-height: $tag-height;
      align-content: center;
      border-radius: 2px;
      margin: 0 .3rem .3131rem 0;
      background: $light;
      color: var(--#{$prefix}body-color);
      font-size: .78rem;
      &:not(.readonly):not(.tag--editing):active,
      &:not(.readonly):not(.tag--editing):focus,
      &:not(.readonly):not(:focus):not(.tag--editing):not(:active):hover {
        background: $primary;
        color: $white;
      }
      .tag-wrapper {
        delete-icon {
          svg {
            height: 14px;
            vertical-align: text-bottom;
          }
          &:hover {
            transform: scale(1.3) translateY(-1px);
          }
        }
      }
    }
    tag-input-form {
      margin-bottom: .3131rem;
      form {
        .ng2-tag-input__text-input {
          height: $tag-height;
          background: transparent;
          color: var(--#{$prefix}body-color);
          &::placeholder {
            color: $input-placeholder-color;
          }
        }
      }
    }
    &.bootstrap {
      .ng2-tags-container {
        tag {
          background: $primary;
          border-radius: 2px;
          &:hover {
          }
          &.tag--editing {
            background: var(--#{$prefix}body-bg);
            color: var(--#{$prefix}body-color);
            border: 1px solid var(--#{$prefix}border-color);
            padding-right: 18px;
          }
          &:not(.readonly):not(.tag--editing):active,
          &:not(.readonly):not(.tag--editing):focus,
          &:not(.readonly):not(:focus):not(.tag--editing):not(:active):hover {
            background: darken($primary, 6%);
            box-shadow: $card-box-shadow;
          }
          .tag-wrapper {
            delete-icon {
              svg {
                height: 14px;
                vertical-align: text-bottom;
              }
            }
          }
        }
      }
    }
  }
}