import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-upload me-2"></i>
        Bulk Upload Product Types
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Bulk Upload Component - Coming Soon</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This component will support bulk uploading of product types with:
        <ul class="mt-2 mb-0">
          <li>Excel template with validation rules</li>
          <li>Drag and drop file upload</li>
          <li>Validation and error reporting</li>
          <li>Preview before import</li>
          <li>Detailed import results</li>
        </ul>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Close</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('uploaded')">Upload</button>
    </div>
  `,
  styles: []
})
export class BulkUploadComponent {
  constructor(public activeModal: NgbActiveModal) {}
}
