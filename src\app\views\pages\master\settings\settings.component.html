<!-- Settings Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-settings me-2"></i>
              Settings Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_settings }} total settings, 
              {{ statistics.encrypted_settings_count }} encrypted
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-success" (click)="exportSettings()">
              <i class="feather icon-download me-1"></i>
              Export
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Setting
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Settings
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Settings
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCategory" (change)="onCategoryFilter()">
                <option value="">All Categories</option>
                <option *ngFor="let category of settingCategories" [value]="category.value">
                  {{ category.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedDataType" (change)="onDataTypeFilter()">
                <option value="">All Data Types</option>
                <option *ngFor="let dataType of dataTypes" [value]="dataType.value">
                  {{ dataType.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedEncrypted" (change)="onEncryptedFilter()">
                <option value="all">All Encryption</option>
                <option value="encrypted">Encrypted Only</option>
                <option value="not_encrypted">Not Encrypted</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedPublic" (change)="onPublicFilter()">
                <option value="all">All Visibility</option>
                <option value="public">Public Only</option>
                <option value="private">Private Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedReadonly" (change)="onReadonlyFilter()">
                <option value="all">All Access</option>
                <option value="readonly">Read-only</option>
                <option value="editable">Editable</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading settings...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40">
                    <input type="checkbox" class="form-check-input" 
                           [(ngModel)]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Setting Details</th>
                  <th>Category & Type</th>
                  <th>Value</th>
                  <th>Configuration</th>
                  <th>Security & Access</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let setting of getCurrentList(); trackBy: trackBySettingId">
                  <td>
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedSettings.has(setting.id)"
                           (change)="toggleSelection(setting.id)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ setting.key }}</strong>
                      <small class="d-block text-muted" *ngIf="setting.description">
                        {{ setting.description }}
                      </small>
                      <small class="d-block text-muted" *ngIf="setting.group_name">
                        Group: {{ setting.group_name }}
                      </small>
                      <small class="d-block text-muted">
                        Order: {{ setting.display_order }}
                      </small>
                      <div class="mt-1" *ngIf="setting.tags?.length">
                        <span class="badge bg-light text-dark me-1" *ngFor="let tag of setting.tags.slice(0, 2)">
                          {{ tag }}
                        </span>
                        <span class="badge bg-light text-dark" *ngIf="setting.tags.length > 2">
                          +{{ setting.tags.length - 2 }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <span [class]="getCategoryBadgeClass(setting.category)">
                        {{ getCategoryLabel(setting.category) }}
                      </span>
                      <div class="mt-1">
                        <span [class]="getDataTypeBadgeClass(setting.data_type)">
                          {{ getDataTypeLabel(setting.data_type) }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="setting-value">
                      <code class="setting-value-display">{{ formatValue(setting) }}</code>
                      <small class="d-block text-muted mt-1" *ngIf="setting.default_value !== undefined">
                        <strong>Default:</strong> {{ setting.default_value }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="configuration-info">
                      <div class="mb-1">
                        <span class="badge bg-warning me-1" *ngIf="setting.requires_restart">Requires Restart</span>
                        <span class="badge bg-info me-1" *ngIf="setting.environment_specific">Environment Specific</span>
                        <span class="badge bg-success me-1" *ngIf="setting.user_configurable">User Configurable</span>
                      </div>
                      <div *ngIf="setting.validation_rules?.length">
                        <small class="text-muted">
                          <strong>Validation:</strong> {{ setting.validation_rules.length }} rules
                        </small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="security-info">
                      <div class="mb-1">
                        <span class="badge bg-danger me-1" *ngIf="setting.is_encrypted">Encrypted</span>
                        <span class="badge bg-success me-1" *ngIf="setting.is_public">Public</span>
                        <span class="badge bg-secondary me-1" *ngIf="setting.is_readonly">Read-only</span>
                      </div>
                      <small class="d-block text-muted" *ngIf="setting.created_by">
                        <strong>Created by:</strong> {{ setting.created_by }}
                      </small>
                      <small class="d-block text-muted" *ngIf="setting.updated_by">
                        <strong>Updated by:</strong> {{ setting.updated_by }}
                      </small>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ setting.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(setting)" 
                                  [disabled]="setting.is_readonly">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active' && setting.default_value !== undefined">
                          <button class="dropdown-item" (click)="resetToDefault(setting)"
                                  [disabled]="setting.is_readonly">
                            <i class="feather icon-rotate-ccw me-2"></i>
                            Reset to Default
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteSetting(setting)"
                                  [disabled]="setting.is_readonly">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-settings text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Settings' : 'No Settings Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No settings have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No settings match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first setting.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Setting
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} settings
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_settings }}</h3>
                      <p class="mb-0">Total Settings</p>
                    </div>
                    <i class="feather icon-settings" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-danger text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.encrypted_settings_count }}</h3>
                      <p class="mb-0">Encrypted Settings</p>
                    </div>
                    <i class="feather icon-shield" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.public_settings_count }}</h3>
                      <p class="mb-0">Public Settings</p>
                    </div>
                    <i class="feather icon-eye" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.readonly_settings_count }}</h3>
                      <p class="mb-0">Read-only Settings</p>
                    </div>
                    <i class="feather icon-lock" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
