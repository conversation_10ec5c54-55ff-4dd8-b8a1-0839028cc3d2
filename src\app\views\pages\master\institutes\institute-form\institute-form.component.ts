import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import {
  InstituteService,
  Institute,
  InstituteCreate,
  InstituteUpdate
} from '../../../../../core/services/institute.service';
import { PopupDesignService } from '../../../../../core/services/popup-design.service';

@Component({
  selector: 'app-institute-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  templateUrl: './institute-form.component.html',
  styleUrls: ['./institute-form.component.scss']
})
export class InstituteFormComponent implements OnInit {
  @Input() isEditMode = false;
  @Input() institute: Institute | null = null;

  instituteForm!: FormGroup;
  saving = false;
  error: string | null = null;

  // Options
  instituteTypes: any[] = [];
  countries: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private fb: FormBuilder,
    public instituteService: InstituteService,
    private popupService: PopupDesignService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.loadOptions();
    this.initializeForm();
  }

  /**
   * Load dropdown options
   */
  loadOptions(): void {
    this.instituteTypes = this.instituteService.getInstituteTypes();
    this.countries = this.instituteService.getCountryList();
    this.regulatoryBodies = this.instituteService.getRegulatoryBodies();
  }

  /**
   * Initialize the form with validation
   */
  private initializeForm(): void {
    this.instituteForm = this.fb.group({
      name: [
        this.institute?.name || '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100)
        ]
      ],
      code: [
        this.institute?.code || '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(10),
          Validators.pattern(/^[A-Z0-9]+$/)
        ]
      ],
      type: [
        this.institute?.type || 'bank',
        [Validators.required]
      ],
      description: [
        this.institute?.description || '',
        [Validators.maxLength(500)]
      ],
      website: [
        this.institute?.website || '',
        [Validators.pattern(/^https?:\/\/.+/)]
      ],
      contact_email: [
        this.institute?.contact_email || '',
        [Validators.email]
      ],
      contact_phone: [
        this.institute?.contact_phone || '',
        [Validators.pattern(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/)]
      ],
      address: [
        this.institute?.address || '',
        [Validators.maxLength(200)]
      ],
      city: [
        this.institute?.city || '',
        [Validators.maxLength(50)]
      ],
      state: [
        this.institute?.state || '',
        [Validators.maxLength(50)]
      ],
      country: [
        this.institute?.country || '',
        []
      ],
      postal_code: [
        this.institute?.postal_code || '',
        [Validators.maxLength(20)]
      ],
      established_date: [
        this.institute?.established_date ? this.formatDateForInput(this.institute.established_date) : '',
        []
      ],
      license_number: [
        this.institute?.license_number || '',
        [Validators.maxLength(50)]
      ],
      regulatory_body: [
        this.institute?.regulatory_body || '',
        []
      ],
      ifsc_code: [
        this.institute?.ifsc_code || '',
        [Validators.pattern(/^[A-Z]{4}[0-9]{7}$/)]
      ],
      swift_code: [
        this.institute?.swift_code || '',
        [Validators.pattern(/^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/)]
      ],
      micr_code: [
        this.institute?.micr_code || '',
        [Validators.pattern(/^[0-9]{9}$/)]
      ],
      is_active: [
        this.institute?.is_active ?? true,
        [Validators.required]
      ]
    });

    // Add custom validators
    this.instituteForm.get('website')?.addValidators(this.websiteValidator.bind(this));
  }

  /**
   * Custom validator for website URL
   */
  private websiteValidator(control: any) {
    if (!control.value) return null;

    // Fallback validation
    try {
      new URL(control.value);
      return null;
    } catch {
      return { invalidWebsite: { message: 'Please enter a valid website URL' } };
    }
  }

  /**
   * Format date for input field
   */
  private formatDateForInput(dateString: string): string {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get form control for template access
   */
  getFormControl(controlName: string) {
    return this.instituteForm.get(controlName);
  }

  /**
   * Check if form control has error
   */
  hasError(controlName: string, errorType?: string): boolean {
    const control = this.getFormControl(controlName);
    if (!control) return false;

    if (errorType) {
      return control.hasError(errorType) && (control.dirty || control.touched);
    }

    return control.invalid && (control.dirty || control.touched);
  }

  /**
   * Get error message for form control
   */
  getErrorMessage(controlName: string): string {
    const control = this.getFormControl(controlName);
    if (!control || !control.errors) return '';

    const errors = control.errors;

    if (errors['required']) {
      return `${this.getFieldLabel(controlName)} is required.`;
    }

    if (errors['minlength']) {
      return `${this.getFieldLabel(controlName)} must be at least ${errors['minlength'].requiredLength} characters.`;
    }

    if (errors['maxlength']) {
      return `${this.getFieldLabel(controlName)} cannot exceed ${errors['maxlength'].requiredLength} characters.`;
    }

    if (errors['pattern']) {
      if (controlName === 'code') {
        return 'Code must contain only uppercase letters and numbers.';
      }
      if (controlName === 'website') {
        return 'Website must start with http:// or https://';
      }
      if (controlName === 'contact_phone') {
        return 'Please enter a valid phone number.';
      }
      if (controlName === 'ifsc_code') {
        return 'IFSC code must be 4 letters followed by 7 digits (e.g., SBIN0001234).';
      }
      if (controlName === 'swift_code') {
        return 'SWIFT code must be 8 or 11 characters (e.g., SBININBB).';
      }
      if (controlName === 'micr_code') {
        return 'MICR code must be 9 digits.';
      }
      return `${this.getFieldLabel(controlName)} format is invalid.`;
    }

    if (errors['email']) {
      return 'Please enter a valid email address.';
    }

    if (errors['invalidWebsite']) {
      return errors['invalidWebsite'].message;
    }

    return 'Invalid input.';
  }

  /**
   * Get field label for error messages
   */
  private getFieldLabel(controlName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Institute name',
      code: 'Institute code',
      type: 'Institute type',
      description: 'Description',
      website: 'Website',
      contact_email: 'Contact email',
      contact_phone: 'Contact phone',
      address: 'Address',
      city: 'City',
      state: 'State',
      country: 'Country',
      postal_code: 'Postal code',
      established_date: 'Established date',
      license_number: 'License number',
      regulatory_body: 'Regulatory body',
      ifsc_code: 'IFSC code',
      swift_code: 'SWIFT code',
      micr_code: 'MICR code',
      is_active: 'Status'
    };
    return labels[controlName] || controlName;
  }

  /**
   * Save institute
   */
  save(): void {
    if (this.instituteForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.saving = true;
    this.error = null;

    const formValue = this.instituteForm.value;

    // Clean up form data
    const instituteData = {
      name: formValue.name.trim(),
      code: formValue.code.trim().toUpperCase(),
      type: formValue.type,
      description: formValue.description?.trim() || undefined,
      website: formValue.website?.trim() || undefined,
      contact_email: formValue.contact_email?.trim() || undefined,
      contact_phone: formValue.contact_phone?.trim() || undefined,
      address: formValue.address?.trim() || undefined,
      city: formValue.city?.trim() || undefined,
      state: formValue.state?.trim() || undefined,
      country: formValue.country || undefined,
      postal_code: formValue.postal_code?.trim() || undefined,
      established_date: formValue.established_date || undefined,
      license_number: formValue.license_number?.trim() || undefined,
      regulatory_body: formValue.regulatory_body || undefined,
      ifsc_code: formValue.ifsc_code?.trim().toUpperCase() || undefined,
      swift_code: formValue.swift_code?.trim().toUpperCase() || undefined,
      micr_code: formValue.micr_code?.trim() || undefined,
      is_active: formValue.is_active
    };

    const operation = this.isEditMode
      ? this.instituteService.updateInstitute(this.institute!.id, instituteData as InstituteUpdate)
      : this.instituteService.createInstitute(instituteData as InstituteCreate);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.activeModal.close('saved');
        } else {
          this.error = response.error || 'Failed to save institute.';
          this.saving = false;
        }
      },
      error: (error) => {
        this.error = error.message;
        this.saving = false;

        this.popupService.showError({
          title: 'Save Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Mark all form controls as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.instituteForm.controls).forEach(key => {
      const control = this.instituteForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Reset form
   */
  reset(): void {
    this.instituteForm.reset();
    this.initializeForm();
    this.error = null;
  }

  /**
   * Cancel and close modal
   */
  cancel(): void {
    if (this.instituteForm.dirty) {
      this.popupService.showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        confirmText: 'Yes, Cancel',
        cancelText: 'Continue Editing'
      }).then((result) => {
        if (result.isConfirmed) {
          this.activeModal.dismiss('cancelled');
        }
      });
    } else {
      this.activeModal.dismiss('cancelled');
    }
  }

  /**
   * Get modal title
   */
  getModalTitle(): string {
    return this.isEditMode ? 'Edit Institute' : 'Create New Institute';
  }

  /**
   * Get save button text
   */
  getSaveButtonText(): string {
    if (this.saving) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update Institute' : 'Create Institute';
  }

  /**
   * Auto-generate code from name
   */
  generateCodeFromName(): void {
    const name = this.instituteForm.get('name')?.value;
    if (name && !this.instituteForm.get('code')?.value) {
      const code = name
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .substring(0, 6);

      this.instituteForm.get('code')?.setValue(code);
    }
  }

  /**
   * Validate and format website URL
   */
  formatWebsiteUrl(): void {
    const website = this.instituteForm.get('website')?.value;
    if (website && !website.startsWith('http')) {
      this.instituteForm.get('website')?.setValue(`https://${website}`);
    }
  }

  /**
   * Format banking codes to uppercase
   */
  formatBankingCode(controlName: string): void {
    const control = this.instituteForm.get(controlName);
    if (control?.value) {
      control.setValue(control.value.toUpperCase());
    }
  }

  /**
   * Get institute type description
   */
  getInstituteTypeDescription(type: string): string {
    const typeObj = this.instituteTypes.find(t => t.value === type);
    return typeObj ? typeObj.description : '';
  }
}
