import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-leave-policy',
  standalone: true,
  imports: [
    CommonModule,
    BreadcrumbComponent
  ],
  templateUrl: './leave-policy.component.html',
  styleUrl: './leave-policy.component.scss'
})
export class LeavePolicyComponent {
  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'Leave Policy' }
  ];
}
