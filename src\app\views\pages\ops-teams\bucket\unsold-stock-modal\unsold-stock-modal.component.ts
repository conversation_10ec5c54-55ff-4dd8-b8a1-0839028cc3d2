import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UnsoldStockRow } from '../ops-team.component';

@Component({
  selector: 'app-unsold-stock-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './unsold-stock-modal.component.html',
  styleUrls: ['./unsold-stock-modal.component.scss']
})
export class UnsoldStockModalComponent implements OnInit {
  @Input() stockId: number;
  @Input() stocks: UnsoldStockRow[] = [];

  formData: UnsoldStockRow = {
    id: 0,
    projectName: '',
    location: '',
    category: '',
    saleableArea: 0,
    rate: 0,
    value: 0,
    debt: 0,
    remarks: ''
  };

  constructor(public activeModal: NgbActiveModal) { }

  // Calculate value based on saleable area and rate
  calculateValue(): void {
    if (this.formData.saleableArea && this.formData.rate) {
      this.formData.value = this.formData.saleableArea * this.formData.rate;
    }
  }

  ngOnInit(): void {
    // If editing an existing record, populate the form
    if (this.stockId) {
      const stock = this.stocks.find(s => s.id === this.stockId);
      if (stock) {
        this.formData = { ...stock };
      }
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Calculate value one more time to ensure it's correct
    this.calculateValue();

    // Close the modal and pass the data back
    this.activeModal.close(this.formData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
