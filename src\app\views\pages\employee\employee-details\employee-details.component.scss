/* Employee details component styling */

.employee-header {
  padding: 1.5rem 0;
}

.avatar {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-weight: bold;
  color: #555;
}

.info-item {
  display: flex;
  margin-bottom: 1rem;
}

.info-label {
  font-weight: 500;
  width: 120px;
  color: #6c757d;
}

.info-value {
  flex: 1;
}

.placeholder-permissions {
  margin-top: 10px;
}

.card-title {
  display: flex;
  align-items: center;
}

.card {
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
