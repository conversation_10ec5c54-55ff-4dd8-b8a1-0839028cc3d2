<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h4 class="card-title mb-0">
                <i data-feather="users" class="icon-sm me-2" appFeatherIcon></i>
                User Management
              </h4>
              <p class="text-muted mb-0">Manage system users and their permissions</p>
            </div>
            <div class="d-flex gap-2">
              <button
                class="btn btn-outline-secondary"
                (click)="toggleViewMode()"
                ngbTooltip="Toggle View Mode">
                <i [attr.data-feather]="viewMode === 'table' ? 'grid' : 'list'" class="icon-sm me-1" appFeatherIcon></i>
                {{ viewMode === 'table' ? 'Card View' : 'Table View' }}
              </button>
              <button
                *ngIf="canPerformAction('edit')"
                class="btn btn-primary"
                (click)="createUser()">
                <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                Add New User
              </button>
            </div>
          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filters -->
          <div class="row mb-4">
            <div class="col-md-4">
              <div class="input-group">
                <span class="input-group-text">
                  <i data-feather="search" class="icon-sm" appFeatherIcon></i>
                </span>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Search users..."
                  [formControl]="searchTerm">
              </div>
            </div>
            <div class="col-md-3">
              <select class="form-select" [formControl]="statusFilter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="deleted">Deleted</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select" [formControl]="roleFilter">
                <option value="all">All Roles</option>
                <option value="superuser">Super Users</option>
                <option value="user">Regular Users</option>
              </select>
            </div>
            <div class="col-md-2">
              <select
                class="form-select"
                [value]="paginationConfig.pageSize"
                (change)="onPageSizeChange(+$any($event.target).value)">
                <option *ngFor="let size of paginationConfig.pageSizeOptions" [value]="size">
                  {{ size }} per page
                </option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading users...</p>
          </div>

          <!-- Empty State -->
          <div *ngIf="!loading && filteredUsers.length === 0" class="text-center py-5">
            <i data-feather="users" class="icon-xxl text-muted mb-3" appFeatherIcon></i>
            <h5 class="text-muted">No users found</h5>
            <p class="text-muted">
              {{ searchTerm.value ? 'Try adjusting your search criteria' : 'No users available' }}
            </p>
            <button
              *ngIf="canPerformAction('edit') && !searchTerm.value"
              class="btn btn-primary"
              (click)="createUser()">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add First User
            </button>
          </div>

          <!-- Table View -->
          <div *ngIf="!loading && filteredUsers.length > 0 && viewMode === 'table'" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th
                    *ngFor="let column of tableColumns"
                    [sortable]="column.sortable && column.key !== 'actions' ? column.key : ''"
                    (sort)="onSort($event)"
                    [style.width]="column.width"
                    scope="col">
                    <i
                      *ngIf="column.key === 'full_name'"
                      data-feather="user"
                      class="icon-sm me-1"
                      appFeatherIcon></i>
                    <i
                      *ngIf="column.key === 'email'"
                      data-feather="mail"
                      class="icon-sm me-1"
                      appFeatherIcon></i>
                    <i
                      *ngIf="column.key === 'is_active'"
                      data-feather="activity"
                      class="icon-sm me-1"
                      appFeatherIcon></i>
                    <i
                      *ngIf="column.key === 'is_superuser'"
                      data-feather="shield"
                      class="icon-sm me-1"
                      appFeatherIcon></i>
                    <i
                      *ngIf="column.key === 'created_at'"
                      data-feather="calendar"
                      class="icon-sm me-1"
                      appFeatherIcon></i>
                    <i
                      *ngIf="column.key === 'actions'"
                      data-feather="settings"
                      class="icon-sm me-1"
                      appFeatherIcon></i>
                    {{ column.label }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let user of paginatedUsers" [class.table-danger]="user.is_deleted">
                  <!-- Name -->
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-sm me-2">
                        <div class="avatar-initial bg-primary text-white rounded-circle">
                          {{ (user.first_name?.[0] || user.email[0]).toUpperCase() }}
                        </div>
                      </div>
                      <div>
                        <div class="fw-medium">{{ user.full_name || 'N/A' }}</div>
                        <small class="text-muted">ID: {{ user.id.substring(0, 8) }}...</small>
                      </div>
                    </div>
                  </td>

                  <!-- Email -->
                  <td>
                    <a [href]="'mailto:' + user.email" class="text-decoration-none">
                      {{ user.email }}
                    </a>
                  </td>

                  <!-- Status -->
                  <td>
                    <span
                      class="badge"
                      [class.bg-success]="user.is_active && !user.is_deleted"
                      [class.bg-warning]="!user.is_active && !user.is_deleted"
                      [class.bg-danger]="user.is_deleted">
                      <i
                        [attr.data-feather]="user.is_deleted ? 'trash-2' : (user.is_active ? 'check-circle' : 'x-circle')"
                        class="icon-xs me-1"
                        appFeatherIcon></i>
                      {{ user.is_deleted ? 'Deleted' : (user.is_active ? 'Active' : 'Inactive') }}
                    </span>
                  </td>

                  <!-- Role -->
                  <td>
                    <span
                      class="badge"
                      [class.bg-danger]="user.is_superuser"
                      [class.bg-secondary]="!user.is_superuser">
                      <i
                        [attr.data-feather]="user.is_superuser ? 'shield' : 'user'"
                        class="icon-xs me-1"
                        appFeatherIcon></i>
                      {{ user.is_superuser ? 'Super User' : 'User' }}
                    </span>
                  </td>

                  <!-- Created Date -->
                  <td>
                    <span ngbTooltip="{{ user.created_at | date:'medium' }}">
                      {{ user.created_at | date:'shortDate' }}
                    </span>
                  </td>

                  <!-- Actions -->
                  <td>
                    <div class="btn-group" role="group">
                      <button
                        *ngIf="canPerformAction('view')"
                        class="btn btn-sm btn-outline-info"
                        (click)="onUserAction({action: 'view', user: user})"
                        ngbTooltip="View Details">
                        <i data-feather="eye" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <button
                        *ngIf="canPerformAction('edit')"
                        class="btn btn-sm btn-outline-primary"
                        (click)="onUserAction({action: 'edit', user: user})"
                        ngbTooltip="Edit User">
                        <i data-feather="edit" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <button
                        *ngIf="canPerformAction('delete') && !user.is_deleted"
                        class="btn btn-sm btn-outline-danger"
                        (click)="onUserAction({action: 'delete', user: user})"
                        [disabled]="deleting && deletingUserId === user.id"
                        ngbTooltip="Delete User">
                        <i
                          *ngIf="!(deleting && deletingUserId === user.id)"
                          data-feather="trash-2"
                          class="icon-xs"
                          appFeatherIcon></i>
                        <div
                          *ngIf="deleting && deletingUserId === user.id"
                          class="spinner-border spinner-border-sm"
                          role="status">
                        </div>
                      </button>
                      <button
                        *ngIf="canPerformAction('edit') && user.is_deleted"
                        class="btn btn-sm btn-outline-success"
                        (click)="onUserAction({action: 'restore', user: user})"
                        [disabled]="restoring && restoringUserId === user.id"
                        ngbTooltip="Restore User">
                        <i
                          *ngIf="!(restoring && restoringUserId === user.id)"
                          data-feather="refresh-cw"
                          class="icon-xs"
                          appFeatherIcon></i>
                        <div
                          *ngIf="restoring && restoringUserId === user.id"
                          class="spinner-border spinner-border-sm"
                          role="status">
                        </div>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Card View -->
          <div *ngIf="!loading && filteredUsers.length > 0 && viewMode === 'cards'" class="row">
            <div class="col-xl-4 col-lg-6 col-md-6 mb-4" *ngFor="let user of paginatedUsers">
              <div class="card h-100" [class.border-danger]="user.is_deleted">
                <div class="card-body">
                  <div class="d-flex align-items-center mb-3">
                    <div class="avatar avatar-md me-3">
                      <div class="avatar-initial bg-primary text-white rounded-circle">
                        {{ (user.first_name?.[0] || user.email[0]).toUpperCase() }}
                      </div>
                    </div>
                    <div class="flex-grow-1">
                      <h6 class="card-title mb-1">{{ user.full_name || 'N/A' }}</h6>
                      <p class="text-muted mb-0">{{ user.email }}</p>
                    </div>
                    <div class="dropdown">
                      <button
                        class="btn btn-sm btn-outline-secondary"
                        type="button"
                        data-bs-toggle="dropdown">
                        <i data-feather="more-vertical" class="icon-xs" appFeatherIcon></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="canPerformAction('view')">
                          <a class="dropdown-item" (click)="onUserAction({action: 'view', user: user})">
                            <i data-feather="eye" class="icon-xs me-2" appFeatherIcon></i>
                            View Details
                          </a>
                        </li>
                        <li *ngIf="canPerformAction('edit')">
                          <a class="dropdown-item" (click)="onUserAction({action: 'edit', user: user})">
                            <i data-feather="edit" class="icon-xs me-2" appFeatherIcon></i>
                            Edit User
                          </a>
                        </li>
                        <li *ngIf="canPerformAction('delete') && !user.is_deleted">
                          <a class="dropdown-item text-danger" (click)="onUserAction({action: 'delete', user: user})">
                            <i data-feather="trash-2" class="icon-xs me-2" appFeatherIcon></i>
                            Delete User
                          </a>
                        </li>
                        <li *ngIf="canPerformAction('edit') && user.is_deleted">
                          <a class="dropdown-item text-success" (click)="onUserAction({action: 'restore', user: user})">
                            <i data-feather="refresh-cw" class="icon-xs me-2" appFeatherIcon></i>
                            Restore User
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="row text-center">
                    <div class="col-6">
                      <span
                        class="badge w-100"
                        [class.bg-success]="user.is_active && !user.is_deleted"
                        [class.bg-warning]="!user.is_active && !user.is_deleted"
                        [class.bg-danger]="user.is_deleted">
                        {{ user.is_deleted ? 'Deleted' : (user.is_active ? 'Active' : 'Inactive') }}
                      </span>
                    </div>
                    <div class="col-6">
                      <span
                        class="badge w-100"
                        [class.bg-danger]="user.is_superuser"
                        [class.bg-secondary]="!user.is_superuser">
                        {{ user.is_superuser ? 'Super User' : 'User' }}
                      </span>
                    </div>
                  </div>

                  <div class="mt-3">
                    <small class="text-muted">
                      <i data-feather="calendar" class="icon-xs me-1" appFeatherIcon></i>
                      Created: {{ user.created_at | date:'shortDate' }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="!loading && filteredUsers.length > 0" class="d-flex justify-content-between align-items-center mt-4">
            <div class="text-muted">
              Showing {{ Math.min((paginationConfig.page - 1) * paginationConfig.pageSize + 1, paginationConfig.totalItems) }}
              to {{ Math.min(paginationConfig.page * paginationConfig.pageSize, paginationConfig.totalItems) }}
              of {{ paginationConfig.totalItems }} users
            </div>
            <ngb-pagination
              [(page)]="paginationConfig.page"
              [pageSize]="paginationConfig.pageSize"
              [collectionSize]="paginationConfig.totalItems"
              [maxSize]="5"
              [rotate]="true"
              [boundaryLinks]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
