import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { PermissionService } from '../services/permission.service';
import { PermissionAuditService } from '../services/permission-audit.service';

interface EndpointPermissionConfig {
  endpoint: string;
  method: string;
  requiredPermissions: string[];
  requireAll?: boolean;
  description?: string;
}

@Injectable()
export class PermissionValidationInterceptor implements HttpInterceptor {

  // Define permission requirements for API endpoints
  private endpointPermissions: EndpointPermissionConfig[] = [
    // User management endpoints
    {
      endpoint: '/api/v1/users',
      method: 'GET',
      requiredPermissions: ['users:read'],
      description: 'View users'
    },
    {
      endpoint: '/api/v1/users',
      method: 'POST',
      requiredPermissions: ['users:create'],
      description: 'Create user'
    },
    {
      endpoint: '/api/v1/users/{id}',
      method: 'PUT',
      requiredPermissions: ['users:update'],
      description: 'Update user'
    },
    {
      endpoint: '/api/v1/users/{id}',
      method: 'DELETE',
      requiredPermissions: ['users:delete'],
      description: 'Delete user'
    },

    // Role management endpoints
    {
      endpoint: '/api/v1/roles',
      method: 'GET',
      requiredPermissions: ['roles:read'],
      description: 'View roles'
    },
    {
      endpoint: '/api/v1/roles',
      method: 'POST',
      requiredPermissions: ['roles:create'],
      description: 'Create role'
    },
    {
      endpoint: '/api/v1/roles/{id}',
      method: 'PUT',
      requiredPermissions: ['roles:update'],
      description: 'Update role'
    },
    {
      endpoint: '/api/v1/roles/{id}',
      method: 'DELETE',
      requiredPermissions: ['roles:delete'],
      description: 'Delete role'
    },

    // Permission management endpoints
    {
      endpoint: '/api/v1/roles/permissions',
      method: 'GET',
      requiredPermissions: ['permissions:read'],
      description: 'View permissions'
    },
    {
      endpoint: '/api/v1/roles/permissions',
      method: 'POST',
      requiredPermissions: ['permissions:create'],
      description: 'Create permission'
    },
    {
      endpoint: '/api/v1/roles/permissions/{id}',
      method: 'PUT',
      requiredPermissions: ['permissions:update'],
      description: 'Update permission'
    },
    {
      endpoint: '/api/v1/roles/permissions/{id}',
      method: 'DELETE',
      requiredPermissions: ['permissions:delete'],
      description: 'Delete permission'
    },

    // Role-Permission assignment endpoints
    {
      endpoint: '/api/v1/roles/{id}/permissions',
      method: 'GET',
      requiredPermissions: ['roles:read', 'admin:access'],
      description: 'Get role permissions'
    },
    {
      endpoint: '/api/v1/roles/{id}/permissions',
      method: 'POST',
      requiredPermissions: ['roles:manage_all', 'admin:access'],
      description: 'Assign permissions to role'
    },
    {
      endpoint: '/api/v1/roles/{id}/permissions',
      method: 'PUT',
      requiredPermissions: ['roles:manage', 'admin:access'],
      description: 'Replace role permissions'
    },
    {
      endpoint: '/api/v1/roles/{id}/permissions',
      method: 'PATCH',
      requiredPermissions: ['roles:manage', 'admin:access'],
      description: 'Bulk update role permissions'
    },

    // Sales endpoints
    {
      endpoint: '/api/v1/sales',
      method: 'GET',
      requiredPermissions: ['sales:read', 'sales:manage'],
      description: 'View sales data'
    },
    {
      endpoint: '/api/v1/sales',
      method: 'POST',
      requiredPermissions: ['sales:create', 'sales:manage'],
      description: 'Create sales record'
    },
    {
      endpoint: '/api/v1/sales/{id}',
      method: 'PUT',
      requiredPermissions: ['sales:update', 'sales:manage'],
      description: 'Update sales record'
    },
    {
      endpoint: '/api/v1/sales/{id}',
      method: 'DELETE',
      requiredPermissions: ['sales:delete', 'sales:manage'],
      description: 'Delete sales record'
    },

    // Employee endpoints
    {
      endpoint: '/api/v1/employees',
      method: 'GET',
      requiredPermissions: ['employees:read'],
      description: 'View employees'
    },
    {
      endpoint: '/api/v1/employees',
      method: 'POST',
      requiredPermissions: ['employees:create'],
      description: 'Create employee'
    },

    // Master data endpoints
    {
      endpoint: '/api/v1/master',
      method: 'GET',
      requiredPermissions: ['master:read'],
      description: 'View master data'
    },
    {
      endpoint: '/api/v1/master',
      method: 'POST',
      requiredPermissions: ['master:create'],
      description: 'Create master data'
    },
    {
      endpoint: '/api/v1/master/{id}',
      method: 'PUT',
      requiredPermissions: ['master:update'],
      description: 'Update master data'
    },
    {
      endpoint: '/api/v1/master/{id}',
      method: 'DELETE',
      requiredPermissions: ['master:delete'],
      description: 'Delete master data'
    },

    // Leave management endpoints
    {
      endpoint: '/api/v1/leave',
      method: 'POST',
      requiredPermissions: ['leave:create'],
      description: 'Apply for leave'
    },
    {
      endpoint: '/api/v1/leave/approve',
      method: 'POST',
      requiredPermissions: ['leave:approve'],
      description: 'Approve leave'
    },

    // Admin endpoints
    {
      endpoint: '/api/v1/admin',
      method: 'GET',
      requiredPermissions: ['admin:access'],
      description: 'Admin access'
    },

    // Audit endpoints
    {
      endpoint: '/api/v1/audit',
      method: 'GET',
      requiredPermissions: ['audit:read', 'admin:access'],
      description: 'View audit logs'
    }
  ];

  constructor(
    private permissionService: PermissionService,
    private auditService: PermissionAuditService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Skip validation for certain endpoints
    if (this.shouldSkipValidation(req)) {
      return next.handle(req);
    }

    // Find matching endpoint configuration
    const endpointConfig = this.findEndpointConfig(req);

    if (endpointConfig) {
      // Validate permissions
      const hasPermission = endpointConfig.requireAll
        ? this.permissionService.hasAllPermissions(endpointConfig.requiredPermissions)
        : this.permissionService.hasAnyPermission(endpointConfig.requiredPermissions);

      // Log the API access attempt
      this.auditService.logApiAccess(
        req.url,
        req.method,
        endpointConfig.requiredPermissions,
        hasPermission
      );

      if (!hasPermission) {
        // Log security event for unauthorized access attempt
        this.auditService.logSecurityEvent(
          'UNAUTHORIZED_API_ACCESS',
          `Attempted to access ${req.method} ${req.url} without required permissions`,
          'MEDIUM',
          {
            endpoint: req.url,
            method: req.method,
            requiredPermissions: endpointConfig.requiredPermissions,
            userPermissions: this.permissionService.getUserPermissions()
          }
        );

        // Return 403 Forbidden error
        const error = new HttpErrorResponse({
          error: {
            message: 'Insufficient permissions for this operation',
            requiredPermissions: endpointConfig.requiredPermissions,
            code: 'PERMISSION_DENIED'
          },
          status: 403,
          statusText: 'Forbidden',
          url: req.url
        });

        return throwError(() => error);
      }
    }

    // Continue with the request
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        // Log API errors for audit purposes
        if (endpointConfig) {
          this.auditService.logApiAccess(
            req.url,
            req.method,
            endpointConfig.requiredPermissions,
            false,
            error.status,
            error.message
          );
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Check if validation should be skipped for this request
   */
  private shouldSkipValidation(req: HttpRequest<any>): boolean {
    const skipPatterns = [
      '/api/v1/auth/',
      '/api/v1/health',
      '/api/v1/users/me',
      '/api/v1/i18n/',
      '/metrics'
    ];

    return skipPatterns.some(pattern => req.url.includes(pattern));
  }

  /**
   * Find endpoint configuration for the request
   */
  private findEndpointConfig(req: HttpRequest<any>): EndpointPermissionConfig | null {
    const url = req.url;
    const method = req.method;

    // Find exact match first
    let config = this.endpointPermissions.find(config =>
      config.method === method && this.matchesEndpoint(url, config.endpoint)
    );

    if (!config) {
      // Try to find a pattern match (for endpoints with path parameters)
      config = this.endpointPermissions.find(config =>
        config.method === method && this.matchesEndpointPattern(url, config.endpoint)
      );
    }

    return config || null;
  }

  /**
   * Check if URL matches endpoint exactly
   */
  private matchesEndpoint(url: string, endpoint: string): boolean {
    return url.includes(endpoint);
  }

  /**
   * Check if URL matches endpoint pattern (with path parameters)
   */
  private matchesEndpointPattern(url: string, endpointPattern: string): boolean {
    // Convert pattern like '/api/v1/users/{id}' to regex
    const regexPattern = endpointPattern
      .replace(/\{[^}]+\}/g, '[^/]+') // Replace {id} with [^/]+
      .replace(/\//g, '\\/'); // Escape forward slashes

    const regex = new RegExp(regexPattern + '(?:/.*)?$');
    return regex.test(url);
  }

  /**
   * Add or update endpoint permission configuration
   */
  addEndpointConfig(config: EndpointPermissionConfig): void {
    const existingIndex = this.endpointPermissions.findIndex(
      existing => existing.endpoint === config.endpoint && existing.method === config.method
    );

    if (existingIndex >= 0) {
      this.endpointPermissions[existingIndex] = config;
    } else {
      this.endpointPermissions.push(config);
    }
  }

  /**
   * Get all endpoint configurations
   */
  getEndpointConfigs(): EndpointPermissionConfig[] {
    return [...this.endpointPermissions];
  }
}
