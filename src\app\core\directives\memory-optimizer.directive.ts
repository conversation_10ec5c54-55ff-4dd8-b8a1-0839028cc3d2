import { 
  Directive, 
  ElementRef, 
  OnInit, 
  OnDestroy, 
  Input,
  inject
} from '@angular/core';
import { MemoryManagerService } from '../services/memory-manager.service';

export interface MemoryOptimizationConfig {
  enableImageLazyLoading?: boolean;
  enableDOMCleaning?: boolean;
  enableEventDelegation?: boolean;
  cleanupInterval?: number;
  memoryThreshold?: number;
}

/**
 * Memory Optimizer Directive
 * 
 * Applies memory optimizations to any element or component.
 * Provides automatic cleanup, lazy loading, and memory monitoring.
 * 
 * Usage:
 * <div appMemoryOptimizer [config]="optimizationConfig">
 *   <!-- Content -->
 * </div>
 */
@Directive({
  selector: '[appMemoryOptimizer]',
  standalone: true
})
export class MemoryOptimizerDirective implements OnInit, OnDestroy {
  @Input('appMemoryOptimizer') config: MemoryOptimizationConfig = {};

  private elementRef = inject(ElementRef);
  private memoryManager = inject(MemoryManagerService);

  private cleanupInterval?: number;
  private mutationObserver?: MutationObserver;
  private intersectionObserver?: IntersectionObserver;
  private resizeObserver?: ResizeObserver;
  
  private defaultConfig: MemoryOptimizationConfig = {
    enableImageLazyLoading: true,
    enableDOMCleaning: true,
    enableEventDelegation: true,
    cleanupInterval: 30000, // 30 seconds
    memoryThreshold: 50 * 1024 * 1024 // 50MB
  };

  ngOnInit(): void {
    this.config = { ...this.defaultConfig, ...this.config };
    console.log('🧠 MemoryOptimizerDirective: Initializing memory optimizations');
    
    this.initializeOptimizations();
  }

  ngOnDestroy(): void {
    this.cleanup();
    console.log('🧠 MemoryOptimizerDirective: Memory optimizations cleaned up');
  }

  /**
   * Initialize all memory optimizations
   */
  private initializeOptimizations(): void {
    if (this.config.enableImageLazyLoading) {
      this.setupImageLazyLoading();
    }

    if (this.config.enableDOMCleaning) {
      this.setupDOMCleaning();
    }

    if (this.config.enableEventDelegation) {
      this.setupEventDelegation();
    }

    this.setupMemoryMonitoring();
  }

  /**
   * Setup image lazy loading
   */
  private setupImageLazyLoading(): void {
    const images = this.elementRef.nativeElement.querySelectorAll('img[data-src]');
    
    if (images.length === 0) return;

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const dataSrc = img.getAttribute('data-src');
          
          if (dataSrc) {
            img.src = dataSrc;
            img.removeAttribute('data-src');
            this.intersectionObserver?.unobserve(img);
          }
        }
      });
    }, {
      rootMargin: '50px'
    });

    images.forEach(img => {
      this.intersectionObserver?.observe(img);
    });

    this.memoryManager.registerObserver('MemoryOptimizerDirective', this.intersectionObserver);
    console.log(`🖼️ MemoryOptimizerDirective: Lazy loading enabled for ${images.length} images`);
  }

  /**
   * Setup DOM cleaning
   */
  private setupDOMCleaning(): void {
    if (this.config.cleanupInterval) {
      this.cleanupInterval = window.setInterval(() => {
        this.performDOMCleanup();
      }, this.config.cleanupInterval);

      this.memoryManager.registerTimer('MemoryOptimizerDirective', this.cleanupInterval);
    }

    // Setup mutation observer to detect DOM changes
    this.mutationObserver = new MutationObserver((mutations) => {
      let shouldCleanup = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          // Check for removed nodes that might need cleanup
          mutation.removedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.cleanupRemovedElement(node as Element);
              shouldCleanup = true;
            }
          });
        }
      });

      if (shouldCleanup) {
        // Debounce cleanup
        this.debouncedCleanup();
      }
    });

    this.mutationObserver.observe(this.elementRef.nativeElement, {
      childList: true,
      subtree: true
    });

    this.memoryManager.registerObserver('MemoryOptimizerDirective', this.mutationObserver);
    console.log('🧹 MemoryOptimizerDirective: DOM cleaning enabled');
  }

  /**
   * Setup event delegation
   */
  private setupEventDelegation(): void {
    const element = this.elementRef.nativeElement;
    
    // Common events that benefit from delegation
    const delegatedEvents = ['click', 'change', 'input', 'focus', 'blur'];
    
    delegatedEvents.forEach(eventType => {
      element.addEventListener(eventType, this.handleDelegatedEvent.bind(this), true);
    });

    console.log('🎯 MemoryOptimizerDirective: Event delegation enabled');
  }

  /**
   * Setup memory monitoring
   */
  private setupMemoryMonitoring(): void {
    this.resizeObserver = new ResizeObserver((entries) => {
      // Monitor element size changes that might indicate memory issues
      entries.forEach(entry => {
        const { width, height } = entry.contentRect;
        const area = width * height;
        
        // Large elements might indicate memory issues
        if (area > 1000000) { // 1M pixels
          console.warn(`⚠️ Large element detected: ${width}x${height} pixels`);
        }
      });
    });

    this.resizeObserver.observe(this.elementRef.nativeElement);
    this.memoryManager.registerObserver('MemoryOptimizerDirective', this.resizeObserver);
  }

  /**
   * Handle delegated events
   */
  private handleDelegatedEvent(event: Event): void {
    const target = event.target as Element;
    
    // Optimize event handling based on target
    if (target.tagName === 'IMG' && event.type === 'load') {
      // Image loaded, can remove data-src attribute
      target.removeAttribute('data-src');
    }
    
    // Add more event optimizations as needed
  }

  /**
   * Perform DOM cleanup
   */
  private performDOMCleanup(): void {
    const element = this.elementRef.nativeElement;
    
    // Remove empty text nodes
    this.removeEmptyTextNodes(element);
    
    // Clean up unused attributes
    this.cleanupUnusedAttributes(element);
    
    // Optimize images
    this.optimizeImages(element);
    
    console.log('🧹 MemoryOptimizerDirective: DOM cleanup performed');
  }

  /**
   * Remove empty text nodes
   */
  private removeEmptyTextNodes(element: Element): void {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          return node.textContent?.trim() === '' ? 
            NodeFilter.FILTER_ACCEPT : 
            NodeFilter.FILTER_REJECT;
        }
      }
    );

    const emptyNodes: Node[] = [];
    let node;
    
    while (node = walker.nextNode()) {
      emptyNodes.push(node);
    }

    emptyNodes.forEach(node => {
      node.parentNode?.removeChild(node);
    });
  }

  /**
   * Clean up unused attributes
   */
  private cleanupUnusedAttributes(element: Element): void {
    const unusedAttributes = ['data-temp', 'data-loading', 'data-processed'];
    
    element.querySelectorAll('*').forEach(el => {
      unusedAttributes.forEach(attr => {
        if (el.hasAttribute(attr)) {
          el.removeAttribute(attr);
        }
      });
    });
  }

  /**
   * Optimize images
   */
  private optimizeImages(element: Element): void {
    const images = element.querySelectorAll('img');
    
    images.forEach(img => {
      // Remove loaded images that are no longer visible
      if (!this.isElementVisible(img) && img.complete) {
        // Store src in data attribute and clear src to free memory
        if (img.src && !img.hasAttribute('data-original-src')) {
          img.setAttribute('data-original-src', img.src);
          img.src = '';
        }
      }
    });
  }

  /**
   * Check if element is visible
   */
  private isElementVisible(element: Element): boolean {
    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0 && 
           rect.top < window.innerHeight && rect.bottom > 0;
  }

  /**
   * Clean up removed element
   */
  private cleanupRemovedElement(element: Element): void {
    // Remove any event listeners
    const events = ['click', 'change', 'input', 'focus', 'blur', 'load', 'error'];
    events.forEach(eventType => {
      element.removeEventListener(eventType, this.handleDelegatedEvent);
    });

    // Clear any data attributes
    Array.from(element.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        element.removeAttribute(attr.name);
      }
    });
  }

  /**
   * Debounced cleanup
   */
  private cleanupTimeout?: number;
  private debouncedCleanup(): void {
    if (this.cleanupTimeout) {
      clearTimeout(this.cleanupTimeout);
    }
    
    this.cleanupTimeout = window.setTimeout(() => {
      this.performDOMCleanup();
    }, 1000);
  }

  /**
   * Cleanup all resources
   */
  private cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.memoryManager.unregisterTimer('MemoryOptimizerDirective', this.cleanupInterval);
    }

    if (this.cleanupTimeout) {
      clearTimeout(this.cleanupTimeout);
    }

    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.memoryManager.unregisterObserver('MemoryOptimizerDirective', this.mutationObserver);
    }

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
      this.memoryManager.unregisterObserver('MemoryOptimizerDirective', this.intersectionObserver);
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.memoryManager.unregisterObserver('MemoryOptimizerDirective', this.resizeObserver);
    }
  }
}
