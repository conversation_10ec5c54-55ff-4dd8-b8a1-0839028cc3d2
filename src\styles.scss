/* You can add global styles to this file, and also import other style files */


// Global icon font
@import "../public/fonts/feather/style.css";


// Critical 3rd party plugins (loaded immediately)
@import "metismenujs/scss/metismenujs"; // Required for navigation

// Non-critical plugins are now loaded dynamically via DynamicCSSLoaderService
// This reduces initial bundle size and improves performance
// Modules loaded dynamically:
// - highlight.js (loaded when code blocks are present)
// - ngx-owl-carousel-o (loaded when carousel components are used)
// - animate.css (loaded when animation classes are detected)
// - ng-select (loaded when ng-select components are used)
// - dropzone (loaded when dropzone components are used)
// - quill (loaded when quill editor is used)
// - angular-archwizard (loaded when wizard components are used)
// - ngx-datatable (loaded when datatable components are used)


// NobleUI styles for LTR layout
@import "./styles/styles.scss";

// NobleUI styles for RTL layout. Replace the above style with this CSS file to enable RTL styles
// @import "./styles/rtl-css/styles.rtl.css";

// Custom global styles
textarea {
  resize: none !important;
}

// Set all table cell padding to 10px
.table > :not(caption) > * > * {
  padding: 5px !important;
}

// Global Session Timer Theme Styles
.session-timer {
  // Light theme (default)
  color: #000000 !important;

  // Dark theme
  [data-bs-theme='dark'] & {
    color: #ffffff !important;
  }
}

// Additional dark theme support
[data-bs-theme='dark'] {
  .session-timer {
    color: #ffffff !important;

    &.normal,
    &.warning,
    &.expired {
      color: #ffffff !important;
    }
  }
}

