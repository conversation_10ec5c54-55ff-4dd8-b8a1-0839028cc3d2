import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-project-land-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './project-land-modal.component.html',
  styleUrl: './project-land-modal.component.scss'
})
export class ProjectLandModalComponent implements OnInit {
  @Input() landId?: number;

  // Form data
  formData = {
    id: 0,
    agreementType: '',
    documentDate: '',
    documentNo: '',
    surveyNo: '',
    areaSqMtrs: 0,
    areaGunthaAcre: 0,
    areaUnit: '',
    partiesNames: '',
    considerationDetails: ''
  };

  // Agreement type options
  agreementTypeOptions = [
    { value: 'Sale Deed', label: 'Sale Deed' },
    { value: 'Joint Development Agreement', label: 'Joint Development Agreement' },
    { value: 'Development Agreement', label: 'Development Agreement' },
    { value: 'Joint Venture Agreement', label: 'Joint Venture Agreement' },
    { value: 'Power of Attorney', label: 'Power of Attorney' },
    { value: 'Lease Deed', label: 'Lease Deed' }
  ];

  // Area unit options
  areaUnitOptions = [
    { value: 'Guntha', label: 'Guntha' },
    { value: 'Acre', label: 'Acre' }
  ];

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      agreementType: '',
      documentDate: '',
      documentNo: '',
      surveyNo: '',
      areaSqMtrs: 0,
      areaGunthaAcre: 0,
      areaUnit: '',
      partiesNames: '',
      considerationDetails: ''
    };

    // If editing an existing record, populate the form
    if (this.landId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll use mock data based on the ID
      if (this.landId === 1) {
        this.formData = {
          id: 1,
          agreementType: 'Sale Deed',
          documentDate: '2022-05-15',
          documentNo: 'SD/2022/1234',
          surveyNo: '123/4A',
          areaSqMtrs: 25000,
          areaGunthaAcre: 6.18,
          areaUnit: 'Acre',
          partiesNames: 'ABC Developers Pvt Ltd and XYZ Landowners',
          considerationDetails: 'Rs. 15 Crores for 6.18 Acres of land'
        };
      } else if (this.landId === 2) {
        this.formData = {
          id: 2,
          agreementType: 'Joint Development Agreement',
          documentDate: '2021-08-22',
          documentNo: 'JDA/2021/5678',
          surveyNo: '456/7B',
          areaSqMtrs: 15000,
          areaGunthaAcre: 3.71,
          areaUnit: 'Acre',
          partiesNames: 'ABC Commercial Properties Pvt Ltd and PQR Landowners',
          considerationDetails: '60% revenue share to landowner, 40% to developer'
        };
      }
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Close the modal and pass the data back
    this.activeModal.close(this.formData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
