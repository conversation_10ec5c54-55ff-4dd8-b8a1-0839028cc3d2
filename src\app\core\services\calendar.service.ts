import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Holiday interfaces
export interface Holiday {
  id?: string;
  date: string;
  name: string;
  description?: string;
  type?: 'public' | 'company' | 'religious' | 'national';
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface HolidayResponse {
  success: boolean;
  data: Holiday[];
  message?: string;
}

// Calendar API interfaces
export interface CalendarEvent {
  id: string | null;
  date: string;
  event_type: 'holiday' | 'weekend' | 'attendance' | 'leave' | 'absent' | 'present';
  status: string;
  title: string;
  description: string;
  // Attendance-related fields
  check_in_time?: string | null;
  check_out_time?: string | null;
  working_hours?: number | null;
  overtime_hours?: number | null;
  // Leave-related fields
  leave_type?: string | null;
  leave_days?: number | null;
  leave_reason?: string | null;
  // Holiday-related fields
  is_optional?: boolean | null;
  region_code?: string | null;
  // Display properties
  color: string;
  text_color: string;
}

export interface CalendarDay {
  date: string;
  day_of_week: number;
  is_weekend: boolean;
  events: CalendarEvent[];
  primary_status: 'present' | 'absent' | 'holiday' | 'weekend' | 'leave';
  has_attendance: boolean;
  has_leave: boolean;
  has_holiday: boolean;
}

export interface CalendarData {
  year: number;
  month: number;
  month_name: string;
  days: CalendarDay[];
}

export interface MyCalendarResponse {
  success: boolean;
  data: {
    employee_id: string;
    employee_code: string;
    employee_name: string;
    calendar_data: CalendarData;
  };
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CalendarService {
  private baseUrl = `${environment.apiUrl}/api/v1/calendar`;
  private holidaysCache: Holiday[] = [];
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor(private http: HttpClient) {}

  /**
   * Get holidays from the API with caching
   */
  getHolidays(): Observable<Holiday[]> {
    console.log('🗓️ CalendarService: Getting holidays...');

    // Check if cache is still valid
    if (this.holidaysCache.length > 0 && Date.now() < this.cacheExpiry) {
      console.log('✅ CalendarService: Using cached holidays');
      return of(this.holidaysCache);
    }

    console.log('🔄 CalendarService: Fetching holidays from API...');
    return this.http.get<any>(`${this.baseUrl}/test/holidays`).pipe(
      tap(response => console.log('📅 Holiday API response:', response)),
      map(response => {
        // Handle different response structures
        let holidays: Holiday[] = [];

        // Handle the new API structure with new_year_activities
        if (response && response.success && response.data && response.data.new_year_activities) {
          holidays = response.data.new_year_activities;
          console.log('✅ CalendarService: Using new_year_activities structure');
        } else if (response && response.success && Array.isArray(response.data)) {
          holidays = response.data;
          console.log('✅ CalendarService: Using direct data array structure');
        } else if (Array.isArray(response)) {
          holidays = response as Holiday[];
          console.log('✅ CalendarService: Using direct array response');
        } else {
          console.warn('⚠️ Unexpected holiday API response structure:', response);
          holidays = [];
        }

        // Cache the holidays
        this.holidaysCache = holidays;
        this.cacheExpiry = Date.now() + this.CACHE_DURATION;

        console.log(`✅ CalendarService: Cached ${holidays.length} holidays`);
        return holidays;
      }),
      catchError(error => {
        console.error('❌ CalendarService: Error fetching holidays:', error);
        // Return empty array on error but don't cache it
        return of([]);
      })
    );
  }

  /**
   * Check if a specific date is a holiday
   */
  isHoliday(date: Date | string): Observable<boolean> {
    const dateStr = typeof date === 'string' ? date : this.formatDate(date);
    
    return this.getHolidays().pipe(
      map(holidays => {
        const isHoliday = holidays.some(holiday => 
          this.formatDate(new Date(holiday.date)) === dateStr
        );
        
        if (isHoliday) {
          console.log(`🎉 Date ${dateStr} is a holiday`);
        }
        
        return isHoliday;
      })
    );
  }

  /**
   * Get fresh holidays from API without caching (for real-time validation)
   */
  getFreshHolidays(): Observable<Holiday[]> {
    console.log('🔄 CalendarService: Fetching fresh holidays from API (no cache)...');
    return this.http.get<any>(`${this.baseUrl}/test/holidays`).pipe(
      tap(response => console.log('📅 Fresh Holiday API response:', response)),
      map(response => {
        // Handle different response structures
        let holidays: Holiday[] = [];

        // Handle the new API structure with new_year_activities
        if (response && response.success && response.data && response.data.new_year_activities) {
          holidays = response.data.new_year_activities;
          console.log('✅ CalendarService: Using new_year_activities structure (fresh)');
        } else if (response && response.success && Array.isArray(response.data)) {
          holidays = response.data;
          console.log('✅ CalendarService: Using direct data array structure (fresh)');
        } else if (Array.isArray(response)) {
          holidays = response as Holiday[];
          console.log('✅ CalendarService: Using direct array response (fresh)');
        } else {
          console.warn('⚠️ Unexpected holiday API response structure (fresh):', response);
          holidays = [];
        }

        console.log(`✅ CalendarService: Retrieved ${holidays.length} fresh holidays`);
        return holidays;
      }),
      catchError(error => {
        console.error('❌ CalendarService: Error fetching fresh holidays:', error);
        // Return empty array on error
        return of([]);
      })
    );
  }

  /**
   * Get holiday information for a specific date
   */
  getHolidayInfo(date: Date | string): Observable<Holiday | null> {
    const dateStr = typeof date === 'string' ? date : this.formatDate(date);

    return this.getHolidays().pipe(
      map(holidays => {
        const holiday = holidays.find(holiday =>
          this.formatDate(new Date(holiday.date)) === dateStr
        );

        return holiday || null;
      })
    );
  }

  /**
   * Check if a date is weekend or holiday
   */
  isWeekendOrHoliday(date: Date | string): Observable<boolean> {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check weekend first (faster)
    const isWeekend = this.isWeekend(dateObj);
    if (isWeekend) {
      return of(true);
    }
    
    // Then check holiday
    return this.isHoliday(dateObj);
  }

  /**
   * Check if a date is weekend
   */
  isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  /**
   * Get working days between two dates (excluding weekends and holidays)
   */
  getWorkingDaysBetween(startDate: string | Date, endDate: string | Date): Observable<number> {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    
    return this.getHolidays().pipe(
      map(holidays => {
        let workingDays = 0;
        const holidayDates = new Set(
          holidays.map(h => this.formatDate(new Date(h.date)))
        );
        
        for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
          const dateStr = this.formatDate(date);
          
          // Skip weekends
          if (this.isWeekend(date)) {
            continue;
          }
          
          // Skip holidays
          if (holidayDates.has(dateStr)) {
            continue;
          }
          
          workingDays++;
        }
        
        console.log(`📊 Working days between ${this.formatDate(start)} and ${this.formatDate(end)}: ${workingDays}`);
        return workingDays;
      })
    );
  }

  /**
   * Get holidays in a date range
   */
  getHolidaysInRange(startDate: string | Date, endDate: string | Date): Observable<Holiday[]> {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    
    return this.getHolidays().pipe(
      map(holidays => {
        return holidays.filter(holiday => {
          const holidayDate = new Date(holiday.date);
          return holidayDate >= start && holidayDate <= end;
        });
      })
    );
  }

  /**
   * Clear holidays cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    console.log('🗑️ CalendarService: Clearing holidays cache');
    this.holidaysCache = [];
    this.cacheExpiry = 0;
  }

  /**
   * Format date to YYYY-MM-DD string
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Test the holiday API endpoint
   */
  testHolidayApi(): Observable<boolean> {
    console.log('🧪 CalendarService: Testing holiday API endpoint...');

    return this.http.get(`${this.baseUrl}/test/holidays`).pipe(
      map(() => {
        console.log('✅ CalendarService: Holiday API is available');
        return true;
      }),
      catchError(error => {
        console.error('❌ CalendarService: Holiday API test failed:', error);
        return of(false);
      })
    );
  }

  /**
   * Get my calendar data from the API
   */
  getMyCalendar(year?: number, month?: number): Observable<MyCalendarResponse> {
    console.log('📅 CalendarService: Getting my calendar data...');

    let url = `${this.baseUrl}/my-calendar`;

    // Add query parameters if provided
    const params: string[] = [];
    if (year) params.push(`year=${year}`);
    if (month) params.push(`month=${month}`);

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    console.log('🔗 CalendarService: API URL:', url);

    return this.http.get<MyCalendarResponse>(url).pipe(
      tap(response => {
        console.log('📅 My Calendar API response:', response);
        if (response.success && response.data) {
          console.log(`✅ CalendarService: Loaded calendar for ${response.data.employee_name} (${response.data.employee_code})`);
          console.log(`📊 Calendar data: ${response.data.calendar_data.month_name} ${response.data.calendar_data.year} - ${response.data.calendar_data.days.length} days`);
        }
      }),
      catchError(error => {
        console.error('❌ CalendarService: Error fetching my calendar:', error);
        throw error;
      })
    );
  }

  /**
   * Convert calendar events to FullCalendar format
   */
  convertToFullCalendarEvents(calendarData: CalendarData): any[] {
    console.log('🔄 CalendarService: Converting calendar data to FullCalendar events...');
    console.log(`📊 CalendarService: Processing ${calendarData.days.length} days for ${calendarData.month_name} ${calendarData.year}`);

    const events: any[] = [];
    let eventTypeCount: { [key: string]: number } = {};
    let statusCount: { [key: string]: number } = {};

    calendarData.days.forEach((day, dayIndex) => {
      // Count primary status for verification
      statusCount[day.primary_status] = (statusCount[day.primary_status] || 0) + 1;

      // Log day-level information for verification
      if (dayIndex < 5) { // Log first 5 days for debugging
        console.log(`📅 Day ${day.date}: ${day.day_of_week} (${day.is_weekend ? 'Weekend' : 'Weekday'}), Status: ${day.primary_status}, Events: ${day.events.length}`);
      }

      // Process explicit events first
      day.events.forEach((event, eventIndex) => {
        // Count event types for verification
        eventTypeCount[event.event_type] = (eventTypeCount[event.event_type] || 0) + 1;

        // Validate and enhance event properties
        const validatedEvent = this.validateAndEnhanceEvent(event, day);

        const fullCalendarEvent = {
          id: event.id || `${event.event_type}-${day.date}-${eventIndex}`,
          title: validatedEvent.title,
          start: day.date,
          allDay: true,
          backgroundColor: validatedEvent.backgroundColor,
          borderColor: validatedEvent.borderColor,
          textColor: validatedEvent.textColor,
          extendedProps: {
            // Event-specific properties
            description: event.description,
            event_type: event.event_type,
            status: event.status,
            // Attendance properties
            check_in_time: event.check_in_time,
            check_out_time: event.check_out_time,
            working_hours: event.working_hours,
            overtime_hours: event.overtime_hours,
            // Leave properties
            leave_type: event.leave_type,
            leave_days: event.leave_days,
            leave_reason: event.leave_reason,
            // Holiday properties
            is_optional: event.is_optional,
            region_code: event.region_code,
            // Day-level properties
            date: day.date,
            day_of_week: day.day_of_week,
            is_weekend: day.is_weekend,
            primary_status: day.primary_status,
            has_attendance: day.has_attendance,
            has_leave: day.has_leave,
            has_holiday: day.has_holiday
          }
        };

        events.push(fullCalendarEvent);

        // Log detailed event information for first few events
        if (events.length <= 3) {
          console.log(`📋 Event ${events.length}:`, {
            title: event.title,
            type: event.event_type,
            status: event.status,
            color: event.color,
            hasAttendance: day.has_attendance,
            hasLeave: day.has_leave,
            hasHoliday: day.has_holiday
          });
        }
      });

      // Create implicit event based on primary_status if no explicit events
      if (day.events.length === 0 && day.primary_status) {
        const implicitEvent = this.createImplicitEventFromPrimaryStatus(day);
        if (implicitEvent) {
          events.push(implicitEvent);
          eventTypeCount[implicitEvent.extendedProps.event_type] = (eventTypeCount[implicitEvent.extendedProps.event_type] || 0) + 1;

          // Log implicit event creation
          if (dayIndex < 5) {
            console.log(`📋 Created implicit event for ${day.date}: ${implicitEvent.title} (${day.primary_status})`);
          }
        }
      }
    });

    console.log(`✅ CalendarService: Converted ${events.length} events for FullCalendar`);
    console.log(`📊 Event type distribution:`, eventTypeCount);
    console.log(`📊 Primary status distribution:`, statusCount);

    return events;
  }

  /**
   * Validate and enhance event properties for consistent display
   */
  private validateAndEnhanceEvent(event: CalendarEvent, day: CalendarDay): any {
    let backgroundColor = event.color;
    let borderColor = event.color;
    let textColor = event.text_color;
    let title = event.title;

    // Ensure proper color handling for different event types
    switch (event.event_type) {
      case 'holiday':
        // Holidays should have distinct colors
        backgroundColor = backgroundColor || '#ff6b6b';
        borderColor = borderColor || '#ff5252';
        textColor = textColor || '#ffffff';
        break;
      case 'leave':
        // Leave events should have leave-specific colors
        backgroundColor = backgroundColor || '#ffa726';
        borderColor = borderColor || '#ff9800';
        textColor = textColor || '#ffffff';
        break;
      case 'attendance':
        // Attendance events should have attendance-specific colors
        backgroundColor = backgroundColor || '#66bb6a';
        borderColor = borderColor || '#4caf50';
        textColor = textColor || '#ffffff';
        break;
      case 'weekend':
        // Weekend events should have weekend-specific colors
        backgroundColor = backgroundColor || '#9e9e9e';
        borderColor = borderColor || '#757575';
        textColor = textColor || '#ffffff';
        break;
      case 'absent':
        // Absent events should have distinct colors
        backgroundColor = backgroundColor || '#ef5350';
        borderColor = borderColor || '#f44336';
        textColor = textColor || '#ffffff';
        break;
      case 'present':
        // Present events should have positive colors
        backgroundColor = backgroundColor || '#26a69a';
        borderColor = borderColor || '#009688';
        textColor = textColor || '#ffffff';
        break;
      default:
        // Default colors for unknown event types
        backgroundColor = backgroundColor || '#42a5f5';
        borderColor = borderColor || '#2196f3';
        textColor = textColor || '#ffffff';
    }

    // Enhance title with status information if needed
    if (!title || title.trim() === '') {
      title = this.generateDefaultTitle(event, day);
    }

    return {
      title,
      backgroundColor,
      borderColor,
      textColor
    };
  }

  /**
   * Generate default title for events without titles
   */
  private generateDefaultTitle(event: CalendarEvent, day: CalendarDay): string {
    switch (event.event_type) {
      case 'holiday':
        return event.description || 'Holiday';
      case 'leave':
        return `${event.leave_type || 'Leave'} - ${event.status}`;
      case 'attendance':
        return `Attendance - ${event.status}`;
      case 'weekend':
        return 'Weekend';
      case 'absent':
        return 'Absent';
      case 'present':
        return 'Present';
      default:
        return event.status || 'Event';
    }
  }

  /**
   * Create implicit event from primary_status when no explicit events exist
   */
  private createImplicitEventFromPrimaryStatus(day: CalendarDay): any | null {
    if (!day.primary_status) {
      return null;
    }

    // Map primary_status to event properties
    let eventType: string;
    let title: string;
    let backgroundColor: string;
    let textColor: string;
    let status: string;

    switch (day.primary_status) {
      case 'weekend':
        eventType = 'weekend';
        title = 'Weekend';
        backgroundColor = '#9e9e9e';
        textColor = '#ffffff';
        status = 'weekend';
        break;
      case 'holiday':
        eventType = 'holiday';
        title = 'Holiday';
        backgroundColor = '#ff6b6b';
        textColor = '#ffffff';
        status = 'holiday';
        break;
      case 'present':
        eventType = 'attendance';
        title = 'Present';
        backgroundColor = '#4caf50';
        textColor = '#ffffff';
        status = 'present';
        break;
      case 'absent':
        eventType = 'absent';
        title = 'Absent';
        backgroundColor = '#ef5350';
        textColor = '#ffffff';
        status = 'absent';
        break;
      case 'leave':
        eventType = 'leave';
        title = 'Leave';
        backgroundColor = '#ffa726';
        textColor = '#ffffff';
        status = 'leave';
        break;
      default:
        console.warn(`⚠️ Unknown primary_status: ${day.primary_status}`);
        return null;
    }

    return {
      id: `implicit-${eventType}-${day.date}`,
      title: title,
      start: day.date,
      allDay: true,
      backgroundColor: backgroundColor,
      borderColor: backgroundColor,
      textColor: textColor,
      extendedProps: {
        description: `${title} (from primary_status)`,
        event_type: eventType,
        status: status,
        date: day.date,
        day_of_week: day.day_of_week,
        is_weekend: day.is_weekend,
        primary_status: day.primary_status,
        has_attendance: day.has_attendance,
        has_leave: day.has_leave,
        has_holiday: day.has_holiday,
        is_implicit: true // Flag to identify implicit events
      }
    };
  }
}
