// Write your custom css/scss here
.bg-danger{
  background-color: #d90606 !important;
}
// Hide number input spinners (up/down arrows) globally
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox and other browsers */
input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

// Common styles for Ops Team, Login at Institute, and Disbursement components

// Card and alert styles
.section-title {
  color: #3F828B;
  font-weight: 600;
  font-size: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 15px;
}

.card-title {
  display: flex;
  align-items: center;
  color: #3F828B; // Updated color as requested
  font-weight: 700 !important;
  font-size: 18px !important;
}

.card-subtitle {
  color: #DF5517; // Updated color to match card-title
  font-weight: 500;
  font-size: 16px;
}

.card-description {
  color: #6c757d;
  margin-bottom: 1.5rem;
}

// action icon css 
.action-icons {
  white-space: nowrap;

  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    background: transparent;
    padding: 0;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin-right: 8px;
    cursor: pointer;

    i {
      color: var(--bs-primary);
      pointer-events: none; // Prevent icon from blocking click
    }
  }
}



// Custom Tab Styles
.nav-tabs {
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1.5rem;

  .nav-item {
    margin-bottom: -1px;
  }

  .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: #495057;
    font-weight: 400;
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;

    .icon-sm {
      width: 18px;
      height: 18px;
      stroke-width: 2px;
      margin-right: 0.5rem;
    }

    &:hover {
      border-color: #e9ecef #e9ecef #dee2e6;
      color: #3F828B;
    }

    &.active {
      background-color: #3F828B !important;
      border-color: #dee2e6 #dee2e6 #fff;
      color: #ffffff !important;
      font-weight: 700;
    }
  }
}

// Table styles
.table {
  margin-bottom: 0;

  th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
  }

  td {
    vertical-align: middle;
  }

  .sr-no-column {
    width: 80px;
    text-align: left;
  }

  .checkbox-column {
    width: 80px;
    text-align: left;
  }

  .action-column {
    width: 120px;
    text-align: left;
  }
}

// Modern table card styling
.modern-table-card {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;

  .card-body {
    padding: 15px;
  }
}

// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  thead {
    background-color: rgba(var(--bs-primary-rgb), 0.05);

    th {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 12px 10px;
      border-top: none;
      border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.02);
      }

      td {
        vertical-align: middle;
        padding: 12px 10px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
      }
    }
  }
}

// Modern ngx-datatable styling
.ngx-datatable.bootstrap {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  border: none;

  .datatable-header {
    background-color: rgba(var(--bs-primary-rgb), 0.05);

    .datatable-header-cell {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      color: #495057;
      text-align: left !important;
      padding: 12px 10px;
      border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.1);
      // white-space: nowrap;
    }
  }

  // Special styling for debt details table
  &.debt-details-table {
    .datatable-header-cell {
      text-align: left;
    }

    .datatable-body-cell {
      text-align: left;
    }
  }

  .datatable-body {
    .datatable-body-row {
      transition: all 0.2s;

      &:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
      }

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.02);
      }

      .datatable-body-cell {
        padding: 12px 10px;
        text-align: left;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;

      }
    }
  }
}

// Sales Plan table value styling
.sales-plan-value {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  font-weight: 500;
  color: #333;
  min-width: 80px;
  white-space: nowrap;
}


// Total column styling
.total-value {
  font-weight: 700;
  background-color: #f0f8ff;
  border-color: #b8d8f8;
  min-width: 120px;
  color: #366F76;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem;
  border: 1px solid #b8d8f8;
}

// Form styles
.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.form-control, .form-select {
  &:focus {
    border-color: #3F828B;
    box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
  }
}

textarea {
  resize: vertical;
}

.text-muted {
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

// background color 
.bg-primary{
  background-color: #3F828B !important;
}
.bg-orange{
  background-color: #DF5517 !important;
}
.bg-secondary{
  background-color: #6c757d !important;
}
// Button styles
.btn-primary {
  background-color: #3F828B;
  border-color: #3F828B;

  &:hover, &:focus, &:active {
    background-color: darken(#3F828B, 10%);
    border-color: darken(#3F828B, 10%);
  }
}
.btn-orange {
  background-color: #DF5517;
  border-color: #DF5517;

  &:hover, &:focus, &:active {
    background-color: darken(#DF5517, 10%);
    border-color: darken(#DF5517, 10%);
  }
}
.btn-outline-orange{
  // background-color: #DF5517;
  border-color: #DF5517 !important;
  color: #DF5517 !important;

  &:hover, &:focus, &:active {
    background-color:#DF5517 !important;
    border-color: #DF5517 !important;
    color: #fff !important; // 👈 Haa line add kar
  }

}
// Nested Tabs Styles (for CAM Note and Agency Finalization)
.nested-tabs {
  display: flex;
  flex-wrap: nowrap; // Force single line
  overflow-x: auto; // Allow horizontal scrolling
  white-space: nowrap; // Prevent text wrapping
  border: none;
  margin-bottom: 1.5rem;
  padding-bottom: 5px; // Space for scrollbar
  gap: 0; // Remove gap to add dividers

  .nav-item {
    margin-bottom: 0;
    position: relative; // For divider positioning

    // Add vertical divider between tabs
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 25%;
      height: 50%;
      width: 1px;
      background-color: #dee2e6; // Grey divider
    }
  }

  .nav-link {
    border-radius: 0; // Remove rounded corners
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    color: #495057;
    background-color: transparent; // Remove background
    border: none; // Remove border
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    position: relative; // For underline positioning

    .icon-sm {
      width: 18px;
      height: 18px;
      stroke-width: 2px;
      margin-right: 0.5rem;
    }

    &:hover {
      color: #3F828B;
    }

    // Add underline only to active tab
    &.active {
      background-color: transparent; // Remove background
      color: #3F828B !important; // Use theme color
      font-weight: 600;

      // Add underline
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 10%;
        width: 80%;
        height: 2px;
        background-color: #3F828B; // Theme color underline
      }
    }
  }
}

// Hide spinners on number input fields globally
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

// Global Modal Styles
.modal {
  // Modal header styling
  .modal-header {
    background-color: #3F828B;
    color: white;

    .modal-title {
      font-weight: 600;
      font-size: 16px !important;
    }

    .btn-close {
      filter: brightness(0) invert(1);
    }
  }

  // Modal body styling
  .modal-body {
    padding: 1.5rem;

    // Form styling within modals
    .form-label {
      font-weight: 500;
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
      color: #495057;
    }

    .text-danger {
      color: #dc3545 !important;
    }

    .form-control, .form-select {
      border-color: #ced4da;

      &:focus {
        border-color: #df5316;
        // box-shadow: 0 0 0 0.25rem rgba(223, 83, 22, 0.25);
      }
    }

    textarea {
      resize: none;
    }

    // Read-only field styling
    input[readonly] {
      background-color: #f8f9fa;
    }
  }

  // Modal footer styling
  .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem;

    .btn-primary {
      background-color: #3F828B;
      border-color: #3F828B;

      &:hover, &:focus, &:active {
        background-color: darken(#3F828B, 10%);
        border-color: darken(#3F828B, 10%);
      }

      &:disabled {
        background-color: lighten(#3F828B, 20%);
        border-color: lighten(#3F828B, 20%);
      }
    }

    .btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;

      &:hover, &:focus, &:active {
        background-color: darken(#6c757d, 10%);
        border-color: darken(#6c757d, 10%);
      }
    }
  }

  // Common section title styling
  .section-title {
    color: #3F828B;
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
  }
}

// CAM Summary Accordion Styling
.cam-summary-accordion {
  .accordion-item {
    border-radius: 0.375rem;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.125);
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    .accordion-header {
      margin-bottom: 0;

      .accordion-button {
        background-color: #f8f9fa;
        color: #3F828B;
        font-weight: 600;
        padding: 1rem 1.25rem;
        border-left: none; // Remove left border completely

        &:not(.collapsed) {
          box-shadow: none;
        }

        &:focus {
          box-shadow: none;
          border-color: rgba(0, 0, 0, 0.125);
        }

        &::after {
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%233F828B'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
        }
      }
    }

    .accordion-body {
      padding: 1.25rem;
      background-color: #fff;

      h6 {
        color: #3F828B;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      ul li strong {
        color: #495057;
        font-weight: 600;
        margin-right: 0.5rem;
      }
    }
  }
}

// Custom Styles for Document Status
.document-section {
  margin-bottom: 2rem;

  .section-header {
    background-color: #f8f9fa;
    border-left: 4px solid #3F828B;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;

    h6 {
      color: #3F828B;
      font-weight: 600;
      margin-bottom: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 0.5rem;
        color: #3F828B;
      }
    }
  }

  .empty-section-message {
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 1rem;

    p {
      color: #6c757d;
      font-style: italic;
    }
  }

  .ngx-datatable {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
    overflow: hidden;

    .datatable-header {
      background-color: rgba(63, 130, 139, 0.05);

      .datatable-header-cell {
        font-weight: 600;
        color: #495057;
      }
    }

    .datatable-body-row {
      border-bottom: 1px solid #f0f0f0;

      &:hover {
        background-color: rgba(63, 130, 139, 0.02);
      }

      .datatable-body-cell {
        display: flex;
        align-items: center;
      }

      // Style for MahaRERA Certificate rows
      .maharera-cert {
        font-style: italic;
        color: #3F828B;
        font-weight: 500;
      }

      // Make the main MahaRERA Certificate header bold
      &[style*="transform"] .datatable-body-cell:has(.maharera-cert:contains("MahaRERA Certificates:")) {
        .maharera-cert {
          font-weight: 600;
          text-decoration: underline;
        }
      }

      // Style for sub-items
      .sub-item {
        font-size: 0.9rem;
        padding-left: 0.5rem;
        font-style: italic;
      }

      // Add indentation for sub-items
      &[style*="transform"] .datatable-body-cell:nth-child(2) {
        // Exclude MahaRERA entries from indentation
        &:has(.maharera-cert) {
          padding-left: 1.5rem;
        }

        // Add indentation for Company Documents sub-items (1.1, 2.1)
        &:has(+ .datatable-body-cell:has(.sub-item)) {
          padding-left: 1.5rem;
        }

        // Special handling for MahaRERA entries (11, 11.a, 11.b, 11.c, 11.d)
        // Remove indentation for these specific entries
        &:has(span:not(.maharera-cert)) {
          &[id*="11"] {
            padding-left: 0 !important;
            font-style: normal !important;
            font-weight: normal !important;
          }
        }
      }

      // Specific override for MahaRERA entries
      .datatable-body-cell:nth-child(2):has([id="11"]),
      .datatable-body-cell:nth-child(2):has([id="11.a"]),
      .datatable-body-cell:nth-child(2):has([id="11.b"]),
      .datatable-body-cell:nth-child(2):has([id="11.c"]),
      .datatable-body-cell:nth-child(2):has([id="11.d"]) {
        padding-left: 0 !important;
      }
    }

    .form-check-input:checked {
      background-color: #3F828B;
      border-color: #3F828B;
    }

    .status-select {
      border-color: #ced4da;

      &:focus {
        border-color: #3F828B;
      }
    }
  }
}

// Custom Styles for One Pager
.one-pager-content {
  .card-subtitle {
    color: #3F828B;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(63, 130, 139, 0.2);
    margin-bottom: 1.5rem;
  }

  .form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
  }

  .form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;

    &:focus {
      border-color: #3F828B;
    }

    &[readonly] {
      background-color: #f8f9fa;
      cursor: not-allowed;
    }
  }

  textarea.form-control {
    resize: vertical;
  }

  .col-md-4 {
    margin-bottom: 1rem;
  }
}

// Custom Styles for CAM Summary Sections
.summary-sections {
  margin-top: 1.5rem;

  .card {
    border: 1px solid rgba(63, 130, 139, 0.2);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    margin-bottom: 0.75rem;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    padding: 1rem 1.25rem;
    background-color: rgba(63, 130, 139, 0.05);
    border-bottom: 1px solid rgba(63, 130, 139, 0.1);

    h5 {
      font-weight: 600;
      font-size: 1rem;

      i {
        color: #3F828B;
      }

      .text-primary {
        color: #3F828B !important;
      }
    }
  }

  .card-body {
    padding: 15px;
    background-color: #ffffff;

    ul {
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Responsive Tab Styles
@media (max-width: 767.98px) {
  .nav-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    white-space: nowrap;

    .nav-item {
      flex-shrink: 0;
    }

    .nav-link {
      padding: 0.5rem 0.75rem;
      font-size: 0.85rem;
    }
  }

  // Responsive styles for nested tabs
  .nested-tabs {
    // Already set to nowrap in the main styles

    .nav-item {
      flex-shrink: 0;
    }

    .nav-link {
      padding: 0.5rem 1rem;
      font-size: 0.85rem;
    }
  }

  // Responsive styles for CAM Summary Sections
  .summary-sections {
    .card-header {
      padding: 0.75rem 1rem;

      h5 {
        font-size: 0.9rem;
      }
    }

    .card-body {
      padding: 15px;
    }
  }

  // Responsive styles for Document Status
  .document-section {
    .section-header {
      padding: 0.5rem 0.75rem;

      h6 {
        font-size: 0.95rem;
      }
    }

    .ngx-datatable {
      .datatable-header-cell {
        font-size: 0.9rem;
      }

      .datatable-body-cell {
        font-size: 0.85rem;
      }
    }
  }

  // Responsive styles for One Pager
  .one-pager-content {
    .card-subtitle {
      font-size: 0.95rem;
    }

    .form-label {
      font-size: 0.85rem;
    }

    .form-control, .form-select {
      padding: 0.25rem 0.5rem;
      font-size: 0.9rem;
    }

    .col-md-4 {
      width: 100%;
    }
  }

  .table-responsive-wrapper {
    overflow-x: auto;
  }
}
// Badge styling
.badge {
  padding: 0.4em 0.8em;
  font-weight: 500;
  font-size: 0.75rem;

  &.rounded-pill {
    padding-left: 0.8em;
    padding-right: 0.8em;
  }

  &.bg-light-primary {
    background-color: rgba(63, 130, 139, 0.1);
    font-family: monospace;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}
  
// Pagination styling for document tables
ngb-pagination {
    .page-item {
      &.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
        color: white;
      }

      .page-link {
        color: var(--bs-primary);
        font-size: 0.85rem;
        padding: 10px 15px;

        

      }
    }
}

// Empty state styling
.empty-state {
  padding: 2rem;
  text-align: center;
 color: var(--bs-gray-600);
  i {
    color: #adb5bd;
    margin-bottom: 1rem;
  }

  p {
    font-weight: 500;
    color: #495057;
  }
  
}

// Custom scrollbar styling
:host ::ng-deep {
  // Global scrollbar styling for the entire component
  ::-webkit-scrollbar {
    height: 4px; // Reduced height for horizontal scrollbar
    width: 6px; // Slightly wider for vertical scrollbar for better usability
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
  }

  // Firefox scrollbar styling
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
  }
}

// inner card css 

.inner-card{
  --bs-card-border-color:none !important;
  --bs-card-box-shadow:none !important;
}
// lms css 
.lms-card-icon {
  width: 80px;
  height: 80px;
}
