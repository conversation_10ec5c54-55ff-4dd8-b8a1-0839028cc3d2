import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';

interface CarInsuranceData {
  developerName: string;
  developerAddress: string;
  companyAddress: string;
  contractorName: string;
  contractorAddress: string;
  siteAddress: string;
  totalProjectValue: number;
  natureOfWork: string;
  numberOfFloors: number;
  hasBasement: boolean;
  numberOfBasement?: number;
  hasPodium: boolean;
  numberOfPodium?: number;
  hasCommercialShops: boolean;
  numberOfShops?: number;
  projectStartDate: string;
  projectEndDate: string;
  bankFinancerDetails: string;
  workCompletedPercentage: number;
  balanceWorkPercentage: number;
  sitePhotographs: FileList | null;
}

@Component({
  selector: 'app-car',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule
  ],
  templateUrl: './car.component.html',
  styleUrl: './car.component.scss'
})
export class CarComponent {
  @Input() selectedProductSubType: string = '';
  @Output() submitSales = new EventEmitter<any>();

  carForm: FormGroup;
  isFormSubmitted: boolean = false;
  carInsuranceList: CarInsuranceData[] = [];

  constructor(private formBuilder: FormBuilder) {
    this.initForm();
  }

  initForm() {
    this.carForm = this.formBuilder.group({
      developerName: ['', Validators.required],
      developerAddress: ['', Validators.required],
      companyAddress: ['', Validators.required],
      contractorName: ['', Validators.required],
      contractorAddress: ['', Validators.required],
      siteAddress: ['', Validators.required],
      totalProjectValue: [0, [Validators.required, Validators.min(1)]],
      natureOfWork: ['', Validators.required],
      numberOfFloors: [1, [Validators.required, Validators.min(1)]],
      hasBasement: [false],
      numberOfBasement: [0],
      hasPodium: [false],
      numberOfPodium: [0],
      hasCommercialShops: [false],
      numberOfShops: [0],
      projectStartDate: ['', Validators.required],
      projectEndDate: ['', Validators.required],
      bankFinancerDetails: ['', Validators.required],
      workCompletedPercentage: [0, [Validators.min(0), Validators.max(100)]],
      balanceWorkPercentage: [100],
      sitePhotographs: [null, Validators.required]
    });

    // Subscribe to work completed percentage changes to auto-calculate balance
    this.carForm.get('workCompletedPercentage')?.valueChanges.subscribe(value => {
      if (value !== null && value !== undefined) {
        const balancePercentage = 100 - (value || 0);
        this.carForm.get('balanceWorkPercentage')?.setValue(balancePercentage, { emitEvent: false });
      }
    });
  }

  // Getter for easy access to form fields
  get form() {
    return this.carForm.controls;
  }

  onSubmit() {
    this.isFormSubmitted = true;

    // Stop here if form is invalid
    if (this.carForm.invalid) {
      return;
    }

    // Process the form data
    const formData = this.carForm.getRawValue();

    console.log('CAR Insurance Form submitted:', formData);

    // Prepare data for sales submission
    const productFormData = {
      component_type: 'car',
      product_type: 'Insurance - CAR',
      product_form_data: formData,
      customers: [] // CAR insurance doesn't have separate customer data
    };

    // Emit the form data to parent component
    this.submitSales.emit(productFormData);

    // Add to the list
    this.carInsuranceList.push(formData);

    // Reset the form
    this.resetForm();
  }

  resetForm() {
    this.carForm.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values
  }

  // Handle basement checkbox change
  onBasementChange(event: any) {
    const hasBasement = event.target.checked;
    if (hasBasement) {
      this.carForm.get('numberOfBasement')?.setValidators([Validators.required, Validators.min(1)]);
      this.carForm.get('numberOfBasement')?.setValue(1);
    } else {
      this.carForm.get('numberOfBasement')?.clearValidators();
      this.carForm.get('numberOfBasement')?.setValue(0);
    }
    this.carForm.get('numberOfBasement')?.updateValueAndValidity();
  }

  // Handle podium checkbox change
  onPodiumChange(event: any) {
    const hasPodium = event.target.checked;
    if (hasPodium) {
      this.carForm.get('numberOfPodium')?.setValidators([Validators.required, Validators.min(1)]);
      this.carForm.get('numberOfPodium')?.setValue(1);
    } else {
      this.carForm.get('numberOfPodium')?.clearValidators();
      this.carForm.get('numberOfPodium')?.setValue(0);
    }
    this.carForm.get('numberOfPodium')?.updateValueAndValidity();
  }

  // Handle commercial shops checkbox change
  onCommercialShopsChange(event: any) {
    const hasCommercialShops = event.target.checked;
    if (hasCommercialShops) {
      this.carForm.get('numberOfShops')?.setValidators([Validators.required, Validators.min(1)]);
      this.carForm.get('numberOfShops')?.setValue(1);
    } else {
      this.carForm.get('numberOfShops')?.clearValidators();
      this.carForm.get('numberOfShops')?.setValue(0);
    }
    this.carForm.get('numberOfShops')?.updateValueAndValidity();
  }

  // Method to get form data for sales submission
  getFormData(): any {
    if (this.carForm.valid) {
      const formData = this.carForm.getRawValue();
      return {
        component_type: 'car',
        product_type: 'Insurance - CAR',
        product_form_data: formData,
        customers: [] // CAR insurance doesn't have separate customer data
      };
    }
    return null;
  }

  /**
   * Populate form with existing data (for edit mode)
   */
  populateFormData(formData: any): void {
    if (!formData || !this.carForm) {
      console.log('⚠️ No form data or Car form not initialized');
      return;
    }

    console.log('🔧 Populating Car form with data:', formData);
    console.log('🔧 Car form current state before population:', this.carForm.value);

    try {
      // Populate main form with the provided data
      this.carForm.patchValue(formData);

      console.log('🔧 Car form state after population:', this.carForm.value);
      console.log('✅ Car form populated successfully');

      // Trigger any necessary validations or calculations
      this.carForm.updateValueAndValidity();
    } catch (error) {
      console.error('❌ Error populating Car form:', error);
      console.error('❌ Form data that caused error:', formData);
    }
  }

  /**
   * Reset form after successful submission
   */
  resetFormAfterSubmission(): void {
    console.log('🔄 Resetting Car form after successful submission');

    this.carForm.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values

    console.log('✅ Car form reset completed');
  }
}
