import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError, retry, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Legacy Employee interface for backward compatibility
export interface Employee {
  id?: number;
  employee_code?: string; // Employee code for identification
  employee_first_name: string;
  employee_last_name: string;
  email: string;
  department: string;
  position: string;
  start_date: string; // Date in YYYY-MM-DD format
  salary?: number;
  user_active?: boolean; // Changed from is_active to user_active
  created_at?: string;
  updated_at?: string;

  // Computed properties for display
  name?: string; // Will be computed from first_name + last_name
  phone?: string;
  joining_date?: Date;
  role?: string;
  permissions?: string[];
}

// BizzCorp Employee API Interface - matching exact API structure
export interface BizzCorpEmployee {
  id: string; // UUID string
  employee_code: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  date_of_birth?: string; // ISO date string
  gender?: string;
  marital_status?: string;
  personal_email?: string;
  office_email?: string;
  phone_no?: string; // Note: API uses phone_no, not alternet_no
  alternet_no?: string; // Alternative phone number
  address?: string;
  joining_date?: string; // ISO date string
  reporting_date?: string; // ISO date string
  resigned_stared_date?: string; // Note: exact spelling from API
  role?: string;
  department_id?: string; // UUID string
  designation_id?: string; // UUID string
  sub_role_id?: string; // UUID string
  office_location?: string;
  shift_time?: string;
  blood_group?: string;
  pan_no?: string;
  aadhar_no?: string;
  uan_no?: string;
  esic_no?: string;
  ctc?: number;
  attendance_bonus?: number;
  pf?: number;
  bank_name?: string;
  bank_account_no?: string;
  ifsc_no?: string;
  user_active?: boolean; // Changed from is_active to user_active
  approver_code?: string;
  second_approver_code?: string;
  resigned_end_date?: string;
  created_at?: string; // ISO timestamp
  updated_at?: string; // ISO timestamp
}

// Department interface for master data
export interface Department {
  id: string; // UUID string
  name: string;
  description?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Designation interface for master data
export interface Designation {
  id: string; // UUID string
  name: string;
  description?: string;
  department_id?: string; // UUID string
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Role interface for sub_role_id lookup
export interface BizzCorpRole {
  id: string; // UUID string
  name: string;
  description?: string;
  permissions?: string[];
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// API Response wrapper interface
export interface BizzCorpApiResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: {
    message?: string;
    total?: number;
    page?: number;
    size?: number;
  };
}

// Error handling interfaces
export interface EmployeeServiceError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export interface ProfileErrorContext {
  operation: 'fetch' | 'update' | 'create' | 'delete';
  employeeId?: string;
  endpoint?: string;
}

export interface BulkUploadResponse {
  success: boolean;
  data: {
    total: number;
    success: Array<{
      row: number;
      employee_code: string;
      name: string;
    }>;
    errors: Array<{
      row: number;
      employee_data: any;
      error_message: string;
    }>;
  };
  error: any;
  meta: {
    message: string;
  };
}

export interface EmployeeSearchParams {
  query?: string;
  department?: string;
  position?: string;
  user_active?: boolean; // Changed from is_active to user_active
  page?: number;
  size?: number;
}

// API Response interface similar to roles
export interface EmployeeApiResponse {
  data: Employee[];
  error: any;
  meta: any;
  success: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  private baseUrl = `${environment.apiUrl}/api/v1/employees`;
  private readonly REQUEST_TIMEOUT = 30000; // 30 seconds
  private readonly MAX_RETRIES = 2;

  constructor(private http: HttpClient) { }

  // ===== ERROR HANDLING METHODS =====

  /**
   * Handle HTTP errors with user-friendly messages
   * @param error HTTP error response
   * @param context Error context for better messaging
   * @returns Observable that throws a user-friendly error
   */
  private handleError(error: any, context: ProfileErrorContext): Observable<never> {
    console.error('EmployeeService Error:', error);
    console.error('Error Context:', context);

    let userMessage = 'An unexpected error occurred. Please try again.';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.status === 0) {
      // Network error
      userMessage = 'Unable to connect to the server. Please check your internet connection.';
      errorCode = 'NETWORK_ERROR';
    } else if (error.status === 401) {
      // Unauthorized
      userMessage = 'You are not authorized to perform this action. Please log in again.';
      errorCode = 'UNAUTHORIZED';
    } else if (error.status === 403) {
      // Forbidden
      userMessage = 'You do not have permission to access this information.';
      errorCode = 'FORBIDDEN';
    } else if (error.status === 404) {
      // Not found
      if (context.operation === 'fetch' && context.employeeId) {
        userMessage = `Employee with ID ${context.employeeId} was not found.`;
      } else {
        userMessage = 'The requested information was not found.';
      }
      errorCode = 'NOT_FOUND';
    } else if (error.status === 422) {
      // Validation error
      userMessage = 'The provided data is invalid. Please check your input.';
      errorCode = 'VALIDATION_ERROR';
    } else if (error.status >= 500) {
      // Server error
      userMessage = 'Server error occurred. Please try again later or contact support.';
      errorCode = 'SERVER_ERROR';
    } else if (error.error && error.error.message) {
      // API error with message
      userMessage = error.error.message;
      errorCode = 'API_ERROR';
    }

    const serviceError: EmployeeServiceError = {
      code: errorCode,
      message: userMessage,
      details: error,
      timestamp: new Date().toISOString()
    };

    return throwError(() => serviceError);
  }

  /**
   * Create error context for better error handling
   * @param operation Type of operation being performed
   * @param employeeId Optional employee ID
   * @param endpoint Optional endpoint being called
   * @returns Error context object
   */
  private createErrorContext(
    operation: ProfileErrorContext['operation'],
    employeeId?: string,
    endpoint?: string
  ): ProfileErrorContext {
    return {
      operation,
      employeeId,
      endpoint
    };
  }

  // ===== BIZZCORP EMPLOYEE API METHODS =====

  /**
   * Get employee profile data (BizzCorp API)
   * GET /api/v1/employees/{employee_id}
   * @param employeeId Employee UUID string
   * @returns Observable of employee profile details
   */
  getBizzCorpEmployeeProfile(employeeId: string): Observable<BizzCorpEmployee> {
    console.log('EmployeeService: Fetching BizzCorp employee profile for ID:', employeeId);

    return this.http.get<BizzCorpApiResponse<BizzCorpEmployee>>(`${this.baseUrl}/${employeeId}`).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: BizzCorp employee profile response:', response);

        if (response && response.success && response.data) {
          return response.data;
        } else if (response && !response.success) {
          throw new Error(response.error?.message || 'Failed to fetch employee profile');
        } else {
          // Handle direct response format
          return response as any as BizzCorpEmployee;
        }
      }),
      catchError(error =>
        this.handleError(error, this.createErrorContext('fetch', employeeId, `${this.baseUrl}/${employeeId}`))
      )
    );
  }

  /**
   * Get departments master data (BizzCorp API)
   * GET /api/v1/master/departments/
   * @returns Observable of departments list
   */
  getDepartmentsMasterData(): Observable<Department[]> {
    console.log('EmployeeService: Fetching departments master data');

    const masterUrl = `${environment.apiUrl}/api/v1/departments/`;

    return this.http.get<BizzCorpApiResponse<Department[]>>(masterUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: Departments response:', response);

        if (response && response.success && response.data) {
          return response.data;
        } else if (Array.isArray(response)) {
          // Handle direct array response
          return response;
        } else {
          console.warn('EmployeeService: Unexpected departments response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.warn('EmployeeService: Error fetching departments, using fallback:', error);
        // Return fallback departments instead of throwing error
        return of([
          { id: 'dept-1', name: 'IT', is_active: true },
          { id: 'dept-2', name: 'HR', is_active: true },
          { id: 'dept-3', name: 'Finance', is_active: true },
          { id: 'dept-4', name: 'Sales', is_active: true },
          { id: 'dept-5', name: 'Marketing', is_active: true }
        ] as Department[]);
      })
    );
  }

  /**
   * Get designations master data (BizzCorp API)
   * GET /api/v1/master/designations/
   * @returns Observable of designations list
   */
  getDesignationsMasterData(): Observable<Designation[]> {
    console.log('EmployeeService: Fetching designations master data');

    const masterUrl = `${environment.apiUrl}/api/v1/designations/`;

    return this.http.get<BizzCorpApiResponse<Designation[]>>(masterUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: Designations response:', response);

        if (response && response.success && response.data) {
          return response.data;
        } else if (Array.isArray(response)) {
          // Handle direct array response
          return response;
        } else {
          console.warn('EmployeeService: Unexpected designations response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.warn('EmployeeService: Error fetching designations, using fallback:', error);
        // Return fallback designations instead of throwing error
        return of([
          { id: 'desig-1', name: 'Software Engineer', is_active: true },
          { id: 'desig-2', name: 'Senior Engineer', is_active: true },
          { id: 'desig-3', name: 'Team Lead', is_active: true },
          { id: 'desig-4', name: 'Manager', is_active: true },
          { id: 'desig-5', name: 'Director', is_active: true }
        ] as Designation[]);
      })
    );
  }

  /**
   * Get specific designation by ID (BizzCorp API)
   * GET /api/v1/master/designations/{designation_id}
   * @param designationId Designation UUID string
   * @returns Observable of designation details
   */
  getDesignationById(designationId: string): Observable<Designation> {
    console.log('EmployeeService: Fetching designation by ID:', designationId);

    const masterUrl = `${environment.apiUrl}/api/v1/master/designations/${designationId}`;

    return this.http.get<BizzCorpApiResponse<Designation>>(masterUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: Designation by ID response:', response);

        if (response && response.success && response.data) {
          return response.data;
        } else {
          // Handle direct response format
          return response as any as Designation;
        }
      }),
      catchError(error =>
        this.handleError(error, this.createErrorContext('fetch', undefined, masterUrl))
      )
    );
  }

  /**
   * Get role by ID for sub_role_id lookup (BizzCorp API) - DEPRECATED
   * GET /api/v1/roles/{role_id}
   * @param roleId Role UUID string
   * @returns Observable of role details
   * @deprecated Use getUserRoles() instead for user-specific role resolution
   */
  getRoleById(roleId: string): Observable<BizzCorpRole> {
    console.log('EmployeeService: Fetching role by ID (DEPRECATED):', roleId);

    const rolesUrl = `${environment.apiUrl}/api/v1/roles/${roleId}`;

    return this.http.get<BizzCorpApiResponse<BizzCorpRole>>(rolesUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: Role by ID response:', response);

        if (response && response.success && response.data) {
          return response.data;
        } else {
          // Handle direct response format
          return response as any as BizzCorpRole;
        }
      }),
      catchError(error =>
        this.handleError(error, this.createErrorContext('fetch', undefined, rolesUrl))
      )
    );
  }

  /**
   * Get current user's roles from User Roles API (BizzCorp API)
   * GET /api/v1/user-roles/me/roles
   * @returns Observable of user roles array
   */
  getUserRoles(): Observable<BizzCorpRole[]> {
    console.log('EmployeeService: Fetching current user roles from User Roles API');

    const userRolesUrl = `${environment.apiUrl}/api/v1/user-roles/me/roles`;

    return this.http.get<BizzCorpApiResponse<BizzCorpRole[]>>(userRolesUrl).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: User roles response:', response);

        if (response && response.success && response.data && Array.isArray(response.data)) {
          return response.data;
        } else if (Array.isArray(response)) {
          // Handle direct array response
          return response;
        } else {
          console.warn('EmployeeService: Unexpected user roles response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.error('EmployeeService: Error fetching user roles:', error);
        // Return empty array instead of throwing error to maintain graceful degradation
        return of([]);
      })
    );
  }

  /**
   * Find role by name from user's roles
   * @param roleName Role name to search for (e.g., "admin", "manager")
   * @param userRoles Array of user roles from getUserRoles()
   * @returns Role object if found, null otherwise
   */
  findRoleByName(roleName: string, userRoles: BizzCorpRole[]): BizzCorpRole | null {
    if (!roleName || !userRoles || userRoles.length === 0) {
      return null;
    }

    console.log('EmployeeService: Searching for role name:', roleName, 'in user roles:', userRoles.map(r => r.name));

    const role = userRoles.find(role =>
      role.name && role.name.toLowerCase() === roleName.toLowerCase()
    );

    if (role) {
      console.log('✅ EmployeeService: Role found:', role);
    } else {
      console.warn('⚠️ EmployeeService: Role not found for name:', roleName);
    }

    return role || null;
  }

  /**
   * Get all employees with pagination and search using skip/limit
   * @param skip Number of records to skip (default: 0)
   * @param limit Maximum number of records to return (default: 10)
   * @param search Search term (optional)
   * @returns Observable of employee API response
   */
  getEmployees(skip: number = 0, limit: number = 10, search?: string): Observable<EmployeeApiResponse> {
    console.log('EmployeeService: Making request to:', this.baseUrl);
    console.log('EmployeeService: Environment API URL:', environment.apiUrl);
    console.log('EmployeeService: Full URL:', this.baseUrl);
    console.log('EmployeeService: Parameters - skip:', skip, 'limit:', limit, 'search:', search);

    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<EmployeeApiResponse>(`${this.baseUrl}/`, { params });
  }

  /**
   * Get employees with page/size pagination (backward compatibility)
   * Converts page/size to skip/limit format
   */
  getEmployeesLegacy(page: number = 1, size: number = 10, search?: string): Observable<EmployeeApiResponse> {
    const skip = (page - 1) * size;
    return this.getEmployees(skip, size, search);
  }

  /**
   * Get all employees (legacy method for backward compatibility)
   * @returns Observable of employee list
   */
  getAllEmployees(): Observable<Employee[]> {
    console.log('EmployeeService: Making legacy request to:', `${this.baseUrl}/`);

    // Add header to skip error interceptor for comp-off service compatibility
    const headers = new HttpHeaders({
      'X-Skip-Error-Interceptor': 'true'
    });

    return this.http.get<any>(`${this.baseUrl}/`, { headers }).pipe(
      map(response => {
        console.log('EmployeeService: Raw API response:', response);

        // Handle different response formats
        if (Array.isArray(response)) {
          // Direct array response
          console.log('EmployeeService: Direct array response detected');
          return response;
        } else if (response && response.data && Array.isArray(response.data)) {
          // Wrapped response with data property
          console.log('EmployeeService: Wrapped response detected, extracting data');
          return response.data;
        } else if (response && response.results && Array.isArray(response.results)) {
          // Paginated response with results property
          console.log('EmployeeService: Paginated response detected, extracting results');
          return response.results;
        } else {
          console.warn('EmployeeService: Unexpected response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.error('EmployeeService: Error fetching employees:', error);
        return of([]); // Return empty array on error
      })
    );
  }



  /**
   * Get a specific employee by ID
   * @param id Employee ID (number or string UUID)
   * @returns Observable of employee details
   */
  getEmployee(id: number | string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get a specific employee by UUID
   * @param uuid Employee UUID string
   * @returns Observable of employee details
   */
  getEmployeeByUuid(uuid: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${uuid}`);
  }

  /**
   * Create a new employee
   * @param employee Employee data
   * @returns Observable of created employee
   */
  createEmployee(employee: Employee): Observable<Employee> {
    return this.http.post<Employee>(`${this.baseUrl}/`, employee);
  }

  /**
   * Update an existing employee by ID (supports both number and string UUID)
   * @param id Employee ID (number or string UUID)
   * @param employee Updated employee data
   * @returns Observable of updated employee
   */
  updateEmployee(id: number | string, employee: Partial<Employee> | any): Observable<any> {
    console.log('EmployeeService: Updating employee with ID:', id);
    console.log('EmployeeService: Update URL:', `${this.baseUrl}/${id}`);
    console.log('EmployeeService: Update data:', employee);
    return this.http.put<any>(`${this.baseUrl}/${id}`, employee);
  }

  /**
   * Update an existing employee by employee code (legacy method)
   * @param employeeCode Employee code (string)
   * @param employee Updated employee data
   * @returns Observable of updated employee
   */
  updateEmployeeByCode(employeeCode: string, employee: any): Observable<any> {
    console.log('EmployeeService: Updating employee with code:', employeeCode);
    console.log('EmployeeService: Update URL:', `${this.baseUrl}/${employeeCode}`);
    console.log('EmployeeService: Update data:', employee);
    return this.http.put<any>(`${this.baseUrl}/${employeeCode}`, employee);
  }

  /**
   * Delete an employee
   * @param id Employee ID
   * @returns Observable of operation result
   */
  deleteEmployee(id: number): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`);
  }

  /**
   * Activate or deactivate an employee
   * @param id Employee ID
   * @param isActive Status to set
   * @returns Observable of updated employee
   */
  setEmployeeStatus(id: number, isActive: boolean): Observable<Employee> {
    return this.http.patch<Employee>(`${this.baseUrl}/${id}/status`, { user_active: isActive }); // Backend now expects user_active
  }

  /**
   * Assign a role to an employee
   * @param employeeId Employee ID
   * @param roleId Role ID
   * @returns Observable of operation result
   */
  assignRole(employeeId: number, roleId: number): Observable<any> {
    return this.http.post(`${this.baseUrl}/${employeeId}/roles`, { role_id: roleId });
  }

  /**
   * Get all available roles
   * @returns Observable of roles list
   */
  getRoles(): Observable<any[]> {
    console.log('EmployeeService: Fetching roles from API');

    return this.http.get<BizzCorpApiResponse<any[]>>(`${environment.apiUrl}/api/v1/roles/`).pipe(
      timeout(this.REQUEST_TIMEOUT),
      retry(this.MAX_RETRIES),
      map(response => {
        console.log('EmployeeService: Roles response:', response);

        if (response && response.success && response.data && Array.isArray(response.data)) {
          return response.data;
        } else if (Array.isArray(response)) {
          // Handle direct array response
          return response;
        } else {
          console.warn('EmployeeService: Unexpected roles response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.error('EmployeeService: Error fetching roles:', error);
        // Return empty array instead of throwing error to maintain graceful degradation
        return of([]);
      })
    );
  }

  /**
   * Export employees to Excel
   * @returns Observable of blob data
   */
  exportEmployees(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/export`, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    });
  }

  /**
   * Get employees by department
   * @param department Department name
   * @returns Observable of employee list
   */
  getEmployeesByDepartment(department: string): Observable<Employee[]> {
    return this.http.get<Employee[]>(`${this.baseUrl}/department/${encodeURIComponent(department)}`);
  }

  /**
   * Get employees by designation/position
   * @param designation Position/designation name
   * @returns Observable of employee list
   */
  getEmployeesByDesignation(designation: string): Observable<Employee[]> {
    return this.http.get<Employee[]>(`${this.baseUrl}/designation/${encodeURIComponent(designation)}`);
  }

  /**
   * Get active employees only
   * @returns Observable of active employee list
   */
  getActiveEmployees(): Observable<Employee[]> {
    return this.http.get<Employee[]>(`${this.baseUrl}/status/active`);
  }

  /**
   * Search employees with various filters
   * @param params Search parameters
   * @returns Observable of employee list
   */
  searchEmployees(params: EmployeeSearchParams): Observable<Employee[]> {
    let queryParams = new URLSearchParams();

    if (params.query) queryParams.append('query', params.query);
    if (params.department) queryParams.append('department', params.department);
    if (params.position) queryParams.append('position', params.position);
    if (params.user_active !== undefined) queryParams.append('user_active', params.user_active.toString()); // Backend now expects user_active
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.size) queryParams.append('size', params.size.toString());

    const url = `${this.baseUrl}/search${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return this.http.get<Employee[]>(url);
  }

  /**
   * Bulk upload employees from Excel file
   * @param file Excel file containing employee data
   * @returns Observable of upload response
   */
  bulkUploadEmployees(file: File): Observable<BulkUploadResponse> {
    console.log('EmployeeService: Starting bulk upload to:', `${this.baseUrl}/bulk-upload`);
    console.log('EmployeeService: File details:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/bulk-upload`, formData);
  }

  /**
   * Download bulk upload error report from server
   * @param file Original Excel file that was uploaded
   * @returns Observable of blob response for file download
   */
  downloadBulkUploadErrorReport(file: File): Observable<Blob> {
    console.log('EmployeeService: Downloading bulk upload error report from:', `${this.baseUrl}/bulk-upload/errors/download`);
    console.log('EmployeeService: File details for error report:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    const formData = new FormData();
    formData.append('file', file);

    return this.http.post(`${this.baseUrl}/bulk-upload/errors/download`, formData, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    });
  }

  /**
   * Download bulk upload error report using error data payload
   * @param errorData Error details from bulk upload validation
   * @returns Observable of blob response for file download
   */
  downloadBulkUploadErrors(errorData: any): Observable<Blob> {
    console.log('EmployeeService: Downloading bulk upload errors with payload to:', `${this.baseUrl}/bulk-upload/errors/download`);
    console.log('EmployeeService: Error data payload:', errorData);

    return this.http.post(`${this.baseUrl}/bulk-upload/errors/download`, errorData, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Type': 'application/json'
      }
    });
  }

  // Additional Employee Management Methods (from OpenAPI analysis)

  /**
   * Get employee profile with detailed information
   * GET /api/v1/employees/{employee_id}/profile
   * @param employeeId Employee ID
   * @returns Observable of detailed employee profile
   */
  getEmployeeProfile(employeeId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${employeeId}/profile`);
  }

  /**
   * Update employee profile
   * PUT /api/v1/employees/{employee_id}/profile
   * @param employeeId Employee ID
   * @param profileData Profile data to update
   * @returns Observable of updated profile
   */
  updateEmployeeProfile(employeeId: string, profileData: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/${employeeId}/profile`, profileData);
  }

  /**
   * Get employee roles and permissions
   * GET /api/v1/employees/{employee_id}/roles
   * @param employeeId Employee ID
   * @returns Observable of employee roles
   */
  getEmployeeRoles(employeeId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/${employeeId}/roles`);
  }

  /**
   * Assign roles to an employee
   * POST /api/v1/employees/{employee_id}/roles
   * @param employeeId Employee ID
   * @param roleIds Array of role IDs to assign
   * @returns Observable of operation result
   */
  assignRolesToEmployee(employeeId: string, roleIds: string[]): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/${employeeId}/roles`, { role_ids: roleIds });
  }

  /**
   * Remove role from employee
   * DELETE /api/v1/employees/{employee_id}/roles/{role_id}
   * @param employeeId Employee ID
   * @param roleId Role ID to remove
   * @returns Observable of operation result
   */
  removeRoleFromEmployee(employeeId: string, roleId: string): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/${employeeId}/roles/${roleId}`);
  }

  /**
   * Get employee attendance summary
   * GET /api/v1/employees/{employee_id}/attendance
   * @param employeeId Employee ID
   * @param fromDate Start date (YYYY-MM-DD)
   * @param toDate End date (YYYY-MM-DD)
   * @returns Observable of attendance summary
   */
  getEmployeeAttendance(employeeId: string, fromDate?: string, toDate?: string): Observable<any> {
    let params = new HttpParams();
    if (fromDate) params = params.set('from_date', fromDate);
    if (toDate) params = params.set('to_date', toDate);

    return this.http.get<any>(`${this.baseUrl}/${employeeId}/attendance`, { params });
  }

  /**
   * Get employee leave summary
   * GET /api/v1/employees/{employee_id}/leaves
   * @param employeeId Employee ID
   * @param year Optional year filter
   * @returns Observable of leave summary
   */
  getEmployeeLeaves(employeeId: string, year?: number): Observable<any> {
    let params = new HttpParams();
    if (year) params = params.set('year', year.toString());

    return this.http.get<any>(`${this.baseUrl}/${employeeId}/leaves`, { params });
  }

  /**
   * Get employee salary information
   * GET /api/v1/employees/{employee_id}/salary
   * @param employeeId Employee ID
   * @returns Observable of salary information
   */
  getEmployeeSalary(employeeId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${employeeId}/salary`);
  }

  /**
   * Update employee salary
   * PUT /api/v1/employees/{employee_id}/salary
   * @param employeeId Employee ID
   * @param salaryData Salary data to update
   * @returns Observable of updated salary information
   */
  updateEmployeeSalary(employeeId: string, salaryData: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/${employeeId}/salary`, salaryData);
  }

  /**
   * Soft delete an employee
   * DELETE /api/v1/employees/{employee_id}/soft
   * @param employeeId Employee ID
   * @returns Observable of operation result
   */
  softDeleteEmployee(employeeId: string): Observable<boolean> {
    return this.http.delete<any>(`${this.baseUrl}/${employeeId}/soft`);
  }

  /**
   * Restore a soft-deleted employee
   * POST /api/v1/employees/{employee_id}/restore
   * @param employeeId Employee ID
   * @returns Observable of restored employee
   */
  restoreEmployee(employeeId: string): Observable<Employee> {
    return this.http.post<Employee>(`${this.baseUrl}/${employeeId}/restore`, {});
  }

  /**
   * Get deleted employees
   * GET /api/v1/employees/deleted
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @returns Observable of deleted employees
   */
  getDeletedEmployees(skip: number = 0, limit: number = 100): Observable<Employee[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    return this.http.get<Employee[]>(`${this.baseUrl}/deleted`, { params });
  }

  /**
   * Advanced employee search with multiple criteria
   * POST /api/v1/employees/search
   * @param searchCriteria Search criteria object
   * @returns Observable of matching employees
   */
  advancedEmployeeSearch(searchCriteria: any): Observable<Employee[]> {
    return this.http.post<Employee[]>(`${this.baseUrl}/search`, searchCriteria);
  }
}
