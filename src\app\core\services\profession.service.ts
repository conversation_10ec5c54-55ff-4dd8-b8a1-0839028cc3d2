import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Profession interfaces based on OpenAPI specification
 */
export interface Profession {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'pending' | 'archived';
  description?: string;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface ProfessionCreate {
  name: string;
  type: string;
  status?: 'active' | 'inactive' | 'pending' | 'archived';
  description?: string;
}

export interface ProfessionUpdate {
  name?: string;
  type?: string;
  status?: 'active' | 'inactive' | 'pending' | 'archived';
  description?: string;
}

export interface ProfessionFilter {
  name?: string;
  type?: string;
  status?: string;
  search?: string;
}

export interface BulkUploadError {
  row: number;
  field?: string;
  error: string;
  value?: string;
}

export interface BulkUploadResponse {
  success: boolean;
  data: {
    total: number;
    success_count: number;
    error_count: number;
    errors: BulkUploadError[];
  };
  error?: any;
  meta?: any;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * Profession Service
 * Handles profession master data management including CRUD operations,
 * bulk upload, and filtering capabilities.
 */
@Injectable({
  providedIn: 'root'
})
export class ProfessionService {
  private baseUrl = `${environment.apiUrl}/api/v1/professions`;

  constructor(private http: HttpClient) {}

  /**
   * Get all professions with filtering and pagination
   * GET /api/v1/professions/
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of professions list
   */
  getProfessions(
    filter: Partial<ProfessionFilter> = {},
    skip: number = 0,
    limit: number = 100,
    includeDeleted: boolean = false
  ): Observable<Profession[]> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    // Add filter parameters
    if (filter.name) params = params.set('name', filter.name);
    if (filter.type) params = params.set('type', filter.type);
    if (filter.status) params = params.set('status', filter.status);
    if (filter.search) params = params.set('search', filter.search);

    return this.http.get<APIResponse<Profession[]>>(`${this.baseUrl}/`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getProfessions', []))
    );
  }

  /**
   * Get a specific profession by ID
   * GET /api/v1/professions/{profession_id}
   * @param professionId Profession ID
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of profession
   */
  getProfession(professionId: string, includeDeleted: boolean = false): Observable<Profession> {
    const params = new HttpParams().set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<Profession>>(`${this.baseUrl}/${professionId}`, { params }).pipe(
      map((response: APIResponse<Profession>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Profession not found');
      }),
      catchError(this.handleError<Profession>('getProfession'))
    );
  }

  /**
   * Create a new profession
   * POST /api/v1/professions/
   * @param profession Profession data
   * @returns Observable of created profession
   */
  createProfession(profession: ProfessionCreate): Observable<Profession> {
    return this.http.post<APIResponse<Profession>>(`${this.baseUrl}/`, profession).pipe(
      map((response: APIResponse<Profession>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create profession');
      }),
      catchError(this.handleError<Profession>('createProfession'))
    );
  }

  /**
   * Update an existing profession
   * PUT /api/v1/professions/{profession_id}
   * @param professionId Profession ID
   * @param profession Updated profession data
   * @returns Observable of updated profession
   */
  updateProfession(professionId: string, profession: ProfessionUpdate): Observable<Profession> {
    return this.http.put<APIResponse<Profession>>(`${this.baseUrl}/${professionId}`, profession).pipe(
      map((response: APIResponse<Profession>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to update profession');
      }),
      catchError(this.handleError<Profession>('updateProfession'))
    );
  }

  /**
   * Delete a profession (soft delete)
   * DELETE /api/v1/professions/{profession_id}
   * @param professionId Profession ID
   * @param hardDelete Whether to permanently delete
   * @returns Observable of operation result
   */
  deleteProfession(professionId: string, hardDelete: boolean = false): Observable<boolean> {
    const params = new HttpParams().set('hard_delete', hardDelete.toString());

    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${professionId}`, { params }).pipe(
      map((response: APIResponse<boolean>) => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to delete profession');
      }),
      catchError(this.handleError<boolean>('deleteProfession'))
    );
  }

  /**
   * Restore a soft-deleted profession
   * POST /api/v1/professions/{profession_id}/restore
   * @param professionId Profession ID
   * @returns Observable of restored profession
   */
  restoreProfession(professionId: string): Observable<Profession> {
    return this.http.post<APIResponse<Profession>>(`${this.baseUrl}/${professionId}/restore`, {}).pipe(
      map((response: APIResponse<Profession>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to restore profession');
      }),
      catchError(this.handleError<Profession>('restoreProfession'))
    );
  }

  /**
   * Bulk upload professions from file
   * POST /api/v1/professions/bulk-upload
   * @param file File containing profession data
   * @returns Observable of bulk upload response
   */
  bulkUploadProfessions(file: File): Observable<BulkUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/bulk-upload`, formData).pipe(
      catchError(this.handleError<BulkUploadResponse>('bulkUploadProfessions'))
    );
  }

  /**
   * Export professions to Excel
   * GET /api/v1/professions/export
   * @param filter Optional filter criteria
   * @returns Observable of Excel file blob
   */
  exportProfessions(filter: Partial<ProfessionFilter> = {}): Observable<Blob> {
    let params = new HttpParams();

    // Add filter parameters
    if (filter.name) params = params.set('name', filter.name);
    if (filter.type) params = params.set('type', filter.type);
    if (filter.status) params = params.set('status', filter.status);
    if (filter.search) params = params.set('search', filter.search);

    return this.http.get(`${this.baseUrl}/export`, {
      params,
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    }).pipe(
      catchError(this.handleError<Blob>('exportProfessions'))
    );
  }

  /**
   * Get profession types (distinct values)
   * GET /api/v1/professions/types
   * @returns Observable of profession types
   */
  getProfessionTypes(): Observable<string[]> {
    return this.http.get<APIResponse<string[]>>(`${this.baseUrl}/types`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getProfessionTypes', []))
    );
  }

  /**
   * Search professions by name or type
   * GET /api/v1/professions/search
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching professions
   */
  searchProfessions(query: string, limit: number = 20): Observable<Profession[]> {
    const params = new HttpParams()
      .set('q', query)
      .set('limit', limit.toString());

    return this.http.get<APIResponse<Profession[]>>(`${this.baseUrl}/search`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('searchProfessions', []))
    );
  }

  /**
   * Get professions by status
   * @param status Profession status
   * @returns Observable of professions with specified status
   */
  getProfessionsByStatus(status: 'active' | 'inactive' | 'pending' | 'archived'): Observable<Profession[]> {
    return this.getProfessions({ status });
  }

  /**
   * Get active professions only
   * @returns Observable of active professions
   */
  getActiveProfessions(): Observable<Profession[]> {
    return this.getProfessionsByStatus('active');
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
