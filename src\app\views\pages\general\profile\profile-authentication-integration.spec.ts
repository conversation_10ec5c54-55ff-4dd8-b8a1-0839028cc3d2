import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { ProfileComponent } from './profile.component';
import { EmployeeService, BizzCorpEmployee, Department, Designation } from '../../../../core/services/employee.service';
import { AuthService, User } from '../../../../core/services/auth.service';
import { RoleService } from '../../../../core/services/role.service';

describe('ProfileComponent - Authentication Integration', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let employeeService: jasmine.SpyObj<EmployeeService>;
  let router: jasmine.SpyObj<Router>;

  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    name: '<PERSON>',
    firstName: '<PERSON>',
    lastName: 'Doe',
    access_token: 'mock-token',
    is_active: true
  };

  const mockEmployee: BizzCorpEmployee = {
    id: '1910042b-3036-40cd-97cd-d2471b5de402',
    employee_code: 'EMP001',
    first_name: 'John',
    last_name: 'Doe',
    office_email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  };

  const mockEmployeeList = [
    {
      id: '1910042b-3036-40cd-97cd-d2471b5de402',
      employee_code: 'EMP001',
      first_name: 'John',
      last_name: 'Doe',
      office_email: '<EMAIL>'
    },
    {
      id: 'other-employee-id',
      employee_code: 'EMP002',
      first_name: 'Jane',
      last_name: 'Smith',
      office_email: '<EMAIL>'
    }
  ];

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'isLoggedIn',
      'currentUserValue'
    ], {
      currentUserValue: mockUser
    });

    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees',
      'getBizzCorpEmployeeProfile',
      'getDepartmentsMasterData',
      'getDesignationsMasterData'
    ]);

    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const roleServiceSpy = jasmine.createSpyObj('RoleService', ['getAllRoles']);

    await TestBed.configureTestingModule({
      imports: [
        ProfileComponent,
        HttpClientTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: RoleService, useValue: roleServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Setup default spy returns
    authService.isLoggedIn.and.returnValue(true);
    Object.defineProperty(authService, 'currentUserValue', {
      get: jasmine.createSpy('currentUserValue').and.returnValue(mockUser)
    });
    employeeService.getAllEmployees.and.returnValue(of(mockEmployeeList));
    employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(mockEmployee));
    employeeService.getDepartmentsMasterData.and.returnValue(of([]));
    employeeService.getDesignationsMasterData.and.returnValue(of([]));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Authentication Flow', () => {
    it('should redirect to login if user is not authenticated', () => {
      authService.isLoggedIn.and.returnValue(false);

      component.ngOnInit();

      expect(router.navigate).toHaveBeenCalledWith(['/auth/login']);
      expect(component.authenticationError).toBe('You must be logged in to view your profile.');
    });

    it('should redirect to login if no current user found', () => {
      authService.isLoggedIn.and.returnValue(true);
      Object.defineProperty(authService, 'currentUserValue', {
        get: jasmine.createSpy('currentUserValue').and.returnValue(null)
      });

      component.ngOnInit();

      expect(router.navigate).toHaveBeenCalledWith(['/auth/login']);
      expect(component.authenticationError).toBe('Unable to determine current user. Please log in again.');
    });

    it('should set current user when authenticated', () => {
      component.ngOnInit();

      expect(component.currentUser).toEqual(mockUser);
    });
  });

  describe('Employee ID Resolution', () => {
    it('should resolve employee ID by matching office email', () => {
      component.ngOnInit();

      expect(employeeService.getAllEmployees).toHaveBeenCalled();
      expect(component.currentEmployeeId).toBe('1910042b-3036-40cd-97cd-d2471b5de402');
    });

    it('should resolve employee ID by matching personal email', () => {
      const employeeListWithPersonalEmail = [
        {
          id: '1910042b-3036-40cd-97cd-d2471b5de402',
          employee_code: 'EMP001',
          first_name: 'John',
          last_name: 'Doe',
          personal_email: '<EMAIL>', // Match on personal email
          office_email: '<EMAIL>'
        }
      ];

      employeeService.getAllEmployees.and.returnValue(of(employeeListWithPersonalEmail));

      component.ngOnInit();

      expect(component.currentEmployeeId).toBe('1910042b-3036-40cd-97cd-d2471b5de402');
    });

    it('should resolve employee ID by matching employee code', () => {
      const userWithEmployeeCode = { ...mockUser, id: 'EMP001' };
      Object.defineProperty(authService, 'currentUserValue', {
        get: jasmine.createSpy('currentUserValue').and.returnValue(userWithEmployeeCode)
      });

      const employeeListWithDifferentEmail = [
        {
          id: '1910042b-3036-40cd-97cd-d2471b5de402',
          employee_code: 'EMP001', // Match on employee code
          first_name: 'John',
          last_name: 'Doe',
          office_email: '<EMAIL>',
          personal_email: '<EMAIL>'
        }
      ];

      employeeService.getAllEmployees.and.returnValue(of(employeeListWithDifferentEmail));

      component.ngOnInit();

      expect(component.currentEmployeeId).toBe('1910042b-3036-40cd-97cd-d2471b5de402');
    });

    it('should use user UUID as fallback if valid', () => {
      const userWithValidUUID = { ...mockUser, id: '550e8400-e29b-41d4-a716-************' };
      Object.defineProperty(authService, 'currentUserValue', {
        get: jasmine.createSpy('currentUserValue').and.returnValue(userWithValidUUID)
      });

      // Return empty employee list to trigger fallback
      employeeService.getAllEmployees.and.returnValue(of([]));

      component.ngOnInit();

      expect(component.currentEmployeeId).toBe('550e8400-e29b-41d4-a716-************');
    });

    it('should handle employee not found error', () => {
      // Return empty employee list and invalid user UUID
      employeeService.getAllEmployees.and.returnValue(of([]));

      component.ngOnInit();

      expect(component.currentEmployeeId).toBeNull();
      expect(component.authenticationError).toBe('Unable to find your employee record. Please contact support.');
    });

    it('should handle API error with valid user UUID fallback', () => {
      const userWithValidUUID = { ...mockUser, id: '550e8400-e29b-41d4-a716-************' };
      Object.defineProperty(authService, 'currentUserValue', {
        get: jasmine.createSpy('currentUserValue').and.returnValue(userWithValidUUID)
      });

      employeeService.getAllEmployees.and.returnValue(throwError(() => new Error('API Error')));

      component.ngOnInit();

      expect(component.currentEmployeeId).toBe('550e8400-e29b-41d4-a716-************');
    });

    it('should handle API error without valid user UUID', () => {
      employeeService.getAllEmployees.and.returnValue(throwError(() => new Error('API Error')));

      component.ngOnInit();

      expect(component.currentEmployeeId).toBeNull();
      expect(component.authenticationError).toBe('Error loading employee information. Please try again.');
    });
  });

  describe('Profile Data Loading', () => {
    it('should load profile data after resolving employee ID', () => {
      component.ngOnInit();

      expect(employeeService.getBizzCorpEmployeeProfile).toHaveBeenCalledWith('1910042b-3036-40cd-97cd-d2471b5de402');
      expect(component.currentEmployee).toEqual(mockEmployee);
    });

    it('should not load profile data if no employee ID resolved', () => {
      employeeService.getAllEmployees.and.returnValue(of([]));

      component.ngOnInit();

      expect(employeeService.getBizzCorpEmployeeProfile).not.toHaveBeenCalled();
      expect(component.currentEmployee).toBeNull();
    });
  });

  describe('Retry Functionality', () => {
    it('should retry authentication when retryAuthentication is called', () => {
      spyOn(component as any, 'initializeCurrentEmployee');

      component.retryAuthentication();

      expect((component as any).initializeCurrentEmployee).toHaveBeenCalled();
    });

    it('should retry profile loading when retryLoadProfile is called with employee ID', () => {
      component.currentEmployeeId = '1910042b-3036-40cd-97cd-d2471b5de402';
      spyOn(component as any, 'loadBizzCorpProfileData');

      component.retryLoadProfile();

      expect((component as any).loadBizzCorpProfileData).toHaveBeenCalled();
    });

    it('should retry authentication when retryLoadProfile is called without employee ID', () => {
      component.currentEmployeeId = null;
      spyOn(component as any, 'initializeCurrentEmployee');

      component.retryLoadProfile();

      expect((component as any).initializeCurrentEmployee).toHaveBeenCalled();
    });
  });

  describe('UUID Validation', () => {
    it('should validate correct UUID format', () => {
      const validUUID = '550e8400-e29b-41d4-a716-************';
      const result = (component as any).isValidUUID(validUUID);
      expect(result).toBe(true);
    });

    it('should reject invalid UUID format', () => {
      const invalidUUID = 'not-a-uuid';
      const result = (component as any).isValidUUID(invalidUUID);
      expect(result).toBe(false);
    });
  });
});
