<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Leased Property</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #leasedPropertyForm="ngForm">
    <div class="row mb-3">
      <!-- Property Information Section -->
      <div class="col-12 mb-3">
        <h6 class="section-title">Property Information</h6>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectName" class="form-label">Project Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="projectName" name="projectName" [(ngModel)]="formData.projectName" required>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="location" name="location" [(ngModel)]="formData.location" required>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="areaLeased" class="form-label">Area Leased (sq.ft) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="areaLeased" name="areaLeased" [(ngModel)]="formData.areaLeased" required>
      </div>
      
      <!-- Lease and Financial Information Section -->
      <div class="col-12 mb-3 mt-2">
        <h6 class="section-title">Lease and Financial Information</h6>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="leaseRentPA" class="form-label">Lease Rent P.A. (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="leaseRentPA" name="leaseRentPA" [(ngModel)]="formData.leaseRentPA" required>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="securityDeposit" class="form-label">Security Deposit (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="securityDeposit" name="securityDeposit" [(ngModel)]="formData.securityDeposit" required>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="debtOS" class="form-label">Debt O/S (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="debtOS" name="debtOS" [(ngModel)]="formData.debtOS" required>
      </div>
      
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="marketValue" class="form-label">Market Value (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="marketValue" name="marketValue" [(ngModel)]="formData.marketValue" required>
      </div>
      
      <div class="col-12 mb-3">
        <label for="remarks" class="form-label">Remarks</label>
        <textarea class="form-control" id="remarks" name="remarks" rows="2" [(ngModel)]="formData.remarks"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="leasedPropertyForm.invalid" (click)="saveChanges()">Save</button>
</div>
