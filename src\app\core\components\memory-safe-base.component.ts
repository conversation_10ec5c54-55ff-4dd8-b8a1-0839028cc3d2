import { Component, OnDestroy, inject } from '@angular/core';
import { Subject, Subscription, Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MemoryManagerService } from '../services/memory-manager.service';

/**
 * Memory Safe Base Component
 * 
 * Provides automatic memory management for Angular components.
 * Extend this class to automatically handle subscription cleanup,
 * observer disconnection, and timer clearing.
 */
@Component({
  template: '',
  standalone: true
})
export abstract class MemorySafeBaseComponent implements OnDestroy {
  protected memoryManager = inject(MemoryManagerService);
  
  // Automatic cleanup subject
  protected destroy$ = new Subject<void>();
  
  // Track component resources
  private subscriptions = new Set<Subscription>();
  private observers = new Set<any>();
  private timers = new Set<number>();
  private intervals = new Set<number>();
  private eventListeners = new Set<{ element: Element; event: string; handler: EventListener }>();
  
  // Component name for tracking
  protected componentName: string;

  constructor() {
    this.componentName = this.constructor.name;
    console.log(`🧠 ${this.componentName}: Memory-safe component initialized`);
  }

  ngOnDestroy(): void {
    console.log(`🧠 ${this.componentName}: Starting memory cleanup`);
    
    // Signal destruction to all observables
    this.destroy$.next();
    this.destroy$.complete();
    
    // Clean up all tracked resources
    this.cleanupSubscriptions();
    this.cleanupObservers();
    this.cleanupTimers();
    this.cleanupEventListeners();
    
    console.log(`✅ ${this.componentName}: Memory cleanup completed`);
  }

  /**
   * Safe subscription method that automatically unsubscribes on destroy
   */
  protected safeSubscribe<T>(
    observable: Observable<T>,
    next?: (value: T) => void,
    error?: (error: any) => void,
    complete?: () => void
  ): Subscription {
    const subscription = observable.pipe(
      takeUntil(this.destroy$)
    ).subscribe({ next, error, complete });
    
    this.trackSubscription(subscription);
    return subscription;
  }

  /**
   * Track a subscription for automatic cleanup
   */
  protected trackSubscription(subscription: Subscription): void {
    this.subscriptions.add(subscription);
    this.memoryManager.registerSubscription(this.componentName, subscription);
  }

  /**
   * Track an observer for automatic cleanup
   */
  protected trackObserver(observer: any): void {
    this.observers.add(observer);
    this.memoryManager.registerObserver(this.componentName, observer);
  }

  /**
   * Safe setTimeout that automatically clears on destroy
   */
  protected safeSetTimeout(callback: () => void, delay: number): number {
    const timerId = window.setTimeout(() => {
      this.timers.delete(timerId);
      this.memoryManager.unregisterTimer(this.componentName, timerId);
      callback();
    }, delay);
    
    this.timers.add(timerId);
    this.memoryManager.registerTimer(this.componentName, timerId);
    
    return timerId;
  }

  /**
   * Safe setInterval that automatically clears on destroy
   */
  protected safeSetInterval(callback: () => void, delay: number): number {
    const intervalId = window.setInterval(callback, delay);
    
    this.intervals.add(intervalId);
    this.memoryManager.registerTimer(this.componentName, intervalId);
    
    return intervalId;
  }

  /**
   * Safe event listener that automatically removes on destroy
   */
  protected safeAddEventListener(
    element: Element,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): void {
    element.addEventListener(event, handler, options);
    
    const listenerInfo = { element, event, handler };
    this.eventListeners.add(listenerInfo);
  }

  /**
   * Create a MutationObserver that automatically disconnects on destroy
   */
  protected createSafeMutationObserver(callback: MutationCallback): MutationObserver {
    const observer = new MutationObserver(callback);
    this.trackObserver(observer);
    return observer;
  }

  /**
   * Create a PerformanceObserver that automatically disconnects on destroy
   */
  protected createSafePerformanceObserver(callback: PerformanceObserverCallback): PerformanceObserver {
    const observer = new PerformanceObserver(callback);
    this.trackObserver(observer);
    return observer;
  }

  /**
   * Create an IntersectionObserver that automatically disconnects on destroy
   */
  protected createSafeIntersectionObserver(
    callback: IntersectionObserverCallback,
    options?: IntersectionObserverInit
  ): IntersectionObserver {
    const observer = new IntersectionObserver(callback, options);
    this.trackObserver(observer);
    return observer;
  }

  /**
   * Create a ResizeObserver that automatically disconnects on destroy
   */
  protected createSafeResizeObserver(callback: ResizeObserverCallback): ResizeObserver {
    const observer = new ResizeObserver(callback);
    this.trackObserver(observer);
    return observer;
  }

  /**
   * Manually clear a timer
   */
  protected clearSafeTimeout(timerId: number): void {
    clearTimeout(timerId);
    this.timers.delete(timerId);
    this.memoryManager.unregisterTimer(this.componentName, timerId);
  }

  /**
   * Manually clear an interval
   */
  protected clearSafeInterval(intervalId: number): void {
    clearInterval(intervalId);
    this.intervals.delete(intervalId);
    this.memoryManager.unregisterTimer(this.componentName, intervalId);
  }

  /**
   * Manually remove an event listener
   */
  protected removeSafeEventListener(
    element: Element,
    event: string,
    handler: EventListener
  ): void {
    element.removeEventListener(event, handler);
    
    const listenerInfo = Array.from(this.eventListeners).find(
      l => l.element === element && l.event === event && l.handler === handler
    );
    
    if (listenerInfo) {
      this.eventListeners.delete(listenerInfo);
    }
  }

  /**
   * Get memory usage statistics for this component
   */
  protected getComponentMemoryStats(): {
    subscriptions: number;
    observers: number;
    timers: number;
    intervals: number;
    eventListeners: number;
  } {
    return {
      subscriptions: this.subscriptions.size,
      observers: this.observers.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      eventListeners: this.eventListeners.size
    };
  }

  /**
   * Log component memory usage
   */
  protected logMemoryUsage(): void {
    const stats = this.getComponentMemoryStats();
    console.log(`📊 ${this.componentName} Memory Usage:`, stats);
  }

  /**
   * Clean up all subscriptions
   */
  private cleanupSubscriptions(): void {
    this.subscriptions.forEach(subscription => {
      if (!subscription.closed) {
        subscription.unsubscribe();
      }
      this.memoryManager.unregisterSubscription(this.componentName, subscription);
    });
    this.subscriptions.clear();
  }

  /**
   * Clean up all observers
   */
  private cleanupObservers(): void {
    this.observers.forEach(observer => {
      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect();
      }
      this.memoryManager.unregisterObserver(this.componentName, observer);
    });
    this.observers.clear();
  }

  /**
   * Clean up all timers and intervals
   */
  private cleanupTimers(): void {
    this.timers.forEach(timerId => {
      clearTimeout(timerId);
      this.memoryManager.unregisterTimer(this.componentName, timerId);
    });
    this.timers.clear();

    this.intervals.forEach(intervalId => {
      clearInterval(intervalId);
      this.memoryManager.unregisterTimer(this.componentName, intervalId);
    });
    this.intervals.clear();
  }

  /**
   * Clean up all event listeners
   */
  private cleanupEventListeners(): void {
    this.eventListeners.forEach(({ element, event, handler }) => {
      try {
        element.removeEventListener(event, handler);
      } catch (error) {
        console.warn(`⚠️ ${this.componentName}: Failed to remove event listener:`, error);
      }
    });
    this.eventListeners.clear();
  }

  /**
   * Force cleanup (for emergency situations)
   */
  protected forceCleanup(): void {
    console.warn(`🚨 ${this.componentName}: Force cleanup initiated`);
    this.ngOnDestroy();
  }
}
