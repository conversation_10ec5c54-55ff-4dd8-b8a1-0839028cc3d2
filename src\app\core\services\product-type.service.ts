import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Product Type interfaces
export interface ProductType {
  id: string;
  name: string;
  code: string;
  description?: string;
  category: 'physical' | 'digital' | 'service' | 'subscription' | 'bundle' | 'other';
  specifications?: ProductSpecification[];
  pricing_model: 'fixed' | 'variable' | 'tiered' | 'usage_based' | 'subscription';
  base_price?: number;
  currency?: string;
  tax_category?: string;
  inventory_tracking: boolean;
  requires_approval: boolean;
  is_customizable: boolean;
  lead_time_days?: number;
  warranty_period_months?: number;
  support_level: 'basic' | 'standard' | 'premium' | 'enterprise';
  tags?: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  sub_product_types_count?: number;
}

export interface ProductSpecification {
  id: string;
  name: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'date' | 'file';
  description?: string;
  required: boolean;
  default_value?: any;
  options?: string[];
  validation_rules?: any;
  display_order: number;
}

// Backward compatibility interface for SubProductType
export interface SubProductType {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  product_type_id: string;
  form_schema?: any;
  default_values?: any;
  validation_rules?: any;
  ui_config?: any;
  created_at: string;
  updated_at: string;
}

export interface ProductTypeCreate {
  name: string;
  code: string;
  description?: string;
  category: 'physical' | 'digital' | 'service' | 'subscription' | 'bundle' | 'other';
  specifications?: Omit<ProductSpecification, 'id'>[];
  pricing_model: 'fixed' | 'variable' | 'tiered' | 'usage_based' | 'subscription';
  base_price?: number;
  currency?: string;
  tax_category?: string;
  inventory_tracking?: boolean;
  requires_approval?: boolean;
  is_customizable?: boolean;
  lead_time_days?: number;
  warranty_period_months?: number;
  support_level?: 'basic' | 'standard' | 'premium' | 'enterprise';
  tags?: string[];
  is_active?: boolean;
}

export interface ProductTypeUpdate {
  name?: string;
  code?: string;
  description?: string;
  category?: 'physical' | 'digital' | 'service' | 'subscription' | 'bundle' | 'other';
  specifications?: Omit<ProductSpecification, 'id'>[];
  pricing_model?: 'fixed' | 'variable' | 'tiered' | 'usage_based' | 'subscription';
  base_price?: number;
  currency?: string;
  tax_category?: string;
  inventory_tracking?: boolean;
  requires_approval?: boolean;
  is_customizable?: boolean;
  lead_time_days?: number;
  warranty_period_months?: number;
  support_level?: 'basic' | 'standard' | 'premium' | 'enterprise';
  tags?: string[];
  is_active?: boolean;
}

export interface ProductTypeStatistics {
  total_product_types: number;
  active_product_types: number;
  inactive_product_types: number;
  product_types_by_category: { [category: string]: number };
  product_types_by_pricing_model: { [model: string]: number };
  product_types_by_support_level: { [level: string]: number };
  popular_product_types: ProductType[];
  recent_product_types: ProductType[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ProductTypeService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/product-types/`;
  private productTypesSubject = new BehaviorSubject<ProductType[]>([]);
  public productTypes$ = this.productTypesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all product types with optional filtering and pagination (returns APIResponse)
   */
  getProductTypesWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    category?: string;
    pricing_model?: string;
    support_level?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<ProductType[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<ProductType[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.productTypesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all product types (backward compatibility) - returns ProductType[] directly
   */
  getProductTypes(skip: number = 0, limit: number = 10, filter: any = {}): Observable<ProductType[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search || filter.name,
      is_active: filter.is_active,
      category: filter.category,
      pricing_model: filter.pricing_model
    };

    return this.getProductTypesWithResponse(params).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  /**
   * Get product type by ID
   */
  getProductTypeById(id: string): Observable<APIResponse<ProductType>> {
    return this.http.get<APIResponse<ProductType>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all active product types for dropdown usage
   */
  getActiveProductTypes(): Observable<ProductType[]> {
    return this.getProductTypes(0, 1000, { is_active: true });
  }

  /**
   * Get product types without pagination (backward compatibility)
   */
  getProductTypesLegacy(filter: any = {}): Observable<ProductType[]> {
    return this.getProductTypes(0, 1000, filter);
  }

  /**
   * Create new product type
   */
  createProductType(productType: ProductTypeCreate): Observable<APIResponse<ProductType>> {
    return this.http.post<APIResponse<ProductType>>(this.baseUrl, productType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update product type
   */
  updateProductType(id: string, productType: ProductTypeUpdate): Observable<APIResponse<ProductType>> {
    return this.http.put<APIResponse<ProductType>>(`${this.baseUrl}${id}`, productType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete product type
   */
  deleteProductType(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted product type
   */
  restoreProductType(id: string): Observable<APIResponse<ProductType>> {
    return this.http.post<APIResponse<ProductType>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get product type statistics
   */
  getProductTypeStatistics(): Observable<APIResponse<ProductTypeStatistics>> {
    return this.http.get<APIResponse<ProductTypeStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload product types
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Get product types for dropdown (simplified data)
   */
  getProductTypesDropdown(): Observable<{ id: string; name: string; code: string; category: string }[]> {
    return this.getProductTypes(0, 1000, { is_active: true }).pipe(
      map(productTypes => {
        return productTypes.map(productType => ({
          id: productType.id,
          name: productType.name,
          code: productType.code,
          category: productType.category
        }));
      })
    );
  }

  /**
   * Search product types by name or code
   */
  searchProductTypes(query: string, limit: number = 20): Observable<ProductType[]> {
    return this.getProductTypes(0, limit, { search: query });
  }

  /**
   * Refresh product types data
   */
  refreshProductTypes(): void {
    this.getProductTypesWithResponse().subscribe();
  }

  /**
   * Clear product types cache
   */
  clearCache(): void {
    this.productTypesSubject.next([]);
  }

  /**
   * Get product categories
   */
  getProductCategories(): { value: string; label: string; description: string }[] {
    return [
      { value: 'physical', label: 'Physical Product', description: 'Tangible goods that can be shipped' },
      { value: 'digital', label: 'Digital Product', description: 'Software, downloads, or digital content' },
      { value: 'service', label: 'Service', description: 'Professional or consulting services' },
      { value: 'subscription', label: 'Subscription', description: 'Recurring subscription-based products' },
      { value: 'bundle', label: 'Bundle', description: 'Combination of multiple products or services' },
      { value: 'other', label: 'Other', description: 'Other product types' }
    ];
  }

  /**
   * Get pricing models
   */
  getPricingModels(): { value: string; label: string; description: string }[] {
    return [
      { value: 'fixed', label: 'Fixed Price', description: 'Single fixed price for the product' },
      { value: 'variable', label: 'Variable Price', description: 'Price varies based on specifications' },
      { value: 'tiered', label: 'Tiered Pricing', description: 'Different price tiers based on quantity or features' },
      { value: 'usage_based', label: 'Usage-Based', description: 'Price based on actual usage or consumption' },
      { value: 'subscription', label: 'Subscription', description: 'Recurring subscription pricing' }
    ];
  }

  /**
   * Get support levels
   */
  getSupportLevels(): { value: string; label: string; description: string }[] {
    return [
      { value: 'basic', label: 'Basic Support', description: 'Standard support with limited hours' },
      { value: 'standard', label: 'Standard Support', description: 'Business hours support with faster response' },
      { value: 'premium', label: 'Premium Support', description: '24/7 support with priority handling' },
      { value: 'enterprise', label: 'Enterprise Support', description: 'Dedicated support with SLA guarantees' }
    ];
  }

  /**
   * Get tax categories
   */
  getTaxCategories(): string[] {
    return [
      'Standard Rate',
      'Reduced Rate',
      'Zero Rate',
      'Exempt',
      'Digital Services',
      'Professional Services',
      'Educational',
      'Medical',
      'Food & Beverages',
      'Books & Publications'
    ];
  }

  /**
   * Get currencies
   */
  getCurrencies(): string[] {
    return [
      'USD',
      'EUR',
      'GBP',
      'INR',
      'CAD',
      'AUD',
      'SGD',
      'HKD',
      'JPY',
      'CHF',
      'AED'
    ];
  }

  /**
   * Get product category label
   */
  getProductCategoryLabel(category: string): string {
    const categories = this.getProductCategories();
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? categoryObj.label : category;
  }

  /**
   * Get pricing model label
   */
  getPricingModelLabel(model: string): string {
    const models = this.getPricingModels();
    const modelObj = models.find(m => m.value === model);
    return modelObj ? modelObj.label : model;
  }

  /**
   * Get support level label
   */
  getSupportLevelLabel(level: string): string {
    const levels = this.getSupportLevels();
    const levelObj = levels.find(l => l.value === level);
    return levelObj ? levelObj.label : level;
  }

  /**
   * Get support level badge class
   */
  getSupportLevelBadgeClass(level: string): string {
    const badgeClasses = {
      'basic': 'badge bg-secondary',
      'standard': 'badge bg-info',
      'premium': 'badge bg-warning',
      'enterprise': 'badge bg-success'
    };
    return badgeClasses[level as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    const badgeClasses = {
      'physical': 'badge bg-primary',
      'digital': 'badge bg-info',
      'service': 'badge bg-success',
      'subscription': 'badge bg-warning',
      'bundle': 'badge bg-secondary',
      'other': 'badge bg-dark'
    };
    return badgeClasses[category as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Format price with currency
   */
  formatPrice(price: number | undefined, currency: string = 'USD'): string {
    if (price === undefined || price === null) {
      return 'Not specified';
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price);
  }

  /**
   * Backward compatibility methods for SubProductType
   */

  /**
   * Get all sub product types with pagination (backward compatibility)
   */
  getSubProductTypes(skip: number = 0, limit: number = 10): Observable<SubProductType[]> {
    // This is a placeholder for backward compatibility
    // In a real implementation, this would call a separate sub-product-types endpoint
    console.warn('getSubProductTypes called - this is a backward compatibility method');
    return of([]);
  }

  /**
   * Get all sub product types without pagination (backward compatibility)
   */
  getSubProductTypesLegacy(): Observable<SubProductType[]> {
    return this.getSubProductTypes(0, 500);
  }

  /**
   * Get active sub product types only (backward compatibility)
   */
  getActiveSubProductTypes(): Observable<SubProductType[]> {
    return this.getSubProductTypes().pipe(
      map(subTypes => subTypes.filter(subType => subType.is_active))
    );
  }

  /**
   * Get sub product types by product type ID (backward compatibility)
   */
  getSubProductTypesByProductTypeId(productTypeId: string): Observable<SubProductType[]> {
    // This is a placeholder for backward compatibility
    // In a real implementation, this would call the sub-product-types endpoint with filtering
    console.warn('getSubProductTypesByProductTypeId called - this is a backward compatibility method');
    return of([]);
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Product Type service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
