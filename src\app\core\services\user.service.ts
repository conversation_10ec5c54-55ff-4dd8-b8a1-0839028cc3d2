import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import {
  User,
  UserCreate,
  UserUpdate,
  UserFilter,
  UserResponse,
  PasswordResetRequest,
  UserAPIResponse
} from '../models/user.model';

// Keep APIResponse for backward compatibility
export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * User Management Service
 * Handles user-related operations including profile management,
 * soft/hard delete, restore, and password reset functionality.
 */
@Injectable({
  providedIn: 'root'
})
export class UserService {
  private baseUrl = `${environment.apiUrl}/api/v1/users`;

  constructor(private http: HttpClient) {}

  /**
   * Get all users with pagination and filtering
   * GET /api/v1/users
   * @param skip Number of records to skip (default: 0)
   * @param limit Maximum number of records to return (default: 100)
   * @param filters Optional filters for search and status
   * @returns Observable of users list with pagination
   */
  getAllUsers(skip: number = 0, limit: number = 100, filters?: UserFilter): Observable<UserResponse> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    if (filters) {
      if (filters.search) {
        params = params.set('search', filters.search);
      }
      if (filters.is_active !== undefined) {
        params = params.set('is_active', filters.is_active.toString());
      }
      if (filters.is_superuser !== undefined) {
        params = params.set('is_superuser', filters.is_superuser.toString());
      }
      if (filters.is_deleted !== undefined) {
        params = params.set('is_deleted', filters.is_deleted.toString());
      }
    }

    return this.http.get<APIResponse<UserResponse>>(`${this.baseUrl}`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          // Add computed full_name field to each user
          const users = response.data.items.map(user => ({
            ...user,
            full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim()
          }));
          return { ...response.data, items: users };
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('getAllUsers', { items: [], total: 0, page: 1, size: 0, pages: 0 } as UserResponse))
    );
  }

  /**
   * Get user by ID
   * GET /api/v1/users/{user_id}
   * @param userId User ID to retrieve
   * @returns Observable of user data
   */
  getUserById(userId: string): Observable<User> {
    return this.http.get<APIResponse<User>>(`${this.baseUrl}/${userId}`).pipe(
      map(response => {
        if (response.success && response.data) {
          // Add computed full_name field
          const user = {
            ...response.data,
            full_name: `${response.data.first_name || ''} ${response.data.last_name || ''}`.trim()
          };
          return user;
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('getUserById', null as any))
    );
  }

  /**
   * Create new user
   * POST /api/v1/users
   * @param userData User data for creation
   * @returns Observable of created user
   */
  createUser(userData: UserCreate): Observable<User> {
    return this.http.post<APIResponse<User>>(`${this.baseUrl}`, userData).pipe(
      map(response => {
        if (response.success && response.data) {
          // Add computed full_name field
          const user = {
            ...response.data,
            full_name: `${response.data.first_name || ''} ${response.data.last_name || ''}`.trim()
          };
          return user;
        }
        throw new Error('Failed to create user');
      }),
      catchError(this.handleError('createUser', null as any))
    );
  }

  /**
   * Update existing user
   * PUT /api/v1/users/{user_id}
   * @param userId User ID to update
   * @param userData Updated user data
   * @returns Observable of updated user
   */
  updateUser(userId: string, userData: UserUpdate): Observable<User> {
    return this.http.put<APIResponse<User>>(`${this.baseUrl}/${userId}`, userData).pipe(
      map(response => {
        if (response.success && response.data) {
          // Add computed full_name field
          const user = {
            ...response.data,
            full_name: `${response.data.first_name || ''} ${response.data.last_name || ''}`.trim()
          };
          return user;
        }
        throw new Error('Failed to update user');
      }),
      catchError(this.handleError('updateUser', null as any))
    );
  }

  /**
   * Get current user profile
   * GET /api/v1/users/me
   * @returns Observable of current user data
   */
  getCurrentUser(): Observable<User> {
    return this.http.get<APIResponse<User>>(`${this.baseUrl}/me`).pipe(
      map(response => {
        if (response.success && response.data) {
          // Add computed full_name field
          const user = {
            ...response.data,
            full_name: `${response.data.first_name || ''} ${response.data.last_name || ''}`.trim()
          };
          return user;
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('getCurrentUser', null as any))
    );
  }

  /**
   * Soft delete a user (admin only)
   * DELETE /api/v1/users/{user_id}/soft
   * @param userId User ID to soft delete
   * @returns Observable of operation result
   */
  softDeleteUser(userId: string): Observable<boolean> {
    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${userId}/soft`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to soft delete user');
      }),
      catchError(this.handleError('softDeleteUser', false))
    );
  }

  /**
   * Restore a soft-deleted user (admin only)
   * POST /api/v1/users/{user_id}/restore
   * @param userId User ID to restore
   * @returns Observable of restored user
   */
  restoreUser(userId: string): Observable<User> {
    return this.http.post<APIResponse<User>>(`${this.baseUrl}/${userId}/restore`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to restore user');
      }),
      catchError(this.handleError('restoreUser', null as any))
    );
  }

  /**
   * Get all soft-deleted users (admin only)
   * GET /api/v1/users/deleted
   * @param skip Number of records to skip (default: 0)
   * @param limit Maximum number of records to return (default: 100)
   * @returns Observable of deleted users list
   */
  getDeletedUsers(skip: number = 0, limit: number = 100): Observable<User[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    return this.http.get<APIResponse<User[]>>(`${this.baseUrl}/deleted`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getDeletedUsers', []))
    );
  }

  /**
   * Permanently delete a user (admin only)
   * DELETE /api/v1/users/{user_id}/hard
   * @param userId User ID to permanently delete
   * @returns Observable of operation result
   */
  hardDeleteUser(userId: string): Observable<boolean> {
    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${userId}/hard`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to hard delete user');
      }),
      catchError(this.handleError('hardDeleteUser', false))
    );
  }

  /**
   * Request a password reset
   * POST /api/v1/users/password-reset
   * @param email Email address for password reset
   * @returns Observable of operation result
   */
  requestPasswordReset(email: string): Observable<boolean> {
    const request: PasswordResetRequest = { email };

    return this.http.post<APIResponse<boolean>>(`${this.baseUrl}/password-reset`, request).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to request password reset');
      }),
      catchError(this.handleError('requestPasswordReset', false))
    );
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
