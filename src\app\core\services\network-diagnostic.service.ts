import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class NetworkDiagnosticService {

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Comprehensive network diagnostic for API connectivity issues
   */
  async runDiagnostics(employeeId?: string): Promise<void> {
    console.log('🔧 NETWORK DIAGNOSTICS STARTING...');
    console.log('='.repeat(60));

    // 1. Basic Environment Check
    this.checkEnvironmentConfig();

    // 2. Authentication Status
    this.checkAuthenticationStatus();

    // 3. Network Connectivity Test
    await this.testNetworkConnectivity();

    // 4. API Base URL Test
    await this.testApiBaseUrl();

    // 5. Specific Employee Endpoint Test
    if (employeeId) {
      await this.testEmployeeEndpoint(employeeId);
    }

    // 6. CORS Test
    await this.testCorsHeaders();

    console.log('='.repeat(60));
    console.log('🔧 NETWORK DIAGNOSTICS COMPLETED');
  }

  private checkEnvironmentConfig(): void {
    console.log('📋 1. ENVIRONMENT CONFIGURATION:');
    console.log('   API URL:', environment.apiUrl);
    console.log('   Production:', environment.production);
    console.log('   Full Employee URL:', `${environment.apiUrl}/api/v1/employees`);
  }

  private checkAuthenticationStatus(): void {
    console.log('\n🔐 2. AUTHENTICATION STATUS:');
    const currentUser = this.authService.currentUserValue;
    
    if (currentUser) {
      console.log('   ✅ User logged in:', currentUser.email);
      console.log('   ✅ Access token exists:', !!currentUser.access_token);
      console.log('   ✅ Token length:', currentUser.access_token?.length || 0);
      console.log('   ✅ Token expired:', this.authService.isTokenExpired());
      console.log('   ✅ User permissions:', currentUser.permissions?.length || 0, 'permissions');
    } else {
      console.log('   ❌ No user logged in');
    }
  }

  private async testNetworkConnectivity(): Promise<void> {
    console.log('\n🌐 3. NETWORK CONNECTIVITY TEST:');
    
    try {
      // Test basic internet connectivity
      const response = await fetch('https://httpbin.org/get', { 
        method: 'GET',
        mode: 'cors'
      });
      
      if (response.ok) {
        console.log('   ✅ Internet connectivity: OK');
      } else {
        console.log('   ⚠️ Internet connectivity: Limited');
      }
    } catch (error) {
      console.log('   ❌ Internet connectivity: Failed', error);
    }
  }

  private async testApiBaseUrl(): Promise<void> {
    console.log('\n🔗 4. API BASE URL TEST:');
    
    try {
      // Test if API server is reachable
      const response = await fetch(`${environment.apiUrl}/health`, {
        method: 'GET',
        mode: 'cors'
      });
      
      console.log('   API Health Check Status:', response.status);
      console.log('   API Health Check OK:', response.ok);
    } catch (error) {
      console.log('   ❌ API Base URL unreachable:', error);
    }
  }

  private async testEmployeeEndpoint(employeeId: string): Promise<void> {
    console.log('\n👤 5. EMPLOYEE ENDPOINT TEST:');
    const url = `${environment.apiUrl}/api/v1/employees/${employeeId}`;
    console.log('   Testing URL:', url);

    // Test without authentication first
    try {
      const response = await fetch(url, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'application/json'
        }
      });
      
      console.log('   Without Auth - Status:', response.status);
      console.log('   Without Auth - OK:', response.ok);
    } catch (error) {
      console.log('   ❌ Without Auth - Failed:', error);
    }

    // Test with authentication
    const currentUser = this.authService.currentUserValue;
    if (currentUser?.access_token) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          mode: 'cors',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${currentUser.access_token}`
          }
        });
        
        console.log('   With Auth - Status:', response.status);
        console.log('   With Auth - OK:', response.ok);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.log('   With Auth - Error Response:', errorText);
        }
      } catch (error) {
        console.log('   ❌ With Auth - Failed:', error);
      }
    }
  }

  private async testCorsHeaders(): Promise<void> {
    console.log('\n🔒 6. CORS HEADERS TEST:');
    
    try {
      const response = await fetch(`${environment.apiUrl}/api/v1/employees/`, {
        method: 'OPTIONS',
        mode: 'cors'
      });
      
      console.log('   CORS Preflight Status:', response.status);
      console.log('   CORS Headers:');
      response.headers.forEach((value, key) => {
        if (key.toLowerCase().includes('access-control')) {
          console.log(`     ${key}: ${value}`);
        }
      });
    } catch (error) {
      console.log('   ❌ CORS Test Failed:', error);
    }
  }

  /**
   * Test specific HTTP methods and headers
   */
  testHttpRequest(url: string, method: string = 'GET'): Observable<any> {
    console.log(`🧪 Testing ${method} request to:`, url);
    
    const headers = new HttpHeaders({
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    });

    return this.http.request(method, url, { headers }).pipe(
      timeout(10000), // 10 second timeout
      catchError((error: HttpErrorResponse) => {
        console.error(`❌ ${method} request failed:`, {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message,
          error: error.error
        });
        return of(null);
      })
    );
  }
}
