import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import {
  FundHouseService,
  FundHouse,
  FundHouseCreate,
  FundHouseUpdate
} from '../../../../../core/services/fund-house.service';
import { PopupDesignService } from '../../../../../core/services/popup-design.service';

@Component({
  selector: 'app-fund-house-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  templateUrl: './fund-house-form.component.html',
  styleUrls: ['./fund-house-form.component.scss']
})
export class FundHouseFormComponent implements OnInit {
  @Input() isEditMode = false;
  @Input() fundHouse: FundHouse | null = null;

  fundHouseForm!: FormGroup;
  saving = false;
  error: string | null = null;

  // Options
  countries: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private fb: FormBuilder,
    public fundHouseService: FundHouseService,
    private popupService: PopupDesignService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.loadOptions();
    this.initializeForm();
  }

  /**
   * Load dropdown options
   */
  loadOptions(): void {
    this.countries = this.fundHouseService.getCountryList();
    this.regulatoryBodies = this.fundHouseService.getRegulatoryBodies();
  }

  /**
   * Initialize the form with validation
   */
  private initializeForm(): void {
    this.fundHouseForm = this.fb.group({
      name: [
        this.fundHouse?.name || '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100)
        ]
      ],
      code: [
        this.fundHouse?.code || '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(10),
          Validators.pattern(/^[A-Z0-9]+$/)
        ]
      ],
      description: [
        this.fundHouse?.description || '',
        [Validators.maxLength(500)]
      ],
      website: [
        this.fundHouse?.website || '',
        [Validators.pattern(/^https?:\/\/.+/)]
      ],
      contact_email: [
        this.fundHouse?.contact_email || '',
        [Validators.email]
      ],
      contact_phone: [
        this.fundHouse?.contact_phone || '',
        [Validators.pattern(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/)]
      ],
      address: [
        this.fundHouse?.address || '',
        [Validators.maxLength(200)]
      ],
      city: [
        this.fundHouse?.city || '',
        [Validators.maxLength(50)]
      ],
      state: [
        this.fundHouse?.state || '',
        [Validators.maxLength(50)]
      ],
      country: [
        this.fundHouse?.country || '',
        []
      ],
      postal_code: [
        this.fundHouse?.postal_code || '',
        [Validators.maxLength(20)]
      ],
      established_date: [
        this.fundHouse?.established_date ? this.formatDateForInput(this.fundHouse.established_date) : '',
        []
      ],
      license_number: [
        this.fundHouse?.license_number || '',
        [Validators.maxLength(50)]
      ],
      regulatory_body: [
        this.fundHouse?.regulatory_body || '',
        []
      ],
      is_active: [
        this.fundHouse?.is_active ?? true,
        [Validators.required]
      ]
    });

    // Add custom validators
    this.fundHouseForm.get('code')?.addValidators(this.fundHouseCodeValidator.bind(this));
    this.fundHouseForm.get('website')?.addValidators(this.websiteValidator.bind(this));
  }

  /**
   * Custom validator for fund house code
   */
  private fundHouseCodeValidator(control: any) {
    if (!control.value) return null;

    if (!this.fundHouseService.validateFundHouseCode(control.value)) {
      return { invalidCode: { message: 'Code must be 3-10 uppercase alphanumeric characters' } };
    }

    return null;
  }

  /**
   * Custom validator for website URL
   */
  private websiteValidator(control: any) {
    if (!control.value) return null;

    if (!this.fundHouseService.validateWebsite(control.value)) {
      return { invalidWebsite: { message: 'Please enter a valid website URL' } };
    }

    return null;
  }

  /**
   * Format date for input field
   */
  private formatDateForInput(dateString: string): string {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get form control for template access
   */
  getFormControl(controlName: string) {
    return this.fundHouseForm.get(controlName);
  }

  /**
   * Check if form control has error
   */
  hasError(controlName: string, errorType?: string): boolean {
    const control = this.getFormControl(controlName);
    if (!control) return false;

    if (errorType) {
      return control.hasError(errorType) && (control.dirty || control.touched);
    }

    return control.invalid && (control.dirty || control.touched);
  }

  /**
   * Get error message for form control
   */
  getErrorMessage(controlName: string): string {
    const control = this.getFormControl(controlName);
    if (!control || !control.errors) return '';

    const errors = control.errors;

    if (errors['required']) {
      return `${this.getFieldLabel(controlName)} is required.`;
    }

    if (errors['minlength']) {
      return `${this.getFieldLabel(controlName)} must be at least ${errors['minlength'].requiredLength} characters.`;
    }

    if (errors['maxlength']) {
      return `${this.getFieldLabel(controlName)} cannot exceed ${errors['maxlength'].requiredLength} characters.`;
    }

    if (errors['pattern']) {
      if (controlName === 'code') {
        return 'Code must contain only uppercase letters and numbers.';
      }
      if (controlName === 'website') {
        return 'Website must start with http:// or https://';
      }
      if (controlName === 'contact_phone') {
        return 'Please enter a valid phone number.';
      }
      return `${this.getFieldLabel(controlName)} format is invalid.`;
    }

    if (errors['email']) {
      return 'Please enter a valid email address.';
    }

    if (errors['invalidCode']) {
      return errors['invalidCode'].message;
    }

    if (errors['invalidWebsite']) {
      return errors['invalidWebsite'].message;
    }

    return 'Invalid input.';
  }

  /**
   * Get field label for error messages
   */
  private getFieldLabel(controlName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Fund house name',
      code: 'Fund house code',
      description: 'Description',
      website: 'Website',
      contact_email: 'Contact email',
      contact_phone: 'Contact phone',
      address: 'Address',
      city: 'City',
      state: 'State',
      country: 'Country',
      postal_code: 'Postal code',
      established_date: 'Established date',
      license_number: 'License number',
      regulatory_body: 'Regulatory body',
      is_active: 'Status'
    };
    return labels[controlName] || controlName;
  }

  /**
   * Save fund house
   */
  save(): void {
    if (this.fundHouseForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.saving = true;
    this.error = null;

    const formValue = this.fundHouseForm.value;

    // Clean up form data
    const fundHouseData = {
      name: formValue.name.trim(),
      code: formValue.code.trim().toUpperCase(),
      description: formValue.description?.trim() || undefined,
      website: formValue.website?.trim() || undefined,
      contact_email: formValue.contact_email?.trim() || undefined,
      contact_phone: formValue.contact_phone?.trim() || undefined,
      address: formValue.address?.trim() || undefined,
      city: formValue.city?.trim() || undefined,
      state: formValue.state?.trim() || undefined,
      country: formValue.country || undefined,
      postal_code: formValue.postal_code?.trim() || undefined,
      established_date: formValue.established_date || undefined,
      license_number: formValue.license_number?.trim() || undefined,
      regulatory_body: formValue.regulatory_body || undefined,
      is_active: formValue.is_active
    };

    const operation = this.isEditMode
      ? this.fundHouseService.updateFundHouse(this.fundHouse!.id, fundHouseData as FundHouseUpdate)
      : this.fundHouseService.createFundHouse(fundHouseData as FundHouseCreate);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.activeModal.close('saved');
        } else {
          this.error = response.error || 'Failed to save fund house.';
          this.saving = false;
        }
      },
      error: (error) => {
        this.error = error.message;
        this.saving = false;

        this.popupService.showError({
          title: 'Save Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Mark all form controls as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.fundHouseForm.controls).forEach(key => {
      const control = this.fundHouseForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Reset form
   */
  reset(): void {
    this.fundHouseForm.reset();
    this.initializeForm();
    this.error = null;
  }

  /**
   * Cancel and close modal
   */
  cancel(): void {
    if (this.fundHouseForm.dirty) {
      this.popupService.showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        confirmText: 'Yes, Cancel',
        cancelText: 'Continue Editing'
      }).then((result) => {
        if (result.isConfirmed) {
          this.activeModal.dismiss('cancelled');
        }
      });
    } else {
      this.activeModal.dismiss('cancelled');
    }
  }

  /**
   * Get modal title
   */
  getModalTitle(): string {
    return this.isEditMode ? 'Edit Fund House' : 'Create New Fund House';
  }

  /**
   * Get save button text
   */
  getSaveButtonText(): string {
    if (this.saving) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update Fund House' : 'Create Fund House';
  }

  /**
   * Auto-generate code from name
   */
  generateCodeFromName(): void {
    const name = this.fundHouseForm.get('name')?.value;
    if (name && !this.fundHouseForm.get('code')?.value) {
      const code = name
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .substring(0, 6);

      this.fundHouseForm.get('code')?.setValue(code);
    }
  }

  /**
   * Validate and format website URL
   */
  formatWebsiteUrl(): void {
    const website = this.fundHouseForm.get('website')?.value;
    if (website && !website.startsWith('http')) {
      this.fundHouseForm.get('website')?.setValue(`https://${website}`);
    }
  }
}
