import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface AttendanceReportSearchParams {
  from_date: string;
  to_date: string;
  employee_id?: string;
  department?: string;
}

export interface AttendanceReportData {
  id: string;
  employee_id: string;
  employee_name: string;
  employee_code: string;
  attendance_date: string;
  check_in_time_12hr: string;
  check_out_time_12hr: string;
  working_hours: number;
  status: string; // present, absent, late, half_day
  department: string;
  overtime_hours?: number;
  is_holiday?: boolean;
  is_weekend?: boolean;
}

export interface AttendanceReportResponseData {
  total_count: number;
  items: AttendanceReportData[];
}

export interface AttendanceReportResponse {
  success: boolean;
  data: AttendanceReportResponseData;
  message: string;
  error?: any;
}

@Injectable({
  providedIn: 'root'
})
export class AttendanceReportService {
  private baseUrl = `${environment.apiUrl}/api/v1/attendance-report`;

  constructor(private http: HttpClient) { }

  /**
   * Search attendance reports based on criteria using POST /api/v1/attendance-report/search
   */
  searchAttendanceReports(params: AttendanceReportSearchParams): Observable<AttendanceReportResponse> {
    console.log('AttendanceReportService: Searching attendance reports with params:', params);

    // Ensure date format is YYYY-MM-DD
    const payload = {
      from_date: this.formatDateToYYYYMMDD(params.from_date),
      to_date: this.formatDateToYYYYMMDD(params.to_date),
      ...(params.employee_id && { employee_id: params.employee_id }),
      ...(params.department && { department: params.department })
    };

    const url = `${this.baseUrl}/search`;
    console.log('AttendanceReportService: API URL:', url);
    console.log('AttendanceReportService: Payload:', payload);

    return this.http.post<AttendanceReportResponse>(url, payload);
  }

  /**
   * Export attendance report data to CSV
   */
  exportToCSV(data: AttendanceReportData[], filename: string = 'attendance_report'): void {
    console.log('AttendanceReportService: Exporting to CSV:', { data, filename });

    if (!data || data.length === 0) {
      console.warn('AttendanceReportService: No data to export');
      return;
    }

    // Define CSV headers exactly as shown in the Excel picture
    const headers = [
      'EmpCode',
      'AttendanceDate',
      'CheckIn',
      'CheckOut',
      'CreatedDate',
      'WorkingHours'
    ];

    // Convert data to CSV format
    const csvData = data.map(record => [
      this.escapeCsvValue(record.employee_code || ''),
      this.formatDateForCSV(record.attendance_date),
      this.escapeCsvValue(record.check_in_time_12hr || ''),
      this.escapeCsvValue(record.check_out_time_12hr || ''),
      this.formatDateForCSV(record.attendance_date), // Using attendance_date as CreatedDate
      record.working_hours?.toString() || '0'
    ]);

    // Combine headers and data
    const csvContent = [headers, ...csvData]
      .map(row => row.join(','))
      .join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }

    console.log('AttendanceReportService: CSV export completed');
  }

  /**
   * Format date to YYYY-MM-DD format for API payload
   */
  private formatDateToYYYYMMDD(dateString: string): string {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn('AttendanceReportService: Invalid date format:', dateString);
      return dateString;
    }
  }

  /**
   * Format date for CSV export
   */
  private formatDateForCSV(dateString: string): string {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('AttendanceReportService: Invalid date format:', dateString);
      return dateString;
    }
  }

  /**
   * Escape CSV values to handle commas, quotes, and newlines
   */
  private escapeCsvValue(value: string): string {
    if (!value) return '';

    // If value contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }

    return value;
  }

  /**
   * Format status for display
   */
  private formatStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'present': 'Present',
      'absent': 'Absent',
      'late': 'Late',
      'half_day': 'Half Day',
      'on_leave': 'On Leave'
    };

    return statusMap[status] || status;
  }

  /**
   * Get attendance summary for a date range
   */
  getAttendanceSummary(fromDate: string, toDate: string): Observable<any> {
    const url = `${this.baseUrl}/summary`;
    const payload = {
      from_date: this.formatDateToYYYYMMDD(fromDate),
      to_date: this.formatDateToYYYYMMDD(toDate)
    };

    return this.http.post<any>(url, payload);
  }

  /**
   * Download attendance report as Excel file (if API supports it)
   */
  downloadAttendanceReportExcel(params: AttendanceReportSearchParams): Observable<Blob> {
    const url = `${this.baseUrl}/export`;
    const payload = {
      from_date: this.formatDateToYYYYMMDD(params.from_date),
      to_date: this.formatDateToYYYYMMDD(params.to_date),
      ...(params.employee_id && { employee_id: params.employee_id }),
      ...(params.department && { department: params.department })
    };

    return this.http.post(url, payload, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    });
  }
}
