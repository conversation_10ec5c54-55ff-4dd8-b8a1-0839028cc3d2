<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Other Business Details</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #otherBusinessForm="ngForm">
    <div class="row mb-3">
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="promoterName" class="form-label">Promoter's Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="promoterName" name="promoterName" [(ngModel)]="formData.promoterName" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="companyName" class="form-label">Company Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="companyName" name="companyName" [(ngModel)]="formData.companyName" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="constitution" class="form-label">Constitution <span class="text-danger">*</span></label>
        <select class="form-select" id="constitution" name="constitution" [(ngModel)]="formData.constitution" required>
          <option value="">Select Constitution</option>
          <option value="Proprietorship">Proprietorship</option>
          <option value="Partnership">Partnership</option>
          <option value="LLP">LLP</option>
          <option value="Private Limited">Private Limited</option>
          <option value="Public Limited">Public Limited</option>
          <option value="HUF">HUF</option>
          <option value="Trust">Trust</option>
        </select>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="dateOfIncorporation" class="form-label">Date of Incorporation <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="dateOfIncorporation" name="dateOfIncorporation" [(ngModel)]="formData.dateOfIncorporation" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="pan" class="form-label">PAN <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="pan" name="pan" [(ngModel)]="formData.pan" required maxlength="10" placeholder="**********">
      </div>
      <div class="col-12 col-md-6 col-lg-4 ">
        <label for="cinGstNo" class="form-label">CIN / GST No</label>
        <input type="text" class="form-control" id="cinGstNo" name="cinGstNo" [(ngModel)]="formData.cinGstNo">
      </div>
      <div class="col-12 col-md-6 col-lg-4">
        <label for="location" class="form-label">Location / Brief Address <span class="text-danger">*</span></label>
        <textarea class="form-control" id="location" name="location" [(ngModel)]="formData.location" rows="2" required></textarea>
      </div>
      <div class="col-12 col-md-6 col-lg-4">
        <label for="lineOfActivity" class="form-label">Line of Activity <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="lineOfActivity" name="lineOfActivity" [(ngModel)]="formData.lineOfActivity" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4">
        <label for="yearsOfExperience" class="form-label">Years of Experience <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="yearsOfExperience" name="yearsOfExperience" [(ngModel)]="formData.yearsOfExperience" required min="0">
      </div>
      <div class="col-12 col-md-6 col-lg-4">
        <label for="avgAnnualTurnover" class="form-label">Avg. Annual Turnover (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="avgAnnualTurnover" name="avgAnnualTurnover" [(ngModel)]="formData.avgAnnualTurnover" required min="0">
      </div>
      <div class="col-12 col-md-6 col-lg-4">
        <label for="avgAnnualProfit" class="form-label">Avg. Annual Profit (%) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="avgAnnualProfit" name="avgAnnualProfit" [(ngModel)]="formData.avgAnnualProfit" required min="0" step="0.1">
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-primary" [disabled]="otherBusinessForm.invalid" (click)="saveChanges()">
    <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save
  </button>
</div>
