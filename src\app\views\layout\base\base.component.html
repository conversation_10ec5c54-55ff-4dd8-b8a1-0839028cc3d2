<div class="main-wrapper">

  <!-- partial:sidebar -->
  <app-sidebar />
  <!-- partial -->

  <div class="page-wrapper">

    <!-- partial:navbar -->
    <app-navbar></app-navbar>
    <!-- partial -->

    <div class="page-content container-xxl">

      <!-- Spinner for lazyload modules -->
       @if (isLoading) {
         <div class="spinner-wrapper">
           <div class="spinner">Loading...</div>
         </div>
       } @else {
         <div>
           <router-outlet></router-outlet>
         </div>
       }


    </div>

    <!-- partial:footer -->
    <app-footer></app-footer>
    <!-- partial -->

  </div>


</div>