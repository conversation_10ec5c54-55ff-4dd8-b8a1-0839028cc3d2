import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SubProductType } from '../../../../../core/services/sub-product-type.service';

@Component({
  selector: 'app-sub-product-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-layers me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Sub Product Type
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Sub Product Type Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ subProductType?.name }}</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced form will include comprehensive sub product type management with:
        <ul class="mt-2 mb-0">
          <li><strong>Dynamic Form Schema Builder</strong> - Visual form designer with drag-and-drop fields</li>
          <li><strong>Validation Rules Engine</strong> - Custom validation rules and business logic</li>
          <li><strong>UI Configuration</strong> - Layout options, themes, and custom styling</li>
          <li><strong>Pricing Overrides</strong> - Custom pricing models and calculation formulas</li>
          <li><strong>Availability Rules</strong> - Date ranges, quantity limits, and conditional availability</li>
          <li><strong>Dependencies Management</strong> - Inter-product dependencies and requirements</li>
          <li><strong>Form Preview</strong> - Real-time preview of the generated form</li>
          <li><strong>JSON Schema Export</strong> - Export form schemas for external use</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-code me-2"></i>
        <strong>Advanced Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Conditional field logic (show/hide based on other fields)</li>
          <li>Multi-step form wizard support</li>
          <li>File upload with validation and preview</li>
          <li>Rich text editor integration</li>
          <li>Date/time pickers with timezone support</li>
          <li>Geolocation and mapping fields</li>
          <li>Integration with external APIs for data validation</li>
        </ul>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
  `]
})
export class SubProductTypeFormComponent {
  @Input() isEditMode = false;
  @Input() subProductType: SubProductType | null = null;

  subProductTypeForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.subProductTypeForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      category: ['', [Validators.required]],
      product_type_id: ['', [Validators.required]]
    });
  }
}
