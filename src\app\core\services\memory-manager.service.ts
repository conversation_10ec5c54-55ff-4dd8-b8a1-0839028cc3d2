import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';

export interface MemoryUsage {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

export interface MemoryLeak {
  type: 'subscription' | 'observer' | 'timer' | 'listener' | 'reference';
  component: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected: number;
}

export interface MemoryStats {
  currentUsage: MemoryUsage;
  peakUsage: MemoryUsage;
  averageUsage: number;
  memoryLeaks: MemoryLeak[];
  recommendations: string[];
}

/**
 * Memory Manager Service
 * 
 * Provides comprehensive memory management, leak detection,
 * and optimization for Angular applications.
 */
@Injectable({
  providedIn: 'root'
})
export class MemoryManagerService implements OnDestroy {
  private memoryUsageSubject = new BehaviorSubject<MemoryUsage | null>(null);
  private memoryLeaksSubject = new BehaviorSubject<MemoryLeak[]>([]);
  
  public memoryUsage$ = this.memoryUsageSubject.asObservable();
  public memoryLeaks$ = this.memoryLeaksSubject.asObservable();

  private monitoringInterval?: number;
  private memoryHistory: MemoryUsage[] = [];
  private detectedLeaks: MemoryLeak[] = [];
  private peakMemoryUsage: MemoryUsage | null = null;
  
  // Track active subscriptions and observers
  private activeSubscriptions = new Map<string, Set<Subscription>>();
  private activeObservers = new Map<string, Set<any>>();
  private activeTimers = new Map<string, Set<number>>();
  private activeListeners = new Map<string, Set<{ element: Element; event: string; handler: Function }>>();

  constructor() {
    console.log('🧠 MemoryManagerService: Initialized for memory management');
    this.startMemoryMonitoring();
    this.setupMemoryLeakDetection();
  }

  ngOnDestroy(): void {
    this.stopMemoryMonitoring();
    this.cleanup();
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    if (!this.isMemoryAPIAvailable()) {
      console.warn('⚠️ Memory API not available - monitoring disabled');
      return;
    }

    this.monitoringInterval = window.setInterval(() => {
      this.collectMemoryUsage();
    }, 5000); // Monitor every 5 seconds

    console.log('📊 MemoryManagerService: Memory monitoring started');
  }

  /**
   * Stop memory monitoring
   */
  private stopMemoryMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  /**
   * Collect current memory usage
   */
  private collectMemoryUsage(): void {
    if (!this.isMemoryAPIAvailable()) return;

    const memory = (performance as any).memory;
    const usage: MemoryUsage = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      timestamp: Date.now()
    };

    // Update peak usage
    if (!this.peakMemoryUsage || usage.usedJSHeapSize > this.peakMemoryUsage.usedJSHeapSize) {
      this.peakMemoryUsage = usage;
    }

    // Store in history (keep last 100 entries)
    this.memoryHistory.push(usage);
    if (this.memoryHistory.length > 100) {
      this.memoryHistory.shift();
    }

    this.memoryUsageSubject.next(usage);

    // Check for memory issues
    this.checkMemoryIssues(usage);
  }

  /**
   * Check for memory issues
   */
  private checkMemoryIssues(usage: MemoryUsage): void {
    const usagePercentage = (usage.usedJSHeapSize / usage.jsHeapSizeLimit) * 100;

    // High memory usage warning
    if (usagePercentage > 80) {
      this.reportMemoryLeak({
        type: 'reference',
        component: 'Global',
        description: `High memory usage: ${usagePercentage.toFixed(1)}%`,
        severity: usagePercentage > 90 ? 'critical' : 'high',
        detected: Date.now()
      });
    }

    // Memory growth detection
    if (this.memoryHistory.length >= 10) {
      const recentUsage = this.memoryHistory.slice(-10);
      const growthRate = this.calculateMemoryGrowthRate(recentUsage);
      
      if (growthRate > 1024 * 1024) { // 1MB growth per measurement
        this.reportMemoryLeak({
          type: 'reference',
          component: 'Global',
          description: `Rapid memory growth detected: ${this.formatBytes(growthRate)}/measurement`,
          severity: 'high',
          detected: Date.now()
        });
      }
    }
  }

  /**
   * Calculate memory growth rate
   */
  private calculateMemoryGrowthRate(usage: MemoryUsage[]): number {
    if (usage.length < 2) return 0;
    
    const first = usage[0].usedJSHeapSize;
    const last = usage[usage.length - 1].usedJSHeapSize;
    
    return (last - first) / usage.length;
  }

  /**
   * Setup memory leak detection
   */
  private setupMemoryLeakDetection(): void {
    // Monitor for common leak patterns
    this.detectUnsubscribedObservables();
    this.detectUnclearedTimers();
    this.detectUnremovedListeners();
    
    console.log('🔍 MemoryManagerService: Memory leak detection enabled');
  }

  /**
   * Register a subscription for tracking
   */
  registerSubscription(componentName: string, subscription: Subscription): void {
    if (!this.activeSubscriptions.has(componentName)) {
      this.activeSubscriptions.set(componentName, new Set());
    }
    this.activeSubscriptions.get(componentName)!.add(subscription);
  }

  /**
   * Unregister a subscription
   */
  unregisterSubscription(componentName: string, subscription: Subscription): void {
    const subscriptions = this.activeSubscriptions.get(componentName);
    if (subscriptions) {
      subscriptions.delete(subscription);
      if (subscriptions.size === 0) {
        this.activeSubscriptions.delete(componentName);
      }
    }
  }

  /**
   * Register an observer for tracking
   */
  registerObserver(componentName: string, observer: any): void {
    if (!this.activeObservers.has(componentName)) {
      this.activeObservers.set(componentName, new Set());
    }
    this.activeObservers.get(componentName)!.add(observer);
  }

  /**
   * Unregister an observer
   */
  unregisterObserver(componentName: string, observer: any): void {
    const observers = this.activeObservers.get(componentName);
    if (observers) {
      observers.delete(observer);
      if (observers.size === 0) {
        this.activeObservers.delete(componentName);
      }
    }
  }

  /**
   * Register a timer for tracking
   */
  registerTimer(componentName: string, timerId: number): void {
    if (!this.activeTimers.has(componentName)) {
      this.activeTimers.set(componentName, new Set());
    }
    this.activeTimers.get(componentName)!.add(timerId);
  }

  /**
   * Unregister a timer
   */
  unregisterTimer(componentName: string, timerId: number): void {
    const timers = this.activeTimers.get(componentName);
    if (timers) {
      timers.delete(timerId);
      if (timers.size === 0) {
        this.activeTimers.delete(componentName);
      }
    }
  }

  /**
   * Force garbage collection (if available)
   */
  forceGarbageCollection(): void {
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
      console.log('🗑️ MemoryManagerService: Forced garbage collection');
    } else {
      console.warn('⚠️ Garbage collection not available');
    }
  }

  /**
   * Get memory statistics
   */
  getMemoryStats(): Observable<MemoryStats> {
    return new Observable(observer => {
      const currentUsage = this.memoryUsageSubject.value;
      const averageUsage = this.calculateAverageMemoryUsage();
      
      const stats: MemoryStats = {
        currentUsage: currentUsage || this.getEmptyMemoryUsage(),
        peakUsage: this.peakMemoryUsage || this.getEmptyMemoryUsage(),
        averageUsage,
        memoryLeaks: [...this.detectedLeaks],
        recommendations: this.generateMemoryRecommendations()
      };

      observer.next(stats);
      observer.complete();
    });
  }

  /**
   * Report a memory leak
   */
  private reportMemoryLeak(leak: MemoryLeak): void {
    // Avoid duplicate reports
    const existing = this.detectedLeaks.find(l => 
      l.type === leak.type && 
      l.component === leak.component && 
      l.description === leak.description
    );

    if (!existing) {
      this.detectedLeaks.push(leak);
      this.memoryLeaksSubject.next([...this.detectedLeaks]);
      
      console.warn(`🚨 Memory leak detected in ${leak.component}: ${leak.description}`);
    }
  }

  /**
   * Detect unsubscribed observables
   */
  private detectUnsubscribedObservables(): void {
    // This would require more sophisticated tracking
    // For now, we'll check for components with many active subscriptions
    setInterval(() => {
      this.activeSubscriptions.forEach((subscriptions, componentName) => {
        if (subscriptions.size > 10) {
          this.reportMemoryLeak({
            type: 'subscription',
            component: componentName,
            description: `${subscriptions.size} active subscriptions detected`,
            severity: subscriptions.size > 20 ? 'high' : 'medium',
            detected: Date.now()
          });
        }
      });
    }, 30000); // Check every 30 seconds
  }

  /**
   * Detect uncleared timers
   */
  private detectUnclearedTimers(): void {
    setInterval(() => {
      this.activeTimers.forEach((timers, componentName) => {
        if (timers.size > 5) {
          this.reportMemoryLeak({
            type: 'timer',
            component: componentName,
            description: `${timers.size} active timers detected`,
            severity: timers.size > 10 ? 'high' : 'medium',
            detected: Date.now()
          });
        }
      });
    }, 30000);
  }

  /**
   * Detect unremoved listeners
   */
  private detectUnremovedListeners(): void {
    setInterval(() => {
      this.activeListeners.forEach((listeners, componentName) => {
        if (listeners.size > 10) {
          this.reportMemoryLeak({
            type: 'listener',
            component: componentName,
            description: `${listeners.size} active event listeners detected`,
            severity: listeners.size > 20 ? 'high' : 'medium',
            detected: Date.now()
          });
        }
      });
    }, 30000);
  }

  /**
   * Generate memory optimization recommendations
   */
  private generateMemoryRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.activeSubscriptions.size > 0) {
      recommendations.push('Ensure all subscriptions are properly unsubscribed in ngOnDestroy');
    }
    
    if (this.activeObservers.size > 0) {
      recommendations.push('Disconnect observers (MutationObserver, PerformanceObserver) when components are destroyed');
    }
    
    if (this.activeTimers.size > 0) {
      recommendations.push('Clear all timers and intervals in component cleanup');
    }
    
    if (this.detectedLeaks.length > 0) {
      recommendations.push('Address detected memory leaks to improve performance');
    }

    return recommendations;
  }

  /**
   * Utility methods
   */
  private isMemoryAPIAvailable(): boolean {
    return 'performance' in window && 'memory' in (performance as any);
  }

  private calculateAverageMemoryUsage(): number {
    if (this.memoryHistory.length === 0) return 0;
    
    const total = this.memoryHistory.reduce((sum, usage) => sum + usage.usedJSHeapSize, 0);
    return total / this.memoryHistory.length;
  }

  private getEmptyMemoryUsage(): MemoryUsage {
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      timestamp: Date.now()
    };
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private cleanup(): void {
    this.activeSubscriptions.clear();
    this.activeObservers.clear();
    this.activeTimers.clear();
    this.activeListeners.clear();
    this.detectedLeaks.length = 0;
    this.memoryHistory.length = 0;
  }
}
