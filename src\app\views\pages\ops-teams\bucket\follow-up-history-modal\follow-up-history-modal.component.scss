.modal-title {
  color: #3F828B;
  font-weight: 500;
}

.document-info {
  h6 {
    color: #3F828B;
    font-weight: 500;
  }

  .document-name {
    font-size: 0.95rem;
    color: #333;
    background-color: #f8f9fa;
    border-left: 3px solid #3F828B !important;
    word-break: break-word;
  }
}

.follow-up-history-container {
  min-height: 200px;
}

.table {
  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #3F828B;
  }

  td {
    vertical-align: middle;
  }
}

// Pagination styling
:host ::ng-deep ngb-pagination {
  .page-item {
    &.active .page-link {
      background-color: #3F828B;
      border-color: #3F828B;
    }

    .page-link {
      color: #3F828B;

      &:focus {
        box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
      }

      &:hover {
        color: darken(#3F828B, 10%);
      }
    }
  }
}
