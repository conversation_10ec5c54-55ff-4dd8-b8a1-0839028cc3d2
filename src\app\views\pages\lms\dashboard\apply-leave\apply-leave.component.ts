import { Component, ViewChild, TemplateRef, OnInit, OnDestroy, ChangeDetectorRef, inject, Injectable } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbModal, NgbModalRef, NgbModalModule, NgbTooltipModule, NgbDatepickerModule, NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, ColumnMode, SortType } from '@swimlane/ngx-datatable';
import { map, catchError, forkJoin, of, Subscription, timeout, tap, shareReplay } from 'rxjs';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';
import { HolidayService, Holiday as NewHoliday } from '../../../../../core/services/holiday.service';
import {
  LeaveService,
  LeaveType,
  LeaveBalance,
  LeaveBalanceSummary,
  Leave,
  LeaveCreate
} from '../../../../../core/services/leave.service';
import { EmployeeService } from '../../../../../core/services/employee.service';
import { EmployeeCacheService, CachedEmployee } from '../../../../../core/services/employee-cache.service';
import { AuthService } from '../../../../../core/services/auth.service';
import { HttpErrorHandlerService } from '../../../../../core/services/http-error-handler.service';
import { CalendarService, Holiday as CalendarHoliday } from '../../../../../core/services/calendar.service';
import { environment } from '../../../../../../environments/environment';
import Swal from 'sweetalert2';

// Custom date formatter for DD-MM-YYYY format
@Injectable()
export class CustomDateParserFormatter extends NgbDateParserFormatter {
  readonly DELIMITER = '-';

  parse(value: string): NgbDate | null {
    if (value) {
      const date = value.split(this.DELIMITER);
      if (date.length === 3) {
        // Parse DD-MM-YYYY format
        const day = parseInt(date[0], 10);
        const month = parseInt(date[1], 10);
        const year = parseInt(date[2], 10);

        if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
          return new NgbDate(year, month, day);
        }
      }
    }
    return null;
  }

  format(date: NgbDate | null): string {
    if (date) {
      // Format as DD-MM-YYYY
      const day = String(date.day).padStart(2, '0');
      const month = String(date.month).padStart(2, '0');
      const year = String(date.year);
      return `${day}${this.DELIMITER}${month}${this.DELIMITER}${year}`;
    }
    return '';
  }
}

// Interface for display purposes
export interface LeaveBalanceDisplay {
  leaveType: string;
  leaveFullName: string;
  totalAllowed: number;
  taken: number;
  remaining: number;
  leaveTypeId: number;
}



// Interface for leave application table
export interface LeaveApplicationDisplay {
  id: string;
  employeeCode: string;
  employeeName: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  totalLeaves: number;
  reason: string;
  status: 'Approved' | 'Pending' | 'Rejected' | 'Cancelled';
  approvedDate?: string;
  approverName?: string;
}


@Component({
  selector: 'app-apply-leave',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgbModalModule,
    NgbTooltipModule,
    NgbDatepickerModule,
    NgxDatatableModule,
    BreadcrumbComponent
  ],
  providers: [
    { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter }
  ],
  templateUrl: './apply-leave.component.html',
  styleUrl: './apply-leave.component.scss'
})
export class ApplyLeaveComponent implements OnInit, OnDestroy {
  @ViewChild('leaveModal') leaveModal!: TemplateRef<any>;

  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'Apply Leave' }
  ];

  // Subscription for leave data reload events
  private reloadSubscription: Subscription = new Subscription();

  // Make environment available to template
  environment = environment;

  // Properties for the leave application
  selectedLeaveTypeId: number = 0;
  selectedLeaveType: string = '';
  selectedLeaveFullName: string = '';
  fromDate: string = '';
  toDate: string = '';
  reason: string = '';
  dayCount: number = 0;

  // Datepicker properties
  calendar = inject(NgbCalendar);
  formatter = inject(NgbDateParserFormatter);
  fromDatePicker: NgbDate | null = null;
  toDatePicker: NgbDate | null = null;

  // Holiday data for date filtering
  holidayDates: string[] = [];

  // API data
  leaveTypes: LeaveType[] = [];
  leaveBalances: LeaveBalanceDisplay[] = [];
  leaveBalanceSummary: LeaveBalanceSummary | null = null;
  myLeaves: Leave[] = [];
  loading: boolean = false;
  error: string | null = null;
  submitting: boolean = false;
  currentEmployeeUuid: string | null = null;

  // Modal reference
  private modalRef: NgbModalRef | null = null;

  // Debug properties
  debugMode: boolean = true;
  apiStatus: {[key: string]: boolean} = {};

  // Holiday properties
  holidays: NewHoliday[] = [];
  holidaysLoaded: boolean = false;

  // Caching properties for optimization
  private masterDataCache: {
    leaveTypes?: LeaveType[];
    holidays?: NewHoliday[];
    currentEmployee?: CachedEmployee | null;
    cacheTimestamp?: number;
  } = {};
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  // Holiday restriction state (reactive property for template) - applies to ALL leave types
  hasHolidayRestriction: boolean = false;

  // Popup control - prevent multiple popups and add debounce
  private isPopupShowing: boolean = false;
  private validationTimeout: any = null;

  // Table properties
  leaveApplications: LeaveApplicationDisplay[] = [];
  filteredApplications: LeaveApplicationDisplay[] = [];
  paginatedApplications: LeaveApplicationDisplay[] = [];
  searchTerm: string = '';
  statusFilter: string = 'all'; // Default to show all leaves
  pageSize: number = 5;
  currentPage: number = 1;

  // NGX Datatable properties
  ColumnMode = ColumnMode;
  SortType = SortType;

  constructor(
    private modalService: NgbModal,
    private leaveService: LeaveService,
    private employeeService: EmployeeService,
    private employeeCacheService: EmployeeCacheService,
    public authService: AuthService,  // Make public for template access
    private httpErrorHandler: HttpErrorHandlerService,
    private calendarService: CalendarService,
    private holidayService: HolidayService,
    private cdr: ChangeDetectorRef
  ) {
    // Ensure leaveTypes is always initialized as an array
    this.leaveTypes = [];
  }

  ngOnInit(): void {
    console.log('🚀 ApplyLeaveComponent initialized with optimized loading');
    console.log('Environment API URL:', (window as any).environment?.apiUrl || 'Not available');

    // Clear any existing errors on component initialization
    this.clearErrors();

    // Load holidays for datepicker filtering
    this.loadHolidaysForDatepicker();

    // Load all data in parallel for optimal performance
    this.loadAllDataOptimized();

    // Subscribe to leave data reload events
    this.reloadSubscription.add(
      this.leaveService.reloadLeaveData$.subscribe(() => {
        console.log('🔄 Received reload event in ApplyLeaveComponent, reloading all leave data...');
        this.clearErrors(); // Clear errors before reloading
        this.loadAllDataOptimized();
      })
    );
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.reloadSubscription.unsubscribe();

    // Clear any pending validation timeout
    this.clearValidationTimeout();

    // Reset popup flag
    this.isPopupShowing = false;
  }

  // Method to clear all error states
  clearErrors(): void {
    this.error = null;
    this.submitting = false;
    console.log('🧹 Cleared all error states');
  }

  // Validate UUID format
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  // Debug method to check employee UUID status
  debugEmployeeUUID(): void {
    console.log('=== EMPLOYEE UUID DEBUG ===');
    console.log('currentEmployeeUuid:', this.currentEmployeeUuid);
    console.log('currentEmployeeUuid type:', typeof this.currentEmployeeUuid);
    console.log('currentEmployeeUuid is valid UUID:', this.currentEmployeeUuid ? this.isValidUUID(this.currentEmployeeUuid) : false);

    const currentUser = this.authService.currentUserValue;
    console.log('Current user ID:', currentUser?.id);
    console.log('Current user ID type:', typeof currentUser?.id);
    console.log('Current user ID is valid UUID:', currentUser?.id ? this.isValidUUID(currentUser.id) : false);
    console.log('Current user email:', currentUser?.email);
    console.log('Current user employee_code:', (currentUser as any)?.employee_code);
    console.log('============================');

    // Show alert with the information
    alert(`Employee UUID Debug:

Current Employee UUID: ${this.currentEmployeeUuid}
Type: ${typeof this.currentEmployeeUuid}
Is Valid UUID: ${this.currentEmployeeUuid ? this.isValidUUID(this.currentEmployeeUuid) : false}

Current User ID: ${currentUser?.id}
User ID Type: ${typeof currentUser?.id}
User ID Is Valid UUID: ${currentUser?.id ? this.isValidUUID(currentUser.id) : false}

Check console for full details.`);
  }

  // Check if modal is currently open
  isModalOpen(): boolean {
    return this.modalRef !== null;
  }

  // Load holidays from the calendar API
  loadHolidays(): void {
    console.log('🗓️ Loading holidays from calendar API...');

    this.holidayService.getHolidays().subscribe({
      next: (holidays) => {
        this.holidays = holidays;
        this.holidaysLoaded = true;
        console.log(`✅ Loaded ${holidays.length} holidays:`, holidays);

        // Log holiday dates for debugging
        if (holidays.length > 0) {
          console.log('📅 Holiday dates:', holidays.map(h => h.date));
        }
      },
      error: (error) => {
        console.error('❌ Error loading holidays:', error);
        this.holidays = [];
        this.holidaysLoaded = false;
        // Don't show error to user as holidays are optional
      }
    });
  }

  // OPTIMIZED: Get current employee UUID using cache service
  getCurrentEmployeeUuid(): void {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('No current user found');
      return;
    }

    console.log('Getting employee UUID for user using cache:', currentUser);

    // Use cached employee service for efficient lookup
    this.employeeCacheService.getCurrentEmployee(
      currentUser.email,
      (currentUser as any)?.employee_code
    ).subscribe({
      next: (currentEmployee) => {
        if (currentEmployee) {
          this.currentEmployeeUuid = currentEmployee.id;
          console.log('✅ Found current employee UUID from cache:', this.currentEmployeeUuid);
          console.log('Employee details from cache:', currentEmployee);

          // Validate UUID format
          if (this.currentEmployeeUuid && !this.isValidUUID(this.currentEmployeeUuid)) {
            console.warn('⚠️ Employee ID is not a valid UUID format:', this.currentEmployeeUuid);
            const userUuid = currentUser.id;
            if (userUuid && this.isValidUUID(userUuid)) {
              console.log('🔄 Using user UUID instead:', userUuid);
              this.currentEmployeeUuid = userUuid;
            }
          }
        } else {
          console.warn('⚠️ Could not find current employee in cache');
          // Fallback to user UUID
          const userUuid = currentUser.id;
          if (userUuid && this.isValidUUID(userUuid)) {
            console.log('🔄 Using current user UUID as fallback:', userUuid);
            this.currentEmployeeUuid = userUuid;
          } else {
            console.error('❌ Current user ID is not a valid UUID:', userUuid);
            this.currentEmployeeUuid = null;
          }
        }
      },
      error: (error) => {
        console.error('❌ Error getting employee UUID from cache:', error);
        // Fallback to user UUID
        const userUuid = currentUser.id;
        if (userUuid && this.isValidUUID(userUuid)) {
          console.log('🔄 Using current user UUID as fallback after cache error:', userUuid);
          this.currentEmployeeUuid = userUuid;
        } else {
          this.currentEmployeeUuid = null;
        }
      }
    });
  }

  // OPTIMIZED: Removed redundant API testing - endpoints are tested during actual data loading

  // Manual API test method for debugging
  testSpecificEndpoint(endpoint: string): void {
    console.log(`Testing endpoint: ${endpoint}`);
    this.leaveService.testApiEndpoint(endpoint).subscribe({
      next: (available) => {
        console.log(`Endpoint ${endpoint} available:`, available);
        alert(`Endpoint ${endpoint} is ${available ? 'available' : 'not available'}`);
      },
      error: (error) => {
        console.error(`Error testing endpoint ${endpoint}:`, error);
        alert(`Error testing endpoint ${endpoint}: ${error.message}`);
      }
    });
  }



  // Debug method to check current state
  debugCurrentState(): void {
    console.log('=== CURRENT COMPONENT STATE ===');
    console.log('leaveTypes:', this.leaveTypes);
    console.log('leaveTypes type:', typeof this.leaveTypes);
    console.log('leaveTypes is array:', Array.isArray(this.leaveTypes));
    console.log('leaveTypes length:', this.leaveTypes?.length);
    console.log('leaveBalances:', this.leaveBalances);
    console.log('myLeaves:', this.myLeaves);
    console.log('leaveApplications:', this.leaveApplications);
    console.log('filteredApplications:', this.filteredApplications);
    console.log('paginatedApplications:', this.paginatedApplications);
    console.log('loading:', this.loading);
    console.log('error:', this.error);
    console.log('selectedLeaveTypeId:', this.selectedLeaveTypeId);
    console.log('selectedLeaveType:', this.selectedLeaveType);
    console.log('selectedLeaveFullName:', this.selectedLeaveFullName);
    console.log('currentUser:', this.authService.currentUserValue);
    console.log('holidays:', this.holidays);
    console.log('holidaysLoaded:', this.holidaysLoaded);
    console.log('apiStatus:', this.apiStatus);
    console.log('================================');
  }

  // Test holiday validation manually
  testODHolidayValidation(): void {
    console.log('🧪 Testing holiday validation manually...');

    // Set test values
    this.selectedLeaveType = 'PL'; // Test with any leave type
    this.fromDate = '2025-08-15'; // Independence Day
    this.toDate = '2025-08-15';

    console.log(`🎯 Test setup: ${this.selectedLeaveType} from ${this.fromDate} to ${this.toDate}`);
    console.log(`🔍 Current holidays loaded: ${this.holidaysLoaded}, count: ${this.holidays.length}`);

    // First, let's check what holidays we have
    console.log('📅 Current holidays in memory:', this.holidays);

    // Trigger validation
    this.calculateDays();

    // Show current state
    setTimeout(() => {
      console.log(`📊 Final state: hasHolidayRestriction = ${this.hasHolidayRestriction}`);
      console.log(`📊 Submit button should be: ${this.hasHolidayRestriction ? 'HIDDEN' : 'VISIBLE'}`);
      alert(`Holiday Validation Test Results:\n\nRestriction Active: ${this.hasHolidayRestriction}\nSubmit Button: ${this.hasHolidayRestriction ? 'HIDDEN' : 'VISIBLE'}\n\nCheck console for detailed logs.`);
    }, 3000);
  }

  // Test holiday API integration
  testHolidayIntegration(): void {
    console.log('🧪 Testing holiday API integration...');

    Swal.fire({
      title: 'Testing Holiday API',
      html: `
        <div class="text-center">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mb-0">Testing holiday API integration...</p>
        </div>
      `,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false
    });

    this.calendarService.getHolidays().subscribe({
      next: (holidays: CalendarHoliday[]) => {
        console.log('✅ Holiday API test successful:', holidays);
        console.log('✅ Holiday count:', holidays.length);
        console.log('✅ First holiday:', holidays.length > 0 ? holidays[0] : 'None');

        // Update the holidayDates array for datepicker
        this.holidayDates = holidays.map(h => h.date);
        console.log('✅ Updated holidayDates for datepicker:', this.holidayDates);

        const today = new Date();
        const testDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7); // Test date 7 days from now

        this.calendarService.isHoliday(testDate).subscribe({
          next: (isHoliday) => {
            Swal.fire({
              icon: 'success',
              title: 'Holiday API Test Results',
              html: `
                <div class="text-start">
                  <div class="alert alert-success">
                    <h6 class="alert-heading">✅ API Integration Successful</h6>
                    <ul class="mb-0">
                      <li><strong>Holidays loaded:</strong> ${holidays.length}</li>
                      <li><strong>API endpoint:</strong> /api/v1/calendar/test/holidays</li>
                      <li><strong>Test date (${testDate.toDateString()}):</strong> ${isHoliday ? 'Is a holiday' : 'Not a holiday'}</li>
                      <li><strong>Holiday dates for datepicker:</strong> ${this.holidayDates.length} dates</li>
                    </ul>
                  </div>
                  ${holidays.length > 0 ? `
                    <div class="alert alert-info">
                      <h6 class="alert-heading">📅 Sample Holidays:</h6>
                      <ul class="mb-0">
                        ${holidays.slice(0, 3).map(h => `<li><strong>${h.name}:</strong> ${h.date}</li>`).join('')}
                        ${holidays.length > 3 ? `<li><em>... and ${holidays.length - 3} more</em></li>` : ''}
                      </ul>
                    </div>
                  ` : ''}
                  <div class="alert alert-warning">
                    <h6 class="alert-heading">🔧 Datepicker Test</h6>
                    <p class="mb-0">After closing this dialog, try opening the datepicker to see if holiday dates are disabled.</p>
                  </div>
                </div>
              `,
              confirmButtonColor: '#3F828B'
            }).then(() => {
              // Trigger change detection to update datepicker
              this.cdr.detectChanges();
            });
          }
        });
      },
      error: (error) => {
        console.error('❌ Holiday API test failed:', error);

        Swal.fire({
          icon: 'error',
          title: 'Holiday API Test Failed',
          html: `
            <div class="text-start">
              <div class="alert alert-danger">
                <h6 class="alert-heading">❌ API Integration Failed</h6>
                <p class="mb-2"><strong>Error:</strong> ${error.message || 'Unknown error'}</p>
                <p class="mb-0"><strong>Status:</strong> ${error.status || 'N/A'}</p>
              </div>
              <div class="alert alert-warning">
                <h6 class="alert-heading">⚠️ Impact</h6>
                <p class="mb-0">Holiday validation will not work. Leave applications may be processed without holiday checking.</p>
              </div>
            </div>
          `,
          confirmButtonColor: '#dc3545'
        });
      }
    });
  }

  // Show insufficient balance error with automatic redirection option
  private showInsufficientBalanceError(remaining: number, requested: number, modal: any): void {
    console.log('⚠️ Showing insufficient balance error with auto-redirect option');

    // Close the leave application modal first
    modal.close();
    this.modalRef = null; // Reset modal reference

    // Show enhanced error dialog with auto-redirect
    Swal.fire({
      icon: 'error',
      title: '❌ Insufficient Leave Balance',
      html: `
        <div class="text-start">
          <div class="alert alert-danger mb-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Cannot Apply for Leave</strong>
          </div>
          <div class="mb-3">
            <h6 class="mb-2">Leave Balance Details:</h6>
            <ul class="list-unstyled">
              <li><strong>Requested Days:</strong> ${requested} day(s)</li>
              <li><strong>Available Balance:</strong> ${remaining} day(s)</li>
              <li><strong>Shortage:</strong> ${requested - remaining} day(s)</li>
            </ul>
          </div>
          <div class="alert alert-info mb-3">
            <i class="fas fa-info-circle me-2"></i>
            <strong>What you can do:</strong>
            <ul class="mb-0 mt-2">
              <li>View your complete leave balance details</li>
              <li>Check your leave history and usage</li>
              <li>Apply for a different leave type</li>
              <li>Reduce the number of requested days</li>
            </ul>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonColor: '#3F828B',
      cancelButtonColor: '#6c757d',
      confirmButtonText: '<i class="fas fa-chart-bar me-2"></i>View Leave Details',
      cancelButtonText: '<i class="fas fa-times me-2"></i>Close',
      width: '600px',
      customClass: {
        popup: 'swal2-popup-large'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // Auto-redirect to leave details section
        this.redirectToLeaveDetails();
      }
    });
  }

  // Handle insufficient balance error from API response
  private handleInsufficientBalanceFromAPI(): void {
    console.log('🔄 Handling insufficient balance error from API response');

    Swal.fire({
      icon: 'warning',
      title: '⚠️ Insufficient Leave Balance',
      html: `
        <div class="text-start">
          <div class="alert alert-warning mb-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Leave Application Failed</strong>
          </div>
          <p class="mb-3">Your leave application was rejected due to insufficient leave balance.</p>
          <div class="alert alert-info mb-3">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Next Steps:</strong>
            <ul class="mb-0 mt-2">
              <li>Check your current leave balance</li>
              <li>Review your leave history</li>
              <li>Consider applying for fewer days</li>
              <li>Try a different leave type</li>
            </ul>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonColor: '#3F828B',
      cancelButtonColor: '#6c757d',
      confirmButtonText: '<i class="fas fa-chart-bar me-2"></i>View Leave Details',
      cancelButtonText: '<i class="fas fa-times me-2"></i>Close',
      width: '500px'
    }).then((result) => {
      if (result.isConfirmed) {
        this.redirectToLeaveDetails();
      }
    });
  }

  // Redirect to leave details section automatically
  private redirectToLeaveDetails(): void {
    console.log('🔄 Auto-redirecting to leave details section');

    // Scroll to leave details section smoothly
    const leaveDetailsSection = document.getElementById('leave-details-section');
    if (leaveDetailsSection) {
      leaveDetailsSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Add highlight effect to draw attention
      leaveDetailsSection.classList.add('highlight-section');
      setTimeout(() => {
        leaveDetailsSection.classList.remove('highlight-section');
      }, 3000);

      // Show success message about redirection
      setTimeout(() => {
        Swal.fire({
          icon: 'info',
          title: '📊 Leave Details',
          text: 'Here you can view your complete leave balance and history.',
          timer: 3000,
          timerProgressBar: true,
          showConfirmButton: false,
          position: 'top-end',
          toast: true,
          customClass: {
            popup: 'swal2-toast-custom'
          }
        });
      }, 1000);
    } else {
      console.warn('⚠️ Leave details section not found, refreshing page');
      // Fallback: refresh the page to ensure leave details are visible
      window.location.reload();
    }
  }

  // Extract user-friendly error message from leave application API response
  private extractLeaveApplicationError(error: any): string {
    console.log('🔍 Extracting leave application error from backend:', error);

    // Priority 1: Check for direct error message from backend
    if (error.error && typeof error.error === 'string') {
      console.log('📝 Backend error message:', error.error);
      return this.cleanErrorMessage(error.error);
    }

    // Priority 2: Check for structured error response
    if (error.error && error.error.detail) {
      console.log('📝 Backend error detail:', error.error.detail);

      if (typeof error.error.detail === 'string') {
        return this.cleanErrorMessage(error.error.detail);
      }

      // Handle array of validation errors
      if (Array.isArray(error.error.detail)) {
        const errorMessages = error.error.detail.map((detail: any) => {
          if (typeof detail === 'string') {
            return this.cleanErrorMessage(detail);
          }
          if (detail.msg) {
            return this.cleanErrorMessage(detail.msg);
          }
          return 'Invalid data provided';
        });
        return errorMessages.join('. ');
      }
    }

    // Priority 3: Check for error message field
    if (error.error && error.error.message) {
      console.log('📝 Backend error message field:', error.error.message);
      return this.cleanErrorMessage(error.error.message);
    }

    // Handle FastAPI validation errors
    if (error.error?.detail) {
      const details = Array.isArray(error.error.detail) ? error.error.detail : [error.error.detail];
      const errorMessages = details.map((detail: any) => {
        if (typeof detail === 'string') {
          return this.cleanErrorMessage(detail);
        }
        if (detail.msg) {
          return this.cleanErrorMessage(detail.msg);
        }
        return 'Invalid data provided';
      });

      return errorMessages.join('. ');
    }

    // Handle other error formats
    if (error.error?.message) {
      return this.cleanErrorMessage(error.error.message);
    }

    // Handle HTTP status codes
    switch (error.status) {
      case 400:
        return 'The request contains invalid data. Please check your input and try again.';
      case 401:
        return 'Your session has expired. Please log in again.';
      case 403:
        return 'You do not have permission to apply for leave. Please contact your administrator.';
      case 422:
        return 'Please check your data format and correct any validation errors.';
      case 500:
        return 'Something went wrong on our end. Please try again in a few moments.';
      default:
        return 'Failed to submit leave application. Please try again.';
    }
  }

  // Clean error messages by removing technical prefixes
  private cleanErrorMessage(message: string): string {
    if (!message) return message;

    // Remove common technical prefixes
    const prefixesToRemove = [
      'Validation errors:',
      'ValidationError:',
      'Error:',
      'Exception:',
      'HTTPException:',
      'Bad request:'
    ];

    let cleanMessage = message;
    prefixesToRemove.forEach(prefix => {
      const regex = new RegExp(`^${prefix.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*`, 'i');
      cleanMessage = cleanMessage.replace(regex, '');
    });

    // Capitalize first letter if it's not already
    if (cleanMessage && cleanMessage.length > 0) {
      cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
    }

    // Ensure message ends with a period
    if (cleanMessage && cleanMessage.length > 0 && !cleanMessage.match(/[.!?]$/)) {
      cleanMessage += '.';
    }

    return cleanMessage;
  }

  // Load all leave-related data (balances and Leave Details)
  loadLeaveData(): void {
    console.log('🔄 Loading leave data (balances + Leave Details)...');
    this.loading = true;
    this.error = null;
    console.log('✅ Loading state set to true');

    // Load leave types first
    this.leaveService.getLeaveTypes().subscribe({
      next: (response) => {
        console.log('Leave types API response:', response);

        // Handle different API response structures
        let leaveTypes: LeaveType[] = [];
        if (response && typeof response === 'object' && response.success && Array.isArray(response.data)) {
          // API returns { success: true, data: [...] }
          leaveTypes = response.data;
          console.log('📋 Extracted leave types from API response.data:', leaveTypes);
        } else if (Array.isArray(response)) {
          // API returns array directly
          leaveTypes = response;
          console.log('📋 Direct array response:', leaveTypes);
        } else {
          console.warn('⚠️ Unexpected leave types API response structure:', response);
          leaveTypes = [];
        }

        // Ensure we always have an array
        this.leaveTypes = Array.isArray(leaveTypes) ? leaveTypes : [];
        console.log('✅ Leave types set:', this.leaveTypes);

        // Then load leave balances and Leave Details
        this.loadLeaveBalanceDataInternal();
      },
      error: (error) => {
        console.error('Error loading leave types:', error);
        this.error = 'Failed to load leave types. Using fallback data.';

        // Use fallback leave types if API fails
        this.leaveTypes = [
          { id: 1, name: 'PL', description: 'Privilege Leave' },
          { id: 2, name: 'SL', description: 'Sick Leave' },
          { id: 3, name: 'CL', description: 'Casual Leave' }
        ];

        // Still try to load leave balances
        this.loadLeaveBalanceDataInternal();
      }
    });
  }

  // Internal method to load balance data (called from loadLeaveData)
  private loadLeaveBalanceDataInternal(): void {
    console.log('Loading leave balance data from API...');

    // Use the individual balance API directly as it has the correct structure
    this.leaveService.getMyLeaveBalance().subscribe({
      next: (response: any) => {
        console.log('Leave balance API response:', response);

        // Handle the API response structure: { success: true, data: [...] }
        let balances: any[] = [];
        if (response && typeof response === 'object' && response.success && Array.isArray(response.data)) {
          balances = response.data;
          console.log('Extracted balances from API:', balances);
        } else if (Array.isArray(response)) {
          balances = response;
          console.log('Direct array response:', balances);
        } else {
          console.error('Unexpected API response structure:', response);
        }

        // Extract employee UUID from the first balance record if available
        if (balances && balances.length > 0 && balances[0] && balances[0].employee_id) {
          this.currentEmployeeUuid = balances[0].employee_id;
          console.log('✅ Employee UUID extracted from balance API:', this.currentEmployeeUuid);
        }

        // Combine leave types with balance data to show all leave types
        this.combineLeaveTypesWithBalances(balances);

        // Console detailed leave balance information
        console.group('📊 Employee Leave Balance Details');
        this.leaveBalances.forEach((balance, index) => {
          console.log(`${index + 1}. ${balance.leaveFullName} (${balance.leaveType}):`);
          console.log(`   📈 Total Allocated: ${balance.totalAllowed} days`);
          console.log(`   📉 Used: ${balance.taken} days`);
          console.log(`   ✅ Remaining: ${balance.remaining} days`);
          console.log(`   📊 Utilization: ${balance.totalAllowed > 0 ? ((balance.taken / balance.totalAllowed) * 100).toFixed(1) : 0}%`);
          console.log('   ---');
        });

        // Summary statistics
        const totalAllocated = this.leaveBalances.reduce((sum, b) => sum + b.totalAllowed, 0);
        const totalUsed = this.leaveBalances.reduce((sum, b) => sum + b.taken, 0);
        const totalRemaining = this.leaveBalances.reduce((sum, b) => sum + b.remaining, 0);

        console.log('📋 SUMMARY:');
        console.log(`   Total Allocated: ${totalAllocated} days`);
        console.log(`   Total Used: ${totalUsed} days`);
        console.log(`   Total Remaining: ${totalRemaining} days`);
        console.log(`   Overall Utilization: ${totalAllocated > 0 ? ((totalUsed / totalAllocated) * 100).toFixed(1) : 0}%`);
        console.groupEnd();

        // Load Leave Details section using GET /api/v1/leave/
        this.loadMyLeaves();
      },
      error: (error) => {
        console.error('❌ Error loading leave balances from API:', error);
        console.error('API Error Details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });

        // Use fallback data when API fails to ensure UI still works
        console.warn('⚠️ Using fallback leave balance data due to API failure');
        this.combineLeaveTypesWithBalances([]); // This will create default entries

        // Load Leave Details section even after balance API failure
        this.loadMyLeaves();
      }
    });
  }

  // Load my leaves for Leave Details section - prioritize regular leaves, then try comp-off
  private loadMyLeaves(): void {
    console.log('🔍 Loading Leave Details - starting with regular leaves...');

    // First, load regular leaves (this is the primary data source)
    console.log('📡 Starting regular leaves API call...');
    this.leaveService.getMyLeaves().pipe(
      timeout(8000) // 8 second timeout
    ).subscribe({
      next: (regularResponse) => {
        console.log('✅ Regular leaves loaded:', regularResponse);

        // Process regular leaves
        let myLeaves: Leave[] = [];
        if (regularResponse && typeof regularResponse === 'object' && (regularResponse as any).success && Array.isArray((regularResponse as any).data)) {
          myLeaves = (regularResponse as any).data;
          console.log('📋 Extracted regular leaves from API response.data:', myLeaves);
        } else if (Array.isArray(regularResponse)) {
          myLeaves = regularResponse;
          console.log('📋 Direct array response for regular leaves:', myLeaves);
        } else {
          console.warn('⚠️ Unexpected regular leaves API response structure:', regularResponse);
          myLeaves = [];
        }

        // Store regular leaves
        this.myLeaves = myLeaves || [];
        console.log('📋 Regular leaves stored:', this.myLeaves.length, 'items');

        // Transform and display regular leaves first (so page loads quickly)
        this.transformAndSortLeaveApplications();
        this.loading = false;
        this.clearErrors();
        console.log('✅ Leave Details loaded successfully with regular leaves');

        // Now try to load comp-off requests in the background and merge them
        console.log('🔍 Now attempting to load comp-off requests in background...');
        this.leaveService.getMyCompoffRequests().pipe(
          timeout(5000) // Shorter timeout for comp-off since it's secondary
        ).subscribe({
          next: (compoffRequests) => {
            console.log('✅ Comp-off requests loaded in background:', compoffRequests);

            // Transform comp-off requests to Leave format
            const compoffAsLeaves: Leave[] = (compoffRequests || []).map(compoff => ({
              id: compoff.id,
              employee_id: compoff.employee_id,
              leave_type_id: 999, // Special ID for comp-off requests
              leave_type: 'compensatory_off',
              start_date: compoff.working_date,
              end_date: compoff.working_date,
              days: 1,
              reason: compoff.reason,
              status: compoff.status as any,
              created_at: compoff.created_at,
              updated_at: compoff.updated_at,
              approved_by: compoff.approved_by,
              approved_at: compoff.approved_at,
              rejected_reason: compoff.rejection_reason
            }));

            console.log('📋 Transformed comp-off requests in background:', compoffAsLeaves.length, 'items');

            // Only merge if we got comp-off data
            if (compoffAsLeaves.length > 0) {
              // Merge with regular leaves
              this.myLeaves = [...this.myLeaves, ...compoffAsLeaves];
              console.log('📋 Final combined data after background merge:', this.myLeaves.length, 'total items');

              // Re-transform and display with comp-off data included
              this.transformAndSortLeaveApplications();
              console.log('✅ Leave Details updated with comp-off requests in background');
            } else {
              console.log('ℹ️ No comp-off requests to merge');
            }
          },
          error: (compoffError) => {
            console.warn('⚠️ Comp-off requests failed in background (page already loaded with regular leaves):', compoffError);
          }
        });
      },
      error: (error) => {
        console.error('❌ Error loading regular leaves:', error);
        this.myLeaves = [];
        this.leaveApplications = [];
        this.filteredApplications = [];
        this.loading = false;
        console.warn('⚠️ Leave Details set to empty due to regular leaves API failure');
      }
    });
  }


  // Transform API leave data to display format and sort in descending order (latest first)
  private transformAndSortLeaveApplications(): void {
    console.log('🔄 Transforming and sorting leave applications...');
    console.log('📋 Raw myLeaves data:', this.myLeaves);
    console.log('📋 myLeaves type:', typeof this.myLeaves);
    console.log('📋 myLeaves is array:', Array.isArray(this.myLeaves));

    // Ensure myLeaves is an array
    if (!Array.isArray(this.myLeaves)) {
      console.error('❌ myLeaves is not an array:', this.myLeaves);
      this.myLeaves = [];
    }

    if (!this.myLeaves || this.myLeaves.length === 0) {
      console.log('⚠️ No leaves to transform');
      this.leaveApplications = [];
      this.filteredApplications = [];
      this.updatePagination();
      return;
    }

    console.log(`📊 Processing ${this.myLeaves.length} leave records...`);

    // Transform API data to display format with error handling
    this.leaveApplications = this.myLeaves
      .filter(leave => leave && typeof leave === 'object') // Filter out invalid entries
      .map(leave => {
        try {
          return this.transformLeaveToDisplay(leave);
        } catch (error) {
          console.error('❌ Error transforming leave:', leave, error);
          return null;
        }
      })
      .filter(leave => leave !== null) as LeaveApplicationDisplay[]; // Remove failed transformations

    console.log('📋 Transformed leave applications:', this.leaveApplications);

    // Sort by descending order (latest first) - by created date, then by start date
    this.leaveApplications.sort((a, b) => {
      // Primary sort: by created date if available (most recent first)
      const createdA = this.myLeaves.find(l => l.id === a.id)?.created_at;
      const createdB = this.myLeaves.find(l => l.id === b.id)?.created_at;

      if (createdA && createdB) {
        const dateA = new Date(createdA).getTime();
        const dateB = new Date(createdB).getTime();
        if (dateA !== dateB) {
          return dateB - dateA; // Descending order (latest first)
        }
      }

      // Secondary sort: by start date (most recent first)
      const startDateA = new Date(a.startDate).getTime();
      const startDateB = new Date(b.startDate).getTime();

      return startDateB - startDateA; // Descending order (latest first)
    });

    console.log('✅ Transformed and sorted leave applications:', this.leaveApplications);
    console.log(`📊 Total applications after transformation: ${this.leaveApplications.length}`);

    // Fetch employee names for better display
    this.fetchEmployeeNamesForLeaves();
  }

  // OPTIMIZED: Fetch employee names using cache service
  private fetchEmployeeNamesForLeaves(): void {
    console.log('🔍 Fetching employee names for leave applications using cache...');

    // Get unique employee IDs from leave applications
    const employeeIds = [...new Set(this.myLeaves.map(leave => leave.employee_id).filter(id => id))];
    console.log('📋 Unique employee IDs from leaves:', employeeIds);

    if (employeeIds.length === 0) {
      console.log('⚠️ No employee IDs found in leaves');
      this.filterTable();
      this.loading = false;
      return;
    }

    // Use cached employee service for efficient lookup
    this.employeeCacheService.getEmployeeNamesByIds(employeeIds).subscribe({
      next: (employeeNameMap) => {
        console.log('✅ Employee names fetched from cache:', employeeNameMap);

        // Update leave applications with employee names
        this.leaveApplications.forEach(leave => {
          const leaveRecord = this.myLeaves.find(l => l.id === leave.id);
          if (leaveRecord && leaveRecord.employee_id) {
            const employeeName = employeeNameMap[leaveRecord.employee_id];
            if (employeeName) {
              leave.employeeName = employeeName;
              // Try to get employee code from cache
              this.employeeCacheService.getEmployeeById(leaveRecord.employee_id).subscribe({
                next: (employee) => {
                  if (employee) {
                    leave.employeeCode = employee.employee_code || `EMP${leaveRecord.employee_id.slice(-4)}`;
                  }
                },
                error: () => {
                  leave.employeeCode = `EMP${leaveRecord.employee_id.slice(-4)}`;
                }
              });
            } else {
              leave.employeeName = `Employee ${leaveRecord.employee_id.slice(-4)}`;
              leave.employeeCode = `EMP${leaveRecord.employee_id.slice(-4)}`;
            }
          }
        });

        console.log('🔄 Updated leave applications with cached employee names');
        this.filterTable();
        this.loading = false;
      },
      error: (error) => {
        console.error('❌ Error fetching employee names from cache:', error);
        // Fallback to default names
        this.leaveApplications.forEach(leave => {
          const leaveRecord = this.myLeaves.find(l => l.id === leave.id);
          if (leaveRecord && leaveRecord.employee_id) {
            leave.employeeName = `Employee ${leaveRecord.employee_id.slice(-4)}`;
            leave.employeeCode = `EMP${leaveRecord.employee_id.slice(-4)}`;
          }
        });

        this.filterTable();
        this.loading = false;
      }
    });
  }

  // Fetch approver names for approved leaves (DEPRECATED: Now using approved_by_name from API)
  private fetchApproverNames(): void {
    console.log('🔍 Fetching approver names for approved leaves...');

    // Get unique approver IDs from approved leaves
    const approverIds = [...new Set(
      this.myLeaves
        .filter(leave => leave.approved_by && leave.status === 'approved')
        .map(leave => leave.approved_by)
        .filter(id => id)
    )];

    console.log('📋 Unique approver IDs:', approverIds);

    if (approverIds.length === 0) {
      console.log('⚠️ No approver IDs found');
      this.filterTable();

      // Ensure loading is set to false
      this.loading = false;
      return;
    }

    // Fetch approver details for each ID
    const approverRequests = approverIds.map(approverId =>
      this.employeeService.getEmployeeByUuid(approverId!).pipe(
        map((response: any) => {
          console.log(`👤 Approver data for ${approverId}:`, response);

          // Handle different response structures
          let approverData = null;
          if (response && response.success && response.data) {
            approverData = response.data;
          } else if (response && response.first_name) {
            approverData = response;
          }

          if (approverData) {
            const firstName = approverData.first_name || approverData.employee_first_name || '';
            const lastName = approverData.last_name || approverData.employee_last_name || '';
            const fullName = `${firstName} ${lastName}`.trim();

            return {
              approverId: approverId,
              approverName: fullName || `Approver ${approverId!.slice(-4)}`
            };
          }

          return {
            approverId: approverId,
            approverName: `Approver ${approverId!.slice(-4)}`
          };
        }),
        catchError((error: any) => {
          console.error(`❌ Error fetching approver ${approverId}:`, error);
          return of({
            approverId: approverId,
            approverName: `Approver ${approverId!.slice(-4)}`
          });
        })
      )
    );

    // Execute all requests and update leave applications with approver names
    forkJoin(approverRequests).subscribe({
      next: (approverData: any) => {
        console.log('✅ All approver data fetched:', approverData);

        // Update leave applications with approver names
        this.leaveApplications.forEach(leave => {
          const leaveRecord = this.myLeaves.find(l => l.id === leave.id);
          if (leaveRecord && leaveRecord.approved_by) {
            const approver = approverData.find((app: any) => app.approverId === leaveRecord.approved_by);
            if (approver) {
              leave.approverName = approver.approverName;
            }
          }
        });

        console.log('🔄 Updated leave applications with approver names:', this.leaveApplications.slice(0, 3));

        // Now apply filters and show the data
        this.filterTable();

        // Ensure loading is set to false
        this.loading = false;
        console.log('✅ All data loaded successfully, loading state set to false');
      },
      error: (error: any) => {
        console.error('❌ Error fetching approver names:', error);
        // Even if approver fetching fails, show the data
        this.filterTable();

        // Ensure loading is set to false
        this.loading = false;
        console.log('✅ Data loading completed (with approver fetch error), loading state set to false');
      }
    });
  }

  // Transform individual leave from API format to display format
  private transformLeaveToDisplay(leave: Leave): LeaveApplicationDisplay {
    // Map leave type enum to display format
    const leaveTypeMap: { [key: string]: string } = {
      'privilege_leave': 'PL',
      'sick_leave': 'SL',
      'casual_leave': 'CL',
      'compensatory_off': 'COMP',
      'outdoor_duty': 'OD',
      'leave_without_pay': 'LWP',
      'maternity_leave': 'ML',
      'paternity_leave': 'PTL'
    };

    // Map status to display format (handle both uppercase and lowercase)
    const statusMap: { [key: string]: 'Approved' | 'Pending' | 'Rejected' | 'Cancelled' } = {
      // Lowercase variants (regular leave API)
      'approved': 'Approved',
      'pending': 'Pending',
      'rejected': 'Rejected',
      'declined': 'Rejected',
      'cancelled': 'Cancelled',
      // Uppercase variants (comp-off API)
      'APPROVED': 'Approved',
      'PENDING': 'Pending',
      'REJECTED': 'Rejected',
      'DECLINED': 'Rejected',
      'CANCELLED': 'Cancelled'
    };

    const currentUser = this.authService.currentUserValue;

    // Handle leave type safely
    const leaveTypeString = typeof leave.leave_type === 'string' ? leave.leave_type :
                           (leave.leave_type as any)?.name || 'unknown';

    // Debug status mapping
    const originalStatus = leave.status;
    const mappedStatus = statusMap[leave.status] || statusMap[leave.status?.toLowerCase()] || statusMap[leave.status?.toUpperCase()] || 'Pending';

    if (this.debugMode && originalStatus !== mappedStatus.toLowerCase()) {
      console.log(`🔄 Status mapping: "${originalStatus}" -> "${mappedStatus}"`);
    }

    return {
      id: leave.id?.toString() || 'N/A',
      employeeCode: (currentUser as any)?.employee_code || 'EMP001',
      employeeName: `${(currentUser as any)?.first_name || ''} ${(currentUser as any)?.last_name || ''}`.trim() || 'Current User',
      leaveType: leaveTypeMap[leaveTypeString] || leaveTypeString.toUpperCase() || 'UNKNOWN',
      startDate: leave.start_date,
      endDate: leave.end_date,
      totalLeaves: leave.days || 0,
      reason: leave.reason || 'No reason provided',
      status: mappedStatus,
      approvedDate: leave.approved_at || '',
      approverName: leave.approved_by_name || '' // Use approved_by_name field from API
    };
  }

  // Comprehensive method to load all leave balance data (public method for refresh)
  loadLeaveBalanceData(): void {
    console.log('🔄 Refreshing leave balance data...');
    this.loading = true;
    this.loadLeaveBalanceDataInternal();
  }

  // Combine leave types with balance data to show all leave types
  private combineLeaveTypesWithBalances(balances: any[]): void {
    console.log('Combining leave types with balance data...');
    console.log('Available leave types:', this.leaveTypes);
    console.log('Balance data from API:', balances);

    // Create a map of balance data by leave type
    const balanceMap = new Map<string, any>();
    if (balances && Array.isArray(balances) && balances.length > 0) {
      balances.forEach(balance => {
        if (balance && balance.leave_type) {
          balanceMap.set(balance.leave_type, balance);
        }
      });
    }

    // Define all possible leave types with their mappings (based on actual API response)
    const allLeaveTypes = [
      { enum: 'privilege_leave', short: 'PL', full: 'Privilege Leave', id: 1 },
      { enum: 'sick_leave', short: 'SL', full: 'Sick Leave', id: 2 },
      { enum: 'casual_leave', short: 'CL', full: 'Casual Leave', id: 3 },
      { enum: 'compensatory_off', short: 'COMP', full: 'Compensatory Off', id: 4 },
      { enum: 'outdoor_duty', short: 'OD', full: 'Outdoor Duty', id: 5 },
      { enum: 'leave_without_pay', short: 'LWP', full: 'Leave Without Pay', id: 6 },
      // { enum: 'maternity_leave', short: 'ML', full: 'Maternity Leave', id: 7 },
      // { enum: 'paternity_leave', short: 'PTL', full: 'Paternity Leave', id: 8 }
    ];

    // Create leave balance display for all leave types
    this.leaveBalances = allLeaveTypes.map(leaveType => {
      const balanceData = balanceMap.get(leaveType.enum);

      if (balanceData) {
        // Use actual balance data from API
        try {
          return this.transformApiBalance(balanceData);
        } catch (error) {
          console.error('❌ Error transforming balance data:', balanceData, error);
          // Return default entry if transformation fails
          return {
            leaveType: leaveType.short,
            leaveFullName: leaveType.full,
            totalAllowed: 0,
            taken: 0,
            remaining: 0,
            leaveTypeId: leaveType.id
          };
        }
      } else {
        // Create default entry for leave types without balance data
        return {
          leaveType: leaveType.short,
          leaveFullName: leaveType.full,
          totalAllowed: 0,
          taken: 0,
          remaining: 0,
          leaveTypeId: leaveType.id
        };
      }
    });

    console.log('✅ Combined leave types with balances:', this.leaveBalances);

    // Ensure we always have at least some leave types to display
    if (this.leaveBalances.length === 0) {
      console.warn('⚠️ No leave balances created, using fallback data');
      this.leaveBalances = [
        { leaveType: 'PL', leaveFullName: 'Privilege Leave', totalAllowed: 0, taken: 0, remaining: 0, leaveTypeId: 1 },
        { leaveType: 'SL', leaveFullName: 'Sick Leave', totalAllowed: 0, taken: 0, remaining: 0, leaveTypeId: 2 },
        { leaveType: 'CL', leaveFullName: 'Casual Leave', totalAllowed: 0, taken: 0, remaining: 0, leaveTypeId: 3 }
      ];
    }
  }

  // Transform API balance response to display format
  private transformApiBalance(apiBalance: any): LeaveBalanceDisplay {
    console.log('Transforming API balance:', apiBalance);

    // Map leave type enum to display name (updated to match API response)
    const leaveTypeMap: { [key: string]: { short: string, full: string } } = {
      'privilege_leave': { short: 'PL', full: 'Privilege Leave' },
      'sick_leave': { short: 'SL', full: 'Sick Leave' },
      'casual_leave': { short: 'CL', full: 'Casual Leave' },
      'compensatory_off': { short: 'COMP', full: 'Compensatory Off' },
      'outdoor_duty': { short: 'OD', full: 'Outdoor Duty' },
      'leave_without_pay': { short: 'LWP', full: 'Leave Without Pay' },
      'maternity_leave': { short: 'ML', full: 'Maternity Leave' },
      'paternity_leave': { short: 'PTL', full: 'Paternity Leave' }
    };

    const leaveTypeInfo = leaveTypeMap[apiBalance.leave_type] || {
      short: apiBalance.leave_type?.toUpperCase() || 'UNKNOWN',
      full: apiBalance.leave_type?.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) || 'Unknown Leave Type'
    };

    // Extract values from API response
    const allocated = Number(apiBalance.allocated) || 0;
    const used = Number(apiBalance.used) || 0;
    const available = Number(apiBalance.available) || 0;

    // Calculate remaining (use available from API if present, otherwise calculate)
    const remaining = available > 0 ? available : Math.max(0, allocated - used);

    const transformed = {
      leaveType: leaveTypeInfo.short,
      leaveFullName: leaveTypeInfo.full,
      totalAllowed: allocated,
      taken: used,
      remaining: remaining,
      leaveTypeId: apiBalance.id || 0
    };

    console.log('API Balance Input:', {
      leave_type: apiBalance.leave_type,
      allocated: apiBalance.allocated,
      used: apiBalance.used,
      available: apiBalance.available,
      carried_forward: apiBalance.carried_forward
    });
    console.log('Transformed Balance Output:', transformed);

    return transformed;
  }



  // Method to get leave balance by leave type
  getLeaveBalance(leaveType: string): LeaveBalanceDisplay | undefined {
    return this.leaveBalances.find(balance => balance.leaveType === leaveType);
  }

  // Method to get total leave summary
  getTotalLeaveSummary(): { totalAllocated: number, totalTaken: number, totalRemaining: number } {
    if (this.leaveBalanceSummary) {
      return {
        totalAllocated: this.leaveBalanceSummary.total_allocated,
        totalTaken: this.leaveBalanceSummary.total_used,
        totalRemaining: this.leaveBalanceSummary.total_remaining
      };
    }

    // Calculate from individual balances if summary not available
    const totals = this.leaveBalances.reduce((acc, balance) => ({
      totalAllocated: acc.totalAllocated + balance.totalAllowed,
      totalTaken: acc.totalTaken + balance.taken,
      totalRemaining: acc.totalRemaining + balance.remaining
    }), { totalAllocated: 0, totalTaken: 0, totalRemaining: 0 });

    return totals;
  }

  // Method to refresh balance data
  refreshBalanceData(): void {
    this.loadLeaveData(); // Reload all data including balances
  }

  // Method to refresh only apply leave dashboard data after cancellation
  refreshAllLeaveData(): void {
    console.log('🔄 Refreshing apply leave dashboard and leave details section after cancellation...');

    // Show loading state
    this.loading = true;

    // First reload leave balance data (apply leave section)
    this.leaveService.getMyLeaveBalance().subscribe({
      next: (balances) => {
        console.log('✅ Fresh leave balances loaded:', balances);

        // Update the balance data
        this.combineLeaveTypesWithBalances(balances);

        // Then reload leave applications with fresh API call (leave details section)
        this.reloadLeaveDetailsSection();

        console.log('✅ Apply leave dashboard balance data refreshed');
      },
      error: (error) => {
        console.error('❌ Error refreshing leave balance data:', error);

        // Still try to reload leave details even if balance fails
        this.reloadLeaveDetailsSection();

        console.warn('⚠️ Leave balance refresh failed, but continuing with leave details refresh');
      }
    });

    console.log('✅ Apply leave dashboard and leave details section refresh initiated');
  }

  // Specifically reload the leave details section with fresh API data
  private reloadLeaveDetailsSection(): void {
    console.log('🔄 Reloading leave details section with fresh API data...');

    // Clear current data to show loading state
    this.leaveApplications = [];
    this.filteredApplications = [];
    this.paginatedApplications = [];

    // First, reload regular leaves (this is the primary data source)
    console.log('📡 Starting reload regular leaves API call...');
    this.leaveService.getMyLeaves().pipe(
      timeout(10000) // 10 second timeout
    ).subscribe({
      next: (regularResponse) => {
        console.log('✅ Fresh regular leaves loaded during reload:', regularResponse);

        // Process regular leaves
        let myLeaves: Leave[] = [];
        if (regularResponse && typeof regularResponse === 'object' && (regularResponse as any).success && Array.isArray((regularResponse as any).data)) {
          myLeaves = (regularResponse as any).data;
          console.log('📋 Extracted fresh regular leaves from API response.data:', myLeaves);
        } else if (Array.isArray(regularResponse)) {
          myLeaves = regularResponse;
          console.log('📋 Direct array response for fresh regular leaves:', myLeaves);
        } else {
          console.warn('⚠️ Unexpected regular leaves API response structure during reload:', regularResponse);
          myLeaves = [];
        }

        // Store regular leaves
        this.myLeaves = myLeaves || [];
        console.log('📋 Fresh regular leaves stored during reload:', this.myLeaves.length, 'items');

        // Now try to reload comp-off requests and merge them
        console.log('🔍 Now attempting to reload comp-off requests...');
        console.log('📡 Starting reload comp-off requests API call...');
        this.leaveService.getMyCompoffRequests().pipe(
          timeout(10000) // 10 second timeout
        ).subscribe({
          next: (compoffRequests) => {
            console.log('✅ Fresh comp-off requests loaded during reload:', compoffRequests);

            // Transform comp-off requests to Leave format
            const compoffAsLeaves: Leave[] = (compoffRequests || []).map(compoff => ({
              id: compoff.id,
              employee_id: compoff.employee_id,
              leave_type_id: 999, // Special ID for comp-off requests
              leave_type: 'compensatory_off',
              start_date: compoff.working_date,
              end_date: compoff.working_date,
              days: 1,
              reason: compoff.reason,
              status: compoff.status as any,
              created_at: compoff.created_at,
              updated_at: compoff.updated_at,
              approved_by: compoff.approved_by,
              approved_at: compoff.approved_at,
              rejected_reason: compoff.rejection_reason
            }));

            console.log('📋 Transformed fresh comp-off requests during reload:', compoffAsLeaves.length, 'items');

            // Merge with regular leaves
            this.myLeaves = [...this.myLeaves, ...compoffAsLeaves];
            console.log('📋 Final combined fresh data during reload:', this.myLeaves.length, 'total items');

            // Transform, filter and display
            this.transformAndSortLeaveApplications();
            this.filterTable();
            this.updatePagination();
            this.loading = false;

            console.log('✅ Leave details section fully refreshed with latest data from both APIs');
          },
          error: (compoffError) => {
            console.warn('⚠️ Comp-off requests failed during reload, continuing with regular leaves only:', compoffError);

            // Continue with just regular leaves
            this.transformAndSortLeaveApplications();
            this.filterTable();
            this.updatePagination();
            this.loading = false;
            console.log('✅ Leave details section refreshed with regular leaves only during reload');
          }
        });
      },
      error: (error) => {
        console.error('❌ Error reloading regular leaves:', error);
        this.myLeaves = [];
        this.leaveApplications = [];
        this.filteredApplications = [];
        this.paginatedApplications = [];
        this.loading = false;
        console.warn('⚠️ Leave details section reload failed due to regular leaves API failure');
      }
    });
  }



  // Method to get leave icon based on leave type
  getLeaveIcon(leaveType: string): string {
    const iconMap: { [key: string]: string } = {
      'PL': 'images/lms/apply-leave/privilege-leave.png',
      'SL': 'images/lms/apply-leave/sick-leave.png',
      'CL': 'images/lms/apply-leave/casual-leave.png',
      'COMP': 'images/lms/apply-leave/comp-off.png',
      'OD': 'images/lms/apply-leave/out-door.png',
      'LWP': 'images/lms/apply-leave/lwp.png',
      'ML': 'images/lms/apply-leave/maternity-leave.png',
      'PTL': 'images/lms/apply-leave/paternity-leave.png'
    };
    return iconMap[leaveType] || 'images/lms/apply-leave/privilege-leave.png';
  }

  // Method to get leave type description and policy info
  getLeaveTypeDescription(leaveType: string): string {
    const descriptions: { [key: string]: string } = {
      'PL': 'Privilege Leave - Annual leave for personal use',
      'SL': 'Sick Leave - For medical emergencies and health issues',
      'CL': 'Casual Leave - For short-term personal work',
      'COMP': 'Compensatory Off - For overtime or weekend work',
      'OD': 'Outdoor Duty - For official work outside office',
      'LWP': 'Leave Without Pay - When no other leave balance available (no salary)',
      'ML': 'Maternity Leave - For expecting mothers',
      'PTL': 'Paternity Leave - For new fathers'
    };
    return descriptions[leaveType] || 'Leave type';
  }

  // Method to get leave type policy tooltip
  getLeaveTypePolicyTooltip(leaveType: string): string {
    const policies: { [key: string]: string } = {
      'OD': '• For official work only (client meetings, site visits)\n• Cannot be used for weekends or holidays\n• Partial day OD allowed\n• Can be cancelled after approval',
      'LWP': '• Applied when no other leave balance available\n• Calculated on calendar days (includes weekends)\n• No salary or allowance during LWP\n• Can be cancelled after approval\n• Use only when other leaves exhausted'
    };
    return policies[leaveType] || '';
  }

  // Method to open the leave modal
  openLeaveModal(leaveType: string, leaveFullName: string): void {
    // Find the leave type ID
    const leaveBalance = this.getLeaveBalance(leaveType);
    if (leaveBalance) {
      this.selectedLeaveTypeId = leaveBalance.leaveTypeId;
      this.selectedLeaveType = leaveType;
      this.selectedLeaveFullName = leaveFullName;

      // Reset form fields and clear any previous errors
      this.fromDate = '';
      this.toDate = '';
      this.reason = '';
      this.dayCount = 0;
      this.error = null; // Clear any existing errors
      this.submitting = false; // Reset submitting state
      this.hasHolidayRestriction = false; // Reset holiday restriction state

      // Reset datepicker values
      this.fromDatePicker = null;
      this.toDatePicker = null;

      // Open the modal using NgbModal
      this.modalRef = this.modalService.open(this.leaveModal, {
        centered: true,
        backdrop: 'static'
      });

      // Clear errors when modal is closed or dismissed
      this.modalRef.result.then(
        (result) => {
          console.log('Modal closed with result:', result);
          this.clearErrors();
          this.modalRef = null; // Reset modal reference
        },
        (reason) => {
          console.log('Modal dismissed with reason:', reason);
          this.clearErrors();
          this.modalRef = null; // Reset modal reference
        }
      );
    }
  }



  // Real-time validation for OD dates (immediate feedback)
  private validateODDatesRealTime(): void {
    if (this.selectedLeaveType !== 'OD' || !this.fromDate || !this.toDate) {
      return;
    }

    console.log('⚡ Real-time OD validation triggered');

    // Check if FROM or TO date is a weekend/holiday (not dates in between)
    const fromDate = new Date(this.fromDate);
    const toDate = new Date(this.toDate);

    // Only check the start and end dates, not the dates in between
    const isFromDateWeekend = this.holidayService.isWeekend(fromDate);
    const isToDateWeekend = this.holidayService.isWeekend(toDate);

    if (isFromDateWeekend || isToDateWeekend) {
      console.log(`⚠️ OD applied on weekend - From: ${isFromDateWeekend ? 'Weekend' : 'Weekday'}, To: ${isToDateWeekend ? 'Weekend' : 'Weekday'}`);
      this.hasHolidayRestriction = true;
      this.showODRestrictionPopup([], []);
      this.cdr.detectChanges();
      return;
    }

    // Then check if FROM or TO date is a holiday
    this.holidayService.getHolidays().subscribe({
      next: (holidays) => {
        const fromDateStr = this.formatDate(fromDate);
        const toDateStr = this.formatDate(toDate);

        const fromDateHoliday = holidays.find(h => h.date === fromDateStr);
        const toDateHoliday = holidays.find(h => h.date === toDateStr);

        const hasHolidayOnStartOrEnd = fromDateHoliday || toDateHoliday;

        if (hasHolidayOnStartOrEnd) {
          console.log(`⚠️ OD applied on holiday - From: ${fromDateHoliday ? fromDateHoliday.name : 'No holiday'}, To: ${toDateHoliday ? toDateHoliday.name : 'No holiday'}`);
          this.hasHolidayRestriction = true;
          const holidaysFound = [fromDateHoliday, toDateHoliday].filter((h): h is NewHoliday => h !== undefined);
          this.showODRestrictionPopup([], holidaysFound);
        } else {
          console.log('✅ OD dates are valid - no weekends or holidays on start/end dates');
          this.hasHolidayRestriction = false;
        }
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('❌ Error in real-time holiday validation:', error);
      }
    });
  }

  // Method to calculate days between from and to date
  calculateDays(): void {
    console.log(`🔄 calculateDays() called - Leave Type: ${this.selectedLeaveType}, From: ${this.fromDate}, To: ${this.toDate}`);

    if (this.fromDate && this.toDate) {
      // For ALL leave types, calculate only working days (exclude weekends and holidays)
      console.log(`📊 Calculating working days for ${this.selectedLeaveType} (excluding weekends and holidays)...`);
      this.calendarService.getWorkingDaysBetween(this.fromDate, this.toDate).subscribe({
        next: (workingDays) => {
          this.dayCount = workingDays;
          console.log(`📊 ${this.selectedLeaveType} working days calculated: ${this.dayCount} days (weekends/holidays excluded)`);
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error(`❌ Error calculating working days for ${this.selectedLeaveType}:`, error);
          // Fallback to regular calculation
          this.dayCount = this.leaveService.calculateDaysBetween(this.fromDate, this.toDate);
          console.log(`📊 Fallback: Regular day count for ${this.selectedLeaveType}: ${this.dayCount} days`);
        }
      });

      // No frontend validation - let backend handle all validation

    } else {
      console.log('❌ Missing dates - Resetting day count');
      this.dayCount = 0;
    }
  }

  // Handle date change events (only when actual date is selected)
  onDateChange(dateType: 'from' | 'to'): void {
    console.log(`📅 Date changed: ${dateType} = ${dateType === 'from' ? this.fromDate : this.toDate}`);
    console.log('✅ Date changed - frontend validation disabled, backend will handle validation');
  }

  // Handle date input click (calendar open) - no validation here
  onDateInputClick(dateType: 'from' | 'to'): void {
    console.log(`📅 Date input clicked: ${dateType} (calendar opening)`);
    // Don't trigger validation on calendar open, only on actual date selection
  }

  // Helper method to convert NgbDate to YYYY-MM-DD format for backend
  private formatDateForBackend(date: NgbDate): string {
    const year = date.year;
    const month = String(date.month).padStart(2, '0');
    const day = String(date.day).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Datepicker methods for ng-bootstrap datepicker
  onDatePickerSelect(date: NgbDate | null, dateType: 'from' | 'to'): void {
    if (!date) return;

    // Convert NgbDate to YYYY-MM-DD format for backend
    const backendDateString = this.formatDateForBackend(date);
    // Convert NgbDate to DD-MM-YYYY format for display (handled by custom formatter)
    const displayDateString = this.formatter.format(date);

    if (dateType === 'from') {
      this.fromDatePicker = date;
      this.fromDate = backendDateString; // Store YYYY-MM-DD for backend
    } else {
      this.toDatePicker = date;
      this.toDate = backendDateString; // Store YYYY-MM-DD for backend
    }

    console.log(`📅 Datepicker date selected: ${dateType}`);
    console.log(`  Display format (DD-MM-YYYY): ${displayDateString}`);
    console.log(`  Backend format (YYYY-MM-DD): ${backendDateString}`);

    // Trigger existing date change logic
    this.onDateChange(dateType);
    this.calculateDays();
    this.clearErrors();
  }

  // Date filter function to disable weekends and holidays
  // Returns TRUE for DISABLED dates, FALSE for ENABLED dates (ng-bootstrap markDisabled expects this)
  dateFilter = (date: NgbDate | null): boolean => {
    if (!date) return true; // Disable null dates

    // Convert NgbDate to JavaScript Date
    const jsDate = new Date(date.year, date.month - 1, date.day);

    // Check if it's a weekend - DISABLE weekends
    const dayOfWeek = jsDate.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday = 0, Saturday = 6
      console.log(`🚫 WEEKEND DISABLED: ${this.formatDate(jsDate)} (day ${dayOfWeek})`);
      return true; // Return TRUE to DISABLE weekends
    }

    // Check if it's a holiday - DISABLE holidays
    if (this.holidayDates.length > 0) {
      const dateStr = this.formatDate(jsDate);
      if (this.holidayDates.includes(dateStr)) {
        console.log(`🚫 HOLIDAY DISABLED: ${dateStr}`);
        return true; // Return TRUE to DISABLE holidays
      }
    }

    // Return FALSE to ENABLE regular weekdays that are not holidays
    return false;
  };

  // Load holidays for datepicker filtering
  loadHolidaysForDatepicker(): void {
    console.log('🎄 Loading holidays for datepicker filtering...');

    // Use the calendar service to get holidays with the new API structure
    this.calendarService.getHolidays().subscribe({
      next: (holidays) => {
        console.log('🔍 Raw holidays received from calendar service:', holidays);
        console.log('🔍 Holiday structure check:', holidays.length > 0 ? holidays[0] : 'No holidays');

        this.holidayDates = holidays.map(h => h.date);
        console.log(`✅ Loaded ${this.holidayDates.length} holidays for datepicker filtering:`, this.holidayDates);

        // Log each holiday date for verification
        holidays.forEach((holiday, index) => {
          console.log(`  ${index + 1}. ${holiday.name} - ${holiday.date}`);
        });

        // Test the date filter with some sample dates
        this.testDateFilter();

        // Trigger change detection to update datepicker
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('❌ Error loading holidays for datepicker:', error);
        this.holidayDates = [];
      }
    });
  }

  // Test method to verify date filtering logic
  private testDateFilter(): void {
    console.log('🧪 Testing date filter logic...');
    console.log('🧪 Current holidayDates array:', this.holidayDates);

    // Test weekend dates (should be disabled = return true)
    const saturday = this.calendar.getToday().constructor.call(this.calendar.getToday(), 2025, 1, 4); // Saturday
    const sunday = this.calendar.getToday().constructor.call(this.calendar.getToday(), 2025, 1, 5); // Sunday

    // Test weekday (should be enabled = return false)
    const monday = this.calendar.getToday().constructor.call(this.calendar.getToday(), 2025, 1, 6); // Monday

    console.log('🧪 Saturday (should be disabled):', this.dateFilter(saturday));
    console.log('🧪 Sunday (should be disabled):', this.dateFilter(sunday));
    console.log('🧪 Monday (should be enabled):', this.dateFilter(monday));

    // Test holiday dates if available
    if (this.holidayDates.length > 0) {
      console.log('🧪 Testing holiday dates...');
      this.holidayDates.forEach((holidayDate, index) => {
        console.log(`🧪 Testing holiday ${index + 1}: ${holidayDate}`);
        const [year, month, day] = holidayDate.split('-').map(Number);

        // Create NgbDate for testing
        const holiday = this.calendar.getToday().constructor.call(this.calendar.getToday(), year, month, day);

        // Create JavaScript Date for comparison
        const jsDate = new Date(year, month - 1, day);
        const formattedJsDate = this.formatDate(jsDate);

        console.log(`🧪 Holiday date breakdown:`);
        console.log(`  - Original: ${holidayDate}`);
        console.log(`  - NgbDate: year=${holiday.year}, month=${holiday.month}, day=${holiday.day}`);
        console.log(`  - JS Date: ${jsDate.toDateString()}`);
        console.log(`  - Formatted JS Date: ${formattedJsDate}`);
        console.log(`  - Match check: ${this.holidayDates.includes(formattedJsDate)}`);

        const isDisabled = this.dateFilter(holiday);
        console.log(`🧪 Holiday ${holidayDate} (should be disabled): ${isDisabled}`);

        if (!isDisabled) {
          console.warn(`⚠️ Holiday ${holidayDate} is NOT being disabled! Check dateFilter logic.`);
          console.warn(`⚠️ Debug info: formatted=${formattedJsDate}, includes=${this.holidayDates.includes(formattedJsDate)}`);
        }
      });
    } else {
      console.warn('⚠️ No holiday dates loaded for testing!');
    }
  }

  // Debounced validation to prevent multiple API calls and popups
  private debouncedValidation(): void {
    // Only run validation if both dates are selected
    if (!this.fromDate || !this.toDate) {
      console.log('⏳ Skipping validation - both dates not selected yet');
      return;
    }

    // Clear any existing timeout
    this.clearValidationTimeout();

    // Set a new timeout for validation
    this.validationTimeout = setTimeout(() => {
      console.log('🔍 Running debounced validation...');
      console.log(`📅 Validating dates: From ${this.fromDate} to ${this.toDate} for ${this.selectedLeaveType}`);

      // UNIVERSAL HOLIDAY VALIDATION - applies to ALL leave types (PL, SL, CL, LWP, OD, etc.)
      console.log('🔍 Running UNIVERSAL holiday validation for ALL leave types...');
      this.checkHolidayRestrictionWithFreshData();

      // Additional weekend validation for ALL leave types (in addition to holiday validation)
      console.log(`🔍 Running additional ${this.selectedLeaveType} weekend validation...`);
      this.checkWeekendOnlyRestriction();
    }, 1000); // 1 second delay to allow user to finish selecting dates
  }

  // Clear validation timeout
  private clearValidationTimeout(): void {
    if (this.validationTimeout) {
      clearTimeout(this.validationTimeout);
      this.validationTimeout = null;
    }
  }

  // OPTIMIZED: Load all data in parallel using forkJoin for maximum performance
  private loadAllDataOptimized(): void {
    console.log('🚀 Starting optimized parallel data loading...');
    this.loading = true;
    this.error = null;

    // Check if we have valid cached data
    const now = Date.now();
    const cacheValid = this.masterDataCache.cacheTimestamp &&
                      (now - this.masterDataCache.cacheTimestamp) < this.CACHE_DURATION;

    if (cacheValid && this.masterDataCache.leaveTypes && this.masterDataCache.holidays) {
      console.log('✅ Using cached master data');
      this.leaveTypes = this.masterDataCache.leaveTypes;
      this.holidays = this.masterDataCache.holidays;
      this.holidaysLoaded = true;

      // Still need to load dynamic data (balances and leaves)
      this.loadDynamicDataOptimized();
      return;
    }

    // Load all independent data sources in parallel
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ No current user found');
      this.loading = false;
      this.error = 'Authentication required';
      return;
    }

    console.log('🔄 Loading master data and dynamic data in parallel...');

    // Parallel requests for all data
    const parallelRequests = {
      leaveTypes: this.leaveService.getLeaveTypes().pipe(
        catchError(error => {
          console.error('❌ Leave types failed:', error);
          return of({ success: false, data: [] });
        })
      ),
      holidays: this.calendarService.getHolidays().pipe(
        catchError(error => {
          console.error('❌ Holidays failed:', error);
          return of([]);
        })
      ),
      currentEmployee: this.employeeCacheService.getCurrentEmployee(
        currentUser.email,
        (currentUser as any)?.employee_code
      ).pipe(
        catchError(error => {
          console.error('❌ Current employee failed:', error);
          return of(null);
        })
      ),
      leaveBalance: this.leaveService.getMyLeaveBalance().pipe(
        catchError(error => {
          console.error('❌ Leave balance failed:', error);
          return of([]);
        })
      ),
      myLeaves: this.leaveService.getMyLeaves().pipe(
        timeout(8000),
        catchError(error => {
          console.error('❌ My leaves failed:', error);
          return of([]);
        })
      )
    };

    forkJoin(parallelRequests).subscribe({
      next: (results) => {
        console.log('✅ All parallel requests completed:', results);
        this.processOptimizedResults(results);
      },
      error: (error) => {
        console.error('❌ Parallel loading failed:', error);
        this.loading = false;
        this.error = 'Failed to load data. Please refresh the page.';
      }
    });
  }

  // OPTIMIZED: Process results from parallel loading
  private processOptimizedResults(results: any): void {
    console.log('🔄 Processing optimized results...');

    // Process leave types
    let leaveTypes: LeaveType[] = [];
    if (results.leaveTypes && typeof results.leaveTypes === 'object' && results.leaveTypes.success && Array.isArray(results.leaveTypes.data)) {
      leaveTypes = results.leaveTypes.data;
    } else if (Array.isArray(results.leaveTypes)) {
      leaveTypes = results.leaveTypes;
    }
    this.leaveTypes = Array.isArray(leaveTypes) ? leaveTypes : [];

    // Process holidays
    this.holidays = Array.isArray(results.holidays) ? results.holidays : [];
    this.holidaysLoaded = true;

    // Process current employee
    if (results.currentEmployee) {
      this.currentEmployeeUuid = results.currentEmployee.id;
      console.log('✅ Current employee from cache:', results.currentEmployee);
    }

    // Cache master data
    this.masterDataCache = {
      leaveTypes: this.leaveTypes,
      holidays: this.holidays,
      currentEmployee: results.currentEmployee,
      cacheTimestamp: Date.now()
    };

    // Process leave balance
    let balances: any[] = [];
    if (results.leaveBalance && typeof results.leaveBalance === 'object' && results.leaveBalance.success && Array.isArray(results.leaveBalance.data)) {
      balances = results.leaveBalance.data;
    } else if (Array.isArray(results.leaveBalance)) {
      balances = results.leaveBalance;
    }
    this.combineLeaveTypesWithBalances(balances);

    // Process my leaves
    let myLeaves: Leave[] = [];
    if (results.myLeaves && typeof results.myLeaves === 'object' && results.myLeaves.success && Array.isArray(results.myLeaves.data)) {
      myLeaves = results.myLeaves.data;
    } else if (Array.isArray(results.myLeaves)) {
      myLeaves = results.myLeaves;
    }
    this.myLeaves = myLeaves || [];

    // Load comp-off requests in background and merge
    this.loadCompoffInBackground();

    console.log('✅ Optimized data processing completed');
    this.loading = false;
    this.clearErrors();
  }

  // OPTIMIZED: Load only dynamic data when master data is cached
  private loadDynamicDataOptimized(): void {
    console.log('🔄 Loading dynamic data only (master data cached)...');

    const dynamicRequests = {
      leaveBalance: this.leaveService.getMyLeaveBalance().pipe(
        catchError(error => {
          console.error('❌ Leave balance failed:', error);
          return of([]);
        })
      ),
      myLeaves: this.leaveService.getMyLeaves().pipe(
        timeout(8000),
        catchError(error => {
          console.error('❌ My leaves failed:', error);
          return of([]);
        })
      )
    };

    forkJoin(dynamicRequests).subscribe({
      next: (results) => {
        console.log('✅ Dynamic data loaded:', results);

        // Process leave balance
        let balances: any[] = [];
        if (results.leaveBalance && typeof results.leaveBalance === 'object' && (results.leaveBalance as any).success && Array.isArray((results.leaveBalance as any).data)) {
          balances = (results.leaveBalance as any).data;
        } else if (Array.isArray(results.leaveBalance)) {
          balances = results.leaveBalance;
        }
        this.combineLeaveTypesWithBalances(balances);

        // Process my leaves
        let myLeaves: Leave[] = [];
        if (results.myLeaves && typeof results.myLeaves === 'object' && results.myLeaves.success && Array.isArray(results.myLeaves.data)) {
          myLeaves = results.myLeaves.data;
        } else if (Array.isArray(results.myLeaves)) {
          myLeaves = results.myLeaves;
        }
        this.myLeaves = myLeaves || [];

        // Load comp-off requests in background
        this.loadCompoffInBackground();

        this.loading = false;
        this.clearErrors();
      },
      error: (error) => {
        console.error('❌ Dynamic data loading failed:', error);
        this.loading = false;
        this.error = 'Failed to load leave data. Please refresh the page.';
      }
    });
  }

  // OPTIMIZED: Load comp-off requests in background and merge with existing leaves
  private loadCompoffInBackground(): void {
    console.log('🔄 Loading comp-off requests in background...');

    this.leaveService.getMyCompoffRequests().pipe(
      timeout(5000),
      catchError(error => {
        console.warn('⚠️ Comp-off requests failed in background:', error);
        return of([]);
      })
    ).subscribe({
      next: (compoffRequests) => {
        if (compoffRequests && compoffRequests.length > 0) {
          console.log('✅ Comp-off requests loaded in background:', compoffRequests.length);

          // Transform comp-off requests to Leave format
          const compoffAsLeaves: Leave[] = compoffRequests.map(compoff => ({
            id: compoff.id,
            employee_id: compoff.employee_id,
            leave_type_id: 999,
            leave_type: 'compensatory_off',
            start_date: compoff.working_date,
            end_date: compoff.working_date,
            days: 1,
            reason: compoff.reason,
            status: compoff.status as any,
            created_at: compoff.created_at,
            updated_at: compoff.updated_at,
            approved_by: compoff.approved_by,
            approved_at: compoff.approved_at,
            rejected_reason: compoff.rejection_reason
          }));

          // Merge with existing leaves
          this.myLeaves = [...this.myLeaves, ...compoffAsLeaves];
          console.log('✅ Merged comp-off requests with leaves:', this.myLeaves.length);
        }

        // Transform and display all data
        this.transformAndSortLeaveApplications();
      }
    });
  }

  // Helper method to check if a date is a holiday (using loaded holidays)
  private isHolidayDate(date: Date | string): boolean {
    if (!this.holidaysLoaded || this.holidays.length === 0) {
      return false;
    }

    const dateStr = typeof date === 'string' ? date : this.formatDate(date);
    return this.holidays.some(holiday =>
      this.formatDate(new Date(holiday.date)) === dateStr
    );
  }

  // Helper method to get holiday info for a date
  private getHolidayInfo(date: Date | string): NewHoliday | null {
    if (!this.holidaysLoaded || this.holidays.length === 0) {
      return null;
    }

    const dateStr = typeof date === 'string' ? date : this.formatDate(date);
    return this.holidays.find(holiday =>
      this.formatDate(new Date(holiday.date)) === dateStr
    ) || null;
  }

  // Helper method to log holidays in a date range
  private logHolidaysInRange(startDate: string, endDate: string): void {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const holidaysInRange: NewHoliday[] = [];

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const holiday = this.getHolidayInfo(date);
      if (holiday) {
        holidaysInRange.push(holiday);
      }
    }

    if (holidaysInRange.length > 0) {
      console.log(`🎉 Holidays in selected range (${startDate} to ${endDate}):`,
        holidaysInRange.map(h => `${h.name} (${h.date})`));
    }
  }

  // Helper method to format date to YYYY-MM-DD (avoiding timezone issues)
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // UNIVERSAL HOLIDAY VALIDATION - applies to ALL leave types (PL, SL, CL, LWP, OD, etc.)
  private checkHolidayRestrictionWithFreshData(): void {
    if (!this.fromDate || !this.toDate) {
      console.log('❌ No dates selected, clearing holiday restriction');
      this.hasHolidayRestriction = false;
      return;
    }

    console.log('🔄 ===== UNIVERSAL HOLIDAY VALIDATION STARTED =====');
    console.log(`📅 Selected dates: From ${this.fromDate} to ${this.toDate}`);
    console.log(`🎯 Leave type: ${this.selectedLeaveType} (UNIVERSAL validation applies to ALL types)`);
    console.log('🔄 Fetching fresh holiday data for UNIVERSAL validation...');

    // Fetch fresh holiday data
    this.calendarService.getHolidays().subscribe({
      next: (response: any) => {
        console.log('📊 Raw holiday API response:', response);

        // Parse the holiday response structure
        let freshHolidays: CalendarHoliday[] = [];
        if (response && response.success && response.data && response.data.new_year_activities) {
          // API returns: { success: true, data: { new_year_activities: [...] } }
          freshHolidays = response.data.new_year_activities;
          console.log(`📊 Parsed holidays from new_year_activities: ${freshHolidays.length} holidays`);
        } else if (Array.isArray(response)) {
          // Fallback: direct array response
          freshHolidays = response;
          console.log(`📊 Direct array response: ${freshHolidays.length} holidays`);
        } else {
          console.warn('⚠️ Unexpected holiday API response structure:', response);
          freshHolidays = [];
        }

        const fromDate = new Date(this.fromDate);
        const toDate = new Date(this.toDate);
        const holidayDates: NewHoliday[] = [];

        // Log all available holidays for debugging
        console.log('📅 Available holidays for validation:');
        freshHolidays.forEach((h, index) => {
          console.log(`  ${index + 1}. ${h.name} - ${h.date}`);
        });

        // For ALL leave types, only check start and end dates (not dates in between)
        console.log(`🎯 ${this.selectedLeaveType} detected: Only checking start and end dates for holidays (not in-between dates)`);

        // Check only FROM date
        const fromDateStr = this.formatDate(fromDate);
        const fromHoliday = freshHolidays.find(h =>
          this.formatDate(new Date(h.date)) === fromDateStr
        );
        if (fromHoliday) {
          // Convert CalendarHoliday to NewHoliday format
          const convertedHoliday: NewHoliday = {
            id: fromHoliday.id || '',
            name: fromHoliday.name,
            date: fromHoliday.date,
            month: new Date(fromHoliday.date).toLocaleDateString('en-US', { month: 'long' }).toLowerCase(),
            day: new Date(fromHoliday.date).getDate().toString(),
            source: 'calendar_api'
          };
          holidayDates.push(convertedHoliday);
          console.log(`🎉 Found holiday on FROM date: ${fromHoliday.name} on ${fromHoliday.date}`);
        }

        // Check only TO date
        const toDateStr = this.formatDate(toDate);
        const toHoliday = freshHolidays.find(h =>
          this.formatDate(new Date(h.date)) === toDateStr
        );
        if (toHoliday) {
          // Convert CalendarHoliday to NewHoliday format
          const convertedHoliday: NewHoliday = {
            id: toHoliday.id || '',
            name: toHoliday.name,
            date: toHoliday.date,
            month: new Date(toHoliday.date).toLocaleDateString('en-US', { month: 'long' }).toLowerCase(),
            day: new Date(toHoliday.date).getDate().toString(),
            source: 'calendar_api'
          };
          holidayDates.push(convertedHoliday);
          console.log(`🎉 Found holiday on TO date: ${toHoliday.name} on ${toHoliday.date}`);
        }

        console.log(`📊 ${this.selectedLeaveType} holiday check complete: ${holidayDates.length} holidays found on start/end dates only`);

        // Update restriction state
        this.hasHolidayRestriction = holidayDates.length > 0;
        console.log(`🎯 Setting hasHolidayRestriction = ${this.hasHolidayRestriction}`);

        if (this.hasHolidayRestriction) {
          console.log(`⚠️ UNIVERSAL Holiday restriction activated: Found ${holidayDates.length} holidays in selected range`);
          console.log(`🚨 SHOWING UNIVERSAL HOLIDAY POPUP FOR ${this.selectedLeaveType}...`);
          this.showHolidayRestrictionPopup(holidayDates);
          console.log('🚨 UNIVERSAL HOLIDAY POPUP SHOULD BE VISIBLE NOW');
        } else {
          console.log(`✅ No holidays found in selected date range for ${this.selectedLeaveType}`);
        }

        // Force UI update
        this.cdr.detectChanges();
        console.log('🔄 ===== UNIVERSAL HOLIDAY VALIDATION COMPLETED =====');
      },
      error: (error: any) => {
        console.error('❌ Error fetching fresh holiday data:', error);
        this.hasHolidayRestriction = false;
      }
    });
  }

  // Immediate weekend check for OD (synchronous)
  private checkImmediateWeekendRestriction(): void {
    if (!this.fromDate || !this.toDate || this.selectedLeaveType !== 'OD') {
      return;
    }

    const fromDate = new Date(this.fromDate);
    const toDate = new Date(this.toDate);
    let hasWeekends = false;

    // Check each date in the range for weekends
    for (let date = new Date(fromDate); date <= toDate; date.setDate(date.getDate() + 1)) {
      if (this.holidayService.isWeekend(date)) {
        hasWeekends = true;
        break;
      }
    }

    // Update restriction state immediately for weekends
    if (hasWeekends) {
      this.hasHolidayRestriction = true;
      console.log('⚠️ Immediate weekend restriction triggered for OD');
      this.cdr.detectChanges();
    }
  }

  // Check if OD is being applied on weekends or holidays with fresh holiday data
  private checkODWeekendHolidayRestrictionWithFreshData(): void {
    if (!this.fromDate || !this.toDate) {
      this.hasHolidayRestriction = false;
      return;
    }

    console.log('🔄 Fetching fresh holiday data for OD validation...');
    console.log(`📅 Selected dates: From ${this.fromDate} to ${this.toDate}`);

    // Fetch fresh holiday data every time (no cache)
    this.holidayService.getHolidays().subscribe({
      next: (freshHolidays) => {
        console.log(`✅ Fresh holiday data loaded: ${freshHolidays.length} holidays`);

        const fromDate = new Date(this.fromDate);
        const toDate = new Date(this.toDate);
        const weekendDates: string[] = [];
        const holidayDates: NewHoliday[] = [];

        // Check each date in the range
        for (let date = new Date(fromDate); date <= toDate; date.setDate(date.getDate() + 1)) {
          const dateStr = this.formatDate(date);
          console.log(`🔍 Checking date: ${dateStr} (${date.toDateString()})`);

          // Check if it's a weekend
          if (this.holidayService.isWeekend(date)) {
            const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
            weekendDates.push(`${dayName} (${dateStr})`);
            console.log(`📅 Weekend found: ${dayName} (${dateStr})`);
          }

          // Check if it's a holiday using fresh data
          console.log(`🔍 Checking against ${freshHolidays.length} holidays...`);
          freshHolidays.forEach(h => {
            const holidayDateStr = this.formatDate(new Date(h.date));
            console.log(`  Holiday: ${h.name} - API date: ${h.date} - Formatted: ${holidayDateStr} - Match: ${holidayDateStr === dateStr}`);
          });

          const holiday = freshHolidays.find(h =>
            this.formatDate(new Date(h.date)) === dateStr
          );
          if (holiday) {
            // Convert CalendarHoliday to NewHoliday format
            const convertedHoliday: NewHoliday = {
              id: holiday.id || '',
              name: holiday.name,
              date: holiday.date,
              month: new Date(holiday.date).toLocaleDateString('en-US', { month: 'long' }).toLowerCase(),
              day: new Date(holiday.date).getDate().toString(),
              source: 'calendar_api'
            };
            holidayDates.push(convertedHoliday);
            console.log(`🎉 Found holiday: ${holiday.name} on ${holiday.date}`);
          } else {
            console.log(`❌ No holiday found for ${dateStr}`);
          }
        }

        // Update restriction state and show popup if there are weekends or holidays
        this.hasHolidayRestriction = (weekendDates.length > 0 || holidayDates.length > 0);

        if (this.hasHolidayRestriction) {
          console.log(`⚠️ OD restriction triggered: ${weekendDates.length} weekends, ${holidayDates.length} holidays`);
          this.showODRestrictionPopup(weekendDates, holidayDates);
        } else {
          console.log('✅ No weekends or holidays found in OD date range - restriction cleared');
        }

        // Force change detection to update the UI immediately
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('❌ Error fetching fresh holiday data:', error);
        // Fallback to weekend-only checking if holiday API fails
        this.checkWeekendOnlyRestriction();
      }
    });
  }

  // Fallback method to check only weekends if holiday API fails (for ALL leave types)
  private checkWeekendOnlyRestriction(): void {
    if (!this.fromDate || !this.toDate) {
      return;
    }

    const fromDate = new Date(this.fromDate);
    const toDate = new Date(this.toDate);

    // Only check if FROM or TO date is a weekend (not dates in between)
    const isFromDateWeekend = this.calendarService.isWeekend(fromDate);
    const isToDateWeekend = this.calendarService.isWeekend(toDate);

    // Update restriction state and show popup if start or end date is weekend
    this.hasHolidayRestriction = isFromDateWeekend || isToDateWeekend;

    if (this.hasHolidayRestriction) {
      console.log(`⚠️ ${this.selectedLeaveType} weekend restriction triggered: Start date weekend: ${isFromDateWeekend}, End date weekend: ${isToDateWeekend} (holiday check failed)`);
      this.showWeekendHolidayRestrictionPopup([], []);
      this.cdr.detectChanges();
    }
  }

  // Check if OD has weekend or holiday dates (for submit button restriction)
  hasODWeekendOrHolidayDates(): boolean {
    if (this.selectedLeaveType !== 'OD' || !this.fromDate || !this.toDate) {
      return false;
    }

    const fromDate = new Date(this.fromDate);
    const toDate = new Date(this.toDate);

    // Only check if FROM or TO date is a weekend (not dates in between)
    const isFromDateWeekend = this.calendarService.isWeekend(fromDate);
    const isToDateWeekend = this.calendarService.isWeekend(toDate);

    if (isFromDateWeekend || isToDateWeekend) {
      return true;
    }

    // For holidays, we need to check against fresh data
    // Since this method needs to return synchronously, we'll use the cached holidays
    // but trigger a fresh fetch for validation
    this.validateODDatesWithFreshHolidays();

    // Check cached holidays for immediate response - only check start and end dates
    if (this.holidaysLoaded && this.holidays.length > 0) {
      const fromDateStr = this.formatDate(fromDate);
      const toDateStr = this.formatDate(toDate);

      const fromDateHoliday = this.holidays.find(h => this.formatDate(new Date(h.date)) === fromDateStr);
      const toDateHoliday = this.holidays.find(h => this.formatDate(new Date(h.date)) === toDateStr);

      if (fromDateHoliday || toDateHoliday) {
        return true;
      }
    }

    return false;
  }

  // Validate OD dates with fresh holiday data (async validation)
  private validateODDatesWithFreshHolidays(): void {
    if (this.selectedLeaveType !== 'OD' || !this.fromDate || !this.toDate) {
      return;
    }

    // Fetch fresh holidays and update the component state
    this.calendarService.getFreshHolidays().subscribe({
      next: (freshHolidays: CalendarHoliday[]) => {
        // Convert calendar holidays to the format expected by this component
        this.holidays = freshHolidays.map(h => ({
          id: h.id || '',
          name: h.name,
          date: h.date,
          month: new Date(h.date).toLocaleDateString('en-US', { month: 'long' }).toLowerCase(),
          day: new Date(h.date).getDate().toString(),
          source: 'calendar_api'
        }));
        this.holidaysLoaded = true;

        // Only check if FROM or TO date is a holiday (not dates in between)
        const fromDate = new Date(this.fromDate);
        const toDate = new Date(this.toDate);

        const fromDateStr = this.formatDate(fromDate);
        const toDateStr = this.formatDate(toDate);

        const fromDateHoliday = freshHolidays.find(h => this.formatDate(new Date(h.date)) === fromDateStr);
        const toDateHoliday = freshHolidays.find(h => this.formatDate(new Date(h.date)) === toDateStr);

        const hasHolidayOnStartOrEnd = !!(fromDateHoliday || toDateHoliday);

        // Check for weekends on start or end dates only
        const isFromDateWeekend = this.calendarService.isWeekend(fromDate);
        const isToDateWeekend = this.calendarService.isWeekend(toDate);
        const hasWeekendOnStartOrEnd = isFromDateWeekend || isToDateWeekend;

        // Update restriction state based on fresh data
        this.hasHolidayRestriction = hasHolidayOnStartOrEnd || hasWeekendOnStartOrEnd;

        if (hasHolidayOnStartOrEnd) {
          console.log(`⚠️ Fresh holiday validation: OD applied on holiday - From: ${fromDateHoliday ? fromDateHoliday.name : 'No holiday'}, To: ${toDateHoliday ? toDateHoliday.name : 'No holiday'}`);
        }
        if (hasWeekendOnStartOrEnd) {
          console.log(`⚠️ Fresh weekend validation: OD applied on weekend - From: ${isFromDateWeekend ? 'Weekend' : 'Weekday'}, To: ${isToDateWeekend ? 'Weekend' : 'Weekday'}`);
        }
      },
      error: (error) => {
        console.error('❌ Error fetching fresh holidays for validation:', error);
      }
    });
  }

  // Show popup for holiday restriction (UNIVERSAL - applies to ALL leave types including OD, LWP)
  private showHolidayRestrictionPopup(holidayDates: NewHoliday[]): void {
    // Prevent multiple popups from showing
    if (this.isPopupShowing) {
      console.log('🚫 Popup already showing, skipping holiday restriction popup');
      return;
    }

    this.isPopupShowing = true;
    const holidayNames = holidayDates.map(h => h.name).join(', ');

    Swal.fire({
      title: `Cannot Apply ${this.selectedLeaveType} on Holiday`,
      text: `The selected dates contain the following holiday(s): ${holidayNames}. Please select different dates.`,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#f39c12',
      customClass: {
        popup: 'swal2-popup-holiday-restriction'
      }
    }).then(() => {
      // Reset popup flag when popup is closed
      this.isPopupShowing = false;
    });
  }

  // Show popup for OD weekend/holiday restriction
  private showODRestrictionPopup(weekendDates: string[], holidayDates: NewHoliday[]): void {
    Swal.fire({
      title: 'Cannot Apply OD on Selected Dates',
      text: 'Outdoor Duty (OD) cannot be applied for weekends or holidays. Please select working days only.',
      icon: 'warning',
      confirmButtonColor: '#3F828B',
      confirmButtonText: 'I Understand'
    });
  }

  // Show popup for weekend/holiday restriction (generic for ALL leave types)
  private showWeekendHolidayRestrictionPopup(weekendDates: string[], holidayDates: NewHoliday[]): void {
    // Prevent multiple popups from showing
    if (this.isPopupShowing) {
      console.log('🚫 Popup already showing, skipping weekend/holiday restriction popup');
      return;
    }

    this.isPopupShowing = true;
    const leaveTypeName = this.selectedLeaveType === 'OD' ? 'Outdoor Duty (OD)' :
                         this.selectedLeaveType === 'PL' ? 'Privilege Leave (PL)' :
                         this.selectedLeaveType === 'CL' ? 'Casual Leave (CL)' :
                         this.selectedLeaveType === 'SL' ? 'Sick Leave (SL)' :
                         this.selectedLeaveType === 'LWP' ? 'Leave Without Pay (LWP)' : this.selectedLeaveType;

    Swal.fire({
      title: `Cannot Apply ${this.selectedLeaveType} on Selected Dates`,
      text: `${leaveTypeName} cannot be applied for weekends or holidays. Please select working days only.`,
      icon: 'warning',
      confirmButtonColor: '#3F828B',
      confirmButtonText: 'I Understand'
    }).then(() => {
      // Reset popup flag when popup is closed
      this.isPopupShowing = false;
    });
  }

  // Validate special leave types (LWP and OD) according to policy
  private validateSpecialLeaveTypes(): string | null {
    const fromDate = new Date(this.fromDate);
    const toDate = new Date(this.toDate);
    const today = new Date();

    // Set time to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    fromDate.setHours(0, 0, 0, 0);
    toDate.setHours(0, 0, 0, 0);

    switch (this.selectedLeaveType) {
      case 'OD': // Outdoor Duty validation
        // OD cannot be applied for weekends or holidays
        if (this.isWeekendOrHoliday(fromDate) || this.isWeekendOrHoliday(toDate)) {
          return 'Outdoor Duty (OD) cannot be applied for weekends or holidays. Please select working days only.';
        }

        // OD should be for official work only (check reason)
        if (!this.reason.trim()) {
          return 'Please provide a detailed reason for Outdoor Duty. It should be for official work only.';
        }

        // Suggest partial day OD for single day
        if (this.dayCount === 1) {
          // This is just informational, not blocking
          console.log('💡 Tip: For single day OD, consider applying for partial day if you\'re only out for part of the day.');
        }
        break;

      case 'LWP': // Leave Without Pay validation
        // LWP is typically applied when no other leave balance is available
        const hasOtherLeaveBalance = this.leaveBalances.some(balance =>
          balance.leaveType !== 'LWP' && balance.remaining > 0
        );

        if (hasOtherLeaveBalance) {
          const availableLeaves = this.leaveBalances
            .filter(balance => balance.leaveType !== 'LWP' && balance.remaining > 0)
            .map(balance => `${balance.leaveType} (${balance.remaining} days)`)
            .join(', ');

          return `You have available leave balance: ${availableLeaves}. LWP should only be used when no other leave is available. Consider using your available leave first.`;
        }

        // LWP calculation includes calendar days (weekends/holidays)
        if (this.dayCount > 1) {
          console.log('📅 Note: LWP is calculated on calendar days including weekends and holidays.');
        }

        // Warn about no salary during LWP
        console.log('⚠️ Important: During Leave Without Pay (LWP), no salary or allowance will be paid.');
        break;
    }

    return null; // No validation errors
  }

  // Helper method to check if a date is weekend or holiday
  private isWeekendOrHoliday(date: Date): boolean {
    // Check if it's weekend (Saturday = 6, Sunday = 0)
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return true;
    }

    // TODO: Add holiday checking logic here if you have a holiday calendar
    // For now, just check weekends
    return false;
  }

  // Create LWP and OD leave types using POST /api/v1/leave/types
  createSpecialLeaveTypes(): void {
    console.log('🔧 Creating special leave types: LWP and OD');

    // First check if leave types already exist
    this.leaveService.getLeaveTypes().subscribe({
      next: (response) => {
        console.log('📋 Checking existing leave types:', response);

        let existingTypes: any[] = [];
        if (response && response.success && Array.isArray(response.data)) {
          existingTypes = response.data;
        } else if (Array.isArray(response)) {
          existingTypes = response;
        }

        const existingNames = existingTypes.map(type => type.name?.toLowerCase());
        const lwpExists = existingNames.includes('leave_without_pay') || existingNames.includes('lwp');
        const odExists = existingNames.includes('outdoor_duty') || existingNames.includes('od');

        if (lwpExists && odExists) {
          Swal.fire({
            icon: 'info',
            title: '✅ Leave Types Already Exist',
            html: `
              <div class="alert alert-info">
                <h6 class="alert-heading">
                  <i class="fas fa-check-circle me-2"></i>Both leave types are already configured
                </h6>
                <ul class="mb-0">
                  <li><strong>Leave Without Pay (LWP)</strong> - Already exists</li>
                  <li><strong>Outdoor Duty (OD)</strong> - Already exists</li>
                </ul>
              </div>
            `,
            confirmButtonColor: '#3F828B',
            confirmButtonText: 'OK'
          });
          return;
        }

        // Show confirmation dialog before creating
        Swal.fire({
          title: '🔧 Create Special Leave Types',
          html: `
            <div class="text-start">
              <p class="mb-3">This will create the following leave types using <code>POST /api/v1/leave/types</code>:</p>
              <div class="alert alert-primary">
                <h6 class="alert-heading">Leave Types to Create:</h6>
                <ul class="mb-0">
                  ${!lwpExists ? '<li><strong>Leave Without Pay (LWP)</strong> - For when no other leave balance is available</li>' : ''}
                  ${!odExists ? '<li><strong>Outdoor Duty (OD)</strong> - For official work outside office premises</li>' : ''}
                  ${lwpExists ? '<li class="text-muted"><s>Leave Without Pay (LWP)</s> - Already exists</li>' : ''}
                  ${odExists ? '<li class="text-muted"><s>Outdoor Duty (OD)</s> - Already exists</li>' : ''}
                </ul>
              </div>
            </div>
          `,
          icon: 'question',
          showCancelButton: true,
          confirmButtonColor: '#3F828B',
          cancelButtonColor: '#6c757d',
          confirmButtonText: '<i class="fas fa-plus me-2"></i>Create Leave Types',
          cancelButtonText: '<i class="fas fa-times me-2"></i>Cancel'
        }).then((result) => {
          if (result.isConfirmed) {
            this.performLeaveTypeCreation(lwpExists, odExists);
          }
        });
      },
      error: (error) => {
        console.error('❌ Error checking existing leave types:', error);
        // Proceed with creation anyway
        this.performLeaveTypeCreation(false, false);
      }
    });
  }

  // Perform the actual leave type creation
  private performLeaveTypeCreation(lwpExists: boolean, odExists: boolean): void {
    // Show loading dialog
    Swal.fire({
      title: '🔧 Creating Leave Types',
      html: `
        <div class="text-center">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mb-0">Creating leave types via POST /api/v1/leave/types...</p>
          <small class="text-muted">Please wait while we set up the leave types</small>
        </div>
      `,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      customClass: {
        popup: 'swal2-popup-loading'
      }
    });

    // Define the leave types to create (only create those that don't exist)
    const leaveTypesToCreate = [];

    if (!lwpExists) {
      leaveTypesToCreate.push({
        name: 'leave_without_pay',
        description: 'Leave Without Pay - Applied when no other leave balance is available. No salary will be paid during this leave.',
        max_days_per_year: undefined, // Unlimited
        is_active: true
      });
    }

    if (!odExists) {
      leaveTypesToCreate.push({
        name: 'outdoor_duty',
        description: 'Outdoor Duty - For official work outside office premises. Cannot be used for weekends or holidays.',
        max_days_per_year: undefined, // Unlimited
        is_active: true
      });
    }

    if (leaveTypesToCreate.length === 0) {
      Swal.fire({
        icon: 'info',
        title: '✅ No Action Needed',
        text: 'All required leave types already exist.',
        confirmButtonColor: '#3F828B'
      });
      return;
    }

    // Create both leave types
    const createRequests = leaveTypesToCreate.map(leaveType =>
      this.leaveService.createLeaveType(leaveType).pipe(
        catchError(error => {
          console.error(`Error creating ${leaveType.name}:`, error);
          return of({ error: true, leaveType: leaveType.name, details: error });
        })
      )
    );

    // Execute all creation requests
    forkJoin(createRequests).subscribe({
      next: (results) => {
        console.log('✅ Leave type creation results:', results);

        const successful = results.filter(result => !(result as any).error);
        const failed = results.filter(result => (result as any).error);

        // Show results
        Swal.fire({
          icon: successful.length === 2 ? 'success' : 'warning',
          title: '🎯 Leave Types Setup Complete',
          html: `
            <div class="text-start">
              ${successful.length > 0 ? `
                <div class="alert alert-success mb-3">
                  <h6 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>Successfully Created (${successful.length}/2)
                  </h6>
                  <ul class="mb-0 small">
                    ${successful.map((result: any) => `
                      <li><strong>${this.getLeaveTypeDisplayName(result.name || 'Unknown')}</strong> - ${result.description || 'No description'}</li>
                    `).join('')}
                  </ul>
                </div>
              ` : ''}

              ${failed.length > 0 ? `
                <div class="alert alert-warning mb-3">
                  <h6 class="alert-heading">
                    <i class="fas fa-exclamation-triangle me-2"></i>Failed to Create (${failed.length}/2)
                  </h6>
                  <ul class="mb-0 small">
                    ${failed.map((result: any) => `
                      <li><strong>${this.getLeaveTypeDisplayName(result.leaveType)}</strong> - ${this.extractErrorMessage(result.details)}</li>
                    `).join('')}
                  </ul>
                </div>
              ` : ''}

              <div class="alert alert-info mb-0">
                <h6 class="alert-heading">
                  <i class="fas fa-info-circle me-2"></i>Next Steps
                </h6>
                <ul class="mb-0 small">
                  <li>Refresh the page to see the new leave types</li>
                  <li>Check leave balance allocation for employees</li>
                  <li>Verify leave type policies are working correctly</li>
                </ul>
              </div>
            </div>
          `,
          confirmButtonColor: '#3F828B',
          confirmButtonText: '<i class="fas fa-sync-alt me-2"></i>Refresh Page',
          showCancelButton: true,
          cancelButtonColor: '#6c757d',
          cancelButtonText: '<i class="fas fa-times me-2"></i>Close',
          width: '600px'
        }).then((result) => {
          if (result.isConfirmed) {
            // Refresh the page to load new leave types
            window.location.reload();
          }
        });
      },
      error: (error) => {
        console.error('❌ Error creating leave types:', error);
        Swal.fire({
          icon: 'error',
          title: '❌ Failed to Create Leave Types',
          html: `
            <div class="text-start">
              <div class="alert alert-danger mb-3">
                <h6 class="alert-heading">
                  <i class="fas fa-exclamation-triangle me-2"></i>Creation Failed
                </h6>
                <p class="mb-0">Failed to create LWP and OD leave types using POST /api/v1/leave/types</p>
              </div>
              <div class="alert alert-info mb-0">
                <h6 class="alert-heading">
                  <i class="fas fa-tools me-2"></i>Troubleshooting
                </h6>
                <ul class="mb-0 small">
                  <li>Check if the API endpoint is available</li>
                  <li>Verify authentication and permissions</li>
                  <li>Check if leave types already exist</li>
                  <li>Review server logs for detailed error information</li>
                </ul>
              </div>
            </div>
          `,
          confirmButtonColor: '#dc3545',
          confirmButtonText: '<i class="fas fa-redo me-2"></i>Try Again',
          showCancelButton: true,
          cancelButtonColor: '#6c757d',
          cancelButtonText: '<i class="fas fa-times me-2"></i>Close'
        }).then((result) => {
          if (result.isConfirmed) {
            this.createSpecialLeaveTypes(); // Retry
          }
        });
      }
    });
  }

  // Helper method to get display name for leave types
  private getLeaveTypeDisplayName(leaveTypeName: string): string {
    const displayNames: { [key: string]: string } = {
      'leave_without_pay': 'Leave Without Pay (LWP)',
      'outdoor_duty': 'Outdoor Duty (OD)',
      'privilege_leave': 'Privilege Leave (PL)',
      'sick_leave': 'Sick Leave (SL)',
      'casual_leave': 'Casual Leave (CL)',
      'compensatory_off': 'Compensatory Off (COMP)'
    };
    return displayNames[leaveTypeName] || leaveTypeName;
  }

  // Helper method to extract error message
  private extractErrorMessage(error: any): string {
    if (error?.error?.detail) {
      return error.error.detail;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'Unknown error occurred';
  }



  // Method to submit the leave application
  submitLeaveApplication(modal: any): void {
    // Basic frontend validation - only check required fields
    if (!this.selectedLeaveTypeId || !this.fromDate || !this.toDate || !this.reason.trim()) {
      this.error = 'Please fill in all required fields.';
      return;
    }

    // Removed dayCount validation - let backend handle all date validation

    // Check if we have the current employee UUID
    if (!this.currentEmployeeUuid) {
      this.error = 'Employee information not found. Please refresh the page and try again.';
      return;
    }

    // Get current user information
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      this.error = 'User not authenticated. Please log in again.';
      return;
    }

    // Note: Removed frontend validation for leave balance, weekends, holidays, etc.
    // All business logic validation will be handled by the backend API
    console.log('📝 Frontend validation passed, submitting to backend for full validation...');

    this.submitting = true;
    this.error = null;

    // Find the selected leave type details
    // Ensure leaveTypes is an array before calling find
    console.log('Current leaveTypes:', this.leaveTypes);
    console.log('leaveTypes type:', typeof this.leaveTypes);
    console.log('Is leaveTypes array:', Array.isArray(this.leaveTypes));

    const leaveTypesArray = Array.isArray(this.leaveTypes) ? this.leaveTypes : [];
    console.log('Using leaveTypesArray:', leaveTypesArray);

    const selectedLeaveTypeDetails = leaveTypesArray.find(lt => lt.id === this.selectedLeaveTypeId);
    console.log('Selected leave type details:', selectedLeaveTypeDetails);

    // Get the leave type enum value for API
    const leaveTypeEnum = this.leaveService.getLeaveTypeEnum(this.selectedLeaveType);
    console.log('Leave type enum:', leaveTypeEnum);

    console.log('Using employee UUID:', this.currentEmployeeUuid);
    console.log('Employee UUID type:', typeof this.currentEmployeeUuid);
    console.log('Employee UUID is valid:', this.currentEmployeeUuid ? this.isValidUUID(this.currentEmployeeUuid) : false);

    // Validate that we have a proper UUID before submitting
    if (!this.currentEmployeeUuid) {
      console.error('❌ No employee UUID available for leave application');
      this.error = 'Unable to identify employee. Please refresh the page and try again.';
      this.submitting = false;
      return;
    }

    if (!this.isValidUUID(this.currentEmployeeUuid)) {
      console.error('❌ Employee UUID is not in valid format:', this.currentEmployeeUuid);
      this.error = 'Invalid employee identifier format. Please contact support.';
      this.submitting = false;
      return;
    }

    const leaveData: LeaveCreate = {
      leave_type_id: this.selectedLeaveTypeId,
      employee_id: this.currentEmployeeUuid, // Use the actual employee UUID (validated)
      leave_type: leaveTypeEnum, // API expects enum string value
      start_date: this.fromDate,
      end_date: this.toDate,
      reason: this.reason.trim()
    };

    console.log('🚀 Submitting leave application to POST /api/v1/leave/');
    console.log('📋 Leave Data:', leaveData);
    console.log('👤 Current User:', currentUser);
    console.log('🆔 Employee UUID:', this.currentEmployeeUuid);
    console.log('📅 Date Range:', `${this.fromDate} to ${this.toDate} (${this.dayCount} days)`);

    // Use HttpErrorHandlerService to skip automatic error handling
    this.leaveService.createLeaveWithCustomErrorHandling(leaveData).subscribe({
      next: (response) => {
        console.log('✅ Leave application submitted successfully to POST /api/v1/leave/');
        console.log('📄 API Response:', response);
        this.submitting = false;

        // Clear any errors and close the modal
        this.clearErrors();
        modal.close();
        this.modalRef = null; // Reset modal reference

        // Note: Leave service will automatically trigger reload of all components
        // No need to manually reload data as fresh data will be loaded

        // Show success message with SweetAlert
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Leave application submitted successfully!',
          timer: 3000,
          showConfirmButton: false
        });
      },
      error: (error) => {
        console.error('❌ Error submitting leave application to POST /api/v1/leave/');
        console.error('🔍 Error Details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url,
          error: error.error
        });
        this.submitting = false;

        // Extract user-friendly error message
        const userFriendlyError = this.extractLeaveApplicationError(error);
        this.error = userFriendlyError;

        // Show error message in the modal (don't use SweetAlert to avoid conflict)
        console.log('Showing error in modal:', userFriendlyError);
      }
    });
  }

  // Initialize table data with sample data
  initializeTableData(): void {
    // Sample data for demonstration (common employee code with leave policy codes)
    this.leaveApplications = [
      {
        id: 'LA-2024-001',
        employeeCode: 'EMP001',
        employeeName: 'John Doe',
        leaveType: 'PL',
        startDate: '2024-01-15',
        endDate: '2024-01-17',
        totalLeaves: 3,
        reason: 'Family vacation',
        status: 'Approved',
        approvedDate: '2024-01-10',
        approverName: 'Jane Smith'
      },
      {
        id: 'LA-2024-002',
        employeeCode: 'EMP001',
        employeeName: 'John Doe',
        leaveType: 'LWP',
        startDate: '2024-01-20',
        endDate: '2024-01-22',
        totalLeaves: 3,
        reason: 'Leave without pay for personal work',
        status: 'Pending',
        approvedDate: '',
        approverName: ''
      },
      {
        id: 'LA-2024-003',
        employeeCode: 'EMP001',
        employeeName: 'John Doe',
        leaveType: 'OD',
        startDate: '2024-01-25',
        endDate: '2024-01-25',
        totalLeaves: 1,
        reason: 'Client site visit for project work',
        status: 'Rejected',
        approvedDate: '2024-01-23',
        approverName: 'Mike Brown'
      },
      {
        id: 'LA-2024-004',
        employeeCode: 'EMP001',
        employeeName: 'John Doe',
        leaveType: 'WFH',
        startDate: '2024-02-01',
        endDate: '2024-02-01',
        totalLeaves: 1,
        reason: 'Work from home due to personal reasons',
        status: 'Approved',
        approvedDate: '2024-01-30',
        approverName: 'Jane Smith'
      },
      {
        id: 'LA-2024-005',
        employeeCode: 'EMP001',
        employeeName: 'John Doe',
        leaveType: 'COMPOFF',
        startDate: '2024-02-05',
        endDate: '2024-02-07',
        totalLeaves: 3,
        reason: 'Compensatory off for weekend work',
        status: 'Pending',
        approvedDate: '',
        approverName: ''
      }
    ];

    this.filteredApplications = [...this.leaveApplications];
    this.updatePagination();
  }

  // Filter table data based on search term and status
  filterTable(): void {
    console.log('🔍 Filtering table data...');
    console.log(`📊 Source: ${this.leaveApplications.length} applications, Search: "${this.searchTerm}", Status: "${this.statusFilter}"`);

    // Debug: Log unique status values in the data
    if (this.debugMode && this.leaveApplications.length > 0) {
      const uniqueStatuses = [...new Set(this.leaveApplications.map(app => app.status))];
      console.log('📋 Unique status values in data:', uniqueStatuses);
    }

    let filtered = [...this.leaveApplications];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(app =>
        app.employeeCode.toLowerCase().includes(searchLower) ||
        app.employeeName.toLowerCase().includes(searchLower) ||
        app.leaveType.toLowerCase().includes(searchLower) ||
        app.reason.toLowerCase().includes(searchLower) ||
        app.status.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter (only filter if not 'all') - case-insensitive comparison
    if (this.statusFilter && this.statusFilter !== 'all') {
      const beforeStatusFilter = filtered.length;
      filtered = filtered.filter(app => {
        const match = app.status.toLowerCase() === this.statusFilter.toLowerCase();
        if (this.debugMode && !match) {
          console.log(`🔍 Status filter mismatch: "${app.status}" !== "${this.statusFilter}"`);
        }
        return match;
      });

      if (this.debugMode) {
        console.log(`🔍 Status filter "${this.statusFilter}": ${beforeStatusFilter} -> ${filtered.length} applications`);
      }
    }

    this.filteredApplications = filtered;
    console.log(`✅ Filtered result: ${this.filteredApplications.length} applications`);

    this.currentPage = 1; // Reset to first page
    this.updatePagination();
  }

  // Update pagination
  updatePagination(): void {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedApplications = this.filteredApplications.slice(startIndex, endIndex);

    console.log(`� Pagination: Page ${this.currentPage}, Size ${this.pageSize}, Showing ${this.paginatedApplications.length} of ${this.filteredApplications.length}`);
  }

  // Handle search input change
  onSearchChange(): void {
    this.filterTable();
  }

  // Handle status filter change
  onStatusFilterChange(): void {
    console.log(`🔄 Status filter changed to: "${this.statusFilter}"`);
    this.filterTable();
  }

  // Handle page size change
  onPageSizeChange(): void {
    this.currentPage = 1; // Reset to first page
    this.updatePagination();
  }

  // Get status badge class
  getStatusClass(status: string): string {
    switch (status) {
      case 'Approved': return 'bg-success';
      case 'Pending': return 'bg-warning';
      case 'Rejected': return 'bg-danger';
      case 'Cancelled': return 'bg-secondary';
      default: return 'bg-secondary';
    }
  }

  // Get status icon
  getStatusIcon(status: string): string {
    switch (status) {
      case 'Approved': return 'check-circle';
      case 'Pending': return 'clock';
      case 'Rejected': return 'x-circle';
      case 'Cancelled': return 'slash';
      default: return 'help-circle';
    }
  }



  // Get leave type badge class (matching leave policy colors)
  getLeaveTypeBadgeClass(leaveType: string): string {
    switch (leaveType) {
      case 'PL': return 'bg-primary text-white';        // Blue - Privilege Leave
      case 'LWP': return 'bg-warning text-dark';        // Yellow - Leave Without Pay
      case 'OD': return 'bg-info text-white';           // Light Blue - Outdoor Duty
      case 'WFH': return 'bg-success text-white';       // Green - Work From Home
      case 'COMPOFF': return 'bg-secondary text-white'; // Gray - Compensatory Off
      default: return 'bg-light text-dark';             // Default
    }
  }

  // Pagination methods
  getTotalPages(): number {
    const totalPages = Math.ceil(this.filteredApplications.length / this.pageSize);
    console.log('📊 getTotalPages - Filtered applications:', this.filteredApplications.length, 'Page size:', this.pageSize, 'Total pages:', totalPages);
    return totalPages;
  }

  getStartIndex(): number {
    return (this.currentPage - 1) * this.pageSize;
  }

  getEndIndex(): number {
    const endIndex = this.currentPage * this.pageSize;
    return Math.min(endIndex, this.filteredApplications.length);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];

    console.log('📄 getPageNumbers - Total pages:', totalPages, 'Filtered applications:', this.filteredApplications.length, 'Page size:', this.pageSize);

    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }

    console.log('📄 Generated page numbers:', pages);
    return pages;
  }

  goToPage(page: number): void {
    console.log('🔄 goToPage called with page:', page, 'Current page:', this.currentPage, 'Total pages:', this.getTotalPages());
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.updatePagination();
      console.log('✅ Page changed to:', this.currentPage);
    } else {
      console.log('❌ Invalid page number:', page);
    }
  }

  // TrackBy function for pagination
  trackByPageNumber(_index: number, page: number): number {
    return page;
  }

  // Cancel leave application method
  cancelLeaveApplication(application: LeaveApplicationDisplay): void {
    console.log('🗑️ Cancel leave application:', application);

    // Check if application can be cancelled
    if (application.status !== 'Pending') {
      Swal.fire({
        icon: 'error',
        title: 'Cannot Cancel',
        text: 'Only pending leave applications can be cancelled.',
        confirmButtonColor: '#3085d6',
        timer: 3000,
        timerProgressBar: true
      });
      return;
    }

    // Enhanced confirmation dialog with better UI
    Swal.fire({
      title: 'Cancel Leave Application',
      html: `
        <div class="text-start">


          <!-- Leave Details Section -->
          <div style="background: #f8f9fa; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
            <h6 style="margin: 0 0 16px 0; color: #495057; font-size: 15px; font-weight: 600; border-bottom: 2px solid #dee2e6; padding-bottom: 8px;">
              <i class="fas fa-calendar-alt" style="color: #6c757d; margin-right: 8px;"></i>
              Leave Application Details
            </h6>

            <!-- Employee Information -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px; margin-bottom: 12px;">
              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #3F828B;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Employee</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.employeeName}</p>
              </div>

              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #DF5517;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Leave Type</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.leaveType}</p>
              </div>
            </div>

            <!-- Duration Information -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px;">
              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #28a745;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">From Date</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.startDate}</p>
              </div>

              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #dc3545;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">To Date</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.endDate}</p>
              </div>

              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #6f42c1;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Total Days</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.totalLeaves} day(s)</p>
              </div>
            </div>
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: '<i class="fas fa-trash me-2"></i>Yes, Cancel Leave',
      cancelButtonText: '<i class="fas fa-times me-2"></i>Keep Application',
      input: 'textarea',
      inputPlaceholder: 'Reason for cancellation (optional)...',
      inputAttributes: {
        'aria-label': 'Cancellation reason',
        'class': 'form-control',
        'rows': '3'
      },
      customClass: {
        popup: 'swal2-popup-large swal2-enhanced-modal',
        input: 'swal2-textarea-custom'
      },
      width: '600px',
      preConfirm: (reason) => {
        return reason?.trim() || 'Cancelled by employee';
      }
    }).then((result) => {
      if (result.isConfirmed) {
        this.performLeaveCancel(application.id, result.value);
      }
    });
  }

  // Perform the actual leave cancellation API call
  private performLeaveCancel(leaveId: string, reason: string): void {
    console.log('🗑️ Performing leave cancellation with DELETE request:', { leaveId, reason });

    // Enhanced loading dialog
    Swal.fire({
      title: '🗑️ Cancelling Leave Application',
      html: `
        <div class="text-center">
          <div class="spinner-border text-warning mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mb-0">Please wait while we process your cancellation request...</p>
        </div>
      `,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      customClass: {
        popup: 'swal2-popup-loading'
      }
    });

    this.leaveService.cancelLeave(leaveId, reason).subscribe({
      next: (response) => {
        console.log('✅ Leave cancelled successfully with DELETE:', response);

        // Enhanced success dialog with refresh information
        Swal.fire({
          icon: 'success',
          title: '🎉 Leave Application Cancelled!',
          html: `
            <div class="text-start">
              <!-- Success Message -->
              <div style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #28a745; border-radius: 8px; padding: 16px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(40, 167, 69, 0.1);">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <i class="fas fa-check-circle" style="color: #155724; font-size: 18px; margin-right: 10px;"></i>
                  <strong style="color: #155724; font-size: 15px;">Success</strong>
                </div>
                <p style="margin: 0; color: #155724; font-size: 14px; line-height: 1.4;">
                  Your leave application has been successfully cancelled and removed from the system.
                </p>
              </div>


              <!-- Refresh Notice -->

          `,
          confirmButtonColor: '#28a745',
          confirmButtonText: '<i class="fas fa-check me-2"></i>Got it!',
          timer: 6000,
          timerProgressBar: true,
          width: '550px',
          customClass: {
            popup: 'swal2-popup-large'
          }
        });

        // Note: Leave service will automatically trigger reload of all components
        // No need to manually refresh data as fresh data will be loaded
      },
      error: (error) => {
        console.error('❌ Error cancelling leave with DELETE:', error);

        let errorMessage = 'Failed to cancel leave application. Please try again.';
        let errorDetails = '';

        // Extract user-friendly error message
        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        // Add technical details for debugging
        if (error.status) {
          errorDetails = `HTTP ${error.status}: ${error.statusText || 'Unknown error'}`;
        }

        Swal.fire({
          icon: 'error',
          title: '❌ Cancellation Failed',
          html: `
            <div class="text-start">
              <div class="alert alert-danger mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error:</strong> ${errorMessage}
              </div>
              ${errorDetails ? `<p class="text-muted small mb-2"><strong>Technical Details:</strong> ${errorDetails}</p>` : ''}
              <p class="mb-0">Please try again or contact support if the problem persists.</p>
            </div>
          `,
          confirmButtonColor: '#dc3545',
          confirmButtonText: '<i class="fas fa-redo me-2"></i>Try Again',
          width: '500px'
        });
      }
    });
  }

  // Check if current user can reject leaves (admin/approver functionality)
  canRejectLeave(): boolean {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) return false;

    return currentUser.is_superuser === true ||
           (currentUser.permissions?.includes('approve_leaves') ?? false) ||
           (currentUser.permissions?.includes('*') ?? false);
  }

  // Simplified admin check method
  isAdmin(): boolean {
    const currentUser = this.authService.currentUserValue;
    return currentUser?.is_superuser === true;
  }

  // View leave details method
  viewLeaveDetails(application: LeaveApplicationDisplay): void {
    console.log('👁️ Viewing leave details:', application);

    // Determine status-specific information
    let statusInfo = '';
    let statusIcon = '';
    let statusColor = '';

    switch (application.status) {
      case 'Approved':
        statusInfo = application.approverName ?
          `<p><strong>Approved by:</strong> ${application.approverName}</p>` : '';
        statusIcon = '✅';
        statusColor = '#28a745';
        break;
      case 'Rejected':
        statusInfo = application.approverName ?
          `<p><strong>Rejected by:</strong> ${application.approverName}</p>` : '';
        statusIcon = '❌';
        statusColor = '#dc3545';
        break;
      case 'Cancelled':
        statusInfo = '<p><strong>Note:</strong> This leave application was cancelled by the employee</p>';
        statusIcon = '🗑️';
        statusColor = '#6c757d';
        break;
      case 'Pending':
        statusInfo = '<p><strong>Note:</strong> This leave application is awaiting approval</p>';
        statusIcon = '⏳';
        statusColor = '#ffc107';
        break;
      default:
        statusIcon = 'ℹ️';
        statusColor = '#17a2b8';
    }

    Swal.fire({
      title: ` Leave Application Details`,
      html: `
        <div class="text-start">
          <!-- Leave Details Section -->
          <div style="background: #f8f9fa; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
            <h6 style="margin: 0 0 16px 0; color: #495057; font-size: 15px; font-weight: 600; border-bottom: 2px solid #dee2e6; padding-bottom: 8px;">
              <i class="fas fa-calendar-alt" style="color: #6c757d; margin-right: 8px;"></i>
              Leave Application Details
            </h6>

            <!-- Employee Information -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px; margin-bottom: 12px;">
              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #3F828B;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Employee</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.employeeName}</p>
                <p style="margin: 2px 0 0 0; font-size: 12px; color: #6c757d;">(${application.employeeCode})</p>
              </div>

              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #DF5517;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Leave Type</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.leaveType}</p>
              </div>
            </div>

            <!-- Duration Information -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px; margin-bottom: 12px;">
              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #28a745;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">From Date</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.startDate}</p>
              </div>

              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #dc3545;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">To Date</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.endDate}</p>
              </div>

              <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #6f42c1;">
                <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Total Days</p>
                <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.totalLeaves} day(s)</p>
              </div>
            </div>

            <!-- Reason Section -->
            <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #17a2b8;">
              <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Reason for Leave</p>
              <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600; line-height: 1.4;">${application.reason}</p>
            </div>
          </div>

          <!-- Status Information Section -->
          <div style="background: #f8f9fa; border-radius: 8px; padding: 16px;">
            <h6 style="margin: 0 0 16px 0; color: #495057; font-size: 15px; font-weight: 600; border-bottom: 2px solid #dee2e6; padding-bottom: 8px;">
              <i class="fas fa-info-circle" style="color: #6c757d; margin-right: 8px;"></i>
              Status Information
            </h6>

            <!-- Status Badge -->
            <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid ${statusColor}; margin-bottom: 12px;">
              <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Current Status</p>
              <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">
                <span class="badge ${this.getStatusClass(application.status)}" style="font-size: 12px;">${application.status}</span>
              </p>
            </div>

            ${application.approvedDate ? `
            <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid #ffc107; margin-bottom: 12px;">
              <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Date Processed</p>
              <p style="margin: 4px 0 0 0; font-size: 14px; color: #495057; font-weight: 600;">${application.approvedDate}</p>
            </div>
            ` : ''}

            ${statusInfo ? `
            <div style="background: #fff; border-radius: 6px; padding: 12px; border-left: 4px solid ${statusColor};">
              <p style="margin: 0; font-size: 12px; color: #6c757d; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Additional Information</p>
              <div style="margin: 4px 0 0 0; font-size: 14px; color: #495057; line-height: 1.4;">${statusInfo}</div>
            </div>
            ` : ''}
          </div>
        </div>
      `,
      icon: 'info',
      confirmButtonText: '<i class="fas fa-times me-2"></i>Close',
      confirmButtonColor: '#3085d6',
      width: '650px',
      customClass: {
        popup: 'swal2-popup-large swal2-enhanced-modal'
      }
    });
  }

  // Delete leave type method (for admin functionality)
  deleteLeaveType(leaveTypeId: number): void {
    console.log('Delete leave type:', leaveTypeId);

    Swal.fire({
      title: 'Delete Leave Type?',
      text: 'Are you sure you want to delete this leave type? This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Delete',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.performLeaveTypeDelete(leaveTypeId);
      }
    });
  }

  // Perform the actual leave type deletion API call
  private performLeaveTypeDelete(leaveTypeId: number): void {
    console.log('Performing leave type deletion:', leaveTypeId);

    // Show loading
    Swal.fire({
      title: 'Deleting Leave Type...',
      text: 'Please wait while we delete the leave type.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.leaveService.deleteLeaveType(leaveTypeId).subscribe({
      next: (response) => {
        console.log('✅ Leave type deleted successfully:', response);

        Swal.fire({
          icon: 'success',
          title: 'Leave Type Deleted!',
          text: 'The leave type has been deleted successfully.',
          confirmButtonColor: '#28a745'
        });

        // Refresh the leave types data
        this.loadLeaveData();
      },
      error: (error) => {
        console.error('❌ Error deleting leave type:', error);

        let errorMessage = 'Failed to delete leave type. Please try again.';

        // Extract user-friendly error message
        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          icon: 'error',
          title: 'Deletion Failed',
          text: errorMessage,
          confirmButtonColor: '#3085d6'
        });
      }
    });
  }

  // Check if current user can cancel a specific leave application
  canCancelLeave(application: LeaveApplicationDisplay): boolean {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) return false;

    // User can cancel their own pending leaves
    // Check if the leave belongs to the current user
    return application.status === 'Pending' &&
           (application.employeeCode === (currentUser as any)?.employee_code ||
            application.employeeName === `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() ||
            application.employeeName === currentUser.name);
  }

  // Approve leave application method
  approveApplication(application: LeaveApplicationDisplay): void {
    console.log('Approve application:', application);

    // Check if application can be approved
    if (application.status !== 'Pending') {
      Swal.fire({
        icon: 'error',
        title: 'Cannot Approve',
        text: 'Only pending leave applications can be approved.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    // Confirm approval with SweetAlert
    Swal.fire({
      title: 'Approve Leave Application?',
      text: `Are you sure you want to approve ${application.employeeName}'s leave application from ${application.startDate} to ${application.endDate}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Approve',
      cancelButtonText: 'Cancel',
      input: 'textarea',
      inputPlaceholder: 'Approval comments (optional)...',
      inputAttributes: {
        'aria-label': 'Approval comments'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const comments = result.value?.trim() || 'Approved by admin';
        this.performLeaveApproval(application.id, comments);
      }
    });
  }

  // Perform the actual leave approval API call
  private performLeaveApproval(leaveId: string, comments: string): void {
    console.log('Performing leave approval:', { leaveId, comments });

    // Show loading
    Swal.fire({
      title: 'Approving Leave...',
      text: 'Please wait while we approve the leave application.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.leaveService.approveLeave(leaveId, comments).subscribe({
      next: (response) => {
        console.log('✅ Leave approved successfully:', response);

        Swal.fire({
          icon: 'success',
          title: 'Leave Approved!',
          text: 'The leave application has been approved successfully.',
          confirmButtonColor: '#28a745'
        });

        // Refresh the leave data to show updated status
        this.loadMyLeaves();
      },
      error: (error) => {
        console.error('❌ Error approving leave:', error);

        let errorMessage = 'Failed to approve leave application. Please try again.';

        // Extract user-friendly error message
        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          icon: 'error',
          title: 'Approval Failed',
          text: errorMessage,
          confirmButtonColor: '#3085d6'
        });
      }
    });
  }

  // Action method
  rejectApplication(application: LeaveApplicationDisplay): void {
    console.log('Reject application:', application);

    // Check if application can be rejected
    if (application.status === 'Approved') {
      Swal.fire({
        icon: 'error',
        title: 'Cannot Reject',
        text: 'Approved applications cannot be rejected.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    if (application.status === 'Rejected') {
      Swal.fire({
        icon: 'info',
        title: 'Already Rejected',
        text: 'This application is already rejected.',
        confirmButtonColor: '#3085d6'
      });
      return;
    }

    // Confirm rejection with SweetAlert
    Swal.fire({
      title: 'Reject Leave Application?',
      text: `Are you sure you want to reject ${application.employeeName}'s leave application?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, Reject',
      cancelButtonText: 'Cancel',
      input: 'textarea',
      inputPlaceholder: 'Reason for rejection (required)...',
      inputAttributes: {
        'aria-label': 'Rejection reason'
      },
      inputValidator: (value) => {
        if (!value || value.trim().length === 0) {
          return 'Please provide a reason for rejection';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const reason = result.value.trim();
        this.performLeaveRejection(application.id, reason);
      }
    });
  }

  // Perform the actual leave rejection API call
  private performLeaveRejection(leaveId: string, reason: string): void {
    console.log('Performing leave rejection:', { leaveId, reason });

    // Show loading
    Swal.fire({
      title: 'Rejecting Leave...',
      text: 'Please wait while we process the rejection.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.leaveService.rejectLeave(leaveId, reason).subscribe({
      next: (response) => {
        console.log('✅ Leave rejected successfully:', response);

        Swal.fire({
          icon: 'success',
          title: 'Leave Rejected!',
          text: 'The leave application has been rejected successfully.',
          confirmButtonColor: '#28a745'
        });

        // Refresh the leave data to show updated status
        this.loadMyLeaves();
      },
      error: (error) => {
        console.error('❌ Error rejecting leave:', error);

        let errorMessage = 'Failed to reject leave application. Please try again.';

        // Extract user-friendly error message
        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          icon: 'error',
          title: 'Rejection Failed',
          text: errorMessage,
          confirmButtonColor: '#3085d6'
        });
      }
    });
  }

  // Add new leave type method
  addNewLeaveType(): void {
    console.log('Add new leave type');

    Swal.fire({
      title: 'Add New Leave Type',
      html: `
        <div class="text-start">
          <div class="mb-3">
            <label for="leaveTypeName" class="form-label">Leave Type Name</label>
            <input type="text" id="leaveTypeName" class="form-control" placeholder="e.g., PL, SL, CL">
          </div>
          <div class="mb-3">
            <label for="leaveTypeDesc" class="form-label">Description</label>
            <input type="text" id="leaveTypeDesc" class="form-control" placeholder="e.g., Privilege Leave">
          </div>
          <div class="mb-3">
            <label for="maxDays" class="form-label">Max Days Per Year</label>
            <input type="number" id="maxDays" class="form-control" placeholder="e.g., 21" min="0">
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="isActive" checked>
              <label class="form-check-label" for="isActive">
                Active
              </label>
            </div>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Add Leave Type',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      preConfirm: () => {
        const name = (document.getElementById('leaveTypeName') as HTMLInputElement).value;
        const description = (document.getElementById('leaveTypeDesc') as HTMLInputElement).value;
        const maxDays = (document.getElementById('maxDays') as HTMLInputElement).value;
        const isActive = (document.getElementById('isActive') as HTMLInputElement).checked;

        if (!name.trim()) {
          Swal.showValidationMessage('Please enter a leave type name');
          return false;
        }

        return {
          name: name.trim(),
          description: description.trim() || undefined,
          max_days_per_year: maxDays ? parseInt(maxDays) : undefined,
          is_active: isActive
        };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        this.performAddLeaveType(result.value);
      }
    });
  }

  // Perform add leave type API call
  private performAddLeaveType(leaveTypeData: any): void {
    console.log('Adding leave type:', leaveTypeData);

    Swal.fire({
      title: 'Adding Leave Type...',
      text: 'Please wait while we add the new leave type.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.leaveService.createLeaveType(leaveTypeData).subscribe({
      next: (response) => {
        console.log('✅ Leave type added successfully:', response);

        Swal.fire({
          icon: 'success',
          title: 'Leave Type Added!',
          text: 'The new leave type has been added successfully.',
          confirmButtonColor: '#28a745'
        });

        // Refresh the leave types data
        this.loadLeaveData();
      },
      error: (error) => {
        console.error('❌ Error adding leave type:', error);

        let errorMessage = 'Failed to add leave type. Please try again.';

        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }
/// gaurav
        Swal.fire({
          icon: 'error',
          title: 'Addition Failed',
          text: errorMessage,
          confirmButtonColor: '#3085d6'
        });
      }
    });
  }

  // Edit leave type method
  editLeaveType(leaveType: any): void {
    console.log('Edit leave type:', leaveType);

    Swal.fire({
      title: 'Edit Leave Type',
      html: `
        <div class="text-start">
          <div class="mb-3">
            <label for="editLeaveTypeName" class="form-label">Leave Type Name</label>
            <input type="text" id="editLeaveTypeName" class="form-control" value="${leaveType.name || ''}">
          </div>
          <div class="mb-3">
            <label for="editLeaveTypeDesc" class="form-label">Description</label>
            <input type="text" id="editLeaveTypeDesc" class="form-control" value="${leaveType.description || ''}">
          </div>
          <div class="mb-3">
            <label for="editMaxDays" class="form-label">Max Days Per Year</label>
            <input type="number" id="editMaxDays" class="form-control" value="${leaveType.max_days_per_year || ''}" min="0">
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="editIsActive" ${leaveType.is_active ? 'checked' : ''}>
              <label class="form-check-label" for="editIsActive">
                Active
              </label>
            </div>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Update Leave Type',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      preConfirm: () => {
        const name = (document.getElementById('editLeaveTypeName') as HTMLInputElement).value;
        const description = (document.getElementById('editLeaveTypeDesc') as HTMLInputElement).value;
        const maxDays = (document.getElementById('editMaxDays') as HTMLInputElement).value;
        const isActive = (document.getElementById('editIsActive') as HTMLInputElement).checked;

        if (!name.trim()) {
          Swal.showValidationMessage('Please enter a leave type name');
          return false;
        }

        return {
          name: name.trim(),
          description: description.trim() || undefined,
          max_days_per_year: maxDays ? parseInt(maxDays) : undefined,
          is_active: isActive
        };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        this.performUpdateLeaveType(leaveType.id, result.value);
      }
    });
  }

  // Perform update leave type API call
  private performUpdateLeaveType(leaveTypeId: number, leaveTypeData: any): void {
    console.log('Updating leave type:', leaveTypeId, leaveTypeData);

    Swal.fire({
      title: 'Updating Leave Type...',
      text: 'Please wait while we update the leave type.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    this.leaveService.updateLeaveType(leaveTypeId, leaveTypeData).subscribe({
      next: (response) => {
        console.log('✅ Leave type updated successfully:', response);

        Swal.fire({
          icon: 'success',
          title: 'Leave Type Updated!',
          text: 'The leave type has been updated successfully.',
          confirmButtonColor: '#28a745'
        });

        // Refresh the leave types data
        this.loadLeaveData();
      },
      error: (error) => {
        console.error('❌ Error updating leave type:', error);

        let errorMessage = 'Failed to update leave type. Please try again.';

        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          icon: 'error',
          title: 'Update Failed',
          text: errorMessage,
          confirmButtonColor: '#3085d6'
        });
      }
    });
  }
}

