import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-debt-details-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './debt-details-modal.component.html',
  styleUrl: './debt-details-modal.component.scss'
})
export class DebtDetailsModalComponent implements OnInit {
  @Input() rowId?: number;

  // Form data
  formData = {
    id: 0,
    borrowerName: '',
    entityType: '',
    nameOfLender: '',
    typeOfFacility: '',
    loanAccountNo: '',
    individualGuarantee: '',
    dateOfSanction: '',
    dateOfDisbursement: '',
    sanctionedAmount: 0,
    disbursedAmount: 0,
    utilized: 0,
    roa: 0,
    repaid: 0,
    emiAmount: 0,
    repaymentBankAccountNo: '',
    totalEmi: 0,
    emiPaid: 0,
    originFees: 0,
    currentOutstanding: 0,
    loanTenor: '',
    moratorium: '',
    detailsOfSecurityCreated: '',
    overdueAmount: 0,
    detailsOfDefault: '',
    remarksForDelay: ''
  };

  // Entity type options
  entityTypes = [
    { value: 'ENTITY', label: 'Entity' },
    { value: 'INDIVIDUAL', label: 'Individual' }
  ];

  // Facility type options
  facilityTypes = [
    { value: 'TERM LOAN', label: 'Term Loan' },
    { value: 'CASH CREDIT', label: 'Cash Credit' },
    { value: 'OVERDRAFT', label: 'Overdraft' },
    { value: 'WORKING CAPITAL', label: 'Working Capital' },
    { value: 'CONSTRUCTION FINANCE', label: 'Construction Finance' }
  ];

  // Yes/No options
  yesNoOptions = [
    { value: 'YES', label: 'Yes' },
    { value: 'NO', label: 'No' }
  ];

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      borrowerName: '',
      entityType: '',
      nameOfLender: '',
      typeOfFacility: '',
      loanAccountNo: '',
      individualGuarantee: '',
      dateOfSanction: '',
      dateOfDisbursement: '',
      sanctionedAmount: 0,
      disbursedAmount: 0,
      utilized: 0,
      roa: 0,
      repaid: 0,
      emiAmount: 0,
      repaymentBankAccountNo: '',
      totalEmi: 0,
      emiPaid: 0,
      originFees: 0,
      currentOutstanding: 0,
      loanTenor: '',
      moratorium: '',
      detailsOfSecurityCreated: '',
      overdueAmount: 0,
      detailsOfDefault: '',
      remarksForDelay: ''
    };

    // If editing an existing record, populate the form
    if (this.rowId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll use mock data based on the ID
      if (this.rowId === 1) {
        this.formData = {
          id: 1,
          borrowerName: 'ABC Developers Pvt Ltd',
          entityType: 'ENTITY',
          nameOfLender: 'HDFC Bank',
          typeOfFacility: 'TERM LOAN',
          loanAccountNo: 'HDFC123456789',
          individualGuarantee: 'YES',
          dateOfSanction: '2023-01-15', // Format for input field
          dateOfDisbursement: '2023-02-01', // Format for input field
          sanctionedAmount: ********,
          disbursedAmount: ********,
          utilized: ********,
          roa: 10.5,
          repaid: 5000000,
          emiAmount: 550000,
          repaymentBankAccountNo: 'HDFC987654321',
          totalEmi: 60,
          emiPaid: 10,
          originFees: 500000,
          currentOutstanding: ********,
          loanTenor: '5 Years',
          moratorium: '6 Months',
          detailsOfSecurityCreated: 'Property Mortgage',
          overdueAmount: 0,
          detailsOfDefault: 'N/A',
          remarksForDelay: 'N/A'
        };
      } else if (this.rowId === 2) {
        this.formData = {
          id: 2,
          borrowerName: 'XYZ Constructions Ltd',
          entityType: 'ENTITY',
          nameOfLender: 'ICICI Bank',
          typeOfFacility: 'CASH CREDIT',
          loanAccountNo: 'ICICI987654321',
          individualGuarantee: 'NO',
          dateOfSanction: '2023-03-10', // Format for input field
          dateOfDisbursement: '2023-03-25', // Format for input field
          sanctionedAmount: ********,
          disbursedAmount: ********,
          utilized: ********,
          roa: 11.25,
          repaid: 2000000,
          emiAmount: 350000,
          repaymentBankAccountNo: 'ICICI123456789',
          totalEmi: 48,
          emiPaid: 6,
          originFees: 300000,
          currentOutstanding: ********,
          loanTenor: '4 Years',
          moratorium: '3 Months',
          detailsOfSecurityCreated: 'Property Mortgage + Personal Guarantee',
          overdueAmount: 350000,
          detailsOfDefault: 'EMI for Apr 2023',
          remarksForDelay: 'Payment processing delay'
        };
      } else if (this.rowId === 3) {
        this.formData = {
          id: 3,
          borrowerName: 'PQR Realtors',
          entityType: 'INDIVIDUAL',
          nameOfLender: 'SBI Bank',
          typeOfFacility: 'TERM LOAN',
          loanAccountNo: 'SBI567891234',
          individualGuarantee: 'YES',
          dateOfSanction: '2023-04-05', // Format for input field
          dateOfDisbursement: '2023-04-20', // Format for input field
          sanctionedAmount: ********,
          disbursedAmount: ********,
          utilized: ********,
          roa: 9.75,
          repaid: 1500000,
          emiAmount: 250000,
          repaymentBankAccountNo: 'SBI432156789',
          totalEmi: 72,
          emiPaid: 3,
          originFees: 200000,
          currentOutstanding: ********,
          loanTenor: '6 Years',
          moratorium: 'None',
          detailsOfSecurityCreated: 'Property Mortgage',
          overdueAmount: 0,
          detailsOfDefault: 'N/A',
          remarksForDelay: 'N/A'
        };
      }
    }
  }

  // Calculate current outstanding
  calculateCurrentOutstanding() {
    // Current outstanding = Disbursed amount - Repaid amount
    this.formData.currentOutstanding = this.formData.disbursedAmount - this.formData.repaid;
    return this.formData.currentOutstanding;
  }

  // Format number with commas
  formatNumber(num: number): string {
    if (num === undefined || num === null) return '0';
    return num.toLocaleString('en-IN');
  }

  // Format date from YYYY-MM-DD to DD-MMM-YYYY format
  formatDateForDisplay(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const day = date.getDate().toString().padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${day}-${month}-${year}`;
    } catch (error) {
      return dateString; // Return original string if parsing fails
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Calculate the current outstanding before saving
    this.calculateCurrentOutstanding();

    // Format dates for display in the table
    const formattedData = {
      ...this.formData,
      dateOfSanction: this.formatDateForDisplay(this.formData.dateOfSanction),
      dateOfDisbursement: this.formatDateForDisplay(this.formData.dateOfDisbursement)
    };

    // Close the modal and pass the data back
    this.activeModal.close(formattedData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
