:host {
  .session-timer {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    visibility: visible !important;
    opacity: 1 !important;
    min-width: 120px;
    z-index: 1000;

    &:hover {
      opacity: 0.8 !important;
    }

    // Normal state (more than 5 minutes remaining)
    &.normal {
      color: #000000 !important;
      background-color: rgba(0, 0, 0, 0.05);
      padding: 2px 4px;
      border-radius: 3px;
    }

    // Warning state (less than 5 minutes remaining)
    &.warning {
      color: #000000 !important;
      background-color: rgba(0, 0, 0, 0.05);
      padding: 2px 4px;
      border-radius: 3px;
      animation: pulse-warning 2s infinite;
    }

    // Expired state
    &.expired {
      color: #000000 !important;
      background-color: rgba(0, 0, 0, 0.05);
      padding: 2px 4px;
      border-radius: 3px;
      animation: pulse-danger 1s infinite;
    }
  }
}

// Pulse animation for warning state
@keyframes pulse-warning {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// Pulse animation for expired state
@keyframes pulse-danger {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

// Dark mode adjustments - Ensure white text visibility
:host-context([data-bs-theme='dark']) {
  .session-timer {
    // Base dark mode styling for all states
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.25) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;

    &.normal {
      color: #ffffff !important;
      background-color: rgba(255, 255, 255, 0.15) !important;
      border: 1px solid rgba(255, 255, 255, 0.25) !important;
    }

    &.warning {
      color: #ffffff !important;
      background-color: rgba(255, 255, 255, 0.15) !important;
      border: 1px solid rgba(255, 255, 255, 0.25) !important;
    }

    &.expired {
      color: #ffffff !important;
      background-color: rgba(255, 255, 255, 0.15) !important;
      border: 1px solid rgba(255, 255, 255, 0.25) !important;
    }

    // Ensure hover state maintains white color
    &:hover {
      color: #ffffff !important;
      opacity: 0.8 !important;
    }
  }
}

// Global dark mode styles (fallback)
::ng-deep [data-bs-theme='dark'] .session-timer {
  color: #ffffff !important;
}

::ng-deep [data-bs-theme='dark'] .session-timer * {
  color: #ffffff !important;
}

// Responsive adjustments for HH:MM:SS format
@media (max-width: 768px) {
  :host {
    .session-timer {
      font-size: 12px;
    }
  }

  // Dark mode responsive
  :host-context([data-bs-theme='dark']) {
    .session-timer {
      color: #ffffff !important;
    }
  }
}

@media (max-width: 576px) {
  :host {
    .session-timer {
      font-size: 11px;
    }
  }

  // Dark mode responsive
  :host-context([data-bs-theme='dark']) {
    .session-timer {
      color: #ffffff !important;
    }
  }
}
