<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
  <div>
    <h4 class="mb-3 mb-md-0">Welcome to Dashboard</h4>
  </div>
  <div class="d-flex align-items-center flex-wrap text-nowrap">
    <div class="input-group w-200px me-2 mb-2 mb-md-0">
      <button class="btn btn-outline-primary btn-icon calendar" type="button" (click)="d.toggle()">
        <i data-feather="calendar" appFeatherIcon></i>
      </button>
      <input class="form-control border-primary bg-transparent" placeholder="yyyy-mm-dd"
          name="dp" [(ngModel)]="currentDate" ngbDatepicker #d="ngbDatepicker">
    </div>
    <button type="button" class="btn btn-outline-primary btn-icon-text me-2 mb-2 mb-md-0">
      <i class="btn-icon-prepend" data-feather="printer" appFeatherIcon></i>
      Print
    </button>
    <button type="button" class="btn btn-primary btn-icon-text mb-2 mb-md-0">
      <i class="btn-icon-prepend" data-feather="download-cloud" appFeatherIcon></i>
      Download Report
    </button>
  </div>
</div>

<div class="row">
  <div class="col-12 col-xl-12 stretch-card">
    <div class="row flex-grow-1">
      <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-baseline">
              <h6 class="card-title mb-0">New Customers</h6>
              <div ngbDropdown class="mb-2">
                <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton">
                  <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="dropdownMenuButton">
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6 col-md-12 col-xl-5">
                <h3 class="mb-2">3,897</h3>
                <div class="d-flex align-items-baseline">
                  <p class="text-success">
                    <span>+3.3%</span>
                    <i data-feather="arrow-up" appFeatherIcon class="icon-sm mb-1"></i>
                  </p>
                </div>
              </div>
              <div class="col-6 col-md-12 col-xl-7">
                <div class="mt-md-3 mt-xl-0">
                  <apx-chart
                    [series]="customersChartOptions.series"
                    [chart]="customersChartOptions.chart"
                    [colors]="customersChartOptions.colors"
                    [xaxis]="customersChartOptions.xaxis"
                    [stroke]="customersChartOptions.stroke"
                    [markers]="customersChartOptions.markers"
                  ></apx-chart>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-baseline">
              <h6 class="card-title mb-0">New Orders</h6>
              <div ngbDropdown class="mb-2">
                <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton1">
                  <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="dropdownMenuButton1">
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6 col-md-12 col-xl-5">
                <h3 class="mb-2">35,084</h3>
                <div class="d-flex align-items-baseline">
                  <p class="text-danger">
                    <span>-2.8%</span>
                    <i data-feather="arrow-down" appFeatherIcon class="icon-sm mb-1"></i>
                  </p>
                </div>
              </div>
              <div class="col-6 col-md-12 col-xl-7">
                <div class="mt-md-3 mt-xl-0">
                  <apx-chart
                    [series]="ordersChartOptions.series"
                    [chart]="ordersChartOptions.chart"
                    [colors]="ordersChartOptions.colors"
                    [plotOptions]="ordersChartOptions.plotOptions"
                    [xaxis]="ordersChartOptions.xaxis"
                  ></apx-chart>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-baseline">
              <h6 class="card-title mb-0">Growth</h6>
              <div ngbDropdown class="mb-2">
                <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton2">
                  <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="dropdownMenuButton2">
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6 col-md-12 col-xl-5">
                <h3 class="mb-2">89.87%</h3>
                <div class="d-flex align-items-baseline">
                  <p class="text-success">
                    <span>+2.8%</span>
                    <i data-feather="arrow-up" appFeatherIcon class="icon-sm mb-1"></i>
                  </p>
                </div>
              </div>
              <div class="col-6 col-md-12 col-xl-7">
                <div class="mt-md-3 mt-xl-0">
                  <apx-chart
                    [series]="growthChartOptions.series"
                    [chart]="growthChartOptions.chart"
                    [colors]="growthChartOptions.colors"
                    [xaxis]="growthChartOptions.xaxis"
                    [stroke]="growthChartOptions.stroke"
                    [markers]="growthChartOptions.markers"
                  ></apx-chart>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> <!-- row -->

<div class="row">
  <div class="col-12 col-xl-12 grid-margin stretch-card">
    <div class="card overflow-hidden">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-4 mb-md-3">
          <h6 class="card-title mb-0">Revenue</h6>
          <div ngbDropdown>
            <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton3">
              <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton3">
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
            </div>
          </div>
        </div>
        <div class="row align-items-start">
          <div class="col-md-7">
            <p class="text-secondary fs-13px mb-3 mb-md-0">Revenue is the income that a business has from its normal business activities, usually from the sale of goods and services to customers.</p>
          </div>
          <div class="col-md-5 d-flex justify-content-md-end">
            <div class="btn-group mb-3 mb-md-0" role="group" aria-label="Basic example">
              <button type="button" class="btn btn-outline-primary">Today</button>
              <button type="button" class="btn btn-outline-primary d-none d-md-block">Week</button>
              <button type="button" class="btn btn-primary">Month</button>
              <button type="button" class="btn btn-outline-primary">Year</button>
            </div>
          </div>
        </div>
        <div class="flot-wrapper">
          <apx-chart
            [series]="revenueChartOptions.series"
            [chart]="revenueChartOptions.chart"
            [colors]="revenueChartOptions.colors"
            [grid]="revenueChartOptions.grid"
            [xaxis]="revenueChartOptions.xaxis"
            [yaxis]="revenueChartOptions.yaxis"
            [markers]="revenueChartOptions.markers"
            [stroke]="revenueChartOptions.stroke"
          ></apx-chart>
        </div>
      </div>
    </div>
  </div>
</div> <!-- row -->

<div class="row">
  <div class="col-lg-7 col-xl-8 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-2">
          <h6 class="card-title mb-0">Monthly sales</h6>
          <div ngbDropdown class="mb-2">
            <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton4">
              <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton4">
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
            </div>
          </div>
        </div>
        <p class="text-secondary">Sales are activities related to selling or the number of goods or services sold in a given time period.</p>
        <apx-chart
          [series]="monthlySalesChartOptions.series"
          [chart]="monthlySalesChartOptions.chart"
          [colors]="monthlySalesChartOptions.colors"
          [fill]="monthlySalesChartOptions.fill"
          [grid]="monthlySalesChartOptions.grid"
          [xaxis]="monthlySalesChartOptions.xaxis"
          [yaxis]="monthlySalesChartOptions.yaxis"
          [legend]="monthlySalesChartOptions.legend"
          [stroke]="monthlySalesChartOptions.stroke"
          [dataLabels]="monthlySalesChartOptions.dataLabels"
          [plotOptions]="monthlySalesChartOptions.plotOptions"
        ></apx-chart>
      </div> 
    </div>
  </div>
  <div class="col-lg-5 col-xl-4 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-2">
          <h6 class="card-title mb-0">Cloud storage</h6>
          <div ngbDropdown class="mb-2">
            <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton5">
              <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton5">
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
            </div>
          </div>
        </div>
        <apx-chart
          [series]="cloudStorageChartOptions.series"
          [chart]="cloudStorageChartOptions.chart"
          [colors]="cloudStorageChartOptions.colors"
          [plotOptions]="cloudStorageChartOptions.plotOptions"
          [fill]="cloudStorageChartOptions.fill"
          [stroke]="cloudStorageChartOptions.stroke"
          [labels]="cloudStorageChartOptions.labels"
        ></apx-chart>
        <div class="row mb-3">
          <div class="col-6 d-flex justify-content-end">
            <div>
              <label class="d-flex align-items-center justify-content-end fs-10px text-uppercase fw-bold">Total storage <span class="p-1 ms-1 rounded-circle bg-secondary"></span></label>
              <h5 class="fw-bolder mb-0 text-end">8TB</h5>
            </div>
          </div>
          <div class="col-6">
            <div>
              <label class="d-flex align-items-center fs-10px text-uppercase fw-bold"><span class="p-1 me-1 rounded-circle bg-primary"></span> Used storage</label>
              <h5 class="fw-bolder mb-0">~5TB</h5>
            </div>
          </div>
        </div>
        <div class="d-grid">
          <button class="btn btn-primary">Upgrade storage</button>
        </div>
      </div>
    </div>
  </div>
</div> <!-- row -->

<!-- LMS Section -->
<!--
<div class="row">
  <div class="col-12">
    <h4 class="lms-section-title">Leave Management System</h4>
  </div>
</div>

<div class="row">
  First Row of LMS Cards
  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="calendar" appFeatherIcon class="text-warning"></i>
        </div>
        <h6 class="card-title">APPLY LEAVE</h6>
        <button class="btn btn-outline-warning btn-sm" (click)="navigateToApplyLeave()">Apply Leave</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="users" appFeatherIcon class="text-primary"></i>
        </div>
        <h6 class="card-title">HR ADMIN</h6>
        <button class="btn btn-outline-primary btn-sm" (click)="openHrAdminModal()">HR Admin</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="bar-chart-2" appFeatherIcon class="text-warning"></i>
        </div>
        <h6 class="card-title">REPORTS</h6>
        <button class="btn btn-outline-warning btn-sm" (click)="navigateToReports()">Reports</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="users" appFeatherIcon class="text-info"></i>
        </div>
        <h6 class="card-title">VIEW EMPLOYEE</h6>
        <button class="btn btn-outline-info btn-sm" (click)="navigateToEmployeeList()">Employee List</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="file-text" appFeatherIcon class="text-secondary"></i>
        </div>
        <h6 class="card-title">LEAVE POLICY</h6>
        <button class="btn btn-outline-secondary btn-sm" (click)="navigateToLeavePolicy()">Leave Policy</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="check-circle" appFeatherIcon class="text-success"></i>
        </div>
        <h6 class="card-title">APPROVE LEAVES</h6>
        <button class="btn btn-outline-success btn-sm" (click)="navigateToApproveLeaves()">Approve Leaves</button>
      </div>
    </div>
  </div>
</div>

<div class="row">
  Second Row of LMS Cards
  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="clock" appFeatherIcon class="text-info"></i>
        </div>
        <h6 class="card-title">MARK ATTENDANCE</h6>
        <button class="btn btn-outline-info btn-sm" (click)="navigateToAttendance()">Mark Attendance</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="plus-circle" appFeatherIcon class="text-primary"></i>
        </div>
        <h6 class="card-title">ASSIGN COMPOFF</h6>
        <button class="btn btn-outline-primary btn-sm" (click)="openAssignCompoffModal()">Assign Compoff</button>
      </div>
    </div>
  </div>

  <div class="col-md-4 col-lg-2 grid-margin stretch-card">
    <div class="card lms-card">
      <div class="card-body text-center">
        <div class="lms-icon mb-3">
          <i data-feather="calendar-plus" appFeatherIcon class="text-warning"></i>
        </div>
        <h6 class="card-title">COMPOFF REQUEST</h6>
        <button class="btn btn-outline-warning btn-sm" (click)="openCompoffRequestModal()">Compoff Request</button>
      </div>
    </div>
  </div>
</div>
-->

<div class="row">
  <div class="col-lg-5 col-xl-4 grid-margin grid-margin-lg-0 stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-2">
          <h6 class="card-title mb-0">Inbox</h6>
          <div ngbDropdown class="mb-2">
            <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton6">
              <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton6">
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
            </div>
          </div>
        </div>
        <div class="d-flex flex-column">
          <a href="" (click)="false" class="d-flex align-items-center border-bottom pb-3">
            <div class="me-3">
              <img src="https://placehold.co/35x35" class="rounded-circle w-35px" alt="user">
            </div>
            <div class="w-100">
              <div class="d-flex justify-content-between">
                <h6 class="text-body mb-2">Leonardo Payne</h6>
                <p class="text-secondary fs-12px">12.30 PM</p>
              </div>
              <p class="text-secondary fs-13px">Hey! there I'm available...</p>
            </div>
          </a>
          <a href="" (click)="false" class="d-flex align-items-center border-bottom py-3">
            <div class="me-3">
              <img src="https://placehold.co/35x35" class="rounded-circle w-35px" alt="user">
            </div>
            <div class="w-100">
              <div class="d-flex justify-content-between">
                <h6 class="text-body mb-2">Carl Henson</h6>
                <p class="text-secondary fs-12px">02.14 AM</p>
              </div>
              <p class="text-secondary fs-13px">I've finished it! See you so..</p>
            </div>
          </a>
          <a href="" (click)="false" class="d-flex align-items-center border-bottom py-3">
            <div class="me-3">
              <img src="https://placehold.co/35x35" class="rounded-circle w-35px" alt="user">
            </div>
            <div class="w-100">
              <div class="d-flex justify-content-between">
                <h6 class="text-body mb-2">Jensen Combs</h6>
                <p class="text-secondary fs-12px">08.22 PM</p>
              </div>
              <p class="text-secondary fs-13px">This template is awesome!</p>
            </div>
          </a>
          <a href="" (click)="false" class="d-flex align-items-center border-bottom py-3">
            <div class="me-3">
              <img src="https://placehold.co/35x35" class="rounded-circle w-35px" alt="user">
            </div>
            <div class="w-100">
              <div class="d-flex justify-content-between">
                <h6 class="text-body mb-2">Amiah Burton</h6>
                <p class="text-secondary fs-12px">05.49 AM</p>
              </div>
              <p class="text-secondary fs-13px">Nice to meet you</p>
            </div>
          </a>
          <a href="" (click)="false" class="d-flex align-items-center border-bottom py-3">
            <div class="me-3">
              <img src="https://placehold.co/35x35" class="rounded-circle w-35px" alt="user">
            </div>
            <div class="w-100">
              <div class="d-flex justify-content-between">
                <h6 class="text-body mb-2">Yaretzi Mayo</h6>
                <p class="text-secondary fs-12px">01.19 AM</p>
              </div>
              <p class="text-secondary fs-13px">Hey! there I'm available...</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div class="col-lg-7 col-xl-8 stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-2">
          <h6 class="card-title mb-0">Projects</h6>
          <div ngbDropdown class="mb-2">
            <a class="no-dropdown-toggle-icon" ngbDropdownToggle id="dropdownMenuButton7">
              <i class="icon-lg text-secondary pb-3px" data-feather="more-horizontal" appFeatherIcon></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton7">
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="eye" appFeatherIcon class="icon-sm me-2"></i> <span class="">View</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="edit-2" appFeatherIcon class="icon-sm me-2"></i> <span class="">Edit</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="trash" appFeatherIcon class="icon-sm me-2"></i> <span class="">Delete</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="printer" appFeatherIcon class="icon-sm me-2"></i> <span class="">Print</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i data-feather="download" appFeatherIcon class="icon-sm me-2"></i> <span class="">Download</span></a>
            </div>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead>
              <tr>
                <th class="pt-0">#</th>
                <th class="pt-0">Project Name</th>
                <th class="pt-0">Start Date</th>
                <th class="pt-0">Due Date</th>
                <th class="pt-0">Status</th>
                <th class="pt-0">Assign</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1</td>
                <td>NobleUI jQuery</td>
                <td>01/01/2024</td>
                <td>26/04/2024</td>
                <td><span class="badge bg-danger">Released</span></td>
                <td>Leonardo Payne</td>
              </tr>
              <tr>
                <td>2</td>
                <td>NobleUI Angular</td>
                <td>01/01/2024</td>
                <td>26/04/2024</td>
                <td><span class="badge bg-success">Review</span></td>
                <td>Carl Henson</td>
              </tr>
              <tr>
                <td>3</td>
                <td>NobleUI ReactJs</td>
                <td>01/05/2024</td>
                <td>10/09/2024</td>
                <td><span class="badge bg-info">Pending</span></td>
                <td>Jensen Combs</td>
              </tr>
              <tr>
                <td>4</td>
                <td>NobleUI VueJs</td>
                <td>01/01/2024</td>
                <td>31/11/2024</td>
                <td><span class="badge bg-warning">Work in Progress</span>
                </td>
                <td>Amiah Burton</td>
              </tr>
              <tr>
                <td>5</td>
                <td>NobleUI Laravel</td>
                <td>01/01/2024</td>
                <td>31/12/2024</td>
                <td><span class="badge bg-danger text-white">Coming soon</span></td>
                <td>Yaretzi Mayo</td>
              </tr>
              <tr>
                <td>6</td>
                <td>NobleUI NodeJs</td>
                <td>01/01/2024</td>
                <td>31/12/2024</td>
                <td><span class="badge bg-primary">Coming soon</span></td>
                <td>Carl Henson</td>
              </tr>
              <tr>
                <td class="border-bottom">3</td>
                <td class="border-bottom">NobleUI EmberJs</td>
                <td class="border-bottom">01/05/2024</td>
                <td class="border-bottom">10/11/2024</td>
                <td class="border-bottom"><span class="badge bg-info">Pending</span></td>
                <td class="border-bottom">Jensen Combs</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>