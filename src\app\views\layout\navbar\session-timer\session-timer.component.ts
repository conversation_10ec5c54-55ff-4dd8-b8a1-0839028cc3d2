import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../../core/services/auth.service';
import { Router } from '@angular/router';
import { Subscription, interval } from 'rxjs';

/**
 * SessionTimerComponent displays a real-time countdown of the user's session expiration time.
 *
 * Features:
 * - Shows remaining time in HH:MM:SS format to accommodate dynamic session times
 * - Updates every second with real-time countdown
 * - Handles dynamic session time changes from backend via token refresh
 * - Visual warning states (orange when < 5 minutes, red when expired)
 * - Integrates with AuthService for JWT token expiration
 * - Responsive design for mobile devices
 * - Clickable for session details (logs to console)
 * - Handles session expiration through AuthService
 * - Supports both light and dark themes
 */
@Component({
  selector: 'app-session-timer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './session-timer.component.html',
  styleUrl: './session-timer.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class SessionTimerComponent implements OnInit, OnDestroy {
  timeRemaining: string = '--:--:--';
  isWarning: boolean = false;
  isExpired: boolean = false;
  private timerSubscription?: Subscription;
  private authSubscription?: Subscription;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Start the timer
    this.startTimer();

    // Subscribe to auth state changes to handle dynamic session updates from backend
    this.authSubscription = this.authService.currentUser$.subscribe(user => {
      if (!user || !this.authService.isLoggedIn()) {
        this.timeRemaining = '--:--';
        this.isWarning = false;
        this.isExpired = false;
      } else {
        // Session time may have changed from backend, update immediately
        this.updateTimer();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  private startTimer(): void {
    // Update every second
    this.timerSubscription = interval(1000).subscribe(() => {
      this.updateTimer();
    });

    // Initial update
    this.updateTimer();
  }

  private updateTimer(): void {
    const currentUser = this.authService.currentUserValue;

    if (!currentUser || !currentUser.token_expiry || !this.authService.isLoggedIn()) {
      this.timeRemaining = '--:--:--';
      this.isWarning = false;
      this.isExpired = false;
      return;
    }

    const now = Date.now();
    const timeLeft = currentUser.token_expiry - now;

    if (timeLeft <= 0) {
      // Session has expired
      this.timeRemaining = '00:00:00';
      this.isExpired = true;
      this.isWarning = false;
      this.handleSessionExpired();
      return;
    }

    // Convert to hours, minutes and seconds
    const totalSeconds = Math.floor(timeLeft / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    // Always format as HH:MM:SS to accommodate dynamic session times from backend
    this.timeRemaining = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    // Set warning state if less than 5 minutes remaining
    this.isWarning = totalSeconds <= 300; // 5 minutes = 300 seconds
    this.isExpired = false;
  }

  private handleSessionExpired(): void {
    // Let the AuthService handle the expiration
    if (this.authService.isTokenExpired()) {
      // The AuthService will automatically redirect to login and show appropriate message
    }
  }

  /**
   * Handle click on timer to show session info (optional feature)
   */
  onTimerClick(): void {
    const currentUser = this.authService.currentUserValue;
    if (currentUser?.token_expiry) {
      const expiryDate = new Date(currentUser.token_expiry);
      const timeLeft = currentUser.token_expiry - Date.now();
      const totalSeconds = Math.floor(timeLeft / 1000);
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      // Session info available for debugging if needed
    }
  }

  /**
   * Get CSS classes for the timer display
   */
  getTimerClasses(): string {
    const classes = ['session-timer'];

    if (this.isExpired) {
      classes.push('expired');
    } else if (this.isWarning) {
      classes.push('warning');
    } else {
      classes.push('normal');
    }

    return classes.join(' ');
  }

  /**
   * Force refresh the timer display (useful when session time changes from backend)
   */
  refreshTimer(): void {
    this.updateTimer();
  }
}
