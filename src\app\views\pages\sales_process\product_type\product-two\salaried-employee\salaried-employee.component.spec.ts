import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SalariedEmployeeComponent } from './salaried-employee.component';

describe('SalariedEmployeeComponent', () => {
  let component: SalariedEmployeeComponent;
  let fixture: ComponentFixture<SalariedEmployeeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SalariedEmployeeComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SalariedEmployeeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return only Shop and Office for NRPL product sub type', () => {
    component.selectedProductSubType = 'NRPL';
    const filteredOptions = component.filteredPropertyTypeOptions;
    expect(filteredOptions).toEqual(['Shop', 'Office']);
  });

  it('should return all property type options for non-NRPL product sub types', () => {
    component.selectedProductSubType = 'HL';
    const filteredOptions = component.filteredPropertyTypeOptions;
    expect(filteredOptions).toEqual(component.propertyTypeOptions);
  });

  it('should return all property type options when no product sub type is selected', () => {
    component.selectedProductSubType = '';
    const filteredOptions = component.filteredPropertyTypeOptions;
    expect(filteredOptions).toEqual(component.propertyTypeOptions);
  });
});
