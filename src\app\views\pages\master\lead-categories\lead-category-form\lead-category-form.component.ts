import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LeadCategory } from '../../../../../core/services/lead-category.service';

@Component({
  selector: 'app-lead-category-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-target me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Lead Category
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Lead Category Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ leadCategory?.name }}</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced form will include comprehensive lead category management with:
        <ul class="mt-2 mb-0">
          <li><strong>Hierarchical Category Structure</strong> - Multi-level category organization with parent-child relationships</li>
          <li><strong>Priority & Scoring System</strong> - Priority levels with custom scoring weights and conversion tracking</li>
          <li><strong>Qualification Criteria Builder</strong> - Budget ranges, timeline requirements, company size filters</li>
          <li><strong>Scoring Rules Engine</strong> - Custom scoring rules based on lead attributes and behaviors</li>
          <li><strong>Follow-up Rules</strong> - Automated follow-up sequences based on time, actions, or score changes</li>
          <li><strong>Automation Rules</strong> - Trigger-based automation for lead assignment and status updates</li>
          <li><strong>Visual Category Designer</strong> - Color coding, icons, and visual customization</li>
          <li><strong>Conversion Analytics</strong> - Expected value calculations and conversion rate tracking</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-settings me-2"></i>
        <strong>Advanced Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Multi-currency support for expected values and budget ranges</li>
          <li>Geographic and industry-based qualification criteria</li>
          <li>Decision maker level requirements and company size filtering</li>
          <li>Custom field requirements and validation rules</li>
          <li>Integration with CRM systems for lead scoring</li>
          <li>Real-time conversion rate calculations</li>
          <li>Automated lead routing based on category rules</li>
          <li>Performance analytics and optimization suggestions</li>
        </ul>
      </div>

      <div class="alert alert-success">
        <i class="feather icon-zap me-2"></i>
        <strong>Automation Capabilities:</strong>
        <ul class="mt-2 mb-0">
          <li>Time-based follow-up sequences with customizable delays</li>
          <li>Score-based lead routing and assignment</li>
          <li>Status change triggers for automated actions</li>
          <li>Email template integration for automated communications</li>
          <li>Task creation and assignment based on category rules</li>
          <li>Meeting scheduling automation for high-priority leads</li>
          <li>Notification systems for sales team alerts</li>
        </ul>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
  `]
})
export class LeadCategoryFormComponent {
  @Input() isEditMode = false;
  @Input() leadCategory: LeadCategory | null = null;

  leadCategoryForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.leadCategoryForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      priority: ['', [Validators.required]],
      scoring_weight: [0, [Validators.required, Validators.min(0), Validators.max(100)]]
    });
  }
}
