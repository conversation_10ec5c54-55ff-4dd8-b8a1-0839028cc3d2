<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">Life Insurance Form</h4>
          </div>
        </div>

        <!-- Life Insurance Form -->
        <form [formGroup]="lifeForm" (ngSubmit)="onSubmit()">
          <div class="row">

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="customerName" class="form-label">Customer Name</label>
              <input type="text" class="form-control" id="customerName" formControlName="customerName"
                [ngClass]="{'is-invalid': isFormSubmitted && form['customerName'].errors}"
                placeholder="Enter customer name">
              @if (isFormSubmitted && form['customerName'].errors?.required) {
                <div class="invalid-feedback">Customer name is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="insuredName" class="form-label">Insured Name</label>
              <input type="text" class="form-control" id="insuredName" formControlName="insuredName"
                [ngClass]="{'is-invalid': isFormSubmitted && form['insuredName'].errors}"
                placeholder="Enter insured name">
              @if (isFormSubmitted && form['insuredName'].errors?.required) {
                <div class="invalid-feedback">Insured name is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="dateOfBirth" class="form-label">Date of Birth</label>
              <input type="date" class="form-control" id="dateOfBirth" formControlName="dateOfBirth"
                [ngClass]="{'is-invalid': isFormSubmitted && form['dateOfBirth'].errors}">
              @if (isFormSubmitted && form['dateOfBirth'].errors?.required) {
                <div class="invalid-feedback">Date of birth is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="height" class="form-label">Height (cm)</label>
              <input type="number" class="form-control" id="height" formControlName="height"
                [ngClass]="{'is-invalid': isFormSubmitted && form['height'].errors}"
                placeholder="Enter height in cm">
              @if (isFormSubmitted && form['height'].errors?.required) {
                <div class="invalid-feedback">Height is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="weight" class="form-label">Weight (kg)</label>
              <input type="number" class="form-control" id="weight" formControlName="weight"
                [ngClass]="{'is-invalid': isFormSubmitted && form['weight'].errors}"
                placeholder="Enter weight in kg">
              @if (isFormSubmitted && form['weight'].errors?.required) {
                <div class="invalid-feedback">Weight is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="aadharCard" class="form-label">Aadhar Card Number</label>
              <input type="text" class="form-control" id="aadharCard" formControlName="aadharCard"
                [ngClass]="{'is-invalid': isFormSubmitted && form['aadharCard'].errors}"
                placeholder="Enter 12-digit Aadhar number"
                maxlength="12"
                (input)="onAadharInput($event)"
                pattern="[0-9]{12}">
              @if (isFormSubmitted && form['aadharCard'].errors?.required) {
                <div class="invalid-feedback">Aadhar card number is required</div>
              }
              @if (isFormSubmitted && form['aadharCard'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 12-digit Aadhar number</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="panCard" class="form-label">PAN Card Number</label>
              <input type="text" class="form-control" id="panCard" formControlName="panCard"
                [ngClass]="{'is-invalid': isFormSubmitted && form['panCard'].errors}"
                placeholder="Enter PAN number (e.g., **********)"
                maxlength="10"
                (input)="onPanInput($event)"
                pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
                style="text-transform: uppercase;">
              @if (isFormSubmitted && form['panCard'].errors?.required) {
                <div class="invalid-feedback">PAN card number is required</div>
              }
              @if (isFormSubmitted && form['panCard'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid PAN number (format: **********)</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="sumInsured" class="form-label">Sum Insured (₹)</label>
              <input type="number" class="form-control" id="sumInsured" formControlName="sumInsured"
                [ngClass]="{'is-invalid': isFormSubmitted && form['sumInsured'].errors}"
                placeholder="Enter sum insured amount">
              @if (isFormSubmitted && form['sumInsured'].errors?.required) {
                <div class="invalid-feedback">Sum insured is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="preExistingDisease" class="form-label">Any pre-existing disease / illness ?</label>
              <select class="form-select" id="preExistingDisease" formControlName="preExistingDisease"
                [ngClass]="{'is-invalid': isFormSubmitted && form['preExistingDisease'].errors}">
                <option value="">Select</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
              @if (isFormSubmitted && form['preExistingDisease'].errors?.required) {
                <div class="invalid-feedback">Please select an option</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="bloodPressureOrDiabetes" class="form-label">Is the applicant suffering from Blood pressure or diabetes?</label>
              <select class="form-select" id="bloodPressureOrDiabetes" formControlName="bloodPressureOrDiabetes"
                [ngClass]="{'is-invalid': isFormSubmitted && form['bloodPressureOrDiabetes'].errors}">
                <option value="">Select</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
              @if (isFormSubmitted && form['bloodPressureOrDiabetes'].errors?.required) {
                <div class="invalid-feedback">Please select an option</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="incomeTaxReturns" class="form-label">Last 3 years Income Tax returns with balance sheet, computation of income and other supporting documents</label>
              <select class="form-select" id="incomeTaxReturns" formControlName="incomeTaxReturns"
                [ngClass]="{'is-invalid': isFormSubmitted && form['incomeTaxReturns'].errors}">
                <option value="">Select Option</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
              @if (isFormSubmitted && form['incomeTaxReturns'].errors?.required) {
                <div class="invalid-feedback">Income tax returns selection is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="existingTermInsurance" class="form-label">Existing term insurance policies already taken by the applicant</label>
              <textarea class="form-control" id="existingTermInsurance" formControlName="existingTermInsurance" rows="3"
                [ngClass]="{'is-invalid': isFormSubmitted && form['existingTermInsurance'].errors}"
                placeholder="Enter details of existing term insurance policies"></textarea>
              @if (isFormSubmitted && form['existingTermInsurance'].errors?.required) {
                <div class="invalid-feedback">Please provide details of existing term insurance policies</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="smoker" class="form-label">Smoker: Please select any one from below options</label>
              <select class="form-select" id="smoker" formControlName="smoker"
                [ngClass]="{'is-invalid': isFormSubmitted && form['smoker'].errors}">
                <option value="">Select</option>
                <option value="Regular">Regular</option>
                <option value="Occasional">Occasional</option>
                <option value="Does not Smoke">Does not Smoke</option>
              </select>
              @if (isFormSubmitted && form['smoker'].errors?.required) {
                <div class="invalid-feedback">Please select smoking status</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3">
              <label for="alcoholConsumption" class="form-label">Alcohol consumption: Please select any one from below options</label>
              <select class="form-select" id="alcoholConsumption" formControlName="alcoholConsumption"
                [ngClass]="{'is-invalid': isFormSubmitted && form['alcoholConsumption'].errors}"
                (change)="onAlcoholConsumptionChange($event)">
                <option value="">Select</option>
                <option value="Regular">Regular</option>
                <option value="Occasional">Occasional</option>
                <option value="Does not consume">Does not consume</option>
              </select>
              @if (isFormSubmitted && form['alcoholConsumption'].errors?.required) {
                <div class="invalid-feedback">Please select alcohol consumption status</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-3" *ngIf="showAlcoholQuantity">
              <label for="alcoholQuantity" class="form-label">If yes, please confirm consumption quantity</label>
              <input type="text" class="form-control" id="alcoholQuantity" formControlName="alcoholQuantity"
                [ngClass]="{'is-invalid': isFormSubmitted && form['alcoholQuantity'].errors}"
                placeholder="Enter alcohol consumption quantity">
              @if (isFormSubmitted && form['alcoholQuantity'].errors?.required) {
                <div class="invalid-feedback">Please specify alcohol consumption quantity</div>
              }
            </div>

          </div>

          <!-- Submit Button -->
          <div class="row mt-4">
            <div class="col-12 text-end">
              <button type="submit" class="btn btn-primary me-2">Submit Application</button>
              <button type="button" class="btn btn-outline-secondary" (click)="resetForm()">Reset</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
