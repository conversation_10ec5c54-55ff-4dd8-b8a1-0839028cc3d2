// Enhanced Header Section
.roles-header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 0 0 1rem 1rem;

  .page-header {
    .page-title {
      font-size: 1.75rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .page-subtitle {
      font-size: 1rem;
      opacity: 0.9;
    }
  }

  .header-actions {
    .btn {
      border-radius: 0.5rem;
      font-weight: 500;

      &.btn-primary {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }
      }

      &.btn-outline-secondary {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
          color: white;
        }
      }
    }
  }
}

// Search and Filter Section
.search-filter-section {
  .search-box {
    .input-group {
      border-radius: 0.75rem;
      overflow: hidden;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

      .input-group-text {
        background: #f8f9fa;
        border: none;
        color: #6c757d;
      }

      .form-control {
        border: none;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }

      .btn {
        border: none;
        background: #f8f9fa;

        &:hover {
          background: #e9ecef;
        }
      }
    }
  }

  .form-select {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;

    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// Quick Stats Cards
.quick-stats-row {
  .stat-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      width: 3rem;
      height: 3rem;
      border-radius: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      color: white;

      &.bg-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.bg-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
      &.bg-info { background: linear-gradient(135deg, #3ca55c 0%, #b5ac49 100%); }
      &.bg-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    }

    .stat-content {
      .stat-number {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: #2d3748;
      }

      .stat-label {
        font-size: 0.875rem;
        color: #718096;
        margin-bottom: 0;
        font-weight: 500;
      }
    }
  }
}

// Modern table styling
.modern-table-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 1rem;
  overflow: hidden;
}

.modern-table {
  th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;

    &[sortable] {
      cursor: pointer;
      user-select: none;
      position: relative;

      &:hover {
        background-color: #e9ecef;
      }

      &::after {
        content: '';
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid #6c757d;
        opacity: 0.3;
      }

      &.asc::after {
        border-bottom: 4px solid #007bff;
        border-top: none;
        opacity: 1;
      }

      &.desc::after {
        border-top: 4px solid #007bff;
        border-bottom: none;
        opacity: 1;
      }
    }
  }

  td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}



// Empty state styling
.empty-state {
  padding: 2rem;
  text-align: center;
  color: #6c757d;

  i {
    color: #adb5bd;
  }
}

// Badge styling
.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

// Permission checkboxes styling
.form-check {
  .form-check-input {
    &:checked {
      background-color: #007bff;
      border-color: #007bff;
    }

    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  .form-check-label {
    cursor: pointer;

    small {
      font-size: 0.75rem;
      line-height: 1.2;
    }
  }
}

// Modal styling
.modal-header {
  border-bottom: 1px solid #dee2e6;

  .modal-title {
    font-weight: 600;
    color: #495057;
  }
}

.modal-footer {
  border-top: 1px solid #dee2e6;
}

// Responsive adjustments
@media (max-width: 768px) {
  .modern-table {
    font-size: 0.875rem;

    th, td {
      padding: 0.5rem 0.25rem;
    }
  }

  .action-icons {
    .action-icon {
      margin-right: 0.25rem;
    }
  }

  .badge {
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
  }
}

// Loading spinner
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

// Search input styling
.input-group {
  .input-group-text {
    border: 1px solid #ced4da;

    &.bg-light {
      background-color: #f8f9fa !important;
    }
  }

  .form-control {
    border-left: none;

    &:focus {
      border-color: #80bdff;
      box-shadow: none;
    }
  }
}

// Role Cards Styling
.roles-grid {
  .role-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #f1f3f4;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    }

    &.deleted {
      opacity: 0.7;
      border-color: #dc3545;
      background: #fff5f5;
    }

    .role-card-header {
      padding: 1.5rem 1.5rem 1rem;
      display: flex;
      justify-content: between;
      align-items: flex-start;

      .role-info {
        flex: 1;

        .role-name {
          font-size: 1.25rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          color: #2d3748;
        }

        .role-status {
          .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 0.5rem;
            font-weight: 500;
          }
        }
      }

      .role-actions {
        .dropdown-toggle {
          border: none;
          background: transparent;
          color: #6c757d;

          &:hover {
            background: #f8f9fa;
            color: #495057;
          }
        }
      }
    }

    .role-card-body {
      padding: 0 1.5rem 1rem;

      .role-description {
        color: #718096;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .role-stats {
        display: flex;
        justify-content: space-between;
        gap: 1rem;

        .stat-item {
          text-align: center;
          flex: 1;

          i {
            display: block;
            margin-bottom: 0.5rem;
          }

          .stat-value {
            display: block;
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
          }

          .stat-label {
            font-size: 0.75rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    .role-card-footer {
      padding: 1rem 1.5rem 1.5rem;
      border-top: 1px solid #f1f3f4;
      background: #fafbfc;

      .quick-actions {
        display: flex;
        gap: 0.5rem;

        .btn {
          flex: 1;
          font-size: 0.8rem;
          padding: 0.5rem 0.75rem;
          border-radius: 0.5rem;
          font-weight: 500;
        }
      }
    }
  }
}

// Enhanced Table Styling
.roles-table {
  .modern-table {
    .role-avatar {
      width: 2.5rem;
      height: 2.5rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
      justify-content: center;

      .btn {
        padding: 0.375rem 0.5rem;
        border-radius: 0.375rem;
      }
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
      border-radius: 0.5rem;
      font-weight: 500;

      &.bg-primary-subtle { background-color: #cfe2ff !important; color: #084298 !important; }
      &.bg-warning-subtle { background-color: #fff3cd !important; color: #664d03 !important; }
      &.bg-success-subtle { background-color: #d1e7dd !important; color: #0f5132 !important; }
      &.bg-danger-subtle { background-color: #f8d7da !important; color: #842029 !important; }
    }
  }
}

// Loading Section
.loading-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

// Empty State
.empty-state-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin: 2rem 0;

  .empty-state-icon {
    .icon-xxl {
      width: 4rem;
      height: 4rem;
    }
  }
}

// Pagination Section
.pagination-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  .pagination-info {
    font-size: 0.9rem;
    color: #6c757d;
  }
}

// Permission selection area
.border.rounded {
  border-color: #dee2e6 !important;

  .form-check {
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .border-bottom {
    border-color: #dee2e6 !important;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .roles-header-section {
    padding: 1.5rem 0;

    .page-title {
      font-size: 1.5rem !important;
    }

    .header-actions {
      margin-top: 1rem;

      .btn {
        width: 100%;
        margin-bottom: 0.5rem;
      }
    }
  }

  .search-filter-section {
    .row > div {
      margin-bottom: 1rem;
    }
  }

  .quick-stats-row {
    .stat-card {
      margin-bottom: 1rem;
    }
  }

  .roles-grid {
    .role-card {
      margin-bottom: 1.5rem;
    }
  }
}

// Advanced Filters Panel
.advanced-filters-panel {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
  margin-bottom: 0;

  &.show {
    max-height: 500px;
    opacity: 1;
    margin-bottom: 2rem;
  }

  .card {
    border: 1px solid #e3e6f0;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);

    .card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-bottom: none;

      .card-title {
        color: white;
        font-weight: 600;
      }
    }

    .card-body {
      padding: 1.5rem;

      .form-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 0.5rem;
      }

      .form-control,
      .form-select {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
        font-size: 0.875rem;

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        &.form-control-sm,
        &.form-select-sm {
          padding: 0.375rem 0.75rem;
          font-size: 0.8rem;
        }
      }

      .border-top {
        border-color: #e3e6f0 !important;
      }

      .filter-summary {
        .text-muted {
          font-size: 0.875rem;
        }
      }

      .filter-actions {
        .btn-sm {
          padding: 0.375rem 0.75rem;
          font-size: 0.8rem;
          border-radius: 0.35rem;
        }
      }
    }
  }
}

// Filter Indicators
.search-filter-section {
  .btn {
    &.btn-outline-primary {
      border-color: #667eea;
      color: #667eea;

      &:hover {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
      }

      &.active {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
      }
    }
  }
}

// Enhanced Form Controls
.form-control,
.form-select {
  transition: all 0.15s ease-in-out;

  &:focus {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075), 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }
}

// Filter Badge Indicators
.filter-indicator {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
    display: none;
  }

  &.has-filters::after {
    display: block;
  }
}

// Animation for filter toggles
@keyframes filterSlideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500px;
    opacity: 1;
  }
}

@keyframes filterSlideUp {
  from {
    max-height: 500px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

// Mobile Responsive for Advanced Filters
@media (max-width: 768px) {
  .advanced-filters-panel {
    &.show {
      max-height: 800px; // More space needed on mobile
    }

    .card-body {
      padding: 1rem;

      .row > div {
        margin-bottom: 1rem;
      }

      .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;

        .form-control,
        .form-select {
          width: 100% !important;
        }
      }

      .filter-actions {
        flex-direction: column;
        gap: 0.5rem;

        .btn {
          width: 100%;
        }
      }
    }
  }
}
