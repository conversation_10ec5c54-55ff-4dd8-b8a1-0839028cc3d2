<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card inner-card">
      <div class="card-body p-0">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">RERA Details</h4>
            <p class="text-secondary">Manage RERA and professional details</p>
          </div>
        </div>

        <form [formGroup]="reraForm" (ngSubmit)="onSubmit()">
          <!-- RERA Basic Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">RERA Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-4 mb-3">
              <label for="reraNo" class="form-label">RERA No.</label>
              <input type="text" class="form-control" id="reraNo" formControlName="reraNo"
                [ngClass]="{'is-invalid': isFormSubmitted && form['reraNo'].errors}">
              @if (isFormSubmitted && form['reraNo'].errors?.required) {
                <div class="invalid-feedback">RERA number is required</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-4 mb-3">
              <label for="mobile" class="form-label">Mobile/Landline</label>
              <input type="text" class="form-control" id="mobile" formControlName="mobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['mobile'].errors}">
              @if (isFormSubmitted && form['mobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-4 mb-3">
              <label for="website" class="form-label">Website</label>
              <input type="text" class="form-control" id="website" formControlName="website">
            </div>
          </div>

          <!-- Architect Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Architect Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="architectCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="architectCompanyName" formControlName="architectCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="architectName" class="form-label">Name</label>
              <input type="text" class="form-control" id="architectName" formControlName="architectName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="architectMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="architectMobile" formControlName="architectMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['architectMobile'].errors}">
              @if (isFormSubmitted && form['architectMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="architectEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="architectEmail" formControlName="architectEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['architectEmail'].errors}">
              @if (isFormSubmitted && form['architectEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Engineer Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Engineer Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="engineerCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="engineerCompanyName" formControlName="engineerCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="engineerName" class="form-label">Name</label>
              <input type="text" class="form-control" id="engineerName" formControlName="engineerName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="engineerMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="engineerMobile" formControlName="engineerMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['engineerMobile'].errors}">
              @if (isFormSubmitted && form['engineerMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="engineerEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="engineerEmail" formControlName="engineerEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['engineerEmail'].errors}">
              @if (isFormSubmitted && form['engineerEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- CA Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">CA Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="caCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="caCompanyName" formControlName="caCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="caName" class="form-label">Name</label>
              <input type="text" class="form-control" id="caName" formControlName="caName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="caMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="caMobile" formControlName="caMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['caMobile'].errors}">
              @if (isFormSubmitted && form['caMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="caEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="caEmail" formControlName="caEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['caEmail'].errors}">
              @if (isFormSubmitted && form['caEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Structural Surveyor Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Structural Surveyor Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ssCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="ssCompanyName" formControlName="ssCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ssName" class="form-label">Name</label>
              <input type="text" class="form-control" id="ssName" formControlName="ssName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ssMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="ssMobile" formControlName="ssMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['ssMobile'].errors}">
              @if (isFormSubmitted && form['ssMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ssEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="ssEmail" formControlName="ssEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['ssEmail'].errors}">
              @if (isFormSubmitted && form['ssEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Advocate Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Advocate Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="advocateCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="advocateCompanyName" formControlName="advocateCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="advocateName" class="form-label">Name</label>
              <input type="text" class="form-control" id="advocateName" formControlName="advocateName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="advocateMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="advocateMobile" formControlName="advocateMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['advocateMobile'].errors}">
              @if (isFormSubmitted && form['advocateMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="advocateEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="advocateEmail" formControlName="advocateEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['advocateEmail'].errors}">
              @if (isFormSubmitted && form['advocateEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Searcher Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Searcher Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="searcherCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="searcherCompanyName" formControlName="searcherCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="searcherName" class="form-label">Name</label>
              <input type="text" class="form-control" id="searcherName" formControlName="searcherName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="searcherMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="searcherMobile" formControlName="searcherMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['searcherMobile'].errors}">
              @if (isFormSubmitted && form['searcherMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="searcherEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="searcherEmail" formControlName="searcherEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['searcherEmail'].errors}">
              @if (isFormSubmitted && form['searcherEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Sole Seller Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Sole Seller Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="soleSellerCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="soleSellerCompanyName" formControlName="soleSellerCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="soleSellerName" class="form-label">Name</label>
              <input type="text" class="form-control" id="soleSellerName" formControlName="soleSellerName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="soleSellerMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="soleSellerMobile" formControlName="soleSellerMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['soleSellerMobile'].errors}">
              @if (isFormSubmitted && form['soleSellerMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="soleSellerEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="soleSellerEmail" formControlName="soleSellerEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['soleSellerEmail'].errors}">
              @if (isFormSubmitted && form['soleSellerEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Channel Partner Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Channel Partner Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="channelPartnerCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="channelPartnerCompanyName" formControlName="channelPartnerCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="channelPartnerName" class="form-label">Name</label>
              <input type="text" class="form-control" id="channelPartnerName" formControlName="channelPartnerName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="channelPartnerMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="channelPartnerMobile" formControlName="channelPartnerMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['channelPartnerMobile'].errors}">
              @if (isFormSubmitted && form['channelPartnerMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="channelPartnerEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="channelPartnerEmail" formControlName="channelPartnerEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['channelPartnerEmail'].errors}">
              @if (isFormSubmitted && form['channelPartnerEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Other Information -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Other Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="otherCompanyName" class="form-label">Company Name</label>
              <input type="text" class="form-control" id="otherCompanyName" formControlName="otherCompanyName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="otherName" class="form-label">Name</label>
              <input type="text" class="form-control" id="otherName" formControlName="otherName">
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="otherMobile" class="form-label">Mobile No.</label>
              <input type="text" class="form-control" id="otherMobile" formControlName="otherMobile"
                [ngClass]="{'is-invalid': isFormSubmitted && form['otherMobile'].errors}">
              @if (isFormSubmitted && form['otherMobile'].errors?.pattern) {
                <div class="invalid-feedback">Please enter a valid 10-digit mobile number</div>
              }
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="otherEmail" class="form-label">Email ID</label>
              <input type="email" class="form-control" id="otherEmail" formControlName="otherEmail"
                [ngClass]="{'is-invalid': isFormSubmitted && form['otherEmail'].errors}">
              @if (isFormSubmitted && form['otherEmail'].errors?.email) {
                <div class="invalid-feedback">Please enter a valid email address</div>
              }
            </div>
          </div>

          <!-- Encumbrance Information -->
          <div class="row">
            <div class="col-12">
              <h6 class="text-primary mb-3">Encumbrance Information</h6>
            </div>
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="encumbrance" class="form-label">Encumbrance</label>
              <select class="form-select" id="encumbrance" formControlName="encumbrance"
                [ngClass]="{'is-invalid': isFormSubmitted && form['encumbrance'].errors}">
                <option value="" selected>Select Encumbrance</option>
                <option value="yes">YES</option>
                <option value="no">NO</option>
              </select>
              @if (isFormSubmitted && form['encumbrance'].errors?.required) {
                <div class="invalid-feedback">Encumbrance selection is required</div>
              }
            </div>

            <!-- Conditional fields shown when Encumbrance is YES -->
          
              <!-- Loan Information -->
              <div class="col-12">
                <h6 class="text-secondary mb-3 mt-3">Loan Information</h6>
              </div>
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="nameOfInstitute" class="form-label">Name of Institute</label>
                <input type="text" class="form-control" id="nameOfInstitute" formControlName="nameOfInstitute"
                  [ngClass]="{'is-invalid': isFormSubmitted && form['nameOfInstitute'].errors}">
                @if (isFormSubmitted && form['nameOfInstitute'].errors?.required) {
                  <div class="invalid-feedback">Name of Institute is required</div>
                }
              </div>
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="loanAmount" class="form-label">Loan Amount (Cr.)</label>
                <input type="number" class="form-control" id="loanAmount" formControlName="loanAmount"
                  [ngClass]="{'is-invalid': isFormSubmitted && form['loanAmount'].errors}"
                  placeholder="Enter loan amount">
                @if (isFormSubmitted && form['loanAmount'].errors?.required) {
                  <div class="invalid-feedback">Loan Amount is required</div>
                }
                @if (isFormSubmitted && form['loanAmount'].errors?.min) {
                  <div class="invalid-feedback">Loan Amount must be greater than 0</div>
                }
              </div>

              <!-- RERA Account Bank Details -->
              <div class="col-12">
                <h6 class="text-secondary mb-3 mt-3">RERA Account Bank Details</h6>
              </div>
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="bankName" class="form-label">Bank Name</label>
                <input type="text" class="form-control" id="bankName" formControlName="bankName"
                  [ngClass]="{'is-invalid': isFormSubmitted && form['bankName'].errors}">
                @if (isFormSubmitted && form['bankName'].errors?.required) {
                  <div class="invalid-feedback">Bank Name is required</div>
                }
              </div>
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="branch" class="form-label">Branch</label>
                <input type="text" class="form-control" id="branch" formControlName="branch"
                  [ngClass]="{'is-invalid': isFormSubmitted && form['branch'].errors}">
                @if (isFormSubmitted && form['branch'].errors?.required) {
                  <div class="invalid-feedback">Branch is required</div>
                }
              </div>
            
          </div>


        </form>
      </div>
    </div>
  </div>
</div>
