<div class="row">
  <div class="col-xl-10 main-content ps-xl-4 pe-xl-5">
    <h1 class="page-title">Media Object</h1>
    <p class="lead">Construct highly repetitive components like blog comments, tweets, and the like using the <a href="https://getbootstrap.com/docs/5.3/utilities/flex/" target="_blank">flexbox utilities</a>.</p>
    
    <hr>
    
    <h4 #default>Basic example</h4>
    <div class="example">
      <div class="d-flex align-items-start">
        <img src="images/others/placeholder.jpg" class="w-100px w-sm-200px me-3" alt="...">
        <div>
          <h5 class="mb-2">Media heading</h5>
          <p>Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.</p>
        </div>
      </div>
    </div>
    <app-code-preview [codeContent]="defaultMediaObjectCode"></app-code-preview>
    
    <hr>
    
    <h4 #nesting>Nesting</h4>
    <p class="mb-3">Can be infinitely nested.</p>
    <div class="example">
      <div class="d-flex align-items-start">
        <img src="images/others/placeholder.jpg" class="w-100px w-sm-150px me-3" alt="...">
        <div>
          <h5 class="mb-2">Media heading</h5>
          Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
      
          <div class="d-flex align-items-start mt-3">
            <a class="me-3" href="#">
              <img src="images/others/placeholder.jpg" class="w-100px w-sm-150px" alt="...">
            </a>
            <div>
              <h5 class="mb-2">Media heading</h5>
              Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
            </div>
          </div>
        </div>
      </div>
    </div>
    <app-code-preview [codeContent]="mediaObjectNestingCode"></app-code-preview>
    
    <hr>
    
    <h4 #alignment>Alignment</h4>
    <p class="mb-3">Can be aligned to the top (default), middle, or end.</p>
    <div class="example">
      <div class="d-flex align-items-start">
        <img src="images/others/placeholder.jpg" class="align-self-start w-100px w-sm-150px me-3" alt="...">
        <div>
          <h5 class="mb-2">Top-aligned media</h5>
          <p>Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.</p>
          <p>Donec sed odio dui. Nullam quis risus eget urna mollis ornare vel eu leo. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.</p>
        </div>
      </div>
    </div>
    <app-code-preview [codeContent]="mediaObjectAlignmentCode"></app-code-preview>
    
    <div class="example">
      <div class="d-flex align-items-start">
        <img src="images/others/placeholder.jpg" class="align-self-center w-100px w-sm-150px me-3" alt="...">
        <div>
          <h5 class="mb-2">Center-aligned media</h5>
          <p>Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.</p>
          <p>Donec sed odio dui. Nullam quis risus eget urna mollis ornare vel eu leo. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.</p>
        </div>
      </div>
    </div>
    <app-code-preview [codeContent]="mediaObjectCenterAlignCode"></app-code-preview>
    
    <div class="example">
      <div class="d-flex align-items-start">
        <img src="images/others/placeholder.jpg" class="align-self-end w-100px w-sm-150px me-3" alt="...">
        <div>
          <h5 class="mb-2">Bottom-aligned media</h5>
          <p>Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.</p>
          <p>Donec sed odio dui. Nullam quis risus eget urna mollis ornare vel eu leo. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.</p>
        </div>
      </div>
    </div>
    <app-code-preview [codeContent]="mediaObjectBottomAlignCode"></app-code-preview>

    <hr>
    
    <h4 #order>Order</h4>
    <div class="example">
      <div class="d-flex align-items-start">
        <div>
          <h5 class="mt-0 mb-1">Media object</h5>
          Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
        </div>
        <img src="images/others/placeholder.jpg" class="w-100px w-sm-150px ms-3" alt="...">
      </div>
    </div>
    <app-code-preview [codeContent]="mediaObjectOrderCode"></app-code-preview>
    
    <hr>

    <h4 #mediaList>Media list</h4>
    <div class="example">
      <ul class="list-unstyled">
        <li class="d-flex align-items-start">
          <img src="images/others/placeholder.jpg" class="w-100px w-sm-150px me-3" alt="...">
          <div>
            <h5 class="mt-0 mb-1">List-based media object</h5>
            Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
          </div>
        </li>
        <li class="d-flex align-items-start my-4">
          <img src="images/others/placeholder.jpg" class="w-100px w-sm-150px me-3" alt="...">
          <div>
            <h5 class="mt-0 mb-1">List-based media object</h5>
            Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
          </div>
        </li>
        <li class="d-flex align-items-start">
          <img src="images/others/placeholder.jpg" class="w-100px w-sm-150px me-3" alt="...">
          <div>
            <h5 class="mt-0 mb-1">List-based media object</h5>
            Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
          </div>
        </li>
      </ul>
    </div>
    <app-code-preview [codeContent]="mediaObjectListCode"></app-code-preview>
                
  </div>
  <div class="col-xl-2 content-nav-wrapper">
    <ul class="nav content-nav d-flex flex-column">
      <li class="nav-item">
        <a (click)="scrollTo(default)" class="nav-link">Basic example</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(nesting)" class="nav-link">Nesting</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(alignment)" class="nav-link">Alignment</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(order)" class="nav-link">Order</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(mediaList)" class="nav-link">Media list</a>
      </li>
    </ul>
  </div>
</div>