import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface LegendItem {
  label: string;
  colorClass: string;
  description?: string;
}

@Component({
  selector: 'app-calendar-legend',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="calendar-legend" *ngIf="showLegend">
      <div class="legend-title" *ngIf="title">
        <small class="text-muted">{{ title }}</small>
      </div>
      <div class="legend-items">
        <div class="legend-item" *ngFor="let item of legendItems" [title]="item.description">
          <div class="legend-color" [ngClass]="item.colorClass"></div>
          <span class="legend-label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .calendar-legend {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #dee2e6;
    }

    .legend-title {
      margin-bottom: 8px;
      font-weight: 500;
    }

    .legend-items {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #495057;
    }

    .legend-color {
      width: 14px;
      height: 14px;
      border-radius: 3px;
      border: 1px solid #dee2e6;
      flex-shrink: 0;
    }

    .legend-color.available {
      background-color: #ffffff;
      border-color: #28a745;
    }

    .legend-color.weekend {
      background-color: #f8f9fa;
      border-color: #6c757d;
    }

    .legend-color.holiday {
      background-color: #fff3cd;
      border-color: #ffc107;
    }

    .legend-color.restricted {
      background-color: #f8d7da;
      border-color: #dc3545;
    }

    .legend-label {
      font-weight: 500;
    }

    @media (max-width: 576px) {
      .legend-items {
        flex-direction: column;
        gap: 8px;
      }
    }
  `]
})
export class CalendarLegendComponent {
  @Input() showLegend = true;
  @Input() title = 'Date Types';
  @Input() customItems: LegendItem[] = [];

  get legendItems(): LegendItem[] {
    if (this.customItems.length > 0) {
      return this.customItems;
    }

    return [
      {
        label: 'Available',
        colorClass: 'available',
        description: 'Available dates for leave application'
      },
      {
        label: 'Weekend',
        colorClass: 'weekend',
        description: 'Weekends (Saturday & Sunday) - Not selectable'
      },
      {
        label: 'Holiday',
        colorClass: 'holiday',
        description: 'Public holidays - Not selectable'
      }
    ];
  }
}
