<!-- Corporate Consultancies Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-briefcase me-2"></i>
              Corporate Consultancies Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_consultancies }} total consultancies, 
              {{ statistics.active_consultancies }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Consultancy
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Consultancies
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Consultancies
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedType" (change)="onTypeFilter()">
                <option value="">All Types</option>
                <option *ngFor="let type of consultancyTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedPartnershipLevel" (change)="onPartnershipLevelFilter()">
                <option value="">All Levels</option>
                <option *ngFor="let level of partnershipLevels" [value]="level.value">
                  {{ level.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCountry" (change)="onCountryFilter()">
                <option value="">All Countries</option>
                <option *ngFor="let country of countries" [value]="country">
                  {{ country }}
                </option>
              </select>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <button class="btn btn-outline-danger w-100" 
                      [disabled]="selectedConsultancies.size === 0"
                      (click)="bulkDelete()">
                <i class="feather icon-trash-2 me-1"></i>
                Delete
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading consultancies...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40" *ngIf="viewMode === 'active'">
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Consultancy</th>
                  <th>Type & Specialization</th>
                  <th>Contact</th>
                  <th>Location</th>
                  <th>Partnership</th>
                  <th>Performance</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let consultancy of getCurrentList(); trackBy: trackByConsultancyId">
                  <td *ngIf="viewMode === 'active'">
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedConsultancies.has(consultancy.id)"
                           (change)="toggleSelection(consultancy.id)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ consultancy.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ consultancy.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="consultancy.description">
                        {{ consultancy.description }}
                      </small>
                      <small class="d-block text-muted" *ngIf="consultancy.website">
                        <i class="feather icon-globe me-1"></i>
                        <a [href]="consultancy.website" target="_blank" class="text-decoration-none">
                          {{ consultancy.website }}
                        </a>
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ getConsultancyTypeLabel(consultancy.type) }}</span>
                    <small class="d-block text-muted mt-1" *ngIf="consultancy.specialization">
                      <strong>Specialization:</strong> {{ consultancy.specialization }}
                    </small>
                    <small class="d-block text-muted" *ngIf="consultancy.regulatory_body">
                      {{ consultancy.regulatory_body }}
                    </small>
                  </td>
                  <td>
                    <div *ngIf="consultancy.contact_email || consultancy.contact_phone">
                      <small class="d-block text-muted" *ngIf="consultancy.contact_email">
                        <i class="feather icon-mail me-1"></i>
                        {{ consultancy.contact_email }}
                      </small>
                      <small class="d-block text-muted" *ngIf="consultancy.contact_phone">
                        <i class="feather icon-phone me-1"></i>
                        {{ consultancy.contact_phone }}
                      </small>
                    </div>
                    <span *ngIf="!consultancy.contact_email && !consultancy.contact_phone" class="text-muted fst-italic">
                      No contact info
                    </span>
                  </td>
                  <td>
                    <div *ngIf="consultancy.city || consultancy.country">
                      <small class="d-block text-muted" *ngIf="consultancy.city">
                        <i class="feather icon-map-pin me-1"></i>
                        {{ consultancy.city }}
                      </small>
                      <small class="d-block text-muted" *ngIf="consultancy.country">
                        {{ consultancy.country }}
                      </small>
                    </div>
                    <span *ngIf="!consultancy.city && !consultancy.country" class="text-muted fst-italic">
                      Not specified
                    </span>
                  </td>
                  <td>
                    <span *ngIf="consultancy.partnership_level" 
                          [class]="getPartnershipLevelBadgeClass(consultancy.partnership_level)">
                      {{ getPartnershipLevelLabel(consultancy.partnership_level) }}
                    </span>
                    <small class="d-block text-muted mt-1" *ngIf="consultancy.client_count">
                      {{ consultancy.client_count }} clients
                    </small>
                  </td>
                  <td>
                    <div class="performance-metrics">
                      <small class="d-block text-muted" *ngIf="consultancy.rating">
                        <strong>Rating:</strong> {{ getRatingStars(consultancy.rating) }} ({{ consultancy.rating }})
                      </small>
                      <small class="d-block text-muted" *ngIf="consultancy.annual_revenue">
                        <strong>Revenue:</strong> {{ formatRevenue(consultancy.annual_revenue) }}
                      </small>
                      <small class="d-block text-muted" *ngIf="consultancy.employee_count">
                        <strong>Employees:</strong> {{ consultancy.employee_count }}
                      </small>
                      <span *ngIf="!consultancy.rating && !consultancy.annual_revenue && !consultancy.employee_count" 
                            class="text-muted fst-italic">
                        No metrics
                      </span>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(consultancy.is_active)">
                      {{ getStatusText(consultancy.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ consultancy.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(consultancy)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteConsultancy(consultancy)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreConsultancy(consultancy)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-briefcase text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Consultancies' : 'No Consultancies Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No consultancies have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No consultancies match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first consultancy.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Consultancy
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} consultancies
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_consultancies }}</h3>
                      <p class="mb-0">Total Consultancies</p>
                    </div>
                    <i class="feather icon-briefcase" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_consultancies }}</h3>
                      <p class="mb-0">Active Consultancies</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_clients }}</h3>
                      <p class="mb-0">Total Clients</p>
                    </div>
                    <i class="feather icon-users" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.average_rating | number:'1.1-1' }}</h3>
                      <p class="mb-0">Average Rating</p>
                    </div>
                    <i class="feather icon-star" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Consultancies by Type -->
          <div *ngIf="statistics.consultancies_by_type" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Consultancies by Type</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.consultancies_by_type)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getConsultancyTypeLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-primary fs-6">{{ statistics.consultancies_by_type[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Consultancies by Partnership Level -->
          <div *ngIf="statistics.consultancies_by_partnership_level" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Consultancies by Partnership Level</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.consultancies_by_partnership_level)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getPartnershipLevelLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span [class]="getPartnershipLevelBadgeClass(key) + ' fs-6'">
                      {{ statistics.consultancies_by_partnership_level[key] }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Consultancies -->
          <div *ngIf="statistics.popular_consultancies?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Top Performing Consultancies</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Consultancy</th>
                      <th>Type</th>
                      <th>Partnership</th>
                      <th>Rating</th>
                      <th>Clients</th>
                      <th>Revenue</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let consultancy of statistics.popular_consultancies">
                      <td>
                        <strong>{{ consultancy.name }}</strong>
                        <small class="d-block text-muted">{{ consultancy.code }}</small>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ getConsultancyTypeLabel(consultancy.type) }}</span>
                      </td>
                      <td>
                        <span *ngIf="consultancy.partnership_level" 
                              [class]="getPartnershipLevelBadgeClass(consultancy.partnership_level)">
                          {{ getPartnershipLevelLabel(consultancy.partnership_level) }}
                        </span>
                      </td>
                      <td>
                        <span *ngIf="consultancy.rating">
                          {{ getRatingStars(consultancy.rating) }} ({{ consultancy.rating }})
                        </span>
                        <span *ngIf="!consultancy.rating" class="text-muted">-</span>
                      </td>
                      <td>
                        <span class="badge bg-primary">{{ consultancy.client_count || 0 }}</span>
                      </td>
                      <td>
                        <span class="text-success">{{ formatRevenue(consultancy.annual_revenue) }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
