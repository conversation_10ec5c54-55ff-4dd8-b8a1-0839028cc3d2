import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { interval, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SmartCacheService } from '../../../core/services/smart-cache.service';

interface PerformanceMetrics {
  memoryUsage: number;
  cacheHitRate: number;
  cacheSize: number;
  loadTime: number;
  fps: number;
  bundleSize: number;
}

/**
 * Performance Monitor Component
 *
 * Displays real-time performance metrics and optimization status.
 * Only visible in development mode.
 */
@Component({
  selector: 'app-performance-monitor',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],
  template: `
    <div class="performance-monitor" *ngIf="isVisible">
      <div class="monitor-header">
        <h6 class="monitor-title">
          <i class="fas fa-tachometer-alt"></i>
          Performance Monitor
        </h6>
        <div class="monitor-controls">
          <button class="btn btn-sm btn-outline-primary" (click)="toggleExpanded()">
            <i [class]="expanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
          </button>
          <button class="btn btn-sm btn-outline-secondary" (click)="clearCache()">
            <i class="fas fa-trash"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger" (click)="hide()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="monitor-content" *ngIf="expanded">
        <!-- Quick Stats -->
        <div class="quick-stats">
          <div class="stat-item" [class.good]="metrics.cacheHitRate > 0.7" [class.warning]="metrics.cacheHitRate < 0.5">
            <div class="stat-value">{{ (metrics.cacheHitRate * 100) | number:'1.0-0' }}%</div>
            <div class="stat-label">Cache Hit Rate</div>
          </div>

          <div class="stat-item" [class.good]="metrics.memoryUsage < 50" [class.warning]="metrics.memoryUsage > 100">
            <div class="stat-value">{{ metrics.memoryUsage | number:'1.0-0' }}MB</div>
            <div class="stat-label">Memory Usage</div>
          </div>

          <div class="stat-item" [class.good]="metrics.fps > 55" [class.warning]="metrics.fps < 30">
            <div class="stat-value">{{ metrics.fps | number:'1.0-0' }}</div>
            <div class="stat-label">FPS</div>
          </div>

          <div class="stat-item" [class.good]="metrics.loadTime < 2000" [class.warning]="metrics.loadTime > 5000">
            <div class="stat-value">{{ metrics.loadTime | number:'1.0-0' }}ms</div>
            <div class="stat-label">Load Time</div>
          </div>
        </div>

        <!-- Detailed Metrics -->
        <div class="detailed-metrics">
          <div class="metric-group">
            <h6>Cache Performance</h6>
            <div class="metric-row">
              <span>Cache Size:</span>
              <span>{{ metrics.cacheSize }} items</span>
            </div>
            <div class="metric-row">
              <span>Hit Rate:</span>
              <span class="metric-value" [class.good]="cacheStats.hitRate > 0.7">
                {{ (cacheStats.hitRate * 100) | number:'1.1-1' }}%
              </span>
            </div>
            <div class="metric-row">
              <span>Total Hits:</span>
              <span>{{ cacheStats.hits }}</span>
            </div>
            <div class="metric-row">
              <span>Total Misses:</span>
              <span>{{ cacheStats.misses }}</span>
            </div>
          </div>

          <div class="metric-group">
            <h6>Memory & Performance</h6>
            <div class="metric-row">
              <span>JS Heap Used:</span>
              <span>{{ formatBytes(jsHeapUsed) }}</span>
            </div>
            <div class="metric-row">
              <span>JS Heap Total:</span>
              <span>{{ formatBytes(jsHeapTotal) }}</span>
            </div>
            <div class="metric-row">
              <span>Change Detection:</span>
              <span class="metric-value" [class.good]="changeDetectionTime < 16">
                {{ changeDetectionTime | number:'1.1-1' }}ms
              </span>
            </div>
          </div>

          <div class="metric-group">
            <h6>Optimizations Status</h6>
            <div class="optimization-item" [class.enabled]="optimizations.onPushEnabled">
              <i [class]="optimizations.onPushEnabled ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></i>
              OnPush Change Detection
            </div>
            <div class="optimization-item" [class.enabled]="optimizations.trackByEnabled">
              <i [class]="optimizations.trackByEnabled ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></i>
              TrackBy Functions
            </div>
            <div class="optimization-item" [class.enabled]="optimizations.lazyLoadingEnabled">
              <i [class]="optimizations.lazyLoadingEnabled ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></i>
              Lazy Loading
            </div>
            <div class="optimization-item" [class.enabled]="optimizations.cachingEnabled">
              <i [class]="optimizations.cachingEnabled ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></i>
              Smart Caching
            </div>
          </div>
        </div>

        <!-- Performance Tips -->
        <div class="performance-tips" *ngIf="tips.length > 0">
          <h6>Performance Tips</h6>
          <div class="tip-item" *ngFor="let tip of tips">
            <i class="fas fa-lightbulb text-warning"></i>
            {{ tip }}
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .performance-monitor {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 320px;
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      font-size: 12px;
    }

    .monitor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      border-radius: 8px 8px 0 0;
    }

    .monitor-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #495057;
    }

    .monitor-controls {
      display: flex;
      gap: 4px;
    }

    .monitor-content {
      padding: 16px;
      max-height: 400px;
      overflow-y: auto;
    }

    .quick-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 16px;
    }

    .stat-item {
      text-align: center;
      padding: 8px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background: #f8f9fa;
    }

    .stat-item.good {
      border-color: #28a745;
      background: #d4edda;
    }

    .stat-item.warning {
      border-color: #ffc107;
      background: #fff3cd;
    }

    .stat-value {
      font-size: 16px;
      font-weight: 600;
      color: #495057;
    }

    .stat-label {
      font-size: 10px;
      color: #6c757d;
      margin-top: 2px;
    }

    .detailed-metrics {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .metric-group h6 {
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #495057;
      border-bottom: 1px solid #dee2e6;
      padding-bottom: 4px;
    }

    .metric-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 11px;
    }

    .metric-value.good {
      color: #28a745;
      font-weight: 600;
    }

    .optimization-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
      font-size: 11px;
    }

    .performance-tips {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #dee2e6;
    }

    .performance-tips h6 {
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #495057;
    }

    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 8px;
      font-size: 11px;
      line-height: 1.4;
    }

    .btn {
      padding: 4px 8px;
      font-size: 10px;
      border-radius: 4px;
    }
  `]
})
export class PerformanceMonitorComponent implements OnInit, OnDestroy {
  isVisible = true;
  expanded = false;

  metrics: PerformanceMetrics = {
    memoryUsage: 0,
    cacheHitRate: 0,
    cacheSize: 0,
    loadTime: 0,
    fps: 0,
    bundleSize: 0
  };

  cacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0
  };

  jsHeapUsed = 0;
  jsHeapTotal = 0;
  changeDetectionTime = 0;

  optimizations = {
    onPushEnabled: false,
    trackByEnabled: false,
    lazyLoadingEnabled: false,
    cachingEnabled: false
  };

  tips: string[] = [];

  private destroy$ = new Subject<void>();
  private frameCount = 0;
  private lastTime = performance.now();

  constructor(
    private cdr: ChangeDetectorRef,
    private smartCache: SmartCacheService
  ) {}

  ngOnInit(): void {
    // Only show in development
    this.isVisible = !this.isProduction();

    if (this.isVisible) {
      this.startMonitoring();
      this.detectOptimizations();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleExpanded(): void {
    this.expanded = !this.expanded;
    this.cdr.markForCheck();
  }

  hide(): void {
    this.isVisible = false;
    this.cdr.markForCheck();
  }

  clearCache(): void {
    this.smartCache.clear();
    this.updateCacheStats();
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  private startMonitoring(): void {
    // Update metrics every 2 seconds
    interval(2000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateMetrics();
        this.updateCacheStats();
        this.updatePerformanceTips();
        this.cdr.markForCheck();
      });

    // Monitor FPS
    this.monitorFPS();
  }

  private updateMetrics(): void {
    // Memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.jsHeapUsed = memory.usedJSHeapSize;
      this.jsHeapTotal = memory.totalJSHeapSize;
      this.metrics.memoryUsage = memory.usedJSHeapSize / (1024 * 1024);
    }

    // Load time (from navigation start)
    if ('timing' in performance) {
      const timing = performance.timing;
      this.metrics.loadTime = timing.loadEventEnd - timing.navigationStart;
    }
  }

  private updateCacheStats(): void {
    const cacheInfo = this.smartCache.getCacheInfo();
    this.cacheStats = {
      hits: cacheInfo.stats.hits,
      misses: cacheInfo.stats.misses,
      size: cacheInfo.size,
      hitRate: cacheInfo.hitRate
    };

    this.metrics.cacheHitRate = cacheInfo.hitRate;
    this.metrics.cacheSize = cacheInfo.size;
  }

  private monitorFPS(): void {
    const measureFPS = () => {
      this.frameCount++;
      const currentTime = performance.now();

      if (currentTime >= this.lastTime + 1000) {
        this.metrics.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
        this.frameCount = 0;
        this.lastTime = currentTime;
      }

      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }

  private detectOptimizations(): void {
    // Check if OnPush is being used (simplified detection)
    this.optimizations.onPushEnabled = document.querySelectorAll('[ng-reflect-change-detection-strategy="0"]').length > 0;

    // Check if caching is enabled
    this.optimizations.cachingEnabled = this.smartCache.getCacheInfo().size > 0;

    // Check for lazy loading (simplified)
    this.optimizations.lazyLoadingEnabled = document.querySelectorAll('[loading="lazy"]').length > 0;

    // Check for trackBy functions (simplified)
    this.optimizations.trackByEnabled = document.querySelectorAll('[ng-reflect-track-by-fn]').length > 0;
  }

  private updatePerformanceTips(): void {
    this.tips = [];

    if (this.metrics.cacheHitRate < 0.5) {
      this.tips.push('Low cache hit rate. Consider caching more frequently accessed data.');
    }

    if (this.metrics.memoryUsage > 100) {
      this.tips.push('High memory usage detected. Check for memory leaks.');
    }

    if (this.metrics.fps < 30) {
      this.tips.push('Low FPS detected. Consider optimizing animations and DOM updates.');
    }

    if (!this.optimizations.onPushEnabled) {
      this.tips.push('Consider using OnPush change detection strategy for better performance.');
    }

    if (!this.optimizations.trackByEnabled) {
      this.tips.push('Add trackBy functions to ngFor loops for better rendering performance.');
    }
  }

  private isProduction(): boolean {
    return typeof window !== 'undefined' &&
           window.location.hostname !== 'localhost' &&
           !window.location.hostname.includes('dev');
  }
}
