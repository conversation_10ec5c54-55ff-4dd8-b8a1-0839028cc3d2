// Modern table card styling


// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  thead {
    background-color: rgba(var(--bs-primary-rgb), 0.05);

    th {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      // color: var(--bs-gray-700);
      padding: 12px 10px;
      border-top: none;
      border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;

      &.asc:after, &.desc:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      &.asc:after {
        border-bottom: 4px solid var(--bs-primary);
      }

      &.desc:after {
        border-top: 4px solid var(--bs-primary);
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.02);
      }

      td {
        vertical-align: middle;
        padding: 12px 10px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
      }
    }
  }
}

// Avatar styling
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 500;
}


// Assign To radio buttons
.form-check-inline {
  margin-right: 0.5rem;
  margin-bottom: 0;
  display: inline-flex;
  flex-shrink: 0;
  align-items: center; /* Vertically center the content */

  .form-check-input {
    cursor: pointer;
    margin-right: 0.2rem;
    margin-top: 0; /* Remove default top margin */
    vertical-align: middle; /* Align with text */
  }

  .form-check-label {
    font-size: 0.7rem;
    cursor: pointer;
    white-space: nowrap;
    line-height: 1; /* Reduce line height for better alignment */
    display: inline-flex; /* Use flexbox for better alignment */
    align-items: center; /* Center vertically */
    margin-bottom: 0; /* Remove bottom margin */
  }
}

// Assign To container
.d-flex.flex-wrap.align-items-center {
  flex-wrap: nowrap !important;
  overflow-x: auto;
  padding: 5px 0;
  scrollbar-width: thin;
  height: 36px; /* Fixed height for consistent alignment */
  align-items: center; /* Ensure vertical centering */

  &::-webkit-scrollbar {
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(var(--bs-primary-rgb), 0.2);
    border-radius: 4px;
  }
}

// Table cell containing radio buttons
.modern-table tbody tr td:nth-child(6) {
  vertical-align: middle;
  padding-top: 0;
  padding-bottom: 0;
}

// Employee dropdown
.form-select-sm {
  min-width: 120px;
}

// Custom dropdown styling for employee selection
.dropdown {
  .dropdown-menu {
    min-width: 200px;
    max-height: 250px;
    overflow-y: auto;
    padding: 8px;

    .form-check {
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      .form-check-input {
        cursor: pointer;
      }

      .form-check-label {
        cursor: pointer;
        font-size: 0.9rem;
        padding-left: 4px;
      }
    }
  }

  .btn-outline-secondary {
    min-width: 150px;
    text-align: left;
    position: relative;

    &::after {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

// Submitted assignments
.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

// Badge styling
.badge {
  padding: 0.4em 0.8em;
  font-weight: 500;
  font-size: 0.75rem;

  &.rounded-pill {
    padding-left: 0.8em;
    padding-right: 0.8em;
  }
}

.fw-medium {
  font-weight: 500;
}

// Pagination styling
.pagination-separated {
  .page-item {
    margin: 0 3px;

    .page-link {
      border-radius: 4px;
    }

    &.active .page-link {
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
    }

    .page-link {
      color: var(--bs-primary);

      &:focus {
        box-shadow: 0 0 0 0.15rem rgba(var(--bs-primary-rgb), 0.25);
      }
    }
  }
}
.bg-orange{
  background-color: #DF5517;
}
.bg-green{
  background-color: #3F828B;
}

// Position Cards Styling
.position-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);

    .position-employee-list {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }

  // Card variants
  &.position-card-primary {
    border-top: 4px solid var(--bs-primary);

    .position-title {
      color: var(--bs-primary);
    }
  }

  &.position-card-success {
    border-top: 4px solid var(--bs-success);

    .position-title {
      color: var(--bs-success);
    }
  }

  &.position-card-warning {
    border-top: 4px solid var(--bs-warning);

    .position-title {
      color: var(--bs-warning);
    }
  }

  .position-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0;
  }

  .position-badge {
    .badge {
      font-size: 0.75rem;
      padding: 0.4em 0.8em;
      border-radius: 4px;
    }
  }

  .position-icon {
    color: rgba(0, 0, 0, 0.2);
  }

  .count-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .count-number {
      font-size: 1.2rem;
      font-weight: 700;
      color: white;
    }

    &.count-circle-primary {
      background-color: var(--bs-primary);
      box-shadow: 0 4px 8px rgba(var(--bs-primary-rgb), 0.3);
    }

    &.count-circle-success {
      background-color: var(--bs-success);
      box-shadow: 0 4px 8px rgba(var(--bs-success-rgb), 0.3);
    }

    &.count-circle-warning {
      background-color: var(--bs-warning);
      box-shadow: 0 4px 8px rgba(var(--bs-warning-rgb), 0.3);
    }

    &.count-circle-secondary {
      background-color: var(--bs-secondary);
      box-shadow: 0 4px 8px rgba(var(--bs-secondary-rgb), 0.3);
    }
  }

  .count-label {
    .text-muted {
      font-size: 0.8rem;
      display: block;
      margin-bottom: 2px;
    }

    .progress {
      border-radius: 2px;
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

// Custom scrollbar styling
// Global scrollbar styling for the entire component
:host ::ng-deep {
  ::-webkit-scrollbar {
    height: 4px; // Reduced height for horizontal scrollbar
    width: 6px; // Slightly wider for vertical scrollbar for better usability
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
  }

  // Firefox scrollbar styling
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
  }
}

// Position Employee List Styling
.position-employee-list {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  padding: 1rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .employee-list-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    h6 {
      font-weight: 600;
      margin-bottom: 0;
      color: var(--bs-gray-800);
    }
  }

  .employee-list {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
    overflow-y: auto;

    .employee-item {
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);

      &:last-child {
        border-bottom: none;
      }

      .employee-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--bs-gray-700);
      }

      .employee-count {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}