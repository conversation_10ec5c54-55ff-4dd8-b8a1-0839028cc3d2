import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, timer, BehaviorSubject } from 'rxjs';
import { catchError, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Health Check interfaces based on OpenAPI specification
 */
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  message: string;
  details?: any;
  timestamp: string;
  response_time_ms?: number;
}

export interface SystemChecks {
  database: HealthCheckResult;
  redis: HealthCheckResult;
  disk: HealthCheckResult;
  memory: HealthCheckResult;
  cpu: HealthCheckResult;
}

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  uptime: number;
  checks: SystemChecks;
  overall_status: 'healthy' | 'unhealthy' | 'degraded';
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * Health Service
 * Handles system health monitoring, status checks, and service availability.
 * Provides real-time health monitoring with automatic polling capabilities.
 */
@Injectable({
  providedIn: 'root'
})
export class HealthService {
  private baseUrl = `${environment.apiUrl}/api/v1/health`;
  
  // Health status state management
  private healthStatusSubject = new BehaviorSubject<HealthStatus | null>(null);
  public healthStatus$ = this.healthStatusSubject.asObservable();
  
  // Monitoring state
  private isMonitoring = false;
  private monitoringInterval = 30000; // 30 seconds default
  private stopMonitoring$ = new BehaviorSubject<boolean>(false);

  constructor(private http: HttpClient) {}

  /**
   * Get overall system health status
   * GET /api/v1/health/
   * @returns Observable of health status
   */
  getHealthStatus(): Observable<HealthStatus> {
    return this.http.get<APIResponse<HealthStatus>>(`${this.baseUrl}/`).pipe(
      map(response => {
        if (response.success && response.data) {
          this.healthStatusSubject.next(response.data);
          return response.data;
        }
        throw new Error('Invalid health status response');
      }),
      catchError(this.handleError('getHealthStatus'))
    );
  }

  /**
   * Get detailed system checks
   * GET /api/v1/health/detailed
   * @returns Observable of detailed system checks
   */
  getDetailedHealthChecks(): Observable<SystemChecks> {
    return this.http.get<APIResponse<SystemChecks>>(`${this.baseUrl}/detailed`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid detailed health checks response');
      }),
      catchError(this.handleError('getDetailedHealthChecks'))
    );
  }

  /**
   * Check database connectivity
   * GET /api/v1/health/database
   * @returns Observable of database health check
   */
  checkDatabase(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/database`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid database health check response');
      }),
      catchError(this.handleError('checkDatabase'))
    );
  }

  /**
   * Check Redis connectivity
   * GET /api/v1/health/redis
   * @returns Observable of Redis health check
   */
  checkRedis(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/redis`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid Redis health check response');
      }),
      catchError(this.handleError('checkRedis'))
    );
  }

  /**
   * Check disk usage
   * GET /api/v1/health/disk
   * @returns Observable of disk health check
   */
  checkDisk(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/disk`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid disk health check response');
      }),
      catchError(this.handleError('checkDisk'))
    );
  }

  /**
   * Check memory usage
   * GET /api/v1/health/memory
   * @returns Observable of memory health check
   */
  checkMemory(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/memory`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid memory health check response');
      }),
      catchError(this.handleError('checkMemory'))
    );
  }

  /**
   * Check CPU usage
   * GET /api/v1/health/cpu
   * @returns Observable of CPU health check
   */
  checkCpu(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/cpu`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid CPU health check response');
      }),
      catchError(this.handleError('checkCpu'))
    );
  }

  /**
   * Get application readiness status
   * GET /api/v1/health/ready
   * @returns Observable of readiness status
   */
  getReadinessStatus(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/ready`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid readiness status response');
      }),
      catchError(this.handleError('getReadinessStatus'))
    );
  }

  /**
   * Get application liveness status
   * GET /api/v1/health/live
   * @returns Observable of liveness status
   */
  getLivenessStatus(): Observable<HealthCheckResult> {
    return this.http.get<APIResponse<HealthCheckResult>>(`${this.baseUrl}/live`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid liveness status response');
      }),
      catchError(this.handleError('getLivenessStatus'))
    );
  }

  /**
   * Start continuous health monitoring
   * @param intervalMs Monitoring interval in milliseconds (default: 30000)
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) {
      console.warn('Health monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = intervalMs;
    this.stopMonitoring$.next(false);

    console.log(`Starting health monitoring with ${intervalMs}ms interval`);

    timer(0, intervalMs).pipe(
      takeUntil(this.stopMonitoring$),
      switchMap(() => this.getHealthStatus()),
      tap(status => {
        console.log('Health status updated:', status.overall_status);
      }),
      catchError(error => {
        console.error('Health monitoring error:', error);
        // Continue monitoring even if one check fails
        return new Observable(observer => observer.complete());
      })
    ).subscribe();
  }

  /**
   * Stop continuous health monitoring
   */
  stopHealthMonitoring(): void {
    if (!this.isMonitoring) {
      console.warn('Health monitoring is not running');
      return;
    }

    console.log('Stopping health monitoring');
    this.isMonitoring = false;
    this.stopMonitoring$.next(true);
  }

  /**
   * Check if monitoring is currently active
   * @returns Boolean indicating if monitoring is active
   */
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  /**
   * Get current monitoring interval
   * @returns Current monitoring interval in milliseconds
   */
  getMonitoringInterval(): number {
    return this.monitoringInterval;
  }

  /**
   * Perform a comprehensive health check of all systems
   * @returns Observable of all health check results
   */
  performComprehensiveHealthCheck(): Observable<{
    overall: HealthStatus;
    database: HealthCheckResult;
    redis: HealthCheckResult;
    disk: HealthCheckResult;
    memory: HealthCheckResult;
    cpu: HealthCheckResult;
    readiness: HealthCheckResult;
    liveness: HealthCheckResult;
  }> {
    return this.getHealthStatus().pipe(
      switchMap(overall => {
        // Perform all individual checks in parallel
        return new Observable(observer => {
          Promise.all([
            this.checkDatabase().toPromise(),
            this.checkRedis().toPromise(),
            this.checkDisk().toPromise(),
            this.checkMemory().toPromise(),
            this.checkCpu().toPromise(),
            this.getReadinessStatus().toPromise(),
            this.getLivenessStatus().toPromise()
          ]).then(([database, redis, disk, memory, cpu, readiness, liveness]) => {
            observer.next({
              overall,
              database: database!,
              redis: redis!,
              disk: disk!,
              memory: memory!,
              cpu: cpu!,
              readiness: readiness!,
              liveness: liveness!
            });
            observer.complete();
          }).catch(error => {
            observer.error(error);
          });
        });
      })
    );
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // For health checks, we might want to return a default unhealthy status
      if (operation.includes('check') || operation.includes('Health') || operation.includes('Status')) {
        const unhealthyResult: HealthCheckResult = {
          status: 'unhealthy',
          message: `${operation} failed: ${error.message || 'Unknown error'}`,
          timestamp: new Date().toISOString(),
          details: { error: error.message }
        };
        
        if (result === undefined) {
          return new Observable(observer => {
            observer.next(unhealthyResult as T);
            observer.complete();
          });
        }
      }

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
