import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, timer } from 'rxjs';
import { switchMap, tap, catchError } from 'rxjs/operators';

export interface LoadingState {
  isLoading: boolean;
  progress: number;
  stage: string;
  error?: string;
}

export interface ComponentLoadConfig {
  priority: 'high' | 'medium' | 'low';
  delay?: number;
  dependencies?: string[];
  preload?: boolean;
}

/**
 * Progressive Loading Service
 * 
 * Manages progressive loading of components and resources to improve
 * perceived performance and user experience.
 */
@Injectable({
  providedIn: 'root'
})
export class ProgressiveLoadingService {
  private loadingStateSubject = new BehaviorSubject<LoadingState>({
    isLoading: false,
    progress: 0,
    stage: 'idle'
  });

  public loadingState$ = this.loadingStateSubject.asObservable();

  // Track loaded components to avoid duplicate loading
  private loadedComponents = new Set<string>();
  
  // Queue for managing loading priorities
  private loadingQueue: Array<{
    id: string;
    config: ComponentLoadConfig;
    loader: () => Promise<any>;
  }> = [];

  private isProcessingQueue = false;

  constructor() {
    console.log('🚀 ProgressiveLoadingService: Initialized');
  }

  /**
   * Register a component for progressive loading
   */
  registerComponent(
    componentId: string, 
    loader: () => Promise<any>, 
    config: ComponentLoadConfig = { priority: 'medium' }
  ): void {
    if (this.loadedComponents.has(componentId)) {
      console.log(`📦 ProgressiveLoadingService: Component ${componentId} already loaded`);
      return;
    }

    this.loadingQueue.push({
      id: componentId,
      config,
      loader
    });

    // Sort queue by priority
    this.loadingQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.config.priority] - priorityOrder[a.config.priority];
    });

    console.log(`📋 ProgressiveLoadingService: Registered ${componentId} with priority ${config.priority}`);

    // Start processing if not already running
    if (!this.isProcessingQueue) {
      this.processQueue();
    }
  }

  /**
   * Load a component immediately (bypass queue)
   */
  loadComponentImmediate(componentId: string, loader: () => Promise<any>): Observable<any> {
    if (this.loadedComponents.has(componentId)) {
      console.log(`✅ ProgressiveLoadingService: Component ${componentId} already loaded`);
      return of(null);
    }

    this.updateLoadingState(true, 0, `Loading ${componentId}`);

    return new Observable(observer => {
      loader()
        .then(result => {
          this.loadedComponents.add(componentId);
          this.updateLoadingState(false, 100, `Loaded ${componentId}`);
          console.log(`✅ ProgressiveLoadingService: Loaded ${componentId} immediately`);
          observer.next(result);
          observer.complete();
        })
        .catch(error => {
          this.updateLoadingState(false, 0, 'Error', error.message);
          console.error(`❌ ProgressiveLoadingService: Failed to load ${componentId}:`, error);
          observer.error(error);
        });
    });
  }

  /**
   * Preload components based on user behavior patterns
   */
  preloadComponents(componentIds: string[]): void {
    console.log(`🔮 ProgressiveLoadingService: Preloading ${componentIds.length} components`);
    
    componentIds.forEach((id, index) => {
      // Stagger preloading to avoid overwhelming the browser
      timer(index * 100).subscribe(() => {
        const queueItem = this.loadingQueue.find(item => item.id === id);
        if (queueItem && queueItem.config.preload) {
          this.loadComponentImmediate(id, queueItem.loader).subscribe({
            next: () => console.log(`🔮 Preloaded: ${id}`),
            error: (error) => console.warn(`⚠️ Preload failed for ${id}:`, error)
          });
        }
      });
    });
  }

  /**
   * Load components based on viewport intersection
   */
  loadOnIntersection(
    element: Element, 
    componentId: string, 
    loader: () => Promise<any>,
    config: ComponentLoadConfig = { priority: 'low' }
  ): Observable<any> {
    return new Observable(observer => {
      const intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              console.log(`👁️ ProgressiveLoadingService: ${componentId} entered viewport`);
              
              this.loadComponentImmediate(componentId, loader).subscribe({
                next: (result) => {
                  observer.next(result);
                  observer.complete();
                  intersectionObserver.disconnect();
                },
                error: (error) => {
                  observer.error(error);
                  intersectionObserver.disconnect();
                }
              });
            }
          });
        },
        {
          rootMargin: '50px', // Start loading 50px before element is visible
          threshold: 0.1
        }
      );

      intersectionObserver.observe(element);

      // Cleanup function
      return () => {
        intersectionObserver.disconnect();
      };
    });
  }

  /**
   * Load components after a delay (for non-critical features)
   */
  loadAfterDelay(
    componentId: string, 
    loader: () => Promise<any>, 
    delay: number = 2000
  ): Observable<any> {
    console.log(`⏰ ProgressiveLoadingService: Scheduling ${componentId} to load after ${delay}ms`);
    
    return timer(delay).pipe(
      switchMap(() => this.loadComponentImmediate(componentId, loader)),
      catchError(error => {
        console.error(`❌ ProgressiveLoadingService: Delayed load failed for ${componentId}:`, error);
        return of(null);
      })
    );
  }

  /**
   * Check if a component is loaded
   */
  isComponentLoaded(componentId: string): boolean {
    return this.loadedComponents.has(componentId);
  }

  /**
   * Get loading statistics
   */
  getLoadingStats(): {
    totalRegistered: number;
    totalLoaded: number;
    loadingProgress: number;
    queueLength: number;
  } {
    const totalRegistered = this.loadedComponents.size + this.loadingQueue.length;
    const totalLoaded = this.loadedComponents.size;
    
    return {
      totalRegistered,
      totalLoaded,
      loadingProgress: totalRegistered > 0 ? (totalLoaded / totalRegistered) * 100 : 0,
      queueLength: this.loadingQueue.length
    };
  }

  /**
   * Process the loading queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.loadingQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`🔄 ProgressiveLoadingService: Processing queue with ${this.loadingQueue.length} items`);

    while (this.loadingQueue.length > 0) {
      const item = this.loadingQueue.shift()!;
      
      if (this.loadedComponents.has(item.id)) {
        continue; // Skip if already loaded
      }

      try {
        // Apply delay if specified
        if (item.config.delay) {
          await new Promise(resolve => setTimeout(resolve, item.config.delay));
        }

        this.updateLoadingState(true, 0, `Loading ${item.id}`);
        
        const result = await item.loader();
        this.loadedComponents.add(item.id);
        
        console.log(`✅ ProgressiveLoadingService: Loaded ${item.id} from queue`);
        
        // Small delay between queue items to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 50));
        
      } catch (error) {
        console.error(`❌ ProgressiveLoadingService: Failed to load ${item.id} from queue:`, error);
      }
    }

    this.updateLoadingState(false, 100, 'Queue processing complete');
    this.isProcessingQueue = false;
    console.log('✅ ProgressiveLoadingService: Queue processing complete');
  }

  /**
   * Update loading state
   */
  private updateLoadingState(
    isLoading: boolean, 
    progress: number, 
    stage: string, 
    error?: string
  ): void {
    this.loadingStateSubject.next({
      isLoading,
      progress,
      stage,
      error
    });
  }

  /**
   * Clear all loaded components (for testing/debugging)
   */
  clearLoadedComponents(): void {
    this.loadedComponents.clear();
    this.loadingQueue.length = 0;
    this.isProcessingQueue = false;
    console.log('🗑️ ProgressiveLoadingService: Cleared all loaded components');
  }
}
