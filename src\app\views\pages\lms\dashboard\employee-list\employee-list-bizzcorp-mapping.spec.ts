import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { EmployeeListComponent, Employee } from './employee-list.component';
import { EmployeeService } from '../../../../../core/services/employee.service';
import { AuthService } from '../../../../../core/services/auth.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder } from '@angular/forms';

describe('EmployeeListComponent - BizzCorp API Field Mapping', () => {
  let component: EmployeeListComponent;
  let fixture: ComponentFixture<EmployeeListComponent>;
  let employeeService: jasmine.SpyObj<EmployeeService>;
  let authService: jasmine.SpyObj<AuthService>;

  // Mock BizzCorp API response with comprehensive employee data
  const mockBizzCorpApiResponse = {
    success: true,
    data: [
      {
        id: '1910042b-3036-40cd-97cd-d2471b5de402',
        employee_code: 'Bizz-1004',
        first_name: '<PERSON><PERSON><PERSON>',
        middle_name: 'Dattatray',
        last_name: 'Said',
        blood_group: 'B+',
        date_of_birth: '1999-08-15',
        gender: 'male',
        marital_status: 'single',
        personal_email: '<EMAIL>',
        office_email: '<EMAIL>',
        phone_no: '**********',
        alternet_no: '**********',
        address: 'Mumbai, Maharashtra',
        joining_date: '2022-08-23',
        reporting_date: '2022-08-24',
        department_id: '4d962f40-6727-4b6f-9db2-1bf6f8b6dddc',
        designation_id: '1b72e7e9-7e43-491b-9195-1a6f7c2b6dde',
        sub_role_id: 'a715253-2211-4c3b-9b51-3027b555f239',
        role: 'U',
        office_location: 'Mumbai',
        shift_time: '9:00 AM - 6:00 PM',
        approver_code: 'APP001',
        second_approver_code: 'APP002',
        ctc: 800000,
        attendance_bonus: 5000,
        pf: 12000,
        bank_name: 'HDFC Bank',
        bank_account_no: '**************',
        ifsc_no: 'HDFC0001234',
        pan_no: '**********',
        aadhar_no: '1234 5678 9012',
        uan_no: 'UAN001234567',
        esic_no: 'ESIC001234',
        is_active: true,
        resigned_stared_date: null,
        resigned_end_date: null,
        created_at: '2022-08-23T10:00:00Z',
        updated_at: '2022-08-23T10:00:00Z'
      }
    ]
  };

  beforeEach(async () => {
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees',
      'updateEmployee'
    ]);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'hasPermission',
      'getUserPermissions'
    ]);
    const modalServiceSpy = jasmine.createSpyObj('NgbModal', ['open', 'dismissAll']);

    await TestBed.configureTestingModule({
      imports: [EmployeeListComponent],
      providers: [
        FormBuilder,
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: NgbModal, useValue: modalServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EmployeeListComponent);
    component = fixture.componentInstance;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default mocks
    employeeService.getAllEmployees.and.returnValue(of(mockBizzCorpApiResponse));
    authService.hasPermission.and.returnValue(true);
    authService.getUserPermissions.and.returnValue(['employees:update']);
  });

  describe('BizzCorp API Field Mapping', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should correctly map basic employee information', () => {
      expect(component.employees.length).toBe(1);
      const employee = component.employees[0];

      expect(employee.id).toBe('1910042b-3036-40cd-97cd-d2471b5de402');
      expect(employee.name).toBe('Sushant Dattatray Said');
      expect(employee.email).toBe('<EMAIL>');
      expect(employee.phone).toBe('**********');
      expect(employee.employeeCode).toBe('Bizz-1004');
    });

    it('should correctly map personal information fields', () => {
      const employee = component.employees[0];

      expect(employee.firstName).toBe('Sushant');
      expect(employee.middleName).toBe('Dattatray');
      expect(employee.lastName).toBe('Said');
      expect(employee.bloodGroup).toBe('B+');
      expect(employee.gender).toBe('male');
      expect(employee.maritalStatus).toBe('single');
      expect(employee.personalEmail).toBe('<EMAIL>');
      expect(employee.officeEmail).toBe('<EMAIL>');
      expect(employee.phoneNo).toBe('**********');
      expect(employee.alternetNo).toBe('**********');
      expect(employee.address).toBe('Mumbai, Maharashtra');
    });

    it('should correctly map employment information fields', () => {
      const employee = component.employees[0];

      expect(employee.joiningDate).toEqual(new Date('2022-08-23'));
      expect(employee.reportingDate).toEqual(new Date('2022-08-24'));
      expect(employee.officeLocation).toBe('Mumbai');
      expect(employee.shiftTime).toBe('9:00 AM - 6:00 PM');
      expect(employee.approverCode).toBe('APP001');
      expect(employee.secondApproverCode).toBe('APP002');
      expect(employee.role).toBe('U');
      expect(employee.isActive).toBe(true);
    });

    it('should correctly map financial information fields', () => {
      const employee = component.employees[0];

      expect(employee.ctc).toBe(800000);
      expect(employee.attendanceBonus).toBe(5000);
      expect(employee.pf).toBe('12000');
    });

    it('should correctly map banking and government ID fields', () => {
      const employee = component.employees[0];

      expect(employee.bankName).toBe('HDFC Bank');
      expect(employee.bankAccountNo).toBe('**************');
      expect(employee.ifscNo).toBe('HDFC0001234');
      expect(employee.panNo).toBe('**********');
      expect(employee.aadharNo).toBe('1234 5678 9012');
      expect(employee.uanNo).toBe('UAN001234567');
      expect(employee.esicNo).toBe('ESIC001234');
    });

    it('should correctly map status information fields', () => {
      const employee = component.employees[0];

      expect(employee.isActive).toBe(true);
      expect(employee.resignedStaredDate).toBe(null);
      expect(employee.resignedEndDate).toBe(null);
    });

    it('should correctly map date fields', () => {
      const employee = component.employees[0];

      expect(employee.dateOfBirth).toEqual(new Date('1999-08-15'));
      expect(employee.joiningDate).toEqual(new Date('2022-08-23'));
      expect(employee.reportingDate).toEqual(new Date('2022-08-24'));
    });
  });

  describe('Null Value Handling', () => {
    const mockApiResponseWithNulls = {
      success: true,
      data: [
        {
          id: 'test-id',
          employee_code: 'TEST-001',
          first_name: 'Test',
          last_name: 'User',
          // All other fields are null/undefined
          blood_group: null,
          date_of_birth: null,
          gender: null,
          personal_email: null,
          phone_no: null,
          ctc: null,
          attendance_bonus: null,
          bank_name: null,
          is_active: null
        }
      ]
    };

    beforeEach(() => {
      employeeService.getAllEmployees.and.returnValue(of(mockApiResponseWithNulls));
      component.ngOnInit();
    });

    it('should handle null values gracefully', () => {
      const employee = component.employees[0];

      expect(employee.bloodGroup).toBe('');
      expect(employee.gender).toBe('');
      expect(employee.personalEmail).toBe('');
      expect(employee.phoneNo).toBe('');
      expect(employee.ctc).toBe(0);
      expect(employee.attendanceBonus).toBe(0);
      expect(employee.bankName).toBe('');
      expect(employee.isActive).toBe(false);
    });
  });

  describe('Form Data Transformation for API Updates', () => {
    it('should correctly transform form data to API format', () => {
      component.ngOnInit();
      const employee = component.employees[0];
      
      // Simulate form data
      const formData = {
        employeeCode: 'Bizz-1004',
        firstName: 'Sushant',
        middleName: 'Dattatray',
        lastName: 'Said',
        bloodGroup: 'B+',
        dateOfBirth: '1999-08-15',
        gender: 'male',
        maritalStatus: 'single',
        personalEmail: '<EMAIL>',
        officeEmail: '<EMAIL>',
        phoneNo: '**********',
        alternetNo: '**********',
        address: 'Mumbai, Maharashtra',
        ctc: 800000,
        attendanceBonus: 5000,
        bankName: 'HDFC Bank',
        bankAccountNo: '**************',
        ifscNo: 'HDFC0001234',
        panNo: '**********',
        aadharNo: '1234 5678 9012',
        uanNo: 'UAN001234567',
        esicNo: 'ESIC001234',
        officeLocation: 'Mumbai',
        shiftTime: '9:00 AM - 6:00 PM',
        approverCode: 'APP001',
        secondApproverCode: 'APP002',
        isActive: true
      };

      // Set up the form with test data
      component.editEmployeeForm.patchValue(formData);
      component.selectedEmployee = employee;

      // Mock the update service call
      employeeService.updateEmployee.and.returnValue(of({}));

      // Call save method
      component.saveEmployeeChanges();

      // Verify the API was called with correct field names
      expect(employeeService.updateEmployee).toHaveBeenCalledWith(
        employee.id,
        jasmine.objectContaining({
          employee_code: 'Bizz-1004',
          first_name: 'Sushant',
          middle_name: 'Dattatray',
          last_name: 'Said',
          blood_group: 'B+',
          date_of_birth: '1999-08-15',
          gender: 'male',
          marital_status: 'single',
          personal_email: '<EMAIL>',
          office_email: '<EMAIL>',
          phone_no: '**********',
          alternet_no: '**********',
          address: 'Mumbai, Maharashtra',
          ctc: 800000,
          attendance_bonus: 5000, // Note: correct API field name
          bank_name: 'HDFC Bank',
          bank_account_no: '**************',
          ifsc_no: 'HDFC0001234',
          pan_no: '**********',
          aadhar_no: '1234 5678 9012',
          uan_no: 'UAN001234567',
          esic_no: 'ESIC001234',
          office_location: 'Mumbai',
          shift_time: '9:00 AM - 6:00 PM',
          approver_code: 'APP001',
          second_approver_code: 'APP002',
          is_active: true
        })
      );
    });
  });

  describe('Helper Methods', () => {
    it('should correctly handle string values', () => {
      expect((component as any).getStringValue('test')).toBe('test');
      expect((component as any).getStringValue(null)).toBe('');
      expect((component as any).getStringValue(undefined)).toBe('');
      expect((component as any).getStringValue('', 'default')).toBe('default');
    });

    it('should correctly handle number values', () => {
      expect((component as any).getNumberValue(123)).toBe(123);
      expect((component as any).getNumberValue('456')).toBe(456);
      expect((component as any).getNumberValue(null)).toBe(0);
      expect((component as any).getNumberValue('invalid', 999)).toBe(999);
    });

    it('should correctly parse dates', () => {
      const testDate = '2022-08-23';
      const parsed = (component as any).parseDate(testDate);
      expect(parsed).toEqual(new Date(testDate));
      
      const invalidParsed = (component as any).parseDate('invalid-date');
      expect(invalidParsed).toBeInstanceOf(Date);
    });

    it('should correctly convert boolean values', () => {
      expect((component as any).convertToBoolean(true)).toBe(true);
      expect((component as any).convertToBoolean('true')).toBe(true);
      expect((component as any).convertToBoolean(1)).toBe(true);
      expect((component as any).convertToBoolean(false)).toBe(false);
      expect((component as any).convertToBoolean('false')).toBe(false);
      expect((component as any).convertToBoolean(0)).toBe(false);
      expect((component as any).convertToBoolean(null)).toBe(false);
    });
  });
});
