import { Injectable } from '@angular/core';
import { DynamicCSSLoaderService } from '../services/dynamic-css-loader.service';
import { ComponentPreloaderService } from '../services/component-preloader.service';
import { ProgressiveLoadingService } from '../services/progressive-loading.service';
import { VendorChunkOptimizerService } from '../services/vendor-chunk-optimizer.service';
import { ModuleFederationService } from '../services/module-federation.service';
import { BundleAnalyzerService } from '../services/bundle-analyzer.service';
import { MemoryManagerService } from '../services/memory-manager.service';

/**
 * Performance Initializer
 * 
 * Initializes performance optimization services including dynamic CSS loading,
 * component preloading, and progressive loading strategies.
 */
@Injectable({
  providedIn: 'root'
})
export class PerformanceInitializer {
  constructor(
    private dynamicCSSLoader: DynamicCSSLoaderService,
    private componentPreloader: ComponentPreloaderService,
    private progressiveLoading: ProgressiveLoadingService,
    private vendorChunkOptimizer: VendorChunkOptimizerService,
    private moduleFederation: ModuleFederationService,
    private bundleAnalyzer: BundleAnalyzerService,
    private memoryManager: MemoryManagerService
  ) {}

  /**
   * Initialize performance optimizations
   */
  initialize(): Promise<void> {
    return new Promise((resolve) => {
      console.log('🚀 PerformanceInitializer: Starting performance optimizations');

      // Initialize dynamic CSS loading
      this.initializeDynamicCSS();

      // Initialize component preloading
      this.initializeComponentPreloading();

      // Initialize progressive loading
      this.initializeProgressiveLoading();

      // Initialize code splitting optimizations
      this.initializeCodeSplitting();

      // Initialize memory management
      this.initializeMemoryManagement();

      // Setup performance monitoring
      this.setupPerformanceMonitoring();

      console.log('✅ PerformanceInitializer: Performance optimizations initialized');
      resolve();
    });
  }

  /**
   * Initialize dynamic CSS loading
   */
  private initializeDynamicCSS(): void {
    // Preload critical CSS modules
    this.dynamicCSSLoader.preloadCriticalModules();

    // Load high priority modules after initial load
    this.dynamicCSSLoader.loadHighPriorityModules();

    // Load low priority modules during idle time
    this.dynamicCSSLoader.loadLowPriorityModules();

    // Setup DOM observer for conditional loading
    this.setupDOMObserver();

    console.log('🎨 PerformanceInitializer: Dynamic CSS loading initialized');
  }

  /**
   * Initialize component preloading
   */
  private initializeComponentPreloading(): void {
    // Register common component routes for preloading
    this.registerCommonComponents();

    // Preload critical components
    this.componentPreloader.preloadCriticalComponents();

    // Setup network-based preloading
    this.componentPreloader.preloadBasedOnNetwork();

    // Setup idle-time preloading
    this.componentPreloader.preloadOnIdle();

    console.log('🎯 PerformanceInitializer: Component preloading initialized');
  }

  /**
   * Initialize progressive loading
   */
  private initializeProgressiveLoading(): void {
    // Progressive loading is initialized automatically
    // Additional setup can be added here if needed
    console.log('📈 PerformanceInitializer: Progressive loading initialized');
  }

  /**
   * Initialize code splitting optimizations
   */
  private initializeCodeSplitting(): void {
    // Start vendor chunk monitoring
    this.vendorChunkOptimizer.monitorChunkPerformance();

    // Initialize module federation for advanced splitting
    this.moduleFederation.preloadModules('critical');

    // Start bundle analysis
    this.bundleAnalyzer.analyzeBundleStructure().subscribe(analysis => {
      console.log('📊 Bundle Analysis:', analysis);

      if (analysis.recommendations.length > 0) {
        console.log('💡 Bundle Recommendations:', analysis.recommendations);
      }
    });

    console.log('📦 PerformanceInitializer: Code splitting optimizations initialized');
  }

  /**
   * Initialize memory management
   */
  private initializeMemoryManagement(): void {
    // Memory management is initialized automatically via service injection
    // Additional setup can be added here if needed

    // Get initial memory stats
    this.memoryManager.getMemoryStats().subscribe(stats => {
      console.log('🧠 Memory Stats:', stats);

      if (stats.memoryLeaks.length > 0) {
        console.warn('⚠️ Memory leaks detected:', stats.memoryLeaks);
      }

      if (stats.recommendations.length > 0) {
        console.log('💡 Memory Recommendations:', stats.recommendations);
      }
    });

    console.log('🧠 PerformanceInitializer: Memory management initialized');
  }

  /**
   * Setup DOM observer for conditional CSS loading
   */
  private setupDOMObserver(): void {
    // Use MutationObserver to detect when components are added to DOM
    const observer = new MutationObserver((mutations) => {
      let shouldCheckConditions = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldCheckConditions = true;
        }
      });

      if (shouldCheckConditions) {
        // Debounce the condition checking
        this.debounceConditionCheck();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    console.log('👁️ PerformanceInitializer: DOM observer setup for conditional CSS loading');
  }

  /**
   * Register common components for preloading
   */
  private registerCommonComponents(): void {
    // Register LMS components
    this.componentPreloader.registerComponent({
      route: '/lms/dashboard',
      componentLoader: () => import('../../views/pages/lms/dashboard/dashboard.component'),
      priority: 'high'
    });

    this.componentPreloader.registerComponent({
      route: '/lms/apply-leave',
      componentLoader: () => import('../../views/pages/lms/dashboard/apply-leave/apply-leave.component'),
      priority: 'high'
    });

    // Register employee components
    this.componentPreloader.registerComponent({
      route: '/employees/list',
      componentLoader: () => import('../../views/pages/employee/employee-list/employee-list.component'),
      priority: 'medium'
    });

    this.componentPreloader.registerComponent({
      route: '/employees/form',
      componentLoader: () => import('../../views/pages/employee/employee-form/employee-form.component'),
      priority: 'medium'
    });

    // Register master data components
    this.componentPreloader.registerComponent({
      route: '/master/master-data',
      componentLoader: () => import('../../views/pages/master/master-data/master-data.component'),
      priority: 'medium'
    });

    this.componentPreloader.registerComponent({
      route: '/master/associate',
      componentLoader: () => import('../../views/pages/master/associate/associate.component'),
      priority: 'medium'
    });

    // Register chart components
    this.componentPreloader.registerComponent({
      route: '/charts/apexcharts',
      componentLoader: () => import('../../views/pages/charts/apexcharts/apexcharts.component'),
      priority: 'low'
    });

    // Register app components
    this.componentPreloader.registerComponent({
      route: '/apps/calendar',
      componentLoader: () => import('../../views/pages/apps/calendar/calendar.component'),
      priority: 'medium'
    });

    console.log('📋 PerformanceInitializer: Registered common components for preloading');
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    // Monitor Core Web Vitals if available
    if ('web-vitals' in window || typeof window !== 'undefined') {
      this.monitorWebVitals();
    }

    // Monitor resource loading
    this.monitorResourceLoading();

    // Setup performance logging
    this.setupPerformanceLogging();

    console.log('📊 PerformanceInitializer: Performance monitoring setup');
  }

  /**
   * Monitor Core Web Vitals
   */
  private monitorWebVitals(): void {
    // This would integrate with web-vitals library if available
    // For now, we'll use basic performance API
    if ('performance' in window && 'getEntriesByType' in performance) {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const metrics = {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            firstPaint: this.getFirstPaint(),
            firstContentfulPaint: this.getFirstContentfulPaint()
          };

          console.log('📊 Performance Metrics:', metrics);
        }
      }, 1000);
    }
  }

  /**
   * Monitor resource loading
   */
  private monitorResourceLoading(): void {
    if ('performance' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            
            // Log slow resources
            if (resourceEntry.duration > 1000) {
              console.warn(`⚠️ Slow resource: ${resourceEntry.name} took ${resourceEntry.duration.toFixed(2)}ms`);
            }
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
    }
  }

  /**
   * Setup performance logging
   */
  private setupPerformanceLogging(): void {
    // Log performance stats periodically
    setInterval(() => {
      const cssStats = this.dynamicCSSLoader.getLoadingStats();
      const preloadStats = this.componentPreloader.getPreloadStats();
      const progressiveStats = this.progressiveLoading.getLoadingStats();

      console.log('📈 Performance Stats:', {
        css: cssStats,
        preload: preloadStats,
        progressive: progressiveStats
      });
    }, 30000); // Log every 30 seconds
  }

  /**
   * Get First Paint timing
   */
  private getFirstPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  /**
   * Get First Contentful Paint timing
   */
  private getFirstContentfulPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return firstContentfulPaint ? firstContentfulPaint.startTime : null;
  }

  /**
   * Debounced condition checking for CSS loading
   */
  private debounceTimer?: number;
  private debounceConditionCheck(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = window.setTimeout(() => {
      this.dynamicCSSLoader.loadConditionalModules();
    }, 500);
  }
}

/**
 * Factory function for APP_INITIALIZER
 */
export function performanceInitializerFactory(initializer: PerformanceInitializer): () => Promise<void> {
  return () => initializer.initialize();
}
