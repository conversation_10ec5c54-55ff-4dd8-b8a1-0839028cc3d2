<div class="page-content">
  <div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
    <div>
      <h4 class="mb-3 mb-md-0">Bulk Upload Employees</h4>
    </div>
    <div class="d-flex align-items-center flex-wrap text-nowrap">
      <button
        type="button"
        class="btn btn-outline-primary btn-icon-text me-2 mb-2 mb-md-0"
        (click)="downloadTemplate()">
        <i data-feather="download" class="btn-icon-prepend" appFeatherIcon></i>
        Download Template
      </button>
      <a routerLink="/employee" class="btn btn-primary btn-icon-text mb-2 mb-md-0">
        <i data-feather="arrow-left" class="btn-icon-prepend" appFeatherIcon></i>
        Back to Employees
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-12 col-xl-8 grid-margin stretch-card">
      <div class="card">
        <div class="card-body">
          <h6 class="card-title">Upload Excel File</h6>
          <p class="text-muted mb-3">
            Upload an Excel file containing employee data. Download the template to see the required format.
          </p>

          <!-- File Upload Area -->
          <div
            class="file-upload-area border-2 border-dashed rounded p-4 text-center"
            [class.border-primary]="dragOver"
            [class.border-secondary]="!dragOver"
            [class.bg-light]="dragOver"
            (dragover)="onDragOver($event)"
            (dragleave)="onDragLeave($event)"
            (drop)="onDrop($event)">

            <div *ngIf="!selectedFile" class="upload-placeholder">
              <i data-feather="upload-cloud" class="icon-lg text-muted mb-3" appFeatherIcon></i>
              <h5 class="text-muted">Drag and drop your Excel file here</h5>
              <p class="text-muted mb-3">or</p>
              <label for="fileInput" class="btn btn-primary">
                <i data-feather="file-plus" class="btn-icon-prepend" appFeatherIcon></i>
                Choose File
              </label>
              <input
                type="file"
                id="fileInput"
                class="d-none"
                accept=".xlsx,.xls,.csv"
                (change)="onFileSelected($event)">
              <p class="text-muted mt-3 mb-0">
                <small>Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</small>
              </p>
            </div>

            <div *ngIf="selectedFile" class="selected-file">
              <div class="d-flex align-items-center justify-content-center">
                <i data-feather="file-text" class="icon-md text-success me-3" appFeatherIcon></i>
                <div class="file-info text-start">
                  <h6 class="mb-1">{{ selectedFile.name }}</h6>
                  <p class="text-muted mb-0">{{ getFileSize(selectedFile.size) }}</p>
                </div>
                <button
                  type="button"
                  class="btn btn-outline-danger btn-sm ms-3"
                  (click)="clearFile()"
                  [disabled]="uploading">
                  <i data-feather="x" appFeatherIcon></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div *ngIf="error" class="alert alert-danger mt-3" role="alert">
            <i data-feather="alert-circle" class="icon-sm me-2" appFeatherIcon></i>
            {{ error }}

            <!-- Debug Info (remove in production) -->
            <div class="mt-2 text-muted small">
              Debug: hasValidationErrors={{ hasValidationErrors }}, selectedFile={{ !!selectedFile }}
            </div>

            <!-- Download Error Report Button -->
            <div *ngIf="hasValidationErrors" class="mt-3">
              <button
                type="button"
                class="btn btn-outline-danger btn-sm"
                (click)="downloadErrorReport()"
                [disabled]="downloadingErrors || !selectedFile">
                <span *ngIf="downloadingErrors" class="spinner-border spinner-border-sm me-2" role="status"></span>
                <i *ngIf="!downloadingErrors" data-feather="download" class="btn-icon-prepend" appFeatherIcon></i>
                {{ downloadingErrors ? 'Downloading...' : 'Download Error Report' }}
              </button>
              <small class="text-muted d-block mt-1">
                Download a detailed error report with all validation issues
              </small>
            </div>
          </div>



          <!-- Upload Button - Disabled during upload, re-enabled after success/error -->
          <div class="mt-4 text-center" *ngIf="selectedFile">
            <button
              type="button"
              class="btn btn-primary btn-lg"
              (click)="uploadFile()"
              [disabled]="isUploadButtonDisabled()">
              <span *ngIf="uploading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i *ngIf="!uploading" data-feather="upload" class="btn-icon-prepend" appFeatherIcon></i>
              {{ uploading ? 'Uploading...' : 'Upload Employees' }}
            </button>

            <!-- Debug info (remove in production) -->
            <!-- <div class="mt-2 text-muted small" style="font-size: 0.8rem;">
              Debug: uploading={{ uploading }}, hasFile={{ !!selectedFile }}, disabled={{ isUploadButtonDisabled() }}
            </div> -->

            <!-- Test button to trigger validation error (remove in production) -->
            <button
              type="button"
              class="btn btn-warning btn-sm mt-2"
              (click)="testValidationError()">
              Test Validation Error
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Instructions -->
    <div class="col-12 col-xl-4 grid-margin stretch-card">
      <div class="card">
        <div class="card-body">
          <h6 class="card-title">Instructions</h6>
          <div class="instructions">
            <div class="instruction-item mb-3">
              <div class="d-flex align-items-start">
                <span class="badge bg-primary me-3 mt-1">1</span>
                <div>
                  <h6 class="mb-1">Download Template</h6>
                  <p class="text-muted mb-0">Click "Download Template" to get the Excel format with required columns.</p>
                </div>
              </div>
            </div>

            <div class="instruction-item mb-3">
              <div class="d-flex align-items-start">
                <span class="badge bg-primary me-3 mt-1">2</span>
                <div>
                  <h6 class="mb-1">Fill Employee Data</h6>
                  <p class="text-muted mb-0">Add employee information following the template format exactly.</p>
                </div>
              </div>
            </div>

            <div class="instruction-item mb-3">
              <div class="d-flex align-items-start">
                <span class="badge bg-primary me-3 mt-1">3</span>
                <div>
                  <h6 class="mb-1">Upload File</h6>
                  <p class="text-muted mb-0">Select or drag your completed Excel file to upload.</p>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-4">
            <h6 class="mb-2">Important Format Requirements:</h6>
            <div class="alert alert-info">
              <h6 class="alert-heading">Date Format</h6>
              <p class="mb-1">All dates must be in <strong>YYYY-MM-DD</strong> format:</p>
              <ul class="mb-2">
                <li>✅ Correct: 1990-01-15</li>
                <li>❌ Wrong: 1/15/1990 or 15-01-1990</li>
              </ul>

              <h6 class="alert-heading mt-3">IFSC Code Format</h6>
              <p class="mb-1">IFSC codes must follow the exact pattern: <strong>4 CAPITAL letters + 0 + 6 alphanumeric</strong></p>
              <ul class="mb-0">
                <li>✅ Correct: HDFC0000001, SBIN0000002, ICIC0000003</li>
                <li>❌ Wrong: HDFC0001234 (too many digits), hdfc0000001 (lowercase), HDFC1234567 (no zero)</li>
              </ul>
              <p class="mb-0 mt-2"><small class="text-info">💡 Tip: Use the template examples exactly as shown!</small></p>
            </div>

            <h6 class="mb-2">Required Columns:</h6>
            <ul class="list-unstyled text-muted">
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>first_name</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>last_name</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>office_email</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>department</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>designation</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>joining_date (YYYY-MM-DD)</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>date_of_birth (YYYY-MM-DD)</li>
              <li><i data-feather="check" class="icon-sm text-success me-2" appFeatherIcon></i>ifsc_no (BANK0123456 format)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>



