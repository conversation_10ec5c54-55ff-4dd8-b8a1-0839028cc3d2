<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title and add button -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Connect With List</h6>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="forceReloadData()" [disabled]="loading">
              <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
              Refresh
            </button>
            <button class="btn btn-primary" (click)="openCreateModal(createEditModal)">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add New Connect With
            </button>
          </div>
        </div>

        <!-- Search and filter -->
        <div class="row mb-3">
          <div class="col-12 col-md-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input type="text" class="form-control" [(ngModel)]="searchTerm"
                placeholder="Search by name or description..." (keyup.enter)="onSearch()">
              <button *ngIf="searchTerm" class="input-group-text bg-light text-danger"
                (click)="clearSearch()">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
            <small class="text-muted" *ngIf="searchTerm">
              Searching for: "{{ searchTerm }}"
            </small>
          </div>

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="itemsPerPage" (ngModelChange)="loadConnectWithItems()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">Actions</th>
                <th scope="col">Name</th>
                <th scope="col">Description</th>
                <th scope="col">Created Date</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of filteredConnectWithItems">
                <td class="action-icons">
                  <button type="button" class="action-icon text-info" ngbTooltip="View"
                    (click)="openViewModal(viewModal, item)">
                    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Edit"
                    (click)="openEditModal(createEditModal, item)" *ngIf="!isDeleted(item)">
                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Delete"
                    (click)="deleteConnectWith(item)" *ngIf="!isDeleted(item)"
                    [disabled]="deleting && deletingItemId === item.id">
                    <span *ngIf="deleting && deletingItemId === item.id"
                      class="spinner-border spinner-border-sm text-danger" role="status"></span>
                    <i *ngIf="!(deleting && deletingItemId === item.id)"
                      data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                  </button>

                  <button *ngIf="isDeleted(item)" type="button" class="action-icon text-success"
                    ngbTooltip="Restore" (click)="restoreConnectWith(item)"
                    [disabled]="restoring && restoringItemId === item.id">
                    <span *ngIf="restoring && restoringItemId === item.id"
                      class="spinner-border spinner-border-sm text-success" role="status"></span>
                    <i *ngIf="!(restoring && restoringItemId === item.id)"
                      data-feather="refresh-cw" class="icon-sm" appFeatherIcon></i>
                  </button>
                </td>

                <td>
                  {{ item.name }}
                  <span *ngIf="isDeleted(item)" class="badge bg-danger ms-2">Deleted</span>
                </td>
                <td>
                  <span class="badge bg-light text-dark">
                    {{ item.description }}
                  </span>
                </td>
                <td>{{ item.created_at | date:'dd-MM-yyyy' }}</td>
              </tr>
              <tr *ngIf="filteredConnectWithItems.length === 0">
                <td colspan="4" class="text-center py-4">
                  <div class="text-muted">
                    <i data-feather="inbox" class="icon-lg mb-3" appFeatherIcon></i>
                    <p>No connect with items found</p>
                    <button type="button" class="btn btn-primary" (click)="openCreateModal(createEditModal)">
                      <i data-feather="plus" class="icon-sm me-2" appFeatherIcon></i>Add First Connect With
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div *ngIf="!loading && filteredConnectWithItems.length > 0" class="d-flex justify-content-between align-items-center mt-3">
          <div class="text-muted">
            Showing {{ getStartIndex() + 1 }} to {{ getEndIndex() }} of {{ filteredConnectWithItems.length }} entries
          </div>
          <nav>
            <ngb-pagination
              [(page)]="currentPage"
              [pageSize]="itemsPerPage"
              [collectionSize]="filteredConnectWithItems.length"
              [maxSize]="5"
              [rotate]="true"
              [boundaryLinks]="true">
            </ngb-pagination>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create/Edit Modal -->
<ng-template #createEditModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">{{ isEditMode ? 'Edit' : 'Create' }} Connect With</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="onModalClose(); modal.dismiss('Cross click')"></button>
  </div>
  
  <div class="modal-body">
    <!-- Modal Error Display -->
    <div *ngIf="modalError" class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>{{ modalError }}
    </div>

    <form [formGroup]="connectWithForm" (ngSubmit)="saveConnectWith()">
      <div class="mb-3">
        <label for="name" class="form-label">Name</label>
        <input type="text" class="form-control" id="name" formControlName="name" placeholder="Enter name">
        <div *ngIf="connectWithForm.get('name')?.invalid && connectWithForm.get('name')?.touched" class="text-danger">
          Name is required
        </div>
      </div>

      <div class="mb-3">
        <label for="description" class="form-label">Description</label>
        <textarea class="form-control" id="description" formControlName="description" 
                  rows="3" placeholder="Enter description"></textarea>
        <div *ngIf="connectWithForm.get('description')?.invalid && connectWithForm.get('description')?.touched" class="text-danger">
          Description is required
        </div>
      </div>
    </form>
  </div>
  
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="onModalClose(); modal.dismiss('Cancel')">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="saveConnectWith()" [disabled]="submitting">
      <span *ngIf="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ isEditMode ? 'Update' : 'Create' }}
    </button>
  </div>
</ng-template>

<!-- View Modal -->
<ng-template #viewModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">View Connect With</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>
  
  <div class="modal-body" *ngIf="selectedConnectWith">
    <div class="row">
      <div class="col-md-6">
        <strong>ID:</strong>
        <p>{{ selectedConnectWith.id }}</p>
      </div>
      <div class="col-md-6">
        <strong>Name:</strong>
        <p>{{ selectedConnectWith.name }}</p>
      </div>
    </div>
    
    <div class="row">
      <div class="col-12">
        <strong>Description:</strong>
        <p>{{ selectedConnectWith.description }}</p>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <strong>Created Date:</strong>
        <p>{{ selectedConnectWith.created_at | date:'dd-MM-yyyy HH:mm' }}</p>
      </div>
      <div class="col-md-6">
        <strong>Updated Date:</strong>
        <p>{{ selectedConnectWith.updated_at | date:'dd-MM-yyyy HH:mm' }}</p>
      </div>
    </div>
    
    <div class="row" *ngIf="isDeleted(selectedConnectWith)">
      <div class="col-12">
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          This connect with item has been deleted on {{ selectedConnectWith.deleted_at | date:'dd-MM-yyyy HH:mm' }}
        </div>
      </div>
    </div>
  </div>
  
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('Close')">Close</button>
    <button type="button" class="btn btn-warning" 
            *ngIf="selectedConnectWith && !isDeleted(selectedConnectWith)"
            (click)="modal.dismiss('Close'); openEditModal(createEditModal, selectedConnectWith)">
      <i class="fas fa-edit me-2"></i>Edit
    </button>
  </div>
</ng-template>
