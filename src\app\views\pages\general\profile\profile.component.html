<!-- <div class="row"> -->
  <!-- <div class="col-12 grid-margin">
    <div class="card">
      <div class="position-relative">
        <figure class="overflow-hidden mb-0 d-flex justify-content-center">
          <img src="https://placehold.co/1300x350" class="rounded-top" alt="profile cover">
        </figure>
        <div class="d-flex justify-content-between align-items-center position-absolute top-90 w-100 px-2 px-md-4 mt-n4">
          <div>
            <img class="w-70px rounded-circle" src="https://placehold.co/70x70" alt="profile">
            <span class="h4 ms-3 text-dark">{{ getEmployeeFullName() || 'Employee Profile' }}</span>
          </div>
          <div class="d-none d-md-block">
            <button class="btn btn-primary btn-icon-text">
              <i class="feather icon-edit btn-icon-prepend"></i> Edit profile
            </button>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-center p-3 rounded-bottom">
        <ul class="d-flex align-items-center m-0 p-0">
          <li class="d-flex align-items-center active">
            <i class="feather icon-columns me-1 icon-md text-primary"></i>
            <a class="pt-1px d-none d-md-block text-primary" routerLink=".">Timeline</a>
          </li>
          <li class="ms-3 ps-3 border-start d-flex align-items-center">
            <i class="feather icon-user me-1 icon-md"></i>
            <a class="pt-1px d-none d-md-block text-body" routerLink=".">About</a>
          </li>
          <li class="ms-3 ps-3 border-start d-flex align-items-center">
            <i class="feather icon-users me-1 icon-md"></i>
            <a class="pt-1px d-none d-md-block text-body" routerLink=".">Friends <span class="text-secondary fs-12px">3,765</span></a>
          </li>
          <li class="ms-3 ps-3 border-start d-flex align-items-center">
            <i class="feather icon-image me-1 icon-md"></i>
            <a class="pt-1px d-none d-md-block text-body" routerLink=".">Photos</a>
          </li>
          <li class="ms-3 ps-3 border-start d-flex align-items-center">
            <i class="feather icon-video me-1 icon-md"></i>
            <a class="pt-1px d-none d-md-block text-body" routerLink=".">Videos</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div> -->
<!-- <div class="row profile-body"> -->
  <!-- left wrapper start -->
  <!-- <div class="d-none d-md-block col-md-4 col-xl-3 left-wrapper">
    <div class="card rounded">
      <div class="card-body">
        <div class="d-flex align-items-center justify-content-between mb-2">
          <h6 class="card-title mb-0">About</h6>
          <div ngbDropdown>
            <a class="no-dropdown-toggle-icon" id="dropdownMenuButton" ngbDropdownToggle>
              <i class="feather icon-more-horizontal icon-lg text-secondary pb-3px"></i>
            </a>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton">
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-edit-2 icon-sm me-2"></i> <span class="">Edit</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-git-branch icon-sm me-2"></i> <span class="">Update</span></a>
              <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-eye icon-sm me-2"></i> <span class="">View all</span></a>
            </div>
          </div>
        </div>
        <p>Hi! I'm Amiah the Senior UI Designer at NobleUI. We hope you enjoy the design and quality of Social.</p>
        <div class="mt-3">
          <label class="fs-11px fw-bolder mb-0 text-uppercase">Joined:</label>
          <p class="text-secondary">November 15, 2015</p>
        </div>
        <div class="mt-3">
          <label class="fs-11px fw-bolder mb-0 text-uppercase">Lives:</label>
          <p class="text-secondary">New York, USA</p>
        </div>
        <div class="mt-3">
          <label class="fs-11px fw-bolder mb-0 text-uppercase">Email:</label>
          <p class="text-secondary">admin&#64;nobleui.com</p>
        </div>
        <div class="mt-3">
          <label class="fs-11px fw-bolder mb-0 text-uppercase">Website:</label>
          <p class="text-secondary">www.nobleui.com</p>
        </div>
        <div class="mt-3 d-flex social-links">
          <a href="" (click)="false" class="btn btn-icon border btn-xs me-2">
            <i class="feather icon-github"></i>
          </a>
          <a href="" (click)="false" class="btn btn-icon border btn-xs me-2">
            <i class="feather icon-twitter"></i>
          </a>
          <a href="" (click)="false" class="btn btn-icon border btn-xs me-2">
            <i class="feather icon-instagram"></i>
          </a>
        </div>
      </div>
    </div>
  </div> -->
  <!-- left wrapper end -->
  <!-- middle wrapper start -->
  <!-- <div class="col-md-8 col-xl-6 middle-wrapper">
    <div class="row">
      <div class="col-md-12 grid-margin">
        <div class="card rounded">
          <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
              <div class="d-flex align-items-center">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">1 min ago</p>
                </div>
              </div>
              <div ngbDropdown>
                <a class="no-dropdown-toggle-icon" id="dropdownMenuButton2" ngbDropdownToggle>
                  <i class="feather icon-more-horizontal icon-lg text-secondary pb-3px"></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="dropdownMenuButton2">
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-meh icon-sm me-2"></i> <span class="">Unfollow</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-corner-right-up icon-sm me-2"></i> <span class="">Go to post</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-share-2 icon-sm me-2"></i> <span class="">Share</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-copy icon-sm me-2"></i> <span class="">Copy link</span></a>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <p class="mb-3">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusamus minima delectus nemo unde quae recusandae assumenda.</p>
            <img class="img-fluid" src="https://placehold.co/500x300" alt="">
          </div>
          <div class="card-footer">
            <div class="d-flex post-actions">
              <a href="" (click)="false" class="d-flex align-items-center text-secondary me-4">
                <i class="feather icon-heart icon-md"></i>
                <p class="d-none d-md-block ms-2">Like</p>
              </a>
              <a href="" (click)="false" class="d-flex align-items-center text-secondary me-4">
                <i class="feather icon-message-square icon-md"></i>
                <p class="d-none d-md-block ms-2">Comment</p>
              </a>
              <a href="" (click)="false" class="d-flex align-items-center text-secondary">
                <i class="feather icon-share icon-md"></i>
                <p class="d-none d-md-block ms-2">Share</p>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="card rounded">
          <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
              <div class="d-flex align-items-center">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">5 min ago</p>
                </div>
              </div>
              <div ngbDropdown>
                <a class="no-dropdown-toggle-icon" id="dropdownMenuButton3" ngbDropdownToggle>
                  <i class="feather icon-more-horizontal icon-lg text-secondary pb-3px"></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="dropdownMenuButton2">
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-meh icon-sm me-2"></i> <span class="">Unfollow</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-corner-right-up icon-sm me-2"></i> <span class="">Go to post</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-share-2 icon-sm me-2"></i> <span class="">Share</span></a>
                  <a ngbDropdownItem class="d-flex align-items-center" href="" (click)="false"><i class="feather icon-copy icon-sm me-2"></i> <span class="">Copy link</span></a>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <p class="mb-3">Lorem ipsum dolor sit amet, consectetur adipisicing elit.</p>
            <img class="img-fluid" src="https://placehold.co/500x300" alt="">
          </div>
          <div class="card-footer">
            <div class="d-flex post-actions">
              <a href="" (click)="false" class="d-flex align-items-center text-secondary me-4">
                <i class="feather icon-heart icon-md"></i>
                <p class="d-none d-md-block ms-2">Like</p>
              </a>
              <a href="" (click)="false" class="d-flex align-items-center text-secondary me-4">
                <i class="feather icon-message-square icon-md"></i>
                <p class="d-none d-md-block ms-2">Comment</p>
              </a>
              <a href="" (click)="false" class="d-flex align-items-center text-secondary">
                <i class="feather icon-share icon-md"></i>
                <p class="d-none d-md-block ms-2">Share</p>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> -->
  <!-- middle wrapper end -->
  <!-- right wrapper start -->
  <!-- <div class="d-none d-xl-block col-xl-3">
    <div class="row">
      <div class="col-md-12 grid-margin">
        <div class="card rounded">
          <div class="card-body">
            <h6 class="card-title">latest photos</h6>
            <div class="row ms-0 me-0">
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-2">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-2">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-2">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-2">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-2">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-2">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-0">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-0">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
              <a href="" (click)="false" class="col-md-4 ps-1 pe-1">
                <figure class="mb-0">
                  <img class="img-fluid rounded" src="https://placehold.co/80x80" alt="">
                </figure>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12 grid-margin">
        <div class="card rounded">
          <div class="card-body">
            <h6 class="card-title">suggestions for you</h6>
            <div class="d-flex justify-content-between mb-2 pb-2 border-bottom">
              <div class="d-flex align-items-center hover-pointer">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">12 Mutual Friends</p>
                </div>
              </div>
              <button class="btn btn-icon border-0"><i class="feather icon-user-plus" ngbTooltip="Connect"></i></button>
            </div>
            <div class="d-flex justify-content-between mb-2 pb-2 border-bottom">
              <div class="d-flex align-items-center hover-pointer">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">12 Mutual Friends</p>
                </div>
              </div>
              <button class="btn btn-icon border-0"><i class="feather icon-user-plus" ngbTooltip="Connect"></i></button>
            </div>
            <div class="d-flex justify-content-between mb-2 pb-2 border-bottom">
              <div class="d-flex align-items-center hover-pointer">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">12 Mutual Friends</p>
                </div>
              </div>
              <button class="btn btn-icon border-0"><i class="feather icon-user-plus" ngbTooltip="Connect"></i></button>
            </div>
            <div class="d-flex justify-content-between mb-2 pb-2 border-bottom">
              <div class="d-flex align-items-center hover-pointer">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">12 Mutual Friends</p>
                </div>
              </div>
              <button class="btn btn-icon border-0"><i class="feather icon-user-plus" ngbTooltip="Connect"></i></button>
            </div>
            <div class="d-flex justify-content-between mb-2 pb-2 border-bottom">
              <div class="d-flex align-items-center hover-pointer">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">12 Mutual Friends</p>
                </div>
              </div>
              <button class="btn btn-icon border-0"><i class="feather icon-user-plus" ngbTooltip="Connect"></i></button>
            </div>
            <div class="d-flex justify-content-between">
              <div class="d-flex align-items-center hover-pointer">
                <img class="img-xs rounded-circle" src="https://placehold.co/35x35" alt="">													
                <div class="ms-2">
                  <p>Mike Popescu</p>
                  <p class="fs-11px text-secondary">12 Mutual Friends</p>
                </div>
              </div>
              <button class="btn btn-icon border-0"><i class="feather icon-user-plus" ngbTooltip="Connect"></i></button>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div> -->
  <!-- right wrapper end -->
<!-- </div> -->

<!-- Profile Data Table Section -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
          <h6 class="card-title mb-0">Employee Profile Data</h6>
         
        </div>
      </div>
      <div class="card-body">
        <!-- Authentication Loading State -->
        <div *ngIf="authenticationLoading" class="text-center py-4">
          <div class="spinner-border text-info" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 text-muted">Authenticating and loading your profile...</p>
        </div>

        <!-- Authentication Error State -->
        <div *ngIf="authenticationError && !authenticationLoading" class="alert alert-warning" role="alert">
          <div class="d-flex align-items-center">
            <i class="feather icon-alert-triangle me-2"></i>
            <div class="flex-grow-1">
              <strong>Authentication Required</strong>
              <p class="mb-0">{{ authenticationError }}</p>
            </div>
            <button type="button" class="btn btn-outline-warning btn-sm" (click)="retryAuthentication()">
              <i class="feather icon-refresh-cw me-1"></i>
              Retry
            </button>
          </div>
        </div>

        <!-- Profile Loading State -->
        <div *ngIf="loading && !authenticationLoading && !authenticationError" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 text-muted">Loading employee profile...</p>
        </div>

        <!-- Profile Error State -->
        <div *ngIf="error && !loading && !authenticationLoading && !authenticationError" class="alert alert-danger" role="alert">
          <div class="d-flex align-items-center">
            <i class="feather icon-alert-circle me-2"></i>
            <div class="flex-grow-1">
              <strong>Error Loading Profile</strong>
              <p class="mb-0">{{ error.message }}</p>
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm" (click)="retryLoadProfile()">
              <i class="feather icon-refresh-cw me-1"></i>
              Retry
            </button>
          </div>
        </div>

        <!-- Profile Data (shown when authenticated, not loading, and no errors) -->
        <div *ngIf="!authenticationLoading && !authenticationError && !loading && !error && currentEmployee && currentUser">

          <!-- Current User Info -->
          <!-- <div class="alert alert-info mb-4" role="alert">
            <div class="d-flex align-items-center">
              <i class="feather icon-user me-2"></i> -->
              <!-- <div>
                <strong>Viewing Profile For:</strong> {{ getEmployeeFullName() }}
                <br>
                <small class="text-muted">
                  Employee ID: {{ currentEmployee.employee_code }} |
                  Logged in as: {{ currentUser.email }}
                </small>
              </div> -->
            <!-- </div>
          </div> -->

          <div class="section-title">Personal Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="employeeCode" class="form-label">Employee Code <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="employeeCode" [value]="(currentEmployee && currentEmployee.employee_code) || ''" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="firstName" [value]="(currentEmployee && currentEmployee.first_name) || ''" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="middleName" class="form-label">Middle Name</label>
          <input type="text" class="form-control" id="middleName" [value]="(currentEmployee && currentEmployee.middle_name) || ''" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="lastName" [value]="(currentEmployee && currentEmployee.last_name) || ''" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="bloodGroup" class="form-label">Blood Group</label>
          <input type="text" class="form-control" id="bloodGroup" [value]="getDisplayValue(profileData.bloodGroup)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="dateOfBirth" class="form-label">Date of Birth</label>
          <input type="text" class="form-control" id="dateOfBirth" [value]="formatDate(profileData.dateOfBirth)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="joiningDate" class="form-label">Joining Date</label>
          <input type="text" class="form-control" id="joiningDate" [value]="formatDate(profileData.joiningDate)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="reportingDate" class="form-label">Reporting Date</label>
          <input type="text" class="form-control" id="reportingDate" [value]="formatDate(profileData.reportingDate)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="gender" class="form-label">Gender</label>
          <input type="text" class="form-control" id="gender" [value]="getDisplayValue(profileData.gender)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="maritalStatus" class="form-label">Marital Status</label>
          <input type="text" class="form-control" id="maritalStatus" [value]="getDisplayValue(profileData.maritalStatus)" readonly>
        </div>
      </div>

        <!-- Contact Information Section -->
      <div class="section-title">Contact Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="personalEmail" class="form-label">Personal Email</label>
          <input type="email" class="form-control" id="personalEmail" [value]="getDisplayValue(profileData.personalEmail)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="officeEmail" class="form-label">Office Email</label>
          <input type="email" class="form-control" id="officeEmail" [value]="(currentEmployee && currentEmployee.office_email) || 'Not Provided'" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="phoneNo" class="form-label">Phone No <small class="text-muted">(Primary)</small></label>
          <input type="tel" class="form-control phone-number" id="phoneNo" [value]="formatPhoneNumber(profileData.phoneNo)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="alternetNo" class="form-label">Alternate No</label>
          <input type="tel" class="form-control phone-number" id="alternetNo" [value]="formatPhoneNumber(profileData.alternateNo)" readonly>
        </div>
        <div class="col-12 mb-3">
          <label for="address" class="form-label">Address</label>
          <textarea class="form-control" id="address" rows="3" readonly>{{ getDisplayValue(profileData.address) }}</textarea>
        </div>
      </div>


         <!-- Government IDs Section -->
      <div class="section-title">Government IDs</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="panNo" class="form-label">PAN No</label>
          <input type="text" class="form-control" id="panNo" [value]="getDisplayValue(profileData.panNo)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="aadharNo" class="form-label">Aadhar No</label>
          <input type="text" class="form-control" id="aadharNo" [value]="getDisplayValue(profileData.aadharNo)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="uanNo" class="form-label">UAN No</label>
          <input type="text" class="form-control" id="uanNo" [value]="getDisplayValue(profileData.uanNo)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="esicNo" class="form-label">ESIC No</label>
          <input type="text" class="form-control" id="esicNo" [value]="getDisplayValue(profileData.esicNo)" readonly>
        </div>
      </div>

          <!-- Financial Information Section -->
      <div class="section-title">Financial Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="ctc" class="form-label">CTC (Cost to Company)</label>
          <input type="text" class="form-control currency-value" id="ctc" [value]="formatCurrency(profileData.ctc)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="attendancebonus" class="form-label">Attendance Bonus</label>
          <input type="text" class="form-control currency-value" id="attendancebonus" [value]="formatCurrency(profileData.attendanceBonus)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="pf" class="form-label">PF (Provident Fund)</label>
          <input type="text" class="form-control" id="pf" [value]="profileData.pf || 'Not Provided'" readonly>
        </div>
      </div>

      <!-- Banking Information Section -->
      <div class="section-title">Banking Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="bankName" class="form-label">Bank Name</label>
          <input type="text" class="form-control" id="bankName" [value]="getDisplayValue(profileData.bankName)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="bankAccountNo" class="form-label">Bank Account No</label>
          <input type="text" class="form-control" id="bankAccountNo" [value]="getDisplayValue(profileData.bankAccountNo)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="ifscNo" class="form-label">IFSC Code</label>
          <input type="text" class="form-control" id="ifscNo" [value]="getDisplayValue(profileData.ifscNo)" readonly>
        </div>
      </div>

         <div class="section-title">Employment Details</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="officeLocation" class="form-label">Office Location</label>
          <input type="text" class="form-control" id="officeLocation" [value]="getDisplayValue(profileData.officeLocation)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="shiftTime" class="form-label">Shift Time</label>
          <input type="text" class="form-control" id="shiftTime" [value]="getDisplayValue(profileData.shiftTime)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="department" class="form-label">Department</label>
          <input type="text" class="form-control" id="department"
                 [value]="(currentEmployee && profileTableData.length > 0) ? profileTableData[0].department : 'Loading...'"
                 readonly>
          <!-- <small class="form-text text-muted" *ngIf="currentEmployee?.department_id">
            Department ID: {{ currentEmployee.department_id }}
          </small> -->
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="designation" class="form-label">Designation</label>
          <input type="text" class="form-control" id="designation"
                 [value]="(currentEmployee && profileTableData.length > 0) ? profileTableData[0].designation : 'Loading...'"
                 readonly>
          <!-- <small class="form-text text-muted" *ngIf="currentEmployee?.designation_id">
            Designation ID: {{ currentEmployee.designation_id }}
          </small> -->
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="role" class="form-label">Role</label>
          <input type="text" class="form-control" id="role"
                 [value]="(currentEmployee && currentEmployee.role) || 'Not Specified'"
                 readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="subRole" class="form-label">
            User Role
            <!-- <button type="button" class="btn btn-sm btn-outline-secondary ms-2"
                    (click)="refreshUserRoles()"
                    title="Refresh role information from User Roles API">
              <i class="feather icon-refresh-cw"></i>
            </button> -->
          </label>
          <input type="text" class="form-control" id="subRole"
                 [value]="(currentEmployee && profileTableData.length > 0) ? profileTableData[0].subRole : 'Loading...'"
                 readonly>
          <small class="form-text text-muted">
            <!-- <span *ngIf="profileTableData.length > 0 && profileTableData[0].subRole !== 'Not Assigned' && profileTableData[0].subRole !== 'Loading...'">
              <i class="feather icon-check-circle text-success"></i> Resolved from User Roles API
            </span> -->
            <span *ngIf="profileTableData.length > 0 && profileTableData[0].subRole === 'Not Assigned'">
              <i class="feather icon-info text-muted"></i> No roles assigned
            </span>
            <span *ngIf="profileTableData.length > 0 && (profileTableData[0].subRole === 'Error Loading Role' || profileTableData[0].subRole === 'No Roles Assigned')">
              <i class="feather icon-alert-triangle text-warning"></i> {{ profileTableData[0].subRole }}
            </span>
          </small>
        </div>

        <!-- Role Name Field - Shows when subrole is selected -->
        <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="showRoleNameField">
          <label for="roleName" class="form-label">Role Name</label>
          <input
            type="text"
            class="form-control"
            id="roleName"
            [value]="selectedRoleName"
            readonly
            placeholder="Role name will appear here"
          >
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="isActive" class="form-label">Status</label>
          <input type="text" class="form-control" id="isActive" [value]="profileData.isActive ? 'Active' : 'Inactive'" readonly>
          <!-- <small class="form-text" [ngClass]="profileData.isActive ? 'text-success' : 'text-danger'">
            <i class="feather" [ngClass]="profileData.isActive ? 'icon-check-circle' : 'icon-x-circle'"></i>
            {{ profileData.isActive ? 'Employee is currently active' : 'Employee is inactive' }}
          </small> -->
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="approverCode" class="form-label">Approver Code</label>
          <input type="text" class="form-control" id="approverCode" [value]="getDisplayValue(profileData.approverCode)" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="secondApproverCode" class="form-label">Second Approver Code</label>
          <input type="text" class="form-control" id="secondApproverCode" [value]="getDisplayValue(profileData.secondApproverCode)" readonly>
        </div>
      </div>

        <!-- Resignation Information Section (only show if resignation dates exist) -->
        <div *ngIf="profileData.resignedStartDate || profileData.resignedEndDate" class="section-title">Resignation Information</div>
        <div *ngIf="profileData.resignedStartDate || profileData.resignedEndDate" class="row">
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="resignedStartDate" class="form-label">Resignation Start Date</label>
            <input type="text" class="form-control" id="resignedStartDate" [value]="formatDate(profileData.resignedStartDate)" readonly>
            <small class="form-text text-muted" *ngIf="profileData.resignedStartDate">
              <i class="feather icon-info"></i> Date when resignation was submitted
            </small>
          </div>
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="resignedEndDate" class="form-label">Last Working Day</label>
            <input type="text" class="form-control" id="resignedEndDate" [value]="formatDate(profileData.resignedEndDate)" readonly>
            <small class="form-text text-muted" *ngIf="profileData.resignedEndDate">
              <i class="feather icon-calendar"></i> Employee's last working day
            </small>
          </div>
        </div>

        </div> <!-- End of conditional div for profile data -->

      </div>
    </div>
  </div>
</div>
