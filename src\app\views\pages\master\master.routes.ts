import { Routes } from '@angular/router';
import { MasterPermissionGuard } from '../../../core/guards/master-permission.guard';

export default [
    {
        path: '',
        redirectTo: 'departments',
        pathMatch: 'full'
    },
    {
        path: 'departments',
        loadComponent: () => import('./departments/departments.component').then(c => c.DepartmentsComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Departments Management',
            breadcrumb: 'Departments',
            permissions: ['master:read']
        }
    },
    {
        path: 'designations',
        loadComponent: () => import('./designations/designations.component').then(c => c.DesignationsComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Designations Management',
            breadcrumb: 'Designations',
            permissions: ['master:read']
        }
    },
    {
        path: 'fund-houses',
        loadComponent: () => import('./fund-houses/fund-houses.component').then(c => c.FundHousesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Fund Houses Management',
            breadcrumb: 'Fund Houses',
            permissions: ['master:read']
        }
    },
    {
        path: 'institutes',
        loadComponent: () => import('./institutes/institutes.component').then(c => c.InstitutesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Institutes Management',
            breadcrumb: 'Institutes',
            permissions: ['master:read']
        }
    },
    {
        path: 'corporate-consultancies',
        loadComponent: () => import('./corporate-consultancies/corporate-consultancies.component').then(c => c.CorporateConsultanciesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Corporate Consultancies Management',
            breadcrumb: 'Corporate Consultancies',
            permissions: ['master:read']
        }
    },
    {
        path: 'board-affiliations',
        loadComponent: () => import('./board-affiliations/board-affiliations.component').then(c => c.BoardAffiliationsComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Board Affiliations Management',
            breadcrumb: 'Board Affiliations',
            permissions: ['master:read']
        }
    },
    {
        path: 'constitutions',
        loadComponent: () => import('./constitutions/constitutions.component').then(c => c.ConstitutionsComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Constitutions Management',
            breadcrumb: 'Constitutions',
            permissions: ['master:read']
        }
    },
    {
        path: 'profession-types',
        loadComponent: () => import('./profession-types/profession-types.component').then(c => c.ProfessionTypesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Profession Types Management',
            breadcrumb: 'Profession Types',
            permissions: ['master:read']
        }
    },
    {
        path: 'locations',
        loadComponent: () => import('./location-management/location-management.component').then(c => c.LocationManagementComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Locations Management',
            breadcrumb: 'Locations',
            permissions: ['master:read']
        }
    },
    {
        path: 'product-types',
        loadComponent: () => import('./product-types/product-types.component').then(c => c.ProductTypesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Product Types Management',
            breadcrumb: 'Product Types',
            permissions: ['master:read']
        }
    },
    {
        path: 'sub-product-types',
        loadComponent: () => import('./sub-product-types/sub-product-types.component').then(c => c.SubProductTypesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Sub Product Types Management',
            breadcrumb: 'Sub Product Types',
            permissions: ['master:read']
        }
    },
    {
        path: 'lead-categories',
        loadComponent: () => import('./lead-categories/lead-categories.component').then(c => c.LeadCategoriesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Lead Categories Management',
            breadcrumb: 'Lead Categories',
            permissions: ['master:read']
        }
    },
    {
        path: 'lead-data-types',
        loadComponent: () => import('./lead-data-types/lead-data-types.component').then(c => c.LeadDataTypesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Lead Data Types Management',
            breadcrumb: 'Lead Data Types',
            permissions: ['master:read']
        }
    },
    {
        path: 'sources',
        loadComponent: () => import('./sources/sources.component').then(c => c.SourcesComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Sources Management',
            breadcrumb: 'Sources',
            permissions: ['master:read']
        }
    },
    {
        path: 'settings',
        loadComponent: () => import('./settings/settings.component').then(c => c.SettingsComponent),
        canActivate: [MasterPermissionGuard],
        data: {
            title: 'Settings Management',
            breadcrumb: 'Settings',
            permissions: ['master:read']
        }
    },
    {
        path: 'profession',
        loadComponent: () => import('./profession/profession.component').then(c => c.ProfessionComponent),
        canActivate: [MasterPermissionGuard]
    },
    {
        path: 'associate',
        loadComponent: () => import('./associate/associate.component').then(c => c.AssociateComponent),
        canActivate: [MasterPermissionGuard]
    },
    {
        path: 'connect-with',
        loadComponent: () => import('./connect-with/connect-with.component').then(c => c.ConnectWithComponent),
        canActivate: [MasterPermissionGuard]
    },
    {
        path: 'master-data',
        loadComponent: () => import('./master-data/master-data.component').then(c => c.MasterDataComponent),
        canActivate: [MasterPermissionGuard]
    },
    {
        path: 'dashboard',
        loadComponent: () => import('./master-dashboard/master-dashboard.component').then(c => c.MasterDashboardComponent),
        canActivate: [MasterPermissionGuard]
    },
] as Routes;
