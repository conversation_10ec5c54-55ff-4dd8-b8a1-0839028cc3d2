import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of } from 'rxjs';

import { ProfileComponent } from './profile.component';
import { EmployeeService, BizzCorpEmployee, Department, Designation } from '../../../../core/services/employee.service';
import { AuthService, User } from '../../../../core/services/auth.service';
import { RoleService } from '../../../../core/services/role.service';

describe('ProfileComponent - Null Department/Designation Handling', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let employeeService: jasmine.SpyObj<EmployeeService>;

  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    name: '<PERSON>',
    access_token: 'mock-token',
    is_active: true
  };

  const mockDepartments: Department[] = [
    { id: 'dept-123', name: 'IT Department', is_active: true },
    { id: 'dept-456', name: 'HR Department', is_active: true }
  ];

  const mockDesignations: Designation[] = [
    { id: 'desig-123', name: 'Software Engineer', is_active: true },
    { id: 'desig-456', name: 'Senior Engineer', is_active: true }
  ];

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['isLoggedIn'], {
      currentUserValue: mockUser
    });
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees',
      'getBizzCorpEmployeeProfile',
      'getDepartmentsMasterData',
      'getDesignationsMasterData',
      'getDesignationById',
      'getRoleById'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const roleServiceSpy = jasmine.createSpyObj('RoleService', ['getAllRoles']);

    await TestBed.configureTestingModule({
      imports: [ProfileComponent, HttpClientTestingModule, ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: RoleService, useValue: roleServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;

    // Setup default spy returns
    authService.isLoggedIn.and.returnValue(true);
    employeeService.getDepartmentsMasterData.and.returnValue(of(mockDepartments));
    employeeService.getDesignationsMasterData.and.returnValue(of(mockDesignations));
  });

  describe('Employee with null department_id and designation_id', () => {
    const employeeWithNullIds: BizzCorpEmployee = {
      id: 'emp-123',
      employee_code: 'EMP001',
      first_name: 'John',
      last_name: 'Doe',
      department_id: null as any, // Explicitly null
      designation_id: undefined as any, // Explicitly undefined
      sub_role_id: '', // Empty string
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };

    beforeEach(() => {
      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>',
        employee_code: 'EMP001'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithNullIds));
    });

    it('should set department to "Not Assigned" when department_id is null', () => {
      component.ngOnInit();

      expect(component.profileTableData[0].department).toBe('Not Assigned');
    });

    it('should set designation to "Not Assigned" when designation_id is undefined', () => {
      component.ngOnInit();

      expect(component.profileTableData[0].designation).toBe('Not Assigned');
    });

    it('should set subRole to "Not Assigned" when sub_role_id is empty', () => {
      component.ngOnInit();

      expect(component.profileTableData[0].subRole).toBe('Not Assigned');
    });

    it('should not make API calls for null/undefined IDs', () => {
      component.ngOnInit();

      expect(employeeService.getDesignationById).not.toHaveBeenCalled();
      expect(employeeService.getRoleById).not.toHaveBeenCalled();
    });
  });

  describe('Employee with valid department_id and designation_id', () => {
    const employeeWithValidIds: BizzCorpEmployee = {
      id: 'emp-123',
      employee_code: 'EMP001',
      first_name: 'John',
      last_name: 'Doe',
      department_id: 'dept-123',
      designation_id: 'desig-123',
      sub_role_id: 'role-123',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };

    beforeEach(() => {
      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>',
        employee_code: 'EMP001'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithValidIds));
      employeeService.getDesignationById.and.returnValue(of({ id: 'desig-123', name: 'Software Engineer', is_active: true }));
      employeeService.getRoleById.and.returnValue(of({ id: 'role-123', name: 'Developer', is_active: true }));
    });

    it('should resolve department name from cached departments', () => {
      component.ngOnInit();

      expect(component.profileTableData[0].department).toBe('IT Department');
    });

    it('should make API call for designation and resolve name', () => {
      component.ngOnInit();

      expect(employeeService.getDesignationById).toHaveBeenCalledWith('desig-123');
      expect(component.profileTableData[0].designation).toBe('Software Engineer');
    });

    it('should make API call for role and resolve name', () => {
      component.ngOnInit();

      expect(employeeService.getRoleById).toHaveBeenCalledWith('role-123');
      expect(component.profileTableData[0].subRole).toBe('Developer');
    });
  });

  describe('Helper methods null handling', () => {
    beforeEach(() => {
      component.departments = mockDepartments;
      component.designations = mockDesignations;
    });

    it('should return "Not Assigned" for null department ID', () => {
      expect(component.getDepartmentName(null as any)).toBe('Not Assigned');
      expect(component.getDepartmentName(undefined as any)).toBe('Not Assigned');
      expect(component.getDepartmentName('')).toBe('Not Assigned');
      expect(component.getDepartmentName('   ')).toBe('Not Assigned');
    });

    it('should return "Not Assigned" for null designation ID', () => {
      expect(component.getDesignationName(null as any)).toBe('Not Assigned');
      expect(component.getDesignationName(undefined as any)).toBe('Not Assigned');
      expect(component.getDesignationName('')).toBe('Not Assigned');
      expect(component.getDesignationName('   ')).toBe('Not Assigned');
    });

    it('should return "Not Assigned" for null role ID', () => {
      expect(component.getRoleName(null as any)).toBe('Not Assigned');
      expect(component.getRoleName(undefined as any)).toBe('Not Assigned');
      expect(component.getRoleName('')).toBe('Not Assigned');
      expect(component.getRoleName('   ')).toBe('Not Assigned');
    });

    it('should return correct department name for valid ID', () => {
      expect(component.getDepartmentName('dept-123')).toBe('IT Department');
    });

    it('should return correct designation name for valid ID', () => {
      expect(component.getDesignationName('desig-123')).toBe('Software Engineer');
    });

    it('should return "Department Not Found" for invalid department ID', () => {
      expect(component.getDepartmentName('invalid-id')).toBe('Department Not Found');
    });

    it('should return "Designation Not Found" for invalid designation ID', () => {
      expect(component.getDesignationName('invalid-id')).toBe('Designation Not Found');
    });

    it('should return "Loading..." when departments array is empty', () => {
      component.departments = [];
      expect(component.getDepartmentName('dept-123')).toBe('Loading...');
    });

    it('should return "Loading..." when designations array is empty', () => {
      component.designations = [];
      expect(component.getDesignationName('desig-123')).toBe('Loading...');
    });
  });

  describe('Mixed scenarios', () => {
    const employeeWithMixedIds: BizzCorpEmployee = {
      id: 'emp-123',
      employee_code: 'EMP001',
      first_name: 'John',
      last_name: 'Doe',
      department_id: 'dept-123', // Valid
      designation_id: null as any, // Null
      sub_role_id: 'role-123', // Valid
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };

    beforeEach(() => {
      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>',
        employee_code: 'EMP001'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithMixedIds));
      employeeService.getRoleById.and.returnValue(of({ id: 'role-123', name: 'Developer', is_active: true }));
    });

    it('should handle mixed null and valid IDs correctly', () => {
      component.ngOnInit();

      expect(component.profileTableData[0].department).toBe('IT Department'); // Resolved from valid ID
      expect(component.profileTableData[0].designation).toBe('Not Assigned'); // Null ID
      expect(component.profileTableData[0].subRole).toBe('Developer'); // Resolved from valid ID
    });

    it('should only make API calls for valid IDs', () => {
      component.ngOnInit();

      expect(employeeService.getDesignationById).not.toHaveBeenCalled(); // Not called for null designation_id
      expect(employeeService.getRoleById).toHaveBeenCalledWith('role-123'); // Called for valid sub_role_id
    });
  });
});
