import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { ThemeModeService } from '../../../core/services/theme-mode.service';
import { AuthService } from '../../../core/services/auth.service';
// Removed UserRoleManagementService dependency - fetch role directly from API
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
// Dynamic import for SweetAlert2 to avoid potential loading issues
// import Swal from 'sweetalert2';
import { SessionTimerComponent } from './session-timer/session-timer.component';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [
    NgbDropdownModule,
    RouterLink,
    CommonModule,
    SessionTimerComponent
  ],
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss'
})
export class NavbarComponent implements OnInit, OnDestroy {
  currentTheme: string;
  userName: string = '';
  userFirstName: string = '';
  userLastName: string = '';
  userEmail: string = '';
  userRole: string = '';
  lastLogin: string = '';
  private authSubscription: Subscription;

  constructor(
    private themeModeService: ThemeModeService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.themeModeService.currentTheme.subscribe( (theme) => {
      this.currentTheme = theme;
      this.showActiveTheme(this.currentTheme);
    });

    // Initialize user data
    this.updateUserData();

    // Subscribe to auth changes with instant role update
    this.authSubscription = this.authService.currentUser$.subscribe(user => {
      console.log('🎭 Navbar - Auth user changed:', user?.email, 'Role:', user?.role);
      if (user) {
        this.userName = user.name || 'User';
        this.userFirstName = user.firstName || '';
        this.userLastName = user.lastName || '';
        this.userEmail = user.email || '';

        // Get role directly from user object like name field
        console.log('🔍 Navbar - Full user object:', user);
        console.log('🔍 Navbar - User role property:', user.role);
        console.log('🔍 Navbar - User roles array:', user.roles);
        this.userRole = this.mapRoleToDisplay(user.role || '');

        // Employee code fetching removed to avoid unnecessary API calls

        this.lastLogin = localStorage.getItem('lastLogin') || new Date().toLocaleString();
        console.log('✅ Navbar - Updated userRole to:', this.userRole);
      } else {
        this.userName = '';
        this.userFirstName = '';
        this.userLastName = '';
        this.userEmail = '';
        this.userRole = '';
        // Keep last login information for display purposes
      }
    });

    // Role will be fetched directly from API when user data changes
    // No need for separate role subscription

    // Add test method to window for debugging
    (window as any).testNavbarRoleMapping = () => {
      this.testRoleMapping();
    };

    // Add method to force role refresh from API
    (window as any).forceRoleRefreshFromAPI = () => {
      console.log('🔄 Forcing role refresh from API...');
      const currentUser = this.authService.currentUserValue;
      if (currentUser && currentUser.id) {
        console.log('🔑 User ID:', currentUser.id);
        // Force re-fetch roles from API
        (this.authService as any).fetchUserRolesAndPermissions(currentUser.id);

        // Wait a bit then refresh navbar
        setTimeout(() => {
          this.refreshRoleDisplay();
        }, 1000);
      } else {
        console.error('❌ No user ID available for role refresh');
      }
    };

    // Add manual role fix method
    (window as any).fixManagerRole = () => {
      this.fixManagerRole();
    };

    // Add debug method to check current role
    (window as any).debugNavbarRole = () => {
      console.log('🐛 DEBUG: Current navbar state');
      const currentUser = this.authService.currentUserValue;
      console.log('🐛 Current user object:', currentUser);
      console.log('🐛 User role property:', currentUser?.role);
      console.log('🐛 User roles array:', currentUser?.roles);
      console.log('🐛 Current userRole in navbar:', this.userRole);
      console.log('🐛 Testing role mapping for current role:');
      if (currentUser?.role) {
        const mapped = this.mapRoleToDisplay(currentUser.role);
        console.log('🐛 Role mapping result:', currentUser.role, '->', mapped);
      }
    };

    // Add force refresh method
    (window as any).forceRefreshNavbarRole = () => {
      console.log('🔄 Force refreshing navbar role...');
      this.updateUserData();
      const currentUser = this.authService.currentUserValue;
      if (currentUser?.role) {
        this.userRole = this.mapRoleToDisplay(currentUser.role);
        console.log('✅ Force updated userRole to:', this.userRole);
      }
    };
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  /**
   * Update user data from current auth state
   */
  private updateUserData(): void {
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      // Set first name and last name
      this.userFirstName = currentUser.firstName || '';
      this.userLastName = currentUser.lastName || '';

      // For backward compatibility, if name exists but no first/last name
      if (!this.userFirstName && !this.userLastName && currentUser.name) {
        const nameParts = currentUser.name.split(' ');
        this.userFirstName = nameParts[0] || '';
        this.userLastName = nameParts.slice(1).join(' ') || '';
      }

      this.userEmail = currentUser.email || '';
      // Debug current user object
      console.log('🔍 Navbar - Full currentUser object:', currentUser);
      console.log('🔍 Navbar - currentUser.role:', currentUser.role);
      console.log('🔍 Navbar - currentUser.roles:', currentUser.roles);
      console.log('🔍 Navbar - Role type:', typeof currentUser.role);

      this.userRole = this.mapRoleToDisplay(currentUser.role || ''); // Get role directly from user object
      console.log('🎭 Navbar - Final userRole set to:', this.userRole);

      // Employee code fetching removed to avoid unnecessary API calls

      // Set last login time
      this.lastLogin = new Date().toLocaleString();
      localStorage.setItem('lastLogin', this.lastLogin);
    } else {
      this.userFirstName = '';
      this.userLastName = '';
      this.userEmail = '';
      this.userRole = '';
      // Try to get last login from localStorage
      this.lastLogin = localStorage.getItem('lastLogin') || '';
    }
  }

  showActiveTheme(theme: string) {
    const themeSwitcher = document.querySelector('#theme-switcher') as HTMLInputElement;
    const box = document.querySelector('.box') as HTMLElement;

    if (!themeSwitcher) {
      return;
    }

    // Toggle the custom checkbox based on the theme
    if (theme === 'dark') {
      themeSwitcher.checked = true;
      box.classList.remove('light');
      box.classList.add('dark');
    } else if (theme === 'light') {
      themeSwitcher.checked = false;
      box.classList.remove('dark');
      box.classList.add('light');
    }
  }

  /**
   * Change the theme on #theme-switcher checkbox changes
   */
  onThemeCheckboxChange(e: Event) {
    const checkbox = e.target as HTMLInputElement;
    const newTheme: string = checkbox.checked ? 'dark' : 'light';
    this.themeModeService.toggleTheme(newTheme);
    this.showActiveTheme(newTheme);
  }

  /**
   * Toggle the sidebar when the hamburger button is clicked
   */
  toggleSidebar(e: Event) {
    e.preventDefault();
    document.body.classList.add('sidebar-open');
    document.querySelector('.sidebar .sidebar-toggler')?.classList.add('active');
  }

  /**
   * Logout with SweetAlert confirmation
   */
  async onLogout(e: Event) {
    e.preventDefault();

    console.log('🚪 Logout button clicked - showing confirmation dialog');

    try {
      // Dynamic import of SweetAlert2 to ensure it's loaded
      const { default: Swal } = await import('sweetalert2');

      console.log('✅ SweetAlert2 loaded successfully');

      // Show enhanced SweetAlert confirmation dialog
      const result = await Swal.fire({
        title: 'Confirm Logout',
        html: `
          <div style="text-align: center; margin-top: 0.5rem;">
            <p style="margin: 0; color: #6c757d; font-size: 1rem; line-height: 1.5;">
              Are you sure you want to sign out of your account?
            </p>
            <p style="margin: 0.5rem 0 0 0; color: #9ca3af; font-size: 0.875rem;">
              You'll need to sign in again to access your dashboard.
            </p>
          </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '🚪 Sign Out',
        cancelButtonText: '↩️ Stay Logged In',
        allowOutsideClick: true,
        allowEscapeKey: true,
        position: 'center',
        backdrop: true,
        heightAuto: false,
        reverseButtons: true,
        focusConfirm: false,
        focusCancel: true,
        returnFocus: false,
        showClass: {
          popup: 'animate__animated animate__fadeInUp animate__faster'
        },
        hideClass: {
          popup: 'animate__animated animate__fadeOutDown animate__faster'
        },
        customClass: {
          container: 'logout-confirmation-popup',
          popup: 'logout-confirmation-popup',
          title: 'logout-popup-title',
          htmlContainer: 'logout-popup-content',
          actions: 'logout-popup-actions',
          confirmButton: 'logout-confirm-btn',
          cancelButton: 'logout-cancel-btn'
        },
        didOpen: () => {
          // Ensure the popup is properly centered and add focus management
          const container = document.querySelector('.swal2-container.logout-confirmation-popup') as HTMLElement;
          if (container) {
            container.style.zIndex = '10001';
            container.style.position = 'fixed';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.display = 'flex';
            container.style.alignItems = 'center';
            container.style.justifyContent = 'center';
          }

          // Focus the cancel button by default for better UX
          const cancelButton = document.querySelector('.logout-cancel-btn') as HTMLElement;
          if (cancelButton) {
            cancelButton.focus();
          }
        },
        willClose: () => {
          // Cleanup function to ensure screen is unblocked
          this.cleanupSweetAlert();
        }
      });

      if (result.isConfirmed) {
        console.log('✅ User confirmed logout');

        // Show loading state
        Swal.fire({
          title: 'Signing Out...',
          html: 'Please wait while we securely log you out.',
          allowOutsideClick: false,
          allowEscapeKey: false,
          showConfirmButton: false,
          customClass: {
            container: 'logout-loading-popup',
            popup: 'logout-loading-popup'
          },
          didOpen: () => {
            Swal.showLoading();
          }
        });

        // Add a small delay for better UX, then logout
        setTimeout(() => {
          // Use enhanced auth service logout but preserve remembered credentials for auto-login
          // Pass false to preserve "Remember Me" credentials
          this.authService.logout(false, 'User logged out manually');
          // Note: No need to manually navigate or set flags - AuthService handles everything
        }, 800);
      } else if (result.isDismissed) {
        console.log('❌ User cancelled logout');

        // Ensure the popup is completely closed and screen is unblocked
        Swal.close();

        // Use the cleanup method with a small delay to ensure DOM is ready
        setTimeout(() => {
          this.cleanupSweetAlert();
          console.log('✅ Popup cleanup completed - screen should be unblocked');
        }, 100);
      }

    } catch (error) {
      console.error('❌ Failed to load SweetAlert2:', error);
      // Fallback to native confirm
      if (confirm('Do you really want to logout? You will be redirected to the login page.')) {
        console.log('✅ User confirmed logout (fallback)');
        this.authService.logout(false, 'User logged out manually');
      } else {
        console.log('❌ User cancelled logout (fallback)');
      }
    }
  }

  /**
   * Get user initials for avatar if no image is available
   */
  getUserInitials(): string {
    if (!this.userFirstName && !this.userLastName) return 'U';

    if (!this.userLastName) return this.userFirstName.charAt(0).toUpperCase();
    if (!this.userFirstName) return this.userLastName.charAt(0).toUpperCase();

    return (this.userFirstName.charAt(0) + this.userLastName.charAt(0)).toUpperCase();
  }

  /**
   * Cleanup SweetAlert2 to prevent screen freezing
   */
  private cleanupSweetAlert(): void {
    try {
      console.log('🧹 Cleaning up SweetAlert2...');

      // Remove any lingering SweetAlert2 elements
      const containers = document.querySelectorAll('.swal2-container');
      containers.forEach(container => {
        if (container && container.parentNode) {
          container.parentNode.removeChild(container);
        }
      });

      // Remove backdrop elements
      const backdrops = document.querySelectorAll('.swal2-backdrop-show');
      backdrops.forEach(backdrop => {
        if (backdrop && backdrop.parentNode) {
          backdrop.parentNode.removeChild(backdrop);
        }
      });

      // Reset body styles
      document.body.classList.remove('swal2-shown', 'swal2-height-auto', 'swal2-no-backdrop', 'swal2-toast-shown');
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';

      // Reset html styles
      document.documentElement.classList.remove('swal2-shown', 'swal2-height-auto');

      console.log('✅ SweetAlert2 cleanup completed');
    } catch (error) {
      console.error('❌ Error during SweetAlert2 cleanup:', error);
    }
  }

  /**
   * Map role to display name - simple mapping like name field
   * Shows hr_manager as HR, and new roles as they are
   */
  private mapRoleToDisplay(role: string): string {
    console.log('🎭 Navbar - Mapping role to display:', role);
    console.log('🎭 Navbar - Role type:', typeof role);
    console.log('🎭 Navbar - Role length:', role?.length);
    console.log('🎭 Navbar - Role charCodes:', role?.split('').map(c => c.charCodeAt(0)));

    if (!role) {
      console.log('❌ No role provided, defaulting to Employee');
      return 'Employee';
    }

    // Direct mapping from role name to display name
    const roleMapping: { [key: string]: string } = {
      'admin': 'Admin',
      'role_manager': 'Manager',
      'manager': 'Manager',
      'employee': 'Employee',
      'role_employee': 'Employee',
      'hr_manager': 'HR Manager'  // Map hr_manager to HR
    };

    // Test the exact mapping
    const lowerRole = role.toLowerCase().trim(); // Add trim to remove any whitespace
    console.log('🔍 Navbar - Role after toLowerCase() and trim():', lowerRole);
    console.log('🔍 Navbar - Available mappings:', Object.keys(roleMapping));
    console.log('🔍 Navbar - Direct lookup result:', roleMapping[lowerRole]);
    console.log('🔍 Navbar - Is hr_manager?', lowerRole === 'hr_manager');

    // If role exists in mapping, use it; otherwise show the role as it is
    const displayRole = roleMapping[lowerRole] || role;
    console.log('📡 Role mapped from', role, 'to:', displayRole);
    return displayRole;
  }

  /**
   * Simple method to refresh role display from user object
   */
  refreshRoleDisplay(): void {
    console.log('🔄 Refreshing navbar role from user object...');
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      const newRole = this.mapRoleToDisplay(currentUser.role || '');
      console.log('🔄 Role from user object:', newRole);
      this.userRole = newRole;
      console.log('✅ Navbar role updated to:', this.userRole);
    }
  }

  /**
   * Public method to force role refresh (called from dashboard)
   */
  public forceRoleRefresh(): void {
    console.log('🚀 Force role refresh called');
    this.refreshRoleDisplay();
  }

  /**
   * Instant role update method - updates navbar immediately from user object
   */
  public updateRoleInstantly(): void {
    console.log('⚡ Instant role update from user object');
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      const newRole = this.mapRoleToDisplay(currentUser.role || '');
      console.log('⚡ User role:', newRole);
      this.userRole = newRole;
      console.log('✅ Navbar instantly updated to:', this.userRole);
    }
  }

  /**
   * Test role mapping - for debugging
   */
  public testRoleMapping(): void {
    console.log('🧪 Testing role mapping...');

    // Test different role values
    const testRoles = ['role_manager', 'manager', 'admin', 'employee', 'role_employee', 'hr_manager'];

    testRoles.forEach(role => {
      const mapped = this.mapRoleToDisplay(role);
      console.log(`🧪 Test: "${role}" -> "${mapped}"`);
    });

    // Test current user role
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      console.log('🧪 Current user role test:');
      console.log('  Raw role:', currentUser.role);
      console.log('  Roles array:', currentUser.roles);
      console.log('  Mapped role:', this.mapRoleToDisplay(currentUser.role || ''));
      console.log('  Current navbar display:', this.userRole);

      // Test if roles array contains role_manager
      if (currentUser.roles?.includes('role_manager')) {
        console.log('✅ User has role_manager in roles array - should display as Manager');
        console.log('🔧 Manually setting role to manager for testing...');

        // Temporarily update the user role for testing
        const updatedUser = { ...currentUser, role: 'manager' };
        this.authService.updateCurrentUser(updatedUser);

        // Refresh navbar display
        setTimeout(() => {
          this.refreshRoleDisplay();
        }, 100);
      }
    }
  }

  /**
   * Manual role fix for testing
   */
  public fixManagerRole(): void {
    console.log('🔧 Manually fixing manager role...');
    const currentUser = this.authService.currentUserValue;

    if (currentUser && currentUser.roles?.includes('role_manager')) {
      console.log('✅ Found role_manager in roles array, updating user.role to manager');

      // Update the user object
      const updatedUser = { ...currentUser, role: 'manager' };
      this.authService.updateCurrentUser(updatedUser);

      // Refresh navbar
      this.refreshRoleDisplay();

      console.log('✅ Role updated and navbar refreshed');
    } else {
      console.log('❌ No role_manager found in user roles');
    }
  }



}
