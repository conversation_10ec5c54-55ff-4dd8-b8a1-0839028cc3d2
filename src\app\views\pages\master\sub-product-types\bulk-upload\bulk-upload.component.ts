import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-upload me-2"></i>
        Bulk Upload Sub Product Types
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Bulk Upload Component - Coming Soon</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced component will support bulk uploading of sub product types with:
        <ul class="mt-2 mb-0">
          <li><strong>Excel Template with Form Schemas</strong> - Complex template supporting form field definitions</li>
          <li><strong>JSON Schema Import</strong> - Import form schemas from JSON files</li>
          <li><strong>Drag and Drop Upload</strong> - Modern file upload interface</li>
          <li><strong>Advanced Validation</strong> - Form schema validation and business rule checking</li>
          <li><strong>Preview Mode</strong> - Preview forms before import with live rendering</li>
          <li><strong>Detailed Error Reporting</strong> - Field-level error reporting with suggestions</li>
          <li><strong>Batch Processing</strong> - Process large files in chunks with progress tracking</li>
          <li><strong>Rollback Support</strong> - Ability to rollback failed imports</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-settings me-2"></i>
        <strong>Advanced Import Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Form schema validation and compatibility checking</li>
          <li>Automatic field type detection and conversion</li>
          <li>Dependency resolution and validation</li>
          <li>Pricing override formula validation</li>
          <li>UI configuration import and validation</li>
          <li>Availability rules import with date validation</li>
        </ul>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Close</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('uploaded')">Upload</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
  `]
})
export class BulkUploadComponent {
  constructor(public activeModal: NgbActiveModal) {}
}
