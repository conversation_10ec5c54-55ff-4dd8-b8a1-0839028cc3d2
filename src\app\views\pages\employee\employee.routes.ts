import { Routes } from '@angular/router';
import { dynamicAuthGuard } from '../../../core/guards/dynamic-auth.guard';

export default [
  {
    path: '',
    loadComponent: () => import('./employee-list/employee-list.component').then(c => c.EmployeeListComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      permissions: ['manage_employees', 'view_employees', '*']
    }
  },
  {
    path: 'create',
    loadComponent: () => import('./employee-form/employee-form.component').then(c => c.EmployeeFormComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      permissions: ['manage_employees', '*']
    }
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./employee-form/employee-form.component').then(c => c.EmployeeFormComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      permissions: ['manage_employees', '*']
    }
  },
  {
    path: 'bulk-upload',
    loadComponent: () => import('./bulk-upload/bulk-upload.component').then(c => c.BulkUploadComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      permissions: ['manage_employees', '*']
    }
  },
  {
    path: ':id',
    loadComponent: () => import('./employee-details/employee-details.component').then(c => c.EmployeeDetailsComponent),
    canActivate: [dynamicAuthGuard],
    data: {
      permissions: ['view_employees', '*']
    }
  }
] as Routes;
