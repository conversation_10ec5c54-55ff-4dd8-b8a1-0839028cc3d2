import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModal, NgbModalRef, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';
import { LeaveReportService, LeaveReportSearchParams, LeaveReportData, LEAVE_TYPES } from '../../../../../core/services/leave-report.service';
import { AttendanceReportService, AttendanceReportSearchParams, AttendanceReportData } from '../../../../../core/services/attendance-report.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NgbModalModule,
    FeatherIconDirective,
    BreadcrumbComponent
  ],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss'
})
export class ReportsComponent implements OnInit {
  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'Reports' }
  ];

  // Modal references
  @ViewChild('attendanceModal') attendanceModal: TemplateRef<any>;
  @ViewChild('leaveModal') leaveModal: TemplateRef<any>;
  @ViewChild('reportDataModal') reportDataModal: TemplateRef<any>;

  // Date range for attendance report
  fromDate: string;
  toDate: string;

  // Date range and status for leave report
  leaveFromDate: string;
  leaveToDate: string;
  leaveStatus: string = '';

  // Available leave types
  leaveTypes = LEAVE_TYPES;

  // Loading states
  downloadingLeaveReport: boolean = false;
  downloadingAttendanceReport: boolean = false;

  // Report data
  leaveReportData: LeaveReportData[] = [];
  attendanceReportData: AttendanceReportData[] = [];

  constructor(
    private modalService: NgbModal,
    private leaveReportService: LeaveReportService,
    private attendanceReportService: AttendanceReportService
  ) { }

  ngOnInit(): void {
    // Initialize component
    // Set default date values (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // Initialize attendance report dates
    this.fromDate = this.formatDate(firstDay);
    this.toDate = this.formatDate(lastDay);

    // Initialize leave report dates
    this.leaveFromDate = this.formatDate(firstDay);
    this.leaveToDate = this.formatDate(lastDay);
  }

  // Format date to YYYY-MM-DD
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Open attendance report modal
  openAttendanceModal(): void {
    this.modalService.open(this.attendanceModal, {
      size: 'md',
      centered: true,
      backdrop: 'static'
    });
  }

  // Download attendance report
  downloadAttendanceReport(): void {
    // Validate date inputs
    if (!this.fromDate || !this.toDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Dates',
        text: 'Please select both from and to dates.',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Validate date order
    if (new Date(this.fromDate) > new Date(this.toDate)) {
      Swal.fire({
        icon: 'warning',
        title: 'Invalid Date Range',
        text: 'From date must be before or equal to to date.',
        confirmButtonText: 'OK'
      });
      return;
    }

    this.downloadingAttendanceReport = true;

    // Prepare search parameters
    const searchParams: AttendanceReportSearchParams = {
      from_date: this.fromDate,
      to_date: this.toDate
    };

    console.log('🔍 Searching attendance report with params:', searchParams);

    // Call the API
    this.attendanceReportService.searchAttendanceReports(searchParams).subscribe({
      next: (response) => {
        this.downloadingAttendanceReport = false;
        console.log('✅ Attendance report API response:', response);

        // Handle the nested response structure: response.data.items
        const reportData = response.success && response.data && response.data.items ? response.data.items : [];
        const totalCount = response.data?.total_count || 0;

        if (response.success && reportData.length > 0) {
          // Store the data
          this.attendanceReportData = reportData;

          // Close the search modal first
          this.modalService.dismissAll();

          // Directly download CSV
          const filename = `attendance_report_${this.fromDate}_to_${this.toDate}`;
          this.attendanceReportService.exportToCSV(reportData, filename);

          // Show success message
          Swal.fire({
            icon: 'success',
            title: 'Downloaded!',
            text: `Attendance report with ${totalCount} records has been downloaded as CSV file.`,
            timer: 2000,
            showConfirmButton: false
          });
        } else {
          // No data found
          Swal.fire({
            icon: 'info',
            title: 'No Data Found',
            text: `No attendance records found for the selected date range (${this.fromDate} to ${this.toDate}).`,
            confirmButtonText: 'OK'
          });
        }
      },
      error: (error) => {
        this.downloadingAttendanceReport = false;
        console.error('❌ Attendance report API error:', error);

        // Show error message
        Swal.fire({
          icon: 'error',
          title: 'Report Generation Failed',
          text: error.error?.message || 'Failed to generate attendance report. Please try again.',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  // Open leave report modal
  openLeaveModal(): void {
    this.modalService.open(this.leaveModal, {
      size: 'md',
      centered: true,
      backdrop: 'static'
    });
  }

  // Download leave report
  downloadLeaveReport(): void {
    // Validate date inputs
    if (!this.leaveFromDate || !this.leaveToDate) {
      Swal.fire({
        icon: 'warning',
        title: 'Missing Dates',
        text: 'Please select both from and to dates.',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Validate date order
    if (new Date(this.leaveFromDate) > new Date(this.leaveToDate)) {
      Swal.fire({
        icon: 'warning',
        title: 'Invalid Date Range',
        text: 'From date must be before or equal to to date.',
        confirmButtonText: 'OK'
      });
      return;
    }

    this.downloadingLeaveReport = true;

    // Prepare search parameters
    const searchParams: LeaveReportSearchParams = {
      from_date: this.leaveFromDate,
      to_date: this.leaveToDate,
      ...(this.leaveStatus && { leave_type: this.leaveStatus })
    };

    console.log('🔍 Searching leave report with params:', searchParams);

    // Call the API
    this.leaveReportService.searchLeaveReports(searchParams).subscribe({
      next: (response) => {
        this.downloadingLeaveReport = false;
        console.log('✅ Leave report API response:', response);

        // Handle the nested response structure: response.data.items
        const reportData = response.success && response.data && response.data.items ? response.data.items : [];
        const totalCount = response.data?.total_count || 0;

        if (response.success && reportData.length > 0) {
          // Store the data
          this.leaveReportData = reportData;

          // Close the search modal first
          this.modalService.dismissAll();

          // Directly download CSV
          const filename = `leave_report_${this.leaveFromDate}_to_${this.leaveToDate}`;
          this.leaveReportService.exportToCSV(reportData, filename);

          // Show success message
          Swal.fire({
            icon: 'success',
            title: 'Downloaded!',
            text: `Leave report with ${totalCount} records has been downloaded as CSV file.`,
            timer: 2000,
            showConfirmButton: false
          });
        } else {
          // No data found
          Swal.fire({
            icon: 'info',
            title: 'No Data Found',
            text: `No leave records found for the selected date range (${this.leaveFromDate} to ${this.leaveToDate}).`,
            confirmButtonText: 'OK'
          });
        }
      },
      error: (error) => {
        this.downloadingLeaveReport = false;
        console.error('❌ Leave report API error:', error);

        // Show error message
        Swal.fire({
          icon: 'error',
          title: 'Report Generation Failed',
          text: error.error?.message || 'Failed to generate leave report. Please try again.',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  // View report data in modal
  // viewReportData(): void {
  //   if (this.leaveReportData && this.leaveReportData.length > 0) {
  //     this.modalService.open(this.reportDataModal, {
  //       size: 'xl',
  //       centered: true,
  //       backdrop: 'static'
  //     });
  //   }
  // }

  // Export current report data to CSV
  exportReportData(): void {
    if (this.leaveReportData && this.leaveReportData.length > 0) {
      const filename = `leave_report_${this.leaveFromDate}_to_${this.leaveToDate}`;
      this.leaveReportService.exportToCSV(this.leaveReportData, filename);

      Swal.fire({
        icon: 'success',
        title: 'Downloaded!',
        text: 'Leave report has been downloaded as CSV file.',
        timer: 2000,
        showConfirmButton: false
      });
    }
  }

  // Format leave type for display
  formatLeaveType(leaveType: string): string {
    const type = this.leaveTypes.find(t => t.value === leaveType);
    return type ? type.label : leaveType;
  }

  // Format date for display
  formatDisplayDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // TrackBy function for performance optimization
  trackByRecordId(_index: number, record: LeaveReportData): string {
    return record.id;
  }
}
