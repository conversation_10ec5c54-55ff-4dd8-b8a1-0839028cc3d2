// Custom styles for Product-two component
.btn-group {
  .btn-check:checked + .btn-outline-primary {
    background-color: var(--bs-primary);
    color: white;
  }
}

.badge {
  padding: 0.5em 0.75em;
}
.section-header {
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  
  h5 {
    margin-bottom: 0;
    color: #495057;
  }
}

.form-check {
  padding-top: 0.5rem;
}

.table {
  font-size: 0.875rem;
  
  th {
    background-color: #f8f9fa;
  }
}

.form-control-sm, .form-select-sm {
  padding: 0.25rem 0.5rem;
}

.alert-info {
  background-color: #e8f4f8;
  border-color: #d6e9f0;
  color: #0c5460;
}

// Make readonly fields look different
input[readonly] {
  background-color: #f8f9fa;
  cursor: not-allowed;
}
