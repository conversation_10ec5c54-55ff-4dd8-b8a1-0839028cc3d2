<div class="row">
  <div class="col-xl-10 main-content ps-xl-4 pe-xl-5">
    <h1 class="page-title">Datepicker</h1>
    <p class="lead">Datepicker will help you with date selection. It can be used either inline with NgbDatepicker component or as a popup on any input element with NgbInputDatepicker directive. Read the <a href="https://ng-bootstrap.github.io/#/components/datepicker/examples" target="_blank">Official Ng-Bootstrap Documentation</a> for a full list of instructions and other options.</p>
    
    <hr>
    
    <h4 #default>Basic Example</h4>
    <div class="example">
      <ngb-datepicker #dp [(ngModel)]="selectedDate" (navigate)="date = $event.next"></ngb-datepicker>
      <hr>
      <button class="btn btn-sm btn-primary mb-1 mb-md-0 me-1" (click)="selectToday()">Select Today</button>
      <button class="btn btn-sm btn-primary mb-1 mb-md-0 me-1" (click)="dp.navigateTo()">To current month</button>
      <button class="btn btn-sm btn-primary mb-1 mb-md-0 me-1" (click)="dp.navigateTo({year: 2013, month: 2})">To Feb 2013</button>
      <hr>
      <p class="text-secondary">Month: {{ date.month }}-{{ date.year }}</p>
      <p class="text-secondary">Selected: {{ selectedDate | json }}</p>
    </div>
    <app-code-preview [codeContent]="defaultDatepickerCode"></app-code-preview>

    <hr>

    <h4 #inPopup>Datepicker in a popup</h4>
    <div class="example">
      <form class="d-flex">
        <div class="input-group">
          <input class="form-control" placeholder="yyyy-mm-dd"
              name="dp" [(ngModel)]="selectedDate" ngbDatepicker #d="ngbDatepicker">
          <button class="input-group-text" type="button" (click)="d.toggle()">
            <i class="feather icon-calendar icon-md text-secondary"></i>
          </button>
        </div>
      </form>
      <hr>
      <p class="mt-3 text-secondary">Selected: {{ selectedDate | json }}</p>
    </div>
    <app-code-preview [codeContent]="inPopupCode"></app-code-preview>

    <hr>

    <h4 #rangeSelection>Range selection</h4>
    <div class="example">
      <ngb-datepicker class="range-selection" #dp (dateSelect)="onDateSelection($event)" [displayMonths]="2" [dayTemplate]="t" outsideDays="hidden">
      </ngb-datepicker>
      
      <ng-template #t let-date let-focused="focused">
        <span class="custom-day"
              [class.focused]="focused"
              [class.range]="isRange(date)"
              [class.faded]="isHovered(date) || isInside(date)"
              (mouseenter)="hoveredDate = date"
              (mouseleave)="hoveredDate = null">
          {{ date.day }}
        </span>
      </ng-template>
      <p class="mt-3 text-secondary">From: {{ fromDate | json }}</p>
      <p class="text-secondary">To: {{ toDate | json }}</p>
    </div>
    <app-code-preview [codeContent]="rangeSelectionCode"></app-code-preview>

    <hr>

    <h4 #rangeSelectionPopup>Range selection in a popup</h4>
    <div class="example">
      <form class="d-flex range-selection">
        <div class="form-group hidden">
          <div class="input-group">
            <input name="datepicker"
                   class="form-control"
                   ngbDatepicker
                   #datepicker="ngbDatepicker"
                   [autoClose]="'outside'"
                   (dateSelect)="onDateSelection($event)"
                   [displayMonths]="2"
                   [dayTemplate]="t"
                   outsideDays="hidden"
                   [startDate]="fromDate!">
            <ng-template #t let-date let-focused="focused">
              <span class="custom-day"
                    [class.focused]="focused"
                    [class.range]="isRange(date)"
                    [class.faded]="isHovered(date) || isInside(date)"
                    (mouseenter)="hoveredDate = date"
                    (mouseleave)="hoveredDate = null">
                {{ date.day }}
              </span>
            </ng-template>
          </div>
        </div>
        <div class="mb-3">
          <div class="input-group" (click)="datepicker.toggle()">
            <input #dpFromDate
                   class="form-control" placeholder="yyyy-mm-dd"
                   name="dpFromDate"
                   [value]="formatter.format(fromDate)"
                   (input)="fromDate = validateInput(fromDate, dpFromDate.value)">
            <button class="input-group-text" type="button">
              <i class="feather icon-calendar icon-md text-secondary"></i>
            </button>
          </div>
        </div>
        <div class="ms-2">
          <div class="input-group" (click)="datepicker.toggle()">
            <input #dpToDate
                   class="form-control" placeholder="yyyy-mm-dd"
                   name="dpToDate"
                   [value]="formatter.format(toDate)"
                   (input)="toDate = validateInput(toDate, dpToDate.value)">
            <button class="input-group-text" type="button">
              <i class="feather icon-calendar icon-md text-secondary"></i>
            </button>
          </div>
        </div>
      </form>
      <p class="mt-3 text-secondary">From: {{ fromDate | json }}</p>
      <p class="text-secondary">To: {{ toDate | json }}</p>
    </div>
    <app-code-preview [codeContent]="rangeSelectionPopupCode"></app-code-preview>
    
  </div>
  <div class="col-xl-2 content-nav-wrapper">
    <ul class="nav content-nav d-flex flex-column">
      <li class="nav-item">
        <a (click)="scrollTo(default)" class="nav-link">Basic example</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(inPopup)" class="nav-link">Datepicker in a popup</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(rangeSelection)" class="nav-link">Range selection</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(rangeSelectionPopup)" class="nav-link">Range selection in a popup</a>
      </li>
    </ul>
  </div>
</div>