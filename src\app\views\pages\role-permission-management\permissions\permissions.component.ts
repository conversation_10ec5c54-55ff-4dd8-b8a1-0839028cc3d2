import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { RoleService } from '../../../../core/services/role.service';
import { Permission, PermissionCreate, PermissionUpdate } from '../../../../core/models/role.model';
import { catchError, debounceTime, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

// Sortable directive
export type SortColumn = keyof Permission | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number | null | undefined, v2: string | number | null | undefined) {
  if (v1 === null || v1 === undefined) return -1;
  if (v2 === null || v2 === undefined) return 1;
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-permissions',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbdSortableHeader,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './permissions.component.html',
  styleUrl: './permissions.component.scss'
})
export class PermissionsComponent implements OnInit {
  // Data
  permissions: Permission[] = [];
  selectedPermission: Permission | null = null;

  // Loading state
  loading = false;
  submitting = false;

  // Form
  permissionForm: FormGroup;
  formMode: 'create' | 'edit' = 'create';
  currentPermissionId?: string;

  // Search
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 10;
  totalItems = 0;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  constructor(
    private roleService: RoleService,
    private modalService: NgbModal,
    private fb: FormBuilder
  ) {
    // Initialize form
    this.permissionForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['']
    });
  }

  ngOnInit(): void {
    // Load initial data
    this.loadPermissions();

    // Set up search
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(value => {
      this.page = 1;
      this.loadPermissions();
    });
  }

  loadPermissions() {
    this.loading = true;

    // Convert page/size to skip/limit for new API format
    const skip = (this.page - 1) * this.pageSize;
    this.roleService.getPermissions(skip, this.pageSize, this.searchTerm.value)
      .pipe(
        catchError(error => {
          console.error('Error loading permissions:', error);
          this.showErrorMessage('Failed to load permissions. Please try again.');
          return of({ data: [], error: null, meta: null, success: false });
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        console.log('Permissions loaded successfully:', response);
        // Handle the actual API response structure
        if (response.success && response.data && Array.isArray(response.data)) {
          this.permissions = response.data;

          // Handle new pagination structure
          if (response.meta && response.meta.pagination) {
            this.totalItems = response.meta.pagination.total_count || response.data.length;
            console.log('📊 Permissions pagination info (new structure):', {
              totalItems: this.totalItems,
              currentPage: response.meta.pagination.current_page,
              totalPages: response.meta.pagination.total_pages,
              hasNext: response.meta.pagination.has_next
            });
          } else {
            // Fallback to old structure or data length
            this.totalItems = response.meta?.total || response.data.length;
            console.log('📊 Permissions pagination info (fallback):', { totalItems: this.totalItems });
          }

          console.log('Permissions set:', this.permissions.length, 'permissions');
        } else {
          console.error('API response indicates failure or unexpected structure:', response);
          this.permissions = [];
          this.totalItems = 0;
        }
      });
  }

  // Refresh when pagination changes
  onPageChange(page: number) {
    this.page = page;
    this.loadPermissions();
  }

  // Handle sorting
  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.loadPermissions();
    } else {
      this.permissions = [...this.permissions].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  // Open modal for creating/editing
  openPermissionModal(modal: TemplateRef<any>, permission?: Permission) {
    if (permission) {
      // Edit mode
      this.formMode = 'edit';
      this.currentPermissionId = permission.id;
      this.permissionForm.patchValue({
        name: permission.name,
        description: permission.description || ''
      });
    } else {
      // Create mode
      this.formMode = 'create';
      this.currentPermissionId = undefined;
      this.permissionForm.reset({
        name: '',
        description: ''
      });
    }

    this.modalService.open(modal, { centered: true });
  }

  // View permission details
  viewPermission(permission: Permission, modal: TemplateRef<any>) {
    console.log('🔍 Viewing permission:', permission);

    // Validate permission object
    if (!permission || !permission.id) {
      console.error('Invalid permission provided to viewPermission:', permission);
      this.showErrorMessage('Invalid permission selected. Please try again.');
      return;
    }

    // Try to fetch fresh permission details from API
    this.roleService.getPermission(permission.id)
      .pipe(
        catchError(error => {
          console.error('❌ Error fetching permission details from API:', error);
          console.log('🔄 Falling back to using existing permission data');
          // Fallback to using the permission data we already have
          return of(permission);
        })
      )
      .subscribe(result => {
        console.log('📋 Permission details result:', result);

        if (result && (result.id || result.name)) {
          console.log('✅ Setting selectedPermission:', result);
          this.selectedPermission = result;
          this.modalService.open(modal, { centered: true, size: 'lg' });
        } else {
          console.error('❌ Invalid permission data received:', result);
          this.showErrorMessage('Failed to load permission details. Please try again.');
        }
      });
  }

  // Save permission (create or update)
  savePermission() {
    if (this.permissionForm.invalid) {
      return;
    }

    this.submitting = true;

    if (this.formMode === 'create') {
      // Create new permission
      const newPermission: PermissionCreate = this.permissionForm.value;

      this.roleService.createPermission(newPermission)
        .pipe(
          catchError(error => {
            console.error('Error creating permission:', error);
            this.showErrorMessage('Failed to create permission. Please try again.');
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            this.modalService.dismissAll();
            this.showSuccessMessage('Permission created successfully!');
            // Reset to first page to see the new permission
            this.page = 1;
            this.loadPermissions();
          }
        });
    } else if (this.formMode === 'edit' && this.currentPermissionId) {
      // Update existing permission
      const updatedPermission: PermissionUpdate = this.permissionForm.value;

      this.roleService.updatePermission(this.currentPermissionId, updatedPermission)
        .pipe(
          catchError(error => {
            console.error('Error updating permission:', error);
            this.showErrorMessage('Failed to update permission. Please try again.');
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            this.modalService.dismissAll();
            this.showSuccessMessage('Permission updated successfully!');
            // Reset to first page to see the updated permission
            this.page = 1;
            this.loadPermissions();
          }
        });
    }
  }

  // Delete permission
  deletePermission(permission: Permission) {
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete permission "${permission.name}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.roleService.deletePermission(permission.id)
          .pipe(
            catchError(error => {
              console.error('Error deleting permission:', error);
              this.showErrorMessage('Failed to delete permission. Please try again.');
              return of(null);
            })
          )
          .subscribe(result => {
            if (result !== null) {
              this.showSuccessMessage('Permission deleted successfully!');
              this.loadPermissions();
            }
          });
      }
    });
  }

  // Restore permission
  restorePermission(permission: Permission) {
    this.roleService.restorePermission(permission.id)
      .pipe(
        catchError(error => {
          console.error('Error restoring permission:', error);
          this.showErrorMessage('Failed to restore permission. Please try again.');
          return of(null);
        })
      )
      .subscribe(result => {
        if (result) {
          this.showSuccessMessage('Permission restored successfully!');
          this.loadPermissions();
        }
      });
  }

  // Helper method to handle editing from view modal
  editFromViewModal(permissionModal: TemplateRef<any>, currentModal: any) {
    if (this.selectedPermission) {
      currentModal.dismiss();
      this.openPermissionModal(permissionModal, this.selectedPermission);
    }
  }

  // Copy ID to clipboard
  copyIdToClipboard(id: string | number): void {
    const idStr = id.toString();
    navigator.clipboard.writeText(idStr).then(() => {
      this.showSuccessMessage('ID copied to clipboard!');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = idStr;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.showSuccessMessage('ID copied to clipboard!');
    });
  }

  // Alerts
  showSuccessMessage(message: string) {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
      timer: 2000,
      showConfirmButton: false
    });
  }

  showErrorMessage(message: string) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message
    });
  }
}
