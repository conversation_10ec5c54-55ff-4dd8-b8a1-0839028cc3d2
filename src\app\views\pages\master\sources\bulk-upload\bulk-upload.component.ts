import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-upload me-2"></i>
        Bulk Upload Sources
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Bulk Upload Component - Coming Soon</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced component will support bulk uploading of sources with:
        <ul class="mt-2 mb-0">
          <li><strong>Excel Template with Campaign Data</strong> - Complex template supporting all source types and configurations</li>
          <li><strong>Campaign Integration Import</strong> - Bulk import of Google Ads, Facebook, LinkedIn campaign data</li>
          <li><strong>Attribution Model Configuration</strong> - Import attribution models with custom weighting</li>
          <li><strong>Tracking Parameters Import</strong> - Bulk import of UTM parameters and custom tracking codes</li>
          <li><strong>Cost & Budget Import</strong> - Import cost models, budgets, and ROI targets</li>
          <li><strong>Analytics Integration Setup</strong> - Import Google Analytics, Facebook Pixel configurations</li>
          <li><strong>Auto-Assignment Rules Import</strong> - Import lead routing and assignment rules</li>
          <li><strong>Follow-up Templates Import</strong> - Import email, SMS, and task templates</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-settings me-2"></i>
        <strong>Advanced Import Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Campaign data validation and API connectivity testing</li>
          <li>Attribution model compatibility checking and optimization</li>
          <li>Tracking parameter validation and URL generation testing</li>
          <li>Cost model validation and budget limit checking</li>
          <li>Analytics integration testing and pixel validation</li>
          <li>Lead routing rule testing and conflict resolution</li>
          <li>Template syntax validation and preview generation</li>
          <li>Performance impact analysis for large source sets</li>
        </ul>
      </div>

      <div class="alert alert-success">
        <i class="feather icon-check-circle me-2"></i>
        <strong>Import Validation:</strong>
        <ul class="mt-2 mb-0">
          <li>Real-time campaign data synchronization testing</li>
          <li>Attribution model accuracy validation</li>
          <li>Tracking parameter functionality verification</li>
          <li>Cost calculation and ROI projection validation</li>
          <li>Analytics integration connectivity testing</li>
          <li>Lead routing simulation and testing</li>
          <li>Template rendering and delivery testing</li>
          <li>Performance optimization recommendations</li>
        </ul>
      </div>

      <div class="alert alert-primary">
        <i class="feather icon-database me-2"></i>
        <strong>Supported Import Sources:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <strong>File Formats:</strong>
            <ul class="mb-2">
              <li>Excel (.xlsx, .xls)</li>
              <li>CSV with headers</li>
              <li>JSON configuration files</li>
              <li>XML campaign exports</li>
            </ul>
            <strong>Platform Exports:</strong>
            <ul class="mb-2">
              <li>Google Ads exports</li>
              <li>Facebook Ads Manager</li>
              <li>LinkedIn Campaign Manager</li>
              <li>Google Analytics reports</li>
            </ul>
          </div>
          <div class="col-md-6">
            <strong>CRM Integrations:</strong>
            <ul class="mb-2">
              <li>Salesforce campaign data</li>
              <li>HubSpot source tracking</li>
              <li>Marketo program data</li>
              <li>Pardot campaign exports</li>
            </ul>
            <strong>Analytics Platforms:</strong>
            <ul class="mb-2">
              <li>Adobe Analytics</li>
              <li>Mixpanel events</li>
              <li>Segment tracking</li>
              <li>Custom analytics APIs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Close</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('uploaded')">Upload</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
    
    .row {
      margin: 0;
    }
    
    .col-md-6 {
      padding: 0 0.5rem;
    }
  `]
})
export class BulkUploadComponent {
  constructor(public activeModal: NgbActiveModal) {}
}
