// Component-specific styles (if any)
// All common modal styles have been moved to global styles

// Section titles
.section-title {
  color: #3F828B;
  font-weight: 500;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

// Read-only field styling
input[readonly] {
  background-color: #f8f9fa;
}

// Hide number input spinners
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}