import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ThemeModeService } from './core/services/theme-mode.service';
import { AuthService } from './core/services/auth.service';
import { PerformanceMonitorComponent } from './shared/components/performance-monitor/performance-monitor.component';
import { PWAInstallPromptComponent } from './shared/components/pwa-install-prompt/pwa-install-prompt.component';
import { PWAService } from './core/services/pwa.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, PerformanceMonitorComponent, PWAInstallPromptComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'demo1';

  constructor(
    private themeModeService: ThemeModeService,
    private authService: AuthService,
    private pwaService: PWAService
  ) {}

  ngOnInit() {
    // Initialize user permissions and roles on app startup
    if (this.authService.isLoggedIn()) {
      console.log('🚀 App startup: Initializing user permissions and roles...');

      // First get current user data to ensure we have the correct UUID
      this.authService.getCurrentUser().subscribe({
        next: () => {
          console.log('✅ App startup: User data updated from /users/me');

          // Small delay to ensure user ID is properly updated
          setTimeout(() => {
            // User roles are loaded from login response and /users/me endpoint
            console.log('✅ App startup: User roles loaded from login response');
          }, 100);
        },
        error: (error) => {
          console.warn('⚠️ App startup: Failed to get current user data:', error);
        }
      });
    }
  }
}
