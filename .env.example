# BizzCorp Frontend Environment Variables
# Copy this file to .env and update the values according to your environment

# API Configuration
API_BASE_URL=https://api-bizzcorp.antllp.com

# Development API Configuration (for local development)
# API_BASE_URL=http://localhost:8000

# Application Environment
NODE_ENV=production

# Optional: Custom port for development server
# PORT=4200

# Optional: Enable/disable debug mode
# DEBUG=false

# Optional: Application version
# APP_VERSION=1.0.0

# Docker Configuration
# These variables are used in docker-compose and nginx configuration
DOCKER_PORT=8020
NGINX_PORT=8020

# CORS Configuration
# Ensure your backend API server is configured to allow requests from:
# - http://localhost:4200 (development)
# - http://localhost:8020 (docker)
# - https://bizzcorp.antllp.com (production)
