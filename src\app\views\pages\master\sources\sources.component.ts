import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import {
  SourceService,
  Source,
  SourceStatistics
} from '../../../../core/services/source.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { SourceFormComponent } from './source-form/source-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-sources',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './sources.component.html',
  styleUrls: ['./sources.component.scss']
})
export class SourcesComponent implements OnInit {
  // Data properties
  sources: Source[] = [];
  deletedSources: Source[] = [];
  statistics: SourceStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedSourceType = '';
  selectedSourceCategory = '';
  selectedAttributionModel = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedSources: Set<string> = new Set();
  selectAll = false;

  // Filter options
  sourceTypes: any[] = [];
  sourceCategories: any[] = [];
  attributionModels: any[] = [];

  constructor(
    private sourceService: SourceService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadSources();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.sourceTypes = this.sourceService.getSourceTypes();
    this.sourceCategories = this.sourceService.getSourceCategories();
    this.attributionModels = this.sourceService.getAttributionModels();
  }

  /**
   * Load sources with current filters
   */
  loadSources(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      source_type: this.selectedSourceType || undefined,
      source_category: this.selectedSourceCategory || undefined,
      attribution_model: this.selectedAttributionModel || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.sourceService.getSourcesWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedSources = response.data.filter(s => s.deleted_at);
            this.sources = [];
          } else {
            this.sources = response.data.filter(s => !s.deleted_at);
            this.deletedSources = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load sources';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load sources. Please try again.'
        });
      }
    });
  }

  /**
   * Load source statistics
   */
  loadStatistics(): void {
    this.sourceService.getSourceStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search sources
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadSources();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadSources();
  }

  /**
   * Filter by source type
   */
  onSourceTypeFilter(): void {
    this.currentPage = 1;
    this.loadSources();
  }

  /**
   * Filter by source category
   */
  onSourceCategoryFilter(): void {
    this.currentPage = 1;
    this.loadSources();
  }

  /**
   * Filter by attribution model
   */
  onAttributionModelFilter(): void {
    this.currentPage = 1;
    this.loadSources();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedSources.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadSources();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadSources();
  }

  /**
   * Open create source modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(SourceFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadSources();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Source created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit source modal
   */
  openEditModal(source: Source): void {
    const modalRef = this.modalService.open(SourceFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.source = { ...source };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadSources();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Source updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete source
   */
  deleteSource(source: Source): void {
    this.popupService.showConfirmation({
      title: 'Delete Source',
      message: `Are you sure you want to delete "${source.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.sourceService.deleteSource(source.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadSources();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Source deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete source.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore source
   */
  restoreSource(source: Source): void {
    this.popupService.showConfirmation({
      title: 'Restore Source',
      message: `Are you sure you want to restore "${source.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.sourceService.restoreSource(source.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadSources();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Source restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore source.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadSources();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Sources uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.sourceService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'sources_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Generate attribution report
   */
  generateAttributionReport(): void {
    const selectedIds = Array.from(this.selectedSources);
    if (selectedIds.length === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select at least one source to generate an attribution report.'
      });
      return;
    }

    const params = {
      source_ids: selectedIds,
      date_from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Last 30 days
      date_to: new Date().toISOString().split('T')[0]
    };

    this.sourceService.getAttributionReport(params).subscribe({
      next: (response) => {
        if (response.success) {
          // Display the report in a modal or download it
          const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'attribution_report.json';
          link.click();
          window.URL.revokeObjectURL(url);

          this.popupService.showSuccess({
            title: 'Report Generated',
            message: 'Attribution report has been generated and downloaded successfully.',
            timer: 3000
          });
        } else {
          this.popupService.showError({
            title: 'Generation Failed',
            message: response.error || 'Failed to generate attribution report.'
          });
        }
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Generation Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Sync campaign data for selected sources
   */
  syncCampaignData(): void {
    const selectedIds = Array.from(this.selectedSources);
    if (selectedIds.length === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select at least one source to sync campaign data.'
      });
      return;
    }

    // For demo purposes, sync with a default platform
    const promises = selectedIds.map(sourceId =>
      this.sourceService.syncCampaignData(sourceId, 'google_ads').toPromise()
    );

    Promise.all(promises).then(results => {
      const successCount = results.filter(r => r?.success).length;
      this.popupService.showSuccess({
        title: 'Sync Complete',
        message: `Successfully synced ${successCount} of ${selectedIds.length} sources.`,
        timer: 3000
      });
      this.loadSources();
    }).catch(error => {
      this.popupService.showError({
        title: 'Sync Failed',
        message: 'Failed to sync campaign data.'
      });
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadSources();
    this.loadStatistics();
  }

  /**
   * Get source type label
   */
  getSourceTypeLabel(sourceType: string): string {
    const type = this.sourceTypes.find(t => t.value === sourceType);
    return type ? type.label : sourceType;
  }

  /**
   * Get source category label
   */
  getSourceCategoryLabel(category: string): string {
    const cat = this.sourceCategories.find(c => c.value === category);
    return cat ? cat.label : category;
  }

  /**
   * Get source category color
   */
  getSourceCategoryColor(category: string): string {
    const cat = this.sourceCategories.find(c => c.value === category);
    return cat ? cat.color : '#6c757d';
  }

  /**
   * Get attribution model label
   */
  getAttributionModelLabel(model: string): string {
    const attr = this.attributionModels.find(a => a.value === model);
    return attr ? attr.label : model;
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Format currency
   */
  formatCurrency(value: number | undefined, currency: string = 'USD'): string {
    if (value === undefined || value === null) {
      return 'N/A';
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  }

  /**
   * Format percentage
   */
  formatPercentage(value: number | undefined): string {
    if (value === undefined || value === null) {
      return 'N/A';
    }
    return `${(value * 100).toFixed(1)}%`;
  }

  /**
   * Get tracking parameters summary
   */
  getTrackingParametersSummary(source: Source): string {
    if (!source.tracking_parameters?.length) {
      return 'No tracking parameters';
    }

    const requiredCount = source.tracking_parameters.filter(p => p.is_required).length;
    const totalCount = source.tracking_parameters.length;

    return `${totalCount} parameters (${requiredCount} required)`;
  }

  /**
   * Get campaign integrations summary
   */
  getCampaignIntegrationsSummary(source: Source): string {
    if (!source.campaign_integration?.length) {
      return 'No integrations';
    }

    const activeCount = source.campaign_integration.filter(c => c.is_active).length;
    const totalCount = source.campaign_integration.length;

    return `${activeCount}/${totalCount} active integrations`;
  }

  /**
   * Toggle selection of a source
   */
  toggleSelection(id: string): void {
    if (this.selectedSources.has(id)) {
      this.selectedSources.delete(id);
    } else {
      this.selectedSources.add(id);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    if (this.selectAll) {
      // Select all
      this.getCurrentList().forEach(source => {
        this.selectedSources.add(source.id);
      });
    } else {
      // Deselect all
      this.getCurrentList().forEach(source => {
        this.selectedSources.delete(source.id);
      });
    }
  }

  /**
   * Update select all state based on current selections
   */
  updateSelectAllState(): void {
    const currentList = this.getCurrentList();
    this.selectAll = currentList.length > 0 &&
      currentList.every(source => this.selectedSources.has(source.id));
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): Source[] {
    return this.viewMode === 'deleted' ? this.deletedSources : this.sources;
  }

  /**
   * Track by function for ngFor performance
   */
  trackBySourceId(index: number, source: Source): string {
    return source.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
