import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LeasedPropertyRow } from '../ops-team.component';

@Component({
  selector: 'app-leased-property-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './leased-property-modal.component.html',
  styleUrls: ['./leased-property-modal.component.scss']
})
export class LeasedPropertyModalComponent implements OnInit {
  @Input() propertyId: number;
  @Input() properties: LeasedPropertyRow[] = [];

  formData: LeasedPropertyRow = {
    id: 0,
    projectName: '',
    location: '',
    areaLeased: 0,
    leaseRentPA: 0,
    securityDeposit: 0,
    debtOS: 0,
    marketValue: 0,
    remarks: ''
  };

  constructor(public activeModal: NgbActiveModal) { }

  ngOnInit(): void {
    // If editing an existing record, populate the form
    if (this.propertyId) {
      const property = this.properties.find(p => p.id === this.propertyId);
      if (property) {
        this.formData = { ...property };
      }
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Close the modal and pass the data back
    this.activeModal.close(this.formData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
