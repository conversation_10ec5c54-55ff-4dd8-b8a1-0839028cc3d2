import { AfterViewInit, Directive, ElementRef, OnDestroy, Input } from '@angular/core';
import * as feather from 'feather-icons';

@Directive({
  selector: '[appFeatherIcon]',
  standalone: true
})

export class FeatherIconDirective implements AfterViewInit, OnDestroy {
  private observer?: MutationObserver;
  private hasBeenReplaced = false;

  constructor(private el: ElementRef) { }

  ngAfterViewInit(): void {
    this.replaceIcon();

    // Watch for changes to prevent icon duplication
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-feather') {
          this.replaceIcon();
        }
      });
    });

    this.observer.observe(this.el.nativeElement, {
      attributes: true,
      attributeFilter: ['data-feather']
    });
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  private replaceIcon(): void {
    const element = this.el.nativeElement;

    // Clear any existing SVG content to prevent duplication
    const existingSvgs = element.querySelectorAll('svg');
    existingSvgs.forEach((svg: SVGElement) => svg.remove());

    // Clear any existing feather classes that might cause duplication
    element.classList.remove('feather');
    const classesToRemove = Array.from(element.classList as DOMTokenList).filter((cls) => cls.startsWith('feather-'));
    classesToRemove.forEach((cls) => element.classList.remove(cls));

    // Get the icon name from data-feather attribute
    const iconName = element.getAttribute('data-feather');

    if (iconName && iconName in feather.icons) {
      try {
        // Create the SVG element directly instead of using feather.replace()
        const iconSvg = (feather.icons as any)[iconName].toSvg({
          class: element.className,
          width: element.style.width || '24',
          height: element.style.height || '24'
        });

        // Replace the element's content with the SVG
        element.innerHTML = iconSvg;
        this.hasBeenReplaced = true;
      } catch (error) {
        console.error('Error replacing feather icon:', error);
        // Fallback to global replace for this specific element
        feather.replace();
      }
    }
  }
}
