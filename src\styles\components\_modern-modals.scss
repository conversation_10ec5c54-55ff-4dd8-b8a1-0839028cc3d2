/* Modern Modal Styles */

// Modal backdrop with blur effect
.modal-backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

// Modern modal dialog
.modal-dialog {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100vh - 2rem);
  }
}

// Modern modal content
.modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  background: var(--bs-body-bg);
  
  // Animation for modal entrance
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Modern modal header
.modal-header {
  background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  position: relative;
  
  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    &::before {
      content: '';
      width: 4px;
      height: 20px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
    }
  }
  
  .btn-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    opacity: 1;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }
    
    &:focus {
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    }
  }
}

// Modern modal body
.modal-body {
  padding: 2rem;
  color: var(--bs-body-color);
  
  // Enhanced form styling within modals
  .form-label {
    font-weight: 500;
    color: var(--bs-gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    
    .text-danger {
      color: var(--bs-danger) !important;
    }
  }
  
  .form-control, .form-select {
    border: 1.5px solid var(--bs-gray-300);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    
    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
      outline: none;
    }
    
    &.is-invalid {
      border-color: var(--bs-danger);
      box-shadow: 0 0 0 3px rgba(var(--bs-danger-rgb), 0.1);
    }
  }
  
  .invalid-feedback {
    font-size: 0.8rem;
    margin-top: 0.25rem;
    color: var(--bs-danger);
  }
  
  // Grid improvements
  .row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
    
    > [class*="col-"] {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }
  }
}

// Modern modal footer
.modal-footer {
  background: var(--bs-gray-50);
  border-top: 1px solid var(--bs-gray-200);
  padding: 1.5rem 2rem;
  gap: 0.75rem;
  
  .btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
    
    &.btn-primary {
      background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
      color: white;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px -5px rgba(var(--bs-primary-rgb), 0.4);
      }
    }
    
    &.btn-secondary {
      background: var(--bs-gray-200);
      color: var(--bs-gray-700);
      
      &:hover {
        background: var(--bs-gray-300);
        color: var(--bs-gray-800);
      }
    }
    
    &.btn-danger {
      background: linear-gradient(135deg, var(--bs-danger) 0%, rgba(var(--bs-danger-rgb), 0.8) 100%);
      color: white;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px -5px rgba(var(--bs-danger-rgb), 0.4);
      }
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: none !important;
    }
  }
}

// Loading state for modals
.modal-loading {
  .modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--bs-gray-300);
      border-top: 3px solid var(--bs-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }
    
    .loading-text {
      color: var(--bs-gray-600);
      font-size: 0.875rem;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Size variations
.modal-sm .modal-content {
  border-radius: 12px;
}

.modal-lg .modal-content,
.modal-xl .modal-content {
  border-radius: 20px;
}

// Responsive improvements
@media (max-width: 576px) {
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
  }
  
  .modal-content {
    border-radius: 12px;
  }
  
  .modal-header {
    padding: 1rem 1.5rem;
    
    .modal-title {
      font-size: 1.1rem;
    }
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}

// Dark mode support
[data-theme="dark"] {
  .modal-content {
    background: var(--bs-dark);
    color: var(--bs-light);
  }
  
  .modal-header {
    background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.9) 100%);
  }
  
  .modal-body {
    .form-label {
      color: var(--bs-gray-300);
    }
    
    .form-control, .form-select {
      background: var(--bs-gray-800);
      border-color: var(--bs-gray-600);
      color: var(--bs-light);
      
      &:focus {
        background: var(--bs-gray-700);
        border-color: var(--bs-primary);
      }
    }
  }
  
  .modal-footer {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
    
    .btn-secondary {
      background: var(--bs-gray-700);
      color: var(--bs-gray-300);
      
      &:hover {
        background: var(--bs-gray-600);
        color: var(--bs-light);
      }
    }
  }
}

// Special modal types
.modal-success .modal-header {
  background: linear-gradient(135deg, var(--bs-success) 0%, rgba(var(--bs-success-rgb), 0.8) 100%);
}

.modal-warning .modal-header {
  background: linear-gradient(135deg, var(--bs-warning) 0%, rgba(var(--bs-warning-rgb), 0.8) 100%);
  color: var(--bs-dark);
}

.modal-danger .modal-header {
  background: linear-gradient(135deg, var(--bs-danger) 0%, rgba(var(--bs-danger-rgb), 0.8) 100%);
}

.modal-info .modal-header {
  background: linear-gradient(135deg, var(--bs-info) 0%, rgba(var(--bs-info-rgb), 0.8) 100%);
}
