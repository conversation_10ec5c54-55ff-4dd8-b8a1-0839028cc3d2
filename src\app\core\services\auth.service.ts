import { Injectable, Injector } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, of, throwError, catchError, switchMap } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

export interface User {
  id: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  access_token?: string;
  refresh_token?: string;
  token_expiry?: number;
  is_active?: boolean;
  is_superuser?: boolean;
  // These will be populated later when the roles API is ready
  role?: string;
  roles?: any[];
  permissions?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private baseUrl = environment.apiUrl; // Use environment variable
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private refreshTokenTimeout?: any;

  // Token management configuration
  private readonly ACCESS_TOKEN_KEY = 'accessToken';
  private readonly REFRESH_TOKEN_KEY = 'refreshToken';
  private readonly USER_DATA_KEY = 'currentUser';

  constructor(
    private http: HttpClient,
    private injector: Injector
  ) {
    // Initialize from stored tokens
    this.initializeFromStorage();
  }

  // 🔐 1. Initialize from stored tokens
  private initializeFromStorage(): void {
    const accessToken = localStorage.getItem(this.ACCESS_TOKEN_KEY);
    const refreshToken = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    const userData = localStorage.getItem(this.USER_DATA_KEY);

    if (accessToken && refreshToken && userData) {
      try {
        const user = JSON.parse(userData);

        // Decode JWT to get expiry time
        const tokenExpiry = this.getTokenExpiry(accessToken);

        if (tokenExpiry) {
          user.token_expiry = tokenExpiry;
          user.access_token = accessToken;
          user.refresh_token = refreshToken;

          this.currentUserSubject.next(user);

          console.log('🔄 Initialized from storage:', {
            user: user.email,
            tokenExpiry: new Date(tokenExpiry).toLocaleString(),
            secondsUntilExpiry: Math.round((tokenExpiry - Date.now()) / 1000)
          });

          // Start refresh timer
          this.startRefreshTimer();
        } else {
          console.warn('⚠️ Could not decode token expiry, clearing storage');
          this.clearTokens();
        }
      } catch (error) {
        console.error('❌ Error parsing stored user data:', error);
        this.clearTokens();
      }
    }
  }

  // 🔐 2. Store tokens securely
  private storeTokens(accessToken: string, refreshToken: string, userData: any): void {
    console.log('💾 Storing tokens:', {
      accessTokenLength: accessToken.length,
      refreshTokenLength: refreshToken.length,
      userEmail: userData.email
    });

    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    localStorage.setItem(this.USER_DATA_KEY, JSON.stringify(userData));
  }

  // 🔐 3. Get current access token
  getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  // 🔐 4. Get current refresh token
  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  // 🔐 5. Clear all tokens
  private clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_DATA_KEY);
    this.currentUserSubject.next(null);
    this.stopRefreshTokenTimer();
  }

  // 🔐 6. Decode JWT token to get expiry time
  private getTokenExpiry(token: string): number | null {
    try {
      // JWT tokens have 3 parts separated by dots
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.warn('⚠️ Invalid JWT token format');
        return null;
      }

      // Decode the payload (second part)
      const payload = JSON.parse(atob(parts[1]));

      if (payload.exp) {
        // JWT exp is in seconds, convert to milliseconds
        const expiry = payload.exp * 1000;
        console.log('🔍 JWT decoded expiry:', new Date(expiry).toLocaleString());
        return expiry;
      } else {
        console.warn('⚠️ No exp field in JWT token');
        return null;
      }
    } catch (error) {
      console.error('❌ Error decoding JWT token:', error);
      return null;
    }
  }

  // 🧠 2. Start refresh timer based on token expiry
  private startRefreshTimer(): void {
    this.stopRefreshTokenTimer();

    const user = this.currentUserValue;
    if (!user?.token_expiry) {
      console.warn('⚠️ Cannot start refresh timer: no token expiry');
      return;
    }

    const timeUntilExpiry = user.token_expiry - Date.now();
    const secondsUntilExpiry = Math.round(timeUntilExpiry / 1000);

    // Adaptive refresh timing based on token duration
    let refreshBeforeExpiry: number;
    if (secondsUntilExpiry <= 120) { // Short tokens (≤2 minutes)
      refreshBeforeExpiry = 10; // Refresh 10s before expiry
    } else if (secondsUntilExpiry <= 600) { // Medium tokens (≤10 minutes)
      refreshBeforeExpiry = 30; // Refresh 30s before expiry
    } else if (secondsUntilExpiry <= 1800) { // Long tokens (≤30 minutes)
      refreshBeforeExpiry = 120; // Refresh 2 minutes before expiry
    } else { // Very long tokens (>30 minutes)
      refreshBeforeExpiry = 300; // Refresh 5 minutes before expiry
    }

    const refreshIn = Math.max(timeUntilExpiry - (refreshBeforeExpiry * 1000), 1000);

    console.log(`🔄 REFRESH TIMER: Set for ${Math.round(refreshIn/1000)}s (${refreshBeforeExpiry}s before ${secondsUntilExpiry}s expiry)`);

    this.refreshTokenTimeout = setTimeout(() => {
      console.log(`🔄 AUTO-REFRESH TRIGGERED: ${refreshBeforeExpiry}s before expiry`);
      this.performTokenRefresh();
    }, refreshIn);
  }

  // 🔄 3. Perform token refresh using refresh token
  private performTokenRefresh(): void {
    // Check if user has been logged out manually
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('🚪 User has been logged out, skipping token refresh');
      return;
    }

    const refreshToken = this.getRefreshToken();

    if (!refreshToken) {
      console.error('❌ No refresh token available for auto-refresh');
      this.handleRefreshFailure('No refresh token available');
      return;
    }

    console.log('📡 Performing automatic token refresh...');

    this.callRefreshAPI(refreshToken).subscribe({
      next: (response) => {
        console.log('✅ Auto-refresh successful');
        this.handleRefreshSuccess(response);
      },
      error: (error) => {
        console.error('❌ Auto-refresh failed:', error);
        this.handleRefreshFailure('Auto-refresh failed');
      }
    });
  }

  // 🔄 4. Call refresh API
  private callRefreshAPI(refreshToken: string): Observable<any> {
    const refreshPayload = { refresh_token: refreshToken };

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    return this.http.post<any>(`${this.baseUrl}/api/v1/auth/refresh`, refreshPayload, { headers });
  }

  // ✅ Handle refresh success
  private handleRefreshSuccess(response: any): void {
    const tokenData = response.data || response;

    const newAccessToken = tokenData.access_token;
    const newRefreshToken = tokenData.refresh_token;

    if (!newAccessToken) {
      console.error('❌ No access token in refresh response');
      this.handleRefreshFailure('Invalid refresh response');
      return;
    }

    // Get token expiry from JWT
    const tokenExpiry = this.getTokenExpiry(newAccessToken);

    if (!tokenExpiry) {
      console.error('❌ Could not decode new token expiry');
      this.handleRefreshFailure('Invalid token format');
      return;
    }

    // Update stored tokens
    const currentUser = this.currentUserValue;
    if (currentUser) {
      currentUser.access_token = newAccessToken;
      currentUser.refresh_token = newRefreshToken || currentUser.refresh_token;
      currentUser.token_expiry = tokenExpiry;

      this.storeTokens(newAccessToken, currentUser.refresh_token!, currentUser);
      this.currentUserSubject.next(currentUser);

      const secondsUntilExpiry = Math.round((tokenExpiry - Date.now()) / 1000);
      console.log(`🔄 TOKENS UPDATED: New expiry in ${secondsUntilExpiry}s`);

      // Restart refresh timer
      this.startRefreshTimer();
    }
  }

  // ❌ Handle refresh failure
  private handleRefreshFailure(reason: string): void {
    // Check if user has already been logged out manually
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('🚪 User already logged out, skipping refresh failure handling');
      return;
    }

    this.logout(false, `Session expired: ${reason}`);
  }  login(username: string, password: string, rememberMe: boolean = false): Observable<any> {
    // Clear the logged out flag if it exists
    localStorage.removeItem('user_logged_out');



    // Try with a simple JSON payload first, which is more common for RESTful APIs
    const loginPayload = {
      username: username,
      password: password
    };

    // Set proper headers for JSON content
    const httpOptions = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      })
    };return this.http.post<any>(`${this.baseUrl}/api/v1/auth/login`, loginPayload, httpOptions)
      .pipe(
        tap(response => {
          console.log('Auth API response:', response);
          console.log('🔍 API Response Debug - expires_in value:', response.expires_in);
          console.log('🔍 API Response Debug - expires_in type:', typeof response.expires_in);
          console.log('🔍 API Response Debug - full response keys:', Object.keys(response));

          try {
            // Extract token data from the nested structure (same as refresh token)
            const tokenData = response.data || response; // Handle both {data: {...}} and direct response

            // Get the access token from the response
            const accessToken = tokenData.access_token || tokenData.token;

            // Get refresh token if available
            const refreshToken = tokenData.refresh_token;

            if (!accessToken) {
              console.error('No access token in response:', response);
              throw new Error('No access token in response');
            }

            // Check if we have a refresh token, log warning if not
            if (!refreshToken) {
              console.warn('No refresh token in response - token refresh will not work');
            }

            // Initialize with minimal data - roles and permissions will be set from API only
            let role: string | undefined = undefined; // No default role
            let isSuperuser = false;
            let roles: any[] = [];
            let permissions: string[] = [];

            // Store is_superuser flag for reference but don't use it for role assignment
            if (response.data && response.data.is_superuser === true) {
              isSuperuser = true;
              console.log('🔑 User has is_superuser flag - will be handled by API roles');
            } else if (response.is_superuser === true) {
              isSuperuser = true;
              console.log('🔑 User has is_superuser flag - will be handled by API roles');
            }

            console.log('⚠️ No hardcoded roles assigned - waiting for API role assignment');

            console.log('🔍 Login API Response Debug - full response keys:', Object.keys(response));
            console.log('🔍 Login API Response Debug - response.data keys:', response.data ? Object.keys(response.data) : 'No data object');
            console.log('🔍 Login API Response Debug - response.data:', response.data);
            console.log('🔍 Login API Response Debug - response.data.id:', response.data?.id);
            console.log('🔍 Login API Response Debug - response.id:', response.id);
            console.log('🔍 Token data structure:', tokenData);
            console.log('🔍 expires_in value:', tokenData.expires_in);

            // 🔐 1. Get token expiry from JWT (more accurate than API expires_in)
            let tokenExpiry = this.getTokenExpiry(accessToken);

            if (!tokenExpiry) {
              // Fallback to API expires_in if JWT decode fails
              const expiresInSeconds = tokenData.expires_in || 60;
              tokenExpiry = Date.now() + (expiresInSeconds * 1000);
              console.log(`⚠️ JWT decode failed, using API expires_in: ${expiresInSeconds}s`);
            }



            // Save last login time
            const lastLogin = new Date().toLocaleString();
            localStorage.setItem('lastLogin', lastLogin);

            // Parse name for firstName and lastName from API response
            let firstName = '';
            let lastName = '';
            let fullName = '';

            // Get data from response.data or response directly
            const userData = response.data || response;

            // Use first_name and last_name from API response
            if (userData.first_name || userData.last_name) {
              firstName = userData.first_name || '';
              lastName = userData.last_name || '';
              fullName = `${firstName} ${lastName}`.trim();
            }
            // Fallback to response.firstName/lastName if available
            else if (response.firstName && response.lastName) {
              firstName = response.firstName;
              lastName = response.lastName;
              fullName = `${firstName} ${lastName}`;
            }
            // Fallback to username-based name
            else {
              fullName = response.name || username.split('@')[0];
              firstName = fullName;
              lastName = '';
            }

            const user: User = {
              id: userData.id || response.id || 'temp-id', // Use temp ID, will be updated from /users/me
              email: userData.email || response.email || username,
              name: fullName,
              firstName: firstName,
              lastName: lastName,
              role: role, // Will be null until API assigns it
              roles: roles, // Will be empty until API assigns them
              permissions: permissions, // Will be empty until API assigns them
              is_superuser: isSuperuser,
              access_token: accessToken,
              refresh_token: refreshToken,
              token_expiry: tokenExpiry,
              is_active: userData.is_active !== false
            };

            // 🔐 1. Store tokens using new secure method
            this.storeTokens(accessToken, refreshToken!, user);
            this.currentUserSubject.next(user);

            // Save credentials if remember me is enabled
            if (rememberMe) {
              this.saveRememberedCredentials(username, password, rememberMe);
            }

            // 🧠 2. Start refresh timer based on token expiry
            this.startRefreshTimer();

            // 🎭 3. Get current user data first to get the correct UUID, then fetch roles and permissions
            this.getCurrentUser().subscribe({
              next: (userResponse) => {
                console.log('✅ User data updated from /users/me:', userResponse);

                // Extract user ID from the response
                const userId = userResponse?.data?.id;
                if (userId) {
                  console.log('🔑 User ID extracted:', userId);
                  // Fetch user roles and permissions from API
                  this.fetchUserRolesAndPermissions(userId);
                } else {
                  console.warn('⚠️ No user ID found in /users/me response');
                }
              },
              error: (error) => {
                console.warn('⚠️ Failed to get current user data after login:', error);
                // Try to fetch roles anyway with the current user ID if available
                const currentUser = this.currentUserValue;
                if (currentUser?.id) {
                  console.log('🔄 Fallback: Using current user ID:', currentUser.id);
                  this.fetchUserRolesAndPermissions(currentUser.id);
                }
              }
            });
          } catch (error) {
            console.error('Error processing auth response:', error);
            throw error;
          }
        })
      );
  }  // 🔄 3. Public refresh token method (for manual calls)
  refreshToken(): Observable<any> {
    // Check if user has been logged out manually
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('🚪 User has been logged out, cannot refresh token');
      return throwError(() => new Error('User has been logged out'));
    }

    const refreshToken = this.getRefreshToken();

    if (!refreshToken) {
      console.error('❌ No refresh token available for manual refresh');
      return throwError(() => new Error('No refresh token available'));
    }

    console.log('🔄 Manual refresh token request...');

    return this.callRefreshAPI(refreshToken).pipe(
      tap(response => {
        console.log('✅ Manual refresh successful');
        this.handleRefreshSuccess(response);
      }),
      catchError(error => {
        console.error('❌ Manual refresh failed:', error);
        return throwError(() => error);
      })
    );
  }
  // Legacy method - redirect to new implementation
  private startRefreshTokenTimer(_user: User): void {
    console.log('🔄 Legacy startRefreshTokenTimer called, redirecting to new implementation');
    this.startRefreshTimer();
  }

  private stopRefreshTokenTimer(): void {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
  }

  register(userData: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/api/v1/auth/register`, userData);
  }

  createEmployee(employeeData: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/api/v1/employees/`, employeeData);
  }

  // 🚪 4. Logout and clear tokens
  logout(clearRememberedCredentials: boolean = false, reason?: string): void {
    console.log('🚪 Logging out user', reason ? `- Reason: ${reason}` : '');

    // Get current access token before clearing
    const accessToken = this.getAccessToken();

    // Clear tokens using new method
    this.clearTokens();

    // Clear user roles - handled by clearTokens()
    console.log('🗑️ User roles cleared');

    // Clear additional data if needed
    if (clearRememberedCredentials) {
      this.clearAllStorageData(true);
      console.log('🗑️ Remembered credentials cleared during logout');
    } else {
      this.clearAllStorageData(false);
      console.log('💾 Remembered credentials preserved for auto-login');
    }

    // Clear session storage
    this.clearSessionStorage();

    // Clear browser cache and cookies
    this.clearBrowserCache();

    // Store logout flags
    localStorage.setItem('user_logged_out', 'true');
    if (reason) {
      localStorage.setItem('logout_reason', reason);
    }

    // Clear any existing error messages or popups
    localStorage.removeItem('session_error');
    localStorage.removeItem('token_refresh_error');

    // Close any existing SweetAlert popups
    try {
      // Import Swal dynamically to avoid dependency issues
      import('sweetalert2').then((Swal) => {
        if (Swal.default.isVisible()) {
          Swal.default.close();
          console.log('🗑️ Closed existing SweetAlert popup during logout');
        }
      }).catch(() => {
        // Ignore if SweetAlert is not available
      });
    } catch (error) {
      // Ignore if SweetAlert is not available
    }

    // Optional: Call backend to invalidate the token
    if (accessToken) {
      this.http.post<any>(`${this.baseUrl}/api/v1/auth/logout`, {})
        .subscribe({
          next: () => console.log('✅ Token invalidated on server'),
          error: (err) => console.error('❌ Failed to invalidate token on server', err)
        });
    }

    console.log('✅ User logged out successfully - all tokens cleared');

    // Redirect to login page
    this.redirectToLogin(reason);
  }

  /**
   * Clear all localStorage data except essential logout flags
   */
  private clearAllStorageData(clearRememberedCredentials: boolean): void {
    try {
      // Preserve remembered credentials if not explicitly clearing them
      let rememberedCredentials = null;
      if (!clearRememberedCredentials) {
        const stored = localStorage.getItem('rememberedCredentials');
        if (stored) {
          rememberedCredentials = stored;
        }
      }

      // Get all keys to clear
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          keysToRemove.push(key);
        }
      }

      // Clear all keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      // Restore remembered credentials if we preserved them
      if (rememberedCredentials && !clearRememberedCredentials) {
        localStorage.setItem('rememberedCredentials', rememberedCredentials);
        console.log('✅ Remembered credentials restored');
      }

      console.log('✅ localStorage cleared');
    } catch (error) {
      console.error('❌ Error clearing localStorage:', error);
    }
  }

  /**
   * Clear all sessionStorage data
   */
  private clearSessionStorage(): void {
    try {
      console.log('🧹 Clearing sessionStorage data...');
      sessionStorage.clear();
      console.log('✅ sessionStorage cleared');
    } catch (error) {
      console.error('❌ Error clearing sessionStorage:', error);
    }
  }

  /**
   * Clear browser cache and cookies (where possible)
   */
  private clearBrowserCache(): void {
    try {
      console.log('🧹 Clearing browser cache and cookies...');

      // Clear all cookies for the current domain
      this.clearAllCookies();

      // Clear browser cache (limited by browser security)
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        }).catch(error => {
          console.warn('Could not clear cache storage:', error);
        });
      }

      // Force reload to clear any cached resources
      if (typeof window !== 'undefined') {
        // Clear any cached data in memory
        if ('performance' in window && 'clearResourceTimings' in window.performance) {
          window.performance.clearResourceTimings();
        }
      }

      console.log('✅ Browser cache and cookies cleared');
    } catch (error) {
      console.error('❌ Error clearing browser cache:', error);
    }
  }

  /**
   * Clear all cookies for the current domain
   */
  private clearAllCookies(): void {
    try {
      const cookies = document.cookie.split(';');

      cookies.forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();

        if (name) {
          // Clear cookie for current path
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;

          // Clear cookie for root path
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;`;

          // Clear cookie for current domain
          const domain = window.location.hostname;
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${domain}`;

          // Clear cookie for parent domain (if subdomain)
          if (domain.includes('.')) {
            const parentDomain = '.' + domain.split('.').slice(-2).join('.');
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${parentDomain}`;
          }
        }
      });

      console.log('✅ All cookies cleared');
    } catch (error) {
      console.error('❌ Error clearing cookies:', error);
    }
  }

  /**
   * Redirect to login page with appropriate message
   */
  private redirectToLogin(reason?: string): void {
    // Use Angular Router if available, otherwise fallback to window.location
    try {
      // Try to get router from injector
      const router = this.injector?.get(Router);
      if (router) {
        const queryParams: any = {};
        if (reason) {
          queryParams.message = reason;
        }
        router.navigate(['/auth/login'], { queryParams });
      } else {
        // Fallback to window.location with forced reload
        window.location.href = '/auth/login' + (reason ? `?message=${encodeURIComponent(reason)}` : '');
      }
    } catch (error) {
      console.error('Error redirecting to login:', error);
      // Final fallback with forced reload
      window.location.href = '/auth/login';
    }
  }  get currentUserValue(): User | null {
    // Check if the user has explicitly logged out
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('User has explicitly logged out');
      return null;
    }

    // Return the current user from the BehaviorSubject
    return this.currentUserSubject.value;
  }

  // Public method to update current user (for debugging/fixing role issues)
  updateCurrentUser(user: User): void {
    localStorage.setItem('currentUser', JSON.stringify(user));
    this.currentUserSubject.next(user);
    console.log('✅ User updated:', user.email, 'Role:', user.role);
  }

  /**
   * Check if auto-login should be attempted
   */
  shouldAttemptAutoLogin(): boolean {
    // Don't auto-login if user explicitly logged out
    if (localStorage.getItem('user_logged_out') === 'true') {
      return false;
    }

    // Don't auto-login if user is already logged in
    if (this.isLoggedIn()) {
      return false;
    }

    // Check if remember me credentials exist
    try {
      const remembered = localStorage.getItem('rememberedCredentials');
      if (remembered) {
        const credentials = JSON.parse(remembered);
        return credentials.rememberMe && credentials.username && credentials.password;
      }
    } catch (error) {
      console.error('Error checking remembered credentials:', error);
    }

    return false;
  }

  /**
   * Get remembered credentials for auto-login
   */
  getRememberedCredentials(): { username: string; password: string; rememberMe: boolean } | null {
    try {
      const remembered = localStorage.getItem('rememberedCredentials');
      if (remembered) {
        const credentials = JSON.parse(remembered);
        // Decrypt the password (simple base64 for demo - use proper encryption in production)
        if (credentials.password) {
          credentials.password = atob(credentials.password);
        }
        return credentials;
      }
    } catch (error) {
      console.error('Error retrieving remembered credentials:', error);
    }
    return null;
  }

  /**
   * Save credentials for remember me functionality
   */
  saveRememberedCredentials(username: string, password: string, rememberMe: boolean): void {
    if (rememberMe) {
      try {
        // Clear the logged out flag since user wants to be remembered
        localStorage.removeItem('user_logged_out');

        // Encrypt the password (simple base64 for demo - use proper encryption in production)
        const encryptedPassword = btoa(password);
        const credentials = {
          username: username,
          password: encryptedPassword,
          rememberMe: true,
          savedAt: Date.now() // Add timestamp for potential expiry
        };
        localStorage.setItem('rememberedCredentials', JSON.stringify(credentials));
        console.log('💾 Credentials saved for remember me functionality');
        console.log('🔓 User logged out flag cleared for auto-login');
      } catch (error) {
        console.error('❌ Error saving credentials:', error);
      }
    } else {
      // Remove remembered credentials if remember me is not checked
      this.clearRememberedCredentials();
    }
  }

  /**
   * Clear remembered credentials
   */
  clearRememberedCredentials(): void {
    localStorage.removeItem('rememberedCredentials');
    console.log('🗑️ Remembered credentials cleared');
  }

  isLoggedIn(): boolean {
    // Check if the user has explicitly logged out
    if (localStorage.getItem('user_logged_out') === 'true') {
      return false;
    }

    const user = this.currentUserValue;
    if (!user?.access_token) {
      return false;
    }

    // Check if token is expired
    if (user.token_expiry && user.token_expiry <= Date.now()) {
      console.log('Token has expired, logging out user');
      this.logout(false, 'Your session has expired. Please log in again.');
      return false;
    }

    return true;
  }

  /**
   * Check if token is expired or about to expire
   */
  isTokenExpired(): boolean {
    const user = this.currentUserValue;
    if (!user?.token_expiry) {
      return false;
    }
    return user.token_expiry <= Date.now();
  }

  /**
   * Check if token will expire soon (adaptive based on token lifetime)
   * This works alongside auto-refresh to give users control
   */
  isTokenExpiringSoon(): boolean {
    const user = this.currentUserValue;
    if (!user?.token_expiry) {
      return false;
    }

    const now = Date.now();
    const timeUntilExpiry = user.token_expiry - now;
    const secondsUntilExpiry = Math.round(timeUntilExpiry / 1000);
    const minutesUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60));
    const hoursUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60 * 60));
    const daysUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60 * 60 * 24));

    // Adaptive warning thresholds based on token duration
    if (secondsUntilExpiry <= 120) { // 2 minutes or less (short tokens)
      const warningThreshold = secondsUntilExpiry <= 35; // Show warning at 35 seconds
      console.log(`🔍 Short token check: ${secondsUntilExpiry}s remaining, warning at 35s: ${warningThreshold}`);
      return warningThreshold;
    }
    else if (secondsUntilExpiry <= 600) { // 10 minutes or less (medium tokens)
      const warningThreshold = secondsUntilExpiry <= 120; // Show warning at 2 minutes
      console.log(`🔍 Medium token check: ${secondsUntilExpiry}s remaining, warning at 2min: ${warningThreshold}`);
      return warningThreshold;
    }
    else if (secondsUntilExpiry <= 1800) { // 30 minutes or less (long tokens)
      const fiveMinutesInMs = 5 * 60 * 1000;
      const warningThreshold = user.token_expiry <= (now + fiveMinutesInMs);
      console.log(`🔍 Long token check: ${Math.round(secondsUntilExpiry/60)}min remaining, warning at 5min: ${warningThreshold}`);
      return warningThreshold;
    }
    else if (secondsUntilExpiry <= 86400) { // 24 hours or less (daily tokens)
      const thirtyMinutesInMs = 30 * 60 * 1000;
      const warningThreshold = user.token_expiry <= (now + thirtyMinutesInMs);
      console.log(`🔍 Daily token check: ${hoursUntilExpiry}h remaining, warning at 30min: ${warningThreshold}`);
      return warningThreshold;
    }
    else if (secondsUntilExpiry <= 604800) { // 7 days or less (weekly tokens)
      const twoHoursInMs = 2 * 60 * 60 * 1000;
      const warningThreshold = user.token_expiry <= (now + twoHoursInMs);
      console.log(`🔍 Weekly token check: ${daysUntilExpiry}d remaining, warning at 2h: ${warningThreshold}`);
      return warningThreshold;
    }
    else if (secondsUntilExpiry <= 2592000) { // 30 days or less (monthly tokens)
      const twentyFourHoursInMs = 24 * 60 * 60 * 1000;
      const warningThreshold = user.token_expiry <= (now + twentyFourHoursInMs);
      console.log(`🔍 Monthly token check: ${daysUntilExpiry}d remaining, warning at 24h: ${warningThreshold}`);
      return warningThreshold;
    }
    else { // Very long tokens (2+ months) - show warning 7 days before expiry
      const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;
      const warningThreshold = user.token_expiry <= (now + sevenDaysInMs);
      console.log(`🔍 Long-term token check: ${daysUntilExpiry}d remaining, warning at 7d: ${warningThreshold}`);
      return warningThreshold;
    }
  }

  // Enhanced role check methods using user data
  isAdmin(): boolean {
    const user = this.currentUserValue;
    if (!user) return false;

    console.log('🔍 isAdmin() check for user:', user.email);
    console.log('🔍 User role:', user.role);
    console.log('🔍 User roles array:', user.roles);
    console.log('🔍 User is_superuser:', user.is_superuser);
    console.log('🔍 User permissions:', user.permissions?.slice(0, 5), '... (showing first 5)');

    // Check role property
    const hasAdminRole = user.role === 'admin';

    // Check roles array for admin role
    const hasAdminInRoles = user.roles?.includes('admin') === true ||
                           user.roles?.includes('role_admin') === true ||
                           user.roles?.includes('role_superuser') === true;

    // Check legacy is_superuser flag
    const isSuperuser = user.is_superuser === true;

    // Check for admin permissions (fallback)
    const hasAdminPermissions = user.permissions?.some(p =>
      ['users:read', 'users:create', 'roles:read', 'permissions:read', 'audit:read'].includes(p)
    ) === true;

    const result = hasAdminRole || hasAdminInRoles || isSuperuser || hasAdminPermissions;

    console.log('🔍 Admin check results:', {
      hasAdminRole,
      hasAdminInRoles,
      isSuperuser,
      hasAdminPermissions,
      finalResult: result
    });

    return result;
  }

  // Check if user is a manager
  isManager(): boolean {
    const user = this.currentUserValue;
    if (!user) return false;
    return user.role === 'manager' || user.roles?.includes('role_manager') === true;
  }

  // Check if user is an employee
  isEmployee(): boolean {
    const user = this.currentUserValue;
    if (!user) return false;
    return user.role === 'employee' || user.roles?.includes('role_employee') === true;
  }

  // Check if user has manager role specifically (for routing guards)
  hasManagerRole(): boolean {
    return this.isManager();
  }

  // Permission check - STRICT: Only explicit permissions allowed
  hasPermission(permission: string): boolean {
    const user = this.currentUserValue;

    if (!user) {
      console.log(`❌ PERMISSION CHECK: No user logged in for permission: ${permission}`);
      return false;
    }

    // STRICT PERMISSION CHECK - Only explicit permissions in array
    const hasExplicitPermission = user.permissions?.includes(permission) || false;

    console.log(`🔍 PERMISSION CHECK: ${permission}`);
    console.log(`  👤 User: ${user.email}`);
    console.log(`  🔑 User permissions:`, user.permissions || []);
    console.log(`  ✅ Has explicit permission: ${hasExplicitPermission}`);
    console.log(`  🎯 Result: ${hasExplicitPermission}`);

    // REMOVED: Wildcard (*) permission check
    // REMOVED: is_superuser bypass
    // Only explicit permissions are allowed

    return hasExplicitPermission;
  }

  // Get all user permissions
  getUserPermissions(): string[] {
    const user = this.currentUserValue;
    return user?.permissions || [];
  }

  // Get user roles
  getUserRoles(): string[] {
    const user = this.currentUserValue;
    return user?.roles || [];
  }

  // Check if user has specific role
  hasRole(role: string): boolean {
    const user = this.currentUserValue;
    return user?.roles?.includes(role) || false;
  }

  // User roles are now loaded from login response and /users/me endpoint
  /**
   * Fetch user roles and permissions from the API endpoint - MANDATORY
   * Uses the current user endpoint /me/roles instead of admin endpoint
   */
  private fetchUserRolesAndPermissions(userId: string): void {
    // Use the current user endpoint instead of admin endpoint
    const apiUrl = `${this.baseUrl}/api/v1/user-roles/me/roles`;

    console.log('🔄 Fetching user roles and permissions from:', apiUrl);
    console.log('🔑 For user ID:', userId);

    this.http.get<any>(apiUrl).subscribe({
      next: (response) => {
        console.log('✅ User roles API response:', response);

        if (response.success && response.data && Array.isArray(response.data)) {
          console.log('📋 Processing roles from API:', response.data);
          this.updateUserPermissionsFromRoles(this.currentUserValue!, response.data);
        } else {
          console.error('❌ No roles found in API response - this is required for access');
          console.log('📝 API Response structure:', response);
          this.handleRoleAPIFailure('No roles found in API response');
        }
      },
      error: (error) => {
        console.error('❌ Failed to fetch user roles from API - this is required for access:', error);
        this.handleRoleAPIFailure(`Role API failed: ${error.message || 'Unknown error'}`);
      }
    });
  }

  /**
   * Handle role API failure by logging out user or showing error
   */
  private handleRoleAPIFailure(reason: string): void {
    console.error('🚨 Role API failure - user cannot access system without roles:', reason);

    // Create a more user-friendly error message
    const errorMessage = `
      Unable to load your user roles from the server.

      Reason: ${reason}

      This is required for system access. Please:
      • Contact your system administrator
      • Check your internet connection
      • Try logging in again

      You will be redirected to the login page.
    `;

    // Show error message to user
    alert(errorMessage);

    // Logout user since they cannot access the system without roles
    this.logout();
  }



  /**
   * Update user permissions based purely on API roles and their permissions
   */
  private updateUserPermissionsFromRoles(user: User, apiRoles: any[]): void {
    console.log('🔍 Updating user permissions based purely on API roles:', apiRoles);

    // Extract all permissions from API roles
    const apiPermissions: string[] = [];
    apiRoles.forEach(apiRole => {
      if (apiRole.permissions) {
        apiRole.permissions.forEach((permission: { name: string }) => {
          apiPermissions.push(permission.name);
        });
      }
    });

    console.log('🔑 API-based permissions extracted:', apiPermissions);

    // Use the actual role name from API (future-proof for any new roles)
    const roleNames = apiRoles.map(r => r.name);
    console.log('📋 API Role names:', roleNames);

    // Use the first role name from API - NO FALLBACK
    if (roleNames.length === 0) {
      console.error('❌ No role names found in API response - cannot assign role');
      this.handleRoleAPIFailure('No role names found in API response');
      return;
    }

    const role = roleNames[0];

    console.log('🎯 FINAL ROLE ASSIGNMENT (using actual API role):', role);
    console.log('🔍 All available role names:', roleNames);
    console.log('🔍 API permissions extracted:', apiPermissions);



    // Use ONLY API-based permissions (no hardcoded permissions)
    console.log(`✅ Final role: ${role}, API permissions: ${apiPermissions.length}`);

    // Update user object with role and permissions
    user.role = role;
    user.permissions = apiPermissions; // Only API permissions
    user.roles = roleNames; // Store the role names array for reference

    console.log('✅ User object updated:', {
      email: user.email,
      role: user.role,
      roles: user.roles,
      permissionCount: user.permissions?.length || 0
    });

    // 🎯 IMPORTANT: Save the updated user to localStorage and notify subscribers
    localStorage.setItem('currentUser', JSON.stringify(user));
    this.currentUserSubject.next(user);

    console.log('💾 User role and permissions saved to localStorage and BehaviorSubject updated');
  }

  // Get current user from API and update permissions
  getCurrentUser(): Observable<any> {
    const currentUser = this.currentUserValue;

    if (!currentUser?.access_token) {
      return throwError(() => new Error('No access token available'));
    }

    const httpOptions = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${currentUser.access_token}`
      })
    };

    return this.http.get<any>(`${this.baseUrl}/api/v1/users/me`, httpOptions).pipe(
      tap(response => {
        console.log('📡 Current user API response:', response);

        if (response.success && response.data) {
          const userData = response.data;
          console.log('🔍 /users/me userData.id:', userData.id);
          console.log('🔍 /users/me userData.id type:', typeof userData.id);

          if (currentUser) {
            // Update user permissions based on is_superuser from API
            const updatedUser = this.updateUserPermissions(currentUser, userData);

            // Update user ID if we got a proper UUID from /users/me
            if (userData.id && userData.id !== currentUser.id) {
              console.log('🔄 Updating user ID from /users/me:', userData.id);
              updatedUser.id = userData.id;
            }

            // Save updated user to localStorage
            localStorage.setItem('currentUser', JSON.stringify(updatedUser));
            this.currentUserSubject.next(updatedUser);

            console.log('✅ User permissions updated from API');
          }
        }
      })
    );
  }

  // Update user permissions based on API data
  private updateUserPermissions(currentUser: User, apiUserData: any): User {
    // IMPORTANT: Don't override role here - keep the existing role and permissions
    // that were set by the role-based logic in updateUserPermissionsFromRoles()

    console.log('🔄 Updating user permissions - preserving existing role:', currentUser.role);
    console.log('🔄 API user data is_superuser:', apiUserData.is_superuser);

    // 🎯 PRESERVE the role and permissions that were set by updateUserPermissionsFromRoles()
    // Do NOT fallback to default values - this was causing the role override bug!
    const role = currentUser.role; // Keep the role as-is (could be 'manager' from API mapping)
    const roles = currentUser.roles; // Keep the roles as-is (could be ['role_manager'] from API)
    const permissions = currentUser.permissions; // Keep the permissions as-is (from API)

    console.log('✅ Preserving role:', role, 'roles:', roles, 'with permissions:', permissions?.length || 0);

    // Update user object with fresh data from API
    return {
      ...currentUser,
      id: apiUserData.id || currentUser.id,
      email: apiUserData.email || currentUser.email,
      firstName: apiUserData.first_name || currentUser.firstName,
      lastName: apiUserData.last_name || currentUser.lastName,
      name: `${apiUserData.first_name || ''} ${apiUserData.last_name || ''}`.trim() || currentUser.name,
      role: role,
      roles: roles,
      permissions: permissions,
      is_superuser: apiUserData.is_superuser,
      is_active: apiUserData.is_active
    };
  }

  // Check and refresh user permissions from API
  refreshUserPermissions(): Observable<any> {
    if (!this.isLoggedIn()) {
      return throwError(() => new Error('User not logged in'));
    }

    return this.getCurrentUser();
  }

  // Initialize user permissions on app startup
  initializeUserPermissions(): Observable<any> {
    const currentUser = this.currentUserValue;

    if (!currentUser) {
      console.log('👤 No user logged in - skipping permission initialization');
      return of(null);
    }

    console.log('🔄 Initializing user permissions from API...');
    return this.getCurrentUser().pipe(
      tap(() => {
        console.log('✅ User permissions initialized successfully');
      })
    );
  }

  /**
   * Test token refresh functionality (for debugging)
   */
  testTokenRefresh(): Observable<any> {
    console.log('🧪 Testing token refresh functionality...');
    return this.refreshToken();
  }

  /**
   * Get current token status for debugging
   */
  getTokenStatus(): any {
    const currentUser = this.currentUserValue;
    const status = {
      isLoggedIn: this.isLoggedIn(),
      isTokenExpired: this.isTokenExpired(),
      isTokenExpiringSoon: this.isTokenExpiringSoon(),
      hasAccessToken: !!currentUser?.access_token,
      hasRefreshToken: !!currentUser?.refresh_token,
      tokenExpiry: currentUser?.token_expiry ? new Date(currentUser.token_expiry).toLocaleString() : 'N/A',
      timeUntilExpiry: currentUser?.token_expiry ? Math.round((currentUser.token_expiry - Date.now()) / (1000 * 60)) : 'N/A'
    };

    console.log('📊 Token Status:', status);
    return status;
  }

  /**
   * Manually set token expiry for testing (ONLY FOR DEVELOPMENT)
   */
  setTokenExpiryForTesting(minutesFromNow: number): void {
    const currentUser = this.currentUserValue;
    if (!currentUser) {
      console.error('❌ No user logged in for testing');
      return;
    }

    const newExpiry = Date.now() + (minutesFromNow * 60 * 1000);
    const updatedUser = {
      ...currentUser,
      token_expiry: newExpiry
    };

    localStorage.setItem('currentUser', JSON.stringify(updatedUser));
    this.currentUserSubject.next(updatedUser);

    // Restart the refresh timer with new expiry
    this.stopRefreshTokenTimer();
    this.startRefreshTokenTimer(updatedUser);

    console.log(`🧪 Token expiry set to ${minutesFromNow} minutes from now: ${new Date(newExpiry).toLocaleString()}`);
  }

  /**
   * Manual method to test user data fetching (ONLY FOR DEVELOPMENT)
   */
  testUserDataFetching(): void {
    console.log('🧪 Testing user data fetching...');

    // Get current user from /users/me to ensure we have the correct data
    this.getCurrentUser().subscribe({
      next: () => {
        console.log('✅ User data updated from /users/me');
        console.log('✅ User data test completed');
      },
      error: (error) => {
        console.error('❌ User data test failed:', error);
      }
    });
  }
}
