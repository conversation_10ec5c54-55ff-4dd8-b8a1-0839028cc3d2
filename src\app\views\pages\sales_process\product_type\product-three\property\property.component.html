<div class="card">
  <div class="card-body">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h4 class="card-title mb-1">Property Form</h4>
        <p class="text-secondary">Property Management Application</p>
      </div>
    </div>

    <!-- Property Form -->
    <form [formGroup]="propertyForm" (ngSubmit)="onSubmit()">

      <!-- Basic Information Section -->
      <div class="section-header mb-3">
        <h5>Basic Information</h5>
      </div>

      <div class="row">
        <!-- Unique Code -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="UniqueCode" class="form-label">Unique Code</label>
          <input type="text" class="form-control" id="UniqueCode" formControlName="UniqueCode" readonly>
        </div>

        <!-- Category -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="category" class="form-label">Category</label>
          <select class="form-select" id="category" formControlName="category"
            [ngClass]="{'is-invalid': isFormSubmitted && form['category'].errors}">
            <option value="">Select Category</option>
            <option value="Developer">Developer</option>
            <option value="Agent">Agent</option>
            <option value="Landowner">Landowner</option>
          </select>
          @if (isFormSubmitted && form['category'].errors?.required) {
            <div class="invalid-feedback">Category is required</div>
          }
        </div>

        <!-- Required Deal -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="requiredDeal" class="form-label">Required Deal</label>
          <select class="form-select" id="requiredDeal" formControlName="requiredDeal"
            [ngClass]="{'is-invalid': isFormSubmitted && form['requiredDeal'].errors}">
            <option value="">Select Deal Type</option>
            <option value="Sale">Sale</option>
            <option value="Purchase">Purchase</option>
            <option value="JV_Syndication">JV Syndication</option>
            <option value="Lease_Rental">Lease Rental</option>
          </select>
          @if (isFormSubmitted && form['requiredDeal'].errors?.required) {
            <div class="invalid-feedback">Required deal is required</div>
          }
        </div>
      </div>

      <!-- Conditional Fields based on Required Deal -->
      @if (dealFromOptions.length > 0) {
        <div class="row">
          <!-- Deal From -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="dealFrom" class="form-label">Deal From</label>
            <select class="form-select" id="dealFrom" formControlName="dealFrom"
              [ngClass]="{'is-invalid': isFormSubmitted && form['dealFrom'].errors}">
              <option value="">Select Deal From</option>
              @for (option of dealFromOptions; track option.value) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
            @if (isFormSubmitted && form['dealFrom'].errors?.required) {
              <div class="invalid-feedback">Deal from is required</div>
            }
          </div>

          <!-- Property Description -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="propertyDescription" class="form-label">Property Description</label>
            <select class="form-select" id="propertyDescription" formControlName="propertyDescription"
              [ngClass]="{'is-invalid': isFormSubmitted && form['propertyDescription'].errors}">
              <option value="">Select Property Type</option>
              @for (option of propertyDescriptionOptions; track option.value) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
            @if (isFormSubmitted && form['propertyDescription'].errors?.required) {
              <div class="invalid-feedback">Property description is required</div>
            }
          </div>

          <!-- Configuration (appears when Flat or Land is selected) -->
          @if (configurationOptions.length > 0) {
            <div class="col-md-6 col-lg-3 mb-3">
              <label for="configuration" class="form-label">Configuration</label>
              <select class="form-select" id="configuration" formControlName="configuration">
                <option value="">Select Configuration</option>
                @for (option of configurationOptions; track option.value) {
                  <option [value]="option.value">{{ option.label }}</option>
                }
              </select>
            </div>
          }
        </div>
      }

      <!-- Area Details Section -->
      <div class="section-header mt-4 mb-3">
        <h5>Area Details</h5>
      </div>

      <div class="row">
        <!-- Location -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="location" class="form-label">Location</label>
          <input type="text" class="form-control" id="location" formControlName="location"
            [ngClass]="{'is-invalid': isFormSubmitted && form['location'].errors}"
            placeholder="Enter location">
          @if (isFormSubmitted && form['location'].errors?.required) {
            <div class="invalid-feedback">Location is required</div>
          }
        </div>

        <!-- Sublocation -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="sublocation" class="form-label">Sub Location</label>
          <input type="text" class="form-control" id="sublocation" formControlName="sublocation"
            [ngClass]="{'is-invalid': isFormSubmitted && form['sublocation'].errors}"
            placeholder="Enter sublocation">
          @if (isFormSubmitted && form['sublocation'].errors?.required) {
            <div class="invalid-feedback">Sublocation is required</div>
          }
        </div>

        <!-- Google Link -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="googleLink" class="form-label">Google Link</label>
          <input type="url" class="form-control" id="googleLink" formControlName="googleLink"
            [ngClass]="{'is-invalid': isFormSubmitted && form['googleLink'].errors}"
            placeholder="Enter Google Maps link">
          @if (isFormSubmitted && form['googleLink'].errors?.required) {
            <div class="invalid-feedback">Google link is required</div>
          }
          @if (isFormSubmitted && form['googleLink'].errors?.pattern) {
            <div class="invalid-feedback">Please enter a valid URL</div>
          }
        </div>

        <!-- Property Photos -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="propertyPhotos" class="form-label">Property Photos</label>
          <select class="form-select" id="propertyPhotos" formControlName="propertyPhotos"
            [ngClass]="{'is-invalid': isFormSubmitted && form['propertyPhotos'].errors}">
            <option value="">Select Status</option>
            <option value="Received">Received</option>
            <option value="Pending">Pending</option>
          </select>
          @if (isFormSubmitted && form['propertyPhotos'].errors?.required) {
            <div class="invalid-feedback">Property photos status is required</div>
          }
        </div>
      </div>

      <!-- Pricing Information Section -->
      @if (pricePerUnitOptions.length > 0) {
        <div class="section-header mt-4 mb-3">
          <h5>Pricing Information</h5>
        </div>

        <div class="row">
          <!-- Price Per Unit -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="pricePerUnit" class="form-label">Price Per Unit</label>
            <select class="form-select" id="pricePerUnit" formControlName="pricePerUnit"
              [ngClass]="{'is-invalid': isFormSubmitted && form['pricePerUnit'].errors}">
              <option value="">Select Unit</option>
              @for (option of pricePerUnitOptions; track option.value) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
            @if (isFormSubmitted && form['pricePerUnit'].errors?.required) {
              <div class="invalid-feedback">Price per unit is required</div>
            }
          </div>

          <!-- Lump Sum Price -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="lumpSumPrice" class="form-label">Lump Sum Price</label>
            <input type="number" class="form-control" id="lumpSumPrice" formControlName="lumpSumPrice"
              [ngClass]="{'is-invalid': isFormSubmitted && form['lumpSumPrice'].errors}"
              placeholder="Enter lump sum price"
              (input)="onLumpSumPriceChange()">
            @if (isFormSubmitted && form['lumpSumPrice'].errors?.required) {
              <div class="invalid-feedback">Lump sum price is required</div>
            }
            @if (isFormSubmitted && form['lumpSumPrice'].errors?.min) {
              <div class="invalid-feedback">Price must be greater than or equal to 0</div>
            }
          </div>

          <!-- Auto Calculated -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="autoCalculated" class="form-label">Auto Calculated</label>
            <input type="number" class="form-control" id="autoCalculated" formControlName="autoCalculated"
              placeholder="Auto calculated value" readonly>
            <small class="form-text text-muted">This field is automatically calculated</small>
          </div>

          <!-- Negotiation If Any -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="negotiationIfAny" class="form-label">Negotiation If Any</label>
            <select class="form-select" id="negotiationIfAny" formControlName="negotiationIfAny"
              [ngClass]="{'is-invalid': isFormSubmitted && form['negotiationIfAny'].errors}">
              <option value="">Select Option</option>
              <option value="Yes">Yes</option>
              <option value="No">No</option>
            </select>
            @if (isFormSubmitted && form['negotiationIfAny'].errors?.required) {
              <div class="invalid-feedback">Negotiation option is required</div>
            }
          </div>
        </div>
      }



      <!-- JV Syndication Specific Fields -->
      @if (form['requiredDeal'].value === 'JV_Syndication') {
        <div class="section-header mt-4 mb-3">
          <h5>JV Syndication Details</h5>
        </div>

        <div class="row">
          <!-- DP Road -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="dpRoad" class="form-label">DP Road</label>
            <input type="text" class="form-control" id="dpRoad" formControlName="dpRoad"
              placeholder="Enter DP Road details">
          </div>

          <!-- Plot Shape -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="plotShape" class="form-label">Plot Shape</label>
            <input type="text" class="form-control" id="plotShape" formControlName="plotShape"
              placeholder="Enter plot shape">
          </div>

          <!-- JV Deposit Amount -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="jvDepositAmount" class="form-label">JV Deposit Amount</label>
            <input type="number" class="form-control" id="jvDepositAmount" formControlName="jvDepositAmount"
              placeholder="Enter deposit amount">
          </div>

          <!-- JV Deposit Type -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="jvDepositType" class="form-label">Deposit Type</label>
            <select class="form-select" id="jvDepositType" formControlName="jvDepositType">
              <option value="">Select Type</option>
              <option value="Refundable">Refundable</option>
              <option value="Non_Refundable">Non Refundable</option>
            </select>
          </div>
        </div>

        <div class="row">
          <!-- Onn Amount -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="onnAmount" class="form-label">Onn Amount</label>
            <input type="number" class="form-control" id="onnAmount" formControlName="onnAmount"
              placeholder="Enter Onn amount">
          </div>

          <!-- JV Ratio -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="jvRatio" class="form-label">JV Ratio</label>
            <select class="form-select" id="jvRatio" formControlName="jvRatio">
              <option value="">Select Ratio</option>
              <option value="Basic">Basic</option>
              <option value="On_Full_Potential">On Full Potential</option>
            </select>
          </div>

          <!-- Total Potential of Plot -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="totalPotentialOfPlot" class="form-label">Total Potential of Plot (Sq. ft)</label>
            <input type="number" class="form-control" id="totalPotentialOfPlot" formControlName="totalPotentialOfPlot"
              placeholder="Enter total potential">
          </div>

          <!-- Total Project Costing -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="totalProjectCosting" class="form-label">Total Project Costing</label>
            <input type="number" class="form-control" id="totalProjectCosting" formControlName="totalProjectCosting"
              placeholder="Enter project costing"
              (input)="onTotalProjectCostingChange()">
          </div>
        </div>

        <div class="row">
          <!-- Total Project Revenue -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="totalProjectRevenue" class="form-label">Total Project Revenue</label>
            <input type="number" class="form-control" id="totalProjectRevenue" formControlName="totalProjectRevenue"
              placeholder="Enter project revenue"
              (input)="onTotalProjectRevenueChange()">
          </div>

          <!-- Net Surplus (Auto Calculated) -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="netSurplus" class="form-label">Net Surplus</label>
            <input type="number" class="form-control" id="netSurplus" formControlName="netSurplus"
              placeholder="Auto calculated" readonly>
            <small class="form-text text-muted">Auto calculated (Revenue - Costing)</small>
          </div>

          <!-- Project Funding -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="projectFunding" class="form-label">Project Funding (Amount)</label>
            <input type="number" class="form-control" id="projectFunding" formControlName="projectFunding"
              placeholder="Enter funding amount">
          </div>
        </div>
      }

      <!-- Customer Information Section -->
      <div class="section-header mt-4 mb-3">
        <h5>Customer Information</h5>
      </div>

      <!-- Customer Entry Form -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="mb-0">Add Customer Details</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <!-- Customer Name -->
            <div class="col-md-4 mb-3">
              <label for="customerName" class="form-label">Customer Name</label>
              <input type="text" class="form-control" id="customerName"
                [(ngModel)]="currentCustomer.customerName"
                [ngModelOptions]="{standalone: true}"
                placeholder="Enter customer name">
            </div>

            <!-- Contact No -->
            <div class="col-md-4 mb-3">
              <label for="contactNo" class="form-label">Contact No</label>
              <input type="tel" class="form-control" id="contactNo"
                [(ngModel)]="currentCustomer.contactNo"
                [ngModelOptions]="{standalone: true}"
                placeholder="Enter contact number">
            </div>

            <!-- Residential Address -->
            <div class="col-md-4 mb-3">
              <label for="resiAddress" class="form-label">Residential Address</label>
              <textarea class="form-control" id="resiAddress" rows="2"
                [(ngModel)]="currentCustomer.resiAddress"
                [ngModelOptions]="{standalone: true}"
                placeholder="Enter complete residential address"></textarea>
            </div>
          </div>

          <!-- Add Customer Button -->
          <div class="row">
            <div class="col-12 text-end">
              <button type="button" class="btn btn-primary me-2" (click)="addCustomer()">
                <i class="feather" [ngClass]="getCustomerButtonIcon()"></i> {{ getCustomerButtonText() }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Added Customers List -->
      @if (customers.length > 0) {
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="mb-0">Added Customers ({{ customers.length }})</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Sr. No</th>
                    <th>Customer Name</th>
                    <th>Contact No</th>
                    <th>Residential Address</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  @for (customer of customers; track customer.id) {
                    <tr>
                      <td>{{ $index + 1 }}</td>
                      <td>{{ customer.customerName }}</td>
                      <td>{{ customer.contactNo }}</td>
                      <td>{{ customer.resiAddress }}</td>
                      <td>
                        <button type="button" class="btn btn-sm btn-outline-primary me-1"
                          (click)="editCustomer(customer)" title="Edit">
                          <i class="feather icon-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger"
                          (click)="removeCustomer(customer.id)" title="Delete">
                          <i class="feather icon-trash-2"></i>
                        </button>
                      </td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          </div>
        </div>
      }

      <!-- Submit Buttons -->
      <div class="row mt-4">
        <div class="col-12 text-end">
          <button type="submit" class="btn btn-primary me-2">Submit</button>
          <button type="button" class="btn btn-secondary me-2" (click)="resetForm()">Reset</button>
        </div>
      </div>

    </form>

  </div>
</div>
