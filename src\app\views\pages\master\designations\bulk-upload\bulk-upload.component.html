<!-- Bulk Upload Modal -->
<div class="modal-header">
  <h5 class="modal-title">
    <i class="feather icon-upload me-2"></i>
    Bulk Upload Designations
  </h5>
  <button type="button" class="btn-close" (click)="close()" aria-label="Close"></button>
</div>

<div class="modal-body">
  
  <!-- Instructions -->
  <div class="alert alert-info">
    <h6 class="alert-heading">
      <i class="feather icon-info me-1"></i>
      Upload Instructions
    </h6>
    <ul class="mb-0">
      <li>Download the template file and fill in your designation data</li>
      <li>Supported formats: Excel (.xlsx, .xls) or CSV files</li>
      <li>Maximum file size: 10MB</li>
      <li>Ensure all required fields are filled correctly</li>
    </ul>
  </div>

  <!-- Download Template -->
  <div class="mb-4">
    <button class="btn btn-outline-primary" (click)="downloadTemplate()">
      <i class="feather icon-download me-2"></i>
      Download Template
    </button>
    <small class="text-muted ms-2">
      Download the Excel template with sample data and required format
    </small>
  </div>

  <!-- File Upload Section -->
  <div class="upload-section">
    <h6 class="mb-3">
      <i class="feather icon-file me-1"></i>
      Select File to Upload
    </h6>

    <!-- File Input -->
    <div class="file-input-wrapper">
      <input 
        type="file" 
        id="fileInput"
        class="file-input"
        accept=".xlsx,.xls,.csv"
        (change)="onFileSelected($event)">
      
      <label for="fileInput" class="file-input-label">
        <div class="upload-area" [class.has-file]="selectedFile">
          <div *ngIf="!selectedFile" class="upload-placeholder">
            <i class="feather icon-upload-cloud upload-icon"></i>
            <h6>Click to select file or drag and drop</h6>
            <p class="text-muted">Excel (.xlsx, .xls) or CSV files only</p>
          </div>
          
          <div *ngIf="selectedFile" class="file-info">
            <i class="feather" [attr.icon]="getFileIcon(selectedFile)"></i>
            <div class="file-details">
              <h6 class="file-name">{{ selectedFile.name }}</h6>
              <p class="file-size text-muted">{{ getFileSize(selectedFile.size) }}</p>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" (click)="reset()">
              <i class="feather icon-x"></i>
            </button>
          </div>
        </div>
      </label>
    </div>
  </div>

  <!-- Error Display -->
  <div *ngIf="error" class="alert alert-danger mt-3">
    <i class="feather icon-alert-circle me-2"></i>
    {{ error }}
  </div>

  <!-- Upload Result -->
  <div *ngIf="uploadResult" class="mt-4">
    <div class="card" [class.border-success]="uploadResult.successful_imports > 0" 
         [class.border-warning]="uploadResult.failed_imports > 0 && uploadResult.successful_imports === 0">
      <div class="card-header" 
           [class.bg-success]="uploadResult.successful_imports > 0 && uploadResult.failed_imports === 0"
           [class.bg-warning]="uploadResult.failed_imports > 0"
           [class.text-white]="uploadResult.successful_imports > 0 || uploadResult.failed_imports > 0">
        <h6 class="mb-0">
          <i class="feather icon-check-circle me-1" *ngIf="uploadResult.successful_imports > 0 && uploadResult.failed_imports === 0"></i>
          <i class="feather icon-alert-triangle me-1" *ngIf="uploadResult.failed_imports > 0"></i>
          Upload Results
        </h6>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-4">
            <h4 class="text-primary">{{ uploadResult.total_processed }}</h4>
            <small class="text-muted">Total Processed</small>
          </div>
          <div class="col-md-4">
            <h4 class="text-success">{{ uploadResult.successful_imports }}</h4>
            <small class="text-muted">Successful</small>
          </div>
          <div class="col-md-4">
            <h4 class="text-danger">{{ uploadResult.failed_imports }}</h4>
            <small class="text-muted">Failed</small>
          </div>
        </div>

        <!-- Error Details -->
        <div *ngIf="uploadResult.errors && uploadResult.errors.length > 0" class="mt-3">
          <h6 class="text-danger">
            <i class="feather icon-alert-circle me-1"></i>
            Errors Found:
          </h6>
          <div class="error-list">
            <div *ngFor="let error of uploadResult.errors" class="error-item">
              <small class="text-danger">• {{ error }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<div class="modal-footer">
  <div class="d-flex justify-content-between w-100">
    <div>
      <button 
        type="button" 
        class="btn btn-outline-secondary"
        (click)="reset()"
        [disabled]="uploading || !selectedFile">
        <i class="feather icon-refresh-cw me-1"></i>
        Reset
      </button>
    </div>
    
    <div class="d-flex gap-2">
      <button 
        type="button" 
        class="btn btn-secondary" 
        (click)="close()"
        [disabled]="uploading">
        <i class="feather icon-x me-1"></i>
        Close
      </button>
      
      <button 
        type="button" 
        class="btn btn-primary" 
        (click)="upload()"
        [disabled]="!selectedFile || uploading">
        
        <!-- Loading Spinner -->
        <span *ngIf="uploading" class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Uploading...</span>
        </span>
        
        <!-- Upload Icon -->
        <i *ngIf="!uploading" class="feather icon-upload me-1"></i>
        
        {{ uploading ? 'Uploading...' : 'Upload File' }}
      </button>
    </div>
  </div>
</div>
