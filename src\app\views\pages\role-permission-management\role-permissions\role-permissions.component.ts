import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { RoleService } from '../../../../core/services/role.service';
import { Role, Permission } from '../../../../core/models/role.model';
import { catchError, debounceTime, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-role-permissions',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbTooltipModule
  ],
  templateUrl: './role-permissions.component.html',
  styleUrl: './role-permissions.component.scss'
})
export class RolePermissionsComponent implements OnInit {
  // Data
  selectedRole: Role | null = null;
  permissions: Permission[] = [];
  filteredPermissions: Permission[] = [];

  // Permission management
  selectedPermissions: Set<string> = new Set();
  originalPermissions: Set<string> = new Set();

  // Loading states
  loading = false;
  submitting = false;

  // Search and filter
  searchTerm = new FormControl('', { nonNullable: true });
  filterStatus: 'all' | 'assigned' | 'unassigned' = 'all';
  categoryFilter = 'all';
  viewMode: 'grid' | 'list' = 'grid';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private roleService: RoleService
  ) {}

  ngOnInit(): void {
    // Get role ID from query parameters
    this.route.queryParams.subscribe(params => {
      const roleId = params['roleId'];
      const roleName = params['roleName'];

      if (roleId) {
        this.loadRoleAndPermissions(roleId, roleName);
      } else {
        this.showErrorMessage('No role specified. Redirecting to roles page.');
        this.goBack();
      }
    });

    // Set up search
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.applyFilters();
    });
  }

  loadRoleAndPermissions(roleId: string, roleName?: string): void {
    this.loading = true;

    // Load role details, all permissions, and role permissions in parallel
    Promise.all([
      this.loadRoleDetails(roleId, roleName),
      this.loadAllPermissions(),
      this.loadRolePermissions(roleId)
    ]).finally(() => {
      this.loading = false;
      this.applyFilters();
    });
  }

  private async loadRoleDetails(roleId: string, roleName?: string): Promise<void> {
    try {
      const role = await this.roleService.getRole(roleId).toPromise();
      if (role) {
        this.selectedRole = role;
      } else if (roleName) {
        // Fallback: create a minimal role object if we have the name
        this.selectedRole = { id: roleId, name: roleName } as Role;
      }
    } catch (error) {
      console.error('Error loading role details:', error);
      if (roleName) {
        this.selectedRole = { id: roleId, name: roleName } as Role;
      }
    }
  }

  private async loadAllPermissions(): Promise<void> {
    try {
      console.log('🔄 RolePermissionsComponent: Starting to load all permissions...');

      const permissions = await this.roleService.getAllPermissions().toPromise() || [];

      console.log(`📊 RolePermissionsComponent: Received ${permissions.length} permissions from service`);
      console.log('🔍 RolePermissionsComponent: Permission data sample:', permissions.slice(0, 3));

      // Check for any filtering or transformation that might happen here
      this.permissions = permissions;

      console.log(`✅ RolePermissionsComponent: Final permissions array length: ${this.permissions.length}`);

      // Additional debugging: Check if there are any permissions with specific patterns
      const opsPermissions = this.permissions.filter(p => p.name.includes('ops'));
      const adminPermissions = this.permissions.filter(p => p.name.includes('admin'));
      const readPermissions = this.permissions.filter(p => p.name.includes('read'));

      console.log(`📈 Permission breakdown:
        - Ops-related: ${opsPermissions.length}
        - Admin-related: ${adminPermissions.length}
        - Read-related: ${readPermissions.length}
        - Total: ${this.permissions.length}`);

    } catch (error) {
      console.error('Error loading permissions:', error);
      this.showErrorMessage('Failed to load permissions.');
    }
  }

  private async loadRolePermissions(roleId: string): Promise<void> {
    try {
      const rolePermissions = await this.roleService.getRolePermissions(roleId).toPromise() || [];

      // Set both current and original permissions
      this.selectedPermissions.clear();
      this.originalPermissions.clear();

      rolePermissions.forEach(permission => {
        this.selectedPermissions.add(permission.id);
        this.originalPermissions.add(permission.id);
      });

      console.log('Loaded role permissions:', rolePermissions.length);
    } catch (error) {
      console.error('Error loading role permissions:', error);
      this.showErrorMessage('Failed to load role permissions.');
    }
  }

  applyFilters(): void {
    let filtered = [...this.permissions];

    // Apply search filter
    if (this.searchTerm.value) {
      const searchLower = this.searchTerm.value.toLowerCase();
      filtered = filtered.filter(permission =>
        permission.name.toLowerCase().includes(searchLower) ||
        (permission.description && permission.description.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (this.filterStatus === 'assigned') {
      filtered = filtered.filter(permission => this.selectedPermissions.has(permission.id));
    } else if (this.filterStatus === 'unassigned') {
      filtered = filtered.filter(permission => !this.selectedPermissions.has(permission.id));
    }

    this.filteredPermissions = filtered;
  }

  onPermissionChange(permissionId: string, event: Event): void {
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      this.selectedPermissions.add(permissionId);
    } else {
      this.selectedPermissions.delete(permissionId);
    }

    // Reapply filters to update the view
    this.applyFilters();
  }

  isPermissionSelected(permissionId: string): boolean {
    return this.selectedPermissions.has(permissionId);
  }

  toggleSelectAll(): void {
    if (this.areAllFilteredSelected()) {
      // Deselect all filtered permissions
      this.filteredPermissions.forEach(permission => {
        this.selectedPermissions.delete(permission.id);
      });
    } else {
      // Select all filtered permissions
      this.filteredPermissions.forEach(permission => {
        this.selectedPermissions.add(permission.id);
      });
    }
    this.applyFilters();
  }

  areAllFilteredSelected(): boolean {
    if (this.filteredPermissions.length === 0) {
      return false;
    }
    return this.filteredPermissions.every(permission =>
      this.selectedPermissions.has(permission.id)
    );
  }





  goBack(): void {
    this.router.navigate(['/role-permission-management/roles']);
  }

  showSuccessMessage(message: string): void {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
      timer: 2000,
      showConfirmButton: false
    });
  }

  showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message
    });
  }

  // New methods for enhanced UI
  hasChanges(): boolean {
    if (this.selectedPermissions.size !== this.originalPermissions.size) {
      return true;
    }

    for (const permission of this.selectedPermissions) {
      if (!this.originalPermissions.has(permission)) {
        return true;
      }
    }

    return false;
  }

  resetChanges(): void {
    this.selectedPermissions = new Set(this.originalPermissions);
    this.applyFilters();
  }

  getPendingChanges(): number {
    let changes = 0;

    // Count additions
    for (const permission of this.selectedPermissions) {
      if (!this.originalPermissions.has(permission)) {
        changes++;
      }
    }

    // Count removals
    for (const permission of this.originalPermissions) {
      if (!this.selectedPermissions.has(permission)) {
        changes++;
      }
    }

    return changes;
  }

  getCompletionPercentage(): number {
    if (this.permissions.length === 0) return 0;
    return Math.round((this.selectedPermissions.size / this.permissions.length) * 100);
  }

  savePermissions(): void {
    if (!this.selectedRole || !this.hasChanges()) {
      return;
    }

    this.submitting = true;
    const permissionIds = Array.from(this.selectedPermissions);

    this.roleService.updateRoleWithPermissions(this.selectedRole.id, this.selectedRole, permissionIds)
      .pipe(
        finalize(() => this.submitting = false)
      )
      .subscribe({
        next: () => {
          this.originalPermissions = new Set(this.selectedPermissions);
          this.showSuccessMessage('Permissions updated successfully');
        },
        error: (error) => {
          console.error('Error updating permissions:', error);
          this.showErrorMessage('Failed to update permissions. Please try again.');
        }
      });
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid';
  }

  getPermissionCategory(permissionName: string): string {
    const name = permissionName.toLowerCase();

    if (name.includes('user')) return 'users';
    if (name.includes('role') || name.includes('permission')) return 'roles';
    if (name.includes('sales') || name.includes('lead')) return 'sales';
    if (name.includes('employee') || name.includes('staff')) return 'employees';
    if (name.includes('master') || name.includes('config')) return 'master';
    if (name.includes('admin') || name.includes('system')) return 'admin';
    if (name.includes('report') || name.includes('analytics')) return 'reports';
    if (name.includes('leave') || name.includes('attendance')) return 'hr';

    return 'general';
  }

  getPermissionType(permissionName: string): string {
    const name = permissionName.toLowerCase();

    if (name.includes(':read') || name.includes(':view')) return 'Read';
    if (name.includes(':create') || name.includes(':add')) return 'Create';
    if (name.includes(':update') || name.includes(':edit')) return 'Update';
    if (name.includes(':delete') || name.includes(':remove')) return 'Delete';
    if (name.includes(':manage') || name.includes(':admin')) return 'Manage';
    if (name.includes(':approve')) return 'Approve';
    if (name.includes(':access')) return 'Access';

    return 'Action';
  }

  isSomeSelected(): boolean {
    return this.selectedPermissions.size > 0 && !this.areAllFilteredSelected();
  }
}
