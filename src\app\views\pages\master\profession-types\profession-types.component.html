<!-- Profession Types Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">

        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-briefcase me-2"></i>
              Profession Types Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_profession_types }} total profession types,
              {{ statistics.active_profession_types }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Profession Type
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'"
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Profession Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'"
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Profession Types
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'"
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">

          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..."
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCategory" (change)="onCategoryFilter()">
                <option value="">All Categories</option>
                <option *ngFor="let category of professionCategories" [value]="category.value">
                  {{ category.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedSkillLevel" (change)="onSkillLevelFilter()">
                <option value="">All Skill Levels</option>
                <option *ngFor="let level of skillLevels" [value]="level.value">
                  {{ level.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedEducationLevel" (change)="onEducationLevelFilter()">
                <option value="">All Education</option>
                <option *ngFor="let level of educationLevels" [value]="level.value">
                  {{ level.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedIndustrySector" (change)="onIndustrySectorFilter()">
                <option value="">All Industries</option>
                <option *ngFor="let sector of industrySectors" [value]="sector">
                  {{ sector }}
                </option>
              </select>
            </div>
          </div>

          <!-- Additional Filters Row -->
          <div class="row mb-3">
            <div class="col-md-3">
              <select class="form-select" [(ngModel)]="selectedCertificationRequired" (change)="onCertificationRequiredFilter()">
                <option value="all">All Certification</option>
                <option value="required">Certification Required</option>
                <option value="not_required">No Certification Required</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select" [(ngModel)]="selectedLicenseRequired" (change)="onLicenseRequiredFilter()">
                <option value="all">All License</option>
                <option value="required">License Required</option>
                <option value="not_required">No License Required</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading profession types...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Profession Details</th>
                  <th>Category & Level</th>
                  <th>Requirements</th>
                  <th>Experience & Salary</th>
                  <th>Work Preferences</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let professionType of getCurrentList(); trackBy: trackByProfessionTypeId">
                  <td>
                    <div>
                      <strong>{{ professionType.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ professionType.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="professionType.description">
                        {{ professionType.description }}
                      </small>
                      <small class="d-block text-muted" *ngIf="professionType.industry_sector">
                        <strong>Industry:</strong> {{ professionType.industry_sector }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ getProfessionCategoryLabel(professionType.category) }}</span>
                    <div class="mt-1" *ngIf="professionType.skill_level">
                      <span [class]="getSkillLevelBadgeClass(professionType.skill_level)">
                        {{ getSkillLevelLabel(professionType.skill_level) }}
                      </span>
                    </div>
                    <small class="d-block text-muted mt-1" *ngIf="professionType.education_level">
                      <strong>Education:</strong> {{ getEducationLevelLabel(professionType.education_level) }}
                    </small>
                  </td>
                  <td>
                    <div class="requirements-info">
                      <div *ngIf="professionType.certification_required !== undefined">
                        <span class="badge" [class]="professionType.certification_required ? 'bg-warning' : 'bg-light text-dark'">
                          <i class="feather icon-award me-1"></i>
                          {{ professionType.certification_required ? 'Certification Required' : 'No Certification' }}
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="professionType.license_required !== undefined">
                        <span class="badge" [class]="professionType.license_required ? 'bg-warning' : 'bg-light text-dark'">
                          <i class="feather icon-file-text me-1"></i>
                          {{ professionType.license_required ? 'License Required' : 'No License' }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="experience-salary-info">
                      <small class="d-block text-muted">
                        <strong>Experience:</strong> {{ formatExperienceRange(professionType) }}
                      </small>
                      <small class="d-block text-muted mt-1">
                        <strong>Salary:</strong> {{ formatSalaryRange(professionType) }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="work-preferences">
                      <div *ngIf="professionType.remote_work_eligible !== undefined">
                        <span class="badge" [class]="professionType.remote_work_eligible ? 'bg-success' : 'bg-light text-dark'">
                          <i class="feather icon-home me-1"></i>
                          {{ professionType.remote_work_eligible ? 'Remote OK' : 'On-site' }}
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="professionType.travel_required !== undefined">
                        <span class="badge" [class]="professionType.travel_required ? 'bg-info' : 'bg-light text-dark'">
                          <i class="feather icon-map-pin me-1"></i>
                          {{ professionType.travel_required ? 'Travel Required' : 'No Travel' }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(professionType.is_active)">
                      {{ getStatusText(professionType.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ professionType.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(professionType)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteProfessionType(professionType)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreProfessionType(professionType)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-briefcase text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Profession Types' : 'No Profession Types Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No profession types have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No profession types match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first profession type.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Profession Type
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} profession types
            </div>
            <ngb-pagination
              [(page)]="currentPage"
              [pageSize]="pageSize"
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_profession_types }}</h3>
                      <p class="mb-0">Total Profession Types</p>
                    </div>
                    <i class="feather icon-briefcase" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_profession_types }}</h3>
                      <p class="mb-0">Active Profession Types</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-secondary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_profession_types }}</h3>
                      <p class="mb-0">Inactive Profession Types</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Profession Types by Category -->
          <div *ngIf="statistics.profession_types_by_category" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Profession Types by Category</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.profession_types_by_category)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getProfessionCategoryLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-primary fs-6">{{ statistics.profession_types_by_category[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Profession Types by Skill Level -->
          <div *ngIf="statistics.profession_types_by_skill_level" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Profession Types by Skill Level</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.profession_types_by_skill_level)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getSkillLevelLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span [class]="getSkillLevelBadgeClass(key) + ' fs-6'">{{ statistics.profession_types_by_skill_level[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Profession Types by Education Level -->
          <div *ngIf="statistics.profession_types_by_education" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Profession Types by Education Level</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.profession_types_by_education)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getEducationLevelLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-info fs-6">{{ statistics.profession_types_by_education[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Profession Types -->
          <div *ngIf="statistics.popular_profession_types?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Popular Profession Types</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Profession Type</th>
                      <th>Category</th>
                      <th>Skill Level</th>
                      <th>Industry</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let professionType of statistics.popular_profession_types">
                      <td>
                        <strong>{{ professionType.name }}</strong>
                        <small class="d-block text-muted">{{ professionType.code }}</small>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ getProfessionCategoryLabel(professionType.category) }}</span>
                      </td>
                      <td>
                        <span *ngIf="professionType.skill_level" [class]="getSkillLevelBadgeClass(professionType.skill_level)">
                          {{ getSkillLevelLabel(professionType.skill_level) }}
                        </span>
                        <span *ngIf="!professionType.skill_level" class="text-muted">-</span>
                      </td>
                      <td>
                        {{ professionType.industry_sector || 'Not specified' }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
