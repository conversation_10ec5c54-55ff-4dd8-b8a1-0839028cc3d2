import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';

export interface AuditLogEntry {
  id?: string;
  timestamp: Date;
  userId: string;
  userEmail: string;
  action: string;
  resource: string;
  permissions: {
    required: string[];
    user: string[];
    granted: boolean;
  };
  context: {
    route?: string;
    component?: string;
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
  };
  result: 'GRANTED' | 'DENIED' | 'ERROR';
  errorMessage?: string;
  metadata?: { [key: string]: any };
}

export interface AuditStatistics {
  totalChecks: number;
  grantedChecks: number;
  deniedChecks: number;
  errorChecks: number;
  uniqueUsers: number;
  topDeniedPermissions: { permission: string; count: number }[];
  recentActivity: AuditLogEntry[];
}

@Injectable({
  providedIn: 'root'
})
export class PermissionAuditService {
  private readonly baseUrl = environment.apiUrl;
  private auditBuffer: AuditLogEntry[] = [];
  private readonly maxBufferSize = 100;
  private readonly flushInterval = 30000; // 30 seconds
  
  private statisticsSubject = new BehaviorSubject<AuditStatistics | null>(null);
  public statistics$ = this.statisticsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    this.initializePeriodicFlush();
  }

  /**
   * Log a permission check
   */
  logPermissionCheck(
    action: string,
    resource: string,
    requiredPermissions: string[],
    userPermissions: string[],
    granted: boolean,
    context?: {
      route?: string;
      component?: string;
      errorMessage?: string;
      metadata?: { [key: string]: any };
    }
  ): void {
    const user = this.authService.currentUserValue;
    if (!user) return;

    const entry: AuditLogEntry = {
      timestamp: new Date(),
      userId: user.id,
      userEmail: user.email,
      action,
      resource,
      permissions: {
        required: requiredPermissions,
        user: userPermissions,
        granted
      },
      context: {
        route: context?.route || this.getCurrentRoute(),
        component: context?.component,
        userAgent: navigator.userAgent,
        ipAddress: 'client-side', // Would be set by server in real implementation
        sessionId: this.getSessionId()
      },
      result: context?.errorMessage ? 'ERROR' : (granted ? 'GRANTED' : 'DENIED'),
      errorMessage: context?.errorMessage,
      metadata: context?.metadata
    };

    // Add to buffer
    this.auditBuffer.push(entry);

    // Log to console for development
    this.logToConsole(entry);

    // Flush if buffer is full
    if (this.auditBuffer.length >= this.maxBufferSize) {
      this.flushAuditBuffer();
    }
  }

  /**
   * Log API access attempt
   */
  logApiAccess(
    endpoint: string,
    method: string,
    requiredPermissions: string[],
    granted: boolean,
    statusCode?: number,
    errorMessage?: string
  ): void {
    this.logPermissionCheck(
      `API_${method}`,
      endpoint,
      requiredPermissions,
      this.authService.currentUserValue?.permissions || [],
      granted,
      {
        metadata: {
          httpMethod: method,
          statusCode,
          endpoint
        },
        errorMessage
      }
    );
  }

  /**
   * Log route access attempt
   */
  logRouteAccess(
    route: string,
    requiredPermissions: string[],
    granted: boolean,
    redirectTo?: string
  ): void {
    this.logPermissionCheck(
      'ROUTE_ACCESS',
      route,
      requiredPermissions,
      this.authService.currentUserValue?.permissions || [],
      granted,
      {
        route,
        metadata: {
          redirectTo
        }
      }
    );
  }

  /**
   * Log component access attempt
   */
  logComponentAccess(
    componentName: string,
    requiredPermissions: string[],
    granted: boolean,
    action: string = 'VIEW'
  ): void {
    this.logPermissionCheck(
      `COMPONENT_${action}`,
      componentName,
      requiredPermissions,
      this.authService.currentUserValue?.permissions || [],
      granted,
      {
        component: componentName
      }
    );
  }

  /**
   * Log security event
   */
  logSecurityEvent(
    eventType: string,
    description: string,
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
    metadata?: { [key: string]: any }
  ): void {
    this.logPermissionCheck(
      'SECURITY_EVENT',
      eventType,
      [],
      [],
      false,
      {
        errorMessage: description,
        metadata: {
          ...metadata,
          severity,
          eventType
        }
      }
    );
  }

  /**
   * Get audit statistics
   */
  getStatistics(): Observable<AuditStatistics> {
    // In a real implementation, this would fetch from the server
    // For now, we'll calculate from local buffer
    const stats = this.calculateLocalStatistics();
    this.statisticsSubject.next(stats);
    return of(stats);
  }

  /**
   * Flush audit buffer to server
   */
  private flushAuditBuffer(): void {
    if (this.auditBuffer.length === 0) return;

    const entries = [...this.auditBuffer];
    this.auditBuffer = [];

    // In a real implementation, send to audit API
    this.sendToAuditAPI(entries).subscribe({
      next: () => {
        console.log(`✅ Flushed ${entries.length} audit entries to server`);
      },
      error: (error) => {
        console.error('❌ Failed to flush audit entries:', error);
        // Re-add entries to buffer for retry
        this.auditBuffer.unshift(...entries);
      }
    });
  }

  /**
   * Send audit entries to API
   */
  private sendToAuditAPI(entries: AuditLogEntry[]): Observable<any> {
    // This would be the actual API call in a real implementation
    // For now, we'll simulate it
    console.log('📋 Audit entries to be sent:', entries);
    
    // Simulate API call
    return of({ success: true }).pipe(
      tap(() => {
        // Update statistics after successful flush
        this.getStatistics().subscribe();
      }),
      catchError(error => {
        console.error('Audit API error:', error);
        return of({ success: false, error });
      })
    );
  }

  /**
   * Calculate statistics from local buffer
   */
  private calculateLocalStatistics(): AuditStatistics {
    const totalChecks = this.auditBuffer.length;
    const grantedChecks = this.auditBuffer.filter(e => e.result === 'GRANTED').length;
    const deniedChecks = this.auditBuffer.filter(e => e.result === 'DENIED').length;
    const errorChecks = this.auditBuffer.filter(e => e.result === 'ERROR').length;
    
    const uniqueUsers = new Set(this.auditBuffer.map(e => e.userId)).size;
    
    // Calculate top denied permissions
    const deniedPermissions: { [key: string]: number } = {};
    this.auditBuffer
      .filter(e => e.result === 'DENIED')
      .forEach(e => {
        e.permissions.required.forEach(perm => {
          deniedPermissions[perm] = (deniedPermissions[perm] || 0) + 1;
        });
      });
    
    const topDeniedPermissions = Object.entries(deniedPermissions)
      .map(([permission, count]) => ({ permission, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const recentActivity = this.auditBuffer
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 20);

    return {
      totalChecks,
      grantedChecks,
      deniedChecks,
      errorChecks,
      uniqueUsers,
      topDeniedPermissions,
      recentActivity
    };
  }

  /**
   * Log to console for development
   */
  private logToConsole(entry: AuditLogEntry): void {
    const emoji = entry.result === 'GRANTED' ? '✅' : entry.result === 'DENIED' ? '❌' : '⚠️';
    const color = entry.result === 'GRANTED' ? 'color: green' : entry.result === 'DENIED' ? 'color: red' : 'color: orange';
    
    console.group(`%c${emoji} Permission Audit: ${entry.action}`, color);
    console.log('Resource:', entry.resource);
    console.log('User:', entry.userEmail);
    console.log('Required Permissions:', entry.permissions.required);
    console.log('User Permissions:', entry.permissions.user);
    console.log('Result:', entry.result);
    if (entry.context.route) console.log('Route:', entry.context.route);
    if (entry.context.component) console.log('Component:', entry.context.component);
    if (entry.errorMessage) console.log('Error:', entry.errorMessage);
    if (entry.metadata) console.log('Metadata:', entry.metadata);
    console.groupEnd();
  }

  /**
   * Initialize periodic flush
   */
  private initializePeriodicFlush(): void {
    setInterval(() => {
      this.flushAuditBuffer();
    }, this.flushInterval);
  }

  /**
   * Get current route
   */
  private getCurrentRoute(): string {
    return window.location.pathname;
  }

  /**
   * Get session ID
   */
  private getSessionId(): string {
    // In a real implementation, this would be a proper session ID
    return 'session_' + Date.now();
  }

  /**
   * Force flush audit buffer
   */
  forceFlush(): void {
    this.flushAuditBuffer();
  }

  /**
   * Clear audit buffer (for testing)
   */
  clearBuffer(): void {
    this.auditBuffer = [];
  }
}
