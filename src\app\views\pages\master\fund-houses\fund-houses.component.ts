import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  FundHouseService,
  FundHouse
} from '../../../../core/services/fund-house.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { FundHouseFormComponent } from './fund-house-form/fund-house-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-fund-houses',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    FundHouseFormComponent,
    BulkUploadComponent
  ],
  templateUrl: './fund-houses.component.html',
  styleUrls: ['./fund-houses.component.scss']
})
export class FundHousesComponent implements OnInit {
  // Data properties
  fundHouses: FundHouse[] = [];
  deletedFundHouses: FundHouse[] = [];

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedCountry = '';
  selectedRegulatoryBody = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedFundHouses: Set<string> = new Set();
  selectAll = false;

  // Filter options
  countries: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private fundHouseService: FundHouseService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadFundHouses();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.countries = this.fundHouseService.getCountryList();
    this.regulatoryBodies = this.fundHouseService.getRegulatoryBodies();
  }

  /**
   * Load fund houses with current filters
   */
  loadFundHouses(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      country: this.selectedCountry || undefined,
      regulatory_body: this.selectedRegulatoryBody || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.fundHouseService.getFundHouses(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedFundHouses = response.data.filter(fh => fh.deleted_at);
            this.fundHouses = [];
          } else {
            this.fundHouses = response.data.filter(fh => !fh.deleted_at);
            this.deletedFundHouses = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load fund houses';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load fund houses. Please try again.'
        });
      }
    });
  }

  /**
   * Search fund houses
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadFundHouses();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadFundHouses();
  }

  /**
   * Filter by country
   */
  onCountryFilter(): void {
    this.currentPage = 1;
    this.loadFundHouses();
  }

  /**
   * Filter by regulatory body
   */
  onRegulatoryBodyFilter(): void {
    this.currentPage = 1;
    this.loadFundHouses();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedFundHouses.clear();
    this.selectAll = false;
    this.loadFundHouses();
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadFundHouses();
  }

  /**
   * Open create fund house modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(FundHouseFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadFundHouses();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Fund house created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit fund house modal
   */
  openEditModal(fundHouse: FundHouse): void {
    const modalRef = this.modalService.open(FundHouseFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.fundHouse = { ...fundHouse };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadFundHouses();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Fund house updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete fund house
   */
  deleteFundHouse(fundHouse: FundHouse): void {
    this.popupService.showConfirmation({
      title: 'Delete Fund House',
      message: `Are you sure you want to delete "${fundHouse.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.fundHouseService.deleteFundHouse(fundHouse.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadFundHouses();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Fund house deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete fund house.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore fund house
   */
  restoreFundHouse(fundHouse: FundHouse): void {
    this.popupService.showConfirmation({
      title: 'Restore Fund House',
      message: `Are you sure you want to restore "${fundHouse.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.fundHouseService.restoreFundHouse(fundHouse.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadFundHouses();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Fund house restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore fund house.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadFundHouses();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Fund houses uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.fundHouseService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'fund_houses_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Toggle fund house selection
   */
  toggleSelection(fundHouseId: string): void {
    if (this.selectedFundHouses.has(fundHouseId)) {
      this.selectedFundHouses.delete(fundHouseId);
    } else {
      this.selectedFundHouses.add(fundHouseId);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    const currentList = this.viewMode === 'deleted' ? this.deletedFundHouses : this.fundHouses;

    if (this.selectAll) {
      this.selectedFundHouses.clear();
    } else {
      currentList.forEach(fundHouse => this.selectedFundHouses.add(fundHouse.id));
    }
    this.selectAll = !this.selectAll;
  }

  /**
   * Update select all state
   */
  private updateSelectAllState(): void {
    const currentList = this.viewMode === 'deleted' ? this.deletedFundHouses : this.fundHouses;
    this.selectAll = currentList.length > 0 &&
      currentList.every(fundHouse => this.selectedFundHouses.has(fundHouse.id));
  }

  /**
   * Bulk delete selected fund houses
   */
  bulkDelete(): void {
    if (this.selectedFundHouses.size === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select fund houses to delete.'
      });
      return;
    }

    this.popupService.showConfirmation({
      title: 'Bulk Delete',
      message: `Are you sure you want to delete ${this.selectedFundHouses.size} selected fund houses?`,
      confirmText: 'Yes, Delete All',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.popupService.showInfo({
          title: 'Feature Coming Soon',
          message: 'Bulk delete functionality will be implemented in the next update.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadFundHouses();
  }

  /**
   * Format AUM
   */
  formatAUM(amount?: number): string {
    return this.fundHouseService.formatAUM(amount);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): FundHouse[] {
    return this.viewMode === 'deleted' ? this.deletedFundHouses : this.fundHouses;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByFundHouseId(index: number, fundHouse: FundHouse): string {
    return fundHouse.id;
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
