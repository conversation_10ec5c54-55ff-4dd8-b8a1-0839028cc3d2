import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const authGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  try {
    console.log('🛡️ Auth guard checking route:', state.url);
    const currentUser = authService.currentUserValue;
    console.log('👤 Current user in auth guard:', currentUser ? 'Logged in' : 'Not logged in');

    if (currentUser) {
      // Check if route requires specific permissions (API-based only)
      const requiredPermissions = route.data['permissions'] as string[];
      if (requiredPermissions && requiredPermissions.length > 0) {
        console.log('🔍 Checking required permissions:', requiredPermissions);
        console.log('🔑 User permissions:', currentUser.permissions);

        const hasPermission = requiredPermissions.some(permission =>
          authService.hasPermission(permission)
        );

        if (!hasPermission) {
          console.log('❌ Missing required permissions, redirecting to dashboard');
          router.navigate(['/lms/dashboard']);
          return false;
        }

        console.log('✅ User has required permissions');
      }

      // Authorized
      console.log('✅ Route authorized');
      return true;
    }

    // Not logged in, redirect to login
    console.log('❌ Not logged in, redirecting to login');
    router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
    return false;
  } catch (error) {
    console.error('❌ Error in auth guard:', error);
    router.navigate(['/auth/login']);
    return false;
  }
};



// import { inject } from '@angular/core';
// import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
// import { AuthService } from '../services/auth.service';

// export const authGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
//   // Development bypass: always return true to skip authentication
//   console.log('Auth guard bypassed - allowing access to:', state.url);
//   return true;
// };