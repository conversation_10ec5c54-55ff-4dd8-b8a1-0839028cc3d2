import { Injectable } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, debounceTime } from 'rxjs/operators';
import { ProgressiveLoadingService } from './progressive-loading.service';

export interface PreloadRule {
  fromRoute: string;
  toRoutes: string[];
  probability: number;
  delay?: number;
}

export interface ComponentPreloadConfig {
  route: string;
  componentLoader: () => Promise<any>;
  priority: 'high' | 'medium' | 'low';
  dependencies?: string[];
}

/**
 * Component Preloader Service
 * 
 * Intelligently preloads components based on user navigation patterns,
 * route relationships, and usage analytics to improve perceived performance.
 */
@Injectable({
  providedIn: 'root'
})
export class ComponentPreloaderService {
  private navigationHistory: string[] = [];
  private preloadRules: PreloadRule[] = [];
  private componentConfigs = new Map<string, ComponentPreloadConfig>();
  private preloadedRoutes = new Set<string>();

  // Default preload rules based on common navigation patterns
  private defaultPreloadRules: PreloadRule[] = [
    // Dashboard to LMS navigation
    {
      fromRoute: '/dashboard',
      toRoutes: ['/lms', '/lms/dashboard'],
      probability: 0.7,
      delay: 2000
    },
    // LMS dashboard to apply leave
    {
      fromRoute: '/lms/dashboard',
      toRoutes: ['/lms/apply-leave', '/lms/my-leaves'],
      probability: 0.8,
      delay: 1000
    },
    // Employee management flows
    {
      fromRoute: '/employees',
      toRoutes: ['/employees/add', '/employees/edit'],
      probability: 0.6,
      delay: 1500
    },
    // Master data management
    {
      fromRoute: '/master',
      toRoutes: ['/master/private-banks', '/master/nbfcs', '/master/institutes'],
      probability: 0.5,
      delay: 2000
    },
    // Apps to specific app components
    {
      fromRoute: '/apps',
      toRoutes: ['/apps/calendar', '/apps/email', '/apps/chat'],
      probability: 0.6,
      delay: 1000
    }
  ];

  constructor(
    private router: Router,
    private progressiveLoadingService: ProgressiveLoadingService
  ) {
    this.initializePreloadRules();
    this.setupNavigationTracking();
    console.log('🎯 ComponentPreloaderService: Initialized with intelligent preloading');
  }

  /**
   * Register a component for preloading
   */
  registerComponent(config: ComponentPreloadConfig): void {
    this.componentConfigs.set(config.route, config);
    console.log(`📋 ComponentPreloaderService: Registered ${config.route} for preloading`);
  }

  /**
   * Add a custom preload rule
   */
  addPreloadRule(rule: PreloadRule): void {
    this.preloadRules.push(rule);
    console.log(`📏 ComponentPreloaderService: Added preload rule from ${rule.fromRoute}`);
  }

  /**
   * Preload components based on current route
   */
  preloadForRoute(currentRoute: string): void {
    const applicableRules = this.preloadRules.filter(rule => 
      this.routeMatches(currentRoute, rule.fromRoute)
    );

    applicableRules.forEach(rule => {
      rule.toRoutes.forEach(targetRoute => {
        if (Math.random() < rule.probability && !this.preloadedRoutes.has(targetRoute)) {
          this.schedulePreload(targetRoute, rule.delay || 1000);
        }
      });
    });
  }

  /**
   * Preload critical components immediately
   */
  preloadCriticalComponents(): void {
    const criticalComponents = Array.from(this.componentConfigs.values())
      .filter(config => config.priority === 'high')
      .sort((a, b) => a.route.localeCompare(b.route));

    console.log(`🚀 ComponentPreloaderService: Preloading ${criticalComponents.length} critical components`);

    criticalComponents.forEach((config, index) => {
      // Stagger critical component loading
      setTimeout(() => {
        this.preloadComponent(config);
      }, index * 200);
    });
  }

  /**
   * Preload components based on user idle time
   */
  preloadOnIdle(): void {
    let idleTimer: number;
    const idleThreshold = 3000; // 3 seconds of inactivity

    const resetIdleTimer = () => {
      clearTimeout(idleTimer);
      idleTimer = window.setTimeout(() => {
        this.preloadLowPriorityComponents();
      }, idleThreshold);
    };

    // Track user activity
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      document.addEventListener(event, resetIdleTimer, true);
    });

    resetIdleTimer();
  }

  /**
   * Preload components based on network conditions
   */
  preloadBasedOnNetwork(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      
      if (connection) {
        const effectiveType = connection.effectiveType;
        
        // Adjust preloading strategy based on network speed
        if (effectiveType === '4g') {
          this.preloadAggressively();
        } else if (effectiveType === '3g') {
          this.preloadModerately();
        } else {
          this.preloadConservatively();
        }
        
        console.log(`📶 ComponentPreloaderService: Adjusted preloading for ${effectiveType} network`);
      }
    } else {
      // Default to moderate preloading if network info unavailable
      this.preloadModerately();
    }
  }

  /**
   * Get preloading statistics
   */
  getPreloadStats(): {
    totalRegistered: number;
    totalPreloaded: number;
    preloadRules: number;
    navigationHistory: string[];
  } {
    return {
      totalRegistered: this.componentConfigs.size,
      totalPreloaded: this.preloadedRoutes.size,
      preloadRules: this.preloadRules.length,
      navigationHistory: [...this.navigationHistory]
    };
  }

  /**
   * Initialize default preload rules
   */
  private initializePreloadRules(): void {
    this.preloadRules = [...this.defaultPreloadRules];
    console.log(`📏 ComponentPreloaderService: Initialized with ${this.preloadRules.length} default rules`);
  }

  /**
   * Setup navigation tracking
   */
  private setupNavigationTracking(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        debounceTime(100) // Debounce rapid navigation changes
      )
      .subscribe((event: NavigationEnd) => {
        this.trackNavigation(event.url);
        this.preloadForRoute(event.url);
      });
  }

  /**
   * Track navigation for pattern analysis
   */
  private trackNavigation(url: string): void {
    this.navigationHistory.push(url);
    
    // Keep only last 20 navigations
    if (this.navigationHistory.length > 20) {
      this.navigationHistory.shift();
    }

    console.log(`🧭 ComponentPreloaderService: Tracked navigation to ${url}`);
  }

  /**
   * Schedule component preload with delay
   */
  private schedulePreload(route: string, delay: number): void {
    setTimeout(() => {
      const config = this.componentConfigs.get(route);
      if (config && !this.preloadedRoutes.has(route)) {
        this.preloadComponent(config);
      }
    }, delay);
  }

  /**
   * Preload a specific component
   */
  private preloadComponent(config: ComponentPreloadConfig): void {
    if (this.preloadedRoutes.has(config.route)) {
      return;
    }

    console.log(`🔮 ComponentPreloaderService: Preloading ${config.route}`);
    
    this.progressiveLoadingService.registerComponent(
      config.route,
      config.componentLoader,
      { 
        priority: config.priority,
        preload: true
      }
    );

    this.preloadedRoutes.add(config.route);
  }

  /**
   * Check if route matches pattern
   */
  private routeMatches(currentRoute: string, pattern: string): boolean {
    // Simple pattern matching - can be enhanced with regex
    return currentRoute.startsWith(pattern) || currentRoute === pattern;
  }

  /**
   * Aggressive preloading strategy (fast network)
   */
  private preloadAggressively(): void {
    const allComponents = Array.from(this.componentConfigs.values());
    allComponents.forEach((config, index) => {
      setTimeout(() => this.preloadComponent(config), index * 100);
    });
  }

  /**
   * Moderate preloading strategy (medium network)
   */
  private preloadModerately(): void {
    const highAndMediumPriority = Array.from(this.componentConfigs.values())
      .filter(config => config.priority === 'high' || config.priority === 'medium');
    
    highAndMediumPriority.forEach((config, index) => {
      setTimeout(() => this.preloadComponent(config), index * 300);
    });
  }

  /**
   * Conservative preloading strategy (slow network)
   */
  private preloadConservatively(): void {
    const highPriorityOnly = Array.from(this.componentConfigs.values())
      .filter(config => config.priority === 'high');
    
    highPriorityOnly.forEach((config, index) => {
      setTimeout(() => this.preloadComponent(config), index * 1000);
    });
  }

  /**
   * Preload low priority components during idle time
   */
  private preloadLowPriorityComponents(): void {
    const lowPriorityComponents = Array.from(this.componentConfigs.values())
      .filter(config => config.priority === 'low' && !this.preloadedRoutes.has(config.route));

    console.log(`😴 ComponentPreloaderService: Preloading ${lowPriorityComponents.length} low priority components during idle time`);

    lowPriorityComponents.forEach((config, index) => {
      setTimeout(() => this.preloadComponent(config), index * 500);
    });
  }
}
