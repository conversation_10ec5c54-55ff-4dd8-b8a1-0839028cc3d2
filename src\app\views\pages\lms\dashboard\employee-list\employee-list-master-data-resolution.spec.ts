import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { EmployeeListComponent } from './employee-list.component';
import { EmployeeService } from '../../../../../core/services/employee.service';
import { AuthService } from '../../../../../core/services/auth.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder } from '@angular/forms';

describe('EmployeeListComponent - Master Data Resolution', () => {
  let component: EmployeeListComponent;
  let fixture: ComponentFixture<EmployeeListComponent>;
  let employeeService: jasmine.SpyObj<EmployeeService>;
  let authService: jasmine.SpyObj<AuthService>;

  // Mock master data responses
  const mockDepartments = [
    { id: '46962f48-e727-40bf-9eb2-1bfe6fb8e6d2', name: 'Engineering' },
    { id: '12345678-1234-1234-1234-123456789012', name: 'Human Resources' },
    { id: '87654321-4321-4321-4321-210987654321', name: 'Finance' }
  ];

  const mockDesignations = [
    { id: '1237af0a-7ed3-491b-9195-1a6f7c62540a', name: 'Software Developer' },
    { id: '98765432-8765-8765-8765-987654321098', name: 'Senior Developer' },
    { id: '11111111-1111-1111-1111-111111111111', name: 'Team Lead' }
  ];

  const mockRoles = [
    { id: '3e715253-2211-4c3b-9b51-3027b555f239', name: 'Employee' },
    { id: 'a1b2c3d4-5678-9012-3456-789012345678', name: 'Admin' },
    { id: 'f9e8d7c6-b5a4-9384-7261-504938271650', name: 'Manager' }
  ];

  // Mock employee API response with IDs
  const mockEmployeeApiResponse = {
    success: true,
    data: [
      {
        id: '1910042b-3036-40cd-97cd-d2471b5de402',
        employee_code: 'Bizz-1004',
        first_name: 'Sushant',
        middle_name: 'Dattatray',
        last_name: 'Said',
        department_id: '46962f48-e727-40bf-9eb2-1bfe6fb8e6d2', // Engineering
        designation_id: '1237af0a-7ed3-491b-9195-1a6f7c62540a', // Software Developer
        sub_role_id: '3e715253-2211-4c3b-9b51-3027b555f239', // Employee
        role: 'U',
        office_email: '<EMAIL>',
        phone_no: '9987043219',
        joining_date: '2022-08-23',
        is_active: true
      },
      {
        id: '2910042b-3036-40cd-97cd-d2471b5de403',
        employee_code: 'Bizz-1005',
        first_name: 'John',
        last_name: 'Doe',
        department_id: '12345678-1234-1234-1234-123456789012', // Human Resources
        designation_id: '98765432-8765-8765-8765-987654321098', // Senior Developer
        sub_role_id: 'a1b2c3d4-5678-9012-3456-789012345678', // Admin
        role: 'A',
        office_email: '<EMAIL>',
        phone_no: '9876543210',
        joining_date: '2023-01-15',
        is_active: true
      },
      {
        id: '3910042b-3036-40cd-97cd-d2471b5de404',
        employee_code: 'Bizz-1006',
        first_name: 'Jane',
        last_name: 'Smith',
        department_id: null, // No department assigned
        designation_id: null, // No designation assigned
        sub_role_id: null, // No sub role assigned
        role: 'U',
        office_email: '<EMAIL>',
        phone_no: '9123456789',
        joining_date: '2023-06-01',
        is_active: true
      }
    ]
  };

  beforeEach(async () => {
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees',
      'getDepartmentsMasterData',
      'getDesignationsMasterData',
      'getRoles',
      'updateEmployee'
    ]);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'hasPermission',
      'getUserPermissions'
    ]);
    const modalServiceSpy = jasmine.createSpyObj('NgbModal', ['open', 'dismissAll']);

    await TestBed.configureTestingModule({
      imports: [EmployeeListComponent],
      providers: [
        FormBuilder,
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: NgbModal, useValue: modalServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EmployeeListComponent);
    component = fixture.componentInstance;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default mocks
    employeeService.getAllEmployees.and.returnValue(of(mockEmployeeApiResponse));
    employeeService.getDepartmentsMasterData.and.returnValue(of(mockDepartments));
    employeeService.getDesignationsMasterData.and.returnValue(of(mockDesignations));
    employeeService.getRoles.and.returnValue(of(mockRoles));
    authService.hasPermission.and.returnValue(true);
    authService.getUserPermissions.and.returnValue(['employees:update']);
  });

  describe('Master Data Loading and Caching', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should load and cache departments master data', () => {
      expect(employeeService.getDepartmentsMasterData).toHaveBeenCalled();
      expect(component.departmentsCache.size).toBe(3);
      expect(component.departmentsCache.get('46962f48-e727-40bf-9eb2-1bfe6fb8e6d2')).toBe('Engineering');
      expect(component.departmentsCache.get('12345678-1234-1234-1234-123456789012')).toBe('Human Resources');
      expect(component.departmentsCache.get('87654321-4321-4321-4321-210987654321')).toBe('Finance');
    });

    it('should load and cache designations master data', () => {
      expect(employeeService.getDesignationsMasterData).toHaveBeenCalled();
      expect(component.designationsCache.size).toBe(3);
      expect(component.designationsCache.get('1237af0a-7ed3-491b-9195-1a6f7c62540a')).toBe('Software Developer');
      expect(component.designationsCache.get('98765432-8765-8765-8765-987654321098')).toBe('Senior Developer');
      expect(component.designationsCache.get('11111111-1111-1111-1111-111111111111')).toBe('Team Lead');
    });

    it('should load and cache roles master data', () => {
      expect(employeeService.getRoles).toHaveBeenCalled();
      expect(component.rolesCache.size).toBe(3);
      expect(component.rolesCache.get('3e715253-2211-4c3b-9b51-3027b555f239')).toBe('Employee');
      expect(component.rolesCache.get('a1b2c3d4-5678-9012-3456-789012345678')).toBe('Admin');
      expect(component.rolesCache.get('f9e8d7c6-b5a4-9384-7261-504938271650')).toBe('Manager');
    });

    it('should load employees after master data is loaded', () => {
      expect(employeeService.getAllEmployees).toHaveBeenCalled();
      expect(component.employees.length).toBe(3);
    });
  });

  describe('Department and Designation Resolution', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should resolve department names from IDs', () => {
      const employees = component.employees;
      
      // First employee - Engineering department
      expect(employees[0].department).toBe('Engineering');
      
      // Second employee - Human Resources department
      expect(employees[1].department).toBe('Human Resources');
      
      // Third employee - No department assigned
      expect(employees[2].department).toBe('Not Assigned');
    });

    it('should resolve designation names from IDs', () => {
      const employees = component.employees;
      
      // First employee - Software Developer designation
      expect(employees[0].position).toBe('Software Developer');
      
      // Second employee - Senior Developer designation
      expect(employees[1].position).toBe('Senior Developer');
      
      // Third employee - No designation assigned
      expect(employees[2].position).toBe('Not Assigned');
    });

    it('should resolve sub role names from IDs', () => {
      const employees = component.employees;

      // First employee - Employee sub role
      expect(employees[0].subRole).toBe('Employee');

      // Second employee - Admin sub role
      expect(employees[1].subRole).toBe('Admin');

      // Third employee - No sub role assigned
      expect(employees[2].subRole).toBe('Not Assigned');
    });

    it('should display role field as-is without resolution', () => {
      const employees = component.employees;

      expect(employees[0].role).toBe('U');
      expect(employees[1].role).toBe('A');
      expect(employees[2].role).toBe('U');
    });

    it('should handle both basic and extended designation fields', () => {
      const employees = component.employees;
      
      // Check that both position and designation fields are resolved
      expect(employees[0].position).toBe('Software Developer');
      expect(employees[0].designation).toBe('Software Developer');
      
      expect(employees[1].position).toBe('Senior Developer');
      expect(employees[1].designation).toBe('Senior Developer');
    });
  });

  describe('Helper Methods', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should resolve department names correctly', () => {
      expect((component as any).resolveDepartmentName('46962f48-e727-40bf-9eb2-1bfe6fb8e6d2')).toBe('Engineering');
      expect((component as any).resolveDepartmentName('12345678-1234-1234-1234-123456789012')).toBe('Human Resources');
      expect((component as any).resolveDepartmentName(null)).toBe('Not Assigned');
      expect((component as any).resolveDepartmentName('')).toBe('Not Assigned');
      expect((component as any).resolveDepartmentName('unknown-id')).toContain('Unknown');
    });

    it('should resolve designation names correctly', () => {
      expect((component as any).resolveDesignationName('1237af0a-7ed3-491b-9195-1a6f7c62540a')).toBe('Software Developer');
      expect((component as any).resolveDesignationName('98765432-8765-8765-8765-987654321098')).toBe('Senior Developer');
      expect((component as any).resolveDesignationName(null)).toBe('Not Assigned');
      expect((component as any).resolveDesignationName('')).toBe('Not Assigned');
      expect((component as any).resolveDesignationName('unknown-id')).toContain('Unknown');
    });

    it('should resolve sub role names correctly', () => {
      expect((component as any).resolveSubRoleName('3e715253-2211-4c3b-9b51-3027b555f239')).toBe('Employee');
      expect((component as any).resolveSubRoleName('a1b2c3d4-5678-9012-3456-789012345678')).toBe('Admin');
      expect((component as any).resolveSubRoleName(null)).toBe('Not Assigned');
      expect((component as any).resolveSubRoleName('')).toBe('Not Assigned');
      expect((component as any).resolveSubRoleName('unknown-id')).toContain('Unknown');
    });
  });

  describe('Error Handling', () => {
    it('should handle department API failure gracefully', () => {
      employeeService.getDepartmentsMasterData.and.returnValue(of([]));
      component.ngOnInit();
      
      expect(component.departmentsCache.size).toBe(0);
      expect((component as any).resolveDepartmentName('some-id')).toContain('Unknown');
    });

    it('should handle designation API failure gracefully', () => {
      employeeService.getDesignationsMasterData.and.returnValue(of([]));
      component.ngOnInit();

      expect(component.designationsCache.size).toBe(0);
      expect((component as any).resolveDesignationName('some-id')).toContain('Unknown');
    });

    it('should handle roles API failure gracefully', () => {
      employeeService.getRoles.and.returnValue(of([]));
      component.ngOnInit();

      expect(component.rolesCache.size).toBe(0);
      expect((component as any).resolveSubRoleName('some-id')).toContain('Unknown');
    });

    it('should handle missing master data gracefully', () => {
      // Test with employee having IDs not in master data
      const employeeWithUnknownIds = {
        id: 'test-id',
        employee_code: 'TEST-001',
        first_name: 'Test',
        last_name: 'User',
        department_id: 'unknown-dept-id',
        designation_id: 'unknown-desig-id',
        sub_role_id: 'unknown-role-id',
        role: 'U',
        office_email: '<EMAIL>',
        phone_no: '1234567890',
        joining_date: '2023-01-01',
        is_active: true
      };

      const mockResponseWithUnknownIds = {
        success: true,
        data: [employeeWithUnknownIds]
      };

      employeeService.getAllEmployees.and.returnValue(of(mockResponseWithUnknownIds));
      component.ngOnInit();

      const employee = component.employees[0];
      expect(employee.department).toContain('Unknown');
      expect(employee.position).toContain('Unknown');
      expect(employee.subRole).toContain('Unknown');
    });
  });

  describe('Refresh Functionality', () => {
    it('should refresh employee data when called', () => {
      component.ngOnInit();
      
      // Clear the call count
      employeeService.getAllEmployees.calls.reset();
      
      // Call refresh
      component.refreshEmployees();
      
      // Verify employees are reloaded
      expect(employeeService.getAllEmployees).toHaveBeenCalled();
    });
  });
});
