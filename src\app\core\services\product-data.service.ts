import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProductDataService {
  // BehaviorSubject to store the selected product type
  private productTypeSubject = new BehaviorSubject<string>('');
  productType$: Observable<string> = this.productTypeSubject.asObservable();

  // BehaviorSubject to store the selected product sub-type
  private productSubTypeSubject = new BehaviorSubject<string>('');
  productSubType$: Observable<string> = this.productSubTypeSubject.asObservable();
  
  // BehaviorSubject to store the selected lead category
  private leadCategorySubject = new BehaviorSubject<string>('');
  leadCategory$: Observable<string> = this.leadCategorySubject.asObservable();
  
  // BehaviorSubject to store the selected lead source
  private leadSourceSubject = new BehaviorSubject<string>('');
  leadSource$: Observable<string> = this.leadSourceSubject.asObservable();

  constructor() { }

  // Method to set the product type
  setProductType(productType: string): void {
    console.log('from product data service ,setProductType method called', productType);
    this.productTypeSubject.next(productType);
  }

  // Method to get the current product type value
  getProductType(): string {
    return this.productTypeSubject.value;
  }

  // Method to set the product sub-type
  setProductSubType(productSubType: string): void {
    this.productSubTypeSubject.next(productSubType);
  }

  // Method to get the current product sub-type value
  getProductSubType(): string {
    console.log('from product data service ,getProductSubType method called', this.productSubTypeSubject.value);
    return this.productSubTypeSubject.value;
  }

  // Store the mapping of sub-product types to their parent product types
  private subTypeToProductTypeMapping: { [key: string]: string } = {};

  // Method to set the mapping of sub-product types to their parent product types
  setSubTypeToProductTypeMapping(mapping: { [key: string]: string }): void {
    this.subTypeToProductTypeMapping = mapping;
    console.log('🗺️ Updated sub-type to product type mapping:', this.subTypeToProductTypeMapping);
  }

  // Method to get the parent product type for a given sub-product type
  getProductTypeFromSubType(subTypeId: string): string {
    const productType = this.subTypeToProductTypeMapping[subTypeId];
    console.log('🔍 Getting product type for sub-type:', subTypeId, '→', productType);
    return productType || '';
  }
  
  // Method to set the lead category
  setLeadCategory(leadCategory: string): void {
    console.log('Setting lead category:', leadCategory);
    this.leadCategorySubject.next(leadCategory);
  }
  
  // Method to get the current lead category value
  getLeadCategory(): string {
    return this.leadCategorySubject.value;
  }
  
  // Method to set the lead source
  setLeadSource(leadSource: string): void {
    console.log('Setting lead source:', leadSource);
    this.leadSourceSubject.next(leadSource);
  }
  
  // Method to get the current lead source value
  getLeadSource(): string {
    return this.leadSourceSubject.value;
  }
}
