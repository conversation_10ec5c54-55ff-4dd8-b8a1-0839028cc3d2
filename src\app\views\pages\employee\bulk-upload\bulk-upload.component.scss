.file-upload-area {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: var(--bs-light);
  }

  &.border-primary {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
  }
}

.upload-placeholder {
  .icon-lg {
    font-size: 3rem;
  }
}

.selected-file {
  .file-info {
    h6 {
      color: var(--bs-dark);
      font-weight: 500;
    }
  }
}

.instructions {
  .instruction-item {
    .badge {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      font-size: 0.75rem;
      font-weight: 600;
    }

    h6 {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--bs-dark);
    }

    p {
      font-size: 0.8rem;
      line-height: 1.4;
    }
  }
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-info {
    border: none;
    
    .card-body {
      h4 {
        font-weight: 700;
        font-size: 2rem;
      }
      
      p {
        font-size: 0.875rem;
        opacity: 0.9;
      }
    }
  }
}

.table {
  font-size: 0.875rem;
  
  th {
    font-weight: 600;
    color: var(--bs-dark);
    border-top: none;
    padding: 0.75rem;
  }
  
  td {
    padding: 0.75rem;
    vertical-align: middle;
  }
}

.alert {
  border: none;
  border-radius: 0.5rem;
  
  &.alert-success {
    background-color: rgba(var(--bs-success-rgb), 0.1);
    color: var(--bs-success);
  }
  
  &.alert-danger {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
  }
}

.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  
  &.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .file-upload-area {
    min-height: 150px;
  }
  
  .upload-placeholder {
    .icon-lg {
      font-size: 2rem;
    }
    
    h5 {
      font-size: 1rem;
    }
  }
  
  .card.bg-primary,
  .card.bg-success,
  .card.bg-danger,
  .card.bg-info {
    .card-body {
      h4 {
        font-size: 1.5rem;
      }
    }
  }
}
