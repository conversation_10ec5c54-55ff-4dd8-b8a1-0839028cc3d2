import {
  Directive,
  Input,
  TemplateRef,
  ViewContainerRef,
  OnInit,
  OnDestroy,
  ElementRef
} from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { PermissionService } from '../services/permission.service';

/**
 * Structural directive for permission-based template rendering
 *
 * Usage examples:
 *
 * Single permission:
 * <div *hasPermission="'users:read'">Content for users with read permission</div>
 *
 * Multiple permissions (any):
 * <div *hasPermission="['users:read', 'users:write']">Content for users with any permission</div>
 *
 * Multiple permissions (all required):
 * <div *hasPermission="['users:read', 'users:write']; requireAll: true">Content for users with all permissions</div>
 *
 * With else template:
 * <div *hasPermission="'admin:access'; else noPermission">Admin content</div>
 * <ng-template #noPermission>You don't have admin access</ng-template>
 *
 * With then/else templates:
 * <div *hasPermission="'users:write'; then writeAccess; else readOnly"></div>
 * <ng-template #writeAccess>You can edit users</ng-template>
 * <ng-template #readOnly>You can only view users</ng-template>
 */
@Directive({
  selector: '[hasPermission]',
  standalone: true
})
export class HasPermissionDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private permissions: string | string[] = [];
  private requireAll = false;
  private elseTemplate: TemplateRef<any> | null = null;
  private thenTemplate: TemplateRef<any> | null = null;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private permissionService: PermissionService
  ) {}

  @Input()
  set hasPermission(permissions: string | string[]) {
    this.permissions = permissions;
    this.updateView();
  }

  @Input()
  set hasPermissionRequireAll(requireAll: boolean) {
    this.requireAll = requireAll;
    this.updateView();
  }

  @Input()
  set hasPermissionElse(template: TemplateRef<any>) {
    this.elseTemplate = template;
    this.updateView();
  }

  @Input()
  set hasPermissionThen(template: TemplateRef<any>) {
    this.thenTemplate = template;
    this.updateView();
  }

  ngOnInit(): void {
    // Subscribe to permission changes
    this.permissionService.permissions$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateView(): void {
    const hasPermission = this.checkPermissions();

    // Clear the view container
    this.viewContainer.clear();

    if (hasPermission) {
      // Show the main template or the 'then' template
      const templateToShow = this.thenTemplate || this.templateRef;
      this.viewContainer.createEmbeddedView(templateToShow);
    } else if (this.elseTemplate) {
      // Show the 'else' template if permission is denied
      this.viewContainer.createEmbeddedView(this.elseTemplate);
    }
  }

  private checkPermissions(): boolean {
    if (!this.permissions) {
      return true; // No permissions required
    }

    const permissionsArray = Array.isArray(this.permissions)
      ? this.permissions
      : [this.permissions];

    if (permissionsArray.length === 0) {
      return true; // No permissions required
    }

    return this.requireAll
      ? this.permissionService.hasAllPermissions(permissionsArray)
      : this.permissionService.hasAnyPermission(permissionsArray);
  }
}

/**
 * Attribute directive for disabling elements based on permissions
 *
 * Usage examples:
 *
 * Disable button if no permission:
 * <button [disableIfNoPermission]="'users:write'">Edit User</button>
 *
 * Disable with multiple permissions:
 * <button [disableIfNoPermission]="['users:write', 'users:update']">Edit User</button>
 */
@Directive({
  selector: '[disableIfNoPermission]',
  standalone: true
})
export class DisableIfNoPermissionDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private permissions: string | string[] = [];
  private requireAll = false;

  constructor(
    private permissionService: PermissionService,
    private elementRef: ElementRef
  ) {}

  @Input()
  set disableIfNoPermission(permissions: string | string[]) {
    this.permissions = permissions;
    this.updateElement();
  }

  @Input()
  set disableIfNoPermissionRequireAll(requireAll: boolean) {
    this.requireAll = requireAll;
    this.updateElement();
  }

  ngOnInit(): void {
    // Subscribe to permission changes
    this.permissionService.permissions$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateElement();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateElement(): void {
    const hasPermission = this.checkPermissions();
    const element = this.elementRef.nativeElement;

    if (element) {
      element.disabled = !hasPermission;

      // Add visual feedback
      if (!hasPermission) {
        element.classList.add('permission-disabled');
        element.title = 'You do not have permission to perform this action';
      } else {
        element.classList.remove('permission-disabled');
        element.removeAttribute('title');
      }
    }
  }

  private checkPermissions(): boolean {
    if (!this.permissions) {
      return true; // No permissions required
    }

    const permissionsArray = Array.isArray(this.permissions)
      ? this.permissions
      : [this.permissions];

    if (permissionsArray.length === 0) {
      return true; // No permissions required
    }

    return this.requireAll
      ? this.permissionService.hasAllPermissions(permissionsArray)
      : this.permissionService.hasAnyPermission(permissionsArray);
  }
}

/**
 * Pipe for permission checking in templates
 *
 * Usage examples:
 *
 * <div *ngIf="'users:read' | hasPermission">User content</div>
 * <button [disabled]="!('users:write' | hasPermission)">Edit</button>
 */
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'hasPermission',
  standalone: true,
  pure: false // Make it impure to react to permission changes
})
export class HasPermissionPipe implements PipeTransform {
  constructor(private permissionService: PermissionService) {}

  transform(permissions: string | string[], requireAll: boolean = false): boolean {
    if (!permissions) {
      return true;
    }

    const permissionsArray = Array.isArray(permissions)
      ? permissions
      : [permissions];

    return requireAll
      ? this.permissionService.hasAllPermissions(permissionsArray)
      : this.permissionService.hasAnyPermission(permissionsArray);
  }
}
