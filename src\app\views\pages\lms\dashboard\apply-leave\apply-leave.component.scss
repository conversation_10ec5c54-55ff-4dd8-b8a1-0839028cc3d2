// LMS card icon styling
.lms-card-icon {
    width: 60px;
    height: 60px;
    object-fit: contain;
}

// Modern table card styling
.modern-table-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;

    .card-body {
        padding: 1.5rem;
    }

    .card-title {
        color: #3F828B;
        font-weight: 600;
        margin-bottom: 1rem;
    }
}

/* NGX-Datatable styles (matching ops-team) */
:host ::ng-deep .ngx-datatable {
  box-shadow: none;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

:host ::ng-deep .ngx-datatable.bootstrap {
  background-color: #fff;
  color: #212529;
}

:host ::ng-deep .ngx-datatable.bootstrap .datatable-header {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

:host ::ng-deep .ngx-datatable.bootstrap .datatable-header .datatable-header-cell {
  font-weight: 500;
  padding: 0.75rem;
  text-align: left;
  color: #495057;
}

:host ::ng-deep .ngx-datatable.bootstrap .datatable-body .datatable-body-row {
  border-bottom: 1px solid #dee2e6;
}

:host ::ng-deep .ngx-datatable.bootstrap .datatable-body .datatable-body-cell {
  padding: 0.75rem;
}

:host ::ng-deep .ngx-datatable.bootstrap .datatable-body .datatable-row-detail {
  background-color: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

// Action icons styling (matching ops-team)
.action-icons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

.action-icon {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Table controls styling
.form-label.small {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.form-select-sm, .form-control-sm {
    border-color: #ced4da;

    &:focus {
        border-color: #3F828B;
        box-shadow: 0 0 0 0.2rem rgba(63, 130, 139, 0.25);
    }
}

// Enhanced action buttons styling
.action-buttons-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  min-height: 2rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    white-space: nowrap;

    i {
        font-size: 0.75rem;
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.15);
    }

    &:active {
        transform: translateY(0);
    }

   

    &.btn-success {
        background-color: #28a745;
        border-color: #28a745;

        &:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
    }

    &.btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;

        &:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
    }

    
}

// Status badge enhancements
.badge {
    &.fs-6 {
        font-size: 0.75rem !important;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    &.bg-success {
        background-color: #28a745 !important;
        color: #fff;
    }

    &.bg-warning {
        background-color: #ffc107 !important;
        color: #000;
    }

    &.bg-danger {
        background-color: #dc3545 !important;
        color: #fff;
    }

    &.bg-secondary {
        background-color: #6c757d !important;
        color: #fff;
    }
}

// Custom SweetAlert2 styling
:host ::ng-deep {
    .swal2-popup-large {
        width: 600px !important;
        max-width: 90vw !important;
    }

    .swal2-enhanced-modal {
        width: 650px !important;
        max-width: 95vw !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;

        .swal2-title {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            color: #495057 !important;
            margin-bottom: 1rem !important;
        }

        .swal2-html-container {
            font-size: 0.9rem !important;
            line-height: 1.5 !important;
            color: #495057 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .swal2-actions {
            margin-top: 1.5rem !important;
            gap: 0.75rem !important;
        }

        .swal2-confirm, .swal2-cancel {
            border-radius: 6px !important;
            font-weight: 500 !important;
            padding: 0.5rem 1.25rem !important;
            font-size: 0.875rem !important;
            transition: all 0.2s ease-in-out !important;

            &:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }
        }
    }

    .swal2-popup-loading {
        width: 400px !important;
    }

    .swal2-textarea-custom {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        padding: 0.75rem !important;
        font-size: 0.875rem !important;
        resize: vertical !important;
        min-height: 80px !important;

        &:focus {
            border-color: #3F828B !important;
            box-shadow: 0 0 0 0.2rem rgba(63, 130, 139, 0.25) !important;
            outline: none !important;
        }
    }

    .swal2-html-container {
        .alert {
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;

            &.alert-warning {
                color: #856404;
                background-color: #fff3cd;
                border-color: #ffeaa7;
            }

            &.alert-success {
                color: #155724;
                background-color: #d4edda;
                border-color: #c3e6cb;
            }

            &.alert-danger {
                color: #721c24;
                background-color: #f8d7da;
                border-color: #f5c6cb;
            }
        }

        .list-unstyled {
            padding-left: 0;
            list-style: none;

            li {
                padding: 0.25rem 0;
                border-bottom: 1px solid #f8f9fa;

                &:last-child {
                    border-bottom: none;
                }
            }
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    }

    .swal2-confirm {
        background-color: #3F828B !important;
        border-color: #3F828B !important;

        &:hover {
            background-color: #2d5f66 !important;
            border-color: #2d5f66 !important;
        }
    }

    .swal2-cancel {
        background-color: #6c757d !important;
        border-color: #6c757d !important;

        &:hover {
            background-color: #545b62 !important;
            border-color: #545b62 !important;
        }
    }
}

// Leave Type Management Section
.leave-type-management {
    .table {
        th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }

        td {
            vertical-align: middle;
        }

        .btn-group {
            .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;

                &:first-child {
                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                }

                &:last-child {
                    border-top-left-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }
        }
    }
}

// Highlight effect for auto-redirect
.highlight-section {
    animation: highlightPulse 3s ease-in-out;
    border: 2px solid #3F828B !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 0 20px rgba(63, 130, 139, 0.3) !important;
}

@keyframes highlightPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(63, 130, 139, 0.7);
        border-color: #3F828B;
    }
    50% {
        box-shadow: 0 0 20px 10px rgba(63, 130, 139, 0.3);
        border-color: #2d5f66;
    }
    100% {
        box-shadow: 0 0 20px 0 rgba(63, 130, 139, 0.1);
        border-color: #3F828B;
    }
}

// Custom toast styling
:host ::ng-deep {
    .swal2-toast-custom {
        background-color: #3F828B !important;
        color: white !important;
        border-radius: 0.5rem !important;

        .swal2-title {
            color: white !important;
            font-size: 1rem !important;
        }

        .swal2-timer-progress-bar {
            background-color: rgba(255, 255, 255, 0.3) !important;
        }
    }
}



// Pagination styling
.pagination {
  .page-item {
    .page-link {
      position: relative;
      z-index: 1;
      cursor: pointer !important;
      border: 1px solid #dee2e6;
      color: #007bff;
      background-color: #fff;
      padding: 0.375rem 0.75rem;
      margin-left: -1px;
      line-height: 1.25;
      text-decoration: none;
      transition: all 0.15s ease-in-out;

      &:hover {
        z-index: 2;
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #dee2e6;
      }

      &:focus {
        z-index: 3;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }

    &.active .page-link {
      z-index: 3;
      color: #fff;
      background-color: #007bff;
      border-color: #007bff;
    }

    &.disabled .page-link {
      color: #6c757d;
      pointer-events: none;
      cursor: auto;
      background-color: #fff;
      border-color: #dee2e6;
    }
  }
}

// Enhanced date input with calendar icon - keeps existing UI appearance
.form-control[type="date"] {
    position: relative;

    // Ensure the entire input area is clickable without changing appearance
    &::-webkit-calendar-picker-indicator {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: transparent;
        opacity: 0;
    }

    // Make date input text areas clickable
    &::-webkit-datetime-edit {
        cursor: pointer;
    }

    &::-webkit-datetime-edit-fields-wrapper {
        cursor: pointer;
    }

    &::-webkit-datetime-edit-text {
        cursor: pointer;
    }

    &::-webkit-datetime-edit-month-field,
    &::-webkit-datetime-edit-day-field,
    &::-webkit-datetime-edit-year-field {
        cursor: pointer;
    }
}

// NgBootstrap datepicker styling - no additional calendar icons needed
.col-12.col-md-6.mb-3 {
    position: relative;

    // Remove any default browser calendar icons that might conflict
    input[ngbDatepicker] {
        &::-webkit-calendar-picker-indicator {
            display: none;
        }

        &::-webkit-inner-spin-button,
        &::-webkit-outer-spin-button {
            display: none;
        }
    }
}

// NgBootstrap datepicker alignment fixes
:host ::ng-deep {
    // Universal alignment fix for all datepicker elements
    .ngb-dp-content * {
        box-sizing: border-box !important;
    }

    .ngb-dp-content {
        .ngb-dp-month {
            .ngb-dp-week {
                display: flex !important;
                align-items: center !important;
                justify-content: space-between !important;
                height: 2.5rem !important;
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;

                .ngb-dp-day {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    width: 2.25rem !important;
                    height: 2.25rem !important;
                    min-width: 2.25rem !important;
                    min-height: 2.25rem !important;
                    max-width: 2.25rem !important;
                    max-height: 2.25rem !important;
                    text-align: center !important;
                    margin: 0.125rem !important;
                    padding: 0 !important;
                    flex: 0 0 2.25rem !important;
                    position: relative !important;
                    cursor: pointer !important;
                    vertical-align: top !important;
                    box-sizing: border-box !important;

                    // Force all button types to same size and alignment
                    button, .btn, .btn-light, .btn-outline-light {
                        width: 2.25rem !important;
                        height: 2.25rem !important;
                        min-width: 2.25rem !important;
                        min-height: 2.25rem !important;
                        max-width: 2.25rem !important;
                        max-height: 2.25rem !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        border: none !important;
                        border-radius: 0.25rem !important;
                        font-size: 0.875rem !important;
                        line-height: 1 !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        box-sizing: border-box !important;
                        position: relative !important;
                        pointer-events: auto !important;
                        z-index: 1 !important;
                        cursor: pointer !important;
                        vertical-align: top !important;
                        top: 0 !important;
                        bottom: 0 !important;
                        left: 0 !important;
                        right: 0 !important;

                        // Ensure click events work properly
                        &:hover {
                            transform: none !important;
                        }

                        &:active {
                            transform: none !important;
                        }
                    }

                    // Disabled dates styling - force exact same dimensions and alignment
                    &.disabled {
                        width: 2.25rem !important;
                        height: 2.25rem !important;
                        min-width: 2.25rem !important;
                        min-height: 2.25rem !important;
                        max-width: 2.25rem !important;
                        max-height: 2.25rem !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        vertical-align: top !important;
                        box-sizing: border-box !important;

                        button, .btn, .btn-light, .btn-outline-light {
                            background-color: #f8f9fa !important;
                            color: #6c757d !important;
                            opacity: 0.6 !important;
                            cursor: not-allowed !important;
                            width: 2.25rem !important;
                            height: 2.25rem !important;
                            min-width: 2.25rem !important;
                            min-height: 2.25rem !important;
                            max-width: 2.25rem !important;
                            max-height: 2.25rem !important;
                            pointer-events: none !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            vertical-align: top !important;
                            box-sizing: border-box !important;
                            top: 0 !important;
                            bottom: 0 !important;
                            left: 0 !important;
                            right: 0 !important;

                            &:hover {
                                background-color: #f8f9fa !important;
                                color: #6c757d !important;
                            }
                        }
                    }

                    // Outside dates (previous/next month) styling
                    &.outside {
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        vertical-align: top !important;
                        box-sizing: border-box !important;

                        button, .btn, .btn-light, .btn-outline-light {
                            color: #adb5bd !important;
                            background-color: transparent !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            vertical-align: top !important;
                            box-sizing: border-box !important;
                            top: 0 !important;
                            bottom: 0 !important;
                            left: 0 !important;
                            right: 0 !important;
                        }

                        &.disabled {
                            button, .btn, .btn-light, .btn-outline-light {
                                color: #dee2e6 !important;
                                background-color: transparent !important;
                                display: flex !important;
                                align-items: center !important;
                                justify-content: center !important;
                                vertical-align: top !important;
                                box-sizing: border-box !important;
                                top: 0 !important;
                                bottom: 0 !important;
                                left: 0 !important;
                                right: 0 !important;
                            }
                        }
                    }

                    // Today's date styling
                    &.today {
                        .btn-light {
                            background-color: #e3f2fd;
                            color: #1976d2;
                            font-weight: 600;
                        }

                        &:not(.disabled) .btn-light:hover {
                            background-color: #bbdefb;
                        }
                    }

                    // Selected date styling
                    &.selected {
                        .btn-light {
                            background-color: #3F828B !important;
                            color: white !important;
                            font-weight: 600;
                        }
                    }

                    // Hover effect for enabled dates
                    &:not(.disabled) {
                        .btn-light:hover {
                            background-color: #e9ecef;
                            color: #495057;
                        }
                    }
                }
            }

            // Month/year navigation styling
            .ngb-dp-arrow {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 2rem;
                height: 2rem;
                border: none;
                background: none;
                color: #6c757d;

                &:hover {
                    color: #3F828B;
                    background-color: #f8f9fa;
                    border-radius: 0.25rem;
                }
            }

            // Month/year display styling
            .ngb-dp-month-name {
                font-weight: 600;
                color: #495057;
                font-size: 1rem;
            }
        }

        // Calendar header (day names) styling
        .ngb-dp-weekdays {
            .ngb-dp-weekday {
                width: 2rem;
                height: 2rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                color: #6c757d;
                font-size: 0.75rem;
                text-transform: uppercase;
                margin: 0.125rem;
            }
        }
    }
}

// Responsive table adjustments
@media (max-width: 768px) {
    .modern-table-card .card-body {
        padding: 1rem;
    }

    :host ::ng-deep .ngx-datatable.bootstrap .datatable-header .datatable-header-cell,
    :host ::ng-deep .ngx-datatable.bootstrap .datatable-body .datatable-body-cell {
        padding: 0.5rem;
    }

    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.6875rem;

        i {
            font-size: 0.6875rem;
        }
    }

    .pagination {
      .page-item .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
      }
    }

    // Mobile-specific date input improvements
    .form-control[type="date"] {
        min-height: 52px; // Larger touch targets on mobile
        font-size: 1.1rem; // Larger text on mobile

        &::-webkit-calendar-picker-indicator {
            // Ensure full coverage on mobile
            width: 100%;
            height: 100%;
        }
    }
}