.modern-table-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  }

  &.border-primary {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
  }
}

.form-check-input {
  &:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
  }
}

.form-check-label {
  cursor: pointer;

  h6 {
    color: var(--bs-dark);
    font-weight: 600;
  }
}

.badge {
  font-size: 0.75rem;
}

.btn {
  border-radius: 0.375rem;

  &:hover {
    transform: translateY(-1px);
  }
}

.alert {
  border-radius: 0.5rem;
  border: none;
}

.empty-state {
  padding: 2rem;

  i {
    opacity: 0.5;
  }
}

.text-muted {
  font-size: 0.875rem;
}

.input-group {
  .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
  }

  .input-group-text {
    border-radius: 0 0.375rem 0.375rem 0;
  }
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

// Enhanced Permission Management Header
.permission-management-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 0 0 1rem 1rem;

  .page-header {
    .page-title {
      font-size: 1.75rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .page-subtitle {
      font-size: 1rem;
      opacity: 0.9;
    }
  }

  .header-actions {
    .btn {
      border-radius: 0.5rem;
      font-weight: 500;

      &.btn-success {
        background: rgba(40, 167, 69, 0.9);
        border: 1px solid rgba(40, 167, 69, 0.9);

        &:hover {
          background: rgba(40, 167, 69, 1);
          transform: translateY(-1px);
        }

        &:disabled {
          opacity: 0.6;
          transform: none;
        }
      }

      &.btn-outline-light {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
          color: white;
        }
      }
    }
  }
}

// Permission Summary Cards
.permission-summary-section {
  .summary-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .summary-icon {
      width: 3rem;
      height: 3rem;
      border-radius: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;
      color: white;

      &.bg-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.bg-info { background: linear-gradient(135deg, #3ca55c 0%, #b5ac49 100%); }
      &.bg-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
      &.bg-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
    }

    .summary-content {
      .summary-number {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: #2d3748;
      }

      .summary-label {
        font-size: 0.875rem;
        color: #718096;
        margin-bottom: 0;
        font-weight: 500;
      }
    }
  }
}

// Permission Controls Section
.permission-controls-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  .search-box {
    .input-group {
      border-radius: 0.75rem;
      overflow: hidden;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

      .input-group-text {
        background: #f8f9fa;
        border: none;
        color: #6c757d;
      }

      .form-control {
        border: none;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }

      .btn {
        border: none;
        background: #f8f9fa;

        &:hover {
          background: #e9ecef;
        }
      }
    }
  }

  .form-select {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;

    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// Permission Cards Grid
.permissions-grid {
  .permission-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 2px solid #f1f3f4;
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border-color: #28a745;
      background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);

      .permission-card-header {
        background: rgba(40, 167, 69, 0.1);
      }
    }

    // Category-based styling
    &.category-users { border-left: 4px solid #007bff; }
    &.category-roles { border-left: 4px solid #6f42c1; }
    &.category-sales { border-left: 4px solid #28a745; }
    &.category-employees { border-left: 4px solid #fd7e14; }
    &.category-master { border-left: 4px solid #20c997; }
    &.category-admin { border-left: 4px solid #dc3545; }

    .permission-card-header {
      padding: 1rem 1.5rem 0.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .permission-checkbox {
        .form-check-input {
          width: 1.25rem;
          height: 1.25rem;
          border-radius: 0.375rem;
          border: 2px solid #dee2e6;

          &:checked {
            background-color: #28a745;
            border-color: #28a745;
          }
        }
      }

      .permission-category {
        .category-badge {
          background: #f8f9fa;
          color: #6c757d;
          padding: 0.25rem 0.75rem;
          border-radius: 1rem;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }

    .permission-card-body {
      padding: 0.5rem 1.5rem 1rem;

      .permission-label {
        cursor: pointer;
        margin-bottom: 0;

        .permission-name {
          font-size: 1rem;
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 0.5rem;
        }

        .permission-description {
          font-size: 0.875rem;
          color: #718096;
          line-height: 1.4;
          margin-bottom: 0;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }

    .permission-card-footer {
      padding: 0.5rem 1.5rem 1rem;
      border-top: 1px solid #f1f3f4;
      background: #fafbfc;

      .permission-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;

        small {
          font-size: 0.75rem;
          color: #6c757d;
        }
      }
    }
  }
}

// Permissions List Table
.permissions-list {
  .table {
    .permission-icon {
      width: 2rem;
      height: 2rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .category-badge-users { background-color: #cfe2ff; color: #084298; }
    .category-badge-roles { background-color: #e2e3ff; color: #3d1a78; }
    .category-badge-sales { background-color: #d1e7dd; color: #0f5132; }
    .category-badge-employees { background-color: #ffe5d0; color: #653208; }
    .category-badge-master { background-color: #d1ecf1; color: #0c5460; }
    .category-badge-admin { background-color: #f8d7da; color: #842029; }
    .category-badge-general { background-color: #f8f9fa; color: #495057; }
  }
}

// Loading Section
.loading-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

// Empty State
.empty-state-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin: 2rem 0;

  .empty-state-icon {
    .icon-xxl {
      width: 4rem;
      height: 4rem;
    }
  }
}

// Action Bar
.action-bar-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  .selection-info {
    font-size: 0.95rem;
  }

  .action-buttons {
    .btn {
      font-weight: 500;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
    }
  }
}
