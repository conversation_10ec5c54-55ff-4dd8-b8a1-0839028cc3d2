<!-- Employee Form Component -->
<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title">{{ pageTitle }}</h4>
            <p class="text-muted">Complete the form below to create a new employee account</p>
          </div>
          <a routerLink="/employees" class="btn btn-outline-secondary">
            <i data-feather="arrow-left" appFeatherIcon class="me-2"></i>
            Back to List
          </a>
        </div>

        <!-- Loading and Error States -->
        <div *ngIf="loading" class="text-center my-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Loading employee data...</p>
        </div>

        <div *ngIf="errorMessage" class="alert alert-danger mt-3" role="alert">
          {{ errorMessage }}
        </div>

        <div *ngIf="successMessage" class="alert alert-success mt-3" role="alert">
          {{ successMessage }}
        </div>

        <!-- Employee Form -->
        <form [formGroup]="employeeForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
          <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
              <h5 class="mb-4">Basic Information</h5>

              <!-- Name -->
              <div class="mb-3">
                <label for="name" class="form-label">Full Name</label>
                <input
                  type="text"
                  class="form-control"
                  id="name"
                  formControlName="name"
                  [ngClass]="{'is-invalid': isInvalid('name')}"
                  placeholder="Enter full name"
                >
                <div *ngIf="isInvalid('name')" class="invalid-feedback">
                  {{ getErrorMessage('name') }}
                </div>
              </div>

              <!-- Email -->
              <div class="mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  formControlName="email"
                  [ngClass]="{'is-invalid': isInvalid('email')}"
                  placeholder="Enter email address"
                >
                <div *ngIf="isInvalid('email')" class="invalid-feedback">
                  {{ getErrorMessage('email') }}
                </div>
              </div>

              <!-- Phone -->
              <div class="mb-3">
                <label for="phone" class="form-label">Phone Number</label>
                <input
                  type="tel"
                  class="form-control"
                  id="phone"
                  formControlName="phone"
                  [ngClass]="{'is-invalid': isInvalid('phone')}"
                  placeholder="Enter phone number"
                >
                <div *ngIf="isInvalid('phone')" class="invalid-feedback">
                  {{ getErrorMessage('phone') }}
                </div>
              </div>
            </div>

            <!-- Work Information -->
            <div class="col-md-6">
              <h5 class="mb-4">Work Information</h5>

              <!-- Department -->
              <div class="mb-3">
                <label for="department" class="form-label">Department</label>
                <input
                  type="text"
                  class="form-control"
                  id="department"
                  formControlName="department"
                  placeholder="Enter department"
                >
              </div>

              <!-- Position -->
              <div class="mb-3">
                <label for="position" class="form-label">Position</label>
                <input
                  type="text"
                  class="form-control"
                  id="position"
                  formControlName="position"
                  placeholder="Enter position/title"
                >
              </div>

              <!-- Joining Date -->
              <div class="mb-3">
                <label for="joining_date" class="form-label">Joining Date</label>
                <input
                  type="date"
                  class="form-control"
                  id="joining_date"
                  formControlName="joining_date"
                >
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <!-- Additional Information -->
            <div class="col-md-6">
              <h5 class="mb-4">Additional Information</h5>

              <!-- Salary -->
              <div class="mb-3">
                <label for="salary" class="form-label">Salary</label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input
                    type="number"
                    class="form-control"
                    id="salary"
                    formControlName="salary"
                    [ngClass]="{'is-invalid': isInvalid('salary')}"
                    placeholder="Enter salary amount"
                  >
                  <div *ngIf="isInvalid('salary')" class="invalid-feedback">
                    {{ getErrorMessage('salary') }}
                  </div>
                </div>
              </div>

              <!-- Status -->
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    id="user_active"
                    formControlName="user_active"
                  >
                  <label class="form-check-label" for="user_active">
                    Account Active
                  </label>
                </div>
                <small class="text-muted">Inactive employees cannot log in to the system</small>
              </div>
            </div>

            <!-- Role and Permissions -->
            <div class="col-md-6">
              <h5 class="mb-4">Role & Permissions</h5>

              <!-- Role Selection -->
              <div class="mb-3">
                <label for="role" class="form-label">Role</label>
                <select
                  class="form-select"
                  id="role"
                  formControlName="role"
                  [attr.disabled]="rolesLoading ? true : null"
                >
                  <option value="" disabled>Select a role</option>
                  <option *ngFor="let role of roles" [value]="role.name.toLowerCase()">
                    {{ role.name }}
                  </option>
                </select>
                <small class="text-muted">Role determines access level in the system</small>
                <div *ngIf="rolesLoading" class="mt-2">
                  <small><i>Loading roles...</i></small>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="d-flex justify-content-end mt-4 pt-4 border-top">
            <button
              type="button"
              class="btn btn-outline-secondary me-2"
              routerLink="/employees"
              [disabled]="submitLoading"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="employeeForm.invalid || submitLoading"
            >
              <span *ngIf="!submitLoading">{{ submitBtnText }}</span>
              <span *ngIf="submitLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              <span *ngIf="submitLoading">Saving...</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
