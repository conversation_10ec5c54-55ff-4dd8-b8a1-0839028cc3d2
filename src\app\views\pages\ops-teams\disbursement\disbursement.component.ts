import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule, NgbDropdownModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgxDatatableModule, ColumnMode, SortType } from '@swimlane/ngx-datatable';

// Sortable directive
export type SortColumn = keyof DisbursementData | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Disbursement data interface
export interface DisbursementData {
  id: number;
  uniqueId: string;
  leadName: string;
  companyName: string;
  projectName: string;
  productType: string;
  docketStatus: string;
  mortgageStatus: string;
  reportStatus: string;
  disbursementStatus: string;
  fileClosedStatus: string;
  createdDate: string;
  lastUpdated: string;
}

// Sample data
const DISBURSEMENT_DATA: DisbursementData[] = [
  {
    id: 1,
    uniqueId: 'BC-2025-001',
    leadName: 'L&T',
    companyName: 'L&T',
    projectName: 'L&T Parel',
    productType: 'CF/PF/IF',
    docketStatus: 'Completed',
    mortgageStatus: 'In Progress',
    reportStatus: 'Pending',
    disbursementStatus: 'Pending',
    fileClosedStatus: 'Pending',
    createdDate: '15 Jun 2023',
    lastUpdated: '22 Aug 2023'
  },
  {
    id: 2,
    uniqueId: 'BC-2025-002',
    leadName: 'Hiranandani',
    companyName: 'Hiranandani Developers',
    projectName: 'Lake Enclave',
    productType: 'HL/LAP/LRD/NRPL',
    docketStatus: 'Completed',
    mortgageStatus: 'Completed',
    reportStatus: 'Completed',
    disbursementStatus: 'In Progress',
    fileClosedStatus: 'Pending',
    createdDate: '10 May 2023',
    lastUpdated: '15 Jul 2023'
  },
  {
    id: 3,
    uniqueId: 'BC-2025-003',
    leadName: 'Lodha Group',
    companyName: 'Lodha Developers',
    projectName: 'World Towers',
    productType: 'Insurance',
    docketStatus: 'In Progress',
    mortgageStatus: 'Pending',
    reportStatus: 'Pending',
    disbursementStatus: 'Pending',
    fileClosedStatus: 'Pending',
    createdDate: '05 Apr 2023',
    lastUpdated: '20 Jun 2023'
  },
  {
    id: 4,
    uniqueId: 'BC-2025-004',
    leadName: 'Oberoi Realty',
    companyName: 'Oberoi Group',
    projectName: 'Sky City',
    productType: 'Property',
    docketStatus: 'Completed',
    mortgageStatus: 'Completed',
    reportStatus: 'Completed',
    disbursementStatus: 'Completed',
    fileClosedStatus: 'Completed',
    createdDate: '12 Mar 2023',
    lastUpdated: '18 May 2023'
  },
  {
    id: 5,
    uniqueId: 'BC-2025-005',
    leadName: 'Godrej Properties',
    companyName: 'Godrej Group',
    projectName: 'Godrej Central',
    productType: 'CF/PF/IF',
    docketStatus: 'Completed',
    mortgageStatus: 'Completed',
    reportStatus: 'In Progress',
    disbursementStatus: 'Pending',
    fileClosedStatus: 'Pending',
    createdDate: '20 Feb 2023',
    lastUpdated: '10 Apr 2023'
  }
];

// Helper function for filtering
function search(text: string, pipe: DecimalPipe): DisbursementData[] {
  return DISBURSEMENT_DATA.filter(item => {
    const term = text.toLowerCase();
    return item.uniqueId.toLowerCase().includes(term)
        || item.leadName.toLowerCase().includes(term)
        || item.companyName.toLowerCase().includes(term)
        || item.projectName.toLowerCase().includes(term)
        || item.productType.toLowerCase().includes(term)
        || item.docketStatus.toLowerCase().includes(term)
        || item.mortgageStatus.toLowerCase().includes(term)
        || item.reportStatus.toLowerCase().includes(term)
        || item.disbursementStatus.toLowerCase().includes(term)
        || item.fileClosedStatus.toLowerCase().includes(term);
  });
}

// Helper function for sorting
function compare(v1: string | number, v2: string | number) {
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-disbursement',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    NgbDropdownModule,
    NgbNavModule,
    FeatherIconDirective,
    NgbdSortableHeader,
    NgxDatatableModule
  ],
  templateUrl: './disbursement.component.html',
  styleUrl: './disbursement.component.scss',
  providers: [DecimalPipe]
})
export class DisbursementComponent implements OnInit {
  // View mode flag
  showListView = true;
  showDetailsForm = false;
  selectedItemId: number | null = null;

  // Original data
  disbursementData = DISBURSEMENT_DATA;

  // Filtered data
  filteredDisbursements: DisbursementData[] = [];

  // Search filter
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 5;
  collectionSize = DISBURSEMENT_DATA.length;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // NgxDatatable properties
  ColumnMode = ColumnMode;
  SortType = SortType;

  // Active tab ID
  activeTabId = 1;

  // Section status variables
  // Docket section
  vettingListStatus: string = '';
  originalDeedStatus: string = '';
  documentsExternalStatus: string = '';

  // Mortgage section
  stampDutyStatus: string = '';
  mortgageProcessStatus: string = '';

  // Waiting for Report section
  pendencyListStatus: string = '';
  pddStatus: string = '';
  otcStatus: string = '';

  // Disbursement section
  disbursementStatus: string = '';

  // File Closed section
  fileClosedStatus: string = '';

  // Follow-up variables
  // Docket section follow-ups
  vettingListFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  originalDeedFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  documentsExternalFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  // Mortgage section follow-ups
  stampDutyFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  mortgageProcessFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  // Waiting for Report section follow-ups
  pendencyListFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  pddFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  otcFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  // Disbursement section follow-ups
  disbursementFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  // File Closed section follow-ups
  fileClosedFollowUp = {
    details: '',
    date: this.getCurrentDate(),
    time: this.getCurrentTime()
  };

  // Follow-up history
  // Docket section history
  vettingListFollowUpHistory: any[] = [];
  originalDeedFollowUpHistory: any[] = [];
  documentsExternalFollowUpHistory: any[] = [];

  // Mortgage section history
  stampDutyFollowUpHistory: any[] = [];
  mortgageProcessFollowUpHistory: any[] = [];

  // Waiting for Report section history
  pendencyListFollowUpHistory: any[] = [];
  pddFollowUpHistory: any[] = [];
  otcFollowUpHistory: any[] = [];

  // Disbursement section history
  disbursementFollowUpHistory: any[] = [];

  // File Closed section history
  fileClosedFollowUpHistory: any[] = [];

  constructor(private pipe: DecimalPipe) {
    this.refreshDisbursements();
  }

  ngOnInit(): void {
    // Set up search filter
    this.searchTerm.valueChanges.subscribe(() => {
      this.page = 1;
      this.refreshDisbursements();
    });
  }

  refreshDisbursements() {
    // Filter by search term
    const filtered = search(this.searchTerm.value, this.pipe);
    this.collectionSize = filtered.length;

    // Slice for pagination
    this.filteredDisbursements = filtered.slice(
      (this.page - 1) * this.pageSize,
      (this.page - 1) * this.pageSize + this.pageSize
    );
  }

  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.refreshDisbursements();
    } else {
      this.filteredDisbursements = [...this.filteredDisbursements].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-success';
      case 'in progress': return 'bg-warning';
      case 'pending': return 'bg-secondary';
      default: return 'bg-info';
    }
  }

  // Toggle between list view and details form
  showAddDetailsForm() {
    this.showListView = false;
    this.showDetailsForm = true;
    this.selectedItemId = null;
    this.resetForm();
  }

  editItem(id: number) {
    this.showListView = false;
    this.showDetailsForm = true;
    this.selectedItemId = id;
    this.loadItemData(id);
  }

  backToList() {
    this.showListView = true;
    this.showDetailsForm = false;
    this.selectedItemId = null;
    this.refreshDisbursements();
  }

  // Load item data for editing
  loadItemData(id: number) {
    const item = this.disbursementData.find(d => d.id === id);
    if (item) {
      // Set form values based on the selected item
      this.vettingListStatus = this.mapStatusToFormValue(item.docketStatus);
      this.originalDeedStatus = this.mapStatusToFormValue(item.docketStatus);
      this.documentsExternalStatus = this.mapStatusToFormValue(item.docketStatus);

      this.stampDutyStatus = this.mapStatusToFormValue(item.mortgageStatus);
      this.mortgageProcessStatus = this.mapStatusToFormValue(item.mortgageStatus);

      this.pendencyListStatus = this.mapStatusToFormValue(item.reportStatus);
      this.pddStatus = this.mapStatusToFormValue(item.reportStatus);
      this.otcStatus = this.mapStatusToFormValue(item.reportStatus);

      this.disbursementStatus = this.mapStatusToFormValue(item.disbursementStatus);

      this.fileClosedStatus = this.mapStatusToFormValue(item.fileClosedStatus);
    }
  }

  // Map status from data to form value
  mapStatusToFormValue(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'Yes';
      case 'in progress': return 'Query';
      case 'pending': return 'No';
      default: return '';
    }
  }

  // Reset form to default values
  resetForm() {
    this.vettingListStatus = '';
    this.originalDeedStatus = '';
    this.documentsExternalStatus = '';

    this.stampDutyStatus = '';
    this.mortgageProcessStatus = '';

    this.pendencyListStatus = '';
    this.pddStatus = '';
    this.otcStatus = '';

    this.disbursementStatus = '';

    this.fileClosedStatus = '';

    // Reset follow-up history
    this.vettingListFollowUpHistory = [];
    this.originalDeedFollowUpHistory = [];
    this.documentsExternalFollowUpHistory = [];
    this.stampDutyFollowUpHistory = [];
    this.mortgageProcessFollowUpHistory = [];
    this.pendencyListFollowUpHistory = [];
    this.pddFollowUpHistory = [];
    this.otcFollowUpHistory = [];
    this.disbursementFollowUpHistory = [];
    this.fileClosedFollowUpHistory = [];
  }

  // Check if follow-up section should be shown
  showFollowUp(status: string): boolean {
    return status === 'No' || status === 'Query';
  }

  // Add follow-up to history
  // Docket section methods
  addVettingListFollowUp(): void {
    if (this.vettingListFollowUp.details.trim()) {
      this.vettingListFollowUpHistory.unshift({
        details: this.vettingListFollowUp.details,
        date: this.vettingListFollowUp.date,
        time: this.vettingListFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.vettingListFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  addOriginalDeedFollowUp(): void {
    if (this.originalDeedFollowUp.details.trim()) {
      this.originalDeedFollowUpHistory.unshift({
        details: this.originalDeedFollowUp.details,
        date: this.originalDeedFollowUp.date,
        time: this.originalDeedFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.originalDeedFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  addDocumentsExternalFollowUp(): void {
    if (this.documentsExternalFollowUp.details.trim()) {
      this.documentsExternalFollowUpHistory.unshift({
        details: this.documentsExternalFollowUp.details,
        date: this.documentsExternalFollowUp.date,
        time: this.documentsExternalFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.documentsExternalFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  // Mortgage section methods
  addStampDutyFollowUp(): void {
    if (this.stampDutyFollowUp.details.trim()) {
      this.stampDutyFollowUpHistory.unshift({
        details: this.stampDutyFollowUp.details,
        date: this.stampDutyFollowUp.date,
        time: this.stampDutyFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.stampDutyFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  addMortgageProcessFollowUp(): void {
    if (this.mortgageProcessFollowUp.details.trim()) {
      this.mortgageProcessFollowUpHistory.unshift({
        details: this.mortgageProcessFollowUp.details,
        date: this.mortgageProcessFollowUp.date,
        time: this.mortgageProcessFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.mortgageProcessFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  // Waiting for Report section methods
  addPendencyListFollowUp(): void {
    if (this.pendencyListFollowUp.details.trim()) {
      this.pendencyListFollowUpHistory.unshift({
        details: this.pendencyListFollowUp.details,
        date: this.pendencyListFollowUp.date,
        time: this.pendencyListFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.pendencyListFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  addPddFollowUp(): void {
    if (this.pddFollowUp.details.trim()) {
      this.pddFollowUpHistory.unshift({
        details: this.pddFollowUp.details,
        date: this.pddFollowUp.date,
        time: this.pddFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.pddFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  addOtcFollowUp(): void {
    if (this.otcFollowUp.details.trim()) {
      this.otcFollowUpHistory.unshift({
        details: this.otcFollowUp.details,
        date: this.otcFollowUp.date,
        time: this.otcFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.otcFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  // Disbursement section methods
  addDisbursementFollowUp(): void {
    if (this.disbursementFollowUp.details.trim()) {
      this.disbursementFollowUpHistory.unshift({
        details: this.disbursementFollowUp.details,
        date: this.disbursementFollowUp.date,
        time: this.disbursementFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.disbursementFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  // File Closed section methods
  addFileClosedFollowUp(): void {
    if (this.fileClosedFollowUp.details.trim()) {
      this.fileClosedFollowUpHistory.unshift({
        details: this.fileClosedFollowUp.details,
        date: this.fileClosedFollowUp.date,
        time: this.fileClosedFollowUp.time,
        timestamp: new Date().getTime()
      });

      // Reset follow-up form but keep the current date
      this.fileClosedFollowUp = {
        details: '',
        date: this.getCurrentDate(),
        time: this.getCurrentTime()
      };
    }
  }

  // Helper methods for date and time
  getCurrentDate(): string {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  getCurrentTime(): string {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  }
}
