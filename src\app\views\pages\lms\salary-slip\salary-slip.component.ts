import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';

// Sortable directive for table headers
export type SortColumn = 'date' | 'monthYear' | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: {[key: string]: SortDirection} = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number, v2: string | number) {
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-salary-slip',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FeatherIconDirective,
    NgbTooltipModule,
    NgbdSortableHeader
  ],
  templateUrl: './salary-slip.component.html',
  styleUrl: './salary-slip.component.scss'
})
export class SalarySlipComponent implements OnInit {
  // Single employee information
  employee = {
    id: 'EMP001',
    name: 'John Doe',
    department: 'IT'
  };

  // Table rows for salary entries
  rows: any[] = [];
  allRows: any[] = []; // Store all rows for filtering

  // Month and Year filter
  allMonths: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  months: string[] = []; // Will hold only available months
  years: number[] = [];
  availableMonths: number[] = []; // Will hold indices of available months
  selectedMonth: string = '';
  selectedYear: string = '';

  // View mode toggle
  viewMode: 'normal' | 'pdf' = 'normal';
  selectedSalarySlip: any = null;
  showSalarySlipDetails: boolean = false;

  // Current date info
  currentYear: number = new Date().getFullYear();
  currentMonth: number = new Date().getMonth(); // 0-based (0 = January, 11 = December)

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  constructor() { }

  ngOnInit(): void {
    // For testing purposes, we can simulate the current date
    // Uncomment these lines to test with a specific date
    // this.currentYear = 2025;
    // this.currentMonth = 4; // May (0-based)

    // Generate available years in descending order (newest first)
    for (let i = this.currentYear; i >= this.currentYear - 2; i--) {
      this.years.push(i);
    }

    // Populate available months based on selected year
    this.updateAvailableMonths();

    this.generateSalaryEntries();
  }

  // Update available months based on selected year
  updateAvailableMonths(selectedYear: string = ''): void {
    this.availableMonths = [];
    this.months = [];

    const year = selectedYear ? parseInt(selectedYear) : this.currentYear;

    // If selected year is current year, only show months up to current month
    const maxMonth = (year === this.currentYear) ? this.currentMonth : 11;

    // Add available months
    for (let i = 0; i <= maxMonth; i++) {
      this.availableMonths.push(i);
      this.months.push(this.allMonths[i]);
    }

    // If the currently selected month is no longer valid, reset it
    if (this.selectedMonth && parseInt(this.selectedMonth) > maxMonth) {
      this.selectedMonth = '';
    }
  }

  // Generate salary entries up to the current month and year
  generateSalaryEntries(): void {
    this.allRows = [];

    // Generate entries for the past 2 years up to the current month
    let entryId = 1;

    // Loop through years in descending order (newest first)
    for (let year = this.currentYear; year >= this.currentYear - 2; year--) {
      // Determine max month for this year
      const maxMonth = (year === this.currentYear) ? this.currentMonth : 11;

      // Loop through months for this year in descending order (newest first)
      for (let month = maxMonth; month >= 0; month--) {
        const date = new Date(year, month, 1);

        this.allRows.push({
          id: entryId++,
          date: this.formatDate(date),
          monthYear: this.getMonthName(month) + '/' + year,
          rawDate: date // Store the raw date for sorting
        });
      }
    }

    // Initially show all entries
    this.rows = [...this.allRows];
  }

  // Filter rows by month and year
  filterByMonthYear(): void {
    if (!this.selectedMonth && !this.selectedYear) {
      this.rows = [...this.allRows];
      return;
    }

    this.rows = this.allRows.filter(row => {
      const rowDate = row.rawDate;
      const rowMonth = rowDate.getMonth();
      const rowYear = rowDate.getFullYear();

      // If both month and year are selected
      if (this.selectedMonth && this.selectedYear) {
        return rowMonth === parseInt(this.selectedMonth) && rowYear === parseInt(this.selectedYear);
      }

      // If only month is selected
      if (this.selectedMonth && !this.selectedYear) {
        return rowMonth === parseInt(this.selectedMonth);
      }

      // If only year is selected
      if (!this.selectedMonth && this.selectedYear) {
        return rowYear === parseInt(this.selectedYear);
      }

      return true;
    });
  }

  // Handle sorting
  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      // Reset to current filtered rows without sorting
      this.rows = [...this.rows];
    } else {
      this.rows = [...this.rows].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  // Format date as DD/MM/YYYY
  formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Get month name
  getMonthName(month: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month];
  }
  // View salary slip
  viewSalarySlip(row: any): void {
    console.log('Viewing salary slip for:', row);
    this.selectedSalarySlip = row;
    this.showSalarySlipDetails = true;
    // Default to normal view when first opening
    this.viewMode = 'normal';
  }


  // Close salary slip detail view
  closeSalarySlipDetails(): void {
    this.showSalarySlipDetails = false;
    this.selectedSalarySlip = null;
  }
  // Download salary slip
  downloadSalarySlip(row: any): void {
    console.log('Downloading salary slip for:', row);
    // In a real application, this would generate and download a PDF
    alert(`Downloading salary slip for ${this.employee.name} - ${row.monthYear}`);
  }

  // Toggle view mode between normal and PDF
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'normal' ? 'pdf' : 'normal';
  }

  // Show details of the selected salary slip
  showDetails(row: any): void {
    this.selectedSalarySlip = row;
    this.showSalarySlipDetails = true;
  }

  // Hide salary slip details
  hideDetails(): void {
    this.selectedSalarySlip = null;
    this.showSalarySlipDetails = false;
  }
}
