import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Profession Type interfaces
export interface ProfessionType {
  id: string;
  name: string;
  code: string;
  category: 'technical' | 'management' | 'sales' | 'finance' | 'legal' | 'healthcare' | 'education' | 'creative' | 'other';
  description?: string;
  industry_sector?: string;
  skill_level?: 'entry' | 'intermediate' | 'senior' | 'expert' | 'executive';
  certification_required?: boolean;
  license_required?: boolean;
  experience_years_min?: number;
  experience_years_max?: number;
  education_level?: 'high_school' | 'diploma' | 'bachelor' | 'master' | 'phd' | 'professional';
  is_active: boolean;
  salary_range_min?: number;
  salary_range_max?: number;
  currency?: string;
  remote_work_eligible?: boolean;
  travel_required?: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface ProfessionTypeCreate {
  name: string;
  code: string;
  category: 'technical' | 'management' | 'sales' | 'finance' | 'legal' | 'healthcare' | 'education' | 'creative' | 'other';
  description?: string;
  industry_sector?: string;
  skill_level?: 'entry' | 'intermediate' | 'senior' | 'expert' | 'executive';
  certification_required?: boolean;
  license_required?: boolean;
  experience_years_min?: number;
  experience_years_max?: number;
  education_level?: 'high_school' | 'diploma' | 'bachelor' | 'master' | 'phd' | 'professional';
  is_active?: boolean;
  salary_range_min?: number;
  salary_range_max?: number;
  currency?: string;
  remote_work_eligible?: boolean;
  travel_required?: boolean;
}

export interface ProfessionTypeUpdate {
  name?: string;
  code?: string;
  category?: 'technical' | 'management' | 'sales' | 'finance' | 'legal' | 'healthcare' | 'education' | 'creative' | 'other';
  description?: string;
  industry_sector?: string;
  skill_level?: 'entry' | 'intermediate' | 'senior' | 'expert' | 'executive';
  certification_required?: boolean;
  license_required?: boolean;
  experience_years_min?: number;
  experience_years_max?: number;
  education_level?: 'high_school' | 'diploma' | 'bachelor' | 'master' | 'phd' | 'professional';
  is_active?: boolean;
  salary_range_min?: number;
  salary_range_max?: number;
  currency?: string;
  remote_work_eligible?: boolean;
  travel_required?: boolean;
}

export interface ProfessionTypeStatistics {
  total_profession_types: number;
  active_profession_types: number;
  inactive_profession_types: number;
  profession_types_by_category: { [category: string]: number };
  profession_types_by_skill_level: { [level: string]: number };
  profession_types_by_education: { [education: string]: number };
  popular_profession_types: ProfessionType[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ProfessionTypeService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/profession-types/`;
  private professionTypesSubject = new BehaviorSubject<ProfessionType[]>([]);
  public professionTypes$ = this.professionTypesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all profession types with optional filtering and pagination (returns APIResponse)
   */
  getProfessionTypesWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    category?: string;
    skill_level?: string;
    education_level?: string;
    industry_sector?: string;
    certification_required?: boolean;
    license_required?: boolean;
    include_deleted?: boolean;
  }): Observable<APIResponse<ProfessionType[]>> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<ProfessionType[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.professionTypesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all profession types (backward compatibility) - returns ProfessionType[] directly
   */
  getProfessionTypes(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    category?: string;
    skill_level?: string;
    education_level?: string;
    industry_sector?: string;
    certification_required?: boolean;
    license_required?: boolean;
    include_deleted?: boolean;
  }): Observable<ProfessionType[]> {
    return this.getProfessionTypesWithResponse(params).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get profession type by ID
   */
  getProfessionTypeById(id: string): Observable<APIResponse<ProfessionType>> {
    return this.http.get<APIResponse<ProfessionType>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new profession type
   */
  createProfessionType(professionType: ProfessionTypeCreate): Observable<APIResponse<ProfessionType>> {
    return this.http.post<APIResponse<ProfessionType>>(this.baseUrl, professionType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProfessionTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update profession type
   */
  updateProfessionType(id: string, professionType: ProfessionTypeUpdate): Observable<APIResponse<ProfessionType>> {
    return this.http.put<APIResponse<ProfessionType>>(`${this.baseUrl}${id}`, professionType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProfessionTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete profession type
   */
  deleteProfessionType(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProfessionTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted profession type
   */
  restoreProfessionType(id: string): Observable<APIResponse<ProfessionType>> {
    return this.http.post<APIResponse<ProfessionType>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProfessionTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get profession type statistics
   */
  getProfessionTypeStatistics(): Observable<APIResponse<ProfessionTypeStatistics>> {
    return this.http.get<APIResponse<ProfessionTypeStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload profession types
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshProfessionTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Get profession types for dropdown (simplified data)
   */
  getProfessionTypesDropdown(): Observable<{ id: string; name: string; code: string; category: string }[]> {
    return this.getProfessionTypes({ per_page: 1000, is_active: true }).pipe(
      map(professionTypes => {
        return professionTypes.map(professionType => ({
          id: professionType.id,
          name: professionType.name,
          code: professionType.code,
          category: professionType.category
        }));
      })
    );
  }

  /**
   * Refresh profession types data
   */
  refreshProfessionTypes(): void {
    this.getProfessionTypesWithResponse().subscribe();
  }

  /**
   * Clear profession types cache
   */
  clearCache(): void {
    this.professionTypesSubject.next([]);
  }

  /**
   * Get profession categories
   */
  getProfessionCategories(): { value: string; label: string; description: string }[] {
    return [
      { value: 'technical', label: 'Technical', description: 'Software development, engineering, IT roles' },
      { value: 'management', label: 'Management', description: 'Leadership and management positions' },
      { value: 'sales', label: 'Sales', description: 'Sales and business development roles' },
      { value: 'finance', label: 'Finance', description: 'Financial and accounting positions' },
      { value: 'legal', label: 'Legal', description: 'Legal and compliance roles' },
      { value: 'healthcare', label: 'Healthcare', description: 'Medical and healthcare positions' },
      { value: 'education', label: 'Education', description: 'Teaching and training roles' },
      { value: 'creative', label: 'Creative', description: 'Design, marketing, and creative positions' },
      { value: 'other', label: 'Other', description: 'Other professional categories' }
    ];
  }

  /**
   * Get skill levels
   */
  getSkillLevels(): { value: string; label: string; description: string }[] {
    return [
      { value: 'entry', label: 'Entry Level', description: '0-2 years of experience' },
      { value: 'intermediate', label: 'Intermediate', description: '2-5 years of experience' },
      { value: 'senior', label: 'Senior', description: '5-10 years of experience' },
      { value: 'expert', label: 'Expert', description: '10+ years of experience' },
      { value: 'executive', label: 'Executive', description: 'C-level and executive positions' }
    ];
  }

  /**
   * Get education levels
   */
  getEducationLevels(): { value: string; label: string; description: string }[] {
    return [
      { value: 'high_school', label: 'High School', description: 'High school diploma or equivalent' },
      { value: 'diploma', label: 'Diploma', description: 'Professional diploma or certificate' },
      { value: 'bachelor', label: 'Bachelor\'s Degree', description: 'Bachelor\'s degree' },
      { value: 'master', label: 'Master\'s Degree', description: 'Master\'s degree' },
      { value: 'phd', label: 'PhD', description: 'Doctoral degree' },
      { value: 'professional', label: 'Professional', description: 'Professional certification or license' }
    ];
  }

  /**
   * Get industry sectors
   */
  getIndustrySectors(): string[] {
    return [
      'Technology',
      'Finance',
      'Healthcare',
      'Education',
      'Manufacturing',
      'Retail',
      'Consulting',
      'Real Estate',
      'Media',
      'Government',
      'Non-Profit',
      'Energy',
      'Transportation',
      'Hospitality',
      'Agriculture'
    ];
  }

  /**
   * Get currencies
   */
  getCurrencies(): string[] {
    return [
      'USD',
      'EUR',
      'GBP',
      'INR',
      'CAD',
      'AUD',
      'SGD',
      'HKD',
      'JPY',
      'CHF',
      'AED'
    ];
  }

  /**
   * Get profession category label
   */
  getProfessionCategoryLabel(category: string): string {
    const categories = this.getProfessionCategories();
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? categoryObj.label : category;
  }

  /**
   * Get skill level label
   */
  getSkillLevelLabel(level: string): string {
    const levels = this.getSkillLevels();
    const levelObj = levels.find(l => l.value === level);
    return levelObj ? levelObj.label : level;
  }

  /**
   * Get education level label
   */
  getEducationLevelLabel(level: string): string {
    const levels = this.getEducationLevels();
    const levelObj = levels.find(l => l.value === level);
    return levelObj ? levelObj.label : level;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Profession Type service error:', error);
    
    let errorMessage = 'An error occurred while processing your request.';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
