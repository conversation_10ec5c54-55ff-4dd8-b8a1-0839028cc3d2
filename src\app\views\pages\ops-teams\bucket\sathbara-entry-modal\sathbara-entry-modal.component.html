<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Sathbara & Mutation Entry</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #sathbaraEntryForm="ngForm">
    <div class="row mb-3">
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="surveyNumber" class="form-label">Survey/Hissa No <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="surveyNumber" name="surveyNumber" [(ngModel)]="formData.surveyNumber" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="area" class="form-label">Area (Sq Mtrs) <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="area" name="area" [(ngModel)]="formData.area" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="mutationEntries" class="form-label">Mutation Entries <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="mutationEntries" name="mutationEntries" [(ngModel)]="formData.mutationEntries" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="landOwnerName" class="form-label">Names of Owners <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="landOwnerName" name="landOwnerName" [(ngModel)]="formData.landOwnerName" required>
      </div>
      <div class="col-12 mb-3">
        <label for="remarks" class="form-label">Remarks, if any</label>
        <textarea class="form-control" id="remarks" name="remarks" [(ngModel)]="formData.remarks" rows="2"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="sathbaraEntryForm.invalid" (click)="saveChanges()">Save</button>
</div>
