import { Component, OnInit, ViewChildren, QueryList, Directive, Input, Output, EventEmitter, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';
import { EmployeeService } from '../../../../../core/services/employee.service';
import { AuthService } from '../../../../../core/services/auth.service';
import { catchError, debounceTime, finalize, of, forkJoin } from 'rxjs';
import Swal from 'sweetalert2';
// Temporarily hardcode API URL to fix compilation
// import { environment } from '../../../../../../environments/environment';

// Sortable header directive
export type SortColumn = keyof LocalEmployee | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: {[key: string]: SortDirection} = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Local Employee interface for this component
export interface LocalEmployee {
  id: string;
  name: string;
  position: string;
  department: string;
  email: string;
  phone: string;
  joinDate: Date;
  // Extended fields for edit modal
  employeeCode: string;
  firstName: string;
  middleName: string;
  lastName: string;
  bloodGroup: string;
  dateOfBirth: Date;
  joiningDate: Date;
  reportingDate: Date;
  personalEmail: string;
  officeEmail: string;
  gender: string;
  maritalStatus: string;
  phoneNo: string;
  alternetNo: string;
  address: string;
  panNo: string;
  aadharNo: string;
  uanNo: string;
  esicNo: string;
  ctc: number;
  attendanceBonus: number;
  bankName: string;
  bankAccountNo: string;
  ifscNo: string;
  officeLocation: string;
  resignedStaredDate: Date | null;
  resignedEndDate: Date | null;
  pf: string;
  shiftTime: string;
  designation: string;
  designationId: string; // Store the designation UUID for form editing
  departmentId: string; // Store the department UUID for form editing
  role: string;
  subRole: string;
  subRoleId: string; // Store the sub role UUID for form editing
  isActive: boolean;
  approverCode: string;
  secondApproverCode: string;
}



@Component({
  selector: 'app-employee-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    NgbdSortableHeader,
    BreadcrumbComponent
  ],
  templateUrl: './employee-list.component.html',
  styleUrl: './employee-list.component.scss'
})
export class EmployeeListComponent implements OnInit {
  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'View Employee' }
  ];

  // Employee data
  employees: LocalEmployee[] = []; // All employees from API
  filteredEmployees: LocalEmployee[] = []; // Filtered employees
  paginatedEmployees: LocalEmployee[] = []; // Current page employees

  // Loading states
  loading = true; // Start with loading true to show loader immediately
  saving = false;
  loadingDesignations = false;
  loadingMasterData = false;

  // Cache for resolving IDs to names
  designationsCache = new Map<string, string>(); // designation_id -> designation_name
  departmentsCache = new Map<string, string>(); // department_id -> department_name
  rolesCache = new Map<string, string>(); // role_id -> role_name

  // Search and filter controls
  searchTerm = new FormControl('', { nonNullable: true });
  filterType = new FormControl('all', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 10; // Default page size
  pageSizeOptions = [5, 10, 20, 50]; // Options for the "show per page" dropdown
  totalItems = 0; // Total filtered items
  totalEmployees = 0; // Total employees from API

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // Edit employee form
  editEmployeeForm: FormGroup;
  selectedEmployee: LocalEmployee | null = null;

  // Department options
  departmentOptions: { id: string; name: string }[] = [];

  // Designation options - populated dynamically from API
  designationOptions: {id: string, name: string}[] = [];

  // Role options
  roleOptions = [
    'U',
    'UAE',
    'UEX'
  ];

  // Sub role options
  subRoleOptions: { id: string; name: string }[] = [];

  // Employee options for approver dropdowns
  allEmployeeOptions: { id: string; name: string; employee_code: string; department_id: string }[] = [];
  filteredApproverOptions: { id: string; name: string; employee_code: string; department_id: string }[] = [];
  filteredSecondApproverOptions: { id: string; name: string; employee_code: string; department_id: string }[] = [];

  // Loading states
  loadingEmployees = false;

  constructor(
    private formBuilder: FormBuilder,
    private modalService: NgbModal,
    private employeeService: EmployeeService,
    private authService: AuthService
  ) {
    this.initEditForm();
  }

  ngOnInit(): void {
    console.log('EmployeeListComponent ngOnInit - Starting initialization with department support');

    // Load designations first, then employees
    this.loadDesignationsAndEmployees();

    // Set up search with debouncing
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.page = 1;
      this.applyFiltersAndPagination();
    });

    // Set up filter type listener
    this.filterType.valueChanges.subscribe(() => {
      this.page = 1;
      this.applyFiltersAndPagination();
    });
  }

  // Load designations, departments, and roles only
  loadDesignations(): void {
    this.loadingDesignations = true;
    console.log('Loading master data (designations, departments, and roles)...');

    // Load designations, departments, and roles in parallel
    forkJoin({
      designations: this.employeeService.getDesignationsMasterData(),
      departments: this.employeeService.getDepartmentsMasterData(),
      roles: this.employeeService.getRoles()
    }).pipe(
      catchError(error => {
        console.error('Failed to load master data:', error);
        this.showErrorMessage('Failed to load master data. Some dropdown options may not display correctly.');
        return of({ designations: [], departments: [], roles: [] });
      }),
      finalize(() => {
        this.loadingDesignations = false;
      })
    ).subscribe(({ designations, departments, roles }) => {
      console.log('Master data loaded:', designations.length, 'designations,', departments.length, 'departments,', roles.length, 'roles');

      // Cache designations and populate dropdown options
      designations.forEach(designation => {
        if (designation.id && designation.name) {
          this.designationsCache.set(designation.id, designation.name);
          console.log(`Cached designation: ${designation.id} -> ${designation.name}`);
        } else {
          console.warn('Invalid designation data:', designation);
        }
      });

      // Cache departments and populate dropdown options
      departments.forEach(department => {
        if (department.id && department.name) {
          this.departmentsCache.set(department.id, department.name);
          console.log(`Cached department: ${department.id} -> ${department.name}`);
        } else {
          console.warn('Invalid department data:', department);
        }
      });

      // Cache roles and populate dropdown options
      roles.forEach(role => {
        if (role.id && role.name) {
          this.rolesCache.set(role.id, role.name);
          console.log(`Cached role: ${role.id} -> ${role.name}`);
        } else {
          console.warn('Invalid role data:', role);
        }
      });

      // Populate designation options for dropdown
      this.designationOptions = designations
        .filter(designation => designation.id && designation.name)
        .map(designation => ({
          id: designation.id,
          name: designation.name
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      // Populate department options for dropdown
      this.departmentOptions = departments
        .filter(department => department.id && department.name)
        .map(department => ({
          id: department.id,
          name: department.name
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      // Populate sub role options for dropdown
      this.subRoleOptions = roles
        .filter(role => role.id && role.name)
        .map(role => ({
          id: role.id,
          name: role.name
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      console.log('Designations cached:', this.designationsCache.size, 'entries');
      console.log('Departments cached:', this.departmentsCache.size, 'entries');
      console.log('Roles cached:', this.rolesCache.size, 'entries');
      console.log('Designation options populated:', this.designationOptions.length, 'options');
      console.log('Department options populated:', this.departmentOptions.length, 'options');
      console.log('SubRole options populated:', this.subRoleOptions.length, 'options');
    });
  }

  // Load designations, departments, and roles first, then employees
  loadDesignationsAndEmployees(): void {
    this.loading = true; // Ensure main loading is true from the start
    this.loadingDesignations = true;
    console.log('Loading master data (designations, departments, and roles)...');

    // Load designations, departments, and roles in parallel
    forkJoin({
      designations: this.employeeService.getDesignationsMasterData(),
      departments: this.employeeService.getDepartmentsMasterData(),
      roles: this.employeeService.getRoles()
    }).pipe(
      catchError(error => {
        console.error('Failed to load master data:', error);
        this.showErrorMessage('Failed to load master data. Some dropdown options may not display correctly.');
        return of({ designations: [], departments: [], roles: [] });
      }),
      finalize(() => {
        this.loadingDesignations = false;
      })
    ).subscribe(({ designations, departments, roles }) => {
      console.log('Master data loaded:', designations.length, 'designations,', departments.length, 'departments');
      console.log('Roles data received:', roles);

      // Cache designations and populate dropdown options
      designations.forEach(designation => {
        if (designation.id && designation.name) {
          this.designationsCache.set(designation.id, designation.name);
          console.log(`Cached designation: ${designation.id} -> ${designation.name}`);
        } else {
          console.warn('Invalid designation data:', designation);
        }
      });

      // Cache departments and populate dropdown options
      departments.forEach(department => {
        if (department.id && department.name) {
          this.departmentsCache.set(department.id, department.name);
          console.log(`Cached department: ${department.id} -> ${department.name}`);
        } else {
          console.warn('Invalid department data:', department);
        }
      });

      // Cache roles and populate dropdown options
      // Handle different response formats for roles
      let rolesArray: any[] = [];
      if (Array.isArray(roles)) {
        rolesArray = roles;
      } else if (roles && typeof roles === 'object') {
        const rolesObj = roles as any;
        if (rolesObj.data && Array.isArray(rolesObj.data)) {
          rolesArray = rolesObj.data;
        } else if (rolesObj.roles && Array.isArray(rolesObj.roles)) {
          rolesArray = rolesObj.roles;
        } else if (rolesObj.items && Array.isArray(rolesObj.items)) {
          rolesArray = rolesObj.items;
        }
      }

      console.log('Processing roles data:', rolesArray);

      rolesArray.forEach((role: any) => {
        if (role.id && role.name) {
          this.rolesCache.set(role.id, role.name);
          console.log(`Cached role: ${role.id} -> ${role.name}`);
        } else {
          console.warn('Invalid role data:', role);
        }
      });

      // Populate designation options for dropdown
      this.designationOptions = designations
        .filter(designation => designation.id && designation.name)
        .map(designation => ({
          id: designation.id,
          name: designation.name
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      // Populate department options for dropdown
      this.departmentOptions = departments
        .filter(department => department.id && department.name)
        .map(department => ({
          id: department.id,
          name: department.name
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      // Populate sub role options for dropdown
      this.subRoleOptions = roles
        .filter(role => role.id && role.name)
        .map(role => ({
          id: role.id,
          name: role.name
        }))
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      console.log('Designations cached:', this.designationsCache.size, 'entries');
      console.log('Departments cached:', this.departmentsCache.size, 'entries');
      console.log('Roles cached:', this.rolesCache.size, 'entries');
      console.log('Designation options populated:', this.designationOptions.length, 'options');
      console.log('Department options populated:', this.departmentOptions.length, 'options');
      console.log('SubRole options populated:', this.subRoleOptions.length, 'options');

      // Now load employees with master data available
      this.loadEmployees();

      // Also load employees for approver dropdowns
      this.loadEmployeesForDropdowns();
    });
  }

  // Load all employees for approver dropdown options
  loadEmployeesForDropdowns(): void {
    this.loadingEmployees = true;
    console.log('Loading employees for approver dropdowns...');

    this.employeeService.getAllEmployees()
      .pipe(
        catchError(error => {
          console.error('Failed to load employees for dropdowns:', error);
          this.loadingEmployees = false;
          return of([]);
        })
      )
      .subscribe({
        next: (response: any) => {
          console.log('Employees for dropdowns response:', response);

          let employees: any[] = [];
          if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
            employees = response.data;
          } else if (Array.isArray(response)) {
            employees = response;
          }

          // Transform employees to dropdown options
          this.allEmployeeOptions = employees
            .filter(emp => emp.id && emp.first_name && emp.employee_code)
            .map(emp => ({
              id: emp.id,
              name: `${emp.first_name} ${emp.last_name || ''}`.trim(),
              employee_code: emp.employee_code,
              department_id: emp.department_id || ''
            }))
            .sort((a, b) => a.name.localeCompare(b.name));

          console.log(`Loaded ${this.allEmployeeOptions.length} employees for approver dropdowns`);
          this.loadingEmployees = false;
        },
        error: (error) => {
          console.error('Error loading employees for dropdowns:', error);
          this.loadingEmployees = false;
        }
      });
  }

  // Filter employees by department for approver dropdowns
  filterEmployeesByDepartment(departmentId: string): void {
    console.log('Filtering employees by department:', departmentId);

    if (!departmentId) {
      // If no department selected, show all employees or empty list
      this.filteredApproverOptions = [];
      this.filteredSecondApproverOptions = [];
      console.log('No department selected, clearing approver options');
      return;
    }

    // Filter employees by the selected department
    const filteredEmployees = this.allEmployeeOptions.filter(emp =>
      emp.department_id === departmentId
    );

    this.filteredApproverOptions = [...filteredEmployees];
    this.filteredSecondApproverOptions = [...filteredEmployees];

    console.log(`Filtered ${filteredEmployees.length} employees for department ${departmentId}`);
  }

  // Handle department change in the form
  onDepartmentChange(): void {
    const selectedDepartmentId = this.editEmployeeForm.get('department')?.value;
    console.log('Department changed to:', selectedDepartmentId);

    // Filter approver options based on selected department
    this.filterEmployeesByDepartment(selectedDepartmentId);

    // Clear current approver selections if they don't belong to the new department
    const currentApprover = this.editEmployeeForm.get('approverCode')?.value;
    const currentSecondApprover = this.editEmployeeForm.get('secondApproverCode')?.value;

    if (currentApprover && !this.filteredApproverOptions.find(emp => emp.employee_code === currentApprover)) {
      this.editEmployeeForm.get('approverCode')?.setValue('');
      console.log('Cleared approver code as it does not belong to selected department');
    }

    if (currentSecondApprover && !this.filteredSecondApproverOptions.find(emp => emp.employee_code === currentSecondApprover)) {
      this.editEmployeeForm.get('secondApproverCode')?.setValue('');
      console.log('Cleared second approver code as it does not belong to selected department');
    }
  }

  // Load employees from API
  loadEmployees(): void {
    // Don't set loading = true here since it should already be true from the start
    // this.loading = true; // Removed to prevent resetting loading state
    console.log('Loading employees from API...');
    console.log('API URL being called:', 'https://api-bizzcorp.antllp.com/api/v1/employees/');

    this.employeeService.getAllEmployees()
      .pipe(
        catchError(error => {
          console.error('API failed:', error);
          console.error('Error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            url: error.url
          });
          this.showErrorMessage('Failed to load employees. Please check your connection and try again.');
          return of([]);
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        console.log('API Response received:', response);
        console.log('Response type:', typeof response);
        console.log('Is array:', Array.isArray(response));

        // Handle different response formats
        if (response && typeof response === 'object' && (response as any).success && (response as any).data && Array.isArray((response as any).data)) {
          // Structured API response
          const structuredResponse = response as any;
          this.employees = structuredResponse.data.map((emp: any) => this.transformApiEmployee(emp));
          this.totalEmployees = structuredResponse.data.length;
          console.log('Employees loaded:', this.employees.length, 'employees');
        } else if (Array.isArray(response)) {
          // Direct array response
          this.employees = response.map((emp: any) => this.transformApiEmployee(emp));
          this.totalEmployees = response.length;
          console.log('Employees loaded:', this.employees.length, 'employees');
        } else {
          console.error('Unexpected API response structure:', response);
          this.employees = [];
          this.totalEmployees = 0;
        }

        // Apply filters and pagination after loading data
        this.applyFiltersAndPagination();
      });
  }

  // Apply search filters and pagination
  applyFiltersAndPagination(): void {
    console.log('Applying filters and pagination - search:', this.searchTerm.value, 'page:', this.page, 'pageSize:', this.pageSize);

    // Start with all employees
    let filtered = [...this.employees];

    // Apply search filter
    const searchValue = this.searchTerm.value?.toLowerCase().trim();
    if (searchValue) {
      filtered = filtered.filter(employee =>
        employee.name.toLowerCase().includes(searchValue) ||
        employee.email.toLowerCase().includes(searchValue) ||
        employee.department.toLowerCase().includes(searchValue) ||
        employee.position.toLowerCase().includes(searchValue) ||
        employee.phone.toLowerCase().includes(searchValue) ||
        employee.employeeCode.toLowerCase().includes(searchValue)
      );
    }

    // Apply filter type (if needed for additional filtering)
    if (this.filterType.value !== 'all') {
      // Add additional filtering logic here if needed
    }

    // Sort employees in descending order by employee code (default sorting)
    filtered.sort((a, b) => {
      // Extract numeric part from employee codes for proper numeric sorting
      const getNumericPart = (code: string): number => {
        const match = code.match(/\d+/);
        return match ? parseInt(match[0], 10) : 0;
      };

      const aNum = getNumericPart(a.employeeCode);
      const bNum = getNumericPart(b.employeeCode);

      // Sort in descending order (highest numbers first)
      return bNum - aNum;
    });

    // Debug logging for sorting verification
    if (filtered.length > 0) {
      console.log('First 5 employee codes after sorting:',
        filtered.slice(0, 5).map(emp => emp.employeeCode));
    }

    // Set filtered results
    this.filteredEmployees = filtered;
    this.totalItems = filtered.length;

    // Apply pagination
    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedEmployees = filtered.slice(startIndex, endIndex);

    console.log('Filtered employees:', this.filteredEmployees.length);
    console.log('Paginated employees:', this.paginatedEmployees.length);
    console.log('Current page:', this.page, 'of', Math.ceil(this.totalItems / this.pageSize));
  }

  // Helper method to show error messages
  private showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message,
      confirmButtonText: 'OK'
    });
  }



  // Transform API employee to local Employee interface
  // Handles both BizzCorp API format and legacy formats
  private transformApiEmployee(apiEmp: any): LocalEmployee {
    // Based on the BizzCorp API response structure
    const fullName = `${this.getStringValue(apiEmp.first_name)} ${this.getStringValue(apiEmp.middle_name)} ${this.getStringValue(apiEmp.last_name)}`.trim();
    const joinDate = this.parseDate(apiEmp.joining_date);
    const dateOfBirth = this.parseDate(apiEmp.date_of_birth);

    // Debug log to see the actual API employee structure
    console.log('Transforming API employee:', {
      id: apiEmp.id,
      employee_code: apiEmp.employee_code,
      first_name: apiEmp.first_name,
      last_name: apiEmp.last_name,
      department_id: apiEmp.department_id,
      designation_id: apiEmp.designation_id,
      sub_role_id: apiEmp.sub_role_id,
      attendance_bonus: apiEmp.attendance_bonus,
      marital_status: apiEmp.marital_status,
      gender: apiEmp.gender
    });

    return {
      id: apiEmp.id || apiEmp.employee_code || 'N/A', // Use UUID id for API calls
      name: fullName || 'N/A',
      position: this.resolveDesignationName(apiEmp.designation_id),
      department: this.resolveDepartmentName(apiEmp.department_id),
      email: apiEmp.office_email || apiEmp.personal_email || 'N/A',
      phone: apiEmp.phone_no || 'N/A',
      joinDate: joinDate,

      // Extended fields - using actual BizzCorp API data with proper field mapping
      employeeCode: this.getStringValue(apiEmp.employee_code, 'N/A'),
      firstName: this.getStringValue(apiEmp.first_name),
      middleName: this.getStringValue(apiEmp.middle_name),
      lastName: this.getStringValue(apiEmp.last_name),
      bloodGroup: this.getStringValue(apiEmp.blood_group),
      dateOfBirth: dateOfBirth,
      joiningDate: joinDate,
      reportingDate: this.parseDate(apiEmp.reporting_date, joinDate),
      personalEmail: this.getStringValue(apiEmp.personal_email),
      officeEmail: this.getStringValue(apiEmp.office_email),
      gender: this.getStringValue(apiEmp.gender),
      maritalStatus: this.getStringValue(apiEmp.marital_status),
      phoneNo: this.getStringValue(apiEmp.phone_no),
      alternetNo: this.getStringValue(apiEmp.alternet_no),
      address: this.getStringValue(apiEmp.address),
      panNo: this.getStringValue(apiEmp.pan_no),
      aadharNo: this.getStringValue(apiEmp.aadhar_no),
      uanNo: this.getStringValue(apiEmp.uan_no),
      esicNo: this.getStringValue(apiEmp.esic_no),
      ctc: this.getNumberValue(apiEmp.ctc),
      attendanceBonus: this.getNumberValue(apiEmp.attendance_bonus),
      bankName: this.getStringValue(apiEmp.bank_name),
      bankAccountNo: this.getStringValue(apiEmp.bank_account_no),
      ifscNo: this.getStringValue(apiEmp.ifsc_no),
      officeLocation: this.getStringValue(apiEmp.office_location),
      resignedStaredDate: apiEmp.resigned_stared_date ? this.parseDate(apiEmp.resigned_stared_date) : null,
      resignedEndDate: apiEmp.resigned_end_date ? this.parseDate(apiEmp.resigned_end_date) : null,
      pf: this.getStringValue(apiEmp.pf),
      shiftTime: this.getStringValue(apiEmp.shift_time, '9:00 AM - 6:00 PM'),
      designation: this.resolveDesignationName(apiEmp.designation_id),
      designationId: this.getStringValue(apiEmp.designation_id), // Store the UUID for form editing
      departmentId: this.getStringValue(apiEmp.department_id), // Store the UUID for form editing
      role: this.getStringValue(apiEmp.role),
      subRole: this.resolveSubRoleName(apiEmp.sub_role_id),
      subRoleId: this.getStringValue(apiEmp.sub_role_id), // Store the UUID for form editing
      isActive: this.convertToBoolean((apiEmp as any).is_active || (apiEmp as any).user_active), // API may return either field
      approverCode: this.getStringValue(apiEmp.approver_code),
      secondApproverCode: this.getStringValue(apiEmp.second_approver_code)
    };
  }

  // Initialize edit form
  initEditForm(): void {
    this.editEmployeeForm = this.formBuilder.group({
      // Personal Information
      employeeCode: ['', Validators.required],
      firstName: ['', Validators.required],
      middleName: [''],
      lastName: ['', Validators.required],
      bloodGroup: [''],
      dateOfBirth: [''],
      joiningDate: [''],
      reportingDate: [''],

      // Contact Information
      personalEmail: ['', [Validators.email]],
      officeEmail: ['', [Validators.email]],
      gender: [''],
      maritalStatus: [''],
      phoneNo: [''],
      alternetNo: [''],
      address: [''],

      // Government IDs
      panNo: [''],
      aadharNo: [''],
      uanNo: [''],
      esicNo: [''],

      // Financial Information
      ctc: [0],
      attendanceBonus: [0],

      // Banking Information
      bankName: [''],
      bankAccountNo: [''],
      ifscNo: [''],

      // Employment Details
      officeLocation: [''],
      resignedStaredDate: [''],
      resignedEndDate: [''],
      pf: [''],
      shiftTime: [''],
      department: [''],
      designation: [''],
      role: [''],
      subRole: [''],
      isActive: [true],
      approverCode: [''],
      secondApproverCode: ['']
    });
  }

  // Refresh employees list (reload from API)
  refreshEmployeesList(): void {
    this.loading = true; // Set loading to true when manually refreshing
    this.loadEmployees();
  }

  // Check if user has permission to edit employees
  canEditEmployee(): boolean {
    const hasPermission = this.authService.hasPermission('employees:update');
    console.log('🔍 Employee Edit Permission Check:', {
      permission: 'employees:update',
      hasPermission: hasPermission,
      userPermissions: this.authService.getUserPermissions()
    });
    return hasPermission;
  }

  // Handle pagination changes
  onPageChange(page: number): void {
    this.page = page;
    this.applyFiltersAndPagination();
  }

  // Handle page size changes
  onPageSizeChange(newPageSize: number): void {
    this.pageSize = newPageSize;
    this.page = 1; // Reset to first page when changing page size
    this.applyFiltersAndPagination();
  }

  // Apply filter
  applyFilter(): void {
    this.page = 1;
    this.applyFiltersAndPagination();
  }

  // Reset filter
  resetFilter(): void {
    this.searchTerm.setValue('');
    this.filterType.setValue('all');
    this.page = 1;
    this.applyFiltersAndPagination();
  }

  // Handle sorting
  onSort(event: SortEvent): void {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== event.column) {
        header.direction = '';
      }
    });

    // Apply sorting to filtered employees
    if (event.direction === '' || event.column === '') {
      this.applyFiltersAndPagination();
      return;
    }

    this.filteredEmployees.sort((a, b) => {
      const aVal = a[event.column as keyof LocalEmployee];
      const bVal = b[event.column as keyof LocalEmployee];

      // Handle null/undefined values
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return -1;
      if (bVal == null) return 1;

      let result = 0;
      if (aVal < bVal) result = -1;
      else if (aVal > bVal) result = 1;

      return event.direction === 'asc' ? result : -result;
    });

    // Re-apply pagination after sorting
    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedEmployees = this.filteredEmployees.slice(startIndex, endIndex);
  }

  // Edit employee
  editEmployee(employee: LocalEmployee, content: TemplateRef<any>): void {
    // Check permission before allowing edit
    if (!this.canEditEmployee()) {
      Swal.fire({
        icon: 'warning',
        title: 'Access Denied',
        text: 'You do not have permission to edit employee information.',
        confirmButtonText: 'OK'
      });
      return;
    }

    console.log('Edit Employee - Designation Options Available:', this.designationOptions.length);
    console.log('Edit Employee - Designation Options:', this.designationOptions);
    console.log('Edit Employee - Employee Designation ID:', employee.designationId);

    // If designations are not loaded yet, load them first
    if (this.designationOptions.length === 0 && !this.loadingDesignations) {
      console.log('Designations not loaded, loading them first...');
      this.loadDesignations();

      // Subscribe to designation loading completion
      const checkDesignations = setInterval(() => {
        if (this.designationOptions.length > 0 || !this.loadingDesignations) {
          clearInterval(checkDesignations);
          if (this.designationOptions.length > 0) {
            console.log('Designations loaded, retrying edit employee...');
            this.editEmployee(employee, content);
          } else {
            console.warn('Failed to load designations, opening modal anyway...');
            this.openEditModal(employee, content);
          }
        }
      }, 100);

      // Timeout after 5 seconds
      setTimeout(() => {
        clearInterval(checkDesignations);
        if (this.designationOptions.length === 0) {
          console.warn('Timeout waiting for designations, opening modal anyway...');
          this.openEditModal(employee, content);
        }
      }, 5000);

      return;
    }

    this.openEditModal(employee, content);
  }

  private openEditModal(employee: LocalEmployee, content: TemplateRef<any>): void {
    console.log('Opening edit modal for employee:', employee.firstName, employee.lastName);
    console.log('Employee designation ID:', employee.designationId);
    console.log('Available designation options:', this.designationOptions.length);
    console.log('Designations cache size:', this.designationsCache.size);
    console.log('Employee maritalStatus:', employee.maritalStatus);
    console.log('Employee gender:', employee.gender);

    this.selectedEmployee = employee;

    // Verify that the employee's designation exists in our options
    const designationExists = this.designationOptions.find(d => d.id === employee.designationId);
    console.log('Employee designation exists in options:', !!designationExists, designationExists);

    if (!designationExists && employee.designationId) {
      console.warn('Employee designation ID not found in options:', employee.designationId);
      console.log('Employee designation ID type:', typeof employee.designationId);
      console.log('Available designation IDs:', this.designationOptions.map(d => ({ id: d.id, type: typeof d.id, name: d.name })));

      // Try to find by string conversion
      const designationExistsAsString = this.designationOptions.find(d => String(d.id) === String(employee.designationId));
      console.log('Designation exists with string conversion:', !!designationExistsAsString, designationExistsAsString);
    }

    // Populate form with employee data
    this.editEmployeeForm.patchValue({
      employeeCode: employee.employeeCode,
      firstName: employee.firstName,
      middleName: employee.middleName,
      lastName: employee.lastName,
      bloodGroup: employee.bloodGroup,
      dateOfBirth: this.formatDateForInput(employee.dateOfBirth),
      joiningDate: this.formatDateForInput(employee.joiningDate),
      reportingDate: this.formatDateForInput(employee.reportingDate),
      personalEmail: employee.personalEmail,
      officeEmail: employee.officeEmail,
      gender: employee.gender,
      maritalStatus: employee.maritalStatus,
      phoneNo: employee.phoneNo,
      alternetNo: employee.alternetNo,
      address: employee.address,
      panNo: employee.panNo,
      aadharNo: employee.aadharNo,
      uanNo: employee.uanNo,
      esicNo: employee.esicNo,
      ctc: employee.ctc,
      attendanceBonus: employee.attendanceBonus,
      bankName: employee.bankName,
      bankAccountNo: employee.bankAccountNo,
      ifscNo: employee.ifscNo,
      officeLocation: employee.officeLocation,
      resignedStaredDate: employee.resignedStaredDate ? this.formatDateForInput(employee.resignedStaredDate) : '',
      resignedEndDate: employee.resignedEndDate ? this.formatDateForInput(employee.resignedEndDate) : '',
      pf: employee.pf,
      shiftTime: employee.shiftTime,
      department: employee.departmentId || '', // Use department ID for form value, empty string if null/undefined
      designation: employee.designationId || '', // Use designation ID for form value, empty string if null/undefined
      role: employee.role,
      subRole: employee.subRoleId || '', // Use sub role ID for form value, empty string if null/undefined
      isActive: employee.isActive,
      approverCode: employee.approverCode,
      secondApproverCode: employee.secondApproverCode
    });

    console.log('Form populated with designation ID:', employee.designationId);
    console.log('Form populated with subRole ID:', employee.subRoleId);

    // Debug form values after patching
    console.log('Form values after patching:');
    console.log('maritalStatus:', this.editEmployeeForm.get('maritalStatus')?.value);
    console.log('gender:', this.editEmployeeForm.get('gender')?.value);
    console.log('Form value after patch:', this.editEmployeeForm.get('designation')?.value);
    console.log('SubRole form value after patch:', this.editEmployeeForm.get('subRole')?.value);
    console.log('Available designation options:', this.designationOptions);

    // Additional debugging - check if the form control value matches any option
    const formDesignationValue = this.editEmployeeForm.get('designation')?.value;
    const matchingOption = this.designationOptions.find(d => d.id === formDesignationValue);
    console.log('Form designation value matches option:', !!matchingOption, matchingOption);

    // Force update the form control if needed
    if (employee.designationId && !matchingOption) {
      console.log('Forcing form control update...');
      setTimeout(() => {
        this.editEmployeeForm.get('designation')?.setValue(employee.designationId);
        console.log('Form control updated, new value:', this.editEmployeeForm.get('designation')?.value);
      }, 100);
    }

    // Initialize approver dropdown options based on employee's department
    const employeeDepartmentId = employee.departmentId;
    if (employeeDepartmentId) {
      console.log('Filtering approver options for department:', employeeDepartmentId);
      this.filterEmployeesByDepartment(employeeDepartmentId);
    } else {
      console.log('No department assigned, clearing approver options');
      this.filteredApproverOptions = [];
      this.filteredSecondApproverOptions = [];
    }

    // Open modal
    this.modalService.open(content, {
      size: 'xl',
      scrollable: true,
      backdrop: 'static',
      keyboard: false
    }).result.then((result) => {
      console.log('Modal closed with result:', result);
    }).catch(() => {
      console.log('Modal dismissed');
    });
  }

  // Helper method to format date for input
  private formatDateForInput(date: Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }

  // Helper method to convert various types to boolean
  private convertToBoolean(value: any): boolean {
    if (typeof value === 'boolean') {
      return value;
    }
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    if (typeof value === 'number') {
      return value !== 0;
    }
    return Boolean(value);
  }

  // Helper method to safely get string value with fallback
  private getStringValue(value: any, fallback: string = ''): string {
    return (value !== null && value !== undefined && value !== '') ? String(value) : fallback;
  }

  // Helper method to safely get number value with fallback
  private getNumberValue(value: any, fallback: number = 0): number {
    const num = Number(value);
    return isNaN(num) ? fallback : num;
  }

  // Helper method to safely parse date
  private parseDate(dateValue: any, fallback: Date = new Date()): Date {
    if (!dateValue) return fallback;
    const date = new Date(dateValue);
    return isNaN(date.getTime()) ? fallback : date;
  } 

  // Helper method to resolve designation name from ID
  private resolveDesignationName(designationId: string | null | undefined): string {
    if (!designationId || designationId.trim() === '') {
      return 'Not Assigned';
    }

    const designationName = this.designationsCache.get(designationId);
    if (designationName) {
      console.log(`Resolved designation: ${designationId} -> ${designationName}`);
      return designationName;
    }

    // If not found in cache, try to load it individually
    console.warn('Designation not found in cache:', designationId);
    console.warn('Available designations in cache:', Array.from(this.designationsCache.keys()));

    // Attempt to load the designation individually
    this.loadIndividualDesignation(designationId);

    // Return a placeholder while loading
    return `Loading... (${designationId.substring(0, 8)}...)`;
  }

  // Helper method to resolve department name from ID (placeholder for future implementation)
  private resolveDepartmentName(departmentId: string | null | undefined): string {
    if (!departmentId || departmentId.trim() === '') {
      return 'Not Assigned';
    }

    const departmentName = this.departmentsCache.get(departmentId);
    if (departmentName) {
      return departmentName;
    }

    // For now, return N/A until department resolution is implemented
    return 'N/A';
  }

  // Helper method to resolve sub role name from ID
  private resolveSubRoleName(subRoleId: string | null | undefined): string {
    if (!subRoleId || subRoleId.trim() === '') {
      return 'Not Assigned';
    }

    // First check cache
    const roleName = this.rolesCache.get(subRoleId);
    if (roleName) {
      return roleName;
    }

    // If not in cache, fetch individual role and cache it
    this.employeeService.getRoleById(subRoleId).subscribe({
      next: (role) => {
        if (role && role.name) {
          this.rolesCache.set(subRoleId, role.name);
          console.log(`Cached individual role: ${subRoleId} -> ${role.name}`);
          // Trigger UI update to show the resolved name
          this.applyFiltersAndPagination();
        }
      },
      error: (error) => {
        console.warn(`Failed to fetch role ${subRoleId}:`, error);
        this.rolesCache.set(subRoleId, 'Unknown Role');
      }
    });

    // Return placeholder while loading
    return 'Loading...';
  }

  // Method to load individual designation by ID (for missing cache entries)
  private loadIndividualDesignation(designationId: string): void {
    console.log('Loading individual designation:', designationId);

    this.employeeService.getDesignationById(designationId)
      .pipe(
        catchError(error => {
          console.error('Failed to load individual designation:', designationId, error);
          return of(null);
        })
      )
      .subscribe(designation => {
        if (designation && designation.id && designation.name) {
          this.designationsCache.set(designation.id, designation.name);
          console.log('Individual designation loaded and cached:', designation.id, '->', designation.name);

          // Trigger a re-render of the employee list to show the updated designation
          this.applyFiltersAndPagination();
        }
      });
  }





  // Save employee changes
  saveEmployeeChanges(): void {
    // Double-check permission before saving
    if (!this.canEditEmployee()) {
      Swal.fire({
        icon: 'error',
        title: 'Access Denied',
        text: 'You do not have permission to update employee information.',
        confirmButtonText: 'OK'
      });
      return;
    }

    if (this.editEmployeeForm.valid && this.selectedEmployee) {
      const formData = this.editEmployeeForm.value;
      console.log('Saving employee changes:', formData);

      // Transform form data to API format - include all fields that might be required
      const apiEmployeeData: any = {
        employee_code: formData.employeeCode,
        first_name: formData.firstName,
        middle_name: formData.middleName || '',
        last_name: formData.lastName,
        blood_group: formData.bloodGroup || '',
        date_of_birth: formData.dateOfBirth || null,
        joining_date: formData.joiningDate || null,
        reporting_date: formData.reportingDate || null,
        personal_email: formData.personalEmail || '',
        office_email: formData.officeEmail || '',
        gender: formData.gender || '',
        marital_status: formData.maritalStatus || '',
        phone_no: formData.phoneNo || '',
        alternet_no: formData.alternetNo || '',
        address: formData.address || '',
        pan_no: formData.panNo || '',
        aadhar_no: formData.aadharNo || '',
        uan_no: formData.uanNo || '',
        esic_no: formData.esicNo || '',
        ctc: formData.ctc || 0,
        attendance_bonus: formData.attendanceBonus || 0,
        bank_name: formData.bankName || '',
        bank_account_no: formData.bankAccountNo || '',
        ifsc_no: formData.ifscNo || '',
        office_location: formData.officeLocation || '',
        resigned_stared_date: formData.resignedStaredDate || null,
        resigned_end_date: formData.resignedEndDate || null,
        pf: formData.pf || '',
        shift_time: formData.shiftTime || '9:00 AM - 6:00 PM',
        department_id: formData.department || null, // Use department_id for API (UUID), null if not assigned
        designation_id: formData.designation || null, // Use designation_id for API, null if not assigned
        role: formData.role || '',
        sub_role_id: formData.subRole || null, // Use sub_role_id for API, null if not assigned
        user_active: this.convertToBoolean(formData.isActive), // Backend now expects user_active
        approver_code: formData.approverCode || '',
        second_approver_code: formData.secondApproverCode || ''
      };

      // Remove null/undefined values to avoid API issues
      Object.keys(apiEmployeeData).forEach(key => {
        if (apiEmployeeData[key] === null || apiEmployeeData[key] === undefined) {
          delete apiEmployeeData[key];
        }
      });

      // Use the UUID id for API calls (not employee code)
      const employeeId = this.selectedEmployee.id;

      if (!employeeId) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Invalid employee ID. Cannot save changes.'
        });
        return;
      }

      console.log('Updating employee with UUID:', employeeId);
      console.log('Employee code:', this.selectedEmployee.employeeCode);
      console.log('Form isActive value:', formData.isActive, 'Type:', typeof formData.isActive);
      console.log('Converted isActive value:', this.convertToBoolean(formData.isActive), 'Type:', typeof this.convertToBoolean(formData.isActive));
      console.log('Department form value:', formData.department);
      console.log('Department ID being sent to API:', apiEmployeeData.department_id);
      console.log('SubRole form value:', formData.subRole);
      console.log('SubRole ID being sent to API:', apiEmployeeData.sub_role_id);
      console.log('API data being sent:', apiEmployeeData);

      // Call API to update employee using UUID
      this.saving = true;
      this.employeeService.updateEmployee(employeeId, apiEmployeeData).subscribe({
        next: (updatedEmployee: any) => {
          console.log('Employee updated successfully:', updatedEmployee);
          this.saving = false;

          // Close modal first
          this.modalService.dismissAll();

          // Show success alert and reload page
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Employee data updated successfully. The page will refresh to show the updated information.',
            timer: 2000,
            showConfirmButton: false
          }).then(() => {
            // Reload page to get fresh data (like F5)
            console.log('🔄 Reloading page to show updated employee data...');
            window.location.reload();
          });
        },
        error: (error: any) => {
          console.error('Error updating employee:', error);
          console.error('Error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            error: error.error
          });
          this.saving = false;

          let errorMessage = 'Failed to update employee data. Please try again.';
          if (error.status === 422) {
            errorMessage = 'Invalid data format. Please check all fields and try again.';
          } else if (error.status === 404) {
            errorMessage = 'Employee not found. Please refresh the page and try again.';
          }

          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: errorMessage,
            confirmButtonText: 'OK'
          });
        }
      });
    } else {
      Swal.fire({
        icon: 'warning',
        title: 'Validation Error',
        text: 'Please fill in all required fields.',
        confirmButtonText: 'OK'
      });
    }
  }


}