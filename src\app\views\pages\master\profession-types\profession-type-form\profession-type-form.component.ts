import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { ProfessionType } from '../../../../../core/services/profession-type.service';

@Component({
  selector: 'app-profession-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-briefcase me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Profession Type
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Profession Type Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ professionType?.name }}</p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: []
})
export class ProfessionTypeFormComponent {
  @Input() isEditMode = false;
  @Input() professionType: ProfessionType | null = null;

  professionTypeForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.professionTypeForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]]
    });
  }
}
