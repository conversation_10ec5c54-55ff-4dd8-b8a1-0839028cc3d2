<!-- Loading indicator while user data is being loaded -->
<div *ngIf="!userDataLoaded" class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
  <div class="text-center">
    <div class="spinner-border text-primary mb-3" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="text-muted">Loading dashboard components...</p>
  </div>
</div>

<!-- DEBUG: Role Detection Info (Remove this after fixing) -->
<!-- <div *ngIf="userDataLoaded" class="alert alert-info mb-3">
  <h6>🔍 Debug Info (Remove after fixing):</h6>
  <p><strong>User Role:</strong> {{ currentUser?.role || 'Not set' }}</p>
  <p><strong>User Permissions:</strong> {{ (currentUser?.permissions || []).length }} permissions</p>
  <p><strong>Is Admin:</strong> {{ isAdmin() }}</p>
  <p><strong>Is Manager:</strong> {{ isManager() }}</p>
  <p><strong>Is Employee:</strong> {{ isEmployee() }}</p>
  <button class="btn btn-sm btn-primary me-2" (click)="debugCurrentRole()">Debug Role</button>
  <button class="btn btn-sm btn-success" (click)="forceAdminRole()">Force Admin Role</button>
</div> -->

<!-- Dashboard components - only show when user data is loaded -->
<div *ngIf="userDataLoaded" class="row">
  <!-- Apply Leave Card -->
  <div *ngIf="showApplyLeave()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/leave.png" alt="Apply Leave" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Apply Leave</h6>
          <button class="btn btn-outline-orange fw-medium " routerLink="/lms/apply-leave">Apply Leave</button>
        </div>
      </div>
    </div>
  </div>

  <!-- HR Admin Card -->
  <div *ngIf="showHRAdmin()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/human-resource.png" alt="HR Admin" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">HR Admin</h6>
          <button class="btn btn-outline-orange fw-medium" (click)="openHrAdminModal()">HR Admin</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Reports Card -->
  <div *ngIf="showReports()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/report.png" alt="Reports" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Reports</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/dashboard/reports">Reports</button>
        </div>
      </div>
    </div>
  </div>

  <!-- View Employee Card -->
  <div *ngIf="showViewEmployee()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/employee.png" alt="View Employee" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">View Employee</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/dashboard/employeelist">Employee List</button>
        </div>
      </div>
    </div>
  </div>
  <!-- Approve Leaves Card -->
  <div *ngIf="showApproveLeaves()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/leave-approve.png" alt="Approve Leaves" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Approve Leaves</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/approve-leaves">Approve Leaves</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Attendance Card -->
  <div *ngIf="showMarkAttendance()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/attendance.png" alt="Attendance" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Mark Attendance</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/mark-attendance">Attendance</button>
        </div>
      </div>
    </div>
  </div>


  <!-- Assign Compoff Card -->
  <div *ngIf="showAssignCompoff()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/assign-compoff.png" alt="Assign Compoff" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Assign <br> Compoff</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/assign-comp-off">Assign Compoff</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Compoff Request Card -->
  <div *ngIf="showCompoffRequest()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/compoff-request.png" alt="Compoff Request" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Compoff Request</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/comp-off-request">Compoff Request</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Calendar Card -->
  <!-- <div *ngIf="showCalendar()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
           <img src="images/lms/dashboard/calendar.png" alt="Calendar" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Calendar</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/calendar">Calendar</button>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Leave Policy Card (Last) -->
  <div *ngIf="showLeavePolicy()" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
          <img src="images/lms/dashboard/leave-policy.png" alt="Leave Policy" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Leave Policy</h6>
          <button class="btn btn-outline-orange fw-medium" routerLink="/lms/leave-policy">Leave Policy</button>
        </div>
      </div>
    </div>
  </div>

</div>

<!-- HR Admin Modal -->
<ng-template #hrAdminModal let-modal>
 

   <div class="modal-header">
        <h5 class="modal-title">HR Admin - Upload Data</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
      </div>
  <div class="modal-body py-3">
    <!-- Activity Count Display -->
    <!-- <div *ngIf="activityCount && !loadingCount" class="alert alert-info py-2 mb-3">
      <div class="d-flex justify-content-between align-items-center">
        <small class="mb-0">
          <strong>New Year Activities:</strong>
          Total: {{ activityCount.total }} |
          Planned: {{ activityCount.planned }} |
          Ongoing: {{ activityCount.ongoing }} |
          Completed: {{ activityCount.completed }}
        </small>
      </div>
    </div> -->

    <form>
      <div class="mb-3">
        <label for="uploadType" class="form-label small mb-1">Upload Type</label>
        <select
          class="form-select form-select-sm"
          id="uploadType"
          [(ngModel)]="uploadType"
          name="uploadType"
        >
          <option value="">Select Upload Type</option>
          <option value="new_year_activity">Add New Year Activity</option>
          <option value="new_employee"> Add New Employee </option>

        </select>
      </div>

      <div class="mb-3">
        <label for="fileUpload" class="form-label small mb-1">Upload File</label>
        <input
          type="file"
          class="form-control form-control-sm"
          id="fileUpload"
          (change)="onFileSelected($event)"
        >
        <div class="form-text small">Supported formats: .xlsx, .csv</div>
      </div>
    </form>



    <!-- Upload Error -->
    <div *ngIf="uploadError" class="alert alert-danger mt-3">
      {{ uploadError }}

      <!-- Download Error Report Button -->
      <div *ngIf="hasValidationErrors && uploadType === 'new_employee'" class="mt-2">
        <button
          type="button"
          class="btn btn-sm btn-outline-danger"
          (click)="downloadErrorReport()"
          [disabled]="downloadingErrors">
          <span *ngIf="downloadingErrors" class="spinner-border spinner-border-sm me-1" role="status"></span>
          <i *ngIf="!downloadingErrors" data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
          {{ downloadingErrors ? 'Downloading...' : 'Download Error Report' }}
        </button>
      </div>
    </div>


  </div>
  <div class="modal-footer py-2">
    <button type="button" class="btn btn-sm btn-outline-orange me-2" (click)="downloadTemplate()" [disabled]="!uploadType || uploading">
      <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
      Download Template
    </button>
    <button type="button" class="btn btn-sm btn-primary" (click)="uploadFile()" [disabled]="!selectedFile || !uploadType || uploading">
      <span *ngIf="uploading" class="spinner-border spinner-border-sm me-1" role="status"></span>
      <i *ngIf="!uploading" data-feather="upload" class="icon-sm me-1" appFeatherIcon></i>
      {{ uploading ? 'Uploading...' : 'Upload' }}
    </button>
  </div>
</ng-template>

<!-- Assign Compoff Modal -->
<ng-template #assignCompoffModal let-modal>
  <div class="modal-header py-2">
    <h6 class="modal-title">Assign Compoff</h6>
    <button type="button" class="btn-close btn-sm" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modal-body py-3">
    <form [formGroup]="assignCompoffForm">

      <!-- Section 1: Employee List -->
      <div class="mb-4">
        <div class="mb-3">
          <label for="employeeList" class="form-label small mb-1">Select Employee</label>
          <select class="form-select form-select-sm" id="employeeList" formControlName="employeeList" [disabled]="loadingEmployees">
            <option value="">{{ loadingEmployees ? 'Loading employees...' : 'Select Employee' }}</option>
            <option *ngFor="let employee of employees" [value]="employee.id">
              {{ employee.display }}
            </option>
          </select>
          <div *ngIf="loadingEmployees" class="text-muted small mt-1">
            <i class="spinner-border spinner-border-sm me-1"></i>
            Loading employees...
          </div>
        </div>
      </div>

      <!-- Section 2: Working Date -->
      <div class="mb-4">
        
        <div class="mb-3">
          <label for="workingDate" class="form-label small mb-1">Working Date (Saturdays & Sundays only)</label>
          <div class="date-input-container" (click)="openDatePicker('workingDate')" style="cursor: pointer;">
            <input
              type="date"
              class="form-control form-control-sm"
              id="workingDate"
              formControlName="workingDate"
              #workingDateInput
            >
          </div>
          <div *ngIf="workingDateError" class="text-danger small mt-1">
            {{ workingDateError }}
          </div>
        </div>
      </div>

      <!-- Section 3: Remark -->
      <div class="mb-3">
       
        <div class="mb-3">
          <label for="remark" class="form-label small mb-1">Remark</label>
          <textarea
            class="form-control form-control-md"
            id="remark"
            formControlName="remark"
            rows="4"
            placeholder="Enter remark for assigning compoff"
          ></textarea>
        </div>
      </div>

    </form>

    <!-- Error Display -->
    <div *ngIf="assignCompoffError" class="alert alert-danger mt-3">
      {{ assignCompoffError }}
    </div>
  </div>
  <div class="modal-footer py-2">
    <button type="button" class="btn btn-sm btn-secondary me-2" (click)="modal.dismiss()">
      Cancel
    </button>
    <button type="button" class="btn btn-sm btn-primary" (click)="assignCompoff()" [disabled]="assignCompoffForm.invalid || assigningCompoff">
      <span *ngIf="assigningCompoff" class="spinner-border spinner-border-sm me-1" role="status"></span>
      <i *ngIf="!assigningCompoff" data-feather="check" class="icon-sm me-1" appFeatherIcon></i>
      {{ assigningCompoff ? 'Assigning...' : 'Assign Compoff' }}
    </button>
  </div>
</ng-template>

<!-- Compoff Request Modal -->
<ng-template #compoffRequestModal let-modal>
  <div class="modal-header py-2">
    <h6 class="modal-title">Compoff Request</h6>
    <button type="button" class="btn-close btn-sm" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modal-body py-3">
    <form [formGroup]="compoffRequestForm">
      <div class="row">
        <div class="col-12 col-md-6 mb-3" [class.has-date-value]="compoffWorkingDatePicker">
          <label for="workingDate" class="form-label">Working Date <span class="text-danger">*</span></label>
          <div class="input-group">
            <input
              class="form-control"
              placeholder="dd-mm-yyyy"
              name="workingDate"
              [(ngModel)]="compoffWorkingDatePicker"
              ngbDatepicker
              #compoffWorkingDatePickerRef="ngbDatepicker"
              [markDisabled]="compoffDateFilter"
              (dateSelect)="onCompoffDatePickerSelect($event)"
              [disabled]="submittingRequest"
              readonly
              required>
            <button
              class="input-group-text"
              type="button"
              (click)="compoffWorkingDatePickerRef.toggle()"
              [disabled]="submittingRequest">
              <i class="feather icon-calendar icon-md text-secondary"></i>
            </button>
          </div>
          <!-- <small class="text-muted mt-1">
            <i class="feather icon-info"></i>
            Only weekends and holidays are selectable for comp off requests
          </small> -->
        </div>

        <div class="col-12 col-md-6 mb-3">
          <label for="reason" class="form-label">Reason <span class="text-danger">*</span></label>
          <select class="form-select" id="reason" formControlName="reason">
            <option value="">Select Reason</option>
            <option value="Work on week off">Work on week off</option>
            <option value="Work on holiday">Work on holiday</option>
          </select>
        </div>
      </div>
    </form>

    <!-- Error Display -->
    <div *ngIf="compoffRequestError" class="alert alert-danger mt-3">
      {{ compoffRequestError }}
    </div>
  </div>
  <div class="modal-footer py-2">
    <button type="button" class="btn btn-sm btn-secondary me-2" (click)="modal.dismiss()">
      Cancel
    </button>
    <button type="button" class="btn btn-sm btn-primary" (click)="submitCompoffRequest()" [disabled]="compoffRequestForm.invalid || submittingRequest">
      <span *ngIf="submittingRequest" class="spinner-border spinner-border-sm me-1" role="status"></span>
      <i *ngIf="!submittingRequest" data-feather="send" class="icon-sm me-1" appFeatherIcon></i>
      {{ submittingRequest ? 'Submitting...' : 'Submit Request' }}
    </button>
  </div>
</ng-template>