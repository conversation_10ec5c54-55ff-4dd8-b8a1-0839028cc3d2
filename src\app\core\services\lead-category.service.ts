import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Lead Category interfaces
export interface LeadCategory {
  id: string;
  name: string;
  code: string;
  description?: string;
  parent_id?: string;
  level: number;
  hierarchy_path?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  scoring_weight: number;
  conversion_rate?: number;
  expected_value?: number;
  currency?: string;
  qualification_criteria?: QualificationCriteria;
  follow_up_rules?: FollowUpRule[];
  automation_rules?: AutomationRule[];
  tags?: string[];
  color_code?: string;
  icon?: string;
  is_active: boolean;
  is_default: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  children?: LeadCategory[];
  leads_count?: number;
}

export interface QualificationCriteria {
  budget_range?: {
    min: number;
    max: number;
    currency: string;
  };
  timeline?: {
    min_days: number;
    max_days: number;
  };
  decision_maker_level?: 'individual' | 'manager' | 'director' | 'executive' | 'board';
  company_size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  industry_sectors?: string[];
  geographic_regions?: string[];
  required_fields?: string[];
  scoring_rules?: ScoringRule[];
}

export interface ScoringRule {
  id: string;
  name: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in_range';
  value: any;
  score: number;
  weight: number;
}

export interface FollowUpRule {
  id: string;
  name: string;
  trigger: 'time_based' | 'action_based' | 'score_based' | 'status_change';
  condition: any;
  action: 'email' | 'task' | 'call' | 'meeting' | 'notification';
  delay_hours?: number;
  template_id?: string;
  assignee_id?: string;
  is_active: boolean;
}

export interface AutomationRule {
  id: string;
  name: string;
  trigger: 'lead_created' | 'score_changed' | 'status_changed' | 'field_updated';
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  is_active: boolean;
}

export interface AutomationCondition {
  field: string;
  operator: string;
  value: any;
}

export interface AutomationAction {
  type: 'assign' | 'move_category' | 'send_email' | 'create_task' | 'update_field';
  parameters: any;
}

export interface LeadCategoryCreate {
  name: string;
  code: string;
  description?: string;
  parent_id?: string;
  level: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  scoring_weight: number;
  expected_value?: number;
  currency?: string;
  qualification_criteria?: QualificationCriteria;
  follow_up_rules?: FollowUpRule[];
  automation_rules?: AutomationRule[];
  tags?: string[];
  color_code?: string;
  icon?: string;
  is_active?: boolean;
  is_default?: boolean;
  display_order?: number;
}

export interface LeadCategoryUpdate {
  name?: string;
  code?: string;
  description?: string;
  parent_id?: string;
  level?: number;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  scoring_weight?: number;
  expected_value?: number;
  currency?: string;
  qualification_criteria?: QualificationCriteria;
  follow_up_rules?: FollowUpRule[];
  automation_rules?: AutomationRule[];
  tags?: string[];
  color_code?: string;
  icon?: string;
  is_active?: boolean;
  is_default?: boolean;
  display_order?: number;
}

export interface LeadCategoryStatistics {
  total_categories: number;
  active_categories: number;
  inactive_categories: number;
  categories_by_priority: { [priority: string]: number };
  categories_by_level: { [level: string]: number };
  conversion_rates: { [category_id: string]: number };
  total_leads_by_category: { [category_id: string]: number };
  average_scoring_weight: number;
  top_performing_categories: LeadCategory[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

// Backward compatibility interface for Source (will be moved to separate service later)
export interface Source {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class LeadCategoryService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/lead-categories/`;
  private leadCategoriesSubject = new BehaviorSubject<LeadCategory[]>([]);
  public leadCategories$ = this.leadCategoriesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all lead categories with optional filtering and pagination (returns APIResponse)
   */
  getLeadCategoriesWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    priority?: string;
    level?: number;
    parent_id?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<LeadCategory[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<LeadCategory[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.leadCategoriesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all lead categories (backward compatibility) - returns LeadCategory[] directly
   */
  getLeadCategories(skip: number = 0, limit: number = 10, filter: any = {}): Observable<LeadCategory[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search || filter.name,
      is_active: filter.is_active,
      priority: filter.priority,
      level: filter.level
    };

    return this.getLeadCategoriesWithResponse(params).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  /**
   * Get lead category by ID
   */
  getLeadCategoryById(id: string): Observable<APIResponse<LeadCategory>> {
    return this.http.get<APIResponse<LeadCategory>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all active lead categories for dropdown usage
   */
  getActiveLeadCategories(): Observable<LeadCategory[]> {
    return this.getLeadCategories(0, 1000, { is_active: true });
  }

  /**
   * Get lead categories without pagination (backward compatibility)
   */
  getLeadCategoriesLegacy(filter: any = {}): Observable<LeadCategory[]> {
    return this.getLeadCategories(0, 1000, filter);
  }

  /**
   * Create new lead category
   */
  createLeadCategory(leadCategory: LeadCategoryCreate): Observable<APIResponse<LeadCategory>> {
    return this.http.post<APIResponse<LeadCategory>>(this.baseUrl, leadCategory)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadCategories();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update lead category
   */
  updateLeadCategory(id: string, leadCategory: LeadCategoryUpdate): Observable<APIResponse<LeadCategory>> {
    return this.http.put<APIResponse<LeadCategory>>(`${this.baseUrl}${id}`, leadCategory)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadCategories();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete lead category
   */
  deleteLeadCategory(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadCategories();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted lead category
   */
  restoreLeadCategory(id: string): Observable<APIResponse<LeadCategory>> {
    return this.http.post<APIResponse<LeadCategory>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadCategories();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get lead category statistics
   */
  getLeadCategoryStatistics(): Observable<APIResponse<LeadCategoryStatistics>> {
    return this.http.get<APIResponse<LeadCategoryStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload lead categories
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadCategories();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Calculate lead score based on category criteria
   */
  calculateLeadScore(categoryId: string, leadData: any): Observable<APIResponse<{ score: number; breakdown: any }>> {
    return this.http.post<APIResponse<{ score: number; breakdown: any }>>(`${this.baseUrl}${categoryId}/calculate-score`, { leadData })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get lead categories hierarchy
   */
  getLeadCategoriesHierarchy(): Observable<APIResponse<LeadCategory[]>> {
    return this.http.get<APIResponse<LeadCategory[]>>(`${this.baseUrl}hierarchy`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get lead categories for dropdown (simplified data)
   */
  getLeadCategoriesDropdown(): Observable<{ id: string; name: string; code: string; priority: string }[]> {
    return this.getLeadCategories(0, 1000, { is_active: true }).pipe(
      map(leadCategories => {
        return leadCategories.map(category => ({
          id: category.id,
          name: category.name,
          code: category.code,
          priority: category.priority
        }));
      })
    );
  }

  /**
   * Search lead categories by name or code
   */
  searchLeadCategories(query: string, limit: number = 20): Observable<LeadCategory[]> {
    return this.getLeadCategories(0, limit, { search: query });
  }

  /**
   * Refresh lead categories data
   */
  refreshLeadCategories(): void {
    this.getLeadCategoriesWithResponse().subscribe();
  }

  /**
   * Clear lead categories cache
   */
  clearCache(): void {
    this.leadCategoriesSubject.next([]);
  }

  /**
   * Get priority levels
   */
  getPriorityLevels(): { value: string; label: string; description: string; color: string }[] {
    return [
      { value: 'low', label: 'Low Priority', description: 'Low value leads with minimal urgency', color: '#6c757d' },
      { value: 'medium', label: 'Medium Priority', description: 'Standard leads with moderate potential', color: '#0d6efd' },
      { value: 'high', label: 'High Priority', description: 'High value leads requiring attention', color: '#fd7e14' },
      { value: 'critical', label: 'Critical Priority', description: 'Urgent leads with immediate action required', color: '#dc3545' }
    ];
  }

  /**
   * Get decision maker levels
   */
  getDecisionMakerLevels(): { value: string; label: string }[] {
    return [
      { value: 'individual', label: 'Individual' },
      { value: 'manager', label: 'Manager' },
      { value: 'director', label: 'Director' },
      { value: 'executive', label: 'Executive' },
      { value: 'board', label: 'Board Level' }
    ];
  }

  /**
   * Get company sizes
   */
  getCompanySizes(): { value: string; label: string }[] {
    return [
      { value: 'startup', label: 'Startup (1-10 employees)' },
      { value: 'small', label: 'Small (11-50 employees)' },
      { value: 'medium', label: 'Medium (51-200 employees)' },
      { value: 'large', label: 'Large (201-1000 employees)' },
      { value: 'enterprise', label: 'Enterprise (1000+ employees)' }
    ];
  }

  /**
   * Get currencies
   */
  getCurrencies(): string[] {
    return [
      'USD',
      'EUR',
      'GBP',
      'INR',
      'CAD',
      'AUD',
      'SGD',
      'HKD',
      'JPY',
      'CHF',
      'AED'
    ];
  }

  /**
   * Get priority label
   */
  getPriorityLabel(priority: string): string {
    const priorities = this.getPriorityLevels();
    const priorityObj = priorities.find(p => p.value === priority);
    return priorityObj ? priorityObj.label : priority;
  }

  /**
   * Get priority color
   */
  getPriorityColor(priority: string): string {
    const priorities = this.getPriorityLevels();
    const priorityObj = priorities.find(p => p.value === priority);
    return priorityObj ? priorityObj.color : '#6c757d';
  }

  /**
   * Get priority badge class
   */
  getPriorityBadgeClass(priority: string): string {
    const badgeClasses = {
      'low': 'badge bg-secondary',
      'medium': 'badge bg-primary',
      'high': 'badge bg-warning',
      'critical': 'badge bg-danger'
    };
    return badgeClasses[priority as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Build category hierarchy
   */
  buildCategoryHierarchy(categories: LeadCategory[]): LeadCategory[] {
    const categoryMap = new Map<string, LeadCategory>();
    const rootCategories: LeadCategory[] = [];

    // First pass: create map and initialize children arrays
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Second pass: build hierarchy
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;

      if (category.parent_id && categoryMap.has(category.parent_id)) {
        const parent = categoryMap.get(category.parent_id)!;
        parent.children!.push(categoryWithChildren);
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  }

  /**
   * Format expected value with currency
   */
  formatExpectedValue(value: number | undefined, currency: string = 'USD'): string {
    if (value === undefined || value === null) {
      return 'Not specified';
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  }

  /**
   * Backward compatibility methods for Source (deprecated - use SourceService instead)
   */

  /**
   * Get all sources with pagination (backward compatibility)
   */
  getSources(skip: number = 0, limit: number = 10): Observable<Source[]> {
    console.warn('getSources is deprecated - use SourceService instead');
    return of([]);
  }

  /**
   * Get all sources without pagination (backward compatibility)
   */
  getSourcesLegacy(): Observable<Source[]> {
    console.warn('getSourcesLegacy is deprecated - use SourceService instead');
    return of([]);
  }

  /**
   * Get active sources for dropdown usage (backward compatibility)
   */
  getActiveSources(): Observable<Source[]> {
    console.warn('getActiveSources is deprecated - use SourceService instead');
    return of([]);
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Lead Category service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
