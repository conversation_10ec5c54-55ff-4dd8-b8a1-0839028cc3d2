import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class SalesPermissionGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {

    console.log('🛡️ SalesPermissionGuard checking access for:', state.url);

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Get required permissions from route data
    const requiredPermissions = route.data['permissions'] as string[];
    
    console.log('🔑 Required permissions:', requiredPermissions);
    console.log('👤 User permissions:', currentUser.permissions);

    // If no permissions required, allow access to authenticated users
    if (!requiredPermissions || requiredPermissions.length === 0) {
      console.log('✅ No permission requirements, allowing access to authenticated users');
      return true;
    }

    // Check for wildcard permission (admin/superuser)
    if (currentUser.permissions?.includes('*')) {
      console.log('✅ Access granted - User has wildcard permission (*)');
      return true;
    }

    // Check if user has any of the required permissions
    const hasAnyPermission = requiredPermissions.some(permission => {
      const hasPermission = this.authService.hasPermission(permission);
      console.log(`  ${hasPermission ? '✅' : '❌'} Permission check: ${permission}`);
      return hasPermission;
    });

    if (hasAnyPermission) {
      console.log('✅ Access granted - User has required sales permissions');
      return true;
    }

    // Access denied
    console.log('❌ Access denied - Missing required sales permissions');
    console.log('📋 Required permissions:', requiredPermissions);
    console.log('🔑 User permissions:', currentUser.permissions);

    const missingPermissions = requiredPermissions.filter(perm =>
      !currentUser.permissions?.includes(perm) && !this.authService.hasPermission(perm)
    );
    console.log('🚫 Missing permissions:', missingPermissions);

    // Show user-friendly error message
    alert(`Access Denied: You need one of these permissions to access sales functionality: ${requiredPermissions.join(', ')}`);
    
    // Redirect to dashboard
    this.router.navigate(['/dashboard']);
    return false;
  }
}
