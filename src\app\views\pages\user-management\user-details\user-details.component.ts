import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import { User, UserAction } from '../../../../core/models/user.model';
import { catchError, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FeatherIconDirective
  ],
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss']
})
export class UserDetailsComponent implements OnInit {
  // Data
  user: User | null = null;
  userId: string | null = null;

  // Loading states
  loading = false;
  deleting = false;
  restoring = false;
  activating = false;

  // Tabs
  activeTab: 'overview' | 'roles' | 'activity' = 'overview';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.userId = params['id'];
        this.loadUser();
      } else {
        this.router.navigate(['/user-management/users']);
      }
    });
  }

  /**
   * Load user data
   */
  private loadUser(): void {
    if (!this.userId) return;

    this.loading = true;
    this.userService.getUserById(this.userId)
      .pipe(
        catchError(error => {
          console.error('Failed to load user:', error);
          this.showErrorMessage('Failed to load user data. Please try again.');
          this.router.navigate(['/user-management/users']);
          return of(null);
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(user => {
        if (user) {
          this.user = user;
        }
      });
  }

  /**
   * Set active tab
   */
  setActiveTab(tab: 'overview' | 'roles' | 'activity'): void {
    this.activeTab = tab;
  }

  /**
   * Edit user
   */
  editUser(): void {
    if (this.user) {
      this.router.navigate(['/user-management/users', this.user.id, 'edit']);
    }
  }

  /**
   * Delete user
   */
  deleteUser(): void {
    if (!this.user) return;

    Swal.fire({
      title: 'Delete User',
      text: `Are you sure you want to delete ${this.user.full_name || this.user.email}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed && this.user) {
        this.performDeleteUser();
      }
    });
  }

  /**
   * Perform user deletion
   */
  private performDeleteUser(): void {
    if (!this.user) return;

    this.deleting = true;
    this.userService.softDeleteUser(this.user.id)
      .pipe(
        catchError(error => {
          console.error('Failed to delete user:', error);
          this.showErrorMessage('Failed to delete user. Please try again.');
          return of(false);
        }),
        finalize(() => {
          this.deleting = false;
        })
      )
      .subscribe(success => {
        if (success) {
          this.showSuccessMessage('User deleted successfully.');
          this.router.navigate(['/user-management/users']);
        }
      });
  }

  /**
   * Restore user
   */
  restoreUser(): void {
    if (!this.user) return;

    this.restoring = true;
    this.userService.restoreUser(this.user.id)
      .pipe(
        catchError(error => {
          console.error('Failed to restore user:', error);
          this.showErrorMessage('Failed to restore user. Please try again.');
          return of(null);
        }),
        finalize(() => {
          this.restoring = false;
        })
      )
      .subscribe(restoredUser => {
        if (restoredUser) {
          this.user = restoredUser;
          this.showSuccessMessage('User restored successfully.');
        }
      });
  }

  /**
   * Toggle user active status
   */
  toggleUserStatus(): void {
    if (!this.user) return;

    const newStatus = !this.user.is_active;
    const action = newStatus ? 'activate' : 'deactivate';

    Swal.fire({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} User`,
      text: `Are you sure you want to ${action} ${this.user.full_name || this.user.email}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: newStatus ? '#198754' : '#ffc107',
      cancelButtonColor: '#6c757d',
      confirmButtonText: `Yes, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.performToggleStatus(newStatus);
      }
    });
  }

  /**
   * Perform status toggle
   */
  private performToggleStatus(isActive: boolean): void {
    if (!this.user) return;

    this.activating = true;
    this.userService.updateUser(this.user.id, { is_active: isActive })
      .pipe(
        catchError(error => {
          console.error('Failed to update user status:', error);
          this.showErrorMessage('Failed to update user status. Please try again.');
          return of(null);
        }),
        finalize(() => {
          this.activating = false;
        })
      )
      .subscribe(updatedUser => {
        if (updatedUser) {
          this.user = updatedUser;
          this.showSuccessMessage(`User ${isActive ? 'activated' : 'deactivated'} successfully.`);
        }
      });
  }

  /**
   * Navigate back to user list
   */
  goBack(): void {
    this.router.navigate(['/user-management/users']);
  }

  /**
   * Get user status badge class
   */
  getUserStatusClass(): string {
    if (!this.user) return 'bg-secondary';
    
    if (this.user.is_deleted) return 'bg-danger';
    if (this.user.is_active) return 'bg-success';
    return 'bg-warning';
  }

  /**
   * Get user status text
   */
  getUserStatusText(): string {
    if (!this.user) return 'Unknown';
    
    if (this.user.is_deleted) return 'Deleted';
    if (this.user.is_active) return 'Active';
    return 'Inactive';
  }

  /**
   * Get user role badge class
   */
  getUserRoleClass(): string {
    if (!this.user) return 'bg-secondary';
    return this.user.is_superuser ? 'bg-danger' : 'bg-secondary';
  }

  /**
   * Get user role text
   */
  getUserRoleText(): string {
    if (!this.user) return 'Unknown';
    return this.user.is_superuser ? 'Super User' : 'Regular User';
  }

  /**
   * Get user initials for avatar
   */
  getUserInitials(): string {
    if (!this.user) return 'U';
    
    if (this.user.first_name && this.user.last_name) {
      return (this.user.first_name[0] + this.user.last_name[0]).toUpperCase();
    }
    
    return this.user.email[0].toUpperCase();
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Get relative time
   */
  getRelativeTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  }

  /**
   * Check if user can perform action
   */
  canPerformAction(action: UserAction): boolean {
    switch (action) {
      case 'view':
        return this.authService.hasPermission('users:read');
      case 'edit':
        return this.authService.hasPermission('users:update');
      case 'delete':
        return this.authService.hasPermission('users:delete');
      case 'restore':
        return this.authService.hasPermission('users:update');
      case 'activate':
      case 'deactivate':
        return this.authService.hasPermission('users:update');
      default:
        return false;
    }
  }

  /**
   * Show success message
   */
  private showSuccessMessage(message: string): void {
    Swal.fire({
      icon: 'success',
      title: 'Success!',
      text: message,
      timer: 3000,
      showConfirmButton: false
    });
  }

  /**
   * Show error message
   */
  private showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error!',
      text: message,
      confirmButtonText: 'OK'
    });
  }
}
