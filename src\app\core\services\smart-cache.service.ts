import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';

export interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry: number;
  key: string;
}

export interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum cache size
  enableLogging?: boolean;
}

/**
 * Smart Cache Service
 * 
 * Provides intelligent caching with TTL, size limits, and automatic cleanup.
 * Optimized for master data and frequently accessed API responses.
 */
@Injectable({
  providedIn: 'root'
})
export class SmartCacheService {
  private cache = new Map<string, CacheItem<any>>();
  private cacheStats = new BehaviorSubject({
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0
  });

  private readonly defaultConfig: CacheConfig = {
    ttl: 300000, // 5 minutes default
    maxSize: 100, // 100 items max
    enableLogging: false
  };

  private stats = {
    hits: 0,
    misses: 0
  };

  constructor() {
    // Cleanup expired items every minute
    setInterval(() => this.cleanupExpired(), 60000);
    
    console.log('🧠 SmartCacheService: Initialized with intelligent caching');
  }

  /**
   * Get data from cache or fetch from source
   */
  get<T>(
    key: string, 
    fetcher: () => Observable<T>, 
    config: CacheConfig = {}
  ): Observable<T> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const cacheItem = this.cache.get(key);

    // Check if cached item exists and is not expired
    if (cacheItem && Date.now() < cacheItem.expiry) {
      this.stats.hits++;
      this.updateStats();
      
      if (finalConfig.enableLogging) {
        console.log(`🎯 Cache HIT for key: ${key}`);
      }
      
      return of(cacheItem.data);
    }

    // Cache miss - fetch from source
    this.stats.misses++;
    this.updateStats();
    
    if (finalConfig.enableLogging) {
      console.log(`❌ Cache MISS for key: ${key}, fetching from source`);
    }

    return fetcher().pipe(
      tap(data => {
        this.set(key, data, finalConfig);
      }),
      catchError(error => {
        console.error(`❌ SmartCacheService: Error fetching data for key ${key}:`, error);
        throw error;
      })
    );
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, config: CacheConfig = {}): void {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    // Check cache size limit
    if (this.cache.size >= finalConfig.maxSize!) {
      this.evictOldest();
    }

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + finalConfig.ttl!,
      key
    };

    this.cache.set(key, cacheItem);
    this.updateStats();

    if (finalConfig.enableLogging) {
      console.log(`💾 Cached data for key: ${key}, expires in ${finalConfig.ttl! / 1000}s`);
    }
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const cacheItem = this.cache.get(key);
    return cacheItem ? Date.now() < cacheItem.expiry : false;
  }

  /**
   * Remove specific key from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    this.updateStats();
    return deleted;
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0 };
    this.updateStats();
    console.log('🧹 SmartCacheService: Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return this.cacheStats.asObservable();
  }

  /**
   * Get current cache info
   */
  getCacheInfo() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      stats: this.stats,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    };
  }

  /**
   * Cleanup expired items
   */
  private cleanupExpired(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now >= item.expiry) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 SmartCacheService: Cleaned up ${cleanedCount} expired items`);
      this.updateStats();
    }
  }

  /**
   * Evict oldest item when cache is full
   */
  private evictOldest(): void {
    let oldestKey = '';
    let oldestTimestamp = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`🗑️ SmartCacheService: Evicted oldest item: ${oldestKey}`);
    }
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    const hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) || 0;
    
    this.cacheStats.next({
      hits: this.stats.hits,
      misses: this.stats.misses,
      size: this.cache.size,
      hitRate: Math.round(hitRate * 100) / 100
    });
  }

  /**
   * Preload data into cache
   */
  preload<T>(key: string, fetcher: () => Observable<T>, config: CacheConfig = {}): void {
    if (!this.has(key)) {
      this.get(key, fetcher, { ...config, enableLogging: true }).subscribe({
        next: () => console.log(`🔮 Preloaded: ${key}`),
        error: (error) => console.warn(`⚠️ Preload failed for ${key}:`, error)
      });
    }
  }

  /**
   * Cache master data with long TTL
   */
  cacheMasterData<T>(key: string, fetcher: () => Observable<T>): Observable<T> {
    return this.get(key, fetcher, {
      ttl: 3600000, // 1 hour
      enableLogging: true
    });
  }

  /**
   * Cache user data with short TTL
   */
  cacheUserData<T>(key: string, fetcher: () => Observable<T>): Observable<T> {
    return this.get(key, fetcher, {
      ttl: 300000, // 5 minutes
      enableLogging: true
    });
  }
}
