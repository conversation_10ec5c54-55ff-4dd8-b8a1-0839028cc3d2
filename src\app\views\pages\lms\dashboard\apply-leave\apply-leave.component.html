<div class="row">
    <div class="col-12 mb-4">
       <div class="card modern-table-card">
      <div class="card-body">
      <h4 class="card-title">Apply Leave</h4>
           <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

      <!-- Debug Panel (only in development) -->
      <!-- <div class="alert alert-info mt-3" *ngIf="!loading && debugMode">
        <h6><i class="fas fa-info-circle"></i> Debug Information</h6>
        <div class="row">
          <div class="col-md-6">
            <small>
              <strong>API Base URL:</strong> {{ environment.apiUrl }}/api/v1/leave<br>
              <strong>Leave Types:</strong> {{ leaveTypes.length }} loaded<br>
              <strong>Leave Balances:</strong> {{ leaveBalances.length }} loaded<br>
              <strong>My Leaves:</strong> {{ myLeaves.length }} loaded<br>
              <strong>Balance Summary:</strong> {{ leaveBalanceSummary ? 'Available' : 'Not Available' }}<br>
              <strong>Current User ID:</strong> {{ authService.currentUserValue?.id || 'Not available' }}<br>
              <strong>User Name:</strong> {{ authService.currentUserValue?.name || 'Not available' }}
            </small>
          </div> -->
          <!-- <div class="col-md-6">
            <small>
              <strong>API Status:</strong><br>
              <span class="badge" [class]="apiStatus['types'] ? 'bg-success' : 'bg-danger'">Types: {{ apiStatus['types'] ? 'OK' : 'FAIL' }}</span>
              <span class="badge ms-1" [class]="apiStatus['balance'] ? 'bg-success' : 'bg-danger'">Balance: {{ apiStatus['balance'] ? 'OK' : 'FAIL' }}</span>
              <span class="badge ms-1" [class]="apiStatus['applications'] ? 'bg-success' : 'bg-danger'">Apps: {{ apiStatus['applications'] ? 'OK' : 'FAIL' }}</span>
            </small>
          </div> -->
        <!-- </div> -->
        <!-- <div class="mt-2">
          <button class="btn btn-sm btn-outline-primary me-2" (click)="testSpecificEndpoint('/types')">Test Types</button>
          <button class="btn btn-sm btn-outline-primary me-2" (click)="testSpecificEndpoint('/balance')">Test Balance</button>
          <button class="btn btn-sm btn-outline-primary me-2" (click)="testSpecificEndpoint('/')">Test Apps</button>
          <button class="btn btn-sm btn-outline-warning me-2" (click)="runAdvancedApiTests()">Advanced Tests</button>
          <button class="btn btn-sm btn-outline-info me-2" (click)="debugCurrentState()">Debug State</button>
          <button class="btn btn-sm btn-outline-secondary" (click)="loadLeaveData()">Reload Data</button>
        </div>
        <small class="text-muted d-block mt-2"><em>Check browser console for detailed API logs</em></small>
      </div> -->

      <!-- Holiday Test Panel (for debugging) -->
      <!-- <div class="alert alert-warning mt-3" *ngIf="debugMode">
        <h6><i class="fas fa-calendar-alt"></i> Holiday API Test</h6>
        <p class="mb-2">Test the holiday API integration and datepicker functionality:</p>
        <button class="btn btn-sm btn-outline-primary me-2" (click)="testHolidayIntegration()">Test Holiday API</button>
        <button class="btn btn-sm btn-outline-info me-2" (click)="loadHolidaysForDatepicker()">Reload Holidays</button>
        <small class="text-muted d-block mt-2">
          <strong>Current holiday dates loaded:</strong> {{ holidayDates.length }} dates<br>
          <strong>Sample dates:</strong> {{ holidayDates.slice(0, 3).join(', ') }}{{ holidayDates.length > 3 ? '...' : '' }}
        </small>
      </div> -->

      <!-- Loading State -->
      <div *ngIf="loading" class="text-center my-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading leave data...</p>
      </div>

      <!-- Error State (hidden when modal is open) -->
      <div *ngIf="error && !loading && !isModalOpen()" class="alert alert-danger alert-dismissible mt-3" role="alert">
        {{ error }}
        <div class="mt-2">
          <button type="button" class="btn btn-link p-0 me-3" (click)="loadLeaveData()">Try again</button>
          <button type="button" class="btn btn-link p-0" (click)="clearErrors()">Dismiss</button>
        </div>
        <button type="button" class="btn-close" aria-label="Close" (click)="clearErrors()"></button>
      </div>

      <div *ngIf="!loading && !error" class="row">
         <!-- Dynamic Leave Type Cards -->
    <div *ngFor="let balance of leaveBalances" class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <div class="d-flex flex-column align-items-center">
            <img [src]="getLeaveIcon(balance.leaveType)" [alt]="balance.leaveFullName" class="mb-3 img-fluid lms-card-icon">
            <div class="leavecount d-flex align-items-center">
              <h5 class="card-title mb-1 me-2">{{ balance.leaveType }}</h5>
              <span class="fw-bold">
                <span *ngIf="balance.totalAllowed > 0">
                  (<span class="text-danger">{{ balance.taken }}</span>/<span class="text-primary">{{ balance.totalAllowed }}</span>)
                </span>
                <span *ngIf="balance.totalAllowed === 0">
                  (<span class="text-danger">{{ balance.taken }}</span>)
                </span>
              </span>
            </div>

            <p class="text-muted mb-2">{{ balance.leaveFullName }}</p>



            <button class="btn btn-outline-orange" (click)="openLeaveModal(balance.leaveType, balance.leaveFullName)">Apply Leave</button>
          </div>
        </div>
      </div>
    </div>
      </div>
      </div>
      </div>

    <!-- Leave Applications Table -->
    <div class="row mt-4" id="leave-details-section">
      <div class="col-12">
        <div class="card modern-table-card">
          <div class="card-body">
            <h5 class="card-title mb-4">Leave Details</h5>

            <!-- Table Controls -->
            <div class="row mb-3">
              <div class="col-12 col-md-4 col-lg-2 mb-2">
                <label for="pageSize" class="form-label small">Show entries:</label>
                <select
                  id="pageSize"
                  class="form-select form-select-sm"
                  [(ngModel)]="pageSize"
                  (change)="onPageSizeChange()"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                </select>
              </div>

              <div class="col-12 col-md-4 col-lg-2 mb-2">
                <label for="statusFilter" class="form-label small">Filter by Status:</label>
                <select
                  id="statusFilter"
                  class="form-select form-select-sm"
                  [(ngModel)]="statusFilter"
                  (change)="onStatusFilterChange()"
                >
                  <option value="all">All Status</option>
                  <option value="Approved">Approved</option>
                  <option value="Pending">Pending</option>
                  <option value="Rejected">Rejected</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>

              <div class="col-12 col-md-4 col-lg-2 mb-2">
                <label for="searchBox" class="form-label small">Search:</label>
                <input
                  type="text"
                  id="searchBox"
                  class="form-control form-control-sm"
                  placeholder="Search applications..."
                  [(ngModel)]="searchTerm"
                  (input)="onSearchChange()"
                >
              </div>
            </div>

            <!-- NGX Datatable -->
            <div class="table-responsive-wrapper">
              <ngx-datatable
                #leaveApplicationsTable
                class="bootstrap"
                [rows]="filteredApplications"
                [columnMode]="ColumnMode.force"
                [headerHeight]="50"
                [rowHeight]="'auto'"
                [scrollbarH]="true"
                [footerHeight]="0"
                [limit]="pageSize"
                [externalPaging]="false"
                [sortType]="SortType.single"
              >

                <!-- Employee Code Column -->
                <ngx-datatable-column name="Employee Code" [width]="120" [sortable]="true" prop="employeeCode">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span class="fw-bold text-primary">{{ row.employeeCode }}</span>
                  </ng-template>
                </ngx-datatable-column>

                <!-- Employee Name Column -->
                <ngx-datatable-column name="Employee Name" [width]="150" [sortable]="true" prop="employeeName">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ row.employeeName }}
                  </ng-template>
                </ngx-datatable-column>

                <!-- Leave Type Column -->
                <ngx-datatable-column name="Leave Type" [width]="120" [sortable]="true" prop="leaveType">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span class="badge" [ngClass]="getLeaveTypeBadgeClass(row.leaveType)">{{ row.leaveType }}</span>
                  </ng-template>
                </ngx-datatable-column>

                <!-- Start Date Column -->
                <ngx-datatable-column name="Start Date" [width]="120" [sortable]="true" prop="startDate">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ row.startDate | date:'dd/MM/yyyy' }}
                  </ng-template>
                </ngx-datatable-column>

                <!-- End Date Column -->
                <ngx-datatable-column name="End Date" [width]="120" [sortable]="true" prop="endDate">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ row.endDate | date:'dd/MM/yyyy' }}
                  </ng-template>
                </ngx-datatable-column>

                <!-- Total Leaves Column -->
                <ngx-datatable-column name="Total Leaves" [width]="120" [sortable]="true" prop="totalLeaves">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span class="fw-bold">{{ row.totalLeaves }}</span>
                  </ng-template>
                </ngx-datatable-column>

                <!-- Reason Column -->
                <ngx-datatable-column name="Reason" [width]="200" [sortable]="true" prop="reason">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span [title]="row.reason">
                      {{ row.reason.length > 30 ? (row.reason | slice:0:30) + '...' : row.reason }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>

                <!-- Status Column -->
                <ngx-datatable-column name="Status" [width]="120" [sortable]="true" prop="status">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.status)">{{ row.status }}</span>
                  </ng-template>
                </ngx-datatable-column>

                <!-- Approved Date Column -->
                <ngx-datatable-column name="Approved Date" [width]="130" [sortable]="true" prop="approvedDate">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ row.approvedDate ? (row.approvedDate | date:'dd/MM/yyyy') : '-' }}
                  </ng-template>
                </ngx-datatable-column>

                <!-- Approver Name Column -->
                <ngx-datatable-column name="Approver Name" [width]="150" [sortable]="true" prop="approverName">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    {{ row.approverName || '-' }}
                  </ng-template>
                </ngx-datatable-column>

                <!-- Action Column -->
                <ngx-datatable-column name="Action" [width]="200" [sortable]="false">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <div class="action-buttons-container">
                      <!-- Pending Leave Actions -->
                      <div *ngIf="row.status === 'Pending'" class="btn-group" role="group">
                        <!-- Cancel button for pending leaves -->
                        <button
                          type="button"
                          class="btn btn-sm btn-primary text-white"
                          (click)="cancelLeaveApplication(row)"
                          title="Cancel Leave Application"
                        >
                          <i data-feather="x-circle" class="icon-sm me-1" appFeatherIcon></i>
                          Cancel
                        </button>

                        <!-- Admin Actions -->
                        <div *ngIf="isAdmin()" class="btn-group ms-1" role="group">
                          <!-- Approve button -->
                          <!-- <button
                            type="button"
                            class="btn btn-sm btn-success"
                            (click)="approveApplication(row)"
                            title="Approve Leave"
                          >
                            <i data-feather="check" class="icon-sm me-1" appFeatherIcon></i>
                            Approve
                          </button> -->

                          <!-- Reject button -->
                          <!-- <button
                            type="button"
                            class="btn btn-sm btn-danger"
                            (click)="rejectApplication(row)"
                            title="Reject Leave"
                          >
                            <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i>
                            Reject
                          </button> -->
                        </div>
                      </div>

                      <!-- Non-Pending Leave Actions -->
                      <div *ngIf="row.status !== 'Pending' && row.status !== 'Cancelled'" class="d-flex align-items-center">
                        <!-- View button for approved/rejected leaves -->
                        <button
                          type="button"
                          class="btn btn-sm btn-outline-primary"
                          (click)="viewLeaveDetails(row)"
                          title="View Details"
                        >
                          <i data-feather="eye" class="icon-sm me-1" appFeatherIcon></i>
                          View
                        </button>
                      </div>

                      <!-- Cancelled Leave Actions -->
                      <div *ngIf="row.status === 'Cancelled'" class="d-flex align-items-center">
                        <!-- View button -->
                        <!-- <button
                          type="button"
                          class="btn btn-sm btn-outline-primary me-2"
                          (click)="viewLeaveDetails(row)"
                          title="View Details"
                        > -->
                          <!-- <i data-feather="eye" class="icon-sm me-1" appFeatherIcon></i>
                          View
                        </button> -->

                        <!-- Status badge -->
                        <span class="badge fs-6 px-2 py-1" [ngClass]="getStatusClass(row.status)">
                          <i [attr.data-feather]="getStatusIcon(row.status)" class="icon-sm me-1" appFeatherIcon></i>
                          {{ row.status }}
                        </span>
                      </div>
                    </div>
                  </ng-template>
                </ngx-datatable-column>

              </ngx-datatable>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="filteredApplications.length > 0">
              <div class="text-muted">
                Showing {{ getStartIndex() + 1 }} to {{ getEndIndex() }} of {{ filteredApplications.length }} entries
                <br>
                <small>Current Page: {{ currentPage }} | Total Pages: {{ getTotalPages() }}</small>
              </div>
              <!-- Test Button -->
              <!-- <button
                type="button"
                class="btn btn-sm btn-info me-2"
                (click)="goToPage(2)"
                style="cursor: pointer;"
              >
                Test Go to Page 2
              </button> -->

              <nav aria-label="Table pagination">
                <ul class="pagination pagination-separated pagination-sm mb-0">
                  <li class="page-item" [class.disabled]="currentPage === 1">
                    <button
                      type="button"
                      class="page-link"
                      (click)="goToPage(currentPage - 1)"
                      [disabled]="currentPage === 1"
                      style="cursor: pointer; z-index: 1;"
                    >
                      Previous
                    </button>
                  </li>
                  <li class="page-item"
                      *ngFor="let page of getPageNumbers(); trackBy: trackByPageNumber"
                      [class.active]="page === currentPage">
                    <button
                      type="button"
                      class="page-link"
                      (click)="goToPage(page)"
                      [attr.data-page]="page"
                      style="cursor: pointer; z-index: 1; min-width: 40px;"
                      [style.background-color]="page === currentPage ? '#007bff' : ''"
                      [style.color]="page === currentPage ? 'white' : ''"
                      [style.border-color]="page === currentPage ? '#007bff' : ''"
                    >
                      {{ page }}
                    </button>
                  </li>
                  <li class="page-item" [class.disabled]="currentPage === getTotalPages()">
                    <button
                      type="button"
                      class="page-link"
                      (click)="goToPage(currentPage + 1)"
                      [disabled]="currentPage === getTotalPages()"
                      style="cursor: pointer; z-index: 1;"
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Leave Type Setup Section (Admin Only) -->
    <!-- <div class="row mt-4" *ngIf="isAdmin()">
      <div class="col-12">
        <div class="card modern-table-card">
          <div class="card-body"> -->
            <!-- <div class="d-flex justify-content-between align-items-center mb-4">
              <div>
                <h5 class="card-title mb-1">Leave Type Setup</h5>
                <p class="text-muted mb-0 small">Create and manage special leave types (LWP & OD)</p>
              </div>
              <div class="btn-group" role="group">
                <button class="btn btn-primary btn-sm" (click)="createSpecialLeaveTypes()">
                  <i class="fas fa-plus me-1"></i>
                  Create LWP & OD Types
                </button>
                <button class="btn btn-outline-secondary btn-sm" (click)="testSpecificEndpoint('/types')">
                  <i class="fas fa-vial me-1"></i>
                  Test API
                </button>
                <button class="btn btn-outline-info btn-sm" (click)="testHolidayIntegration()">
                  <i class="fas fa-calendar-alt me-1"></i>
                  Test Holidays
                </button>
                <button class="btn btn-outline-warning btn-sm" (click)="testODHolidayValidation()">
                  <i class="fas fa-bug me-1"></i>
                  Test Holiday Validation
                </button>
              </div>
            </div> -->


          <!-- </div>
        </div>
     </div>
    </div>-->

    <!-- Leave Type Management Section (Admin Only) -->
    <!-- <div class="row mt-4" *ngIf="isAdmin()">
      <div class="col-12">
        <div class="card modern-table-card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h5 class="card-title mb-0">Leave Type Management</h5>
              <button class="btn btn-primary btn-sm" (click)="addNewLeaveType()">
                <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                Add Leave Type
              </button>
            </div> -->

            <!-- Leave Types Table -->
            <!-- <div class="table-responsive" *ngIf="leaveTypes.length > 0">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Max Days/Year</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let leaveType of leaveTypes">
                    <td>{{ leaveType.id }}</td>
                    <td>
                      <span class="badge" [ngClass]="getLeaveTypeBadgeClass(leaveType.name || '')">
                        {{ leaveType.name }}
                      </span>
                    </td>
                    <td>{{ leaveType.description || '-' }}</td>
                    <td>{{ leaveType.max_days_per_year || 'Unlimited' }}</td>
                    <td>
                      <span class="badge" [ngClass]="leaveType.is_active ? 'bg-success' : 'bg-secondary'">
                        {{ leaveType.is_active ? 'Active' : 'Inactive' }}
                      </span>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <button
                          type="button"
                          class="btn btn-sm btn-outline-primary"
                          (click)="editLeaveType(leaveType)"
                          title="Edit Leave Type"
                        >
                          <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                        </button>
                        <button
                          type="button"
                          class="btn btn-sm btn-outline-danger"
                          (click)="deleteLeaveType(leaveType.id!)"
                          title="Delete Leave Type"
                          [disabled]="!leaveType.id"
                        >
                          <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div> -->

            <!-- No leave types message -->
            <!-- <div *ngIf="leaveTypes.length === 0" class="text-center py-4">
              <i data-feather="inbox" class="icon-lg text-muted mb-2" appFeatherIcon></i>
              <p class="text-muted">No leave types found. Add a new leave type to get started.</p>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>

  <ng-template #leaveModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title">Apply for {{ selectedLeaveFullName }}</h5>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')" [disabled]="submitting"></button>
    </div>
    <div class="modal-body">
      <!-- Error Message -->
      <div *ngIf="error" class="alert alert-danger alert-dismissible" role="alert">
        {{ error }}
        <button type="button" class="btn-close" aria-label="Close" (click)="clearErrors()"></button>
      </div>

      <form>
        <div class="row">
          <div class="col-12 mb-3">
            <label for="leaveType" class="form-label">Leave Type</label>
            <input type="text" class="form-control" id="leaveType" [value]="selectedLeaveFullName" readonly>
          </div>

          <div class="col-12 col-md-6 mb-3" [class.has-date-value]="fromDate">
            <label for="fromDate" class="form-label">From Date <span class="text-danger">*</span></label>

            <div class="input-group">
              <input
                class="form-control"
                placeholder="dd-mm-yyyy"
                name="fromDate"
                [(ngModel)]="fromDatePicker"
                ngbDatepicker
                #fromDatePickerRef="ngbDatepicker"
                [markDisabled]="dateFilter"
                (dateSelect)="onDatePickerSelect($event, 'from')"
                [disabled]="submitting"
                readonly
                required>
              <button
                class="input-group-text"
                type="button"
                (click)="fromDatePickerRef.toggle()"
                [disabled]="submitting">
                <i class="feather icon-calendar icon-md text-secondary"></i>
              </button>
            </div>
            <!-- <small class="text-muted mt-1">
              <i class="feather icon-info"></i>
              Weekends and holidays are disabled
            </small> -->
          </div>
          <div class="col-12 col-md-6 mb-3" [class.has-date-value]="toDate">
            <label for="toDate" class="form-label">To Date <span class="text-danger">*</span></label>
            <div class="input-group">
              <input
                class="form-control"
                placeholder="yyyy-mm-dd"
                name="toDate"
                [(ngModel)]="toDatePicker"
                ngbDatepicker
                #toDatePickerRef="ngbDatepicker"
                [markDisabled]="dateFilter"
                (dateSelect)="onDatePickerSelect($event, 'to')"
                [disabled]="submitting"
                readonly
                required>
              <button
                class="input-group-text"
                type="button"
                (click)="toDatePickerRef.toggle()"
                [disabled]="submitting">
                <i class="feather icon-calendar icon-md text-secondary"></i>
              </button>
            </div>
            <!-- <small class="text-muted mt-1">
              <i class="feather icon-info"></i>
              Weekends and holidays are disabled
            </small> -->
          </div>
          <div class="col-12 mb-3" *ngIf="dayCount > 0">
            <span class="badge bg-orange fs-6 px-3 py-2">
              <i class="fas fa-calendar-alt"></i>
              Total Days: {{ dayCount }}
            </span>
          </div>
          <div class="col-12">
            <label for="reason" class="form-label">Reason <span class="text-danger">*</span></label>
            <textarea
              class="form-control"
              id="reason"
              rows="3"
              [(ngModel)]="reason"
              name="reason"
              (input)="clearErrors()"
              [disabled]="submitting"
              placeholder="Please provide a reason for your leave..."
              required></textarea>
          </div>
        </div>
      </form>
    </div>
    <div class="d-flex justify-content-end gap-2 p-3">
      <button type="button" class="btn btn-secondary" (click)="modal.close('Close click')" [disabled]="submitting">Close</button>
      <button
        type="button"
        class="btn btn-primary"
        (click)="submitLeaveApplication(modal)"
        [disabled]="submitting || !fromDate || !toDate || !reason.trim()">
        <span *ngIf="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
        {{ submitting ? 'Submitting...' : 'Submit' }}
      </button>
    </div>
  </ng-template>
