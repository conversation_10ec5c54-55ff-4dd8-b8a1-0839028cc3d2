<!-- Sources Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-trending-up me-2"></i>
              Sources Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_sources }} total sources, 
              {{ statistics.active_sources }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-warning" (click)="generateAttributionReport()" 
                    [disabled]="selectedSources.size === 0">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Attribution Report
            </button>
            <button class="btn btn-outline-success" (click)="syncCampaignData()" 
                    [disabled]="selectedSources.size === 0">
              <i class="feather icon-refresh-cw me-1"></i>
              Sync Campaigns
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Source
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Sources
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Sources
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedSourceType" (change)="onSourceTypeFilter()">
                <option value="">All Source Types</option>
                <option *ngFor="let sourceType of sourceTypes" [value]="sourceType.value">
                  {{ sourceType.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedSourceCategory" (change)="onSourceCategoryFilter()">
                <option value="">All Categories</option>
                <option *ngFor="let category of sourceCategories" [value]="category.value">
                  {{ category.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedAttributionModel" (change)="onAttributionModelFilter()">
                <option value="">All Attribution Models</option>
                <option *ngFor="let model of attributionModels" [value]="model.value">
                  {{ model.label }}
                </option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading sources...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40">
                    <input type="checkbox" class="form-check-input" 
                           [(ngModel)]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Source Details</th>
                  <th>Type & Category</th>
                  <th>Attribution & Tracking</th>
                  <th>Performance Metrics</th>
                  <th>Campaign Integration</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let source of getCurrentList(); trackBy: trackBySourceId">
                  <td>
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedSources.has(source.id)"
                           (change)="toggleSelection(source.id)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ source.name }}</strong>
                      <span class="badge bg-info ms-2" *ngIf="source.is_default">Default</span>
                      <small class="d-block text-muted">
                        Code: {{ source.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="source.description">
                        {{ source.description }}
                      </small>
                      <small class="d-block text-muted">
                        Order: {{ source.display_order }}
                      </small>
                      <div class="mt-1" *ngIf="source.tags?.length">
                        <span class="badge bg-light text-dark me-1" *ngFor="let tag of source.tags.slice(0, 2)">
                          {{ tag }}
                        </span>
                        <span class="badge bg-light text-dark" *ngIf="source.tags.length > 2">
                          +{{ source.tags.length - 2 }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <span class="badge bg-primary me-1">
                        {{ getSourceTypeLabel(source.source_type) }}
                      </span>
                      <div class="mt-1">
                        <span class="badge me-1" [style.background-color]="getSourceCategoryColor(source.source_category)" 
                              style="color: white;">
                          {{ getSourceCategoryLabel(source.source_category) }}
                        </span>
                      </div>
                      <small class="d-block text-muted mt-1" *ngIf="source.quality_score">
                        Quality Score: {{ source.quality_score }}/10
                      </small>
                      <small class="d-block text-muted" *ngIf="source.lead_score_modifier">
                        Score Modifier: {{ source.lead_score_modifier > 0 ? '+' : '' }}{{ source.lead_score_modifier }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div>
                      <span class="badge bg-secondary me-1">
                        {{ getAttributionModelLabel(source.attribution_model) }}
                      </span>
                      <small class="d-block text-muted mt-1">
                        {{ getTrackingParametersSummary(source) }}
                      </small>
                      <div class="mt-1" *ngIf="source.conversion_tracking?.track_conversions">
                        <span class="badge bg-success me-1">Conversion Tracking</span>
                      </div>
                      <div class="mt-1" *ngIf="source.cost_tracking?.track_costs">
                        <span class="badge bg-warning me-1">Cost Tracking</span>
                      </div>
                      <small class="d-block text-muted" *ngIf="source.conversion_tracking?.attribution_window_days">
                        Attribution Window: {{ source.conversion_tracking.attribution_window_days }} days
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="performance-metrics">
                      <small class="d-block text-muted" *ngIf="source.leads_count">
                        <strong>Leads:</strong> {{ source.leads_count }}
                      </small>
                      <small class="d-block text-muted" *ngIf="source.conversion_rate">
                        <strong>Conversion:</strong> {{ formatPercentage(source.conversion_rate) }}
                      </small>
                      <small class="d-block text-muted" *ngIf="source.total_cost">
                        <strong>Total Cost:</strong> {{ formatCurrency(source.total_cost) }}
                      </small>
                      <small class="d-block text-muted" *ngIf="source.cost_per_lead">
                        <strong>Cost/Lead:</strong> {{ formatCurrency(source.cost_per_lead) }}
                      </small>
                      <small class="d-block text-muted" *ngIf="source.roi">
                        <strong>ROI:</strong> {{ formatPercentage(source.roi) }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="campaign-integration">
                      <small class="d-block text-muted">
                        {{ getCampaignIntegrationsSummary(source) }}
                      </small>
                      <div class="mt-1" *ngIf="source.analytics_integration?.google_analytics?.tracking_id">
                        <span class="badge bg-info me-1">Google Analytics</span>
                      </div>
                      <div class="mt-1" *ngIf="source.analytics_integration?.facebook_pixel?.pixel_id">
                        <span class="badge bg-primary me-1">Facebook Pixel</span>
                      </div>
                      <div class="mt-1" *ngIf="source.analytics_integration?.linkedin_insight?.partner_id">
                        <span class="badge bg-secondary me-1">LinkedIn Insight</span>
                      </div>
                      <div class="mt-1" *ngIf="source.auto_assignment_rules?.length">
                        <span class="badge bg-success me-1">
                          {{ source.auto_assignment_rules.length }} auto-assignment rules
                        </span>
                      </div>
                      <div class="mt-1" *ngIf="source.follow_up_templates?.length">
                        <span class="badge bg-warning me-1">
                          {{ source.follow_up_templates.length }} follow-up templates
                        </span>
                      </div>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(source.is_active)">
                      {{ getStatusText(source.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ source.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(source)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteSource(source)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreSource(source)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-trending-up text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Sources' : 'No Sources Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No sources have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No sources match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first source.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Source
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} sources
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_sources }}</h3>
                      <p class="mb-0">Total Sources</p>
                    </div>
                    <i class="feather icon-trending-up" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_sources }}</h3>
                      <p class="mb-0">Active Sources</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_leads }}</h3>
                      <p class="mb-0">Total Leads</p>
                    </div>
                    <i class="feather icon-users" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ formatPercentage(statistics.average_conversion_rate) }}</h3>
                      <p class="mb-0">Avg Conversion Rate</p>
                    </div>
                    <i class="feather icon-target" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
