import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { DesignationService, BulkUploadResult } from '../../../../../core/services/designation.service';
import { PopupDesignService } from '../../../../../core/services/popup-design.service';

@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective
  ],
  templateUrl: './bulk-upload.component.html',
  styleUrls: ['./bulk-upload.component.scss']
})
export class BulkUploadComponent {
  selectedFile: File | null = null;
  uploading = false;
  uploadResult: BulkUploadResult | null = null;
  error: string | null = null;

  constructor(
    private designationService: DesignationService,
    private popupService: PopupDesignService,
    public activeModal: NgbActiveModal
  ) {}

  /**
   * Handle file selection
   */
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        this.error = 'Please select a valid Excel (.xlsx, .xls) or CSV file.';
        this.selectedFile = null;
        return;
      }
      
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        this.error = 'File size must be less than 10MB.';
        this.selectedFile = null;
        return;
      }
      
      this.selectedFile = file;
      this.error = null;
      this.uploadResult = null;
    }
  }

  /**
   * Upload file
   */
  upload(): void {
    if (!this.selectedFile) {
      this.error = 'Please select a file to upload.';
      return;
    }

    this.uploading = true;
    this.error = null;

    this.designationService.bulkUpload(this.selectedFile).subscribe({
      next: (response) => {
        if (response.success) {
          this.uploadResult = response.data;
          if (this.uploadResult.successful_imports > 0) {
            this.popupService.showSuccess({
              title: 'Upload Successful!',
              message: `${this.uploadResult.successful_imports} designations imported successfully.`,
              timer: 3000
            });
          }
        } else {
          this.error = response.error || 'Upload failed.';
        }
        this.uploading = false;
      },
      error: (error) => {
        this.error = error.message;
        this.uploading = false;
      }
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.designationService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'designations_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Reset upload
   */
  reset(): void {
    this.selectedFile = null;
    this.uploadResult = null;
    this.error = null;
    
    // Reset file input
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  /**
   * Close modal
   */
  close(): void {
    if (this.uploadResult && this.uploadResult.successful_imports > 0) {
      this.activeModal.close('uploaded');
    } else {
      this.activeModal.dismiss();
    }
  }

  /**
   * Get file size in readable format
   */
  getFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file icon based on type
   */
  getFileIcon(file: File): string {
    if (file.type.includes('excel') || file.type.includes('spreadsheet')) {
      return 'file-text';
    } else if (file.type.includes('csv')) {
      return 'file';
    }
    return 'file';
  }
}
