import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Interfaces for attendance-related data
export interface AttendanceRecord {
  id?: string;
  employee_id: string;
  employee_code: string;
  employee_name: string;
  attendance_date: string; // YYYY-MM-DD format
  check_in_time: string; // HH:mm format
  check_out_time: string; // HH:mm format
  working_hours?: number;
  status?: 'present' | 'absent' | 'late' | 'half_day';
  created_at?: string;
  updated_at?: string;
}

export interface AttendanceCreateRequest {
  employee_id: string;
  attendance_date: string; // YYYY-MM-DD format
  check_in_time: string; // HH:mm format
  check_out_time: string; // HH:mm format
}

export interface AttendanceMarkRequest {
  employee_code: string;
  attendance_date: string; // YYYY-MM-DD format
  check_in_time: string; // HH:mm format
  check_out_time: string; // HH:mm format
}

export interface AttendanceBulkUploadRequest {
  file: File;
}

export interface APIResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AttendanceService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/attendance`;

  constructor(private http: HttpClient) {}

  /**
   * Download attendance template
   * GET /api/v1/attendance/template/download
   * @returns Observable of blob for file download
   */
  downloadTemplate(): Observable<Blob> {
    console.log('🗓️ AttendanceService: Downloading attendance template...');

    const url = `${this.baseUrl}/template/download`;
    console.log('📡 AttendanceService: API URL:', url);

    const headers = new HttpHeaders({
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/octet-stream,*/*'
    });

    return this.http.get(url, {
      responseType: 'blob',
      headers: headers,
      observe: 'response'
    }).pipe(
      map((response: any) => {
        console.log('✅ AttendanceService: Template response received');
        console.log('📡 Response headers:', response.headers);
        console.log('📊 Response status:', response.status);

        const blob = response.body;
        console.log('📊 File size:', blob.size, 'bytes');
        console.log('📋 File type:', blob.type);

        // If the blob is very small (like 22 bytes), it might be JSON error response
        if (blob.size < 100) {
          console.warn('⚠️ AttendanceService: Received small response, might be JSON error');
          // Try to read as text to see if it's an error message
          blob.text().then((text: string) => {
            console.log('📄 Response content:', text);
          });
        }

        return blob;
      }),
      catchError(this.handleError('downloadTemplate'))
    );
  }

  /**
   * Create a new attendance record
   * POST /api/v1/attendance
   * @param attendance Attendance record to create
   * @returns Observable of created attendance record
   */
  createAttendance(attendance: AttendanceCreateRequest): Observable<AttendanceRecord> {
    console.log('🗓️ AttendanceService: Creating attendance record:', attendance);

    const url = this.baseUrl;
    return this.http.post<APIResponse<AttendanceRecord>>(url, attendance).pipe(
      map(response => {
        if (response.success && response.data) {
          console.log('✅ AttendanceService: Attendance created successfully:', response.data);
          return response.data;
        }
        throw new Error(response.message || 'Failed to create attendance record');
      }),
      catchError(this.handleError('createAttendance'))
    );
  }

  /**
   * Mark attendance for an employee
   * POST /api/v1/attendance/mark
   * @param attendance Attendance record to mark
   * @returns Observable of marked attendance record
   */
  markAttendance(attendance: AttendanceMarkRequest): Observable<AttendanceRecord> {
    console.log('🗓️ AttendanceService: Marking attendance:', attendance);

    const url = `${this.baseUrl}/mark`;
    return this.http.post<APIResponse<AttendanceRecord>>(url, attendance).pipe(
      map(response => {
        console.log('AttendanceService: Mark attendance response:', response);

        if (response.success && response.data) {
          console.log('✅ AttendanceService: Attendance marked successfully:', response.data);
          return response.data;
        }
        throw new Error(response.message || 'Failed to mark attendance');
      }),
      catchError(this.handleError('markAttendance'))
    );
  }

  /**
   * Upload bulk attendance data
   * POST /api/v1/attendance/bulk-upload
   * @param file CSV or Excel file containing attendance data
   * @returns Observable of upload result
   */
  uploadBulkAttendance(file: File): Observable<any> {
    console.log('AttendanceService: Uploading bulk attendance file:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Validate file type
    const allowedTypes = [
      'text/csv',
      'application/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    const allowedExtensions = ['.csv', '.xls', '.xlsx'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      console.error('AttendanceService: Invalid file type:', file.type);
      throw new Error('Please upload a valid CSV or Excel file (.csv, .xls, .xlsx)');
    }

    const formData = new FormData();
    formData.append('file', file);

    const url = `${this.baseUrl}/bulk-upload`;
    console.log('AttendanceService: Upload URL:', url);

    return this.http.post<APIResponse<any>>(url, formData).pipe(
      map(response => {
        console.log('AttendanceService: Upload response received:', response);

        if (response.success) {
          console.log('AttendanceService: Bulk upload successful:', response.data);
          return response.data;
        }
        throw new Error(response.message || 'Failed to upload bulk attendance data');
      }),
      catchError(this.handleError('uploadBulkAttendance'))
    );
  }

  /**
   * Get attendance records for a specific date range
   * GET /api/v1/attendance
   * @param fromDate Start date (YYYY-MM-DD)
   * @param toDate End date (YYYY-MM-DD)
   * @param employeeId Optional employee ID filter
   * @returns Observable of attendance records
   */
  getAttendanceRecords(fromDate: string, toDate: string, employeeId?: string): Observable<AttendanceRecord[]> {
    console.log('🗓️ AttendanceService: Getting attendance records:', { fromDate, toDate, employeeId });
    
    let url = `${this.baseUrl}?from_date=${fromDate}&to_date=${toDate}`;
    if (employeeId) {
      url += `&employee_id=${employeeId}`;
    }

    return this.http.get<APIResponse<AttendanceRecord[]>>(url).pipe(
      map(response => {
        if (response.success && response.data) {
          console.log('✅ AttendanceService: Attendance records retrieved:', response.data.length, 'records');
          return response.data;
        }
        throw new Error(response.message || 'Failed to retrieve attendance records');
      }),
      catchError(this.handleError('getAttendanceRecords'))
    );
  }

  /**
   * Download template and save to user's device
   * @param filename Optional custom filename
   */
  downloadAndSaveTemplate(filename: string = 'attendance_template.xlsx'): void {
    console.log('📥 AttendanceService: Initiating template download...');

    const url = `${this.baseUrl}/template/download`;
    console.log('📡 AttendanceService: Download URL:', url);

    // Method 1: Try direct window.open (works if server sends proper headers)
    console.log('🔗 AttendanceService: Attempting direct download via window.open...');
    window.open(url, '_blank');

    // Method 2: Try programmatic download with link element
    setTimeout(() => {
      console.log('🔗 AttendanceService: Attempting programmatic download...');
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('✅ AttendanceService: Programmatic download initiated');
    }, 500);

    // Method 3: Fetch and create blob (fallback)
    setTimeout(() => {
      console.log('🔗 AttendanceService: Attempting blob download as fallback...');
      fetch(url)
        .then(response => {
          console.log('� Fetch response status:', response.status);
          console.log('� Fetch response headers:', response.headers);
          return response.blob();
        })
        .then(blob => {
          console.log('📊 Fetched blob size:', blob.size, 'bytes');
          console.log('📋 Fetched blob type:', blob.type);

          if (blob.size > 50) { // If we got a reasonable file
            const blobUrl = window.URL.createObjectURL(blob);
            const blobLink = document.createElement('a');
            blobLink.href = blobUrl;
            blobLink.download = filename;
            blobLink.style.display = 'none';

            document.body.appendChild(blobLink);
            blobLink.click();
            document.body.removeChild(blobLink);

            window.URL.revokeObjectURL(blobUrl);
            console.log('✅ AttendanceService: Blob download completed');
          } else {
            console.warn('⚠️ AttendanceService: Blob too small, likely error response');
            blob.text().then(text => console.log('📄 Response:', text));
          }
        })
        .catch(error => {
          console.error('❌ AttendanceService: Fetch download failed:', error);
        });
    }, 1000);
  }

  /**
   * Download template using Swagger-style HTTP request with local fallback
   * @param filename Optional custom filename
   */
  downloadTemplateSwaggerStyle(filename: string = 'attendance_template.csv'): void {
    console.log('AttendanceService: Initiating template download (Swagger-style)...');

    const apiUrl = `${this.baseUrl}/template/download`;
    console.log('AttendanceService: API Download URL:', apiUrl);

    // Use HttpClient with proper headers to mimic Swagger behavior
    const headers = new HttpHeaders({
      'Accept': 'text/csv,application/csv,*/*',
      'Content-Type': 'application/json'
    });

    console.log('AttendanceService: Making HTTP request with proper headers...');

    this.http.get(apiUrl, {
      headers: headers,
      responseType: 'blob',
      observe: 'response'
    }).subscribe({
      next: (response) => {
        console.log('AttendanceService: Template response received from API');
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers.keys());

        const blob = response.body;
        if (blob) {
          console.log('File size:', blob.size, 'bytes');
          console.log('File type:', blob.type);

          // Extract filename from Content-Disposition header if available
          const contentDisposition = response.headers.get('content-disposition');
          let downloadFilename = filename;

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (filenameMatch && filenameMatch[1]) {
              downloadFilename = filenameMatch[1].replace(/['"]/g, '');
              console.log('Extracted filename from header:', downloadFilename);
            }
          }

          // Create blob URL and trigger download (same as Swagger)
          const blobUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = downloadFilename;
          link.style.display = 'none';

          // Add to DOM, click, and remove
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up blob URL
          window.URL.revokeObjectURL(blobUrl);

          console.log('AttendanceService: File download completed from API:', downloadFilename);
        } else {
          console.error('AttendanceService: No blob received in response');
          this.downloadTemplateFromAssets(filename);
        }
      },
      error: (error) => {
        console.error('AttendanceService: API template download failed:', error);
        console.error('Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });

        // Fallback: download from local assets
        console.log('AttendanceService: Falling back to local template...');
        this.downloadTemplateFromAssets(filename);
      }
    });
  }

  /**
   * Download template from local assets folder
   * @param filename Optional custom filename
   */
  private downloadTemplateFromAssets(filename: string = 'attendance_template.csv'): void {
    console.log('AttendanceService: Downloading template from local assets...');

    const assetUrl = 'assets/templates/hr-admin/attendance_template.csv';
    console.log('AttendanceService: Asset URL:', assetUrl);

    this.http.get(assetUrl, {
      responseType: 'blob'
    }).subscribe({
      next: (blob) => {
        console.log('AttendanceService: Template loaded from assets');
        console.log('File size:', blob.size, 'bytes');
        console.log('File type:', blob.type);

        // Create blob URL and trigger download
        const blobUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = filename;
        link.style.display = 'none';

        // Add to DOM, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up blob URL
        window.URL.revokeObjectURL(blobUrl);

        console.log('AttendanceService: File download completed from assets:', filename);
      },
      error: (error) => {
        console.error('AttendanceService: Asset template download failed:', error);

        // Final fallback: direct link to asset
        console.log('AttendanceService: Attempting direct asset download...');
        const link = document.createElement('a');
        link.href = assetUrl;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('AttendanceService: Direct asset download initiated');
      }
    });
  }

  /**
   * Handle HTTP errors
   * @param operation Name of the operation that failed
   * @returns Error handler function
   */
  private handleError(operation: string) {
    return (error: HttpErrorResponse): Observable<never> => {
      console.error(`❌ AttendanceService: ${operation} failed:`, error);
      
      let errorMessage = 'An unexpected error occurred';
      
      if (error.error instanceof ErrorEvent) {
        // Client-side error
        errorMessage = `Client Error: ${error.error.message}`;
      } else {
        // Server-side error
        errorMessage = `Server Error: ${error.status} - ${error.message}`;
        
        if (error.error && typeof error.error === 'object') {
          if (error.error.message) {
            errorMessage = error.error.message;
          } else if (error.error.error) {
            errorMessage = error.error.error;
          }
        }
      }
      
      console.error(`❌ AttendanceService: ${operation} error details:`, {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: errorMessage
      });
      
      return throwError(() => new Error(errorMessage));
    };
  }

  /**
   * Format date to YYYY-MM-DD format
   * @param date Date string or Date object
   * @returns Formatted date string
   */
  private formatDateToYYYYMMDD(date: string | Date): string {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
