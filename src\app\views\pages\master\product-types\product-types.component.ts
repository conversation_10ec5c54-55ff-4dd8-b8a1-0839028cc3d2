import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  ProductTypeService,
  ProductType,
  ProductTypeStatistics
} from '../../../../core/services/product-type.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { ProductTypeFormComponent } from './product-type-form/product-type-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-product-types',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    ProductTypeFormComponent,
    BulkUploadComponent
  ],
  templateUrl: './product-types.component.html',
  styleUrls: ['./product-types.component.scss']
})
export class ProductTypesComponent implements OnInit {
  // Data properties
  productTypes: ProductType[] = [];
  deletedProductTypes: ProductType[] = [];
  statistics: ProductTypeStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedCategory = '';
  selectedPricingModel = '';
  selectedSupportLevel = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedProductTypes: Set<string> = new Set();
  selectAll = false;

  // Filter options
  productCategories: any[] = [];
  pricingModels: any[] = [];
  supportLevels: any[] = [];
  taxCategories: string[] = [];
  currencies: string[] = [];

  constructor(
    private productTypeService: ProductTypeService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadProductTypes();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.productCategories = this.productTypeService.getProductCategories();
    this.pricingModels = this.productTypeService.getPricingModels();
    this.supportLevels = this.productTypeService.getSupportLevels();
    this.taxCategories = this.productTypeService.getTaxCategories();
    this.currencies = this.productTypeService.getCurrencies();
  }

  /**
   * Load product types with current filters
   */
  loadProductTypes(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      category: this.selectedCategory || undefined,
      pricing_model: this.selectedPricingModel || undefined,
      support_level: this.selectedSupportLevel || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.productTypeService.getProductTypesWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedProductTypes = response.data.filter(pt => pt.deleted_at);
            this.productTypes = [];
          } else {
            this.productTypes = response.data.filter(pt => !pt.deleted_at);
            this.deletedProductTypes = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load product types';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load product types. Please try again.'
        });
      }
    });
  }

  /**
   * Load product type statistics
   */
  loadStatistics(): void {
    this.productTypeService.getProductTypeStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search product types
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadProductTypes();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadProductTypes();
  }

  /**
   * Filter by category
   */
  onCategoryFilter(): void {
    this.currentPage = 1;
    this.loadProductTypes();
  }

  /**
   * Filter by pricing model
   */
  onPricingModelFilter(): void {
    this.currentPage = 1;
    this.loadProductTypes();
  }

  /**
   * Filter by support level
   */
  onSupportLevelFilter(): void {
    this.currentPage = 1;
    this.loadProductTypes();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedProductTypes.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadProductTypes();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadProductTypes();
  }

  /**
   * Open create product type modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(ProductTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadProductTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Product type created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit product type modal
   */
  openEditModal(productType: ProductType): void {
    const modalRef = this.modalService.open(ProductTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.productType = { ...productType };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadProductTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Product type updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete product type
   */
  deleteProductType(productType: ProductType): void {
    this.popupService.showConfirmation({
      title: 'Delete Product Type',
      message: `Are you sure you want to delete "${productType.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.productTypeService.deleteProductType(productType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadProductTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Product type deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete product type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore product type
   */
  restoreProductType(productType: ProductType): void {
    this.popupService.showConfirmation({
      title: 'Restore Product Type',
      message: `Are you sure you want to restore "${productType.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.productTypeService.restoreProductType(productType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadProductTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Product type restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore product type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadProductTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Product types uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.productTypeService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'product_types_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadProductTypes();
    this.loadStatistics();
  }

  /**
   * Get product category label
   */
  getProductCategoryLabel(category: string): string {
    return this.productTypeService.getProductCategoryLabel(category);
  }

  /**
   * Get pricing model label
   */
  getPricingModelLabel(model: string): string {
    return this.productTypeService.getPricingModelLabel(model);
  }

  /**
   * Get support level label
   */
  getSupportLevelLabel(level: string): string {
    return this.productTypeService.getSupportLevelLabel(level);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    return this.productTypeService.getCategoryBadgeClass(category);
  }

  /**
   * Get support level badge class
   */
  getSupportLevelBadgeClass(level: string): string {
    return this.productTypeService.getSupportLevelBadgeClass(level);
  }

  /**
   * Format price
   */
  formatPrice(price: number | undefined, currency: string = 'USD'): string {
    return this.productTypeService.formatPrice(price, currency);
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): ProductType[] {
    return this.viewMode === 'deleted' ? this.deletedProductTypes : this.productTypes;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByProductTypeId(index: number, productType: ProductType): string {
    return productType.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
