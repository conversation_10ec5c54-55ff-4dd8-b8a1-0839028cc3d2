import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, timer } from 'rxjs';
import { map, tap } from 'rxjs/operators';

export interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessed: Date;
}

export interface CacheConfig {
  defaultTTL: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of entries
  cleanupInterval: number; // Cleanup interval in milliseconds
  enableMetrics: boolean;
}

export interface CacheMetrics {
  totalEntries: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  oldestEntry: Date | null;
  newestEntry: Date | null;
  totalMemoryUsage: number; // Estimated in bytes
}

@Injectable({
  providedIn: 'root'
})
export class PermissionCacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private metricsSubject = new BehaviorSubject<CacheMetrics>(this.getInitialMetrics());
  public metrics$ = this.metricsSubject.asObservable();

  private config: CacheConfig = {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 1000,
    cleanupInterval: 60 * 1000, // 1 minute
    enableMetrics: true
  };

  private hitCount = 0;
  private missCount = 0;

  constructor() {
    this.startCleanupTimer();
  }

  /**
   * Get cached data or execute factory function
   */
  get<T>(key: string, factory: () => Observable<T>, ttl?: number): Observable<T> {
    const entry = this.cache.get(key);
    const now = new Date();

    // Check if we have a valid cache entry
    if (entry && entry.expiresAt > now) {
      // Cache hit
      entry.accessCount++;
      entry.lastAccessed = now;
      this.hitCount++;
      this.updateMetrics();

      console.log(`🎯 Cache HIT for key: ${key}`);
      return of(entry.data);
    }

    // Cache miss - execute factory function
    this.missCount++;
    console.log(`❌ Cache MISS for key: ${key}`);

    return factory().pipe(
      tap(data => {
        this.set(key, data, ttl);
      })
    );
  }

  /**
   * Set cache entry
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const now = new Date();
    const timeToLive = ttl || this.config.defaultTTL;

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: new Date(now.getTime() + timeToLive),
      accessCount: 1,
      lastAccessed: now
    };

    // Check if we need to make room
    if (this.cache.size >= this.config.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(key, entry);
    this.updateMetrics();

    console.log(`💾 Cached data for key: ${key}, expires at: ${entry.expiresAt.toISOString()}`);
  }

  /**
   * Get cached data without factory function
   */
  getSync<T>(key: string): T | null {
    const entry = this.cache.get(key);
    const now = new Date();

    if (entry && entry.expiresAt > now) {
      entry.accessCount++;
      entry.lastAccessed = now;
      this.hitCount++;
      this.updateMetrics();
      return entry.data;
    }

    this.missCount++;
    this.updateMetrics();
    return null;
  }

  /**
   * Check if key exists and is valid
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    const now = new Date();
    return entry ? entry.expiresAt > now : false;
  }

  /**
   * Remove specific cache entry
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateMetrics();
      console.log(`🗑️ Deleted cache entry: ${key}`);
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    this.updateMetrics();
    console.log('🧹 Cache cleared');
  }

  /**
   * Invalidate entries matching pattern
   */
  invalidatePattern(pattern: string | RegExp): number {
    let deletedCount = 0;
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;

    const keysToDelete: string[] = [];
    this.cache.forEach((value, key) => {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      deletedCount++;
    });

    if (deletedCount > 0) {
      this.updateMetrics();
      console.log(`🗑️ Invalidated ${deletedCount} cache entries matching pattern: ${pattern}`);
    }

    return deletedCount;
  }

  /**
   * Invalidate user-specific cache entries
   */
  invalidateUserCache(userId: string): number {
    return this.invalidatePattern(`user:${userId}:`);
  }

  /**
   * Invalidate permission-related cache entries
   */
  invalidatePermissionCache(): number {
    return this.invalidatePattern(/permission|role|auth/i);
  }

  /**
   * Get cache statistics
   */
  getMetrics(): CacheMetrics {
    return this.calculateMetrics();
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Cache configuration updated:', this.config);
  }

  /**
   * Get current configuration
   */
  getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * Manually trigger cleanup
   */
  cleanup(): number {
    const now = new Date();
    let deletedCount = 0;

    const keysToDelete: string[] = [];
    this.cache.forEach((entry, key) => {
      if (entry.expiresAt <= now) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      deletedCount++;
    });

    if (deletedCount > 0) {
      this.updateMetrics();
      console.log(`🧹 Cleaned up ${deletedCount} expired cache entries`);
    }

    return deletedCount;
  }

  /**
   * Get all cache keys
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache entry info
   */
  getEntryInfo(key: string): Partial<CacheEntry<any>> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    return {
      timestamp: entry.timestamp,
      expiresAt: entry.expiresAt,
      accessCount: entry.accessCount,
      lastAccessed: entry.lastAccessed
    };
  }

  /**
   * Evict least recently used entry
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = new Date();

    this.cache.forEach((entry, key) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`🗑️ Evicted LRU cache entry: ${oldestKey}`);
    }
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    timer(this.config.cleanupInterval, this.config.cleanupInterval).subscribe(() => {
      this.cleanup();
    });
  }

  /**
   * Calculate current metrics
   */
  private calculateMetrics(): CacheMetrics {
    const entries: CacheEntry<any>[] = [];
    this.cache.forEach(entry => entries.push(entry));

    const totalRequests = this.hitCount + this.missCount;

    let oldestEntry: Date | null = null;
    let newestEntry: Date | null = null;
    let totalMemoryUsage = 0;

    if (entries.length > 0) {
      oldestEntry = entries.reduce((oldest, entry) =>
        entry.timestamp < oldest ? entry.timestamp : oldest, entries[0].timestamp);

      newestEntry = entries.reduce((newest, entry) =>
        entry.timestamp > newest ? entry.timestamp : newest, entries[0].timestamp);

      // Rough estimation of memory usage
      totalMemoryUsage = entries.length * 1024; // Assume 1KB per entry on average
    }

    return {
      totalEntries: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0,
      oldestEntry,
      newestEntry,
      totalMemoryUsage
    };
  }

  /**
   * Update metrics subject
   */
  private updateMetrics(): void {
    if (this.config.enableMetrics) {
      this.metricsSubject.next(this.calculateMetrics());
    }
  }

  /**
   * Get initial metrics
   */
  private getInitialMetrics(): CacheMetrics {
    return {
      totalEntries: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      oldestEntry: null,
      newestEntry: null,
      totalMemoryUsage: 0
    };
  }
}
