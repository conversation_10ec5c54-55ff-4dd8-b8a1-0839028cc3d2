import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { trigger, state, style, transition, animate, keyframes } from '@angular/animations';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../../core/services/auth.service';
import { AutoLoginService } from '../../../../core/services/auto-login.service';

@Component({
  selector: 'app-login',
  standalone: true,  imports: [
    FeatherIconDirective,
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  animations: [
    // Fade in animation
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 }))
      ])
    ],),

    // Fade in up animation
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('600ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),

    // Slide in from right animation
    trigger('slideInRight', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(20px)' }),
        animate('500ms 300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
      ])
    ]),

    // Pulse animation for logo
    trigger('pulse', [
      transition(':enter', [
        animate('1000ms ease-in-out', keyframes([
          style({ transform: 'scale(0.95)', opacity: 0.7, offset: 0 }),
          style({ transform: 'scale(1.05)', opacity: 0.8, offset: 0.5 }),
          style({ transform: 'scale(1)', opacity: 1, offset: 1 })
        ]))
      ])
    ]),

    // Pulse animation for button
    trigger('pulseAnimation', [
      state('void', style({ transform: 'scale(1)' })),
      transition('void => *', []),
      transition('* => void', []),
      transition('* => *', [
        animate('400ms ease-in-out', keyframes([
          style({ transform: 'scale(1)', offset: 0 }),
          style({ transform: 'scale(1.05)', offset: 0.5 }),
          style({ transform: 'scale(1)', offset: 1 })
        ]))
      ])
    ]),

    // Scale in animation for quick login buttons
    trigger('scaleIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate('500ms 500ms ease-out', style({ opacity: 1, transform: 'scale(1)' }))
      ])
    ])
  ]
})
export class LoginComponent implements OnInit, OnDestroy, AfterViewInit {
  returnUrl: any;
  showPassword: boolean = false;
  pulseState: boolean = false;
  private pulseInterval: any;

  // Add form and auth properties
  loginForm: FormGroup;
  errorMessage: string = '';
  loading: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private authService: AuthService,
    private autoLoginService: AutoLoginService,
    private cdr: ChangeDetectorRef
  ) {}  ngOnInit(): void {
    // Check if the user has been logged out
    const loggedOut = this.route.snapshot.queryParamMap.get('loggedOut') === 'true';

    // If logged out, show a message
    if (loggedOut) {
      console.log('User has been logged out');
    }

    // Check if user is logged in - but respect the logged out flag
    if (localStorage.getItem('user_logged_out') !== 'true' && this.authService.isLoggedIn()) {
      this.router.navigate(['/dashboard']);
      return;
    }

    // Get the return URL from the route parameters, or default to '/dashboard'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';

    // Check for remembered credentials
    const rememberedCredentials = this.getRememberedCredentials();

    // Initialize login form
    this.loginForm = this.fb.group({
      username: [rememberedCredentials.username || '', [Validators.required]], // Changed from email to username
      password: [rememberedCredentials.password || '', Validators.required],
      rememberMe: [rememberedCredentials.rememberMe || false]
    });

    // Auto-login if conditions are met
    if (this.autoLoginService.shouldAttemptAutoLogin() && !loggedOut) {
      console.log('🔄 Auto-login: Conditions met, attempting automatic login...');
      this.autoLoginService.attemptAutoLogin().subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Auto-login successful, redirecting...');
            this.router.navigate([this.returnUrl]);
          } else {
            console.log('❌ Auto-login failed, user will need to login manually');
          }
        },
        error: (error) => {
          console.error('❌ Auto-login error:', error);
        }
      });
    }

    // Start pulse animation for login button every 3 seconds
    this.pulseInterval = setInterval(() => {
      this.pulseState = !this.pulseState;
    }, 3000);
  }

  ngAfterViewInit(): void {
    // Icons will be handled by the FeatherIconDirective
    // No need to manually call feather.replace() here
  }

  ngOnDestroy(): void {
    // Clean up the pulse interval
    if (this.pulseInterval) {
      clearInterval(this.pulseInterval);
    }
  }

  /**
   * Get remembered credentials from localStorage
   */
  private getRememberedCredentials(): { username: string; password: string; rememberMe: boolean } {
    try {
      const remembered = localStorage.getItem('rememberedCredentials');
      if (remembered) {
        const credentials = JSON.parse(remembered);
        // Decrypt the password (simple base64 for demo - use proper encryption in production)
        if (credentials.password) {
          credentials.password = atob(credentials.password);
        }
        return credentials;
      }
    } catch (error) {
      console.error('Error retrieving remembered credentials:', error);
    }
    return { username: '', password: '', rememberMe: false };
  }





  /**
   * Handle remember me checkbox change
   */
  onRememberMeChange(): void {
    const rememberMe = this.loginForm.get('rememberMe')?.value;
    console.log('🔄 Remember me checkbox changed:', rememberMe);

    if (rememberMe) {
      console.log('✅ Remember me enabled - credentials will be saved on successful login');
    } else {
      console.log('❌ Remember me disabled - credentials will be cleared');
      // Clear any existing remembered credentials immediately
      this.authService.clearRememberedCredentials();
    }

    // Icons will be handled automatically by the FeatherIconDirective
    // Just trigger change detection
    this.cdr.detectChanges();
  }

  /**
   * Toggle password visibility between plain text and hidden
   */
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
    // Icons will be handled automatically by the FeatherIconDirective
    this.cdr.detectChanges();
  }


  /**
   * Handle login form submission
   */
  onLoggedin(e: Event) {
    e.preventDefault();

    // if (this.loginForm.invalid) {
    //   // Mark form controls as touched to trigger validation display
    //   Object.keys(this.loginForm.controls).forEach(key => {
    //     this.loginForm.get(key)?.markAsTouched();
    //   });
    //   return;
    // }

    this.loading = true;
    this.errorMessage = '';

    const username = this.loginForm.get('username')?.value;
    const password = this.loginForm.get('password')?.value;
    const rememberMe = this.loginForm.get('rememberMe')?.value;

    console.log('🔄 Login attempt with remember me:', rememberMe);

    // TEMPORARY: Mock login for testing Ops Team permissions
    if (username === '<EMAIL>' && password === 'password123') {
      console.log("🧪 Using mock login for testing");

      // Create a mock user with ops:access permission
      const mockUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        role: 'admin',
        roles: ['admin'],
        permissions: ['ops:access', 'admin:access', 'users:read', 'sales:read', 'roles:read', 'permissions:read'], // Include ops:access and roles permissions
        is_superuser: false,
        access_token: 'mock-token-for-testing',
        refresh_token: 'mock-refresh-token',
        token_expiry: Date.now() + (24 * 60 * 60 * 1000), // 24 hours from now
        is_active: true
      };

      // Store the mock user
      localStorage.setItem('currentUser', JSON.stringify(mockUser));
      localStorage.setItem('accessToken', mockUser.access_token);

      // Update auth service
      this.authService.updateCurrentUser(mockUser);

      console.log("✅ Mock login successful with ops:access permission");
      this.router.navigate([this.returnUrl]);
      this.loading = false;
      return;
    }

    this.authService.login(username, password, rememberMe)      .subscribe({        next: (response) => {
          console.log("✅ Login successful:", response);
          console.log("💾 Remember me was:", rememberMe);

          // Check if credentials were saved for remember me
          if (rememberMe) {
            const savedCredentials = this.authService.getRememberedCredentials();
            console.log("💾 Credentials saved for auto-login:", !!savedCredentials);
            console.log("🔓 User logged out flag cleared:", localStorage.getItem('user_logged_out') !== 'true');
          }

          // Check if user is properly set
          const currentUser = this.authService.currentUserValue;
          console.log("👤 Current user:", currentUser);
          console.log("🔑 Is admin?", this.authService.isAdmin());
          console.log("🔒 Is superuser?", currentUser?.is_superuser);
          console.log("👔 User role:", currentUser?.role);
          console.log("🎭 User roles:", this.authService.getUserRoles());
          console.log("🔐 User permissions:", this.authService.getUserPermissions());

          // Fetch current user data from API to verify permissions
          console.log("🔄 Fetching current user data from API to verify permissions...");
          this.authService.getCurrentUser().subscribe({
            next: (userResponse) => {
              console.log("📡 User data fetched from /api/v1/users/me:", userResponse);

              // Get updated user data
              const updatedUser = this.authService.currentUserValue;
              console.log("🔄 Updated user after API check:", updatedUser);
              console.log("🔑 Final admin status:", this.authService.isAdmin());
              console.log("🎭 Final roles:", this.authService.getUserRoles());
              console.log("🔐 Final permissions:", this.authService.getUserPermissions());

              // Log final role and permissions status
              if (this.authService.isAdmin()) {
                console.log("🎉 ADMIN ROLE CONFIRMED BY API - Full permissions granted");
                console.log("✅ Admin permissions include:", [
                  'manage_employees', 'manage_roles', 'approve_leaves',
                  'view_all_employees', 'manage_system_settings', 'bulk_upload'
                ]);
              } else {
                console.log("👤 EMPLOYEE ROLE CONFIRMED BY API - Limited permissions");
                console.log("✅ Employee permissions include:", [
                  'view_profile', 'edit_profile', 'apply_leave',
                  'view_own_leaves', 'mark_attendance'
                ]);
              }
            },
            error: (error) => {
              console.error("❌ Failed to fetch current user data:", error);
              console.log("⚠️ Using permissions from login response");
            }
          });
            // Navigate after login
          try {
            // Ensure we have a valid URL to navigate to
            const targetUrl = this.returnUrl && this.returnUrl !== '/' ? this.returnUrl : '/dashboard';
            console.log("Redirecting to:", targetUrl);

            // For now, all users go to the main dashboard until admin dashboard is implemented
            this.router.navigate([targetUrl]).then(
              navigated => console.log('Navigation result:', navigated)
            ).catch(err => console.error('Navigation error:', err));
          } catch (error) {
            console.error("Navigation error:", error);
            // Fallback navigation
            this.router.navigate(['/dashboard']).catch(err => console.error('Fallback navigation error:', err));
          }
        },        error: (error) => {          console.error('Login error:', error);

          // Clear saved credentials on login failure if remember me was checked
          if (this.loginForm.get('rememberMe')?.value) {
            this.authService.clearRememberedCredentials();
          }

          if (error.error && typeof error.error === 'object') {
            // Handle FastAPI validation error format
            if (error.error.detail) {
              if (Array.isArray(error.error.detail)) {
                // FastAPI often returns detail as an array of validation errors
                this.errorMessage = error.error.detail.map((err: { loc: any[]; msg: any; }) =>
                  typeof err === 'object' ? `${err.loc.join('.')}: ${err.msg}` : err
                ).join('; ');
              } else {
                this.errorMessage = error.error.detail;
              }
            } else if (error.error.message) {
              this.errorMessage = error.error.message;
            } else {
              // Try to extract validation errors if present
              const validationErrors = [];
              for (const key in error.error) {
                if (Array.isArray(error.error[key])) {
                  validationErrors.push(`${key}: ${error.error[key].join(', ')}`);
                }
              }

              if (validationErrors.length > 0) {
                this.errorMessage = `Validation errors: ${validationErrors.join('; ')}`;
              } else {
                this.errorMessage = 'Login failed. Please check your credentials.';
              }
            }
          } else {
            this.errorMessage = `Login failed (${error.status}). Please check your credentials.`;
          }

          this.loading = false;

          // Trigger change detection after error state change
          this.cdr.detectChanges();
        },
        complete: () => {
          this.loading = false;
        }
      });
 // Instead, simulate successful login
//   console.log("Login bypassed for development");

//   // Simulate a delay
//   setTimeout(() => {
//     // Mock a user object
//     const mockUser = {
//       id: 1,
//       email: '<EMAIL>',
//       name: 'Developer User',
//       firstName: 'Developer',
//       lastName: 'User',
//       role: 'admin',
//       access_token: 'mock-token-for-development'
//     };

//     // Store the mock user in localStorage to simulate login
//     localStorage.setItem('currentUser', JSON.stringify(mockUser));

//     // Navigate to dashboard
//     this.router.navigate(['/dashboard']).then(
//       navigated => console.log('Navigation result:', navigated)
//     ).catch(err => console.error('Navigation error:', err));

//     this.loading = false;
//   }, 1000); // 1 second delay to simulate network request
// }

  }
}

