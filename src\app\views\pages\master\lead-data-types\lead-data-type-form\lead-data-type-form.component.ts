import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LeadDataType } from '../../../../../core/services/lead-data-type.service';

@Component({
  selector: 'app-lead-data-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-database me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Lead Data Type
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Lead Data Type Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ leadDataType?.name }}</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced form will include comprehensive lead data type management with:
        <ul class="mt-2 mb-0">
          <li><strong>Dynamic Field Type Builder</strong> - 20+ field types with custom configuration options</li>
          <li><strong>Advanced Validation Engine</strong> - 14 validation rule types with custom messages and severity levels</li>
          <li><strong>Conditional Logic Builder</strong> - Show/hide/enable/disable fields based on other field values</li>
          <li><strong>Data Category Management</strong> - 9 predefined categories with custom color coding</li>
          <li><strong>Privacy & Security Settings</strong> - PII marking, encryption requirements, access levels</li>
          <li><strong>Field Options Designer</strong> - Dynamic options for select/radio/checkbox fields</li>
          <li><strong>Formatting Rules Engine</strong> - Masks, transforms, prefixes, suffixes, currency formatting</li>
          <li><strong>Integration Mapping</strong> - Map fields to external systems with transformation functions</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-settings me-2"></i>
        <strong>Advanced Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Real-time field preview with live validation testing</li>
          <li>Audit settings for change tracking and access logging</li>
          <li>Privacy compliance tools (GDPR, CCPA) with retention policies</li>
          <li>Multi-language support for field labels and help text</li>
          <li>Custom CSS styling and theme integration</li>
          <li>Field dependency management and circular reference detection</li>
          <li>Performance optimization for large forms</li>
          <li>Accessibility compliance (ARIA labels, keyboard navigation)</li>
        </ul>
      </div>

      <div class="alert alert-success">
        <i class="feather icon-code me-2"></i>
        <strong>Technical Capabilities:</strong>
        <ul class="mt-2 mb-0">
          <li>JSON schema generation for form validation</li>
          <li>TypeScript interface generation for type safety</li>
          <li>API endpoint generation for CRUD operations</li>
          <li>Database schema migration scripts</li>
          <li>Unit test generation for validation rules</li>
          <li>Documentation generation with examples</li>
          <li>Performance benchmarking and optimization</li>
        </ul>
      </div>

      <div class="alert alert-primary">
        <i class="feather icon-layers me-2"></i>
        <strong>Field Type Categories:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <strong>Input Types:</strong>
            <ul class="mb-2">
              <li>Text, Textarea, Number</li>
              <li>Email, Phone, URL</li>
              <li>Password, Search</li>
            </ul>
            <strong>Selection Types:</strong>
            <ul class="mb-2">
              <li>Dropdown, Multi-select</li>
              <li>Radio buttons, Checkboxes</li>
              <li>Toggle switches</li>
            </ul>
          </div>
          <div class="col-md-6">
            <strong>Date/Time Types:</strong>
            <ul class="mb-2">
              <li>Date, DateTime, Time</li>
              <li>Date range, Time range</li>
            </ul>
            <strong>Advanced Types:</strong>
            <ul class="mb-2">
              <li>File upload, Image upload</li>
              <li>JSON editor, Rich text</li>
              <li>Currency, Percentage, Rating</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
    
    .row {
      margin: 0;
    }
    
    .col-md-6 {
      padding: 0 0.5rem;
    }
  `]
})
export class LeadDataTypeFormComponent {
  @Input() isEditMode = false;
  @Input() leadDataType: LeadDataType | null = null;

  leadDataTypeForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.leadDataTypeForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      field_type: ['', [Validators.required]],
      data_category: ['', [Validators.required]]
    });
  }
}
