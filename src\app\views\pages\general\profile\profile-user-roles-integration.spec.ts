import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { ProfileComponent } from './profile.component';
import { EmployeeService, BizzCorpEmployee, Department, Designation, BizzCorpRole } from '../../../../core/services/employee.service';
import { AuthService, User } from '../../../../core/services/auth.service';
import { RoleService } from '../../../../core/services/role.service';

describe('ProfileComponent - User Roles API Integration', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let employeeService: jasmine.SpyObj<EmployeeService>;

  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    name: '<PERSON>',
    access_token: 'mock-token',
    is_active: true
  };

  const mockUserRoles: BizzCorpRole[] = [
    {
      id: 'role-1',
      name: 'admin',
      description: 'Administrator with all permissions',
      permissions: ['*'],
      is_active: true
    },
    {
      id: 'role-2', 
      name: 'manager',
      description: 'Manager with team oversight',
      permissions: ['manage_team', 'approve_leaves'],
      is_active: true
    },
    {
      id: 'role-3',
      name: 'employee',
      description: 'Standard employee access',
      permissions: ['view_profile', 'apply_leave'],
      is_active: true
    }
  ];

  const mockDepartments: Department[] = [
    { id: 'dept-123', name: 'IT Department', is_active: true }
  ];

  const mockDesignations: Designation[] = [
    { id: 'desig-123', name: 'Software Engineer', is_active: true }
  ];

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['isLoggedIn'], {
      currentUserValue: mockUser
    });
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees',
      'getBizzCorpEmployeeProfile',
      'getDepartmentsMasterData',
      'getDesignationsMasterData',
      'getDesignationById',
      'getUserRoles',
      'findRoleByName'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const roleServiceSpy = jasmine.createSpyObj('RoleService', ['getAllRoles']);

    await TestBed.configureTestingModule({
      imports: [ProfileComponent, HttpClientTestingModule, ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: RoleService, useValue: roleServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;

    // Setup default spy returns
    authService.isLoggedIn.and.returnValue(true);
    employeeService.getDepartmentsMasterData.and.returnValue(of(mockDepartments));
    employeeService.getDesignationsMasterData.and.returnValue(of(mockDesignations));
    employeeService.getUserRoles.and.returnValue(of(mockUserRoles));
  });

  describe('User Roles API Integration', () => {
    const employeeProfile: BizzCorpEmployee = {
      id: 'emp-123',
      employee_code: 'EMP001',
      first_name: 'John',
      last_name: 'Doe',
      department_id: 'dept-123',
      designation_id: 'desig-123',
      sub_role_id: 'some-role-id', // This is now ignored, we use User Roles API instead
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };

    beforeEach(() => {
      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>',
        employee_code: 'EMP001'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeProfile));
      employeeService.getDesignationById.and.returnValue(of({ id: 'desig-123', name: 'Software Engineer', is_active: true }));
    });

    it('should always call getUserRoles API regardless of sub_role_id', () => {
      component.ngOnInit();

      expect(employeeService.getUserRoles).toHaveBeenCalled();
    });

    it('should display primary role name when roles are found', () => {
      // Mock Employee role as primary role
      const employeeRoles = [mockUserRoles[2]]; // Employee role
      employeeService.getUserRoles.and.returnValue(of(employeeRoles));

      component.ngOnInit();

      expect(component.profileTableData[0].subRole).toBe('employee');
    });

    it('should display role name only (no description in parentheses)', () => {
      // Mock Admin role with description - should only show name
      const adminRoles = [mockUserRoles[0]]; // Admin role with description
      employeeService.getUserRoles.and.returnValue(of(adminRoles));

      component.ngOnInit();

      expect(component.profileTableData[0].subRole).toBe('admin');
    });

    it('should display role name when description is null', () => {
      // Mock role without description (like the real API response)
      const roleWithoutDescription = [{
        id: 'role-1',
        name: 'Employee',
        description: null,
        is_active: true
      }];
      employeeService.getUserRoles.and.returnValue(of(roleWithoutDescription));

      component.ngOnInit();

      expect(component.profileTableData[0].subRole).toBe('Employee');
    });

    it('should display "No Roles Assigned" when user has no roles', () => {
      employeeService.getUserRoles.and.returnValue(of([]));

      component.ngOnInit();

      expect(component.profileTableData[0].subRole).toBe('No Roles Assigned');
    });

    it('should display "Error Loading Role" when getUserRoles API fails', () => {
      employeeService.getUserRoles.and.returnValue(throwError(() => new Error('API Error')));

      component.ngOnInit();

      expect(component.profileTableData[0].subRole).toBe('Error Loading Role');
    });
  });

  describe('Different Role Scenarios', () => {
    it('should handle manager role correctly', () => {
      const employeeWithManagerRole: BizzCorpEmployee = {
        id: 'emp-123',
        employee_code: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        sub_role_id: 'manager',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithManagerRole));
      employeeService.findRoleByName.and.returnValue(mockUserRoles[1]); // Return manager role

      component.ngOnInit();

      expect(employeeService.findRoleByName).toHaveBeenCalledWith('manager', mockUserRoles);
      expect(component.profileTableData[0].subRole).toBe('Manager with team oversight');
    });

    it('should handle employee role correctly', () => {
      const employeeWithEmployeeRole: BizzCorpEmployee = {
        id: 'emp-123',
        employee_code: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        sub_role_id: 'employee',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithEmployeeRole));
      employeeService.findRoleByName.and.returnValue(mockUserRoles[2]); // Return employee role

      component.ngOnInit();

      expect(employeeService.findRoleByName).toHaveBeenCalledWith('employee', mockUserRoles);
      expect(component.profileTableData[0].subRole).toBe('Standard employee access');
    });

    it('should handle custom role names', () => {
      const customRole: BizzCorpRole = {
        id: 'role-custom',
        name: 'custom_role',
        description: 'Custom role for special permissions',
        is_active: true
      };

      const employeeWithCustomRole: BizzCorpEmployee = {
        id: 'emp-123',
        employee_code: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        sub_role_id: 'custom_role',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithCustomRole));
      employeeService.findRoleByName.and.returnValue(customRole);

      component.ngOnInit();

      expect(employeeService.findRoleByName).toHaveBeenCalledWith('custom_role', mockUserRoles);
      expect(component.profileTableData[0].subRole).toBe('Custom role for special permissions');
    });
  });

  describe('Null Handling with User Roles API', () => {
    it('should display "Not Assigned" when sub_role_id is null', () => {
      const employeeWithNullRole: BizzCorpEmployee = {
        id: 'emp-123',
        employee_code: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        sub_role_id: null as any,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithNullRole));

      component.ngOnInit();

      expect(employeeService.getUserRoles).not.toHaveBeenCalled();
      expect(component.profileTableData[0].subRole).toBe('Not Assigned');
    });

    it('should display "Not Assigned" when sub_role_id is empty string', () => {
      const employeeWithEmptyRole: BizzCorpEmployee = {
        id: 'emp-123',
        employee_code: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        sub_role_id: '',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      employeeService.getAllEmployees.and.returnValue(of([{
        id: 'emp-123',
        office_email: '<EMAIL>'
      }]));
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithEmptyRole));

      component.ngOnInit();

      expect(employeeService.getUserRoles).not.toHaveBeenCalled();
      expect(component.profileTableData[0].subRole).toBe('Not Assigned');
    });
  });

  describe('Helper Methods', () => {
    beforeEach(() => {
      component.profileTableData = [{
        id: 'emp-123',
        employeeCode: 'EMP001',
        firstName: 'John',
        middleName: '',
        lastName: 'Doe',
        bloodGroup: '',
        dateOfBirth: '',
        joiningDate: '',
        reportingDate: '',
        gender: '',
        maritalStatus: '',
        personalEmail: '',
        officeEmail: '',
        phoneNo: '',
        alternateNo: '',
        address: '',
        panNo: '',
        aadharNo: '',
        uanNo: '',
        esicNo: '',
        ctc: 0,
        attendanceBonus: 0,
        pf: 0,
        bankName: '',
        bankAccountNo: '',
        ifscNo: '',
        officeLocation: '',
        shiftTime: '',
        department: '',
        designation: '',
        role: '',
        subRole: 'Administrator with all permissions',
        isActive: true,
        approverCode: '',
        secondApproverCode: '',
        resignedStartDate: '',
        resignedEndDate: '',
        departmentId: '',
        designationId: '',
        subRoleId: 'admin'
      }];
    });

    it('should return resolved role name from profile data', () => {
      const roleName = component.getRoleName('admin');
      expect(roleName).toBe('Administrator with all permissions');
    });

    it('should return "Not Assigned" for null role name', () => {
      const roleName = component.getRoleName(null as any);
      expect(roleName).toBe('Not Assigned');
    });

    it('should return "Not Assigned" for empty role name', () => {
      const roleName = component.getRoleName('');
      expect(roleName).toBe('Not Assigned');
    });
  });

  describe('Refresh User Roles', () => {
    beforeEach(() => {
      component.currentEmployee = {
        id: 'emp-123',
        employee_code: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        sub_role_id: 'admin',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };
      component.profileTableData = [{ subRole: 'Old Role' } as any];
    });

    it('should refresh user roles and update profile', () => {
      employeeService.findRoleByName.and.returnValue(mockUserRoles[0]);

      component.refreshUserRoles();

      expect(employeeService.getUserRoles).toHaveBeenCalled();
      expect(employeeService.findRoleByName).toHaveBeenCalledWith('admin', mockUserRoles);
      expect(component.profileTableData[0].subRole).toBe('Administrator with all permissions');
    });

    it('should handle refresh error gracefully', () => {
      employeeService.getUserRoles.and.returnValue(throwError(() => new Error('API Error')));

      component.refreshUserRoles();

      expect(component.profileTableData[0].subRole).toBe('Error Loading Role');
    });

    it('should not refresh when no sub_role_id exists', () => {
      component.currentEmployee!.sub_role_id = null as any;

      component.refreshUserRoles();

      expect(employeeService.getUserRoles).not.toHaveBeenCalled();
    });
  });
});
