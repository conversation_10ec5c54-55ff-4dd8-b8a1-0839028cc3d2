import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, combineLatest, of } from 'rxjs';
import { map, catchError, tap, shareReplay } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { NewYearActivityService, NewYearActivity } from './new-year-activity.service';
import { SettingsService } from './settings.service';

export interface RestrictedDate {
  date: string; // YYYY-MM-DD format
  reason: string; // 'new_year_activity' | 'holiday'
  description?: string;
  source: 'new_year_activity' | 'holiday_setting';
}

export interface DateRestrictionData {
  restrictedDates: RestrictedDate[];
  newYearActivityDates: string[];
  holidayDates: string[];
  lastUpdated: Date;
}

/**
 * Date Restriction Service
 * Handles date restrictions for OD (Outdoor Duty) and LWP (Leave Without Pay) leave types
 * Fetches data from New Year Activities API and Holiday Settings API
 */
@Injectable({
  providedIn: 'root'
})
export class DateRestrictionService {
  private baseUrl = `${environment.apiUrl}/api/v1`;
  
  // Cache for restriction data
  private restrictionDataSubject = new BehaviorSubject<DateRestrictionData | null>(null);
  public restrictionData$ = this.restrictionDataSubject.asObservable();
  
  // Cache expiry time (5 minutes)
  private cacheExpiryMs = 5 * 60 * 1000;
  private lastFetchTime: Date | null = null;

  constructor(
    private http: HttpClient,
    private newYearActivityService: NewYearActivityService,
    private settingsService: SettingsService
  ) {}

  /**
   * Get all restricted dates for OD and LWP leave types
   * Combines New Year Activities and Holiday Settings data
   * @param forceRefresh Force refresh from API
   * @returns Observable of restriction data
   */
  getRestrictedDates(forceRefresh: boolean = false): Observable<DateRestrictionData> {
    console.log('🚫 DateRestrictionService: Getting restricted dates for OD/LWP...');

    // Check if we have cached data and it's still valid
    const currentData = this.restrictionDataSubject.value;
    const now = new Date();
    
    if (!forceRefresh && currentData && this.lastFetchTime) {
      const timeSinceLastFetch = now.getTime() - this.lastFetchTime.getTime();
      if (timeSinceLastFetch < this.cacheExpiryMs) {
        console.log('✅ DateRestrictionService: Using cached restriction data');
        return of(currentData);
      }
    }

    console.log('🔄 DateRestrictionService: Fetching fresh restriction data from APIs...');

    // Combine both API calls
    return combineLatest([
      this.getNewYearActivityDates(),
      this.getHolidaySettingDates()
    ]).pipe(
      map(([newYearDates, holidayDates]) => {
        console.log('📊 DateRestrictionService: Combining restriction data...');
        console.log('🎊 New Year Activity dates:', newYearDates);
        console.log('🏖️ Holiday Setting dates:', holidayDates);

        // Combine all restricted dates
        const restrictedDates: RestrictedDate[] = [
          ...newYearDates.map(date => ({
            date,
            reason: 'new_year_activity' as const,
            description: 'New Year Activity',
            source: 'new_year_activity' as const
          })),
          ...holidayDates.map(date => ({
            date,
            reason: 'holiday' as const,
            description: 'Holiday',
            source: 'holiday_setting' as const
          }))
        ];

        // Remove duplicates
        const uniqueRestrictedDates = restrictedDates.filter((date, index, self) =>
          index === self.findIndex(d => d.date === date.date)
        );

        const restrictionData: DateRestrictionData = {
          restrictedDates: uniqueRestrictedDates,
          newYearActivityDates: newYearDates,
          holidayDates: holidayDates,
          lastUpdated: now
        };

        console.log(`✅ DateRestrictionService: Combined ${uniqueRestrictedDates.length} unique restricted dates`);
        console.log('🚫 All restricted dates:', uniqueRestrictedDates.map(d => d.date));

        // Update cache
        this.restrictionDataSubject.next(restrictionData);
        this.lastFetchTime = now;

        return restrictionData;
      }),
      catchError(error => {
        console.error('❌ DateRestrictionService: Error fetching restriction data:', error);
        
        // Return empty restriction data on error
        const emptyData: DateRestrictionData = {
          restrictedDates: [],
          newYearActivityDates: [],
          holidayDates: [],
          lastUpdated: now
        };
        
        this.restrictionDataSubject.next(emptyData);
        return of(emptyData);
      }),
      shareReplay(1)
    );
  }

  /**
   * Get dates from New Year Activities API
   * @returns Observable of date strings
   */
  private getNewYearActivityDates(): Observable<string[]> {
    console.log('🎊 DateRestrictionService: Fetching New Year Activity dates...');

    return this.newYearActivityService.getAllActivities().pipe(
      map((response: any) => {
        console.log('🎊 New Year Activities API response:', response);

        let activities: NewYearActivity[] = [];
        
        // Handle different response structures
        if (response && response.success && Array.isArray(response.data)) {
          activities = response.data;
        } else if (Array.isArray(response)) {
          activities = response;
        } else if (response && Array.isArray(response.items)) {
          activities = response.items;
        }

        // Extract dates from activities (check both 'date' and 'holiday_date' fields)
        const dates = activities
          .filter(activity => activity.date || activity.holiday_date)
          .map(activity => {
            // Try 'holiday_date' first (new API format), then 'date' (old format)
            const dateValue = activity.holiday_date || activity.date;
            return this.formatDate(dateValue);
          })
          .filter((date): date is string => date !== null); // Remove invalid dates with type guard

        console.log(`✅ DateRestrictionService: Found ${dates.length} New Year Activity dates:`, dates);
        return dates;
      }),
      catchError(error => {
        console.error('❌ DateRestrictionService: Error fetching New Year Activities:', error);
        return of([]);
      })
    );
  }

  /**
   * Get holiday dates from Settings API
   * @returns Observable of date strings
   */
  private getHolidaySettingDates(): Observable<string[]> {
    console.log('🏖️ DateRestrictionService: Fetching Holiday Setting dates...');

    return this.settingsService.getSettings().pipe(
      map((settings: any[]) => {
        console.log('🏖️ Settings API response:', settings);

        // Filter settings with key "Holiday"
        const holidaySettings = settings.filter(setting => 
          setting.key === 'Holiday' || setting.key === 'holiday'
        );

        console.log('🏖️ Holiday settings found:', holidaySettings);

        const dates: string[] = [];

        // Extract dates from holiday settings
        holidaySettings.forEach(setting => {
          try {
            // Parse the holiday setting value
            let holidayData = setting.value;
            
            // If value is a string, try to parse as JSON
            if (typeof holidayData === 'string') {
              try {
                holidayData = JSON.parse(holidayData);
              } catch (e) {
                // If not JSON, treat as a single date
                const formattedDate = this.formatDate(holidayData);
                if (formattedDate !== null) {
                  dates.push(formattedDate);
                }
                return;
              }
            }

            // Handle different holiday data structures
            if (Array.isArray(holidayData)) {
              // Array of dates or holiday objects
              holidayData.forEach(item => {
                if (typeof item === 'string') {
                  const formattedDate = this.formatDate(item);
                  if (formattedDate !== null) dates.push(formattedDate);
                } else if (item && item.date) {
                  const formattedDate = this.formatDate(item.date);
                  if (formattedDate !== null) dates.push(formattedDate);
                }
              });
            } else if (holidayData && holidayData.date) {
              // Single holiday object
              const formattedDate = this.formatDate(holidayData.date);
              if (formattedDate !== null) dates.push(formattedDate);
            } else if (holidayData && typeof holidayData === 'object') {
              // Object with date properties
              Object.values(holidayData).forEach(value => {
                if (typeof value === 'string') {
                  const formattedDate = this.formatDate(value);
                  if (formattedDate !== null) dates.push(formattedDate);
                }
              });
            }
          } catch (error) {
            console.warn('⚠️ DateRestrictionService: Error parsing holiday setting:', setting, error);
          }
        });

        // Remove duplicates
        const uniqueDates = [...new Set(dates)];

        console.log(`✅ DateRestrictionService: Found ${uniqueDates.length} Holiday Setting dates:`, uniqueDates);
        return uniqueDates;
      }),
      catchError(error => {
        console.error('❌ DateRestrictionService: Error fetching Holiday Settings:', error);
        return of([]);
      })
    );
  }

  /**
   * Check if a specific date is restricted for OD/LWP
   * @param date Date to check (Date object or string)
   * @returns Observable of boolean
   */
  isDateRestricted(date: Date | string): Observable<boolean> {
    const dateStr = this.formatDate(date);
    if (!dateStr) return of(false);

    return this.getRestrictedDates().pipe(
      map(data => {
        const isRestricted = data.restrictedDates.some(restrictedDate => 
          restrictedDate.date === dateStr
        );
        
        if (isRestricted) {
          console.log(`🚫 DateRestrictionService: Date ${dateStr} is restricted for OD/LWP`);
        }
        
        return isRestricted;
      })
    );
  }

  /**
   * Get restriction reason for a specific date
   * @param date Date to check
   * @returns Observable of restriction info or null
   */
  getDateRestrictionInfo(date: Date | string): Observable<RestrictedDate | null> {
    const dateStr = this.formatDate(date);
    if (!dateStr) return of(null);

    return this.getRestrictedDates().pipe(
      map(data => {
        const restriction = data.restrictedDates.find(restrictedDate => 
          restrictedDate.date === dateStr
        );
        
        return restriction || null;
      })
    );
  }

  /**
   * Format date to YYYY-MM-DD string
   * @param date Date object or string
   * @returns Formatted date string or null if invalid
   */
  private formatDate(date: Date | string): string | null {
    try {
      if (!date) return null;
      
      let dateObj: Date;
      
      if (typeof date === 'string') {
        // Handle different date string formats
        if (date.includes('T')) {
          dateObj = new Date(date);
        } else if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
          // Already in YYYY-MM-DD format
          return date;
        } else {
          dateObj = new Date(date);
        }
      } else {
        dateObj = date;
      }

      if (isNaN(dateObj.getTime())) {
        return null;
      }

      return dateObj.toISOString().split('T')[0];
    } catch (error) {
      console.warn('⚠️ DateRestrictionService: Invalid date format:', date, error);
      return null;
    }
  }

  /**
   * Clear the restriction data cache
   */
  clearCache(): void {
    console.log('🗑️ DateRestrictionService: Clearing restriction data cache');
    this.restrictionDataSubject.next(null);
    this.lastFetchTime = null;
  }

  /**
   * Refresh restriction data from APIs
   * @returns Observable of fresh restriction data
   */
  refreshRestrictionData(): Observable<DateRestrictionData> {
    console.log('🔄 DateRestrictionService: Force refreshing restriction data...');
    return this.getRestrictedDates(true);
  }
}
