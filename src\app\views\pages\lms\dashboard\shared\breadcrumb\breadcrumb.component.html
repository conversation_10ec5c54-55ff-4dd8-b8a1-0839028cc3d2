<nav aria-label="breadcrumb" class="breadcrumb-nav mb-4">
  <ol class="breadcrumb">
    <li
      *ngFor="let item of items; let last = last"
      class="breadcrumb-item"
      [class.active]="last"
    >
      <a
        *ngIf="!last && item.route"
        [routerLink]="item.route"
        class="breadcrumb-link"
      >
        {{ item.label }}
      </a>
      <span *ngIf="last || !item.route" class="breadcrumb-current">
        {{ item.label }}
      </span>
    </li>
  </ol>
</nav>
