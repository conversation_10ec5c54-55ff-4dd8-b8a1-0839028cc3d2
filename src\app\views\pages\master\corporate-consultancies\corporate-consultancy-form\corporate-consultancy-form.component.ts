import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import {
  CorporateConsultancyService,
  CorporateConsultancy,
  CorporateConsultancyCreate,
  CorporateConsultancyUpdate
} from '../../../../../core/services/corporate-consultancy.service';
import { PopupDesignService } from '../../../../../core/services/popup-design.service';

@Component({
  selector: 'app-corporate-consultancy-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  templateUrl: './corporate-consultancy-form.component.html',
  styleUrls: ['./corporate-consultancy-form.component.scss']
})
export class CorporateConsultancyFormComponent implements OnInit {
  @Input() isEditMode = false;
  @Input() consultancy: CorporateConsultancy | null = null;

  consultancyForm!: FormGroup;
  saving = false;
  error: string | null = null;

  // Options
  consultancyTypes: any[] = [];
  partnershipLevels: any[] = [];
  countries: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private fb: FormBuilder,
    public consultancyService: CorporateConsultancyService,
    private popupService: PopupDesignService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.loadOptions();
    this.initializeForm();
  }

  /**
   * Load dropdown options
   */
  loadOptions(): void {
    this.consultancyTypes = this.consultancyService.getConsultancyTypes();
    this.partnershipLevels = this.consultancyService.getPartnershipLevels();
    this.countries = this.consultancyService.getCountryList();
    this.regulatoryBodies = this.consultancyService.getRegulatoryBodies();
  }

  /**
   * Initialize the form with validation
   */
  private initializeForm(): void {
    this.consultancyForm = this.fb.group({
      name: [
        this.consultancy?.name || '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100)
        ]
      ],
      code: [
        this.consultancy?.code || '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(10),
          Validators.pattern(/^[A-Z0-9]+$/)
        ]
      ],
      type: [
        this.consultancy?.type || 'financial',
        [Validators.required]
      ],
      specialization: [
        this.consultancy?.specialization || '',
        [Validators.maxLength(100)]
      ],
      description: [
        this.consultancy?.description || '',
        [Validators.maxLength(500)]
      ],
      website: [
        this.consultancy?.website || '',
        [Validators.pattern(/^https?:\/\/.+/)]
      ],
      contact_email: [
        this.consultancy?.contact_email || '',
        [Validators.email]
      ],
      contact_phone: [
        this.consultancy?.contact_phone || '',
        [Validators.pattern(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/)]
      ],
      address: [
        this.consultancy?.address || '',
        [Validators.maxLength(200)]
      ],
      city: [
        this.consultancy?.city || '',
        [Validators.maxLength(50)]
      ],
      state: [
        this.consultancy?.state || '',
        [Validators.maxLength(50)]
      ],
      country: [
        this.consultancy?.country || '',
        []
      ],
      postal_code: [
        this.consultancy?.postal_code || '',
        [Validators.maxLength(20)]
      ],
      established_date: [
        this.consultancy?.established_date ? this.formatDateForInput(this.consultancy.established_date) : '',
        []
      ],
      license_number: [
        this.consultancy?.license_number || '',
        [Validators.maxLength(50)]
      ],
      regulatory_body: [
        this.consultancy?.regulatory_body || '',
        []
      ],
      partnership_level: [
        this.consultancy?.partnership_level || 'standard',
        []
      ],
      rating: [
        this.consultancy?.rating || null,
        [Validators.min(1), Validators.max(5)]
      ],
      annual_revenue: [
        this.consultancy?.annual_revenue || null,
        [Validators.min(0)]
      ],
      employee_count: [
        this.consultancy?.employee_count || null,
        [Validators.min(1)]
      ],
      is_active: [
        this.consultancy?.is_active ?? true,
        [Validators.required]
      ]
    });

    // Add custom validators
    this.consultancyForm.get('website')?.addValidators(this.websiteValidator.bind(this));
  }

  /**
   * Custom validator for website URL
   */
  private websiteValidator(control: any) {
    if (!control.value) return null;
    
    try {
      new URL(control.value);
      return null;
    } catch {
      return { invalidWebsite: { message: 'Please enter a valid website URL' } };
    }
  }

  /**
   * Format date for input field
   */
  private formatDateForInput(dateString: string): string {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get form control for template access
   */
  getFormControl(controlName: string) {
    return this.consultancyForm.get(controlName);
  }

  /**
   * Check if form control has error
   */
  hasError(controlName: string, errorType?: string): boolean {
    const control = this.getFormControl(controlName);
    if (!control) return false;

    if (errorType) {
      return control.hasError(errorType) && (control.dirty || control.touched);
    }

    return control.invalid && (control.dirty || control.touched);
  }

  /**
   * Get error message for form control
   */
  getErrorMessage(controlName: string): string {
    const control = this.getFormControl(controlName);
    if (!control || !control.errors) return '';

    const errors = control.errors;

    if (errors['required']) {
      return `${this.getFieldLabel(controlName)} is required.`;
    }

    if (errors['minlength']) {
      return `${this.getFieldLabel(controlName)} must be at least ${errors['minlength'].requiredLength} characters.`;
    }

    if (errors['maxlength']) {
      return `${this.getFieldLabel(controlName)} cannot exceed ${errors['maxlength'].requiredLength} characters.`;
    }

    if (errors['pattern']) {
      if (controlName === 'code') {
        return 'Code must contain only uppercase letters and numbers.';
      }
      if (controlName === 'website') {
        return 'Website must start with http:// or https://';
      }
      if (controlName === 'contact_phone') {
        return 'Please enter a valid phone number.';
      }
      return `${this.getFieldLabel(controlName)} format is invalid.`;
    }

    if (errors['email']) {
      return 'Please enter a valid email address.';
    }

    if (errors['min']) {
      if (controlName === 'rating') {
        return 'Rating must be between 1 and 5.';
      }
      if (controlName === 'annual_revenue') {
        return 'Annual revenue must be a positive number.';
      }
      if (controlName === 'employee_count') {
        return 'Employee count must be at least 1.';
      }
      return `${this.getFieldLabel(controlName)} is too small.`;
    }

    if (errors['max']) {
      if (controlName === 'rating') {
        return 'Rating must be between 1 and 5.';
      }
      return `${this.getFieldLabel(controlName)} is too large.`;
    }

    if (errors['invalidWebsite']) {
      return errors['invalidWebsite'].message;
    }

    return 'Invalid input.';
  }

  /**
   * Get field label for error messages
   */
  private getFieldLabel(controlName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Consultancy name',
      code: 'Consultancy code',
      type: 'Consultancy type',
      specialization: 'Specialization',
      description: 'Description',
      website: 'Website',
      contact_email: 'Contact email',
      contact_phone: 'Contact phone',
      address: 'Address',
      city: 'City',
      state: 'State',
      country: 'Country',
      postal_code: 'Postal code',
      established_date: 'Established date',
      license_number: 'License number',
      regulatory_body: 'Regulatory body',
      partnership_level: 'Partnership level',
      rating: 'Rating',
      annual_revenue: 'Annual revenue',
      employee_count: 'Employee count',
      is_active: 'Status'
    };
    return labels[controlName] || controlName;
  }

  /**
   * Save consultancy
   */
  save(): void {
    if (this.consultancyForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.saving = true;
    this.error = null;

    const formValue = this.consultancyForm.value;

    // Clean up form data
    const consultancyData = {
      name: formValue.name.trim(),
      code: formValue.code.trim().toUpperCase(),
      type: formValue.type,
      specialization: formValue.specialization?.trim() || undefined,
      description: formValue.description?.trim() || undefined,
      website: formValue.website?.trim() || undefined,
      contact_email: formValue.contact_email?.trim() || undefined,
      contact_phone: formValue.contact_phone?.trim() || undefined,
      address: formValue.address?.trim() || undefined,
      city: formValue.city?.trim() || undefined,
      state: formValue.state?.trim() || undefined,
      country: formValue.country || undefined,
      postal_code: formValue.postal_code?.trim() || undefined,
      established_date: formValue.established_date || undefined,
      license_number: formValue.license_number?.trim() || undefined,
      regulatory_body: formValue.regulatory_body || undefined,
      partnership_level: formValue.partnership_level || undefined,
      rating: formValue.rating || undefined,
      annual_revenue: formValue.annual_revenue || undefined,
      employee_count: formValue.employee_count || undefined,
      is_active: formValue.is_active
    };

    const operation = this.isEditMode
      ? this.consultancyService.updateConsultancy(this.consultancy!.id, consultancyData as CorporateConsultancyUpdate)
      : this.consultancyService.createConsultancy(consultancyData as CorporateConsultancyCreate);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.activeModal.close('saved');
        } else {
          this.error = response.error || 'Failed to save consultancy.';
          this.saving = false;
        }
      },
      error: (error) => {
        this.error = error.message;
        this.saving = false;

        this.popupService.showError({
          title: 'Save Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Mark all form controls as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.consultancyForm.controls).forEach(key => {
      const control = this.consultancyForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Reset form
   */
  reset(): void {
    this.consultancyForm.reset();
    this.initializeForm();
    this.error = null;
  }

  /**
   * Cancel and close modal
   */
  cancel(): void {
    if (this.consultancyForm.dirty) {
      this.popupService.showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        confirmText: 'Yes, Cancel',
        cancelText: 'Continue Editing'
      }).then((result) => {
        if (result.isConfirmed) {
          this.activeModal.dismiss('cancelled');
        }
      });
    } else {
      this.activeModal.dismiss('cancelled');
    }
  }

  /**
   * Get modal title
   */
  getModalTitle(): string {
    return this.isEditMode ? 'Edit Corporate Consultancy' : 'Create New Corporate Consultancy';
  }

  /**
   * Get save button text
   */
  getSaveButtonText(): string {
    if (this.saving) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update Consultancy' : 'Create Consultancy';
  }

  /**
   * Auto-generate code from name
   */
  generateCodeFromName(): void {
    const name = this.consultancyForm.get('name')?.value;
    if (name && !this.consultancyForm.get('code')?.value) {
      const code = name
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .substring(0, 6);
      
      this.consultancyForm.get('code')?.setValue(code);
    }
  }

  /**
   * Validate and format website URL
   */
  formatWebsiteUrl(): void {
    const website = this.consultancyForm.get('website')?.value;
    if (website && !website.startsWith('http')) {
      this.consultancyForm.get('website')?.setValue(`https://${website}`);
    }
  }

  /**
   * Get consultancy type description
   */
  getConsultancyTypeDescription(type: string): string {
    const typeObj = this.consultancyTypes.find(t => t.value === type);
    return typeObj ? typeObj.description : '';
  }

  /**
   * Get partnership level description
   */
  getPartnershipLevelDescription(level: string): string {
    const levelObj = this.partnershipLevels.find(l => l.value === level);
    return levelObj ? levelObj.description : '';
  }

  /**
   * Format revenue display
   */
  formatRevenueDisplay(amount?: number): string {
    return this.consultancyService.formatRevenue(amount);
  }
}
