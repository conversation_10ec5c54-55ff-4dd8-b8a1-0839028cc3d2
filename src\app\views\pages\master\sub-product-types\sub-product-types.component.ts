import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import {
  SubProductTypeService,
  SubProductType,
  SubProductTypeStatistics
} from '../../../../core/services/sub-product-type.service';
import { ProductTypeService } from '../../../../core/services/product-type.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { SubProductTypeFormComponent } from './sub-product-type-form/sub-product-type-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-sub-product-types',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './sub-product-types.component.html',
  styleUrls: ['./sub-product-types.component.scss']
})
export class SubProductTypesComponent implements OnInit {
  // Data properties
  subProductTypes: SubProductType[] = [];
  deletedSubProductTypes: SubProductType[] = [];
  statistics: SubProductTypeStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedCategory = '';
  selectedProductType = '';
  selectedFeatured: 'all' | 'featured' | 'regular' = 'all';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedSubProductTypes: Set<string> = new Set();
  selectAll = false;

  // Filter options
  subProductTypeCategories: any[] = [];
  productTypes: any[] = [];
  formFieldTypes: any[] = [];

  constructor(
    private subProductTypeService: SubProductTypeService,
    private productTypeService: ProductTypeService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadSubProductTypes();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.subProductTypeCategories = this.subProductTypeService.getSubProductTypeCategories();
    this.formFieldTypes = this.subProductTypeService.getFormFieldTypes();

    // Load product types for filtering
    this.productTypeService.getProductTypesDropdown().subscribe({
      next: (productTypes) => {
        this.productTypes = productTypes;
        this.cdr.markForCheck();
      },
      error: (error) => {
        console.error('Failed to load product types:', error);
      }
    });
  }

  /**
   * Load sub product types with current filters
   */
  loadSubProductTypes(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      category: this.selectedCategory || undefined,
      product_type_id: this.selectedProductType || undefined,
      is_featured: this.selectedFeatured === 'all' ? undefined : this.selectedFeatured === 'featured',
      include_deleted: this.viewMode === 'deleted'
    };

    this.subProductTypeService.getSubProductTypesWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedSubProductTypes = response.data.filter(spt => spt.deleted_at);
            this.subProductTypes = [];
          } else {
            this.subProductTypes = response.data.filter(spt => !spt.deleted_at);
            this.deletedSubProductTypes = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load sub product types';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load sub product types. Please try again.'
        });
      }
    });
  }

  /**
   * Load sub product type statistics
   */
  loadStatistics(): void {
    this.subProductTypeService.getSubProductTypeStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search sub product types
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadSubProductTypes();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadSubProductTypes();
  }

  /**
   * Filter by category
   */
  onCategoryFilter(): void {
    this.currentPage = 1;
    this.loadSubProductTypes();
  }

  /**
   * Filter by product type
   */
  onProductTypeFilter(): void {
    this.currentPage = 1;
    this.loadSubProductTypes();
  }

  /**
   * Filter by featured status
   */
  onFeaturedFilter(): void {
    this.currentPage = 1;
    this.loadSubProductTypes();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedSubProductTypes.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadSubProductTypes();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadSubProductTypes();
  }

  /**
   * Open create sub product type modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(SubProductTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadSubProductTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Sub product type created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit sub product type modal
   */
  openEditModal(subProductType: SubProductType): void {
    const modalRef = this.modalService.open(SubProductTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.subProductType = { ...subProductType };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadSubProductTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Sub product type updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete sub product type
   */
  deleteSubProductType(subProductType: SubProductType): void {
    this.popupService.showConfirmation({
      title: 'Delete Sub Product Type',
      message: `Are you sure you want to delete "${subProductType.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.subProductTypeService.deleteSubProductType(subProductType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadSubProductTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Sub product type deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete sub product type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore sub product type
   */
  restoreSubProductType(subProductType: SubProductType): void {
    this.popupService.showConfirmation({
      title: 'Restore Sub Product Type',
      message: `Are you sure you want to restore "${subProductType.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.subProductTypeService.restoreSubProductType(subProductType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadSubProductTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Sub product type restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore sub product type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadSubProductTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Sub product types uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.subProductTypeService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'sub_product_types_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadSubProductTypes();
    this.loadStatistics();
  }

  /**
   * Get sub product type category label
   */
  getSubProductTypeCategoryLabel(category: string): string {
    const categories = this.subProductTypeCategories;
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? categoryObj.label : category;
  }

  /**
   * Get product type name
   */
  getProductTypeName(productTypeId: string): string {
    const productType = this.productTypes.find(pt => pt.id === productTypeId);
    return productType ? productType.name : 'Unknown';
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    const badgeClasses = {
      'variant': 'badge bg-primary',
      'addon': 'badge bg-info',
      'component': 'badge bg-success',
      'service': 'badge bg-warning',
      'configuration': 'badge bg-secondary',
      'other': 'badge bg-dark'
    };
    return badgeClasses[category as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Get featured badge class
   */
  getFeaturedBadgeClass(isFeatured: boolean): string {
    return isFeatured ? 'badge bg-warning' : '';
  }

  /**
   * Get form schema summary
   */
  getFormSchemaSummary(subProductType: SubProductType): string {
    if (!subProductType.form_schema?.fields?.length) {
      return 'No form fields';
    }
    const fieldCount = subProductType.form_schema.fields.length;
    const requiredCount = subProductType.form_schema.fields.filter(f => f.required).length;
    return `${fieldCount} fields (${requiredCount} required)`;
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): SubProductType[] {
    return this.viewMode === 'deleted' ? this.deletedSubProductTypes : this.subProductTypes;
  }

  /**
   * Track by function for ngFor performance
   */
  trackBySubProductTypeId(index: number, subProductType: SubProductType): string {
    return subProductType.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
