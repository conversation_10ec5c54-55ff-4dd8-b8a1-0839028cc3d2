# ================================
# PRODUCTION DOCKERFILE
# Multi-stage build for optimized production deployment
# ================================

# ================================
# Stage 1: Build Stage
# ================================
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app



# Set environment variables for build optimization
ENV NODE_ENV=production


# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && ln -sf python3 /usr/bin/python

# Copy package files and npm configuration first for better caching
COPY package*.json .npmrc ./

# Install Angular CLI globally (only needed for build)
RUN npm install -g @angular/cli@latest

# Install dependencies (production + dev dependencies needed for build)
# Ensure devDependencies are installed for Angular build tools
RUN npm ci --include=dev --legacy-peer-deps || \
    npm install --include=dev --legacy-peer-deps || \
    npm install --force || \
    (npm cache clean --force && npm install --include=dev --legacy-peer-deps --no-audit)

    
# Verify Angular CLI and build tools are available
RUN npx ng version || echo "Angular CLI check failed, but continuing..."

# Copy source code
COPY . .

# Build the Angular application for production
# Use npx to ensure local Angular CLI is used, with multiple fallback strategies
RUN ng build


# ================================
# Stage 2: Production Stage
# ================================
FROM nginx:1.25-alpine AS production

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Copy built application from builder stage
COPY --from=builder /app/dist/demo1/browser /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy .htaccess rules to nginx (if needed)
COPY public/.htaccess /usr/share/nginx/html/

# Set proper permissions
RUN chown -R appuser:appgroup /usr/share/nginx/html && \
    chown -R appuser:appgroup /var/cache/nginx && \
    chown -R appuser:appgroup /var/log/nginx && \
    chown -R appuser:appgroup /etc/nginx/conf.d

# Create nginx PID directory with proper permissions
RUN mkdir -p /var/run/nginx && \
    chown -R appuser:appgroup /var/run/nginx

# Switch to non-root user
USER appuser

# Expose port 8020
EXPOSE 8020

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8020/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
