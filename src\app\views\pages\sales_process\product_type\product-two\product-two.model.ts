export interface LoanFormData {
    // Personal Information
    age: number;
    address: string;
    addressType: string; // Owned/Rental/Parental
    status: string;

    // Employment/Business Information
    profession: string;
    jobProfile: string; // Private/Government/Semi Government/Proprietorship Company
    typeOfBusiness: string;
    companyName: string; // or Business Name
    businessPlace: string;
    currentJobExperience: number; // in years
    totalPastJobExperience: number; // in years
    currentBusinessExperience: number; // in years
    totalPastBusinessExperience: number; // in years

    // Business Financial Information
    monthlyTurnover: number;
    monthlyAvgExpense: number;
    monthlyNetProfit: number;
    itrStatus: string; // ITR Since

    // Fiscal Year Information
    fyYear1: string; // 22-23
    fyYear1Gross: number;
    fyYear1Net: number;
    fyYear1Date: Date;

    fyYear2: string; // 23-24
    fyYear2Gross: number;
    fyYear2Net: number;
    fyYear2Date: Date;

    fyYear3: string; // 24-25
    fyYear3Gross: number;
    fyYear3Net: number;
    fyYear3Date: Date;

    otherIncomeDetails: string;
    grossIncome: number;

    // Banking Information
    currentAccount: string;
    currentAccountBankName: string;
    savingAccount: string;
    savingAccountBankName: string;
    businessStatutoryDoc: string;

    // Document Information
    appointmentLetter: boolean;
    confirmationLetter: boolean;
    salarySlip: boolean;
    salaryCertificate: boolean;

    // Salary Information
    totalGrossSalary: number;

    // Deductions
    deductionPF: number;
    deductionPT: number;
    deductionHRA: number;
    deductionESIC: number;
    deductionEmpLoan: number;
    deductionSocLoan: number;
    deductionOther: number;
    totalDeduction: number;

    netSalary: number;
    salaryMode: string; // Cash/Cheque/A/C Pay

    // Tax Information
    itrAvailable: boolean;
    formNo16: boolean;

    // Property Information
    propertySelected: boolean;
    propertyLocation: string;
    propertyType: string;
    propertyDetails: string;
    propertyArea: number;
    propertyConfiguration: string;
    propertyStatus: string;
    ucPercentage: number;
    propertyAge: number;
    societyStatus: string;
    ccOc: string; // CC/OC
    agreementValue: number;
    purchaseDate: Date;
    currentMarketValue: number;
    loanAmountRequired: number;
    ltvPercentage: number;
    ocr: number;
    ocrPercentage: number;
    bankLoanAmount: number;
    projectAPF: string;

    // Existing Loans
    existingLoans: ExistingLoan[];

    // Investments
    investments: Investment[];

    // Co Applicants
    coApplicants: CoApplicant[];
  }

  export interface ExistingLoan {
    loanType: string; // PL/BL/GL/VL/CL/HL/LAP/LRD
    bankName: string;
    sanctionedAmount: number;
    disbursedAmount: number;
    outstandingAmount: number;
    startDate: Date;
    tenure: number; // in months
    emiAmount: number;
    defaulted: boolean;
  }

  export interface Investment {
    instituteName: string;
    investmentProduct: string;
    yearlyAmount: number;
    investmentMode: string;
    startDate: Date;
    endDate: Date;
    currentSavingAmount: number;
  }

  export enum AddressType {
    Owned = 'Owned',
    Rental = 'Rental',
    Parental = 'Parental'
  }

  export enum JobProfileType {
    Private = 'Private',
    Government = 'Government',
    SemiGovernment = 'Semi Government',
    Proprietorship = 'Proprietorship Company'
  }

  export enum SalaryMode {
    Cash = 'Cash',
    Cheque = 'Cheque',
    AccountPay = 'A/C Pay'
  }

  export enum LoanType {
    PL = 'PL',
    BL = 'BL',
    GL = 'GL',
    VL = 'VL',
    CL = 'CL',
    HL = 'HL',
    LAP = 'LAP',
    LRD = 'LRD',
    OD = 'OD',
    CC = 'CC'
  }

  export enum InvestmentMode {
    Monthly = 'Monthly',
    Quarterly = 'Quarterly',
    HalfYearly = 'Half Yearly',
    Yearly = 'Yearly',
    OneTime = 'One Time'
  }

  export enum ProfessionType {
    Salaried = 'Salaried',
    SelfEmployed = 'Self Employed',
    Business = 'Business',
    Professional = 'Professional',
    Other = 'Other'
  }

  export enum BusinessType {
    Manufacturing = 'Manufacturing',
    Trading = 'Trading',
    Service = 'Service',
    Retail = 'Retail',
    Wholesale = 'Wholesale',
    Other = 'Other'
  }

  export enum RelationshipType {
    Spouse = 'Spouse',
    Parent = 'Parent',
    Child = 'Child',
    Sibling = 'Sibling',
    Friend = 'Friend',
    Other = 'Other'
  }

  export interface CoApplicant {
    name: string;
    relationship: string;
    age: number;
    profession: string;
    monthlyIncome: number;
    contactNo: string;
    address: string;
  }
