import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { PermissionErrorService, PermissionErrorDetails } from '../services/permission-error.service';

@Component({
  selector: 'app-permission-error-notification',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      *ngIf="currentError" 
      class="permission-error-notification"
      [class.show]="showNotification"
    >
      <div class="notification-content">
        <div class="notification-header">
          <i class="icon-alert-circle error-icon"></i>
          <h4>Access Denied</h4>
          <button 
            class="close-btn" 
            (click)="dismissError()"
            aria-label="Close notification"
          >
            <i class="icon-x"></i>
          </button>
        </div>
        
        <div class="notification-body">
          <p class="error-message">{{ currentError.message }}</p>
          
          <div *ngIf="currentError.suggestions && currentError.suggestions.length > 0" class="suggestions">
            <h5>What you can do:</h5>
            <ul>
              <li *ngFor="let suggestion of currentError.suggestions">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
        
        <div class="notification-actions">
          <button 
            class="btn btn-primary btn-sm" 
            (click)="goToDashboard()"
          >
            Go to Dashboard
          </button>
          <button 
            class="btn btn-outline-secondary btn-sm" 
            (click)="dismissError()"
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .permission-error-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      max-width: 400px;
      background: #fff;
      border: 1px solid #dc3545;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      transform: translateX(100%);
      transition: transform 0.3s ease-in-out;
    }

    .permission-error-notification.show {
      transform: translateX(0);
    }

    .notification-content {
      padding: 16px;
    }

    .notification-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }

    .error-icon {
      color: #dc3545;
      font-size: 20px;
      margin-right: 8px;
    }

    .notification-header h4 {
      margin: 0;
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: #dc3545;
    }

    .close-btn {
      background: none;
      border: none;
      color: #6c757d;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
    }

    .close-btn:hover {
      background-color: #f8f9fa;
    }

    .notification-body {
      margin-bottom: 16px;
    }

    .error-message {
      margin: 0 0 12px 0;
      color: #495057;
      font-size: 14px;
      line-height: 1.4;
    }

    .suggestions {
      background-color: #f8f9fa;
      padding: 12px;
      border-radius: 4px;
      border-left: 3px solid #17a2b8;
    }

    .suggestions h5 {
      margin: 0 0 8px 0;
      font-size: 13px;
      font-weight: 600;
      color: #17a2b8;
    }

    .suggestions ul {
      margin: 0;
      padding-left: 16px;
    }

    .suggestions li {
      font-size: 13px;
      color: #495057;
      margin-bottom: 4px;
    }

    .notification-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }

    .btn {
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
      text-decoration: none;
      cursor: pointer;
      border: 1px solid transparent;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      color: #fff;
    }

    .btn-primary:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }

    .btn-outline-secondary {
      color: #6c757d;
      border-color: #6c757d;
      background-color: transparent;
    }

    .btn-outline-secondary:hover {
      background-color: #6c757d;
      color: #fff;
    }

    .btn-sm {
      padding: 4px 8px;
      font-size: 12px;
    }

    /* Animation for mobile */
    @media (max-width: 768px) {
      .permission-error-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
      }
    }
  `]
})
export class PermissionErrorNotificationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  currentError: PermissionErrorDetails | null = null;
  showNotification = false;

  constructor(private errorService: PermissionErrorService) {}

  ngOnInit(): void {
    this.errorService.currentError$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => {
        this.currentError = error;
        if (error) {
          // Small delay to trigger animation
          setTimeout(() => {
            this.showNotification = true;
          }, 100);

          // Auto-dismiss after 10 seconds
          setTimeout(() => {
            this.dismissError();
          }, 10000);
        } else {
          this.showNotification = false;
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  dismissError(): void {
    this.showNotification = false;
    setTimeout(() => {
      this.errorService.clearCurrentError();
    }, 300); // Wait for animation to complete
  }

  goToDashboard(): void {
    if (this.currentError) {
      const redirectRoute = this.errorService.getRedirectRoute(this.currentError.userPermissions);
      window.location.href = redirectRoute;
    }
    this.dismissError();
  }
}
