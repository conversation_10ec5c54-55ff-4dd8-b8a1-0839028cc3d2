import { Injectable } from '@angular/core';
import { AuthService } from '../services/auth.service';
import { SessionExpirationService } from '../services/session-expiration.service';
import { AutoLoginService } from '../services/auto-login.service';
import { RefreshTokenTestService } from '../services/refresh-token-test.service';

@Injectable({
  providedIn: 'root'
})
export class SessionInitializer {
  
  constructor(
    private authService: AuthService,
    private sessionExpirationService: SessionExpirationService,
    private autoLoginService: AutoLoginService,
    private refreshTokenTestService: RefreshTokenTestService
  ) {}

  /**
   * Initialize session monitoring and user activity tracking
   */
  initialize(): Promise<void> {
    return new Promise((resolve) => {
      console.log('🔄 Initializing session management...');

      // Initialize session expiration service (this ensures it's created and starts monitoring)
      console.log('🔄 Initializing session expiration monitoring...');
      this.sessionExpirationService.getSessionStatus(); // This triggers service initialization

      // Check if user is logged in and initialize permissions
      if (this.authService.isLoggedIn()) {
        console.log('👤 User is logged in, initializing permissions...');

        // Initialize user permissions from API
        this.authService.initializeUserPermissions().subscribe({
          next: () => {
            console.log('✅ User permissions initialized');
            this.setupUserActivityTracking();
            this.initializeDebugUtilities();
            resolve();
          },
          error: (error) => {
            console.error('❌ Failed to initialize user permissions:', error);
            // Continue anyway, don't block app startup
            this.setupUserActivityTracking();
            this.initializeDebugUtilities();
            resolve();
          }
        });
      } else {
        // Check if auto-login should be attempted
        if (this.autoLoginService.shouldAttemptAutoLogin()) {
          console.log('🔄 Auto-login conditions met, attempting automatic login...');
          this.autoLoginService.handleStartupAutoLogin().then((success) => {
            if (success) {
              console.log('✅ Auto-login completed successfully');
            } else {
              console.log('❌ Auto-login failed, continuing with normal startup');
            }
            this.setupUserActivityTracking();
            this.initializeDebugUtilities();
            resolve();
          }).catch(() => {
            console.log('❌ Auto-login error, continuing with normal startup');
            this.setupUserActivityTracking();
            this.initializeDebugUtilities();
            resolve();
          });
        } else {
          console.log('👤 No user logged in and auto-login not available, skipping permission initialization');
          this.setupUserActivityTracking();
          this.initializeDebugUtilities();
          resolve();
        }
      }
    });
  }



  /**
   * Set up user activity tracking to reset session warnings
   */
  private setupUserActivityTracking(): void {
    // Track user activity to reset session warnings
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    let activityTimeout: any;

    const onActivity = () => {
      // Debounce activity tracking to avoid excessive calls
      if (activityTimeout) {
        clearTimeout(activityTimeout);
      }

      activityTimeout = setTimeout(() => {
        this.sessionExpirationService.onUserActivity();
      }, 1000);
    };

    // Add event listeners for user activity
    activityEvents.forEach(event => {
      document.addEventListener(event, onActivity, true);
    });

    console.log('✅ User activity tracking initialized');
  }

  /**
   * Initialize debug utilities for session testing
   */
  private initializeDebugUtilities(): void {
    // Debug utilities removed for production build
    console.log('🧪 Debug utilities disabled in production build');
  }
}

/**
 * Factory function for APP_INITIALIZER
 */
export function sessionInitializerFactory(sessionInitializer: SessionInitializer) {
  return () => sessionInitializer.initialize();
}
