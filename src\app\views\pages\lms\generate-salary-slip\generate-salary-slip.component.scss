
// Table responsive wrapper
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  min-width: 1300px; // Ensure minimum width for all columns (reduced from 1400px)

  thead {
    background-color: rgba(223, 83, 22, 0.05);

    th {
      font-weight: 600;
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      color: #495057;
      padding: 8px 6px;
      border-top: none;
      border-bottom: 1px solid rgba(223, 83, 22, 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;
      white-space: nowrap;
      text-align: center;

      &.asc:after, &.desc:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      &.asc:after {
        border-bottom: 4px solid #df5316;
      }

      &.desc:after {
        border-top: 4px solid #df5316;
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(223, 83, 22, 0.02);
      }

      td {
        vertical-align: middle;
        padding: 8px 6px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.85rem;
        white-space: nowrap;
        text-align: center;

        &.action-icons {
          text-align: center;
          width: 60px;
        }
      }
    }
  }
}

