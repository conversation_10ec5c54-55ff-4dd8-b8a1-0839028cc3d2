import { Compo<PERSON>, OnInit, QueryList, ViewChildren, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import {
  User,
  UserFilter,
  UserResponse,
  UserAction,
  UserActionEvent,
  UserTableColumn,
  UserSortConfig,
  UserPaginationConfig,
  UserStatus
} from '../../../../core/models/user.model';
import { catchError, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

// Sortable header directive (reused from existing components)
import { Directive, EventEmitter, Input, Output } from '@angular/core';

export type SortColumn = keyof User | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    NgbdSortableHeader
  ],
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  // Data
  users: User[] = [];
  filteredUsers: User[] = [];
  selectedUser: User | null = null;

  // Loading states
  loading = false;
  deleting = false;
  restoring = false;
  deletingUserId: string | null = null;
  restoringUserId: string | null = null;

  // View mode
  viewMode: 'table' | 'cards' = 'table';

  // Search and filters
  searchTerm = new FormControl('', { nonNullable: true });
  statusFilter = new FormControl('all', { nonNullable: true });
  roleFilter = new FormControl('all', { nonNullable: true });

  // Pagination
  paginationConfig: UserPaginationConfig = {
    page: 1,
    pageSize: 10,
    totalItems: 0,
    pageSizeOptions: [5, 10, 20, 50]
  };

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;
  sortConfig: UserSortConfig = { column: 'created_at', direction: 'desc' };

  // Table columns configuration
  tableColumns: UserTableColumn[] = [
    { key: 'full_name', label: 'Name', sortable: true, type: 'text' },
    { key: 'email', label: 'Email', sortable: true, type: 'email' },
    { key: 'is_active', label: 'Status', sortable: true, type: 'boolean' },
    { key: 'is_superuser', label: 'Role', sortable: true, type: 'boolean' },
    { key: 'created_at', label: 'Created', sortable: true, type: 'date' },
    { key: 'actions', label: 'Actions', sortable: false, type: 'actions', width: '150px' }
  ];

  // Make Math available in template
  Math = Math;

  // User status enum for template
  UserStatus = UserStatus;

  constructor(
    private userService: UserService,
    private authService: AuthService,
    private modalService: NgbModal,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUsers();
    this.setupSearchAndFilters();
  }

  /**
   * Setup search and filter subscriptions
   */
  private setupSearchAndFilters(): void {
    // Search term changes
    this.searchTerm.valueChanges.subscribe(() => {
      this.applyFilters();
    });

    // Status filter changes
    this.statusFilter.valueChanges.subscribe(() => {
      this.applyFilters();
    });

    // Role filter changes
    this.roleFilter.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  /**
   * Load users from API
   */
  loadUsers(): void {
    this.loading = true;

    const filters: UserFilter = {
      is_deleted: false // Only show non-deleted users by default
    };

    this.userService.getAllUsers(0, 1000, filters)
      .pipe(
        catchError(error => {
          console.error('Failed to load users:', error);
          this.showErrorMessage('Failed to load users. Please try again.');
          return of({ items: [], total: 0, page: 1, size: 0, pages: 0 } as UserResponse);
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        this.users = response.items;
        this.paginationConfig.totalItems = response.total;
        this.applyFilters();
      });
  }

  /**
   * Apply search and filters
   */
  applyFilters(): void {
    let filtered = [...this.users];

    // Apply search filter
    const searchTerm = this.searchTerm.value.toLowerCase().trim();
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.full_name?.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm) ||
        user.first_name?.toLowerCase().includes(searchTerm) ||
        user.last_name?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply status filter
    const statusFilter = this.statusFilter.value;
    if (statusFilter !== 'all') {
      if (statusFilter === 'active') {
        filtered = filtered.filter(user => user.is_active === true);
      } else if (statusFilter === 'inactive') {
        filtered = filtered.filter(user => user.is_active === false);
      } else if (statusFilter === 'deleted') {
        filtered = filtered.filter(user => user.is_deleted === true);
      }
    }

    // Apply role filter
    const roleFilter = this.roleFilter.value;
    if (roleFilter !== 'all') {
      if (roleFilter === 'superuser') {
        filtered = filtered.filter(user => user.is_superuser === true);
      } else if (roleFilter === 'user') {
        filtered = filtered.filter(user => user.is_superuser === false);
      }
    }

    // Apply sorting
    this.applySorting(filtered);

    this.filteredUsers = filtered;
    this.paginationConfig.totalItems = filtered.length;
    this.paginationConfig.page = 1; // Reset to first page when filtering
  }

  /**
   * Apply sorting to filtered users
   */
  private applySorting(users: User[]): void {
    if (!this.sortConfig.column) return;

    users.sort((a, b) => {
      const aVal = a[this.sortConfig.column];
      const bVal = b[this.sortConfig.column];

      let result = 0;
      if (aVal < bVal) result = -1;
      else if (aVal > bVal) result = 1;

      return this.sortConfig.direction === 'desc' ? -result : result;
    });
  }

  /**
   * Handle sorting
   */
  onSort(event: SortEvent): void {
    if (event.direction === '') {
      // Reset sorting
      this.sortConfig = { column: 'created_at', direction: 'desc' };
    } else {
      this.sortConfig = { column: event.column as keyof User, direction: event.direction as 'asc' | 'desc' };
    }

    // Update header states
    this.headers.forEach(header => {
      if (header.sortable !== event.column) {
        header.direction = '';
      }
    });

    this.applyFilters();
  }

  /**
   * Get paginated users for current page
   */
  get paginatedUsers(): User[] {
    const startIndex = (this.paginationConfig.page - 1) * this.paginationConfig.pageSize;
    const endIndex = startIndex + this.paginationConfig.pageSize;
    return this.filteredUsers.slice(startIndex, endIndex);
  }

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    this.paginationConfig.page = page;
  }

  /**
   * Handle page size change
   */
  onPageSizeChange(pageSize: number): void {
    this.paginationConfig.pageSize = pageSize;
    this.paginationConfig.page = 1; // Reset to first page
  }

  /**
   * Toggle view mode between table and cards
   */
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'table' ? 'cards' : 'table';
  }

  /**
   * Handle user action events
   */
  onUserAction(event: UserActionEvent): void {
    switch (event.action) {
      case 'view':
        this.viewUser(event.user);
        break;
      case 'edit':
        this.editUser(event.user);
        break;
      case 'delete':
        this.deleteUser(event.user);
        break;
      case 'restore':
        this.restoreUser(event.user);
        break;
      case 'activate':
        this.toggleUserStatus(event.user, true);
        break;
      case 'deactivate':
        this.toggleUserStatus(event.user, false);
        break;
    }
  }

  /**
   * View user details
   */
  viewUser(user: User): void {
    this.router.navigate(['/user-management/users', user.id]);
  }

  /**
   * Edit user
   */
  editUser(user: User): void {
    this.router.navigate(['/user-management/users', user.id, 'edit']);
  }

  /**
   * Create new user
   */
  createUser(): void {
    this.router.navigate(['/user-management/users/create']);
  }

  /**
   * Delete user (soft delete)
   */
  deleteUser(user: User): void {
    Swal.fire({
      title: 'Delete User',
      text: `Are you sure you want to delete ${user.full_name || user.email}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.performDeleteUser(user);
      }
    });
  }

  /**
   * Perform user deletion
   */
  private performDeleteUser(user: User): void {
    this.deleting = true;
    this.deletingUserId = user.id;

    this.userService.softDeleteUser(user.id)
      .pipe(
        catchError(error => {
          console.error('Failed to delete user:', error);
          this.showErrorMessage('Failed to delete user. Please try again.');
          return of(false);
        }),
        finalize(() => {
          this.deleting = false;
          this.deletingUserId = null;
        })
      )
      .subscribe(success => {
        if (success) {
          this.showSuccessMessage('User deleted successfully.');
          this.loadUsers(); // Reload users
        }
      });
  }

  /**
   * Restore deleted user
   */
  restoreUser(user: User): void {
    this.restoring = true;
    this.restoringUserId = user.id;

    this.userService.restoreUser(user.id)
      .pipe(
        catchError(error => {
          console.error('Failed to restore user:', error);
          this.showErrorMessage('Failed to restore user. Please try again.');
          return of(null);
        }),
        finalize(() => {
          this.restoring = false;
          this.restoringUserId = null;
        })
      )
      .subscribe(restoredUser => {
        if (restoredUser) {
          this.showSuccessMessage('User restored successfully.');
          this.loadUsers(); // Reload users
        }
      });
  }

  /**
   * Toggle user active status
   */
  toggleUserStatus(user: User, isActive: boolean): void {
    const action = isActive ? 'activate' : 'deactivate';

    this.userService.updateUser(user.id, { is_active: isActive })
      .pipe(
        catchError(error => {
          console.error(`Failed to ${action} user:`, error);
          this.showErrorMessage(`Failed to ${action} user. Please try again.`);
          return of(null);
        })
      )
      .subscribe(updatedUser => {
        if (updatedUser) {
          this.showSuccessMessage(`User ${action}d successfully.`);
          this.loadUsers(); // Reload users
        }
      });
  }

  /**
   * Check if user can perform action
   */
  canPerformAction(action: UserAction): boolean {
    switch (action) {
      case 'view':
        return this.authService.hasPermission('users:read');
      case 'edit':
        return this.authService.hasPermission('users:update');
      case 'delete':
        return this.authService.hasPermission('users:delete');
      case 'restore':
        return this.authService.hasPermission('users:update');
      case 'activate':
      case 'deactivate':
        return this.authService.hasPermission('users:update');
      default:
        return false;
    }
  }

  /**
   * Show success message
   */
  private showSuccessMessage(message: string): void {
    Swal.fire({
      icon: 'success',
      title: 'Success!',
      text: message,
      timer: 3000,
      showConfirmButton: false
    });
  }

  /**
   * Show error message
   */
  private showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error!',
      text: message,
      confirmButtonText: 'OK'
    });
  }
}
