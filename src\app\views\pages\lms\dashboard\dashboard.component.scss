.dashboard-card{
    padding: 15px;
    text-align: center;
}

// LMS card icon styling
.lms-card-icon {
    width: 60px;
    height: 60px;
    object-fit: contain;
}

// Compoff modal styling
.modal-body {
    .form-label {
        font-weight: 500;
        color: #3F828B;
    }

    .form-control, .form-select {
        &:focus {
            border-color: #3F828B;
            box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
        }
    }

    // Section title styling
    .section-title {
        color: #3F828B;
        font-weight: 600;
        font-size: 14px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e9ecef;
        margin-bottom: 15px;
    }
}

// Button styling for compoff modals
.btn-primary {
    background-color: #3F828B;
    border-color: #3F828B;

    &:hover, &:focus, &:active {
        background-color: darken(#3F828B, 10%);
        border-color: darken(#3F828B, 10%);
    }
}

// Date input container styling
.date-input-container {
    position: relative;

    &:hover {
        .form-control {
            border-color: #3F828B;
            box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.15);
        }
    }

    .form-control {
        cursor: pointer;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

        &::-webkit-calendar-picker-indicator {
            cursor: pointer;
            opacity: 0.7;

            &:hover {
                opacity: 1;
            }
        }
    }
}

// Comp off calendar styling - highlight enabled dates (weekends/holidays)
::ng-deep .comp-off-calendar {
    .ngb-dp-day {
        // Default styling for all dates
        border-radius: 4px;
        transition: all 0.2s ease;

        // Enabled dates (weekends and holidays) - green styling
        &:not(.disabled) {
            background-color: #d4edda !important;
            border: 1px solid #c3e6cb !important;
            color: #155724 !important;
            font-weight: 600;

            &:hover {
                background-color: #c3e6cb !important;
                border-color: #b8dacc !important;
                transform: scale(1.05);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &.selected {
                background-color: #28a745 !important;
                border-color: #28a745 !important;
                color: white !important;
            }
        }

        // Disabled dates (weekdays) - muted styling
        &.disabled {
            background-color: #f8f9fa !important;
            border: 1px solid #e9ecef !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.5;

            &:hover {
                background-color: #f8f9fa !important;
                border-color: #e9ecef !important;
                transform: none;
                box-shadow: none;
            }
        }

        // Today's date styling
        &.today {
            border: 2px solid #007bff !important;
            font-weight: bold;

            &:not(.disabled) {
                background-color: #cce7ff !important;
                color: #004085 !important;
            }
        }
    }

    // Calendar header styling
    .ngb-dp-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;

        .ngb-dp-arrow {
            color: #3F828B;

            &:hover {
                color: darken(#3F828B, 15%);
            }
        }

        .ngb-dp-month-name {
            color: #3F828B;
            font-weight: 600;
        }
    }

    // Day names styling
    .ngb-dp-weekdays {
        background-color: #f8f9fa;

        .ngb-dp-weekday {
            color: #495057;
            font-weight: 600;
            font-size: 0.875rem;
        }
    }
}

// Info text styling for comp off calendar
.comp-off-info {
    font-size: 0.875rem;
    color: #6c757d;

    .feather {
        margin-right: 4px;
        width: 14px;
        height: 14px;
    }
}
