/* Date Restriction Styles for OD and LWP Leave Types */

/* Restricted date input styling */
.date-input-restricted {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
  cursor: not-allowed !important;
  background-color: #f8f9fa !important;
  opacity: 0.8;
}

.date-input-restricted:focus {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
  cursor: not-allowed !important;
}

.date-input-restricted:hover {
  cursor: not-allowed !important;
  background-color: #f8f9fa !important;
}

/* Disabled state for OD/LWP when restrictions are active */
.date-input-od-lwp-restricted {
  cursor: not-allowed !important;
  background-color: #fff3cd !important;
  border-color: #ffc107 !important;
  color: #856404 !important;
  position: relative;
}

.date-input-od-lwp-restricted:hover {
  cursor: not-allowed !important;
  background-color: #fff3cd !important;
}

.date-input-od-lwp-restricted:focus {
  cursor: not-allowed !important;
  background-color: #fff3cd !important;
  border-color: #ffc107 !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* Overlay to prevent clicking on restricted date inputs */
.date-input-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.1) 25%, transparent 25%, transparent 50%, rgba(255, 193, 7, 0.1) 50%, rgba(255, 193, 7, 0.1) 75%, transparent 75%, transparent);
  background-size: 8px 8px;
  cursor: not-allowed !important;
  z-index: 10;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.date-input-overlay:hover {
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 193, 7, 0.2) 50%, rgba(255, 193, 7, 0.2) 75%, transparent 75%, transparent);
  background-size: 8px 8px;
}

/* Additional cursor restrictions for all child elements */
.date-input-od-lwp-restricted * {
  cursor: not-allowed !important;
}

/* Prevent text selection in restricted inputs */
.date-input-od-lwp-restricted {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Calendar icon styling for restricted inputs */
.date-input-od-lwp-restricted::-webkit-calendar-picker-indicator {
  cursor: not-allowed !important;
  opacity: 0.5;
  filter: grayscale(100%);
}

.date-input-od-lwp-restricted::-webkit-inner-spin-button,
.date-input-od-lwp-restricted::-webkit-outer-spin-button {
  cursor: not-allowed !important;
  opacity: 0.5;
}

/* Firefox date input styling */
.date-input-od-lwp-restricted::-moz-focus-inner {
  cursor: not-allowed !important;
}

/* Warning message for restricted dates */
.date-restriction-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
  color: #856404;
  font-size: 0.875rem;
}

.date-restriction-warning .warning-icon {
  color: #f39c12;
  margin-right: 0.5rem;
}

/* Error message for restricted dates */
.date-restriction-error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
  color: #721c24;
  font-size: 0.875rem;
}

.date-restriction-error .error-icon {
  color: #dc3545;
  margin-right: 0.5rem;
}

/* Info message for date restrictions */
.date-restriction-info {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
  color: #0c5460;
  font-size: 0.875rem;
}

.date-restriction-info .info-icon {
  color: #17a2b8;
  margin-right: 0.5rem;
}

/* Submit button disabled state for restrictions */
.btn-primary:disabled.restriction-disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  opacity: 0.65;
  cursor: not-allowed;
}

/* Tooltip styling for restricted dates */
.date-restriction-tooltip {
  background-color: #dc3545;
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  position: absolute;
  z-index: 1000;
  white-space: nowrap;
}

/* Leave type specific styling */
.od-restriction {
  border-left: 4px solid #f39c12;
}

.lwp-restriction {
  border-left: 4px solid #e74c3c;
}

/* Animation for restriction messages */
.date-restriction-message {
  animation: fadeInUp 0.3s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .date-restriction-warning,
  .date-restriction-error,
  .date-restriction-info {
    font-size: 0.8rem;
    padding: 0.5rem;
  }
}

/* Custom SweetAlert2 styling for date restrictions */
.swal2-popup-date-restriction {
  border-left: 5px solid #f39c12;
}

.swal2-popup-date-restriction .swal2-title {
  color: #856404;
}

.swal2-popup-date-restriction .swal2-content {
  color: #6c757d;
}

/* Date input container styling */
.date-input-container {
  position: relative;
}

.date-input-container.has-restriction {
  margin-bottom: 1rem;
}

/* Restriction badge */
.restriction-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  z-index: 10;
}

/* Loading state for date validation */
.date-validation-loading {
  position: relative;
}

.date-validation-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Success state for valid dates */
.date-input-valid {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* Restriction summary box */
.restriction-summary {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #f39c12;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.restriction-summary h6 {
  color: #856404;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.restriction-summary ul {
  margin-bottom: 0;
  padding-left: 1.5rem;
}

.restriction-summary li {
  color: #6c757d;
  margin-bottom: 0.25rem;
}
