import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, from } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

export interface RemoteModule {
  name: string;
  url: string;
  exposedModule: string;
  version?: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  loadStrategy: 'eager' | 'lazy' | 'on-demand';
  dependencies?: string[];
}

export interface ModuleFederationConfig {
  remotes: RemoteModule[];
  shared: { [key: string]: any };
  exposes?: { [key: string]: string };
}

export interface LoadedModule {
  name: string;
  module: any;
  loadTime: number;
  size?: number;
  status: 'loading' | 'loaded' | 'error';
}

/**
 * Module Federation Service
 * 
 * Provides advanced code splitting capabilities through module federation,
 * enabling micro-frontend architecture and dynamic module loading.
 */
@Injectable({
  providedIn: 'root'
})
export class ModuleFederationService {
  private loadedModules = new Map<string, LoadedModule>();
  private loadingModules = new Set<string>();
  private moduleSubject = new BehaviorSubject<LoadedModule[]>([]);

  public loadedModules$ = this.moduleSubject.asObservable();

  // Configuration for potential remote modules
  private remoteModuleConfigs: RemoteModule[] = [
    {
      name: 'shared-components',
      url: '/assets/remotes/shared-components.js',
      exposedModule: './SharedComponents',
      priority: 'high',
      loadStrategy: 'lazy',
      dependencies: ['@angular/core', '@angular/common']
    },
    {
      name: 'reporting-module',
      url: '/assets/remotes/reporting.js',
      exposedModule: './ReportingModule',
      priority: 'medium',
      loadStrategy: 'on-demand',
      dependencies: ['@angular/core', 'apexcharts']
    },
    {
      name: 'admin-tools',
      url: '/assets/remotes/admin-tools.js',
      exposedModule: './AdminTools',
      priority: 'low',
      loadStrategy: 'on-demand',
      dependencies: ['@angular/core', '@angular/forms']
    }
  ];

  constructor() {
    console.log('🔗 ModuleFederationService: Initialized for advanced code splitting');
    this.initializeModuleFederation();
  }

  /**
   * Initialize module federation
   */
  private initializeModuleFederation(): void {
    // Setup shared dependencies
    this.setupSharedDependencies();
    
    // Load critical modules
    this.loadCriticalModules();
    
    console.log('🔗 ModuleFederationService: Module federation initialized');
  }

  /**
   * Load a remote module dynamically
   */
  loadRemoteModule(moduleName: string): Observable<any> {
    if (this.loadedModules.has(moduleName)) {
      const loadedModule = this.loadedModules.get(moduleName)!;
      if (loadedModule.status === 'loaded') {
        console.log(`✅ ModuleFederationService: Module ${moduleName} already loaded`);
        return from(Promise.resolve(loadedModule.module));
      }
    }

    if (this.loadingModules.has(moduleName)) {
      console.log(`⏳ ModuleFederationService: Module ${moduleName} already loading`);
      return from(Promise.resolve(null));
    }

    const moduleConfig = this.remoteModuleConfigs.find(config => config.name === moduleName);
    if (!moduleConfig) {
      console.error(`❌ ModuleFederationService: Module ${moduleName} not found in configuration`);
      return from(Promise.reject(new Error(`Module ${moduleName} not found`)));
    }

    return this.loadModule(moduleConfig);
  }

  /**
   * Load multiple modules in parallel
   */
  loadModules(moduleNames: string[]): Observable<LoadedModule[]> {
    const loadPromises = moduleNames.map(name => 
      this.loadRemoteModule(name).toPromise().catch(error => {
        console.error(`Failed to load module ${name}:`, error);
        return null;
      })
    );

    return from(Promise.all(loadPromises)).pipe(
      map(results => results.filter(result => result !== null)),
      tap(modules => {
        console.log(`📦 ModuleFederationService: Loaded ${modules.length}/${moduleNames.length} modules`);
      })
    );
  }

  /**
   * Preload modules based on priority
   */
  preloadModules(priority: 'critical' | 'high' | 'medium' | 'low'): void {
    const modulesToPreload = this.remoteModuleConfigs.filter(config => 
      config.priority === priority && 
      config.loadStrategy !== 'on-demand' &&
      !this.loadedModules.has(config.name)
    );

    console.log(`🔮 ModuleFederationService: Preloading ${modulesToPreload.length} ${priority} priority modules`);

    modulesToPreload.forEach((config, index) => {
      setTimeout(() => {
        this.loadModule(config).subscribe({
          next: () => console.log(`🔮 Preloaded: ${config.name}`),
          error: (error) => console.warn(`⚠️ Preload failed for ${config.name}:`, error)
        });
      }, index * 200);
    });
  }

  /**
   * Load module with error handling and performance tracking
   */
  private loadModule(config: RemoteModule): Observable<any> {
    const startTime = performance.now();
    this.loadingModules.add(config.name);

    // Create loading entry
    const loadingModule: LoadedModule = {
      name: config.name,
      module: null,
      loadTime: 0,
      status: 'loading'
    };
    this.loadedModules.set(config.name, loadingModule);
    this.updateModuleSubject();

    console.log(`📥 ModuleFederationService: Loading module ${config.name} from ${config.url}`);

    return from(this.dynamicImport(config)).pipe(
      tap(module => {
        const loadTime = performance.now() - startTime;
        
        // Update loaded module
        const loadedModule: LoadedModule = {
          name: config.name,
          module,
          loadTime,
          status: 'loaded'
        };
        
        this.loadedModules.set(config.name, loadedModule);
        this.loadingModules.delete(config.name);
        this.updateModuleSubject();
        
        console.log(`✅ ModuleFederationService: Loaded ${config.name} in ${loadTime.toFixed(2)}ms`);
      }),
      catchError(error => {
        // Update error status
        const errorModule: LoadedModule = {
          name: config.name,
          module: null,
          loadTime: performance.now() - startTime,
          status: 'error'
        };
        
        this.loadedModules.set(config.name, errorModule);
        this.loadingModules.delete(config.name);
        this.updateModuleSubject();
        
        console.error(`❌ ModuleFederationService: Failed to load ${config.name}:`, error);
        throw error;
      })
    );
  }

  /**
   * Dynamic import with fallback strategies
   */
  private async dynamicImport(config: RemoteModule): Promise<any> {
    try {
      // Try to load from configured URL
      const module = await import(/* webpackIgnore: true */ config.url);
      return module[config.exposedModule] || module.default || module;
    } catch (error) {
      console.warn(`⚠️ Primary load failed for ${config.name}, trying fallback strategies`);
      
      // Fallback strategy 1: Try without exposed module
      try {
        const module = await import(/* webpackIgnore: true */ config.url);
        return module.default || module;
      } catch (fallbackError) {
        // Fallback strategy 2: Load from alternative CDN or local fallback
        console.warn(`⚠️ All fallback strategies failed for ${config.name}`);
        throw fallbackError;
      }
    }
  }

  /**
   * Setup shared dependencies for module federation
   */
  private setupSharedDependencies(): void {
    // This would configure shared dependencies in a real module federation setup
    const sharedDependencies = {
      '@angular/core': { singleton: true, strictVersion: true },
      '@angular/common': { singleton: true, strictVersion: true },
      '@angular/router': { singleton: true, strictVersion: true },
      'rxjs': { singleton: true, strictVersion: false }
    };

    console.log('🔗 ModuleFederationService: Shared dependencies configured', sharedDependencies);
  }

  /**
   * Load critical modules immediately
   */
  private loadCriticalModules(): void {
    const criticalModules = this.remoteModuleConfigs.filter(config => 
      config.priority === 'critical' || config.loadStrategy === 'eager'
    );

    if (criticalModules.length > 0) {
      console.log(`🚀 ModuleFederationService: Loading ${criticalModules.length} critical modules`);
      
      criticalModules.forEach(config => {
        this.loadModule(config).subscribe({
          error: (error) => console.error(`❌ Critical module ${config.name} failed to load:`, error)
        });
      });
    }
  }

  /**
   * Get module loading statistics
   */
  getModuleStats(): {
    totalModules: number;
    loadedModules: number;
    loadingModules: number;
    errorModules: number;
    averageLoadTime: number;
  } {
    const modules = Array.from(this.loadedModules.values());
    const loadedCount = modules.filter(m => m.status === 'loaded').length;
    const errorCount = modules.filter(m => m.status === 'error').length;
    const loadTimes = modules.filter(m => m.status === 'loaded').map(m => m.loadTime);
    const averageLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length 
      : 0;

    return {
      totalModules: this.remoteModuleConfigs.length,
      loadedModules: loadedCount,
      loadingModules: this.loadingModules.size,
      errorModules: errorCount,
      averageLoadTime
    };
  }

  /**
   * Check if a module is loaded
   */
  isModuleLoaded(moduleName: string): boolean {
    const module = this.loadedModules.get(moduleName);
    return module?.status === 'loaded';
  }

  /**
   * Unload a module (for cleanup/testing)
   */
  unloadModule(moduleName: string): void {
    this.loadedModules.delete(moduleName);
    this.loadingModules.delete(moduleName);
    this.updateModuleSubject();
    console.log(`🗑️ ModuleFederationService: Unloaded module ${moduleName}`);
  }

  /**
   * Update the module subject with current state
   */
  private updateModuleSubject(): void {
    const modules = Array.from(this.loadedModules.values());
    this.moduleSubject.next(modules);
  }

  /**
   * Get module federation configuration for webpack
   */
  getWebpackModuleFederationConfig(): any {
    return {
      name: 'bizz-corp-shell',
      remotes: this.remoteModuleConfigs.reduce((acc, config) => {
        acc[config.name] = `${config.name}@${config.url}`;
        return acc;
      }, {} as any),
      shared: {
        '@angular/core': { singleton: true, strictVersion: true },
        '@angular/common': { singleton: true, strictVersion: true },
        '@angular/router': { singleton: true, strictVersion: true },
        'rxjs': { singleton: true, strictVersion: false }
      }
    };
  }
}
