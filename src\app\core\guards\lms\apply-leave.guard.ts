import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class ApplyLeaveGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    console.log('🛡️ APPLY LEAVE GUARD - Checking access for Apply Leave');
    console.log('🔑 Required permission: leave:create');

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ APPLY LEAVE GUARD - No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Use STRICT permission checking from AuthService (no wildcards, no superuser bypass)
    const hasPermission = this.authService.hasPermission('leave:create');

    if (!hasPermission) {
      console.log('❌ APPLY LEAVE GUARD - Permission DENIED');
      console.log('🔑 Required: leave:create');
      console.log('👤 User permissions:', currentUser.permissions || []);
      console.log('📧 User email:', currentUser.email);
      console.log('🎭 User role:', currentUser.role);
      console.log('🚫 Redirecting to dashboard');
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log('✅ APPLY LEAVE GUARD - Permission GRANTED');
    console.log('🎯 User can access Apply Leave functionality');
    console.log('👤 User permissions:', currentUser.permissions || []);
    return true;
  }
}
