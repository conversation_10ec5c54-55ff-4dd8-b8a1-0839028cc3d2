import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import {
  DesignationService,
  Designation,
  DesignationCreate,
  DesignationUpdate
} from '../../../../../core/services/designation.service';
import { PopupDesignService } from '../../../../../core/services/popup-design.service';

@Component({
  selector: 'app-designation-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  templateUrl: './designation-form.component.html',
  styleUrls: ['./designation-form.component.scss']
})
export class DesignationFormComponent implements OnInit {
  @Input() isEditMode = false;
  @Input() designation: Designation | null = null;
  @Input() departments: any[] = [];

  designationForm!: FormGroup;
  saving = false;
  error: string | null = null;

  // Level options
  levelOptions = [
    { value: 1, label: 'Level 1 - Entry Level' },
    { value: 2, label: 'Level 2 - Junior Level' },
    { value: 3, label: 'Level 3 - Mid Level' },
    { value: 4, label: 'Level 4 - Senior Level' },
    { value: 5, label: 'Level 5 - Lead Level' },
    { value: 6, label: 'Level 6 - Manager Level' },
    { value: 7, label: 'Level 7 - Senior Manager' },
    { value: 8, label: 'Level 8 - Director Level' },
    { value: 9, label: 'Level 9 - VP Level' },
    { value: 10, label: 'Level 10 - Executive Level' }
  ];

  constructor(
    private fb: FormBuilder,
    private designationService: DesignationService,
    private popupService: PopupDesignService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  /**
   * Initialize the form with validation
   */
  private initializeForm(): void {
    this.designationForm = this.fb.group({
      name: [
        this.designation?.name || '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100),
          Validators.pattern(/^[a-zA-Z0-9\s\-&().,]+$/)
        ]
      ],
      description: [
        this.designation?.description || '',
        [
          Validators.maxLength(500)
        ]
      ],
      level: [
        this.designation?.level || 1,
        [
          Validators.required,
          Validators.min(1),
          Validators.max(10)
        ]
      ],
      department_id: [
        this.designation?.department_id || '',
        []
      ],
      salary_range_min: [
        this.designation?.salary_range_min || null,
        [
          Validators.min(0),
          Validators.max(10000000)
        ]
      ],
      salary_range_max: [
        this.designation?.salary_range_max || null,
        [
          Validators.min(0),
          Validators.max(10000000)
        ]
      ],
      is_active: [
        this.designation?.is_active ?? true,
        [Validators.required]
      ]
    });

    // Add custom validator for salary range
    this.designationForm.addValidators(this.salaryRangeValidator.bind(this));
  }

  /**
   * Custom validator for salary range
   */
  private salaryRangeValidator(form: FormGroup) {
    const min = form.get('salary_range_min')?.value;
    const max = form.get('salary_range_max')?.value;

    if (min && max && min >= max) {
      return { salaryRangeInvalid: { message: 'Maximum salary must be greater than minimum salary' } };
    }

    return null;
  }

  /**
   * Get form control for template access
   */
  getFormControl(controlName: string) {
    return this.designationForm.get(controlName);
  }

  /**
   * Check if form control has error
   */
  hasError(controlName: string, errorType?: string): boolean {
    const control = this.getFormControl(controlName);
    if (!control) return false;

    if (errorType) {
      return control.hasError(errorType) && (control.dirty || control.touched);
    }

    return control.invalid && (control.dirty || control.touched);
  }

  /**
   * Check if form has salary range error
   */
  hasSalaryRangeError(): boolean {
    return this.designationForm.hasError('salaryRangeInvalid') && 
           (this.designationForm.get('salary_range_min')?.dirty || 
            this.designationForm.get('salary_range_max')?.dirty);
  }

  /**
   * Get error message for form control
   */
  getErrorMessage(controlName: string): string {
    const control = this.getFormControl(controlName);
    if (!control || !control.errors) return '';

    const errors = control.errors;

    if (errors['required']) {
      return `${this.getFieldLabel(controlName)} is required.`;
    }

    if (errors['minlength']) {
      return `${this.getFieldLabel(controlName)} must be at least ${errors['minlength'].requiredLength} characters.`;
    }

    if (errors['maxlength']) {
      return `${this.getFieldLabel(controlName)} cannot exceed ${errors['maxlength'].requiredLength} characters.`;
    }

    if (errors['pattern']) {
      return `${this.getFieldLabel(controlName)} contains invalid characters.`;
    }

    if (errors['min']) {
      return `${this.getFieldLabel(controlName)} must be at least ${errors['min'].min}.`;
    }

    if (errors['max']) {
      return `${this.getFieldLabel(controlName)} cannot exceed ${errors['max'].max}.`;
    }

    return 'Invalid input.';
  }

  /**
   * Get field label for error messages
   */
  private getFieldLabel(controlName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Designation name',
      description: 'Description',
      level: 'Level',
      department_id: 'Department',
      salary_range_min: 'Minimum salary',
      salary_range_max: 'Maximum salary',
      is_active: 'Status'
    };
    return labels[controlName] || controlName;
  }

  /**
   * Get level name from level number
   */
  getLevelName(level: number): string {
    return this.designationService.getLevelName(level);
  }

  /**
   * Format salary for display
   */
  formatSalary(amount: number): string {
    return amount ? `₹${amount.toLocaleString()}` : '';
  }

  /**
   * Save designation
   */
  save(): void {
    if (this.designationForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.saving = true;
    this.error = null;

    const formValue = this.designationForm.value;

    // Clean up form data
    const designationData = {
      name: formValue.name.trim(),
      description: formValue.description?.trim() || undefined,
      level: formValue.level,
      department_id: formValue.department_id || undefined,
      salary_range_min: formValue.salary_range_min || undefined,
      salary_range_max: formValue.salary_range_max || undefined,
      is_active: formValue.is_active
    };

    const operation = this.isEditMode
      ? this.designationService.updateDesignation(this.designation!.id, designationData as DesignationUpdate)
      : this.designationService.createDesignation(designationData as DesignationCreate);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.activeModal.close('saved');
        } else {
          this.error = response.error || 'Failed to save designation.';
          this.saving = false;
        }
      },
      error: (error) => {
        this.error = error.message;
        this.saving = false;

        this.popupService.showError({
          title: 'Save Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Mark all form controls as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.designationForm.controls).forEach(key => {
      const control = this.designationForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Reset form
   */
  reset(): void {
    this.designationForm.reset();
    this.initializeForm();
    this.error = null;
  }

  /**
   * Cancel and close modal
   */
  cancel(): void {
    if (this.designationForm.dirty) {
      this.popupService.showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        confirmText: 'Yes, Cancel',
        cancelText: 'Continue Editing'
      }).then((result) => {
        if (result.isConfirmed) {
          this.activeModal.dismiss('cancelled');
        }
      });
    } else {
      this.activeModal.dismiss('cancelled');
    }
  }

  /**
   * Get modal title
   */
  getModalTitle(): string {
    return this.isEditMode ? 'Edit Designation' : 'Create New Designation';
  }

  /**
   * Get save button text
   */
  getSaveButtonText(): string {
    if (this.saving) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update Designation' : 'Create Designation';
  }

  /**
   * Get department name by ID
   */
  getDepartmentName(departmentId: string): string {
    const department = this.departments.find(d => d.id === departmentId);
    return department ? department.name : 'Unknown Department';
  }

  /**
   * Preview salary range
   */
  getPreviewSalaryRange(): string {
    const min = this.designationForm.get('salary_range_min')?.value;
    const max = this.designationForm.get('salary_range_max')?.value;
    return this.designationService.formatSalaryRange(min, max);
  }
}
