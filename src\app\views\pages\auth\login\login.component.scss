:host {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fb 0%, #e4e8f0 100%);
  overflow: hidden;
  position: relative;
}

/* Main container */
.auth-container {
  width: 100%;
  max-width: 450px;
  padding: 20px;
  position: relative;
  z-index: 1;
}

/* Card styling */
.auth-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1),
              0 1px 8px rgba(0, 0, 0, 0.05),
              0 0 0 1px rgba(255, 255, 255, 0.6) inset;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15),
                0 1px 10px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.8) inset;
  }
}



/* Header section */
.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo-container {
  margin-bottom: 20px;
  display: inline-block;
}

.logo {
  width: 120px;
  height: 80px;
  object-fit: contain;
}

.auth-title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.auth-subtitle {
  color: #6c757d;
  font-size: 16px;
  margin-bottom: 0;
}

/* Form styling */
.auth-form {
  margin-bottom: 20px;
}

/* Input group styling */
.input-group {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;

  .input-group-text {
    background-color: #f8f9fa;
    border: none;
    color: #6c757d;
    padding: 0.75rem 1rem;

    i {
      width: 18px;
      height: 18px;
    }
  }

  .password-toggle {
    cursor: pointer;
  }

  .form-control {
    border: none;
    padding: 0.75rem 1rem;
    height: auto;
    font-size: 16px;
    background-color: #fff;

    &:focus {
      box-shadow: none;
    }
  }

  label {
    padding: 0.75rem 1rem;
    color: #6c757d;
  }
}

/* Form floating adjustments */
.form-floating {
  > .form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
  }

  > label {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }
}

/* Prevent feather icon duplication globally */
[data-feather] {
  svg + svg {
    display: none !important;
  }

  // Remove duplicate feather classes
  &.feather {
    &::before {
      display: none;
    }
  }
}

/* Remember me and forgot password */
.form-check {
  .form-check-input {
    border-color: #ced4da;
    cursor: pointer;

    &:checked {
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
    }

    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }

  .form-check-label {
    color: #6c757d;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;

    &:hover {
      color: var(--bs-primary);
    }

    i, svg {
      width: 16px;
      height: 16px;
      color: var(--bs-primary);
      flex-shrink: 0;
    }

    // Prevent icon duplication in labels
    svg + svg {
      display: none;
    }
  }
}

.forgot-password {
  font-size: 14px;
  color: var(--bs-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    text-decoration: underline;
    color: var(--bs-primary);
    transform: translateX(2px);
  }

  i, svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  // Prevent icon duplication
  svg + svg {
    display: none;
  }
}

/* Login button */
.login-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 15px rgba(var(--bs-primary-rgb, 13, 110, 253), 0.3);

  i, svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  // Prevent icon duplication
  svg + svg {
    display: none;
  }

  &:hover {
    transform: translateY(-2px);
  }
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  margin: 15px 0;

  &::before, &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: calc(50% - 20px);
    height: 1px;
    background-color: #dee2e6;
  }

  &::before { left: 0; }
  &::after { right: 0; }

  span {
    display: inline-block;
    padding: 0 10px;
    background-color: #fff;
    position: relative;
    z-index: 1;
  }
}

/* Quick login options */
.quick-login {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.quick-login-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  cursor: pointer;

  i {
    width: 20px;
    height: 20px;
  }
}

/* Footer */
.auth-footer {
  text-align: center;
  color: #6c757d;
  font-size: 12px;
  margin-top: 20px;
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .auth-card { padding: 30px 20px; }
  .auth-title { font-size: 24px; }
  .auth-subtitle { font-size: 14px; }
}
