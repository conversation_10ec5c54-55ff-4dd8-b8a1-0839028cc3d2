<!-- Breadcrumb Navigation -->
<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<div class="row">
  <!-- Report Cards -->
  <div class="col-md-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
          <img src="images/lms/report/attendance-report.png" alt="Attendance Report" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title text-center mb-3">Attendance Report</h6>
          <button class="btn btn-outline-orange fw-medium" (click)="openAttendanceModal()">View Report</button>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-2 mb-4">
    <div class="card h-100">
      <div class="card-body dashboard-card">
        <div class="d-flex flex-column align-items-center">
          <img src="images/lms/report/leave-report.png" alt="Leave Report" class="mb-3 img-fluid lms-card-icon">
          <h6 class="card-title mb-3">Leave Report</h6>
          <button class="btn btn-outline-orange fw-medium" (click)="openLeaveModal()">View Report</button>
        </div>
      </div>
    </div>
  </div>

</div>

<!-- Attendance Report Modal -->
<ng-template #attendanceModal let-modal>
  <div class="modal-header ">
    <h6 class="modal-title">Attendance Report</h6>
    <button type="button" class="btn-close btn-sm" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modal-body py-3">
    <form>
      <div class="row g-3">
        <div class="col-md-6">
          <label for="fromDate" class="form-label small mb-1">From Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            id="fromDate"
            [(ngModel)]="fromDate"
            name="fromDate"
          >
        </div>
        <div class="col-md-6">
          <label for="toDate" class="form-label small mb-1">To Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            id="toDate"
            [(ngModel)]="toDate"
            name="toDate"
          >
        </div>
      </div>

      <div class="alert alert-info  mt-3 mb-0 small" role="alert">
        <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
        Select a date range to generate the attendance report.
      </div>
    </form>
  </div>
  <div class="modal-footer ">
    <button type="button" class="btn btn-sm btn-secondary" (click)="modal.close('Close click')" [disabled]="downloadingAttendanceReport">Close</button>
    <button type="button" class="btn btn-sm btn-primary" (click)="downloadAttendanceReport()" [disabled]="downloadingAttendanceReport">
      <span *ngIf="!downloadingAttendanceReport">
        <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
        Download
      </span>
      <span *ngIf="downloadingAttendanceReport">
        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
        Downloading...
      </span>
    </button>
  </div>
</ng-template>

<!-- Leave Report Modal -->
<ng-template #leaveModal let-modal>
  <div class="modal-header">
    <h6 class="modal-title">Leave Report</h6>
    <button type="button" class="btn-close btn-sm" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modal-body py-3">
    <form>
      <div class="row g-3">
        <div class="col-md-6">
          <label for="leaveFromDate" class="form-label small mb-1">From Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            id="leaveFromDate"
            [(ngModel)]="leaveFromDate"
            name="leaveFromDate"
          >
        </div>
        <div class="col-md-6">
          <label for="leaveToDate" class="form-label small mb-1">To Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            id="leaveToDate"
            [(ngModel)]="leaveToDate"
            name="leaveToDate"
          >
        </div>
        <div class="col-md-12">
          <label for="leaveStatus" class="form-label small mb-1">Leave Type</label>
          <select
            class="form-select form-select-sm"
            id="leaveStatus"
            [(ngModel)]="leaveStatus"
            name="leaveStatus"
          >
            <option value="">All Leave Types</option>
            <option *ngFor="let leaveType of leaveTypes" [value]="leaveType.value">
              {{ leaveType.label }}
            </option>
          </select>
        </div>
      </div>

      <div class="alert alert-info mt-3 mb-0 small" role="alert">
        <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
        Select a date range and leave status to generate the leave report.
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-sm btn-secondary" (click)="modal.close('Close click')" [disabled]="downloadingLeaveReport">Close</button>
    <button type="button" class="btn btn-sm btn-primary" (click)="downloadLeaveReport()" [disabled]="downloadingLeaveReport">
      <span *ngIf="!downloadingLeaveReport">
        <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
        Download
      </span>
      <span *ngIf="downloadingLeaveReport">
        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
        Downloading...
      </span>
    </button>
  </div>
</ng-template>

<!-- Report Data Modal -->
<ng-template #reportDataModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Leave Report Data</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modal-body">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h6 class="mb-0">
        Report Period: {{ formatDisplayDate(leaveFromDate) }} to {{ formatDisplayDate(leaveToDate) }}
        <span class="badge bg-primary ms-2">{{ leaveReportData.length }} Records</span>
      </h6>
      <button type="button" class="btn btn-sm btn-success" (click)="exportReportData()">
        <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
        Export CSV
      </button>
    </div>

    <div class="table-responsive">
      <table class="table table-striped table-hover">
        <thead class="table-dark">
          <tr>
            <th>Employee</th>
            <th>Code</th>
            <th>Leave Type</th>
            <th>From Date</th>
            <th>To Date</th>
            <th>Days</th>
            <th>Status</th>
            <th>Applied Date</th>
            <th>Approved By</th>
            <th>Reason</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of leaveReportData; trackBy: trackByRecordId">
            <td>{{ record.employee_name }}</td>
            <td>{{ record.employee_code }}</td>
            <td>
              <span class="badge bg-info">{{ formatLeaveType(record.leave_type) }}</span>
            </td>
            <td>{{ formatDisplayDate(record.start_date) }}</td>
            <td>{{ formatDisplayDate(record.end_date) }}</td>
            <td class="text-center">{{ record.days }}</td>
            <td>
              <span class="badge"
                    [ngClass]="{
                      'bg-success': record.status === 'approved',
                      'bg-warning': record.status === 'pending',
                      'bg-danger': record.status === 'rejected',
                      'bg-secondary': record.status === 'cancelled'
                    }">
                {{ record.status | titlecase }}
              </span>
            </td>
            <td>{{ formatDisplayDate(record.start_date) }}</td>
            <td>{{ record.approved_by || '-' }}</td>
            <td>
              <span [title]="record.reason" class="text-truncate d-inline-block" style="max-width: 150px;">
                {{ record.reason }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div *ngIf="leaveReportData.length === 0" class="text-center py-4">
      <i data-feather="inbox" class="icon-lg text-muted mb-2" appFeatherIcon></i>
      <p class="text-muted">No leave records found for the selected criteria.</p>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-sm btn-secondary" (click)="modal.close('Close click')">Close</button>
    <button type="button" class="btn btn-sm btn-primary" (click)="exportReportData()">
      <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
      Download CSV
    </button>
  </div>
</ng-template>