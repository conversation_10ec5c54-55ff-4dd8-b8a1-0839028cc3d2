// Profile Component Styles

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin: 2rem 0 1rem 0;
  padding: 0.5rem 0;
  border-bottom: 2px solid #e9ecef;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #007bff, #0056b3);
  }

  &:first-of-type {
    margin-top: 1rem;
  }
}

// Profile field styling
.form-control[readonly] {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
}

// Status indicators
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-muted {
  color: #6c757d !important;
}

// Currency and formatted values
.currency-value {
  font-weight: 500;
  color: #28a745;
}

.phone-number {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

// Profile sections spacing
.profile-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #ffffff;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

// Employee status badge
.employee-status {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;

  &.active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .section-title {
    font-size: 1rem;
    margin: 1.5rem 0 0.75rem 0;
  }
}

// Loading states
.loading-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}