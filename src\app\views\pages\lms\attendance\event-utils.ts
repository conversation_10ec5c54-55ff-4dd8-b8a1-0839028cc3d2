import { EventInput } from '@fullcalendar/core';

let eventGuid = 0;
const TODAY_STR = () => {
  let dateObj = new Date();
  if(dateObj.getUTCMonth() < 10) {
    return dateObj.getUTCFullYear() + '-' + ('0'+(dateObj.getUTCMonth() + 1));
  } else {
    return dateObj.getUTCFullYear() + '-' + (dateObj.getUTCMonth() + 1);
  }
}

let holidayEvents= [
  {
    id: createEventId(),
    start: TODAY_STR() +'-14',
    end: TODAY_STR() +'-17',
    title: 'Public Holiday',
    backgroundColor: 'rgb(241,0,117)',
    textColor: 'white',
    borderColor: '#f10075',
    editable: false
  },
  {
    id: createEventId(),
    start: TODAY_STR() +'-26',
    end: TODAY_STR() +'-27',
    title: 'Company Holiday',
    backgroundColor: 'rgb(241,0,117)',
    textColor: 'white',
    borderColor: '#f10075',
    editable: false
  }
];

let leaveEvents= [
  {
    id: createEventId(),
    start: TODAY_STR() +'-06',
    end: TODAY_STR() +'-08',
    title: 'Sick Leave',
    backgroundColor: 'rgb(253,126,20)',
    borderColor: '#fd7e14',
    textColor: 'white',
    editable: false
  },
  {
    id: createEventId(),
    start: TODAY_STR() +'-10',
    end: TODAY_STR() +'-10',
    title: 'Paid Leave',
    backgroundColor: 'rgb(2, 142, 14)',
    borderColor: 'rgb(2, 142, 14)',
    textColor: 'white',
    editable: false
  },
  {
    id: createEventId(),
    start: TODAY_STR() +'-13',
    end: TODAY_STR() +'-13',
    title: 'Lwp',
    backgroundColor: 'rgb(194, 9, 9)',
    textColor: 'white',
    borderColor: 'rgb(194, 9, 9)',
    editable: false
  }
];

// Function to generate all Sundays for a given year
function generateSundayWeekOffs(year: number) {
  const sundays = [];
  const startDate = new Date(year, 0, 1); // January 1st of the year

  // Find the first Sunday of the year
  let currentDate = new Date(startDate);
  while (currentDate.getDay() !== 0) { // 0 = Sunday
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Generate all Sundays for the year
  while (currentDate.getFullYear() === year) {
    const dateStr = currentDate.getFullYear() + '-' +
                   String(currentDate.getMonth() + 1).padStart(2, '0') + '-' +
                   String(currentDate.getDate()).padStart(2, '0');

    sundays.push({
      id: createEventId(),
      start: dateStr,
      title: 'Week Off',
      borderColor: '#f10075',
      textColor: 'rgb(241,0,117)',
      editable: false
    });

    // Move to next Sunday
    currentDate.setDate(currentDate.getDate() + 7);
  }

  return sundays;
}

// Generate week-off events for current year and next year
const currentYear = new Date().getFullYear();
let weekOffEvents = [
  ...generateSundayWeekOffs(currentYear),
  ...generateSundayWeekOffs(currentYear + 1)
];


export const INITIAL_EVENTS: EventInput[] = [ ...holidayEvents, ...leaveEvents, ...weekOffEvents];

export function createEventId() {
  return String(eventGuid++);
}
