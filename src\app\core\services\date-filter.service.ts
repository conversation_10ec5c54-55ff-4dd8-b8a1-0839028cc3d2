import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HolidayService } from './holiday.service';

@Injectable({
  providedIn: 'root'
})
export class DateFilterService {
  
  constructor(private holidayService: HolidayService) {}

  /**
   * Create a date filter function for Angular Material date pickers
   * This function returns true for selectable dates and false for restricted dates
   * @returns Function that can be used as a date filter
   */
  createDateFilter(): (date: Date | null) => boolean {
    // Cache holidays for the filter function
    let cachedHolidays: string[] = [];
    let holidaysLoaded = false;

    // Load holidays when filter is created
    this.holidayService.getCachedHolidays().subscribe(holidays => {
      cachedHolidays = holidays.map(h => h.holiday_date);
      holidaysLoaded = true;
      console.log('📅 DATE FILTER - Holidays loaded for filter:', cachedHolidays.length);
    });

    // Return the filter function
    return (date: Date | null): boolean => {
      if (!date) {
        return false;
      }

      // Check if it's a weekend (Saturday = 6, Sunday = 0)
      const day = date.getDay();
      if (day === 0 || day === 6) {
        console.log(`🚫 DATE FILTER - Weekend blocked: ${this.formatDate(date)}`);
        return false;
      }

      // Check if it's a holiday (only if holidays are loaded)
      if (holidaysLoaded) {
        const dateStr = this.formatDate(date);
        if (cachedHolidays.includes(dateStr)) {
          console.log(`🚫 DATE FILTER - Holiday blocked: ${dateStr}`);
          return false;
        }
      }

      // Date is selectable
      return true;
    };
  }

  /**
   * Create an async date filter function that checks holidays dynamically
   * @returns Function that returns Observable<boolean>
   */
  createAsyncDateFilter(): (date: Date | null) => Observable<boolean> {
    return (date: Date | null): Observable<boolean> => {
      if (!date) {
        return of(false);
      }

      // Check if it's a weekend first (synchronous check)
      const day = date.getDay();
      if (day === 0 || day === 6) {
        console.log(`🚫 ASYNC DATE FILTER - Weekend blocked: ${this.formatDate(date)}`);
        return of(false);
      }

      // Check if it's a holiday (asynchronous check)
      return this.holidayService.isHoliday(date).pipe(
        map(isHoliday => {
          if (isHoliday) {
            console.log(`🚫 ASYNC DATE FILTER - Holiday blocked: ${this.formatDate(date)}`);
            return false;
          }
          return true;
        })
      );
    };
  }

  /**
   * Check if a date is selectable (not weekend or holiday)
   * @param date Date to check
   * @returns Observable<boolean> indicating if date is selectable
   */
  isDateSelectable(date: Date | string): Observable<boolean> {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check weekend first
    if (this.holidayService.isWeekend(dateObj)) {
      return of(false);
    }

    // Check holiday
    return this.holidayService.isHoliday(dateObj).pipe(
      map(isHoliday => !isHoliday)
    );
  }

  /**
   * Get CSS class for date styling based on its type
   * @param date Date to check
   * @returns Observable<string> CSS class name
   */
  getDateCssClass(date: Date): Observable<string> {
    // Check if it's a weekend
    if (this.holidayService.isWeekend(date)) {
      return of('weekend-date');
    }

    // Check if it's a holiday
    return this.holidayService.isHoliday(date).pipe(
      map(isHoliday => {
        if (isHoliday) {
          return 'holiday-date';
        }
        return 'available-date';
      })
    );
  }

  /**
   * Get date type for display purposes
   * @param date Date to check
   * @returns Observable<string> date type description
   */
  getDateType(date: Date): Observable<string> {
    // Check if it's a weekend
    if (this.holidayService.isWeekend(date)) {
      return of('Weekend');
    }

    // Check if it's a holiday
    return this.holidayService.getHolidayInfo(date).pipe(
      map(holiday => {
        if (holiday) {
          return `Holiday: ${holiday.holiday}`;
        }
        return 'Available';
      })
    );
  }

  /**
   * Validate date range to ensure no weekends or holidays are included
   * @param startDate Start date of the range
   * @param endDate End date of the range
   * @returns Observable<{valid: boolean, invalidDates: Date[], message: string}>
   */
  validateDateRange(startDate: Date, endDate: Date): Observable<{valid: boolean, invalidDates: Date[], message: string}> {
    console.log(`📅 DATE RANGE VALIDATION - Checking range: ${this.formatDate(startDate)} to ${this.formatDate(endDate)}`);
    
    const invalidDates: Date[] = [];
    const dateChecks: Observable<boolean>[] = [];
    
    // Check each date in the range
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateToCheck = new Date(currentDate);
      
      // Check if this date is restricted
      const isRestricted = this.holidayService.isRestrictedDate(dateToCheck);
      dateChecks.push(isRestricted.pipe(
        map(restricted => {
          if (restricted) {
            invalidDates.push(new Date(dateToCheck));
          }
          return !restricted;
        })
      ));
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Wait for all checks to complete
    return new Observable(observer => {
      Promise.all(dateChecks.map(check => check.toPromise())).then(results => {
        const allValid = results.every(valid => valid);
        
        let message = '';
        if (!allValid) {
          const invalidDateStrings = invalidDates.map(d => this.formatDate(d));
          message = `The following dates are not allowed (weekends/holidays): ${invalidDateStrings.join(', ')}`;
        }
        
        console.log(`📅 DATE RANGE VALIDATION - Result: ${allValid ? 'VALID' : 'INVALID'}`, {
          invalidDates: invalidDates.length,
          message
        });
        
        observer.next({
          valid: allValid,
          invalidDates,
          message
        });
        observer.complete();
      });
    });
  }

  /**
   * Format date to YYYY-MM-DD string
   * @param date Date to format
   * @returns Formatted date string
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Get next available (non-restricted) date from a given date
   * @param fromDate Starting date
   * @returns Observable<Date> next available date
   */
  getNextAvailableDate(fromDate: Date): Observable<Date> {
    const checkDate = new Date(fromDate);
    
    return new Observable(observer => {
      const findNext = () => {
        this.isDateSelectable(checkDate).subscribe(selectable => {
          if (selectable) {
            observer.next(new Date(checkDate));
            observer.complete();
          } else {
            checkDate.setDate(checkDate.getDate() + 1);
            // Prevent infinite loop - max 30 days ahead
            if (checkDate.getTime() - fromDate.getTime() > 30 * 24 * 60 * 60 * 1000) {
              observer.next(new Date(fromDate)); // Return original date if no available date found
              observer.complete();
            } else {
              findNext();
            }
          }
        });
      };
      
      findNext();
    });
  }
}
