import { Component, OnInit, ViewChildren, QueryList, Directive, Input, Output, EventEmitter, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';
import { NewYearActivityService, NewYearActivity } from '../../../../../core/services/new-year-activity.service';
import { catchError, debounceTime, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

// Sortable header directive
export type SortColumn = keyof NewYearActivity | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: {[key: string]: SortDirection} = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

@Component({
  selector: 'app-activity-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    NgbdSortableHeader,
    BreadcrumbComponent
  ],
  templateUrl: './activity-list.component.html',
  styleUrl: './activity-list.component.scss'
})
export class ActivityListComponent implements OnInit {
  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'View New Year Activity' }
  ];

  // Activity data
  activities: NewYearActivity[] = []; // All activities from API
  filteredActivities: NewYearActivity[] = []; // Filtered activities
  paginatedActivities: NewYearActivity[] = []; // Current page activities

  // Loading states
  loading = false;
  saving = false;

  // Search and filter controls
  searchTerm = new FormControl('', { nonNullable: true });
  filterType = new FormControl('all', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 10; // Default page size
  pageSizeOptions = [5, 10, 20, 50]; // Options for the "show per page" dropdown
  totalItems = 0; // Total filtered items
  totalActivities = 0; // Total activities from API

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // Edit activity form
  editActivityForm: FormGroup;
  selectedActivity: NewYearActivity | null = null;

  // Create activity form
  createActivityForm: FormGroup;
  creating = false;

  // Activity count
  activityCount: any = null;
  loadingCount = false;

  constructor(
    private formBuilder: FormBuilder,
    private modalService: NgbModal,
    private newYearActivityService: NewYearActivityService
  ) {
    this.initEditForm();
    this.initCreateForm();
  }

  ngOnInit(): void {
    // Load initial data
    this.loadActivities();
    this.loadActivityCount();

    // Set up search with debouncing
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.page = 1;
      this.applyFiltersAndPagination();
    });

    // Set up filter type listener
    this.filterType.valueChanges.subscribe(() => {
      this.page = 1;
      this.applyFiltersAndPagination();
    });
  }

  // Load activities from API
  loadActivities(): void {
    this.loading = true;
    console.log('Loading activities from API...');

    // Use pagination parameters for better performance
    this.newYearActivityService.getAllActivities(this.page, this.pageSize)
      .pipe(
        catchError(error => {
          console.error('API failed:', error);
          this.showErrorMessage('Failed to load activities. Please check your connection and try again.');
          return of({ success: false, data: [], error: error });
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        console.log('API Response received:', response);

        // Handle API response structure
        if (response && response.success && response.data) {
          // Standard API response with success flag
          if (Array.isArray(response.data)) {
            this.activities = response.data.map((activity: any) => this.transformApiActivity(activity));
            this.totalActivities = response.data.length;
          } else {
            console.error('Expected data to be an array:', response.data);
            this.activities = [];
            this.totalActivities = 0;
          }
        } else if (Array.isArray(response)) {
          // Direct array response (fallback)
          this.activities = response.map((activity: any) => this.transformApiActivity(activity));
          this.totalActivities = response.length;
        } else {
          console.error('Unexpected API response structure:', response);
          this.activities = [];
          this.totalActivities = 0;
        }

        console.log('Activities loaded:', this.activities.length, 'activities');

        // Apply filters and pagination after loading data
        this.applyFiltersAndPagination();
      });
  }

  // Apply search filters and pagination
  applyFiltersAndPagination(): void {
    console.log('Applying filters and pagination - search:', this.searchTerm.value, 'page:', this.page, 'pageSize:', this.pageSize);

    // If we have search term or filter, reload from API with parameters
    const searchValue = this.searchTerm.value?.toLowerCase().trim();
    const hasFilters = searchValue || this.filterType.value !== 'all';

    if (hasFilters) {
      // For now, do client-side filtering until API supports search parameters
      this.applyClientSideFiltering();
    } else {
      // No filters, use all loaded activities
      this.filteredActivities = [...this.activities];
      this.totalItems = this.activities.length;
      this.applyPagination();
    }
  }

  // Apply client-side filtering (fallback when API doesn't support search)
  private applyClientSideFiltering(): void {
    let filtered = [...this.activities];

    // Apply search filter
    const searchValue = this.searchTerm.value?.toLowerCase().trim();
    if (searchValue) {
      filtered = filtered.filter(activity =>
        activity.activity_name.toLowerCase().includes(searchValue) ||
        (activity.holiday && activity.holiday.toLowerCase().includes(searchValue)) ||
        (activity.holiday_month && activity.holiday_month.toLowerCase().includes(searchValue)) ||
        (activity.holiday_day && activity.holiday_day.toLowerCase().includes(searchValue)) ||
        activity.date.toLowerCase().includes(searchValue) ||
        (activity.holiday_date && activity.holiday_date.toLowerCase().includes(searchValue)) ||
        this.formatDate(activity.date).toLowerCase().includes(searchValue)
      );
    }

    // Apply status filter
    if (this.filterType.value !== 'all') {
      filtered = filtered.filter(activity => activity.status === this.filterType.value);
    }

    // Set filtered results
    this.filteredActivities = filtered;
    this.totalItems = filtered.length;

    // Apply pagination
    this.applyPagination();

    console.log('Filtered activities:', this.filteredActivities.length);
    console.log('Paginated activities:', this.paginatedActivities.length);
    console.log('Current page:', this.page, 'of', Math.ceil(this.totalItems / this.pageSize));
  }

  // Apply pagination to filtered results
  private applyPagination(): void {
    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedActivities = this.filteredActivities.slice(startIndex, endIndex);
  }

  // Helper method to show error messages
  private showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message,
      confirmButtonText: 'OK'
    });
  }

  // Transform API activity to local NewYearActivity interface
  private transformApiActivity(apiActivity: any): NewYearActivity {
    // Debug log to see the actual API activity structure
    console.log('Transforming API activity:', {
      id: apiActivity.id,
      activity_name: apiActivity.activity_name,
      holiday: apiActivity.holiday,
      date: apiActivity.date,
      holiday_date: apiActivity.holiday_date
    });

    // Use either date or holiday_date
    const activityDate = apiActivity.date || apiActivity.holiday_date || new Date().toISOString().split('T')[0];

    return {
      id: apiActivity.id || '',
      activity_name: apiActivity.activity_name || apiActivity.holiday || 'Untitled Activity',
      date: activityDate,
      description: '', // Default empty
      department: '', // Default empty
      status: 'planned', // Default status
      created_at: apiActivity.created_at || '',
      updated_at: apiActivity.updated_at || '',
      // Excel/Holiday fields
      holiday: apiActivity.holiday || apiActivity.activity_name || '',
      holiday_month: apiActivity.holiday_month || this.getMonthFromDate(activityDate),
      holiday_day: apiActivity.holiday_day || this.getDayFromDate(activityDate),
      holiday_date: apiActivity.holiday_date || apiActivity.date || ''
    };
  }

  // Initialize edit form
  initEditForm(): void {
    this.editActivityForm = this.formBuilder.group({
      activity_name: ['', Validators.required],
      date: ['', Validators.required],
      holiday: [''],
      holiday_month: [''],
      holiday_day: ['']
    });
  }

  // Initialize create form
  initCreateForm(): void {
    this.createActivityForm = this.formBuilder.group({
      activity_name: ['', Validators.required],
      date: ['', Validators.required],
      holiday: [''],
      holiday_month: [''],
      holiday_day: ['']
    });
  }

  // Load activity count from API
  loadActivityCount(): void {
    this.loadingCount = true;
    console.log('Loading activity count from API...');

    this.newYearActivityService.getActivitiesCount()
      .pipe(
        catchError(error => {
          console.error('Failed to load activity count:', error);
          return of(null);
        }),
        finalize(() => {
          this.loadingCount = false;
        })
      )
      .subscribe(response => {
        console.log('Activity count response:', response);
        this.activityCount = response;
      });
  }

  // Refresh activities list (reload from API)
  refreshActivitiesList(): void {
    this.loadActivities();
    this.loadActivityCount();
  }

  // Handle pagination changes
  onPageChange(page: number): void {
    this.page = page;
    this.applyFiltersAndPagination();
  }

  // Handle page size changes
  onPageSizeChange(newPageSize: number): void {
    this.pageSize = newPageSize;
    this.page = 1; // Reset to first page when changing page size
    this.applyFiltersAndPagination();
  }

  // Apply filter
  applyFilter(): void {
    this.page = 1;
    this.applyFiltersAndPagination();
  }

  // Reset filter
  resetFilter(): void {
    this.searchTerm.setValue('');
    this.filterType.setValue('all');
    this.page = 1;
    this.applyFiltersAndPagination();
  }

  // Handle sorting
  onSort(event: SortEvent): void {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== event.column) {
        header.direction = '';
      }
    });

    // Apply sorting to filtered activities
    if (event.direction === '' || event.column === '') {
      this.applyFiltersAndPagination();
      return;
    }

    this.filteredActivities.sort((a, b) => {
      const aVal = a[event.column as keyof NewYearActivity];
      const bVal = b[event.column as keyof NewYearActivity];

      // Handle null/undefined values
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return -1;
      if (bVal == null) return 1;

      let result = 0;
      if (aVal < bVal) result = -1;
      else if (aVal > bVal) result = 1;

      return event.direction === 'asc' ? result : -result;
    });

    // Re-apply pagination after sorting
    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedActivities = this.filteredActivities.slice(startIndex, endIndex);
  }

  // Open create activity modal
  openCreateActivityModal(content: TemplateRef<any>): void {
    // Reset form
    this.createActivityForm.reset({
      activity_name: '',
      date: '',
      holiday: '',
      holiday_month: '',
      holiday_day: ''
    });

    // Open modal
    this.modalService.open(content, {
      size: 'lg',
      scrollable: true,
      backdrop: 'static',
      keyboard: false
    }).result.then((result) => {
      console.log('Create modal closed with result:', result);
    }).catch(() => {
      console.log('Create modal dismissed');
    });
  }

  // Create new activity
  createActivity(): void {
    if (this.createActivityForm.invalid) {
      this.markFormGroupTouched(this.createActivityForm);
      return;
    }

    this.creating = true;
    const formData = this.createActivityForm.value;

    // Prepare API payload with required fields
    const apiPayload = {
      activity_name: formData.activity_name,
      date: formData.date,
      holiday: formData.holiday || formData.activity_name || '',
      holiday_month: formData.holiday_month || '',
      holiday_day: formData.holiday_day || '',
      holiday_date: formData.date,
      // Add required fields with defaults
      status: 'planned' as const,
      department: '',
      description: ''
    };

    console.log('Creating new activity:', apiPayload);

    this.newYearActivityService.createActivity(apiPayload)
      .pipe(
        finalize(() => {
          this.creating = false;
        })
      )
      .subscribe({
        next: (response) => {
          console.log('Activity created successfully:', response);

          // Show success message
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Activity created successfully.',
            timer: 2000,
            showConfirmButton: false
          });

          // Close modal
          this.modalService.dismissAll();

          // Refresh the list and count
          this.loadActivities();
          this.loadActivityCount();
        },
        error: (error) => {
          console.error('Failed to create activity:', error);

          // Show error message
          Swal.fire({
            icon: 'error',
            title: 'Creation Failed',
            text: 'Failed to create activity. Please try again.',
            confirmButtonText: 'OK'
          });
        }
      });
  }

  // Edit activity
  editActivity(activity: NewYearActivity, content: TemplateRef<any>): void {
    this.selectedActivity = activity;

    console.log('📝 Opening edit modal for activity:', {
      id: activity.id,
      name: activity.activity_name || activity.holiday,
      date: activity.date || activity.holiday_date
    });

    // Reset form validation state
    this.editActivityForm.markAsUntouched();
    this.editActivityForm.markAsPristine();

    // Populate form with activity data
    this.editActivityForm.patchValue({
      activity_name: activity.activity_name || activity.holiday || '',
      date: this.formatDateForInput(activity.date ?? activity.holiday_date ?? ''),
      holiday: activity.holiday || activity.activity_name || '',
      holiday_month: activity.holiday_month || '',
      holiday_day: activity.holiday_day || ''
    });

    // Open modal
    const modalRef = this.modalService.open(content, {
      size: 'lg',
      scrollable: true,
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    modalRef.result.then((result) => {
      console.log('✅ Edit modal closed with result:', result);
      this.selectedActivity = null;
    }).catch(() => {
      console.log('❌ Edit modal dismissed');
      this.selectedActivity = null;
    });
  }

  // Save activity changes
  saveActivityChanges(): void {
    if (this.editActivityForm.invalid || !this.selectedActivity) {
      this.markFormGroupTouched(this.editActivityForm);
      console.warn('Form is invalid or no activity selected');
      return;
    }

    this.saving = true;
    const formData = this.editActivityForm.value;

    // Prepare API payload with required fields
    const apiPayload = {
      activity_name: formData.activity_name?.trim(),
      date: formData.date,
      holiday: formData.holiday?.trim() || formData.activity_name?.trim() || '',
      holiday_month: formData.holiday_month || '',
      holiday_day: formData.holiday_day || '',
      holiday_date: formData.date,
      // Keep existing values for removed fields
      status: this.selectedActivity.status || 'planned' as const,
      department: this.selectedActivity.department || '',
      description: this.selectedActivity.description || ''
    };

    console.log('🔄 Updating activity:', {
      id: this.selectedActivity.id,
      payload: apiPayload
    });

    this.newYearActivityService.updateActivity(this.selectedActivity.id!, apiPayload)
      .pipe(
        finalize(() => {
          this.saving = false;
        })
      )
      .subscribe({
        next: (response) => {
          console.log('✅ Activity updated successfully:', response);

          // Show success message
          Swal.fire({
            icon: 'success',
            title: 'Activity Updated!',
            text: `"${apiPayload.activity_name}" has been updated successfully.`,
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
          });

          // Close modal
          this.modalService.dismissAll();

          // Refresh the list and count
          this.loadActivities();
          this.loadActivityCount();
        },
        error: (error) => {
          console.error('❌ Failed to update activity:', error);

          // Extract error message
          let errorMessage = 'Failed to update activity. Please try again.';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.error?.detail) {
            errorMessage = error.error.detail;
          } else if (error.message) {
            errorMessage = error.message;
          }

          // Show error message
          Swal.fire({
            icon: 'error',
            title: 'Update Failed',
            text: errorMessage,
            confirmButtonText: 'OK',
            confirmButtonColor: '#dc3545'
          });
        }
      });
  }

  // Delete activity
  deleteActivity(activity: NewYearActivity): void {
    const activityName = activity.activity_name || activity.holiday || 'this activity';

    Swal.fire({
      title: 'Delete Activity?',
      html: `
        <div class="text-start">
          <p class="mb-2">You are about to delete:</p>
          <div class="alert alert-warning mb-3">
            <strong>${activityName}</strong><br>
            <small class="text-muted">Date: ${this.formatDate(activity.date || activity.holiday_date)}</small>
          </div>
          <p class="text-danger mb-0">
            <i class="fas fa-exclamation-triangle me-1"></i>
            This action cannot be undone.
          </p>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: '<i class="fas fa-trash me-1"></i> Yes, Delete',
      cancelButtonText: '<i class="fas fa-times me-1"></i> Cancel',
      reverseButtons: true,
      focusCancel: true
    }).then((result) => {
      if (result.isConfirmed) {
        // Show loading state
        Swal.fire({
          title: 'Deleting...',
          text: 'Please wait while we delete the activity.',
          allowOutsideClick: false,
          allowEscapeKey: false,
          showConfirmButton: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        console.log('🗑️ Deleting activity:', {
          id: activity.id,
          name: activityName
        });

        this.newYearActivityService.deleteActivity(activity.id!)
          .subscribe({
            next: () => {
              console.log('✅ Activity deleted successfully');

              // Show success message
              Swal.fire({
                icon: 'success',
                title: 'Deleted Successfully!',
                text: `"${activityName}" has been deleted.`,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
              });

              // Refresh the list and count
              this.loadActivities();
              this.loadActivityCount();
            },
            error: (error) => {
              console.error('❌ Failed to delete activity:', error);

              // Extract error message
              let errorMessage = 'Failed to delete activity. Please try again.';
              if (error.error?.message) {
                errorMessage = error.error.message;
              } else if (error.error?.detail) {
                errorMessage = error.error.detail;
              } else if (error.message) {
                errorMessage = error.message;
              }

              // Show error message
              Swal.fire({
                icon: 'error',
                title: 'Delete Failed',
                html: `
                  <div class="text-start">
                    <p class="mb-2">Could not delete "${activityName}"</p>
                    <div class="alert alert-danger mb-0">
                      <small>${errorMessage}</small>
                    </div>
                  </div>
                `,
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc3545'
              });
            }
          });
      }
    });
  }

  // Helper method to format date for input
  private formatDateForInput(date: string | Date): string {
    if (!date) return '';

    const d = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(d.getTime())) return '';

    return d.toISOString().split('T')[0];
  }

  // Helper method to mark all form controls as touched
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Validate if activity name already exists (excluding current activity)
  private isActivityNameDuplicate(activityName: string, excludeId?: string): boolean {
    return this.activities.some(activity =>
      activity.id !== excludeId &&
      (activity.activity_name?.toLowerCase() === activityName.toLowerCase() ||
       activity.holiday?.toLowerCase() === activityName.toLowerCase())
    );
  }

  // Validate if date already exists (excluding current activity)
  private isDateDuplicate(date: string, excludeId?: string): boolean {
    return this.activities.some(activity =>
      activity.id !== excludeId &&
      (activity.date === date || activity.holiday_date === date)
    );
  }

  // Get activity display name
  getActivityDisplayName(activity: NewYearActivity): string {
    return activity.activity_name || activity.holiday || 'Unnamed Activity';
  }

  // Check if activity can be edited (add business logic here if needed)
  canEditActivity(activity: NewYearActivity): boolean {
    // Add any business rules for editing activities
    // For example, you might not allow editing completed activities
    return activity.status !== 'completed';
  }

  // Check if activity can be deleted (add business logic here if needed)
  canDeleteActivity(activity: NewYearActivity): boolean {
    // Add any business rules for deleting activities
    // For example, you might not allow deleting ongoing activities
    return activity.status !== 'ongoing';
  }



  // Format date for display
  formatDate(date: string | undefined): string {
    if (!date) return 'N/A';

    const d = new Date(date);
    if (isNaN(d.getTime())) return 'N/A';

    return d.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  // Get month name from date
  getMonthFromDate(date: string): string {
    if (!date) return 'N/A';

    const d = new Date(date);
    if (isNaN(d.getTime())) return 'N/A';

    return d.toLocaleDateString('en-US', { month: 'long' });
  }

  // Get day name from date
  getDayFromDate(date: string): string {
    if (!date) return 'N/A';

    const d = new Date(date);
    if (isNaN(d.getTime())) return 'N/A';

    return d.toLocaleDateString('en-US', { weekday: 'long' });
  }
}
