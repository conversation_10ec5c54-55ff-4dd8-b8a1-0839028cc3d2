<div class="row">
  <div class="col-xl-10 main-content ps-xl-4 pe-xl-5">
    <h1 class="page-title">Collapse</h1>
    <p class="lead">Toggle the visibility of content across your project with a few classes and javascript. Read the <a href="https://ng-bootstrap.github.io/#/components/collapse/examples" target="_blank">Official Ng-Bootstrap Documentation</a> for a full list of instructions and other options.</p>
    
    <hr>
    
    <h4 #default>Basic Example</h4>
    <p class="mb-3">Click the buttons below to show and hide another element via class changes:</p>
    <div class="example">
      <p class="mb-2">
        <button type="button" class="btn btn-outline-primary" (click)="isCollapsed = !isCollapsed"
                [attr.aria-expanded]="!isCollapsed" aria-controls="collapseExample">
          Toggle
        </button>
      </p>
      <div id="collapseExample" [ngbCollapse]="isCollapsed">
        <div class="card">
          <div class="card-body">
            Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.
          </div>
        </div>
      </div>
    </div>
    <app-code-preview [codeContent]="defaultCollapseCode"></app-code-preview>

    <hr>
    
    <h4 #responsiveNavbar>Responsive Navbar</h4>
    <p class="mb-3">A responsive navbar can be achieved with an <code>ngbCollapse</code> directive. Resize your browser window to see it in action!</p>
    <div class="example">
      <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-3">
        <a class="navbar-brand" routerLink=".">Responsive navbar</a>

        <!-- Toggle the value of the property when the toggler button is clicked. -->
        <button class="navbar-toggler" type="button" (click)="isMenuCollapsed = !isMenuCollapsed">
          &#9776;
        </button>

        <!-- Add the ngbCollapse directive to the element below. -->
        <div [ngbCollapse]="isMenuCollapsed" class="collapse navbar-collapse">
          <ul class="navbar-nav">
            <li class="nav-item active">
              <!-- Close the menu when a link is clicked. -->
              <a class="nav-link" routerLink="." (click)="isMenuCollapsed = true">Features</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="." (click)="isMenuCollapsed = true">Examples</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" routerLink="." (click)="isMenuCollapsed = true">About</a>
            </li>
          </ul>
        </div>
      </nav>
    </div>
    <app-code-preview [codeContent]="responsiveNavbarCode"></app-code-preview>
    
  </div>
  <div class="col-xl-2 content-nav-wrapper">
    <ul class="nav content-nav d-flex flex-column">
      <li class="nav-item">
        <a (click)="scrollTo(default)" class="nav-link">Basic example</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollTo(responsiveNavbar)" class="nav-link">Responsive Navbar</a>
      </li>
    </ul>
  </div>
</div>