import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { HttpErrorHandlerService } from './http-error-handler.service';

@Injectable({
  providedIn: 'root'
})
export class ErrorTestingService {

  constructor(
    private http: HttpClient,
    private httpErrorHandler: HttpErrorHandlerService
  ) {}

  /**
   * Test different error scenarios for demonstration
   */

  // Test 400 Bad Request
  testBadRequest(): Observable<any> {
    return this.http.post('/api/v1/test/400', { invalid: 'data' });
  }

  // Test 401 Unauthorized
  testUnauthorized(): Observable<any> {
    return this.http.get('/api/v1/test/401');
  }

  // Test 403 Forbidden
  testForbidden(): Observable<any> {
    return this.http.get('/api/v1/test/403');
  }

  // Test 404 Not Found
  testNotFound(): Observable<any> {
    return this.http.get('/api/v1/test/nonexistent');
  }

  // Test 422 Validation Error
  testValidationError(): Observable<any> {
    return this.http.post('/api/v1/test/422', {
      email: 'invalid-email',
      password: '123' // too short
    });
  }

  // Test 500 Server Error
  testServerError(): Observable<any> {
    return this.http.get('/api/v1/test/500');
  }

  // Test Network Error (invalid endpoint)
  testNetworkError(): Observable<any> {
    return this.http.get('/api/v1/test/network-error');
  }

  // Test with manual error handling
  testManualErrorHandling(): Observable<any> {
    return this.httpErrorHandler.get('/api/v1/test/500',
      HttpErrorHandlerService.OPTIONS.MANUAL_ERROR_HANDLING
    );
  }

  /**
   * Simulate FastAPI validation error response
   */
  simulateFastAPIValidationError(): Observable<any> {
    // This would typically come from your actual API
    // For testing, you can modify this to call a real endpoint that returns validation errors
    return this.http.post('/api/v1/employees/', {
      // Missing required fields to trigger validation errors
      email: 'invalid-email-format',
      password: '123', // too short
      // missing first_name, last_name, etc.
    });
  }

  /**
   * Test bulk upload error scenario
   */
  testBulkUploadError(): Observable<any> {
    const formData = new FormData();
    // Add an invalid file to trigger bulk upload errors
    const invalidFile = new Blob(['invalid,data,format'], { type: 'text/plain' });
    formData.append('file', invalidFile, 'invalid.txt');

    return this.http.post('/api/v1/employees/bulk-upload', formData);
  }
}
