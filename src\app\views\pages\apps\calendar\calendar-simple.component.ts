import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, AfterViewInit, ViewChild } from '@angular/core';

import { FullCalendarModule, FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions, DateSelectArg, EventClickArg, EventApi } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';
import { CalendarService, MyCalendarResponse, CalendarData } from '../../../../core/services/calendar.service';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'app-calendar-simple',
  standalone: true,
  imports: [
    <PERSON><PERSON><PERSON>le,
    NgIf,
    CommonModule,
    FullCalendarModule
  ],
  template: `
    <div class="card">
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h4 class="card-title">My Calendar</h4>
              <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" (click)="navigatePrevious()">
                  <i class="feather icon-chevron-left"></i> Previous
                </button>
                <button type="button" class="btn btn-outline-primary" (click)="navigateToday()">
                  Today
                </button>
                <button type="button" class="btn btn-outline-primary" (click)="navigateNext()">
                  Next <i class="feather icon-chevron-right"></i>
                </button>
              </div>
            </div>
            
            <div *ngIf="loading" class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <p class="mt-2">Loading calendar data...</p>
            </div>
            
            <div *ngIf="error" class="alert alert-danger">
              {{ error }}
            </div>
            
            <full-calendar 
              #calendar
              [options]="calendarOptions"
              class="demo-app-calendar">
            </full-calendar>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: './calendar.component.scss'
})
export class CalendarSimpleComponent implements OnInit, AfterViewInit {

  @ViewChild('calendar') calendarComponent: FullCalendarComponent;

  calendarOptions: CalendarOptions = {
    plugins: [
      dayGridPlugin,
      timeGridPlugin,
      listPlugin,
      interactionPlugin
    ],
    headerToolbar: {
      left: '',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay'
    },
    initialView: 'dayGridMonth',
    weekends: true,
    editable: false,
    selectable: false,
    selectMirror: true,
    dayMaxEvents: true,
    events: [],
    eventClick: this.handleEventClick.bind(this),
    eventsSet: this.handleEvents.bind(this),
    viewDidMount: this.handleViewDidMount.bind(this),
    height: 'auto',
    contentHeight: 600,
    aspectRatio: 1.35,
    firstDay: 1, // Monday
    businessHours: {
      daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
      startTime: '09:00',
      endTime: '18:00',
    },
    eventDisplay: 'block',
    displayEventTime: false,
    eventTextColor: '#ffffff',
    eventBorderColor: 'transparent'
  };

  // Component state
  loading = false;
  error: string | null = null;
  currentDisplayMonth: { year: number; month: number } | null = null;
  calendarEvents: any[] = [];

  constructor(
    private calendarService: CalendarService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    console.log('📅 CalendarSimpleComponent: Initializing...');
    this.loadCurrentMonthData();
  }

  ngAfterViewInit() {
    console.log('📅 CalendarSimpleComponent: View initialized');
  }

  /**
   * Load calendar data for current month
   */
  loadCurrentMonthData() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // JavaScript months are 0-based
    
    this.loadCalendarData(year, month);
  }

  /**
   * Load calendar data for specific year and month
   */
  loadCalendarData(year: number, month: number) {
    console.log(`📅 CalendarSimpleComponent: Loading data for ${year}-${month.toString().padStart(2, '0')}`);
    
    this.loading = true;
    this.error = null;
    this.currentDisplayMonth = { year, month };

    this.calendarService.getMyCalendar(year, month).subscribe({
      next: (response: MyCalendarResponse) => {
        console.log('📅 CalendarSimpleComponent: API Response:', response);
        
        if (response.success && response.data?.calendar_data?.days) {
          this.processCalendarData(response.data.calendar_data);
        } else {
          this.error = 'Failed to load calendar data. Invalid response format.';
          this.calendarEvents = [];
          this.updateCalendarEvents([]);
        }
        
        this.loading = false;
      },
      error: (error) => {
        console.error('❌ CalendarSimpleComponent: Error loading calendar data:', error);
        this.error = 'Failed to load calendar data. Please try again.';
        this.calendarEvents = [];
        this.updateCalendarEvents([]);
        this.loading = false;
      }
    });
  }

  /**
   * Process calendar data from API response
   */
  processCalendarData(calendarData: CalendarData) {
    console.log('📅 CalendarSimpleComponent: Processing calendar data...');
    
    const events: any[] = [];
    
    if (calendarData.days && Array.isArray(calendarData.days)) {
      calendarData.days.forEach(day => {
        // Process explicit events
        if (day.events && Array.isArray(day.events)) {
          day.events.forEach(event => {
            const calendarEvent = this.transformEventToFullCalendar(event, day);
            events.push(calendarEvent);
          });
        }
        
        // Process primary status as implicit event if no explicit events
        if ((!day.events || day.events.length === 0) && day.primary_status) {
          const implicitEvent = this.createImplicitEvent(day);
          if (implicitEvent) {
            events.push(implicitEvent);
          }
        }
      });
    }
    
    console.log(`📅 CalendarSimpleComponent: Processed ${events.length} events`);
    console.log('📅 CalendarSimpleComponent: Sample events:', events.slice(0, 3));
    
    this.calendarEvents = events;
    this.updateCalendarEvents(events);
  }

  /**
   * Transform API event to FullCalendar format
   */
  transformEventToFullCalendar(event: any, day: any): any {
    return {
      id: event.id || `event-${day.date}-${event.event_type}`,
      title: event.title || event.event_type,
      start: day.date,
      backgroundColor: event.color || '#007bff',
      textColor: event.text_color || '#ffffff',
      borderColor: event.color || '#007bff',
      extendedProps: {
        event_type: event.event_type,
        status: event.status,
        description: event.description,
        primary_status: day.primary_status
      }
    };
  }

  /**
   * Create implicit event from primary status
   */
  createImplicitEvent(day: any): any | null {
    const primaryStatus = day.primary_status;
    
    switch (primaryStatus) {
      case 'weekend':
        return {
          id: `weekend-${day.date}`,
          title: 'Weekend',
          start: day.date,
          backgroundColor: '#6c757d',
          textColor: '#ffffff',
          borderColor: '#6c757d',
          extendedProps: {
            event_type: 'weekend',
            status: 'weekend',
            primary_status: primaryStatus
          }
        };
      case 'holiday':
        return {
          id: `holiday-${day.date}`,
          title: 'Holiday',
          start: day.date,
          backgroundColor: '#dc3545',
          textColor: '#ffffff',
          borderColor: '#dc3545',
          extendedProps: {
            event_type: 'holiday',
            status: 'holiday',
            primary_status: primaryStatus
          }
        };
      case 'absent':
        return {
          id: `absent-${day.date}`,
          title: 'Absent',
          start: day.date,
          backgroundColor: '#fd7e14',
          textColor: '#ffffff',
          borderColor: '#fd7e14',
          extendedProps: {
            event_type: 'absent',
            status: 'absent',
            primary_status: primaryStatus
          }
        };
      default:
        return null;
    }
  }

  /**
   * Update calendar with new events
   */
  updateCalendarEvents(events: any[]) {
    console.log(`📅 CalendarSimpleComponent: Updating calendar with ${events.length} events`);

    // Debug: Log first few events
    if (events.length > 0) {
      console.log('📅 CalendarSimpleComponent: Sample events:', events.slice(0, 3).map(e => ({
        id: e.id,
        title: e.title,
        start: e.start,
        backgroundColor: e.backgroundColor
      })));
    }

    if (this.calendarComponent) {
      try {
        const calendarApi = this.calendarComponent.getApi();

        // Remove existing events
        const existingEvents = calendarApi.getEvents();
        console.log(`📅 CalendarSimpleComponent: Removing ${existingEvents.length} existing events`);
        existingEvents.forEach(event => event.remove());

        // Add new events
        let addedCount = 0;
        events.forEach((event, index) => {
          try {
            calendarApi.addEvent(event);
            addedCount++;
          } catch (eventError) {
            console.error(`❌ CalendarSimpleComponent: Failed to add event ${index}:`, eventError, event);
          }
        });

        console.log(`✅ CalendarSimpleComponent: Successfully added ${addedCount}/${events.length} events`);

        // Force calendar refresh
        setTimeout(() => {
          calendarApi.render();
          console.log('🔄 CalendarSimpleComponent: Calendar re-rendered');
        }, 50);

      } catch (error) {
        console.error('❌ CalendarSimpleComponent: Error updating calendar events:', error);
      }
    } else {
      console.log('📅 CalendarSimpleComponent: Using fallback method - updating calendar options');
      // Fallback: update calendar options
      this.calendarOptions = {
        ...this.calendarOptions,
        events: events
      };
    }
  }

  /**
   * Navigation methods
   */
  navigatePrevious() {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      calendarApi.prev();
    }
  }

  navigateNext() {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      calendarApi.next();
    }
  }

  navigateToday() {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      calendarApi.today();
    }
  }

  /**
   * Event handlers
   */
  handleEventClick(clickInfo: EventClickArg) {
    console.log('📅 CalendarSimpleComponent: Event clicked:', clickInfo.event.title);
  }

  handleEvents(events: EventApi[]) {
    console.log('📅 CalendarSimpleComponent: Events set:', events.length);
  }

  handleViewDidMount(info: any) {
    console.log('📅 CalendarSimpleComponent: View mounted:', info.view.type, info.view.title);

    // Extract year and month from view
    const viewDate = info.view.currentStart;
    const year = viewDate.getFullYear();
    const month = viewDate.getMonth() + 1;

    console.log(`📅 CalendarSimpleComponent: View shows ${year}-${month.toString().padStart(2, '0')}`);

    // Always load data for the new view to ensure events are displayed
    console.log(`📅 CalendarSimpleComponent: Loading data for view: ${year}-${month}`);
    this.loadCalendarData(year, month);
  }
}
