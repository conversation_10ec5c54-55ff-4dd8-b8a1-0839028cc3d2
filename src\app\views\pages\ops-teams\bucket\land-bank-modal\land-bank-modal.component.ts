import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LandBankRow } from '../ops-team.component';

@Component({
  selector: 'app-land-bank-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './land-bank-modal.component.html',
  styleUrl: './land-bank-modal.component.scss'
})
export class LandBankModalComponent implements OnInit {
  @Input() landId?: number;

  // Form data
  formData: LandBankRow = {
    id: 0,
    projectName: '',
    entityName: '',
    ownershipType: '',
    documentationStatus: '',
    location: '',
    projectType: '',
    expectedStartDate: '',
    residentialArea: 0,
    residentialUnits: 0,
    commercialArea: 0,
    commercialUnits: 0,
    mixedArea: 0,
    mixedUnits: 0,
    remarks: ''
  };

  // Ownership type options
  ownershipTypeOptions = [
    { value: 'Owned', label: 'Owned' },
    { value: 'JDA', label: 'JDA (Joint Development Agreement)' },
    { value: 'JV', label: 'JV (Joint Venture)' },
    { value: 'Leased', label: 'Leased' }
  ];

  // Project type options
  projectTypeOptions = [
    { value: 'Residential', label: 'Residential' },
    { value: 'Commercial', label: 'Commercial' },
    { value: 'Mixed Use', label: 'Mixed Use' },
    { value: 'Industrial', label: 'Industrial' },
    { value: 'Retail', label: 'Retail' }
  ];

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      projectName: '',
      entityName: '',
      ownershipType: '',
      documentationStatus: '',
      location: '',
      projectType: '',
      expectedStartDate: '',
      residentialArea: 0,
      residentialUnits: 0,
      commercialArea: 0,
      commercialUnits: 0,
      mixedArea: 0,
      mixedUnits: 0,
      remarks: ''
    };

    // If editing an existing record, populate the form
    if (this.landId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll use mock data based on the ID
      if (this.landId === 1) {
        this.formData = {
          id: 1,
          projectName: 'Green Valley Township',
          entityName: 'ABC Developers Pvt Ltd',
          ownershipType: 'Owned',
          documentationStatus: 'All approvals in process',
          location: 'Panvel, Navi Mumbai',
          projectType: 'Residential',
          expectedStartDate: '2023-12-01',
          residentialArea: 250000,
          residentialUnits: 1200,
          commercialArea: 0,
          commercialUnits: 0,
          mixedArea: 0,
          mixedUnits: 0,
          remarks: 'Land fully paid'
        };
      } else if (this.landId === 2) {
        this.formData = {
          id: 2,
          projectName: 'Tech Hub',
          entityName: 'ABC Commercial Properties Pvt Ltd',
          ownershipType: 'JDA',
          documentationStatus: 'Approvals pending',
          location: 'Bandra Kurla Complex, Mumbai',
          projectType: 'Commercial',
          expectedStartDate: '2024-03-15',
          residentialArea: 0,
          residentialUnits: 0,
          commercialArea: 180000,
          commercialUnits: 120,
          mixedArea: 0,
          mixedUnits: 0,
          remarks: 'Premium commercial project'
        };
      } else if (this.landId === 3) {
        this.formData = {
          id: 3,
          projectName: 'Urban Square',
          entityName: 'XYZ Realty Pvt Ltd',
          ownershipType: 'JV',
          documentationStatus: 'All approvals received',
          location: 'Thane West, Mumbai',
          projectType: 'Mixed Use',
          expectedStartDate: '2024-01-10',
          residentialArea: 150000,
          residentialUnits: 800,
          commercialArea: 50000,
          commercialUnits: 40,
          mixedArea: 20000,
          mixedUnits: 15,
          remarks: 'Mixed-use development with retail, office and residential components'
        };
      }
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Close the modal and pass the data back
    this.activeModal.close(this.formData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
