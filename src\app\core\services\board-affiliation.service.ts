import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Board Affiliation interfaces
export interface BoardAffiliation {
  id: string;
  name: string;
  code: string;
  type: 'corporate' | 'non_profit' | 'government' | 'professional' | 'educational' | 'other';
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active: boolean;
  established_date?: string;
  registration_number?: string;
  regulatory_body?: string;
  membership_type?: 'board_member' | 'advisor' | 'observer' | 'committee_member' | 'honorary';
  position_title?: string;
  appointment_date?: string;
  term_end_date?: string;
  compensation_type?: 'paid' | 'unpaid' | 'equity' | 'mixed';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface BoardAffiliationCreate {
  name: string;
  code: string;
  type: 'corporate' | 'non_profit' | 'government' | 'professional' | 'educational' | 'other';
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  registration_number?: string;
  regulatory_body?: string;
  membership_type?: 'board_member' | 'advisor' | 'observer' | 'committee_member' | 'honorary';
  position_title?: string;
  appointment_date?: string;
  term_end_date?: string;
  compensation_type?: 'paid' | 'unpaid' | 'equity' | 'mixed';
}

export interface BoardAffiliationUpdate {
  name?: string;
  code?: string;
  type?: 'corporate' | 'non_profit' | 'government' | 'professional' | 'educational' | 'other';
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  registration_number?: string;
  regulatory_body?: string;
  membership_type?: 'board_member' | 'advisor' | 'observer' | 'committee_member' | 'honorary';
  position_title?: string;
  appointment_date?: string;
  term_end_date?: string;
  compensation_type?: 'paid' | 'unpaid' | 'equity' | 'mixed';
}

export interface BoardAffiliationStatistics {
  total_affiliations: number;
  active_affiliations: number;
  inactive_affiliations: number;
  affiliations_by_type: { [type: string]: number };
  affiliations_by_membership_type: { [type: string]: number };
  affiliations_by_compensation: { [type: string]: number };
  popular_affiliations: BoardAffiliation[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class BoardAffiliationService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/board-affiliations/`;
  private affiliationsSubject = new BehaviorSubject<BoardAffiliation[]>([]);
  public affiliations$ = this.affiliationsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all board affiliations with optional filtering and pagination
   */
  getAffiliations(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    type?: string;
    membership_type?: string;
    compensation_type?: string;
    country?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<BoardAffiliation[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<BoardAffiliation[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.affiliationsSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get affiliation by ID
   */
  getAffiliationById(id: string): Observable<APIResponse<BoardAffiliation>> {
    return this.http.get<APIResponse<BoardAffiliation>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new affiliation
   */
  createAffiliation(affiliation: BoardAffiliationCreate): Observable<APIResponse<BoardAffiliation>> {
    return this.http.post<APIResponse<BoardAffiliation>>(this.baseUrl, affiliation)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshAffiliations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update affiliation
   */
  updateAffiliation(id: string, affiliation: BoardAffiliationUpdate): Observable<APIResponse<BoardAffiliation>> {
    return this.http.put<APIResponse<BoardAffiliation>>(`${this.baseUrl}${id}`, affiliation)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshAffiliations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete affiliation
   */
  deleteAffiliation(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshAffiliations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted affiliation
   */
  restoreAffiliation(id: string): Observable<APIResponse<BoardAffiliation>> {
    return this.http.post<APIResponse<BoardAffiliation>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshAffiliations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get affiliation statistics
   */
  getAffiliationStatistics(): Observable<APIResponse<BoardAffiliationStatistics>> {
    return this.http.get<APIResponse<BoardAffiliationStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload affiliations
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshAffiliations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Get affiliations for dropdown (simplified data)
   */
  getAffiliationsDropdown(): Observable<{ id: string; name: string; code: string; type: string }[]> {
    return this.getAffiliations({ per_page: 1000, is_active: true }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map(affiliation => ({
            id: affiliation.id,
            name: affiliation.name,
            code: affiliation.code,
            type: affiliation.type
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get active board affiliations (backward compatibility)
   */
  getActiveBoardAffiliations(): Observable<BoardAffiliation[]> {
    return this.getAffiliations({ is_active: true, per_page: 1000 }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh affiliations data
   */
  refreshAffiliations(): void {
    this.getAffiliations().subscribe();
  }

  /**
   * Clear affiliations cache
   */
  clearCache(): void {
    this.affiliationsSubject.next([]);
  }

  /**
   * Get affiliation types
   */
  getAffiliationTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'corporate', label: 'Corporate Board', description: 'Corporate company board positions' },
      { value: 'non_profit', label: 'Non-Profit Board', description: 'Non-profit organization board positions' },
      { value: 'government', label: 'Government Board', description: 'Government agency board positions' },
      { value: 'professional', label: 'Professional Board', description: 'Professional association board positions' },
      { value: 'educational', label: 'Educational Board', description: 'Educational institution board positions' },
      { value: 'other', label: 'Other', description: 'Other types of board affiliations' }
    ];
  }

  /**
   * Get membership types
   */
  getMembershipTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'board_member', label: 'Board Member', description: 'Full voting board member' },
      { value: 'advisor', label: 'Advisor', description: 'Advisory board member' },
      { value: 'observer', label: 'Observer', description: 'Non-voting observer' },
      { value: 'committee_member', label: 'Committee Member', description: 'Committee member' },
      { value: 'honorary', label: 'Honorary Member', description: 'Honorary board member' }
    ];
  }

  /**
   * Get compensation types
   */
  getCompensationTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'paid', label: 'Paid', description: 'Receives monetary compensation' },
      { value: 'unpaid', label: 'Unpaid', description: 'Volunteer position without compensation' },
      { value: 'equity', label: 'Equity', description: 'Receives equity compensation' },
      { value: 'mixed', label: 'Mixed', description: 'Combination of cash and equity' }
    ];
  }

  /**
   * Get country list for dropdown
   */
  getCountryList(): string[] {
    return [
      'India',
      'United States',
      'United Kingdom',
      'Canada',
      'Australia',
      'Singapore',
      'Hong Kong',
      'Japan',
      'Germany',
      'France',
      'Switzerland',
      'Netherlands',
      'Luxembourg',
      'UAE',
      'Malaysia'
    ];
  }

  /**
   * Get affiliation type label
   */
  getAffiliationTypeLabel(type: string): string {
    const types = this.getAffiliationTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Get membership type label
   */
  getMembershipTypeLabel(type: string): string {
    const types = this.getMembershipTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Get compensation type label
   */
  getCompensationTypeLabel(type: string): string {
    const types = this.getCompensationTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Board Affiliation service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
