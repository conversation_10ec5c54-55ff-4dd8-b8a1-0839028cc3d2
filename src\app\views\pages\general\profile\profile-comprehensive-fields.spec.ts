import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { ProfileComponent } from './profile.component';
import { EmployeeService, BizzCorpEmployee } from '../../../../core/services/employee.service';
import { AuthService, User } from '../../../../core/services/auth.service';
import { RoleService } from '../../../../core/services/role.service';

describe('ProfileComponent - Comprehensive Fields Display', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let employeeService: jasmine.SpyObj<EmployeeService>;
  let authService: jasmine.SpyObj<AuthService>;
  let roleService: jasmine.SpyObj<RoleService>;

  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    name: '<PERSON>',
    roles: ['employee']
  };

  const comprehensiveEmployee: BizzCorpEmployee = {
    id: 'emp-123',
    employee_code: 'EMP001',
    first_name: '<PERSON>',
    middle_name: '<PERSON>',
    last_name: 'Doe',
    
    // Personal Information
    blood_group: 'O+',
    date_of_birth: '1990-05-15',
    gender: 'Male',
    marital_status: 'Single',
    personal_email: '<EMAIL>',
    office_email: '<EMAIL>',
    phone_no: '9876543210',
    alternet_no: '****** 567 8901',
    address: '123 Main St, New York, NY 10001',

    // Employment Information
    joining_date: '2023-01-15',
    reporting_date: '2023-01-16',
    shift_time: '9:00 AM - 6:00 PM',
    office_location: 'Mumbai',
    approver_code: 'APP001',
    second_approver_code: 'APP002',
    role: 'U',
    department_id: 'dept-123',
    designation_id: 'desig-456',
    sub_role_id: 'role-789',

    // Financial Information
    ctc: 800000,
    attendance_bonus: 5000,
    pf: 12000,

    // Banking Information
    bank_name: 'HDFC Bank',
    bank_account_no: '**************',
    ifsc_no: 'HDFC0001234',
    pan_no: '**********',
    aadhar_no: '1234 5678 9012',
    uan_no: 'UAN001234567',
    esic_no: 'ESIC001234',

    // Status Information
    is_active: true,
    resigned_stared_date: null,
    resigned_end_date: null,

    created_at: '2023-01-15T10:00:00Z',
    updated_at: '2023-01-15T10:00:00Z'
  };

  beforeEach(async () => {
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees',
      'getBizzCorpEmployeeProfile',
      'getDepartments',
      'getDesignations',
      'getDesignationById',
      'getUserRoles'
    ]);

    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'getCurrentUser',
      'getEmployeeIdFromToken'
    ]);

    const roleServiceSpy = jasmine.createSpyObj('RoleService', ['getRoles']);

    await TestBed.configureTestingModule({
      imports: [ProfileComponent],
      providers: [
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: RoleService, useValue: roleServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    roleService = TestBed.inject(RoleService) as jasmine.SpyObj<RoleService>;

    // Setup default mocks
    authService.getCurrentUser.and.returnValue(of(mockUser));
    authService.getEmployeeIdFromToken.and.returnValue('emp-123');
    employeeService.getAllEmployees.and.returnValue(of([{
      id: 'emp-123',
      office_email: '<EMAIL>',
      employee_code: 'EMP001'
    }]));
    employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(comprehensiveEmployee));
    employeeService.getDepartments.and.returnValue(of([]));
    employeeService.getDesignations.and.returnValue(of([]));
    employeeService.getUserRoles.and.returnValue(of([{
      id: 'role-1',
      name: 'Employee',
      description: null,
      is_active: true
    }]));
  });

  describe('Personal Information Fields', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should display blood group correctly', () => {
      expect(component.profileData.bloodGroup).toBe('O+');
    });

    it('should display date of birth correctly', () => {
      expect(component.profileData.dateOfBirth).toBe('1990-05-15');
    });

    it('should display gender correctly', () => {
      expect(component.profileData.gender).toBe('Male');
    });

    it('should display marital status correctly', () => {
      expect(component.profileData.maritalStatus).toBe('Single');
    });

    it('should display personal email correctly', () => {
      expect(component.profileData.personalEmail).toBe('<EMAIL>');
    });

    it('should display phone numbers correctly', () => {
      expect(component.profileData.phoneNo).toBe('9876543210');
      expect(component.profileData.alternateNo).toBe('****** 567 8901');
    });

    it('should display address correctly', () => {
      expect(component.profileData.address).toBe('123 Main St, New York, NY 10001');
    });
  });

  describe('Employment Information Fields', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should display joining date correctly', () => {
      expect(component.profileData.joiningDate).toBe('2023-01-15');
    });

    it('should display reporting date correctly', () => {
      expect(component.profileData.reportingDate).toBe('2023-01-16');
    });

    it('should display shift time correctly', () => {
      expect(component.profileData.shiftTime).toBe('9:00 AM - 6:00 PM');
    });

    it('should display office location correctly', () => {
      expect(component.profileData.officeLocation).toBe('Mumbai');
    });

    it('should display approver codes correctly', () => {
      expect(component.profileData.approverCode).toBe('APP001');
      expect(component.profileData.secondApproverCode).toBe('APP002');
    });
  });

  describe('Financial Information Fields', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should display CTC correctly', () => {
      expect(component.profileData.ctc).toBe(800000);
    });

    it('should display attendance bonus correctly', () => {
      expect(component.profileData.attendanceBonus).toBe(5000);
    });

    it('should display PF correctly', () => {
      expect(component.profileData.pf).toBe(12000);
    });
  });

  describe('Banking Information Fields', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should display banking details correctly', () => {
      expect(component.profileData.bankName).toBe('HDFC Bank');
      expect(component.profileData.bankAccountNo).toBe('**************');
      expect(component.profileData.ifscNo).toBe('HDFC0001234');
    });

    it('should display government IDs correctly', () => {
      expect(component.profileData.panNo).toBe('**********');
      expect(component.profileData.aadharNo).toBe('1234 5678 9012');
      expect(component.profileData.uanNo).toBe('UAN001234567');
      expect(component.profileData.esicNo).toBe('ESIC001234');
    });
  });

  describe('Status Information Fields', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should display active status correctly', () => {
      expect(component.profileData.isActive).toBe(true);
    });

    it('should handle null resignation dates', () => {
      expect(component.profileData.resignedStartDate).toBe('');
      expect(component.profileData.resignedEndDate).toBe('');
    });
  });

  describe('Formatting Helper Methods', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should format dates correctly', () => {
      expect(component.formatDate('2023-01-15')).toBe('15 January 2023');
      expect(component.formatDate('')).toBe('Not Provided');
      expect(component.formatDate('invalid-date')).toBe('Invalid Date');
    });

    it('should format currency correctly', () => {
      expect(component.formatCurrency(800000)).toBe('₹8,00,000');
      expect(component.formatCurrency(null)).toBe('Not Provided');
    });

    it('should format phone numbers correctly', () => {
      expect(component.formatPhoneNumber('9876543210')).toBe('+91 98765 43210');
      expect(component.formatPhoneNumber('')).toBe('Not Provided');
      expect(component.formatPhoneNumber('****** 567 8901')).toBe('****** 567 8901');
    });

    it('should handle display values with fallbacks', () => {
      expect(component.getDisplayValue('Test Value')).toBe('Test Value');
      expect(component.getDisplayValue('')).toBe('Not Provided');
      expect(component.getDisplayValue(null)).toBe('Not Provided');
      expect(component.getDisplayValue(undefined)).toBe('Not Provided');
    });
  });

  describe('Null Value Handling', () => {
    const employeeWithNulls: BizzCorpEmployee = {
      ...comprehensiveEmployee,
      blood_group: null,
      date_of_birth: null,
      gender: null,
      marital_status: null,
      personal_email: null,
      phone_no: null,
      alternet_no: null,
      address: null,
      ctc: null,
      attendance_bonus: null,
      bank_name: null,
      pan_no: null
    };

    beforeEach(() => {
      employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(employeeWithNulls));
      component.ngOnInit();
    });

    it('should handle null values gracefully', () => {
      expect(component.profileData.bloodGroup).toBe('Not Provided');
      expect(component.profileData.gender).toBe('Not Provided');
      expect(component.profileData.personalEmail).toBe('Not Provided');
      expect(component.profileData.phoneNo).toBe('Not Provided');
      expect(component.profileData.ctc).toBe(null);
      expect(component.profileData.bankName).toBe('Not Provided');
    });
  });
});
