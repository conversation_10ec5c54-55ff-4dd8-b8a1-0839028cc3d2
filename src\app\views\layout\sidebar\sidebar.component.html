<nav class="sidebar">
  <div class="sidebar-header">

    <!--- Logo -->
    <a routerLink="/" class="sidebar-brand">

      <img
    [src]="currentLogo"
    class="img-fluid sidebar-brand-logo"
    alt="sidebar-brand"
  />

    </a>

    <!--- Toggler -->
    <div class="sidebar-toggler" #sidebarToggler (click)="toggleSidebar($event)">
      <span></span>
      <span></span>
      <span></span>
    </div>

  </div>
  <div class="sidebar-body" (mouseenter)="operSidebarFolded()" (mouseleave)="closeSidebarFolded()">

    <!--- Sidemenu start -->

    <ng-scrollbar orientation="vertical" appearance="compact">

      <ul class="sidebar-nav" id="sidebar-menu" #sidebarMenu>

        @for (item of menuItems; track item.label) {

          <!-- nav item title -->
          @if (item.isTitle) {
            <li class="nav-item nav-category">{{ item.label }}</li>
          }

          <!-- nav item -->
          @else {
            <li class="nav-item">
              <!-- nav item with submenu -->
              @if (hasItems(item)) {
                <a class="nav-link" href="javascript:void(0);" (click)="toggleMenuItem(item)">
                  <i class="link-icon" [attr.data-feather]="item.icon" appFeatherIcon></i>
                  <span class="link-title"> {{ item.label }}</span>
                  <span class="link-arrow" [attr.data-feather]="item.expanded ? 'chevron-up' : 'chevron-down'" appFeatherIcon></span>
                </a>
                <ul class="sidebar-nav sub-menu nav-second-level" [class.show]="item.expanded" [attr.aria-expanded]="item.expanded">
                  @for (subitem of item.subItems; track subitem.label) {
                    <li class="nav-item" [ngClass]="{'side-nav-item': hasItems(subitem)}">
                      <!-- nav link with submenu -->
                      @if (hasItems(subitem)) {
                        <a class="nav-link side-nav-link-a-ref" href="javascript:void(0);" (click)="toggleMenuItem(subitem)">
                          <span class="link-title"> {{ subitem.label }}</span>
                          <span class="link-arrow" [attr.data-feather]="subitem.expanded ? 'chevron-up' : 'chevron-down'" appFeatherIcon></span>
                        </a>
                        <ul class="sidebar-nav sub-menu nav-third-level" [class.show]="subitem.expanded" [attr.aria-expanded]="subitem.expanded">
                          @for (subSubitem of subitem.subItems; track subSubitem.label) {
                            <li class="nav-item">
                              <a class="nav-link nav-link-ref" [routerLink]="subSubitem.link" [routerLinkActive]="['active']">
                                {{ subSubitem.label }}
                              </a>
                            </li>
                          }
                        </ul>
                      }
                      <!-- nav link without submenu -->
                      @else {
                        <a class="nav-link nav-link-ref" [routerLink]="subitem.link" [fragment]="subitem.fragment" [routerLinkActive]="['active']">
                          {{ subitem.label }}
                        </a>
                      }
                    </li>
                  }
                </ul>
              }
              <!-- nav item without submenu -->
              @else {
                <a class="nav-link nav-link-ref" [routerLink]="item.link" [fragment]="item.fragment" [routerLinkActive]="['active']">
                  <i class="link-icon" [attr.data-feather]="item.icon" appFeatherIcon></i>
                  <span class="link-title"> {{ item.label }}</span>
                  @if (item.badge) {
                    <span class="badge bg-{{item.badge.variant}}">{{item.badge.text}}</span>
                  }
                </a>
              }
            </li>
          }

        }

      </ul>

    </ng-scrollbar>

    <!--- Sidemenu end -->

  </div>
</nav>






