import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of, forkJoin } from 'rxjs';
import { map, tap, catchError, shareReplay } from 'rxjs/operators';
import { EmployeeService } from './employee.service';

export interface CachedEmployee {
  id: string;
  employee_code: string;
  first_name: string;
  last_name: string;
  full_name: string;
  office_email: string;
  personal_email: string;
  department_id?: string;
  designation_id?: string;
  sub_role_id?: string;
}

export interface EmployeeNameMap {
  [employeeId: string]: string;
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeCacheService {
  private employeesCache = new BehaviorSubject<CachedEmployee[]>([]);
  private employeeNamesCache = new BehaviorSubject<EmployeeNameMap>({});
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
  private loadingEmployees = false;

  // Observable streams for components to subscribe to
  public employees$ = this.employeesCache.asObservable();
  public employeeNames$ = this.employeeNamesCache.asObservable();

  constructor(private employeeService: EmployeeService) {}

  /**
   * Get all employees with caching
   * Returns cached data if available and not expired, otherwise fetches fresh data
   */
  getEmployees(): Observable<CachedEmployee[]> {
    const now = Date.now();
    const cachedEmployees = this.employeesCache.value;

    // Return cached data if still valid
    if (cachedEmployees.length > 0 && now < this.cacheExpiry) {
      console.log('✅ EmployeeCacheService: Returning cached employees');
      return of(cachedEmployees);
    }

    // Prevent multiple simultaneous requests
    if (this.loadingEmployees) {
      console.log('⏳ EmployeeCacheService: Request already in progress, waiting...');
      return this.employees$.pipe(
        map(employees => employees.length > 0 ? employees : cachedEmployees)
      );
    }

    console.log('🔄 EmployeeCacheService: Fetching fresh employee data...');
    this.loadingEmployees = true;

    return this.employeeService.getAllEmployees().pipe(
      map(response => this.transformEmployeeData(response)),
      tap(employees => {
        console.log(`✅ EmployeeCacheService: Cached ${employees.length} employees`);
        this.employeesCache.next(employees);
        this.cacheExpiry = now + this.CACHE_DURATION;
        this.loadingEmployees = false;

        // Update employee names cache
        this.updateEmployeeNamesCache(employees);
      }),
      catchError(error => {
        console.error('❌ EmployeeCacheService: Error fetching employees:', error);
        this.loadingEmployees = false;
        return of(cachedEmployees); // Return existing cache on error
      }),
      shareReplay(1)
    );
  }

  /**
   * Get employee names map for efficient lookups
   */
  getEmployeeNames(): Observable<EmployeeNameMap> {
    const cachedNames = this.employeeNamesCache.value;
    
    // If we have cached names, return them
    if (Object.keys(cachedNames).length > 0) {
      return of(cachedNames);
    }

    // Otherwise, fetch employees and extract names
    return this.getEmployees().pipe(
      map(employees => {
        const nameMap: EmployeeNameMap = {};
        employees.forEach(emp => {
          nameMap[emp.id] = emp.full_name;
        });
        return nameMap;
      })
    );
  }

  /**
   * Get employee by ID from cache
   */
  getEmployeeById(employeeId: string): Observable<CachedEmployee | null> {
    return this.getEmployees().pipe(
      map(employees => employees.find(emp => emp.id === employeeId) || null)
    );
  }

  /**
   * Get employee name by ID from cache
   */
  getEmployeeNameById(employeeId: string): Observable<string> {
    return this.getEmployeeNames().pipe(
      map(nameMap => nameMap[employeeId] || `Employee ${employeeId.slice(-4)}`)
    );
  }

  /**
   * Get multiple employee names efficiently
   */
  getEmployeeNamesByIds(employeeIds: string[]): Observable<EmployeeNameMap> {
    return this.getEmployeeNames().pipe(
      map(nameMap => {
        const result: EmployeeNameMap = {};
        employeeIds.forEach(id => {
          result[id] = nameMap[id] || `Employee ${id.slice(-4)}`;
        });
        return result;
      })
    );
  }

  /**
   * Find current employee by email or employee code
   */
  getCurrentEmployee(userEmail: string, userEmployeeCode?: string): Observable<CachedEmployee | null> {
    return this.getEmployees().pipe(
      map(employees => {
        // Try to find by email first
        let employee = employees.find(emp => 
          emp.office_email === userEmail || emp.personal_email === userEmail
        );

        // If not found by email and we have employee code, try that
        if (!employee && userEmployeeCode) {
          employee = employees.find(emp => emp.employee_code === userEmployeeCode);
        }

        return employee || null;
      })
    );
  }

  /**
   * Clear the cache (useful for refreshing data)
   */
  clearCache(): void {
    console.log('🗑️ EmployeeCacheService: Clearing cache');
    this.employeesCache.next([]);
    this.employeeNamesCache.next({});
    this.cacheExpiry = 0;
  }

  /**
   * Preload employee data (useful for initialization)
   */
  preloadEmployees(): void {
    console.log('🚀 EmployeeCacheService: Preloading employee data');
    this.getEmployees().subscribe();
  }

  /**
   * Transform raw employee API response to cached format
   */
  private transformEmployeeData(response: any): CachedEmployee[] {
    let employees: any[] = [];
    
    if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
      employees = response.data;
    } else if (Array.isArray(response)) {
      employees = response;
    }

    return employees.map(emp => ({
      id: emp.id?.toString() || '',
      employee_code: emp.employee_code || '',
      first_name: emp.first_name || '',
      last_name: emp.last_name || '',
      full_name: `${emp.first_name || ''} ${emp.last_name || ''}`.trim() || `Employee ${emp.id?.toString().slice(-4) || 'Unknown'}`,
      office_email: emp.office_email || '',
      personal_email: emp.personal_email || '',
      department_id: emp.department_id?.toString(),
      designation_id: emp.designation_id?.toString(),
      sub_role_id: emp.sub_role_id?.toString()
    }));
  }

  /**
   * Update employee names cache
   */
  private updateEmployeeNamesCache(employees: CachedEmployee[]): void {
    const nameMap: EmployeeNameMap = {};
    employees.forEach(emp => {
      nameMap[emp.id] = emp.full_name;
    });
    this.employeeNamesCache.next(nameMap);
  }

  /**
   * Get cache statistics for debugging
   */
  getCacheStats(): { employeeCount: number, cacheExpiry: Date | null, isExpired: boolean } {
    const now = Date.now();
    return {
      employeeCount: this.employeesCache.value.length,
      cacheExpiry: this.cacheExpiry > 0 ? new Date(this.cacheExpiry) : null,
      isExpired: now >= this.cacheExpiry
    };
  }
}
