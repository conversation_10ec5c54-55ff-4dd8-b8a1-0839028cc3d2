import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, tap, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Settings interfaces based on OpenAPI specification
 */
export interface Setting {
  id: string;
  key: string;
  value: any;
  category: 'system' | 'user' | 'security' | 'notification' | 'integration' | 'appearance' | 'performance' | 'backup' | 'audit' | 'custom';
  data_type: 'string' | 'number' | 'boolean' | 'json' | 'array' | 'date' | 'time' | 'datetime' | 'email' | 'url' | 'password' | 'file';
  description?: string;
  default_value?: any;
  validation_rules?: ValidationRule[];
  is_encrypted: boolean;
  is_public: boolean;
  is_readonly: boolean;
  requires_restart: boolean;
  environment_specific: boolean;
  user_configurable: boolean;
  display_order: number;
  group_name?: string;
  tags?: string[];
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'enum' | 'custom';
  value?: any;
  message?: string;
}

export interface SettingCreate {
  key: string;
  value: any;
  category: 'system' | 'user' | 'security' | 'notification' | 'integration' | 'appearance' | 'performance' | 'backup' | 'audit' | 'custom';
  data_type: 'string' | 'number' | 'boolean' | 'json' | 'array' | 'date' | 'time' | 'datetime' | 'email' | 'url' | 'password' | 'file';
  description?: string;
  default_value?: any;
  validation_rules?: ValidationRule[];
  is_encrypted?: boolean;
  is_public?: boolean;
  is_readonly?: boolean;
  requires_restart?: boolean;
  environment_specific?: boolean;
  user_configurable?: boolean;
  display_order?: number;
  group_name?: string;
  tags?: string[];
}

export interface SettingUpdate {
  key?: string;
  value?: any;
  category?: 'system' | 'user' | 'security' | 'notification' | 'integration' | 'appearance' | 'performance' | 'backup' | 'audit' | 'custom';
  data_type?: 'string' | 'number' | 'boolean' | 'json' | 'array' | 'date' | 'time' | 'datetime' | 'email' | 'url' | 'password' | 'file';
  description?: string;
  default_value?: any;
  validation_rules?: ValidationRule[];
  is_encrypted?: boolean;
  is_public?: boolean;
  is_readonly?: boolean;
  requires_restart?: boolean;
  environment_specific?: boolean;
  user_configurable?: boolean;
  display_order?: number;
  group_name?: string;
  tags?: string[];
}

export interface SettingsStatistics {
  total_settings: number;
  settings_by_category: { [category: string]: number };
  settings_by_type: { [type: string]: number };
  encrypted_settings_count: number;
  public_settings_count: number;
  readonly_settings_count: number;
  user_configurable_count: number;
  recent_changes: Setting[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
}

/**
 * Settings Service
 * Handles system configuration and settings management.
 * Provides CRUD operations for application settings with caching support.
 */
@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/settings`;

  // Settings cache
  private settingsCache = new Map<string, any>();
  private settingsCacheSubject = new BehaviorSubject<Map<string, any>>(new Map());
  public settingsCache$ = this.settingsCacheSubject.asObservable();

  // Settings list cache
  private settingsSubject = new BehaviorSubject<Setting[]>([]);
  public settings$ = this.settingsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all settings
   * GET /api/v1/settings/
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted settings
   * @returns Observable of settings list
   */
  getSettings(
    skip: number = 0,
    limit: number = 100,
    includeDeleted: boolean = false
  ): Observable<Setting[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<Setting[]>>(`${this.baseUrl}/`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          // Update cache
          response.data.forEach(setting => {
            this.settingsCache.set(setting.key, setting.value);
          });
          this.settingsCacheSubject.next(new Map(this.settingsCache));
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getSettings', []))
    );
  }

  /**
   * Get a specific setting by ID
   * GET /api/v1/settings/{setting_id}
   * @param settingId Setting ID
   * @param includeDeleted Whether to include soft-deleted settings
   * @returns Observable of setting
   */
  getSetting(settingId: string, includeDeleted: boolean = false): Observable<Setting> {
    const params = new HttpParams().set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<Setting>>(`${this.baseUrl}/${settingId}`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          // Update cache
          this.settingsCache.set(response.data.key, response.data.value);
          this.settingsCacheSubject.next(new Map(this.settingsCache));
          return response.data;
        }
        throw new Error('Setting not found');
      }),
      catchError((error: any): Observable<Setting> => {
        console.error('getSetting failed:', error);
        return throwError(() => new Error('Failed to get setting'));
      })
    );
  }

  /**
   * Get a setting value by key (with caching)
   * @param key Setting key
   * @param defaultValue Default value if setting not found
   * @param useCache Whether to use cached value
   * @returns Observable of setting value
   */
  getSettingByKey<T = any>(key: string, defaultValue?: T, useCache: boolean = true): Observable<T> {
    // Check cache first if enabled
    if (useCache && this.settingsCache.has(key)) {
      return new Observable(observer => {
        observer.next(this.settingsCache.get(key) as T);
        observer.complete();
      });
    }

    // Fetch all settings and find by key
    return this.getSettings().pipe(
      map(settings => {
        const setting = settings.find(s => s.key === key);
        return setting ? setting.value as T : defaultValue as T;
      })
    );
  }

  /**
   * Create a new setting
   * POST /api/v1/settings/
   * @param setting Setting data
   * @returns Observable of created setting
   */
  createSetting(setting: SettingCreate): Observable<Setting> {
    return this.http.post<APIResponse<Setting>>(`${this.baseUrl}/`, setting).pipe(
      map(response => {
        if (response.success && response.data) {
          // Update cache
          this.settingsCache.set(response.data.key, response.data.value);
          this.settingsCacheSubject.next(new Map(this.settingsCache));
          return response.data;
        }
        throw new Error('Failed to create setting');
      }),
      catchError((error: any): Observable<Setting> => {
        console.error('createSetting failed:', error);
        return throwError(() => new Error('Failed to create setting'));
      })
    );
  }

  /**
   * Update an existing setting
   * PUT /api/v1/settings/{setting_id}
   * @param settingId Setting ID
   * @param setting Updated setting data
   * @returns Observable of updated setting
   */
  updateSetting(settingId: string, setting: SettingUpdate): Observable<Setting> {
    return this.http.put<APIResponse<Setting>>(`${this.baseUrl}/${settingId}`, setting).pipe(
      map(response => {
        if (response.success && response.data) {
          // Update cache
          this.settingsCache.set(response.data.key, response.data.value);
          this.settingsCacheSubject.next(new Map(this.settingsCache));
          return response.data;
        }
        throw new Error('Failed to update setting');
      }),
      catchError((error: any): Observable<Setting> => {
        console.error('updateSetting failed:', error);
        return throwError(() => new Error('Failed to update setting'));
      })
    );
  }

  /**
   * Update or create a setting by key
   * @param key Setting key
   * @param value Setting value
   * @returns Observable of setting
   */
  setSettingByKey(key: string, value: any): Observable<Setting> {
    // First try to find existing setting
    return this.getSettings().pipe(
      map(settings => settings.find(s => s.key === key)),
      switchMap(existingSetting => {
        if (existingSetting) {
          // Update existing setting
          return this.updateSetting(existingSetting.id, { value });
        } else {
          // Create new setting
          return this.createSetting({
            key,
            value,
            category: 'custom',
            data_type: typeof value === 'boolean' ? 'boolean' : typeof value === 'number' ? 'number' : 'string'
          });
        }
      })
    );
  }

  /**
   * Delete a setting (soft delete)
   * DELETE /api/v1/settings/{setting_id}
   * @param settingId Setting ID
   * @param hardDelete Whether to permanently delete
   * @returns Observable of operation result
   */
  deleteSetting(settingId: string, hardDelete: boolean = false): Observable<APIResponse<boolean>> {
    const params = new HttpParams().set('hard_delete', hardDelete.toString());

    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${settingId}`, { params }).pipe(
      tap(response => {
        if (response.success) {
          // Remove from cache (we'd need to fetch the setting first to get the key)
          this.clearCache();
        }
      }),
      catchError((error: any): Observable<APIResponse<boolean>> => {
        console.error('deleteSetting failed:', error);
        return throwError(() => new Error('Failed to delete setting'));
      })
    );
  }

  /**
   * Restore a soft-deleted setting
   * POST /api/v1/settings/{setting_id}/restore
   * @param settingId Setting ID
   * @returns Observable of restored setting
   */
  restoreSetting(settingId: string): Observable<Setting> {
    return this.http.post<APIResponse<Setting>>(`${this.baseUrl}/${settingId}/restore`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          // Update cache
          this.settingsCache.set(response.data.key, response.data.value);
          this.settingsCacheSubject.next(new Map(this.settingsCache));
          return response.data;
        }
        throw new Error('Failed to restore setting');
      }),
      catchError((error: any): Observable<Setting> => {
        console.error('restoreSetting failed:', error);
        return throwError(() => new Error('Failed to restore setting'));
      })
    );
  }

  /**
   * Get multiple settings by keys
   * @param keys Array of setting keys
   * @returns Observable of settings object
   */
  getSettingsByKeys(keys: string[]): Observable<{ [key: string]: any }> {
    return this.getSettings().pipe(
      map(settings => {
        const result: { [key: string]: any } = {};
        keys.forEach(key => {
          const setting = settings.find(s => s.key === key);
          if (setting) {
            result[key] = setting.value;
          }
        });
        return result;
      })
    );
  }

  /**
   * Clear settings cache
   */
  clearCache(): void {
    this.settingsCache.clear();
    this.settingsCacheSubject.next(new Map());
  }

  /**
   * Refresh cache by fetching all settings
   * @returns Observable of settings
   */
  refreshCache(): Observable<Setting[]> {
    this.clearCache();
    return this.getSettings();
  }

  /**
   * Get settings with enhanced filtering and pagination
   */
  getSettingsWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    category?: string;
    data_type?: string;
    is_encrypted?: boolean;
    is_public?: boolean;
    is_readonly?: boolean;
    user_configurable?: boolean;
    include_deleted?: boolean;
  }): Observable<APIResponse<Setting[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Setting[]>>(`${this.baseUrl}/`, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.settingsSubject.next(response.data);
            // Update cache
            response.data.forEach(setting => {
              this.settingsCache.set(setting.key, setting.value);
            });
            this.settingsCacheSubject.next(new Map(this.settingsCache));
          }
        }),
        catchError((error: any): Observable<APIResponse<Setting[]>> => {
          console.error('getSettingsWithResponse failed:', error);
          return throwError(() => new Error('Failed to get settings'));
        })
      );
  }

  /**
   * Get settings statistics
   */
  getSettingsStatistics(): Observable<APIResponse<SettingsStatistics>> {
    return this.http.get<APIResponse<SettingsStatistics>>(`${this.baseUrl}/statistics`)
      .pipe(
        catchError((error: any): Observable<APIResponse<SettingsStatistics>> => {
          console.error('getSettingsStatistics failed:', error);
          return throwError(() => new Error('Failed to get settings statistics'));
        })
      );
  }

  /**
   * Bulk upload settings
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}/bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshCache();
          }
        }),
        catchError((error: any): Observable<APIResponse<BulkUploadResult>> => {
          console.error('bulkUpload failed:', error);
          return throwError(() => new Error('Failed to upload settings'));
        })
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/template/download`, {
      responseType: 'blob'
    }).pipe(
      catchError((error: any): Observable<Blob> => {
        console.error('downloadTemplate failed:', error);
        return throwError(() => new Error('Failed to download template'));
      })
    );
  }

  /**
   * Export settings
   */
  exportSettings(params?: {
    category?: string;
    include_encrypted?: boolean;
    format?: 'json' | 'csv' | 'xlsx';
  }): Observable<Blob> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get(`${this.baseUrl}/export`, {
      params: httpParams,
      responseType: 'blob'
    }).pipe(
      catchError((error: any): Observable<Blob> => {
        console.error('exportSettings failed:', error);
        return throwError(() => new Error('Failed to export settings'));
      })
    );
  }

  /**
   * Validate setting value
   */
  validateSetting(key: string, value: any): Observable<APIResponse<{ valid: boolean; errors?: string[] }>> {
    return this.http.post<APIResponse<{ valid: boolean; errors?: string[] }>>(`${this.baseUrl}/validate`, { key, value })
      .pipe(
        catchError((error: any): Observable<APIResponse<{ valid: boolean; errors?: string[] }>> => {
          console.error('validateSetting failed:', error);
          return throwError(() => new Error('Failed to validate setting'));
        })
      );
  }

  /**
   * Reset setting to default value
   */
  resetToDefault(settingId: string): Observable<Setting> {
    return this.http.post<APIResponse<Setting>>(`${this.baseUrl}/${settingId}/reset`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          // Update cache
          this.settingsCache.set(response.data.key, response.data.value);
          this.settingsCacheSubject.next(new Map(this.settingsCache));
          return response.data;
        }
        throw new Error('Failed to reset setting');
      }),
      catchError((error: any): Observable<Setting> => {
        console.error('resetToDefault failed:', error);
        return throwError(() => new Error('Failed to reset setting'));
      })
    );
  }

  /**
   * Get setting categories
   */
  getSettingCategories(): { value: string; label: string; description: string; icon: string }[] {
    return [
      { value: 'system', label: 'System', description: 'Core system configuration', icon: 'settings' },
      { value: 'user', label: 'User', description: 'User preferences and settings', icon: 'user' },
      { value: 'security', label: 'Security', description: 'Security and authentication settings', icon: 'shield' },
      { value: 'notification', label: 'Notifications', description: 'Email and notification settings', icon: 'bell' },
      { value: 'integration', label: 'Integrations', description: 'Third-party integrations', icon: 'link' },
      { value: 'appearance', label: 'Appearance', description: 'UI themes and customization', icon: 'eye' },
      { value: 'performance', label: 'Performance', description: 'Performance optimization settings', icon: 'zap' },
      { value: 'backup', label: 'Backup', description: 'Backup and recovery settings', icon: 'archive' },
      { value: 'audit', label: 'Audit', description: 'Audit and logging settings', icon: 'file-text' },
      { value: 'custom', label: 'Custom', description: 'Custom application settings', icon: 'tool' }
    ];
  }

  /**
   * Get data types
   */
  getDataTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'string', label: 'Text', description: 'Text string value' },
      { value: 'number', label: 'Number', description: 'Numeric value' },
      { value: 'boolean', label: 'Boolean', description: 'True/false value' },
      { value: 'json', label: 'JSON', description: 'JSON object or array' },
      { value: 'array', label: 'Array', description: 'Array of values' },
      { value: 'date', label: 'Date', description: 'Date value' },
      { value: 'time', label: 'Time', description: 'Time value' },
      { value: 'datetime', label: 'Date & Time', description: 'Date and time value' },
      { value: 'email', label: 'Email', description: 'Email address' },
      { value: 'url', label: 'URL', description: 'Web URL' },
      { value: 'password', label: 'Password', description: 'Encrypted password' },
      { value: 'file', label: 'File', description: 'File upload' }
    ];
  }

  /**
   * Get category label
   */
  getCategoryLabel(category: string): string {
    const categories = this.getSettingCategories();
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? categoryObj.label : category;
  }

  /**
   * Get data type label
   */
  getDataTypeLabel(dataType: string): string {
    const types = this.getDataTypes();
    const typeObj = types.find(t => t.value === dataType);
    return typeObj ? typeObj.label : dataType;
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    const badgeClasses = {
      'system': 'badge bg-primary',
      'user': 'badge bg-success',
      'security': 'badge bg-danger',
      'notification': 'badge bg-warning',
      'integration': 'badge bg-info',
      'appearance': 'badge bg-secondary',
      'performance': 'badge bg-warning',
      'backup': 'badge bg-dark',
      'audit': 'badge bg-info',
      'custom': 'badge bg-light text-dark'
    };
    return badgeClasses[category as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Get data type badge class
   */
  getDataTypeBadgeClass(dataType: string): string {
    const badgeClasses = {
      'string': 'badge bg-primary',
      'number': 'badge bg-success',
      'boolean': 'badge bg-warning',
      'json': 'badge bg-danger',
      'array': 'badge bg-info',
      'date': 'badge bg-secondary',
      'time': 'badge bg-secondary',
      'datetime': 'badge bg-secondary',
      'email': 'badge bg-info',
      'url': 'badge bg-info',
      'password': 'badge bg-danger',
      'file': 'badge bg-dark'
    };
    return badgeClasses[dataType as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Format setting value for display
   */
  formatValue(setting: Setting): string {
    if (setting.is_encrypted && setting.data_type === 'password') {
      return '••••••••';
    }

    if (setting.value === null || setting.value === undefined) {
      return 'Not set';
    }

    switch (setting.data_type) {
      case 'boolean':
        return setting.value ? 'Yes' : 'No';
      case 'json':
      case 'array':
        return JSON.stringify(setting.value);
      case 'date':
        return new Date(setting.value).toLocaleDateString();
      case 'datetime':
        return new Date(setting.value).toLocaleString();
      case 'time':
        return new Date(setting.value).toLocaleTimeString();
      default:
        return setting.value.toString();
    }
  }

  /**
   * Check if setting requires restart
   */
  requiresRestart(settings: Setting[]): boolean {
    return settings.some(setting => setting.requires_restart);
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
