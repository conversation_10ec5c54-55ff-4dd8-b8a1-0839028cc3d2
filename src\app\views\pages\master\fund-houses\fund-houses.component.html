<!-- Fund Houses Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-briefcase me-2"></i>
              Fund Houses Management
            </h4>
            <p class="text-muted mb-0">
              {{ viewMode === 'active' ? 'Active' : 'Deleted' }} fund houses
              ({{ getCurrentList().length }} total)
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Fund House
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Fund Houses
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Fund Houses
            </button>
          </li>
        </ul>

        <!-- Search and Filters -->
        <div class="row mb-3">
          <div class="col-md-3">
            <div class="input-group">
              <span class="input-group-text">
                <i class="feather icon-search"></i>
              </span>
              <input type="text" class="form-control" placeholder="Search fund houses..." 
                     [(ngModel)]="searchTerm" (input)="onSearch()">
            </div>
          </div>
          <div class="col-md-2" *ngIf="viewMode === 'active'">
            <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
              <option value="all">All Status</option>
              <option value="active">Active Only</option>
              <option value="inactive">Inactive Only</option>
            </select>
          </div>
          <div class="col-md-2">
            <select class="form-select" [(ngModel)]="selectedCountry" (change)="onCountryFilter()">
              <option value="">All Countries</option>
              <option *ngFor="let country of countries" [value]="country">
                {{ country }}
              </option>
            </select>
          </div>
          <div class="col-md-3">
            <select class="form-select" [(ngModel)]="selectedRegulatoryBody" (change)="onRegulatoryBodyFilter()">
              <option value="">All Regulatory Bodies</option>
              <option *ngFor="let body of regulatoryBodies" [value]="body">
                {{ body }}
              </option>
            </select>
          </div>
          <div class="col-md-2" *ngIf="viewMode === 'active'">
            <button class="btn btn-outline-danger w-100" 
                    [disabled]="selectedFundHouses.size === 0"
                    (click)="bulkDelete()">
              <i class="feather icon-trash-2 me-1"></i>
              Delete Selected
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 text-muted">Loading fund houses...</p>
        </div>

        <!-- Error State -->
        <div *ngIf="error && !loading" class="alert alert-danger">
          <i class="feather icon-alert-circle me-2"></i>
          {{ error }}
        </div>

        <!-- Data Table -->
        <div *ngIf="!loading && !error" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th width="40" *ngIf="viewMode === 'active'">
                  <input type="checkbox" class="form-check-input" 
                         [checked]="selectAll" (change)="toggleSelectAll()">
                </th>
                <th>Fund House</th>
                <th>Code</th>
                <th>Contact</th>
                <th>Location</th>
                <th>Regulatory Body</th>
                <th>AUM</th>
                <th>Funds</th>
                <th *ngIf="viewMode === 'active'">Status</th>
                <th *ngIf="viewMode === 'deleted'">Deleted</th>
                <th width="120">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let fundHouse of getCurrentList(); trackBy: trackByFundHouseId">
                <td *ngIf="viewMode === 'active'">
                  <input type="checkbox" class="form-check-input" 
                         [checked]="selectedFundHouses.has(fundHouse.id)"
                         (change)="toggleSelection(fundHouse.id)">
                </td>
                <td>
                  <div>
                    <strong>{{ fundHouse.name }}</strong>
                    <small class="d-block text-muted" *ngIf="fundHouse.description">
                      {{ fundHouse.description }}
                    </small>
                    <small class="d-block text-muted" *ngIf="fundHouse.website">
                      <i class="feather icon-globe me-1"></i>
                      <a [href]="fundHouse.website" target="_blank" class="text-decoration-none">
                        {{ fundHouse.website }}
                      </a>
                    </small>
                  </div>
                </td>
                <td>
                  <span class="badge bg-info">{{ fundHouse.code }}</span>
                  <small class="d-block text-muted" *ngIf="fundHouse.license_number">
                    License: {{ fundHouse.license_number }}
                  </small>
                </td>
                <td>
                  <div *ngIf="fundHouse.contact_email || fundHouse.contact_phone">
                    <small class="d-block text-muted" *ngIf="fundHouse.contact_email">
                      <i class="feather icon-mail me-1"></i>
                      {{ fundHouse.contact_email }}
                    </small>
                    <small class="d-block text-muted" *ngIf="fundHouse.contact_phone">
                      <i class="feather icon-phone me-1"></i>
                      {{ fundHouse.contact_phone }}
                    </small>
                  </div>
                  <span *ngIf="!fundHouse.contact_email && !fundHouse.contact_phone" class="text-muted fst-italic">
                    No contact info
                  </span>
                </td>
                <td>
                  <div *ngIf="fundHouse.city || fundHouse.country">
                    <small class="d-block text-muted" *ngIf="fundHouse.city">
                      <i class="feather icon-map-pin me-1"></i>
                      {{ fundHouse.city }}
                    </small>
                    <small class="d-block text-muted" *ngIf="fundHouse.country">
                      {{ fundHouse.country }}
                    </small>
                  </div>
                  <span *ngIf="!fundHouse.city && !fundHouse.country" class="text-muted fst-italic">
                    Not specified
                  </span>
                </td>
                <td>
                  <span *ngIf="fundHouse.regulatory_body" class="text-muted">
                    {{ fundHouse.regulatory_body }}
                  </span>
                  <span *ngIf="!fundHouse.regulatory_body" class="text-muted fst-italic">
                    Not specified
                  </span>
                </td>
                <td>
                  <span class="badge bg-light text-dark">
                    {{ formatAUM(fundHouse.total_aum) }}
                  </span>
                </td>
                <td>
                  <span class="badge bg-primary">
                    {{ fundHouse.fund_count || 0 }} funds
                  </span>
                </td>
                <td *ngIf="viewMode === 'active'">
                  <span [class]="getStatusBadgeClass(fundHouse.is_active)">
                    {{ getStatusText(fundHouse.is_active) }}
                  </span>
                </td>
                <td *ngIf="viewMode === 'deleted'">
                  <small class="text-muted">
                    {{ fundHouse.deleted_at | date:'short' }}
                  </small>
                </td>
                <td>
                  <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            type="button" data-bs-toggle="dropdown">
                      <i class="feather icon-more-horizontal"></i>
                    </button>
                    <ul class="dropdown-menu">
                      <li *ngIf="viewMode === 'active'">
                        <button class="dropdown-item" (click)="openEditModal(fundHouse)">
                          <i class="feather icon-edit me-2"></i>
                          Edit
                        </button>
                      </li>
                      <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                      <li *ngIf="viewMode === 'active'">
                        <button class="dropdown-item text-danger" (click)="deleteFundHouse(fundHouse)">
                          <i class="feather icon-trash-2 me-2"></i>
                          Delete
                        </button>
                      </li>
                      <li *ngIf="viewMode === 'deleted'">
                        <button class="dropdown-item text-success" (click)="restoreFundHouse(fundHouse)">
                          <i class="feather icon-refresh-cw me-2"></i>
                          Restore
                        </button>
                      </li>
                    </ul>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Empty State -->
          <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
            <i class="feather icon-briefcase text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">
              {{ viewMode === 'deleted' ? 'No Deleted Fund Houses' : 'No Fund Houses Found' }}
            </h5>
            <p class="text-muted">
              <span *ngIf="viewMode === 'deleted'">
                No fund houses have been deleted yet.
              </span>
              <span *ngIf="viewMode === 'active' && searchTerm">
                No fund houses match your search criteria.
              </span>
              <span *ngIf="viewMode === 'active' && !searchTerm">
                Get started by creating your first fund house.
              </span>
            </p>
            <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Create Fund House
            </button>
          </div>
        </div>

        <!-- Pagination -->
        <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
          <div class="text-muted">
            Showing {{ (currentPage - 1) * pageSize + 1 }} to 
            {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} fund houses
          </div>
          <ngb-pagination 
            [(page)]="currentPage" 
            [pageSize]="pageSize" 
            [collectionSize]="totalItems"
            [maxSize]="5"
            [rotate]="true"
            (pageChange)="onPageChange($event)">
          </ngb-pagination>
        </div>

      </div>
    </div>
  </div>
</div>
