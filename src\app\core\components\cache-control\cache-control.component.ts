import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { CacheManagerService, CacheMetrics } from '../../services/cache-manager.service';
import { FeatherIconDirective } from '../../feather-icon/feather-icon.directive';
import { environment } from '../../../../environments/environment';

/**
 * Cache Control Component
 * 
 * Provides a UI for monitoring and managing HTTP request caching.
 * Useful for development and debugging cache performance.
 */
@Component({
  selector: 'app-cache-control',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FeatherIconDirective
  ],
  template: `
    <div class="cache-control-panel" *ngIf="isVisible">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">
            <i data-feather="database" appFeatherIcon class="icon-sm me-2"></i>
            Cache Control Panel
          </h6>
          <button class="btn btn-sm btn-outline-secondary" (click)="toggleVisibility()">
            <i data-feather="x" appFeatherIcon class="icon-sm"></i>
          </button>
        </div>
        
        <div class="card-body">
          <!-- Cache Metrics -->
          <div class="row mb-3">
            <div class="col-md-3">
              <div class="metric-card">
                <div class="metric-value">{{ metrics.hits }}</div>
                <div class="metric-label">Cache Hits</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="metric-card">
                <div class="metric-value">{{ metrics.misses }}</div>
                <div class="metric-label">Cache Misses</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="metric-card">
                <div class="metric-value">{{ hitRate.toFixed(1) }}%</div>
                <div class="metric-label">Hit Rate</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="metric-card">
                <div class="metric-value">{{ metrics.totalRequests }}</div>
                <div class="metric-label">Total Requests</div>
              </div>
            </div>
          </div>

          <!-- Cache Efficiency -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="d-flex align-items-center">
                <span class="me-2">Cache Efficiency:</span>
                <span class="badge" [ngClass]="getEfficiencyBadgeClass()">
                  {{ efficiency }}
                </span>
              </div>
            </div>
          </div>

          <!-- Cache Actions -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" (click)="clearAllCache()">
                  <i data-feather="trash-2" appFeatherIcon class="icon-sm me-1"></i>
                  Clear All
                </button>
                <button class="btn btn-sm btn-outline-info" (click)="preloadCache()">
                  <i data-feather="download" appFeatherIcon class="icon-sm me-1"></i>
                  Preload
                </button>
                <button class="btn btn-sm btn-outline-success" (click)="refreshMetrics()">
                  <i data-feather="refresh-cw" appFeatherIcon class="icon-sm me-1"></i>
                  Refresh
                </button>
              </div>
            </div>
          </div>

          <!-- Cache Invalidation -->
          <div class="row mb-3">
            <div class="col-12">
              <label class="form-label">Invalidate by Data Type:</label>
              <div class="btn-group-vertical w-100" role="group">
                <button class="btn btn-sm btn-outline-warning mb-1" 
                        (click)="invalidateDataType('employees')">
                  Employee Data
                </button>
                <button class="btn btn-sm btn-outline-warning mb-1" 
                        (click)="invalidateDataType('masterData')">
                  Master Data
                </button>
                <button class="btn btn-sm btn-outline-warning mb-1" 
                        (click)="invalidateDataType('settings')">
                  Settings & i18n
                </button>
                <button class="btn btn-sm btn-outline-warning mb-1" 
                        (click)="invalidateDataType('leave')">
                  Leave & Calendar
                </button>
              </div>
            </div>
          </div>

          <!-- Custom Pattern -->
          <div class="row">
            <div class="col-12">
              <label class="form-label">Clear by Pattern:</label>
              <div class="input-group">
                <input type="text" 
                       class="form-control form-control-sm" 
                       [(ngModel)]="customPattern"
                       placeholder="e.g., /api/v1/employees">
                <button class="btn btn-sm btn-outline-danger" 
                        (click)="clearByPattern()"
                        [disabled]="!customPattern">
                  Clear
                </button>
              </div>
            </div>
          </div>

          <!-- Recommendations -->
          <div class="row mt-3" *ngIf="recommendations.length > 0">
            <div class="col-12">
              <h6>Recommendations:</h6>
              <ul class="list-unstyled">
                <li *ngFor="let rec of recommendations" class="text-info">
                  <i data-feather="info" appFeatherIcon class="icon-sm me-1"></i>
                  {{ rec }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toggle Button (when panel is hidden) -->
    <button class="cache-toggle-btn btn btn-primary btn-sm" 
            *ngIf="!isVisible"
            (click)="toggleVisibility()"
            title="Show Cache Control Panel">
      <i data-feather="database" appFeatherIcon class="icon-sm"></i>
    </button>
  `,
  styles: [`
    .cache-control-panel {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 400px;
      z-index: 1050;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .cache-toggle-btn {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1050;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .metric-card {
      text-align: center;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background: #f8f9fa;
    }

    .metric-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #495057;
    }

    .metric-label {
      font-size: 0.8rem;
      color: #6c757d;
      margin-top: 4px;
    }

    @media (max-width: 768px) {
      .cache-control-panel {
        width: 90%;
        right: 5%;
      }
    }
  `]
})
export class CacheControlComponent implements OnInit, OnDestroy {
  isVisible = false;
  metrics: CacheMetrics = { hits: 0, misses: 0, evictions: 0, totalRequests: 0 };
  hitRate = 0;
  efficiency = 'Unknown';
  customPattern = '';
  recommendations: string[] = [];
  
  private destroy$ = new Subject<void>();

  constructor(private cacheManager: CacheManagerService) {}

  ngOnInit(): void {
    // Subscribe to cache metrics
    this.cacheManager.metrics$
      .pipe(takeUntil(this.destroy$))
      .subscribe(metrics => {
        this.metrics = metrics;
        this.hitRate = metrics.totalRequests > 0 
          ? (metrics.hits / metrics.totalRequests) * 100 
          : 0;
        this.updateEfficiency();
      });

    // Get initial recommendations
    this.updateRecommendations();

    // Only show in development environment
    this.isVisible = !environment.production;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleVisibility(): void {
    this.isVisible = !this.isVisible;
  }

  clearAllCache(): void {
    this.cacheManager.clearAllCache();
    this.showNotification('All cache cleared', 'success');
  }

  preloadCache(): void {
    this.cacheManager.preloadCriticalData();
    this.showNotification('Cache preload initiated', 'info');
  }

  refreshMetrics(): void {
    // Metrics are automatically updated via subscription
    this.updateRecommendations();
    this.showNotification('Metrics refreshed', 'success');
  }

  invalidateDataType(dataType: string): void {
    this.cacheManager.invalidateByDataType(dataType as any);
    this.showNotification(`${dataType} cache invalidated`, 'warning');
  }

  clearByPattern(): void {
    if (this.customPattern.trim()) {
      this.cacheManager.clearCacheByPattern(this.customPattern.trim());
      this.showNotification(`Cache cleared for pattern: ${this.customPattern}`, 'warning');
      this.customPattern = '';
    }
  }

  private updateEfficiency(): void {
    if (this.hitRate >= 80) this.efficiency = 'Excellent';
    else if (this.hitRate >= 60) this.efficiency = 'Good';
    else if (this.hitRate >= 40) this.efficiency = 'Fair';
    else this.efficiency = 'Poor';
  }

  private updateRecommendations(): void {
    this.recommendations = this.cacheManager.getCacheRecommendations();
  }

  getEfficiencyBadgeClass(): string {
    switch (this.efficiency) {
      case 'Excellent': return 'bg-success';
      case 'Good': return 'bg-primary';
      case 'Fair': return 'bg-warning';
      case 'Poor': return 'bg-danger';
      default: return 'bg-secondary';
    }
  }

  private showNotification(message: string, type: string): void {
    // Simple console notification for now
    console.log(`🎯 Cache Control: ${message}`);
  }
}
