// Sales List style table for Salary Slip component
.card {
  &.modern-table-card,
  &.filter-card {
    border: none;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;

    .card-body {
      padding: 15px;
    }
  }

  &.filter-card {
    background-color: #f8f9fa;

    .form-label {
      font-weight: 500;
      font-size: 0.875rem;
      color: #495057;
    }
  }
}
   

    input[type="date"],
    select.form-select {
      height: 38px;
      border-color: #ced4da;

      &:focus {
        border-color: #df5316;
        box-shadow: 0 0 0 0.2rem rgba(223, 83, 22, 0.25);
      }
    }


  

  .card-title {
    color: #3F828B;
    font-weight: 500;
    font-size: 1rem;
  }

  p {
    color: #495057;
    line-height: 1.6;
  }


// Employee info section
.employee-info {
  background-color: rgba(63, 130, 139, 0.05);
  padding: 15px;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;

  p {
    margin-bottom: 0;
  }
}

// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  thead {
    background-color: rgba(223, 83, 22, 0.05);

    th {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      color: #495057;
      padding: 12px 10px;
      border-top: none;
      border-bottom: 1px solid rgba(223, 83, 22, 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;

      &.asc:after, &.desc:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      &.asc:after {
        border-bottom: 4px solid #df5316;
      }

      &.desc:after {
        border-top: 4px solid #df5316;
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(223, 83, 22, 0.02);
      }

      td {
        vertical-align: middle;
        padding: 12px 10px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
      }
    }
  }
}

// Empty state styling
.empty-state {
  padding: 20px;
  color: #6c757d;

  i {
    color: #adb5bd;
  }
}

// Salary Slip Details Modal Styles
.salary-slip-details {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .salary-slip-modal {
    background-color: #fff;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .modal-header {
      padding: 1rem;
      border-bottom: 1px solid #dee2e6;
      
      .modal-title {
        margin-bottom: 0;
        font-weight: 600;
      }
    }
    
    .modal-body {
      padding: 0;
      overflow-y: auto;
      flex: 1;
    }
  }
}

// PDF View Styles
.pdf-view {
  .pdf-container {
    border: 1px solid #dee2e6;
    
    .pdf-toolbar {
      border-bottom: 1px solid #dee2e6;
    }
    
    .pdf-content {
      min-height: 700px;
      overflow-y: auto;
      
      .pdf-mock {
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        
        .watermark {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(-45deg);
          font-size: 4rem;
          color: rgba(0, 0, 0, 0.05);
          font-weight: bold;
          z-index: 0;
          white-space: nowrap;
        }
        
        .pdf-mockup {
          position: relative;
          z-index: 1;
        }
      }
    }
  }
  
  .pdf-table {
    font-size: 0.875rem;
  }
  
  .signature-line {
    border-bottom: 1px solid #000;
    width: 80%;
    margin: 0 auto 10px;
  }
}

// Normal view styles
.normal-view {
  .card {
    border: none;
  }
}

