import { Injectable } from '@angular/core';
import { <PERSON>ttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { LoggingService } from '../services/logging.service';

@Injectable()
export class LoggingInterceptor implements HttpInterceptor {

  constructor(private loggingService: LoggingService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Log the request using logging service
    this.loggingService.logRequest(request.method, request.url, request.body);

    const startTime = Date.now();
    return next.handle(request).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          const timeElapsed = Date.now() - startTime;
          // Log the response using logging service
          this.loggingService.logResponse(request.method, request.url, event.status, timeElapsed);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        const timeElapsed = Date.now() - startTime;
        // Provide more specific error logging using logging service
        if (error.status === 401) {
          this.loggingService.error(`Authentication Error (${timeElapsed}ms): ${request.url}`, error);
        } else if (error.status === 403) {
          this.loggingService.error(`Authorization Error (${timeElapsed}ms): ${request.url}`, error);
        } else if (error.status === 0) {
          this.loggingService.error(`Network Error (${timeElapsed}ms): ${request.url} - Check network connection`);
        } else {
          this.loggingService.error(`HTTP Error (${timeElapsed}ms): ${request.url} - ${error.status}`, error);
        }
        return throwError(() => error);
      })
    );
  }
}
