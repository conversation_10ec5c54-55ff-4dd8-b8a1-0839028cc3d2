// Component-specific styles can be added here if needed
// Most styles are now in the global _custom.scss file
.box-shadow{
    box-shadow: none !important;
}
// Table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;

    &[sortable] {
      cursor: pointer;
      position: relative;
      padding-right: 24px;

      &:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid #adb5bd;
        opacity: 0.3;
      }

      &.asc:after {
        opacity: 1;
        border-bottom: 5px solid #495057;
      }

      &.desc:after {
        opacity: 1;
        transform: translateY(-50%) rotate(180deg);
        border-bottom: 5px solid #495057;
      }
    }
  }

  td {
    padding: 12px 16px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
  }

  tbody tr {
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
}

// Modern Follow-up Section Styles
.follow-up-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  border: 1px solid #eaeaea;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }
}
.follow-up-header {
  background-color: #3F828B;
  color: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;

  h6 {
    font-weight: 600;
  }

  .icon-sm {
    stroke-width: 2px;
  }
}
.follow-up-body {
  padding: 16px;
  background-color: #f8f9fa;
}
// Follow-up History Styles
.follow-up-history {
  margin-top: 1rem;
  border-top: 1px solid #eaeaea;
}
.follow-up-history-header {
  background-color: #f0f2f5;
  padding: 12px 16px;
  display: flex;
  align-items: center;

  h6 {
    font-weight: 600;
    color: #495057;
  }
}
.follow-up-history-body {
  padding: 16px;
  background-color: white;
}
// Timeline Style for History
.history-timeline {
  position: relative;
  padding-left: 20px;

  &:before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
  }
}
.history-item {
  position: relative;
  padding-bottom: 20px;

  &:last-child {
    padding-bottom: 0;
  }
}
.history-item-badge {
  position: absolute;
  left: -20px;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3F828B;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  z-index: 1;
}
.history-item-content {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px 16px;
  margin-left: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}
.history-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  color: #6c757d;
}
.history-date, .history-time {
  display: flex;
  align-items: center;

  .icon-xs {
    stroke-width: 2.5px;
    width: 14px;
    height: 14px;
  }
}
.history-item-body {
  color: #212529;
  font-size: 14px;
  line-height: 1.5;
}
// Input group styling
.input-group {
  .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #6c757d;

    .icon-xs {
      width: 16px;
      height: 16px;
      stroke-width: 2.5px;
    }
  }
}
// Button styling
.btn-primary {
  background-color: #3F828B;
  border-color: #3F828B;

  &:hover, &:focus, &:active {
    background-color: darken(#3F828B, 10%);
    border-color: darken(#3F828B, 10%);
  }

  .icon-xs {
    stroke-width: 2.5px;
    width: 16px;
    height: 16px;
  }
}
// Responsive adjustments
@media (max-width: 767.98px) {
  .history-item-header {
    flex-direction: column;
    gap: 4px;
  }
}