<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- List View -->
        <div *ngIf="showListView">
          <!-- Header with title and add button -->
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h6 class="card-title mb-0">Login at Institute</h6>
            <button class="btn btn-primary" (click)="showAddDetailsForm()">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add Details
            </button>
          </div>

          <!-- Search and filter controls -->
          <div class="row mb-3">
            <div class="col-md-6 col-lg-4">
              <div class="input-group">
                <span class="input-group-text bg-light">
                  <i data-feather="search" class="icon-sm" appFeatherIcon></i>
                </span>
                <input type="text" class="form-control" placeholder="Search institutes..." [formControl]="searchTerm">
              </div>
            </div>
            <div class="col-md-6 col-lg-2 d-flex align-items-center mt-2 mt-md-0">
              <div class="d-flex align-items-center">
                <span class="text-muted me-2">Show:</span>
                <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="refreshInstitutes()">
                  <option [ngValue]="5">5</option>
                  <option [ngValue]="10">10</option>
                  <option [ngValue]="20">20</option>
                  <option [ngValue]="50">50</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Table -->
          <div class="table-responsive-wrapper">
            <ngx-datatable #instituteTable class="bootstrap" [rows]="filteredInstitutes" [columnMode]="ColumnMode.force"
              [headerHeight]="50" [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0">


              <!-- Actions Column -->
              <ngx-datatable-column name="Actions" [width]="120" [sortable]="false">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <div class="action-icons">
                    <button type="button" class="action-icon" ngbTooltip="View Details" (click)="editItem(row.id)">
                      <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                    </button>

                    <button type="button" class="action-icon" ngbTooltip="Edit" (click)="editItem(row.id)">
                      <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                    </button>

                    <button type="button" class="action-icon" ngbTooltip="Delete">
                      <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                    </button>
                  </div>

                </ng-template>
              </ngx-datatable-column>

              <!-- Unique ID Column -->
              <ngx-datatable-column name="Unique ID" [width]="120" [sortable]="true" prop="uniqueId">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge bg-light-primary text-primary">{{ row.uniqueId }}</span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Lead Name Column -->
              <ngx-datatable-column name="Lead Name" [width]="120" [sortable]="true" prop="leadName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.leadName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Company Column -->
              <ngx-datatable-column name="Company" [width]="150" [sortable]="true" prop="companyName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.companyName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Project Column -->
              <ngx-datatable-column name="Project" [width]="150" [sortable]="true" prop="projectName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.projectName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Product Type Column -->
              <ngx-datatable-column name="Product Type" [width]="150" [sortable]="true" prop="productType">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge bg-light text-dark">
                    {{ row.productType }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Institute Type Column -->
              <ngx-datatable-column name="Institute Type" [width]="150" [sortable]="true" prop="instituteType">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.instituteType }}
                </ng-template>
              </ngx-datatable-column>



              <!-- Legal Status Column -->
              <ngx-datatable-column name="Legal" [width]="100" [sortable]="true" prop="legalStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.legalStatus)">
                    {{ row.legalStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Technical Status Column -->
              <ngx-datatable-column name="Technical" [width]="100" [sortable]="true" prop="technicalStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.technicalStatus)">
                    {{ row.technicalStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Credit Status Column -->
              <ngx-datatable-column name="Credit" [width]="100" [sortable]="true" prop="creditStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.creditStatus)">
                    {{ row.creditStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Cashflow Status Column -->
              <ngx-datatable-column name="Cashflow" [width]="100" [sortable]="true" prop="cashflowStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.cashflowStatus)">
                    {{ row.cashflowStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Final Sanction Status Column -->
              <ngx-datatable-column name="Final Sanction" [width]="120" [sortable]="true" prop="finalSanctionStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.finalSanctionStatus)">
                    {{ row.finalSanctionStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>



            </ngx-datatable>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
              <span class="text-muted" *ngIf="collectionSize > 0">
                Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, collectionSize) }} of {{
                collectionSize }} entries
              </span>
            </div>
            <ngb-pagination [collectionSize]="collectionSize" [(page)]="page" [pageSize]="pageSize" [maxSize]="5"
              [rotate]="true" [boundaryLinks]="true" (pageChange)="refreshInstitutes()"
              class="pagination-sm"></ngb-pagination>
          </div>

        </div>

        <!-- Details Form View -->
        <div *ngIf="showDetailsForm">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h6 class="card-title mb-0">
              {{ selectedItemId ? 'Edit Institute Login Details' : 'Add Institute Login Details' }}
            </h6>
            <button class="btn btn-secondary" (click)="backToList()">
              <i data-feather="arrow-left" class="icon-sm me-1" appFeatherIcon></i>
              Back to List
            </button>
          </div>

          <!-- Tabs Navigation -->
          <ul ngbNav #loginInstituteNav="ngbNav" [(activeId)]="activeTabId" class="nav-tabs mb-4">
            <li [ngbNavItem]="1">
              <a ngbNavLink>Institute</a>
              <ng-template ngbNavContent>



                <!-- Initial Fees and CIBIL Status Section -->
                <div class="row mb-4">
                  <div class="col-12 col-md-6 col-lg-3 mb-3">
                    <label class="form-label">Initial Fees Payment</label>
                    <select class="form-select">
                      <option value="" selected disabled>Select</option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                  <div class="col-12 col-md-6 col-lg-3 mb-3">
                    <label class="form-label">CIBIL Mail Status</label>
                    <select class="form-select">
                      <option value="" selected disabled>Select</option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="2">
              <a ngbNavLink>Agency Finalization</a>
              <ng-template ngbNavContent>
                <!-- Agency Finalization Content -->
                <div class="card border-0">
                  <div>


                    <!-- Nested Tabs for Agency Types -->
                    <ul ngbNav #agencyNav="ngbNav" [(activeId)]="activeAgencyTabId" class="nav-pills nested-tabs mb-4">
                      <li [ngbNavItem]="1">
                        <a ngbNavLink>
                          <i data-feather="book" class="icon-sm me-2" appFeatherIcon></i> Legal
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Legal Agency Selection -->
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Agency Name</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select Agency</option>
                                    <option value="1">EDATE & COMPANY</option>
                                    <option value="2">SHAMDASANI & ASSOCIATES</option>
                                    <option value="3">VS LEGAL ASSOCIATES</option>
                                    <option value="4">SSP LEGAL</option>
                                    <option value="5">GAYAKWAD & ASSOCIATES</option>
                                    <option value="6">MARWAL - RACHANA MARWAL</option>
                                    <option value="7">KT JAIN</option>
                                    <option value="8">DESAI & DIWANJI</option>
                                    <option value="9">ASA LEGAL</option>
                                    <option value="10">DM ASSOCIATES</option>
                                    <option value="11">MDP PARTNERS</option>
                                    <option value="12">TATVA LEGAL</option>
                                    <option value="13">SANJEEV KANCHAN & CO (MADHURA)</option>
                                    <option value="14">LEX AETERNA PRACTICES - LEAP</option>
                                    <option value="15">ADV RASHMI SINGH & ASSOCIATES</option>
                                  </select>
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Date</label>
                                  <input type="date" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Time</label>
                                  <input type="time" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Vetting List Received</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="button" class="btn btn-secondary me-2"
                                  (click)="backToList()">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save & Next</button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="2">
                        <a ngbNavLink>
                          <i data-feather="tool" class="icon-sm me-2" appFeatherIcon></i> Technical
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Technical Agency Selection -->
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Agency Name</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select Agency</option>
                                    <option value="1">CREATIVE VALUERS & ENGINEERS PVT LTD</option>
                                    <option value="2">NEELAM ARCHITECTS AND CONSULTANTS</option>
                                    <option value="3">LIBRA VALUERS</option>
                                    <option value="4">PK ASSOCIATES</option>
                                    <option value="5">VASTUKALA CONSULTANTS (I) PVT LTD</option>
                                    <option value="6">CUSHMAN AND WAKEFIELD INDIA PVT LTD</option>
                                    <option value="7">ADROIT VALUERS</option>
                                    <option value="8">MAI'S SONS</option>
                                    <option value="9">COLLIERS INDIA</option>
                                    <option value="10">GREEN ARYA VALUERS</option>
                                  </select>
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Date</label>
                                  <input type="date" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Time</label>
                                  <input type="time" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">TSR Report</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary me-2">Save</button>
                                <button type="button" class="btn btn-secondary">Cancel</button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="3">
                        <a ngbNavLink>
                          <i data-feather="bar-chart-2" class="icon-sm me-2" appFeatherIcon></i> Valuation
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Valuation Agency Selection -->
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Agency Name</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select Agency</option>
                                    <option value="1">CREATIVE VALUERS & ENGINEERS PVT LTD</option>
                                    <option value="2">NEELAM ARCHITECTS AND CONSULTANTS</option>
                                    <option value="3">LIBRA VALUERS</option>
                                    <option value="4">PK ASSOCIATES</option>
                                    <option value="5">VASTUKALA CONSULTANTS (I) PVT LTD</option>
                                    <option value="6">CUSHMAN AND WAKEFIELD INDIA PVT LTD</option>
                                    <option value="7">ADROIT VALUERS</option>
                                    <option value="8">MAI'S SONS</option>
                                    <option value="9">COLLIERS INDIA</option>
                                    <option value="10">GREEN ARYA VALUERS</option>
                                  </select>
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Date</label>
                                  <input type="date" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Time</label>
                                  <input type="time" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">TSR Report</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="button" class="btn btn-secondary me-2"
                                  (click)="backToList()">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save & Next</button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="4">
                        <a ngbNavLink>
                          <i data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i> Credit
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Credit Agency Selection -->
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Agency Name</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select Agency</option>
                                    <option value="1">Credit Agency 1</option>
                                    <option value="2">Credit Agency 2</option>
                                    <option value="3">Credit Agency 3</option>
                                  </select>
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Date</label>
                                  <input type="date" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Time</label>
                                  <input type="time" class="form-control">
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">RCU Report Received</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">FCU Report Received</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>
                                <div class="col-12 col-md-6 col-lg-4 mb-3">
                                  <label class="form-label">Cashflow Created</label>
                                  <select class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="button" class="btn btn-secondary me-2"
                                  (click)="backToList()">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save & Next</button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>
                    </ul>

                    <!-- Nested Tab Content -->
                    <div [ngbNavOutlet]="agencyNav" class="tab-content"></div>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="3">
              <a ngbNavLink>Cash Flow</a>
              <ng-template ngbNavContent>
                <div class="card border-0 mb-4">
                  <div>
                    <div class="mb-4">

                      <div class="row">
                        <div class="col-12 col-md-6 col-lg-3 mb-3">
                          <h6 class="card-subtitle mb-3">Cash Flow from Institute</h6>
                          <label class="form-label">Cash Flow Received</label>
                          <select class="form-select">
                            <option value="" selected disabled>Select</option>
                            <option value="Yes">Yes</option>
                            <option value="No">No</option>
                          </select>
                        </div>

                      </div>
                      <div class="row">
                        <div class="col-12 mb-3">
                          <h6 class="card-subtitle mb-3">Cash Flow from Developer</h6>
                          <div class="row">
                            <div class="col-12 col-md-6  mb-3">
                              <label class="form-label">Cashflow Explanation</label>
                              <select class="form-select">
                                <option value="" selected disabled>Select</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </select>
                            </div>
                            <div class="col-12 col-md-6  mb-3">
                              <label class="form-label">Go Ahead Receiver</label>
                              <select class="form-select">
                                <option value="" selected disabled>Select</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Cash Flow from Developer Section -->
                    <div class="mb-4">

                    </div>

                    <!-- Form Buttons -->
                    <div class="text-end">
                      <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                      <button type="submit" class="btn btn-primary">Save & Next</button>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="4">
              <a ngbNavLink>Final Sanction</a>
              <ng-template ngbNavContent>
                <!-- Final Sanction Content -->
                <div class="card border-0">
                  <div>
                    <div class="sanction-content">
                      <form class="forms-sample">
                        <div class="row">
                          <!-- Final Sanction Status -->
                          <div class="col-12 mb-4">
                            <div class="row">
                              <div class="col-12 col-md-6 col-lg-3 mb-3">
                                <label class="form-label">Final Sanction Received</label>
                                <select class="form-select">
                                  <option value="" selected disabled>Select</option>
                                  <option value="Yes">Yes</option>
                                  <option value="No">No</option>
                                </select>
                              </div>
                            </div>
                          </div>

                          <!-- Form Buttons -->
                          <div class="col-12 text-end">
                            <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                            <button type="submit" class="btn btn-primary">Save & Next</button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
          </ul>

          <!-- Tab Content -->
          <div [ngbNavOutlet]="loginInstituteNav" class="tab-content"></div>
        </div>
      </div>
    </div>
  </div>