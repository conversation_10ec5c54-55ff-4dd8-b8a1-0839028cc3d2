<!-- Breadcrumb Navigation -->


<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">View Employee</h6>
        </div>

        <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>



        <!-- Controls row (similar to roles) -->
        <div class="row mb-3">
          <div class="col-md-6 col-lg-4 mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Search:</span>
              <input
                type="text"
                class="form-control form-control-sm"
                [formControl]="searchTerm"
                placeholder="Search employees..."
              >
            </div>
          </div>

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="loadEmployees()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>



        <!-- Loading indicator (similar to roles) -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Employee Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">Actions</th>
                <th scope="col" sortable="employeeCode" (sort)="onSort($event)">Employee Code</th>
                <th scope="col" sortable="name" (sort)="onSort($event)">Name</th>
                <th scope="col" sortable="position" (sort)="onSort($event)">Position</th>
                <th scope="col" sortable="department" (sort)="onSort($event)">Department</th>
                <th scope="col" sortable="email" (sort)="onSort($event)">Email</th>
                <th scope="col" sortable="phone" (sort)="onSort($event)">Phone</th>
                <th scope="col" sortable="joinDate" (sort)="onSort($event)">Join Date</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let employee of paginatedEmployees">
                <td class="action-icons">
                  <a *ngIf="canEditEmployee()" href="javascript:;" class="action-icon" ngbTooltip="Edit" (click)="editEmployee(employee, editEmployeeModal)">
                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                  </a>
                  <span *ngIf="!canEditEmployee()" class="text-muted" ngbTooltip="No permission to edit employees">
                    <i data-feather="edit" class="icon-sm text-muted" appFeatherIcon></i>
                  </span>
                </td>
                <td>{{ employee.employeeCode }}</td>
                <td>{{ employee.name }}</td>
                <td>{{ employee.position }}</td>
                <td>{{ employee.department }}</td>
                <td>{{ employee.email }}</td>
                <td>{{ employee.phone }}</td>
                <td>{{ employee.joinDate | date:'dd-MM-yyyy' }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty state message -->
        <div class="empty-state text-center py-5" *ngIf="!loading && paginatedEmployees.length === 0">
          <i data-feather="users" class="icon-lg mb-3" appFeatherIcon></i>
          <p class="mb-0">{{ searchTerm.value ? 'No employees found matching your search' : 'No employees found' }}</p>
          <button *ngIf="searchTerm.value" class="btn btn-outline-primary btn-sm mt-2" (click)="resetFilter()">
            Clear Search
          </button>
        </div>

        <!-- Pagination and Info -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!loading && totalItems > 0">
          <div class="d-flex align-items-center gap-3">
            <span class="text-muted">
              Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems) }} of {{ totalItems }} entries
              <span *ngIf="totalItems !== totalEmployees" class="text-info">(filtered from {{ totalEmployees }} total)</span>
            </span>
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChange($event)" style="width: auto;">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
          <ngb-pagination
            [collectionSize]="totalItems"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            (pageChange)="onPageChange($event)"
            class="pagination-sm"
          ></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Employee Modal -->
<ng-template #editEmployeeModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Edit Employee</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="editEmployeeForm" (ngSubmit)="saveEmployeeChanges()">

      <!-- Personal Information Section -->
      <div class="section-title">Personal Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="employeeCode" class="form-label">Employee Code <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="employeeCode" formControlName="employeeCode" readonly>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="firstName" formControlName="firstName">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="middleName" class="form-label">Middle Name</label>
          <input type="text" class="form-control" id="middleName" formControlName="middleName">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control" id="lastName" formControlName="lastName">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="bloodGroup" class="form-label">Blood Group</label>
          <select class="form-select" id="bloodGroup" formControlName="bloodGroup">
            <option value="">Select Blood Group</option>
            <option value="A+">A+</option>
            <option value="A-">A-</option>
            <option value="B+">B+</option>
            <option value="B-">B-</option>
            <option value="AB+">AB+</option>
            <option value="AB-">AB-</option>
            <option value="O+">O+</option>
            <option value="O-">O-</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="dateOfBirth" class="form-label">Date of Birth</label>
          <input type="date" class="form-control" id="dateOfBirth" formControlName="dateOfBirth">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="joiningDate" class="form-label">Joining Date</label>
          <input type="date" class="form-control" id="joiningDate" formControlName="joiningDate">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="reportingDate" class="form-label">Reporting Date</label>
          <input type="date" class="form-control" id="reportingDate" formControlName="reportingDate">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="gender" class="form-label">Gender</label>
          <select class="form-select" id="gender" formControlName="gender">
            <option value="">Select Gender</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="maritalStatus" class="form-label">Marital Status</label>
          <select class="form-select" id="maritalStatus" formControlName="maritalStatus">
            <option value="">Select Status</option>
            <option value="Single">Single</option>
            <option value="Married">Married</option>
               <option value="Unmarried">Unmarried</option>
            <option value="Divorced">Divorced</option>
          </select>
        </div>
      </div>

      <!-- Contact Information Section -->
      <div class="section-title">Contact Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="personalEmail" class="form-label">Personal Email</label>
          <input type="email" class="form-control" id="personalEmail" formControlName="personalEmail">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="officeEmail" class="form-label">Office Email</label>
          <input type="email" class="form-control" id="officeEmail" formControlName="officeEmail">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="phoneNo" class="form-label">Phone No</label>
          <input type="tel" class="form-control" id="phoneNo" formControlName="phoneNo">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="alternetNo" class="form-label">Alternate No</label>
          <input type="tel" class="form-control" id="alternetNo" formControlName="alternetNo">
        </div>
        <div class="col-12 mb-3">
          <label for="address" class="form-label">Address</label>
          <textarea class="form-control" id="address" formControlName="address" rows="3"></textarea>
        </div>
      </div>

      <!-- Government IDs Section -->
      <div class="section-title">Government IDs</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="panNo" class="form-label">PAN No</label>
          <input type="text" class="form-control" id="panNo" formControlName="panNo">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="aadharNo" class="form-label">Aadhar No</label>
          <input type="text" class="form-control" id="aadharNo" formControlName="aadharNo">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="uanNo" class="form-label">UAN No</label>
          <input type="text" class="form-control" id="uanNo" formControlName="uanNo">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="esicNo" class="form-label">ESIC No</label>
          <input type="text" class="form-control" id="esicNo" formControlName="esicNo">
        </div>
      </div>

      <!-- Financial Information Section -->
      <div class="section-title">Financial Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="ctc" class="form-label">CTC</label>
          <input type="number" class="form-control" id="ctc" formControlName="ctc">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="attendanceBonus" class="form-label">Attendance Bonus</label>
          <input type="number" class="form-control" id="attendanceBonus" formControlName="attendanceBonus">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="pf" class="form-label">PF</label>
          <input type="text" class="form-control" id="pf" formControlName="pf">
        </div>
      </div>

      <!-- Banking Information Section -->
      <div class="section-title">Banking Information</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="bankName" class="form-label">Bank Name</label>
          <input type="text" class="form-control" id="bankName" formControlName="bankName">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="bankAccountNo" class="form-label">Bank Account No</label>
          <input type="text" class="form-control" id="bankAccountNo" formControlName="bankAccountNo">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="ifscNo" class="form-label">IFSC No</label>
          <input type="text" class="form-control" id="ifscNo" formControlName="ifscNo">
        </div>
      </div>

      <!-- Employment Details Section -->
      <div class="section-title">Employment Details</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="officeLocation" class="form-label">Office Location</label>
          <input type="text" class="form-control" id="officeLocation" formControlName="officeLocation">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="shiftTime" class="form-label">Shift Time</label>
          <input type="text" class="form-control" id="shiftTime" formControlName="shiftTime">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="department" class="form-label">Department</label>
          <select class="form-select" id="department" formControlName="department" (change)="onDepartmentChange()">
            <option value="">Not Assigned</option>
            <option *ngFor="let department of departmentOptions" [value]="department.id">{{department.name}}</option>
          </select>
          <!-- <small class="text-muted">Available options: {{departmentOptions.length}} | Form value: {{editEmployeeForm.get('department')?.value}}</small> -->
          <div class="mt-1" *ngIf="departmentOptions.length === 0">
            <small class="text-warning">Loading departments...</small>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="designation" class="form-label">Designation</label>
          <select class="form-select" id="designation" formControlName="designation">
            <option value="">Not Assigned</option>
            <option *ngFor="let designation of designationOptions" [value]="designation.id">{{designation.name}}</option>
          </select>
          <!-- <small class="text-muted">Available options: {{designationOptions.length}} | Form value: {{editEmployeeForm.get('designation')?.value}}</small> -->
          <div class="mt-1" *ngIf="designationOptions.length === 0">
            <button type="button" class="btn btn-sm btn-outline-primary" (click)="loadDesignations()">
              Load Designations
            </button>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="role" class="form-label">Role</label>
          <select class="form-select" id="role" formControlName="role">
          
            <option *ngFor="let role of roleOptions" [value]="role">{{role}}</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="subRole" class="form-label">Sub Role</label>
          <select class="form-select" id="subRole" formControlName="subRole">
            <option value="">Not Assigned</option>
            <option *ngFor="let subRole of subRoleOptions" [value]="subRole.id">{{subRole.name}}</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="isActive" class="form-label">Is Active</label>
          <select class="form-select" id="isActive" formControlName="isActive">
            <option [value]="true">Yes</option>
            <option [value]="false">No</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="approverCode" class="form-label">Approver Code</label>
          <select class="form-select" id="approverCode" formControlName="approverCode">
            <option value="">Not Assigned</option>
            <option *ngFor="let approver of filteredApproverOptions" [value]="approver.employee_code">
              {{approver.name}} ({{approver.employee_code}})
            </option>
          </select>
          <div class="mt-1" *ngIf="loadingEmployees">
            <small class="text-warning">Loading employees...</small>
          </div>
          <div class="mt-1" *ngIf="!loadingEmployees && filteredApproverOptions.length === 0 && editEmployeeForm.get('department')?.value">
            <small class="text-info">No employees found in selected department</small>
          </div>
          <div class="mt-1" *ngIf="!loadingEmployees && !editEmployeeForm.get('department')?.value">
            <small class="text-muted">Select a department first</small>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="secondApproverCode" class="form-label">Second Approver Code</label>
          <select class="form-select" id="secondApproverCode" formControlName="secondApproverCode">
            <option value="">Not Assigned</option>
            <option *ngFor="let approver of filteredSecondApproverOptions" [value]="approver.employee_code">
              {{approver.name}} ({{approver.employee_code}})
            </option>
          </select>
          <div class="mt-1" *ngIf="loadingEmployees">
            <small class="text-warning">Loading employees...</small>
          </div>
          <div class="mt-1" *ngIf="!loadingEmployees && filteredSecondApproverOptions.length === 0 && editEmployeeForm.get('department')?.value">
            <small class="text-info">No employees found in selected department</small>
          </div>
          <div class="mt-1" *ngIf="!loadingEmployees && !editEmployeeForm.get('department')?.value">
            <small class="text-muted">Select a department first</small>
          </div>
        </div>
      </div>

      <!-- Resignation Details Section -->
      <div class="section-title">Resignation Details</div>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="resignedStaredDate" class="form-label">Resigned Start Date</label>
          <input type="date" class="form-control" id="resignedStaredDate" formControlName="resignedStaredDate">
        </div>
        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="resignedEndDate" class="form-label">Resigned End Date</label>
          <input type="date" class="form-control" id="resignedEndDate" formControlName="resignedEndDate">
        </div>
      </div>

    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()" [disabled]="saving">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="saveEmployeeChanges()" [disabled]="saving">
      <span *ngIf="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ saving ? 'Saving...' : 'Save Changes' }}
    </button>
  </div>
</ng-template>