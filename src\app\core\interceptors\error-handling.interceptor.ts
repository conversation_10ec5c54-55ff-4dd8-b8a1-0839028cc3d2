import { Injectable } from '@angular/core';
import { Http<PERSON>e<PERSON>, HttpHandler, HttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import Swal from 'sweetalert2';
import { ErrorMessageService, ErrorMapping } from '../services/error-message.service';

@Injectable()
export class ErrorHandlingInterceptor implements HttpInterceptor {

  constructor(private errorMessageService: ErrorMessageService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    console.log('🚀 ErrorHandlingInterceptor: Processing request to:', request.url);

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        console.log('🔴 ErrorHandlingInterceptor: CAUGHT ERROR!');
        console.log('📊 Error Details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message,
          error: error.error
        });

        // Check if component wants to handle error manually
        const skipInterceptor = request.headers.get('X-Skip-Error-Interceptor') === 'true';

        // Skip auth endpoints and manual handling requests
        const isAuthEndpoint = request.url.includes('/auth/login') ||
                              request.url.includes('/auth/register') ||
                              request.url.includes('/auth/refresh-token');

        // Special handling for 401 errors - these are handled by AuthInterceptor
        const is401Error = error.status === 401;

        if (!isAuthEndpoint && !skipInterceptor && !is401Error) {
          console.log('✅ ErrorHandlingInterceptor: Handling error for non-auth endpoint');
          this.handleError(error, request.url);
        } else {
          console.log('⏭️ ErrorHandlingInterceptor: Skipping error handling - auth endpoint, 401 error, or manual handling requested');
        }

        // Always re-throw the error so components can still handle it if needed
        return throwError(() => error);
      })
    );
  }

  private handleError(error: HttpErrorResponse, url: string): void {
    console.error('ErrorHandlingInterceptor: Handling error for URL:', url);
    console.error('ErrorHandlingInterceptor: Full error object:', error);

    // Get user-friendly error message
    const errorMapping: ErrorMapping = this.errorMessageService.getUserFriendlyError(error);
    console.log('ErrorHandlingInterceptor: Generated error mapping:', errorMapping);

    // Show user-friendly error notification
    this.showErrorNotification(errorMapping);
  }

  private showErrorNotification(errorMapping: ErrorMapping): void {
    console.log('🚨 ErrorHandlingInterceptor: Showing notification:', errorMapping);

    // Skip showing notification if message or title is empty (disabled errors)
    if (!errorMapping.message || !errorMapping.title ||
        errorMapping.message.trim() === '' || errorMapping.title.trim() === '') {
      console.log('⏭️ ErrorHandlingInterceptor: Skipping notification - empty message or title');
      return;
    }

    // Determine SweetAlert2 icon based on error type
    let icon: 'error' | 'warning' | 'info' | 'success' = 'error';
    let confirmButtonColor = '#d33';

    if (errorMapping.type === 'warning') {
      icon = 'warning';
      confirmButtonColor = '#f39c12';
    } else if (errorMapping.type === 'info') {
      icon = 'info';
      confirmButtonColor = '#3085d6';
    }

    // Show the notification with enhanced styling
    Swal.fire({
      icon: icon,
      title: errorMapping.title || 'Error',
      text: errorMapping.message,
      confirmButtonText: 'OK',
      confirmButtonColor: confirmButtonColor,
      allowOutsideClick: true,
      allowEscapeKey: true,
      backdrop: true,
      heightAuto: false,
      customClass: {
        popup: 'error-popup',
        title: 'error-title'
      },
      showClass: {
        popup: 'animate__animated animate__fadeInDown animate__faster'
      },
      hideClass: {
        popup: 'animate__animated animate__fadeOutUp animate__faster'
      }
    }).then((result: any) => {
      console.log('✅ ErrorHandlingInterceptor: Error notification closed', result);
    }).catch((error: any) => {
      console.error('❌ ErrorHandlingInterceptor: Error showing notification:', error);
    });
  }
}
