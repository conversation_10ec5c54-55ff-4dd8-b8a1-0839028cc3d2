export interface ConnectWith {
  id?: number;
  name: string;
  description: string;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string | null;
  _originalId?: string;
}

export interface ConnectWithResponse {
  items: ConnectWith[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ConnectWithCreate {
  name: string;
  description: string;
}

export interface ConnectWithUpdate {
  name?: string;
  description?: string;
}

export interface ConnectWithFilter {
  name?: string;
  description?: string;
  search?: string;
}
