<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Debt Details</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #debtDetailsForm="ngForm">
    <div class="row mb-3">
      <!-- Basic Information -->
      <div class="col-12 mb-3">
        <h6 class="section-title">Basic Information</h6>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="borrowerName" class="form-label"><PERSON><PERSON><PERSON>'s Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="borrowerName" name="borrowerName" [(ngModel)]="formData.borrowerName" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="entityType" class="form-label">Entity / Individual <span class="text-danger">*</span></label>
        <select class="form-select" id="entityType" name="entityType" [(ngModel)]="formData.entityType" required>
          <option value="">Select Type</option>
          <option *ngFor="let type of entityTypes" [value]="type.value">{{ type.label }}</option>
        </select>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="nameOfLender" class="form-label">Name of Lender <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="nameOfLender" name="nameOfLender" [(ngModel)]="formData.nameOfLender" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="typeOfFacility" class="form-label">Type of Facility <span class="text-danger">*</span></label>
        <select class="form-select" id="typeOfFacility" name="typeOfFacility" [(ngModel)]="formData.typeOfFacility" required>
          <option value="">Select Type</option>
          <option *ngFor="let type of facilityTypes" [value]="type.value">{{ type.label }}</option>
        </select>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="loanAccountNo" class="form-label">Loan Account No <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="loanAccountNo" name="loanAccountNo" [(ngModel)]="formData.loanAccountNo" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="individualGuarantee" class="form-label">Individual Guarantee <span class="text-danger">*</span></label>
        <select class="form-select" id="individualGuarantee" name="individualGuarantee" [(ngModel)]="formData.individualGuarantee" required>
          <option value="">Select</option>
          <option *ngFor="let option of yesNoOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="dateOfSanction" class="form-label">Date of Sanction <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="dateOfSanction" name="dateOfSanction" [(ngModel)]="formData.dateOfSanction" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="dateOfDisbursement" class="form-label">Date of Disbursement <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="dateOfDisbursement" name="dateOfDisbursement" [(ngModel)]="formData.dateOfDisbursement" required>
      </div>

      <!-- Financial Information -->
      <div class="col-12 mt-3 mb-3">
        <h6 class="section-title">Financial Information</h6>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="sanctionedAmount" class="form-label">Sanctioned Amount (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="sanctionedAmount" name="sanctionedAmount" [(ngModel)]="formData.sanctionedAmount" required min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="disbursedAmount" class="form-label">Disbursed Amount (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="disbursedAmount" name="disbursedAmount" [(ngModel)]="formData.disbursedAmount" required min="0" (change)="calculateCurrentOutstanding()">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="utilized" class="form-label">Utilized (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="utilized" name="utilized" [(ngModel)]="formData.utilized" required min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="roa" class="form-label">ROA (%) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="roa" name="roa" [(ngModel)]="formData.roa" required min="0" step="0.01">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="repaid" class="form-label">Repaid (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="repaid" name="repaid" [(ngModel)]="formData.repaid" required min="0" (change)="calculateCurrentOutstanding()">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="emiAmount" class="form-label">EMI Amount (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="emiAmount" name="emiAmount" [(ngModel)]="formData.emiAmount" required min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="repaymentBankAccountNo" class="form-label">Repayment Bank Account No</label>
        <input type="text" class="form-control" id="repaymentBankAccountNo" name="repaymentBankAccountNo" [(ngModel)]="formData.repaymentBankAccountNo">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="totalEmi" class="form-label">Total EMI <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="totalEmi" name="totalEmi" [(ngModel)]="formData.totalEmi" required min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="emiPaid" class="form-label">EMI Paid <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="emiPaid" name="emiPaid" [(ngModel)]="formData.emiPaid" required min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="originFees" class="form-label">Origin Fees (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="originFees" name="originFees" [(ngModel)]="formData.originFees" required min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="currentOutstanding" class="form-label">Current Outstanding (₹)</label>
        <input type="text" class="form-control" id="currentOutstanding" name="currentOutstanding" [value]="formatNumber(formData.currentOutstanding)" readonly>
        <small class="form-text text-muted">Calculated automatically</small>
      </div>

      <!-- Loan Terms -->
      <div class="col-12 mt-3 mb-3">
        <h6 class="section-title">Loan Terms & Security</h6>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="loanTenor" class="form-label">Loan Tenor <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="loanTenor" name="loanTenor" [(ngModel)]="formData.loanTenor" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="moratorium" class="form-label">Moratorium <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="moratorium" name="moratorium" [(ngModel)]="formData.moratorium" required>
      </div>

      <div class="col-12 col-md-6 col-lg-6 mb-3">
        <label for="detailsOfSecurityCreated" class="form-label">Details of Security Created <span class="text-danger">*</span></label>
        <textarea class="form-control" id="detailsOfSecurityCreated" name="detailsOfSecurityCreated" [(ngModel)]="formData.detailsOfSecurityCreated" rows="2" required></textarea>
      </div>

      <!-- Default Information -->
      <div class="col-12 mt-3 mb-3">
        <h6 class="section-title">Default Information</h6>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="overdueAmount" class="form-label">Overdue Amount (₹)</label>
        <input type="number" class="form-control" id="overdueAmount" name="overdueAmount" [(ngModel)]="formData.overdueAmount" min="0">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="detailsOfDefault" class="form-label">Details of Default (if any)</label>
        <textarea class="form-control" id="detailsOfDefault" name="detailsOfDefault" [(ngModel)]="formData.detailsOfDefault" rows="2"></textarea>
      </div>

      <div class="col-12 col-md-6 col-lg-5 mb-3">
        <label for="remarksForDelay" class="form-label">Remarks for Delay (if any)</label>
        <textarea class="form-control" id="remarksForDelay" name="remarksForDelay" [(ngModel)]="formData.remarksForDelay" rows="2"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="debtDetailsForm.invalid" (click)="saveChanges()">Save</button>
</div>
