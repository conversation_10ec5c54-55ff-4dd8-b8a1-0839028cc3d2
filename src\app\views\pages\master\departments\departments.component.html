<!-- Departments Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-users me-2"></i>
              Departments Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_departments }} total departments, 
              {{ statistics.active_departments }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Department
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'list'" 
                    (click)="setViewMode('list')">
              <i class="feather icon-list me-1"></i>
              List View
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'tree'" 
                    (click)="setViewMode('tree')">
              <i class="feather icon-git-branch me-1"></i>
              Tree View
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode === 'list'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-4">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search departments..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-3">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select" [(ngModel)]="selectedParent" (change)="onParentFilter()">
                <option value="">All Departments</option>
                <option *ngFor="let dept of departments" [value]="dept.id">
                  {{ getLevelIndicator(dept.level) }} {{ dept.name }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <button class="btn btn-outline-danger w-100" 
                      [disabled]="selectedDepartments.size === 0"
                      (click)="bulkDelete()">
                <i class="feather icon-trash-2 me-1"></i>
                Delete Selected
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading departments...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40">
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Department Name</th>
                  <th>Parent Department</th>
                  <th>Level</th>
                  <th>Employees</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let department of departments; trackBy: trackByDepartmentId">
                  <td>
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedDepartments.has(department.id)"
                           (change)="toggleSelection(department.id)">
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <span class="me-2">{{ getLevelIndicator(department.level) }}</span>
                      <div>
                        <strong>{{ department.name }}</strong>
                        <small class="d-block text-muted" *ngIf="department.description">
                          {{ department.description }}
                        </small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span *ngIf="department.parent_name" class="text-muted">
                      {{ department.parent_name }}
                    </span>
                    <span *ngIf="!department.parent_name" class="text-muted fst-italic">
                      Root Department
                    </span>
                  </td>
                  <td>
                    <span class="badge bg-info">Level {{ department.level }}</span>
                  </td>
                  <td>
                    <span class="badge bg-light text-dark">
                      {{ department.employee_count || 0 }} employees
                    </span>
                  </td>
                  <td>
                    <span [class]="getStatusBadgeClass(department.is_active)">
                      {{ getStatusText(department.is_active) }}
                    </span>
                  </td>
                  <td>
                    <small class="text-muted">
                      {{ department.created_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li>
                          <button class="dropdown-item" (click)="viewHierarchy(department)">
                            <i class="feather icon-git-branch me-2"></i>
                            View Hierarchy
                          </button>
                        </li>
                        <li>
                          <button class="dropdown-item" (click)="openEditModal(department)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                          <button class="dropdown-item text-danger" (click)="deleteDepartment(department)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="departments.length === 0" class="text-center py-5">
              <i class="feather icon-users text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">No Departments Found</h5>
              <p class="text-muted">
                {{ searchTerm ? 'No departments match your search criteria.' : 'Get started by creating your first department.' }}
              </p>
              <button *ngIf="!searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Department
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} departments
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Tree View -->
        <div *ngIf="viewMode === 'tree'">
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading tree...</span>
            </div>
            <p class="mt-2 text-muted">Loading department tree...</p>
          </div>

          <div *ngIf="!loading && departmentTree.length > 0" class="department-tree">
            <!-- Tree structure will be implemented in a separate component -->
            <p class="text-muted">Tree view implementation coming soon...</p>
          </div>

          <div *ngIf="!loading && departmentTree.length === 0" class="text-center py-5">
            <i class="feather icon-git-branch text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">No Department Tree</h5>
            <p class="text-muted">Create departments to see the organizational tree structure.</p>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_departments }}</h3>
                      <p class="mb-0">Total Departments</p>
                    </div>
                    <i class="feather icon-users" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_departments }}</h3>
                      <p class="mb-0">Active Departments</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_departments }}</h3>
                      <p class="mb-0">Inactive Departments</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.max_depth }}</h3>
                      <p class="mb-0">Max Depth</p>
                    </div>
                    <i class="feather icon-layers" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Departments by Level Chart -->
          <div *ngIf="statistics.departments_by_level" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Departments by Level</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let level of getObjectKeys(statistics.departments_by_level)" 
                     class="col-md-2 mb-3">
                  <div class="text-center">
                    <div class="badge bg-light text-dark p-3 w-100">
                      <div class="h4 mb-1">{{ statistics.departments_by_level[level] }}</div>
                      <small>Level {{ level }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Top Departments by Employees -->
          <div *ngIf="statistics.top_departments_by_employees?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Top Departments by Employee Count</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Department</th>
                      <th>Level</th>
                      <th>Employee Count</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let dept of statistics.top_departments_by_employees">
                      <td>{{ dept.name }}</td>
                      <td>
                        <span class="badge bg-info">Level {{ dept.level }}</span>
                      </td>
                      <td>
                        <span class="badge bg-light text-dark">{{ dept.employee_count || 0 }}</span>
                      </td>
                      <td>
                        <span [class]="getStatusBadgeClass(dept.is_active)">
                          {{ getStatusText(dept.is_active) }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
