import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class CompoffRequestGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): boolean {

    console.log('🛡️ COMP-OFF REQUEST GUARD - Checking access for Comp-off Request');
    console.log('🔑 Required permission: leave:create');

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ COMP-OFF REQUEST GUARD - No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check permission directly from AuthService
    console.log('🔍 COMP-OFF REQUEST GUARD - Checking permission');
    console.log('👤 User role:', currentUser?.role);
    console.log('🔑 User permissions:', currentUser?.permissions);

    // Check for leave:request_compoff permission using AuthService
    const hasPermission = this.authService.hasPermission('leave:request_compoff');

    if (!hasPermission) {
      console.log('❌ COMP-OFF REQUEST GUARD - Permission DENIED');
      console.log('🔑 Required: leave:request_compoff');
      console.log('👤 User has:', currentUser?.permissions);
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log('✅ COMP-OFF REQUEST GUARD - Permission GRANTED');
    console.log('🎯 User can access Comp-off Request functionality');
    return true;
  }
}
