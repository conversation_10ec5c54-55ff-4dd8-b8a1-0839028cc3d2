import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {

    // Get required permissions from route data (STRICT PERMISSION-BASED ONLY)
    const requiredPermission = route.data['permission'] as string;

    console.log('🛡️ PermissionGuard checking access for:', state.url);
    console.log('🔑 Required permission:', requiredPermission);

    // STRICT PERMISSION CHECK - No fallback mechanism
    if (!requiredPermission) {
      console.log('✅ No permission requirement, allowing access to authenticated users');
      return true;
    }

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check permission directly using AuthService
    const hasPermission = this.authService.hasPermission(requiredPermission);

    if (!hasPermission) {
      console.log(`❌ STRICT Permission denied. Required: ${requiredPermission}`);
      console.log(`👤 User permissions:`, currentUser.permissions);
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log(`✅ STRICT Permission granted: ${requiredPermission}`);
    console.log('✅ STRICT PERMISSION-BASED ACCESS GRANTED for:', state.url);
    return true;
  }

}
