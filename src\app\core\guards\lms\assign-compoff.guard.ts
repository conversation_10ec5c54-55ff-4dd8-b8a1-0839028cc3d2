import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AssignCompoffGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): boolean {

    console.log('🛡️ ASSIGN COMP-OFF GUARD - Checking access for Assign Comp-off');
    console.log('🔑 Required permission: leave:assign_compoff');

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ ASSIGN COMP-OFF GUARD - No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check permission directly from AuthService
    console.log('🔍 ASSIGN COMP-OFF GUARD - Checking permission');
    console.log('👤 User role:', currentUser?.role);
    console.log('🔑 User permissions:', currentUser?.permissions);

    // Check for leave:assign_compoff permission using AuthService
    const hasPermission = this.authService.hasPermission('leave:assign_compoff');

    if (!hasPermission) {
      console.log('❌ ASSIGN COMP-OFF GUARD - Permission DENIED');
      console.log('🔑 Required: leave:assign_compoff');
      console.log('👤 User has:', currentUser?.permissions);
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log('✅ ASSIGN COMP-OFF GUARD - Permission GRANTED');
    console.log('🎯 User can access Assign Comp-off functionality');
    return true;
  }
}
