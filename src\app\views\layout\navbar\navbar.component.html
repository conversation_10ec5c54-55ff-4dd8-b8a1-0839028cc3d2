<nav class="navbar">
  <div class="navbar-content">

    <div class="logo-mini-wrapper">
      <img src="images/brand-logo/brand-color.png"class="logo-mini logo-mini-light" alt="logo">
      <img src="images/brand-logo/brand-white.png" class="logo-mini logo-mini-dark" alt="logo">
    </div>

    <!-- <form class="search-form">
      <div class="input-group">
        <div class="input-group-text">
          <i class="feather icon-search"></i>
        </div>
        <input type="text" class="form-control" id="navbarForm" placeholder="Search here...">
      </div>
    </form> -->

    <ul class="navbar-nav">
      <li class="session-timer-wrapper nav-item">
        <app-session-timer></app-session-timer>
      </li>
      <li class="theme-switcher-wrapper nav-item">
        <input type="checkbox" (change)="onThemeCheckboxChange($event)" value="" id="theme-switcher">
        <label for="theme-switcher">
          <div class="box">
            <div class="ball"></div>
            <div class="icons">
              <i class="feather icon-sun"></i>
              <i class="feather icon-moon"></i>
            </div>
          </div>
        </label>
      </li>
      <!-- <li class="nav-item" ngbDropdown>
        <a class="nav-link" ngbDropdownToggle id="languageDropdown" role="button">
          <img src="images/flags/us.svg" class="w-20px" title="us" alt="us">
          <span class="ms-2 d-none d-md-inline-block">English</span>
        </a>
        <div ngbDropdownMenu aria-labelledby="languageDropdown">
          <a ngbDropdownItem class="py-2 d-flex"> <img src="images/flags/us.svg" class="w-20px me-1" title="us" alt="us"> <span class="ms-2"> English </span></a>
          <a ngbDropdownItem class="py-2 d-flex"> <img src="images/flags/fr.svg" class="w-20px me-1" title="fr" alt="fr"> <span class="ms-2"> French </span></a>
          <a ngbDropdownItem class="py-2 d-flex"> <img src="images/flags/de.svg" class="w-20px me-1" title="de" alt="de"> <span class="ms-2"> German </span></a>
          <a ngbDropdownItem class="py-2 d-flex"> <img src="images/flags/pt.svg" class="w-20px me-1" title="pt" alt="pt"> <span class="ms-2"> Portuguese </span></a>
          <a ngbDropdownItem class="py-2 d-flex"> <img src="images/flags/es.svg" class="w-20px me-1" title="es" alt="es"> <span class="ms-2"> Spanish </span></a>
        </div>
      </li> -->
      <!-- <li class="nav-item nav-apps" ngbDropdown>
        <a class="nav-link" ngbDropdownToggle id="appsDropdown">
          <i class="link-icon feather icon-grid"></i>
        </a>
        <div ngbDropdownMenu class="px-0" aria-labelledby="appsDropdown">
          <div class="px-3 py-2 d-flex align-items-center justify-content-between border-bottom">
            <p class="mb-0 fw-bold">Web Apps</p>
            <a href="javascript:;" class="text-secondary">Edit</a>
          </div>
          <div class="row g-0 p-1">
            <div class="col-3 text-center">
              <a routerLink="/apps/chat" class="dropdown-item d-flex flex-column align-items-center justify-content-center w-70px h-70px"><i class="feather icon-message-square icon-lg mb-1"></i><p class="fs-12px">Chat</p></a>
            </div>
            <div class="col-3 text-center">
              <a routerLink="/apps/calendar" class="dropdown-item d-flex flex-column align-items-center justify-content-center w-70px h-70px"><i class="feather icon-calendar icon-lg mb-1"></i><p class="fs-12px">Calendar</p></a>
            </div>
            <div class="col-3 text-center">
              <a routerLink="/apps/email/inbox" class="dropdown-item d-flex flex-column align-items-center justify-content-center w-70px h-70px"><i class="feather icon-mail icon-lg mb-1"></i><p class="fs-12px">Email</p></a>
            </div>
            <div class="col-3 text-center">
              <a routerLink="/general/profile" class="dropdown-item d-flex flex-column align-items-center justify-content-center w-70px h-70px"><i class="feather icon-instagram icon-lg mb-1"></i><p class="fs-12px">Profile</p></a>
            </div>
          </div>
          <div class="px-3 py-2 d-flex align-items-center justify-content-center border-top">
            <a href="" (click)="false">View all</a>
          </div>
        </div>
      </li> -->
      <li class="nav-item nav-messages" ngbDropdown>
        <a class="nav-link" ngbDropdownToggle id="messageDropdown">
          <i class="link-icon feather icon-mail"></i>
        </a>
        <div ngbDropdownMenu class="px-0" aria-labelledby="messageDropdown">
          <div class="px-3 py-2 d-flex align-items-center justify-content-between border-bottom">
            <p>9 New Messages</p>
            <a href="" (click)="false" class="text-secondary">Clear all</a>
          </div>
          <div class="p-1">
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="me-3">
                <img class="w-30px h-30px rounded-circle" src="https://placehold.co/30x30" alt="user">
              </div>
              <div class="d-flex justify-content-between flex-grow-1">
                <div class="me-4">
                  <p>Leonardo Payne</p>
                  <p class="fs-12px text-secondary">Project status</p>
                </div>
                <p class="fs-12px text-secondary">2 min ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="me-3">
                <img class="w-30px h-30px rounded-circle" src="https://placehold.co/30x30" alt="user">
              </div>
              <div class="d-flex justify-content-between flex-grow-1">
                <div class="me-4">
                  <p>Carl Henson</p>
                  <p class="fs-12px text-secondary">Client meeting</p>
                </div>
                <p class="fs-12px text-secondary">30 min ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="me-3">
                <img class="w-30px h-30px rounded-circle" src="https://placehold.co/30x30" alt="user">
              </div>
              <div class="d-flex justify-content-between flex-grow-1">
                <div class="me-4">
                  <p>Jensen Combs</p>
                  <p class="fs-12px text-secondary">Project updates</p>
                </div>
                <p class="fs-12px text-secondary">1 hrs ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="me-3">
                <img class="w-30px h-30px rounded-circle" src="https://placehold.co/30x30" alt="user">
              </div>
              <div class="d-flex justify-content-between flex-grow-1">
                <div class="me-4">
                  <p>Amiah Burton</p>
                  <p class="fs-12px text-secondary">Project deatline</p>
                </div>
                <p class="fs-12px text-secondary">2 hrs ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="me-3">
                <img class="w-30px h-30px rounded-circle" src="https://placehold.co/30x30" alt="user">
              </div>
              <div class="d-flex justify-content-between flex-grow-1">
                <div class="me-4">
                  <p>Yaretzi Mayo</p>
                  <p class="fs-12px text-secondary">New record</p>
                </div>
                <p class="fs-12px text-secondary">5 hrs ago</p>
              </div>
            </a>
          </div>
          <div class="px-3 py-2 d-flex align-items-center justify-content-center border-top">
            <a href="" (click)="false">View all</a>
          </div>
        </div>
      </li>
      <li class="nav-item nav-notifications" ngbDropdown>
        <a class="nav-link" ngbDropdownToggle id="notificationDropdown">
          <i class="link-icon feather icon-bell"></i>
          <div class="indicator">
            <div class="circle"></div>
          </div>
        </a>
        <div ngbDropdownMenu class="px-0" aria-labelledby="notificationDropdown">
          <div class="px-3 py-2 d-flex align-items-center justify-content-between border-bottom">
            <p>6 New Notifications</p>
            <a href="" (click)="false" class="text-secondary">Clear all</a>
          </div>
          <div class="p-1">
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="w-30px h-30px d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                <i class="feather icon-gift icon-sm text-white"></i>
              </div>
              <div class="flex-grow-1 me-2">
                <p>New Order Recieved</p>
                <p class="fs-12px text-secondary">30 min ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="w-30px h-30px d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                <i class="feather icon-alert-circle icon-sm text-white"></i>
              </div>
              <div class="flex-grow-1 me-2">
                <p>Server Limit Reached!</p>
                <p class="fs-12px text-secondary">1 hrs ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="w-30px h-30px d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                <img class="w-30px h-30px rounded-circle" src="https://placehold.co/30x30" alt="userr">
              </div>
              <div class="flex-grow-1 me-2">
                <p>New customer registered</p>
                <p class="fs-12px text-secondary">2 sec ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="w-30px h-30px d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                <i class="feather icon-layers icon-sm text-white"></i>
              </div>
              <div class="flex-grow-1 me-2">
                <p>Apps are ready for update</p>
                <p class="fs-12px text-secondary">5 hrs ago</p>
              </div>
            </a>
            <a href="" (click)="false" class="dropdown-item d-flex align-items-center py-2">
              <div class="w-30px h-30px d-flex align-items-center justify-content-center bg-primary rounded-circle me-3">
                <i class="feather icon-download icon-sm text-white"></i>
              </div>
              <div class="flex-grow-1 me-2">
                <p>Download completed</p>
                <p class="fs-12px text-secondary">6 hrs ago</p>
              </div>
            </a>
          </div>
          <div class="px-3 py-2 d-flex align-items-center justify-content-center border-top">
            <a href="" (click)="false">View all</a>
          </div>
        </div>
      </li>
      <li class="nav-item nav-profile" ngbDropdown>
        <a class="nav-link" ngbDropdownToggle id="profileDropdown">

          <div *ngIf="userFirstName || userLastName" class="avatar-wrapper w-30px h-30px ms-1 rounded-circle d-flex align-items-center justify-content-center bg-primary text-white">
            {{ getUserInitials() }}
          </div>
          <img *ngIf="!userFirstName && !userLastName" class="w-30px h-30px ms-1 rounded-circle" src="https://placehold.co/30x30" alt="profile">
        </a>
        <div ngbDropdownMenu class="px-0" aria-labelledby="profileDropdown">
          <div class="d-flex flex-column align-items-center border-bottom px-5 py-3">
            <div class="mb-3">
              <div *ngIf="userName" class="avatar-wrapper w-80px h-80px rounded-circle d-flex align-items-center justify-content-center bg-primary text-white">
                <span class="fs-24">{{ getUserInitials() }}</span>
              </div>
              <img *ngIf="!userName" class="w-80px h-80px rounded-circle" src="https://placehold.co/80x80" alt="">
            </div>
            <div class="text-center">
              <p class="tx-16 fw-bolder">{{ (userFirstName + ' ' + userLastName) || 'User' }}</p>
              <p class="fs-12px text-secondary">{{ userEmail || '<EMAIL>' }}</p>
              <span *ngIf="userRole" class="badge bg-primary">
                {{ userRole }}
              </span>
              <p *ngIf="lastLogin" class="fs-12px text-secondary mt-2">
                <i class="feather icon-clock me-1"></i> Last login: {{ lastLogin }}
              </p>
            </div>
          </div>
          <ul class="list-unstyled p-1">
            <li>
              <a routerLink="/general/profile" class="dropdown-item py-2 d-flex ms-0">
                <i class="feather icon-user me-2 icon-md"></i>
                <span>Profile</span>
              </a>
            </li>
            <!-- <li>
              <a href="" (click)="false" class="dropdown-item py-2 d-flex ms-0">
                <i class="feather icon-edit me-2 icon-md"></i>
                <span>Edit Profile</span>
              </a>
            </li>
            <li>
              <a href="" (click)="false" class="dropdown-item py-2 d-flex ms-0">
                <i class="feather icon-repeat me-2 icon-md"></i>
                <span>Switch User</span>
              </a>
            </li> -->
            <li>
              <a href="#" (click)="onLogout($event)" class="dropdown-item py-2 d-flex ms-0">
                <i class="feather icon-log-out me-2 icon-md"></i>
                <span>Log Out</span>
              </a>
            </li>
          </ul>
        </div>
      </li>
    </ul>

    <a href="" class="sidebar-toggler" (click)="toggleSidebar($event)">
      <i class="feather icon-menu"></i>
    </a>

  </div>
</nav>
