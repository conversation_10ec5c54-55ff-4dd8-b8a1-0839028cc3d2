import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Sub Product Type interfaces
export interface SubProductType {
  id: string;
  name: string;
  code: string; // Required field for standardized codes (CF, IF, PF, etc.)
  description?: string;
  product_type_id: string;
  product_type_name?: string;
  category?: 'variant' | 'addon' | 'component' | 'service' | 'configuration' | 'other' | null; // Allow null
  form_schema?: FormSchema;
  default_values?: any;
  validation_rules?: ValidationRule[];
  ui_config?: UIConfig;
  pricing_override?: PricingOverride;
  availability_rules?: AvailabilityRule[];
  dependencies?: string[];
  tags?: string[];
  is_active: boolean;
  is_featured: boolean; // Required in API response
  display_order: number; // Required in API response
  created_at: string;
  updated_at: string | null; // Allow null as per API response
  deleted_at?: string;
}

export interface FormSchema {
  fields: FormField[];
  layout?: LayoutConfig;
  validation?: SchemaValidation;
}

export interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'tel' | 'url' | 'password' | 'textarea' | 'select' | 'multiselect' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'time' | 'file' | 'image' | 'color' | 'range' | 'json';
  description?: string;
  placeholder?: string;
  required: boolean;
  readonly?: boolean;
  hidden?: boolean;
  default_value?: any;
  options?: FieldOption[];
  validation?: FieldValidation;
  conditional?: ConditionalLogic;
  display_order: number;
  group?: string;
}

export interface FieldOption {
  value: any;
  label: string;
  description?: string;
  disabled?: boolean;
  group?: string;
}

export interface FieldValidation {
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  custom?: string;
  message?: string;
}

export interface ConditionalLogic {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  action: 'show' | 'hide' | 'enable' | 'disable' | 'require' | 'optional';
}

export interface LayoutConfig {
  columns: number;
  groups?: FieldGroup[];
  tabs?: TabConfig[];
}

export interface FieldGroup {
  id: string;
  title: string;
  description?: string;
  collapsible?: boolean;
  collapsed?: boolean;
  fields: string[];
}

export interface TabConfig {
  id: string;
  title: string;
  icon?: string;
  fields: string[];
}

export interface SchemaValidation {
  rules?: ValidationRule[];
  custom_validators?: string[];
}

export interface ValidationRule {
  id: string;
  name: string;
  type: 'required' | 'unique' | 'custom' | 'cross_field' | 'business_rule';
  fields: string[];
  condition?: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface UIConfig {
  theme?: string;
  layout?: 'vertical' | 'horizontal' | 'grid' | 'tabs';
  show_progress?: boolean;
  show_summary?: boolean;
  auto_save?: boolean;
  confirmation_required?: boolean;
  custom_css?: string;
  custom_js?: string;
}

export interface PricingOverride {
  base_price?: number;
  currency?: string;
  pricing_model?: 'fixed' | 'variable' | 'calculated';
  calculation_formula?: string;
  price_modifiers?: PriceModifier[];
}

export interface PriceModifier {
  field: string;
  type: 'percentage' | 'fixed' | 'multiplier';
  value: number;
  condition?: string;
}

export interface AvailabilityRule {
  id: string;
  name: string;
  type: 'date_range' | 'quantity' | 'location' | 'user_role' | 'custom';
  condition: any;
  action: 'allow' | 'deny' | 'require_approval';
  message?: string;
}

export interface SubProductTypeCreate {
  name: string;
  code: string;
  description?: string;
  product_type_id: string;
  category: 'variant' | 'addon' | 'component' | 'service' | 'configuration' | 'other';
  form_schema?: FormSchema;
  default_values?: any;
  validation_rules?: ValidationRule[];
  ui_config?: UIConfig;
  pricing_override?: PricingOverride;
  availability_rules?: AvailabilityRule[];
  dependencies?: string[];
  tags?: string[];
  is_active?: boolean;
  is_featured?: boolean;
  display_order?: number;
}

export interface SubProductTypeUpdate {
  name?: string;
  code?: string;
  description?: string;
  product_type_id?: string;
  category?: 'variant' | 'addon' | 'component' | 'service' | 'configuration' | 'other';
  form_schema?: FormSchema;
  default_values?: any;
  validation_rules?: ValidationRule[];
  ui_config?: UIConfig;
  pricing_override?: PricingOverride;
  availability_rules?: AvailabilityRule[];
  dependencies?: string[];
  tags?: string[];
  is_active?: boolean;
  is_featured?: boolean;
  display_order?: number;
}

export interface SubProductTypeStatistics {
  total_sub_product_types: number;
  active_sub_product_types: number;
  inactive_sub_product_types: number;
  sub_product_types_by_category: { [category: string]: number };
  sub_product_types_by_product_type: { [product_type: string]: number };
  featured_sub_product_types: number;
  popular_sub_product_types: SubProductType[];
  recent_sub_product_types: SubProductType[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SubProductTypeService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/sub-product-types/`;
  private subProductTypesSubject = new BehaviorSubject<SubProductType[]>([]);
  public subProductTypes$ = this.subProductTypesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all sub product types with optional filtering and pagination (returns APIResponse)
   */
  getSubProductTypesWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    category?: string;
    product_type_id?: string;
    is_featured?: boolean;
    include_deleted?: boolean;
  }): Observable<APIResponse<SubProductType[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<SubProductType[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.subProductTypesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all sub product types (backward compatibility) - returns SubProductType[] directly
   */
  getSubProductTypes(skip: number = 0, limit: number = 10, filter: any = {}): Observable<SubProductType[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search || filter.name,
      is_active: filter.is_active,
      category: filter.category,
      product_type_id: filter.product_type_id
    };

    return this.getSubProductTypesWithResponse(params).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  /**
   * Get sub product type by ID
   */
  getSubProductTypeById(id: string): Observable<APIResponse<SubProductType>> {
    return this.http.get<APIResponse<SubProductType>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get sub product types by product type ID
   */
  getSubProductTypesByProductTypeId(productTypeId: string): Observable<SubProductType[]> {
    return this.getSubProductTypes(0, 1000, { product_type_id: productTypeId, is_active: true });
  }

  /**
   * Get all active sub product types for dropdown usage
   */
  getActiveSubProductTypes(): Observable<SubProductType[]> {
    return this.getSubProductTypes(0, 1000, { is_active: true });
  }

  /**
   * Get sub product types without pagination (backward compatibility)
   */
  getSubProductTypesLegacy(filter: any = {}): Observable<SubProductType[]> {
    return this.getSubProductTypes(0, 1000, filter);
  }

  /**
   * Create new sub product type
   */
  createSubProductType(subProductType: SubProductTypeCreate): Observable<APIResponse<SubProductType>> {
    return this.http.post<APIResponse<SubProductType>>(this.baseUrl, subProductType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSubProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update sub product type
   */
  updateSubProductType(id: string, subProductType: SubProductTypeUpdate): Observable<APIResponse<SubProductType>> {
    return this.http.put<APIResponse<SubProductType>>(`${this.baseUrl}${id}`, subProductType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSubProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete sub product type
   */
  deleteSubProductType(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSubProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted sub product type
   */
  restoreSubProductType(id: string): Observable<APIResponse<SubProductType>> {
    return this.http.post<APIResponse<SubProductType>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSubProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get sub product type statistics
   */
  getSubProductTypeStatistics(): Observable<APIResponse<SubProductTypeStatistics>> {
    return this.http.get<APIResponse<SubProductTypeStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload sub product types
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSubProductTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Validate form schema
   */
  validateFormSchema(schema: FormSchema): Observable<APIResponse<any>> {
    return this.http.post<APIResponse<any>>(`${this.baseUrl}validate-schema`, { schema })
      .pipe(catchError(this.handleError));
  }

  /**
   * Preview form rendering
   */
  previewForm(subProductTypeId: string, data?: any): Observable<APIResponse<any>> {
    return this.http.post<APIResponse<any>>(`${this.baseUrl}${subProductTypeId}/preview`, { data })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get sub product types for dropdown (simplified data)
   */
  getSubProductTypesDropdown(productTypeId?: string): Observable<{ id: string; name: string; code: string; category: string }[]> {
    const filter = productTypeId ? { is_active: true, product_type_id: productTypeId } : { is_active: true };

    return this.getSubProductTypes(0, 1000, filter).pipe(
      map(subProductTypes => {
        return subProductTypes.map(subProductType => ({
          id: subProductType.id,
          name: subProductType.name,
          code: subProductType.code,
          category: subProductType.category
        }));
      })
    );
  }

  /**
   * Search sub product types by name or code
   */
  searchSubProductTypes(query: string, limit: number = 20): Observable<SubProductType[]> {
    return this.getSubProductTypes(0, limit, { search: query });
  }

  /**
   * Refresh sub product types data
   */
  refreshSubProductTypes(): void {
    this.getSubProductTypesWithResponse().subscribe();
  }

  /**
   * Clear sub product types cache
   */
  clearCache(): void {
    this.subProductTypesSubject.next([]);
  }

  /**
   * Get sub product type categories
   */
  getSubProductTypeCategories(): { value: string; label: string; description: string }[] {
    return [
      { value: 'variant', label: 'Product Variant', description: 'Different versions or variations of the main product' },
      { value: 'addon', label: 'Add-on', description: 'Additional features or services that can be added' },
      { value: 'component', label: 'Component', description: 'Individual components or parts of the product' },
      { value: 'service', label: 'Service', description: 'Related services or support offerings' },
      { value: 'configuration', label: 'Configuration', description: 'Different configuration options or setups' },
      { value: 'other', label: 'Other', description: 'Other sub-product types' }
    ];
  }

  /**
   * Get form field types
   */
  getFormFieldTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'text', label: 'Text Input', description: 'Single line text input' },
      { value: 'textarea', label: 'Text Area', description: 'Multi-line text input' },
      { value: 'number', label: 'Number', description: 'Numeric input with validation' },
      { value: 'email', label: 'Email', description: 'Email address input with validation' },
      { value: 'tel', label: 'Phone', description: 'Phone number input' },
      { value: 'url', label: 'URL', description: 'Website URL input' },
      { value: 'password', label: 'Password', description: 'Password input field' },
      { value: 'select', label: 'Dropdown', description: 'Single selection dropdown' },
      { value: 'multiselect', label: 'Multi-Select', description: 'Multiple selection dropdown' },
      { value: 'radio', label: 'Radio Buttons', description: 'Single choice radio buttons' },
      { value: 'checkbox', label: 'Checkboxes', description: 'Multiple choice checkboxes' },
      { value: 'date', label: 'Date', description: 'Date picker' },
      { value: 'datetime', label: 'Date & Time', description: 'Date and time picker' },
      { value: 'time', label: 'Time', description: 'Time picker' },
      { value: 'file', label: 'File Upload', description: 'File upload field' },
      { value: 'image', label: 'Image Upload', description: 'Image upload with preview' },
      { value: 'color', label: 'Color Picker', description: 'Color selection input' },
      { value: 'range', label: 'Range Slider', description: 'Numeric range slider' },
      { value: 'json', label: 'JSON Editor', description: 'JSON data editor' }
    ];
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Sub Product Type service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
