<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title and add button -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Associate List</h6>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="forceReloadData()" [disabled]="loading">
              <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
              Refresh
            </button>
            <button class="btn btn-primary" (click)="openAssociateModal(associateModal)">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add New Associate
            </button>
          </div>
        </div> <!-- Search and filter -->
        <div class="row mb-3">
          <div class="col-12 col-md-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input type="text" class="form-control" [formControl]="searchTerm"
                placeholder="Search by name, category, or status...">
              <button *ngIf="searchTerm.value" class="input-group-text bg-light text-danger"
                (click)="searchTerm.setValue(''); loadAssociates()">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
            <small class="text-muted" *ngIf="searchTerm.value">
              Searching for: "{{ searchTerm.value }}"
            </small>
          </div>

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="loadAssociates()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div> <!-- Loading indicator -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">Actions</th>
                <th scope="col" sortable="name" (sort)="onSort($event)">Associate</th>
                <th scope="col" sortable="category" (sort)="onSort($event)">Company</th>
                <th scope="col" sortable="status" (sort)="onSort($event)">Status</th>

              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let associate of associates">
                <td class="action-icons">
                  <button type="button" class="action-icon text-info" ngbTooltip="View"
                    (click)="viewAssociate(associate, viewAssociateModal)">
                    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Edit"
                    (click)="openAssociateModal(associateModal, associate)">
                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Delete"
                    (click)="deleteAssociate(associate)"
                    [disabled]="deleting && deletingAssociateId === associate.id">
                    <span *ngIf="deleting && deletingAssociateId === associate.id"
                      class="spinner-border spinner-border-sm text-danger" role="status"></span>
                    <i *ngIf="!(deleting && deletingAssociateId === associate.id)"
                      data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                  </button>

                  <button *ngIf="associate.deleted_at" type="button" class="action-icon text-success"
                    ngbTooltip="Restore" (click)="restoreAssociate(associate)"
                    [disabled]="restoring && restoringAssociateId === associate.id">
                    <span *ngIf="restoring && restoringAssociateId === associate.id"
                      class="spinner-border spinner-border-sm text-success" role="status"></span>
                    <i *ngIf="!(restoring && restoringAssociateId === associate.id)"
                      data-feather="refresh-cw" class="icon-sm" appFeatherIcon></i>
                  </button>
                </td>

                <td>{{ associate.name }}</td>
                <td>
                  <span class="badge bg-light text-dark">
                    {{ associate.company_name || associate.category }}
                  </span>
                </td>
                <td>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(associate.status)">
                    {{ associate.status | titlecase }}
                  </span>
                </td>

              </tr>
              <tr *ngIf="associates.length === 0">
                <td colspan="4" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="users" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No associates found</p>
                    <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
                    <small class="text-muted" *ngIf="searchTerm.value">You can search by name, category, or status
                      (active/inactive)</small>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!loading && totalItems > 0">
          <div>
            <span class="text-muted">Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems)
              }} of {{ totalItems }} entries</span>
          </div>
          <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="pageSize" [maxSize]="5"
            [rotate]="true" [boundaryLinks]="true" (pageChange)="onPageChange($event)"
            class="pagination-sm"></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Associate Modal -->
<ng-template #associateModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">{{ formMode === 'create' ? 'Add New Associate' : 'Edit Associate' }}</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <!-- Error Alert -->
    <div *ngIf="modalError" class="alert alert-danger alert-dismissible fade show" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ modalError }}
      <button type="button" class="btn-close" (click)="clearModalError()"></button>
    </div>

    <form [formGroup]="associateForm">
      <div class="mb-3">
        <label for="associate_name" class="form-label">Associate Name</label>
        <input type="text" class="form-control" id="associate_name" formControlName="associate_name" placeholder="Enter associate name">
        <div *ngIf="associateForm.get('associate_name')?.invalid && associateForm.get('associate_name')?.touched" class="text-danger">
          Associate name is required
        </div>
      </div>

      <div class="mb-3">
        <label for="company_name" class="form-label">Company Name</label>
        <input type="text" class="form-control" id="company_name" formControlName="company_name" placeholder="Enter company name">
        <div *ngIf="associateForm.get('company_name')?.invalid && associateForm.get('company_name')?.touched" class="text-danger">
          Company name is required
        </div>
      </div>

      <div class="mb-3">
        <label for="location_id" class="form-label">Location</label>
        <select class="form-select" id="location_id" formControlName="location_id">
          <option value="">Select Location</option>
          <option *ngFor="let location of locations" [value]="location.id">{{ location.name }}</option>
        </select>
        <div *ngIf="associateForm.get('location_id')?.invalid && associateForm.get('location_id')?.touched" class="text-danger">
          Location is required
        </div>
      </div>

      <div class="mb-3">
        <label for="sub_location" class="form-label">Sub Location</label>
        <input type="text" class="form-control" id="sub_location" formControlName="sub_location" placeholder="Enter sub location">
        <div *ngIf="associateForm.get('sub_location')?.invalid && associateForm.get('sub_location')?.touched" class="text-danger">
          Sub location is required
        </div>
      </div>

      <div class="mb-3">
        <label for="profession_type" class="form-label">Profession Type</label>
        <select class="form-select" id="profession_type" formControlName="profession_type">
          <option value="">Select Profession Type</option>
          <option *ngFor="let type of professionTypes" [value]="type.value">{{ type.label }}</option>
        </select>
        <div *ngIf="associateForm.get('profession_type')?.invalid && associateForm.get('profession_type')?.touched" class="text-danger">
          Profession type is required
        </div>
      </div>

      <div class="mb-3">
        <label for="profession_id" class="form-label">Profession</label>
        <select class="form-select" id="profession_id" formControlName="profession_id">
          <option value="">Select Profession</option>
          <option *ngFor="let profession of filteredProfessions" [value]="profession.id">{{ profession.name }}</option>
        </select>
        <div *ngIf="associateForm.get('profession_id')?.invalid && associateForm.get('profession_id')?.touched" class="text-danger">
          Profession is required
        </div>
        <small class="form-text text-muted" *ngIf="associateForm.get('profession_type')?.value">
          Only showing professions for selected profession type
        </small>
      </div>

      <div class="mb-3">
        <label for="status" class="form-label">Status</label>
        <select class="form-select" id="status" formControlName="status">
          <option value="">Select Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
        <div *ngIf="associateForm.get('status')?.invalid && associateForm.get('status')?.touched" class="text-danger">
          Status is required
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    <button type="button" class="btn btn-primary" [disabled]="associateForm.invalid || submitting"
      (click)="saveAssociate()">
      <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
      {{ formMode === 'create' ? 'Create' : 'Update' }}
    </button>
  </div>
</ng-template>

<!-- View Associate Modal -->
<ng-template #viewAssociateModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">View Associate</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body" *ngIf="selectedAssociate">
    <div class="card">
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Associate Name:</div>
          <div class="col-md-8">{{ selectedAssociate.name }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.company_name">
          <div class="col-md-4 fw-bold">Company Name:</div>
          <div class="col-md-8">{{ selectedAssociate.company_name }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.location_name">
          <div class="col-md-4 fw-bold">Location:</div>
          <div class="col-md-8">{{ selectedAssociate.location_name }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.sub_location">
          <div class="col-md-4 fw-bold">Sub Location:</div>
          <div class="col-md-8">{{ selectedAssociate.sub_location }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.profession_type">
          <div class="col-md-4 fw-bold">Profession Type:</div>
          <div class="col-md-8">{{ selectedAssociate.profession_type | titlecase }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.profession_name">
          <div class="col-md-4 fw-bold">Profession:</div>
          <div class="col-md-8">{{ selectedAssociate.profession_name }}</div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Status:</div>
          <div class="col-md-8">
            <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(normalizeStatus(selectedAssociate.status))">
              {{ normalizeStatus(selectedAssociate.status) | titlecase }}
            </span>
          </div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.updated_at">
          <div class="col-md-4 fw-bold">Last Updated:</div>
          <div class="col-md-8">{{ selectedAssociate.updated_at | date:'medium' }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedAssociate.deleted_at">
          <div class="col-md-4 fw-bold">Deleted:</div>
          <div class="col-md-8">{{ selectedAssociate.deleted_at | date:'medium' }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Close</button>
    <button type="button" class="btn btn-primary" (click)="editFromViewModal(associateModal, modal)">
      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
      Edit
    </button>
  </div>
</ng-template>