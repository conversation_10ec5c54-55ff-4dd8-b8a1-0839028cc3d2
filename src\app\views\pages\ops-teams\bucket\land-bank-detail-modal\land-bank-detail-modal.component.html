<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Land Bank Detail</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #landBankDetailForm="ngForm">
    <div class="row mb-3">
      <!-- Company Name -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="companyName" class="form-label">Company Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="companyName" name="companyName" [(ngModel)]="formData.companyName" required>
      </div>

      <!-- Location -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="location" name="location" [(ngModel)]="formData.location" required>
      </div>

      <!-- Land Type -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="landType" class="form-label">Land Type <span class="text-danger">*</span></label>
        <select class="form-select" id="landType" name="landType" [(ngModel)]="formData.landType" required>
          <option value="" disabled>Select Land Type</option>
          <option *ngFor="let option of landTypeOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <!-- Ownership Type -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="ownershipType" class="form-label">Ownership Type <span class="text-danger">*</span></label>
        <select class="form-select" id="ownershipType" name="ownershipType" [(ngModel)]="formData.ownershipType" required>
          <option value="" disabled>Select Ownership Type</option>
          <option *ngFor="let option of ownershipTypeOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <!-- Plot Area -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="plotArea" class="form-label">Plot Area <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="plotArea" name="plotArea" [(ngModel)]="formData.plotArea" required min="0">
      </div>

      <!-- Plot Area Unit -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="plotAreaUnit" class="form-label">Plot Area Unit <span class="text-danger">*</span></label>
        <select class="form-select" id="plotAreaUnit" name="plotAreaUnit" [(ngModel)]="formData.plotAreaUnit" required>
          <option value="" disabled>Select Unit</option>
          <option *ngFor="let option of plotAreaUnitOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <!-- Acquisition Year -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="acquisitionYear" class="form-label">Acquisition Year <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="acquisitionYear" name="acquisitionYear" [(ngModel)]="formData.acquisitionYear" required>
      </div>

      <!-- Purchase Value -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="purchaseValue" class="form-label">Purchase Value (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="purchaseValue" name="purchaseValue" [(ngModel)]="formData.purchaseValue" required min="0">
      </div>

      <!-- Market Value -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="marketValue" class="form-label">Market Value (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="marketValue" name="marketValue" [(ngModel)]="formData.marketValue" required min="0">
      </div>

      <!-- Has Loan -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="hasLoan" class="form-label">Has Loan <span class="text-danger">*</span></label>
        <select class="form-select" id="hasLoan" name="hasLoan" [(ngModel)]="formData.hasLoan" required>
          <option value="" disabled>Select Option</option>
          <option *ngFor="let option of hasLoanOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <!-- Approval Status -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="approvalStatus" class="form-label">Approval Status <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="approvalStatus" name="approvalStatus" [(ngModel)]="formData.approvalStatus" required>
      </div>

      <!-- Expected Launch Date -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="expectedLaunchDate" class="form-label">Expected Launch Date <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="expectedLaunchDate" name="expectedLaunchDate" [(ngModel)]="formData.expectedLaunchDate" required>
      </div>

      <!-- Remarks -->
      <div class="col-12 mb-3">
        <label for="remarks" class="form-label">Remarks</label>
        <textarea class="form-control" id="remarks" name="remarks" [(ngModel)]="formData.remarks" rows="3"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="landBankDetailForm.invalid" (click)="saveChanges()">
    <i data-feather="save" class="icon-sm me-1"></i> Save
  </button>
</div>
