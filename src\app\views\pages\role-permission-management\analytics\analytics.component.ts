import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { RoleService } from '../../../../core/services/role.service';
import { finalize } from 'rxjs/operators';

interface RoleAnalytics {
  totalRoles: number;
  activeRoles: number;
  deletedRoles: number;
  totalUsers: number;
  totalPermissions: number;
  averagePermissionsPerRole: number;
  rolesWithoutUsers: number;
  rolesWithoutPermissions: number;
}

interface RoleUsageData {
  roleName: string;
  userCount: number;
  permissionCount: number;
  createdAt: Date;
  lastModified: Date;
  isActive: boolean;
}

interface PermissionDistribution {
  category: string;
  count: number;
  percentage: number;
  color: string;
}

@Component({
  selector: 'app-analytics',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FeatherIconDirective,
    NgbTooltipModule
  ],
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss']
})
export class AnalyticsComponent implements OnInit {
  loading = false;
  analytics: RoleAnalytics = {
    totalRoles: 0,
    activeRoles: 0,
    deletedRoles: 0,
    totalUsers: 0,
    totalPermissions: 0,
    averagePermissionsPerRole: 0,
    rolesWithoutUsers: 0,
    rolesWithoutPermissions: 0
  };

  roleUsageData: RoleUsageData[] = [];
  permissionDistribution: PermissionDistribution[] = [];
  topRolesByUsers: RoleUsageData[] = [];
  recentlyCreatedRoles: RoleUsageData[] = [];

  constructor(private roleService: RoleService) {}

  ngOnInit(): void {
    this.loadAnalytics();
  }

  loadAnalytics(): void {
    this.loading = true;
    
    Promise.all([
      this.loadRoleAnalytics(),
      this.loadRoleUsageData(),
      this.loadPermissionDistribution()
    ]).finally(() => {
      this.loading = false;
    });
  }

  private async loadRoleAnalytics(): Promise<void> {
    try {
      const roles = await this.roleService.getAllRoles().toPromise() || [];
      const permissions = await this.roleService.getAllPermissions().toPromise() || [];

      this.analytics.totalRoles = roles.length;
      this.analytics.activeRoles = roles.filter(role => !role.deleted_at).length;
      this.analytics.deletedRoles = roles.filter(role => role.deleted_at).length;
      this.analytics.totalPermissions = permissions.length;

      // Calculate total users across all roles
      this.analytics.totalUsers = roles.reduce((total, role) => {
        return total + (role.users ? role.users.length : 0);
      }, 0);

      // Calculate average permissions per role
      const totalPermissionsAssigned = roles.reduce((total, role) => {
        return total + (role.permissions ? role.permissions.length : 0);
      }, 0);
      this.analytics.averagePermissionsPerRole = this.analytics.totalRoles > 0 
        ? Math.round(totalPermissionsAssigned / this.analytics.totalRoles) 
        : 0;

      // Calculate roles without users or permissions
      this.analytics.rolesWithoutUsers = roles.filter(role => 
        !role.users || role.users.length === 0
      ).length;
      
      this.analytics.rolesWithoutPermissions = roles.filter(role => 
        !role.permissions || role.permissions.length === 0
      ).length;

    } catch (error) {
      console.error('Error loading role analytics:', error);
    }
  }

  private async loadRoleUsageData(): Promise<void> {
    try {
      const roles = await this.roleService.getAllRoles().toPromise() || [];
      
      this.roleUsageData = roles.map(role => ({
        roleName: role.name,
        userCount: role.users ? role.users.length : 0,
        permissionCount: role.permissions ? role.permissions.length : 0,
        createdAt: new Date(role.created_at),
        lastModified: new Date(role.updated_at || role.created_at),
        isActive: !role.deleted_at
      }));

      // Get top roles by user count
      this.topRolesByUsers = [...this.roleUsageData]
        .filter(role => role.isActive)
        .sort((a, b) => b.userCount - a.userCount)
        .slice(0, 5);

      // Get recently created roles
      this.recentlyCreatedRoles = [...this.roleUsageData]
        .filter(role => role.isActive)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5);

    } catch (error) {
      console.error('Error loading role usage data:', error);
    }
  }

  private async loadPermissionDistribution(): Promise<void> {
    try {
      const permissions = await this.roleService.getAllPermissions().toPromise() || [];
      
      // Categorize permissions
      const categories = new Map<string, number>();
      
      permissions.forEach(permission => {
        const category = this.getPermissionCategory(permission.name);
        categories.set(category, (categories.get(category) || 0) + 1);
      });

      const total = permissions.length;
      const colors = [
        '#667eea', '#764ba2', '#f093fb', '#f5576c',
        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
        '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
      ];

      this.permissionDistribution = Array.from(categories.entries()).map(([category, count], index) => ({
        category: this.formatCategoryName(category),
        count,
        percentage: Math.round((count / total) * 100),
        color: colors[index % colors.length]
      })).sort((a, b) => b.count - a.count);

    } catch (error) {
      console.error('Error loading permission distribution:', error);
    }
  }

  private getPermissionCategory(permissionName: string): string {
    const name = permissionName.toLowerCase();
    
    if (name.includes('user')) return 'users';
    if (name.includes('role') || name.includes('permission')) return 'roles';
    if (name.includes('sales') || name.includes('lead')) return 'sales';
    if (name.includes('employee') || name.includes('staff')) return 'employees';
    if (name.includes('master') || name.includes('config')) return 'master';
    if (name.includes('admin') || name.includes('system')) return 'admin';
    if (name.includes('report') || name.includes('analytics')) return 'reports';
    if (name.includes('leave') || name.includes('attendance')) return 'hr';
    
    return 'general';
  }

  private formatCategoryName(category: string): string {
    const categoryNames: { [key: string]: string } = {
      'users': 'User Management',
      'roles': 'Role & Permissions',
      'sales': 'Sales Management',
      'employees': 'Employee Management',
      'master': 'Master Data',
      'admin': 'Administration',
      'reports': 'Reports & Analytics',
      'hr': 'Human Resources',
      'general': 'General'
    };
    
    return categoryNames[category] || category;
  }

  getHealthScore(): number {
    const factors = [
      this.analytics.rolesWithoutUsers === 0 ? 25 : Math.max(0, 25 - (this.analytics.rolesWithoutUsers * 5)),
      this.analytics.rolesWithoutPermissions === 0 ? 25 : Math.max(0, 25 - (this.analytics.rolesWithoutPermissions * 5)),
      this.analytics.averagePermissionsPerRole >= 3 ? 25 : (this.analytics.averagePermissionsPerRole / 3) * 25,
      this.analytics.activeRoles > 0 ? 25 : 0
    ];
    
    return Math.round(factors.reduce((sum, factor) => sum + factor, 0));
  }

  getHealthScoreColor(): string {
    const score = this.getHealthScore();
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'danger';
  }

  getHealthScoreText(): string {
    const score = this.getHealthScore();
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Attention';
  }

  refreshAnalytics(): void {
    this.loadAnalytics();
  }
}
