<div class="row">
  <div class="col-md-12">
    <div class="row">
      <div class="col-12 grid-margin">
        <div class="card">
          <div class="card-body">
            <h4 class="card-title">Attendance Calendar</h4>
            <full-calendar
              [options]='calendarOptions'
            ></full-calendar>
          </div>
        </div>
      </div>
      <div class="col-12 d-none d-md-block">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-2 text-secondary">Draggable Events</h6>
            <div class='d-flex' #externalEvents>
              <div class='fc-event' [ngStyle]="{'background-color': 'rgba(253,126,20,.25)', 'border-color': '#fd7e14'}" [attr.bgColor]="'rgba(253,126,20,.25)'" [attr.bdcolor]="'#fd7e14'">Sick Leave</div>
              <div class='fc-event' [ngStyle]="{'background-color': 'rgba(241,0,117,.25)', 'border-color': '#f10075'}" [attr.bgColor]="'rgba(241,0,117,.25)'" [attr.bdColor]="'#f10075'">Vacation</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="fullCalModal" class="modal fade">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h4 id="modalTitle1" class="modal-title"></h4>
        <button type="button" class="btn-close" data-dismiss="modal"><span class="visually-hidden">close</span></button>
      </div>
      <div id="modalBody1" class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button class="btn btn-primary">Event Details</button>
      </div>
    </div>
  </div>
</div>

<div id="createEventModal" class="modal fade">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h4 id="modalTitle2" class="modal-title">Add Attendance Event</h4>
        <button type="button" class="btn-close" data-dismiss="modal"><span class="visually-hidden">close</span></button>
      </div>
      <div id="modalBody2" class="modal-body">
        <form>
          <div class="mb-3">
            <label for="eventTitle" class="form-label">Event Title</label>
            <input type="text" class="form-control" id="eventTitle" placeholder="Enter event title">
          </div>
          <div class="mb-3">
            <label for="eventType" class="form-label">Event Type</label>
            <select class="form-select" id="eventType">
              <option value="sick">Sick Leave</option>
              <option value="vacation">Vacation</option>
              <option value="training">Training</option>
              <option value="meeting">Meeting</option>
              <option value="wfh">Work From Home</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button class="btn btn-primary">Add</button>
      </div>
    </div>
  </div>
</div>
