import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { Employee, EmployeeService, EmployeeSearchParams } from '../../../../core/services/employee.service';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';
import Swal from 'sweetalert2';
import { MemorySafeBaseComponent } from '../../../../core/components/memory-safe-base.component';
import { VirtualScrollTableComponent, VirtualScrollColumn, VirtualScrollConfig } from '../../../../shared/components/virtual-scroll-table/virtual-scroll-table.component';

@Component({
  selector: 'app-employee-list',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    FeatherIconDirective,
    VirtualScrollTableComponent
  ],
  templateUrl: './employee-list.component.html',
  styleUrls: ['./employee-list.component.scss']
})
export class EmployeeListComponent extends MemorySafeBaseComponent implements OnInit {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  searchTerm: string = '';
  loading: boolean = true;
  error: string | null = null;

  // Search functionality
  private searchSubject = new Subject<string>();

  // Sorting
  sortColumn: string = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';

  // Filters
  statusFilter: 'all' | 'active' | 'inactive' = 'all';
  departmentFilter: string = 'all';
  designationFilter: string = 'all';
  departments: string[] = [];
  designations: string[] = [];

  // Pagination
  currentPage: number = 1;
  pageSize: number = 10;
  totalEmployees: number = 0;

  // Virtual scroll configuration
  virtualScrollColumns: VirtualScrollColumn[] = [
    { key: 'name', label: 'Name', sortable: true, width: '200px' },
    { key: 'email', label: 'Email', sortable: true, width: '250px' },
    { key: 'department', label: 'Department', sortable: true, width: '150px' },
    { key: 'designation', label: 'Designation', sortable: true, width: '150px' },
    { key: 'status', label: 'Status', template: 'badge', width: '100px' },
    { key: 'actions', label: 'Actions', template: 'actions', width: '120px' }
  ];

  virtualScrollConfig: VirtualScrollConfig = {
    itemSize: 60,
    bufferSize: 300,
    enableSort: true,
    enableSelection: false
  };

  // Export/Import
  exporting = false;

  constructor(
    private employeeService: EmployeeService,
    private cdr: ChangeDetectorRef
  ) {
    super(); // Call MemorySafeBaseComponent constructor

    // Set up search with debounce using memory-safe subscription
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.performSearch(searchTerm);
    });
  }

  ngOnInit(): void {
    this.loadEmployees();
  }

  loadEmployees(): void {
    this.loading = true;
    this.error = null;

    // Use new skip/limit pagination format with proper pagination
    this.employeeService.getEmployees(0, 1000).subscribe({
      next: (data) => {

        // Process employee data to add computed name field
        let employeeArray: any[] = [];
        if (Array.isArray(data)) {
          employeeArray = data;
        } else if ('data' in data && Array.isArray((data as any).data)) {
          employeeArray = (data as any).data;
        } else {
          employeeArray = [];
        }
        this.employees = employeeArray.map((emp: any) => ({
          ...emp,
          name: `${emp.employee_first_name || ''} ${emp.employee_last_name || ''}`.trim()
        }));

        this.totalEmployees = this.employees.length;
        this.extractDepartmentsAndDesignations();
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading employees:', err);
        this.error = 'Failed to load employees. Please try again.';
        this.loading = false;
      }
    });
  }

  // Enhanced search functionality using API
  performSearch(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.loadEmployees();
      return;
    }

    this.loading = true;
    this.error = null;

    const searchParams: EmployeeSearchParams = {
      query: searchTerm.trim(),
      department: this.departmentFilter !== 'all' ? this.departmentFilter : undefined,
      position: this.designationFilter !== 'all' ? this.designationFilter : undefined,
      user_active: this.statusFilter !== 'all' ? this.statusFilter === 'active' : undefined,
      page: this.currentPage,
      size: this.pageSize
    };

    this.employeeService.searchEmployees(searchParams).subscribe({
      next: (data) => {

        this.employees = data.map(emp => ({
          ...emp,
          name: `${emp.employee_first_name || ''} ${emp.employee_last_name || ''}`.trim()
        }));

        this.totalEmployees = this.employees.length;
        this.extractDepartmentsAndDesignations();
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        console.error('Search error:', err);
        this.error = 'Search failed. Please try again.';
        this.loading = false;
      }
    });
  }

  // Trigger search when user types
  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.searchSubject.next(this.searchTerm);
  }

  private extractDepartmentsAndDesignations(): void {
    // Extract unique departments for filtering
    this.departments = [...new Set(
      this.employees
        .map(emp => emp.department)
        .filter(Boolean) as string[]
    )];

    // Extract unique designations for filtering
    this.designations = [...new Set(
      this.employees
        .map(emp => emp.position)
        .filter(Boolean) as string[]
    )];
  }

  applyFilters(): void {
    let filtered = [...this.employees];

    // Apply search term
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(emp =>
        (emp.name && emp.name.toLowerCase().includes(term)) ||
        emp.email.toLowerCase().includes(term) ||
        (emp.position && emp.position.toLowerCase().includes(term)) ||
        (emp.department && emp.department.toLowerCase().includes(term))
      );
    }

    // Apply status filter
    if (this.statusFilter !== 'all') {
      const isActive = this.statusFilter === 'active';
      filtered = filtered.filter(emp => emp.user_active === isActive);
    }

    // Apply department filter
    if (this.departmentFilter !== 'all') {
      filtered = filtered.filter(emp => emp.department === this.departmentFilter);
    }

    // Apply designation filter
    if (this.designationFilter !== 'all') {
      filtered = filtered.filter(emp => emp.position === this.designationFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[this.sortColumn as keyof Employee] as any;
      const bValue = b[this.sortColumn as keyof Employee] as any;

      if (!aValue && !bValue) return 0;
      if (!aValue) return this.sortDirection === 'asc' ? 1 : -1;
      if (!bValue) return this.sortDirection === 'asc' ? -1 : 1;

      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    this.filteredEmployees = filtered;
  }

  toggleStatus(employee: Employee): void {
    if (!employee.id) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Invalid employee data. Please refresh the page and try again.'
      });
      return;
    }

    const newStatus = !employee.user_active;
    const statusText = newStatus ? 'activate' : 'deactivate';
    const employeeName = employee.name || `${employee.employee_first_name} ${employee.employee_last_name}`;

    Swal.fire({
      title: `${statusText.charAt(0).toUpperCase() + statusText.slice(1)} Employee`,
      text: `Are you sure you want to ${statusText} ${employeeName}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: newStatus ? '#28a745' : '#ffc107',
      cancelButtonColor: '#6c757d',
      confirmButtonText: `Yes, ${statusText}!`,
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.employeeService.setEmployeeStatus(employee.id!, newStatus).subscribe({
          next: (updatedEmployee) => {

            // Show success message and reload page
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: `Employee ${statusText}d successfully. The page will refresh to show the updated status.`,
              timer: 2000,
              showConfirmButton: false
            }).then(() => {
              // Reload page to get fresh data (like F5)
              window.location.reload();
            });
          },
          error: (err) => {
            console.error('Error updating employee status:', err);

            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: `Failed to ${statusText} employee. Please try again.`,
              confirmButtonText: 'OK'
            });
          }
        });
      }
    });
  }

  deleteEmployee(employeeId: number): void {
    if (!employeeId) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Invalid employee data. Please refresh the page and try again.'
      });
      return;
    }

    const employee = this.employees.find(e => e.id === employeeId);
    const employeeName = employee ?
      (employee.name || `${employee.employee_first_name} ${employee.employee_last_name}`) :
      'this employee';

    Swal.fire({
      title: 'Delete Employee',
      text: `Are you sure you want to delete ${employeeName}? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, delete!',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        console.log(`Deleting employee: ${employeeId}`);

        this.employeeService.deleteEmployee(employeeId).subscribe({
          next: () => {
            console.log('Employee deleted successfully');

            // Show success message and reload page
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: 'Employee has been deleted successfully. The page will refresh to show the updated list.',
              timer: 2000,
              showConfirmButton: false
            }).then(() => {
              // Reload page to get fresh data (like F5)
              console.log('🔄 Reloading page to show updated employee list...');
              window.location.reload();
            });
          },
          error: (err) => {
            console.error('Error deleting employee:', err);

            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to delete employee. Please try again.',
              confirmButtonText: 'OK'
            });
          }
        });
      }
    });
  }

  sortBy(column: string): void {
    if (this.sortColumn === column) {
      // Toggle direction if clicking the same column
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Default to ascending order for a new column
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }

    this.applyFilters();
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) {
      return 'filter';
    }
    return this.sortDirection === 'asc' ? 'arrow-up' : 'arrow-down';
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.statusFilter = 'all';
    this.departmentFilter = 'all';
    this.designationFilter = 'all';
    this.applyFilters();
  }

  exportEmployees(): void {
    this.exporting = true;

    console.log('Starting employee export...');

    this.employeeService.exportEmployees().subscribe({
      next: (blob) => {
        console.log('Export successful, blob size:', blob.size);

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `employees_export_${new Date().toISOString().split('T')[0]}.xlsx`;
        link.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        this.exporting = false;

        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Export Successful!',
          text: 'Employee data has been exported successfully.',
          timer: 2000,
          showConfirmButton: false
        });
      },
      error: (err) => {
        console.error('Export error:', err);
        this.exporting = false;

        Swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'Failed to export employee data. Please try again.',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  // Refresh employee list
  refreshEmployees(): void {
    console.log('Refreshing employee list...');
    this.searchTerm = '';
    this.statusFilter = 'all';
    this.departmentFilter = 'all';
    this.designationFilter = 'all';
    this.currentPage = 1;
    this.loadEmployees();
  }

  // Quick filter methods
  filterByDepartment(department: string): void {
    this.departmentFilter = department;
    this.currentPage = 1;
    if (this.searchTerm.trim()) {
      this.performSearch(this.searchTerm);
    } else {
      this.loadEmployeesByDepartment(department);
    }
  }

  filterByDesignation(designation: string): void {
    this.designationFilter = designation;
    this.currentPage = 1;
    if (this.searchTerm.trim()) {
      this.performSearch(this.searchTerm);
    } else {
      this.loadEmployeesByDesignation(designation);
    }
  }

  // Getter methods for template
  get activeEmployeesCount(): number {
    return this.employees.filter(emp => emp.user_active).length;
  }

  get inactiveEmployeesCount(): number {
    return this.employees.filter(emp => !emp.user_active).length;
  }



  loadEmployeesByDepartment(department: string): void {
    this.loading = true;
    this.employeeService.getEmployeesByDepartment(department).subscribe({
      next: (data) => {
        this.employees = data.map(emp => ({
          ...emp,
          name: `${emp.employee_first_name} ${emp.employee_last_name}`.trim()
        }));
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load employees by department.';
        this.loading = false;
        console.error('Error:', err);
      }
    });
  }

  loadEmployeesByDesignation(designation: string): void {
    this.loading = true;
    this.employeeService.getEmployeesByDesignation(designation).subscribe({
      next: (data) => {
        this.employees = data.map(emp => ({
          ...emp,
          name: `${emp.employee_first_name} ${emp.employee_last_name}`.trim()
        }));
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load employees by designation.';
        this.loading = false;
        console.error('Error:', err);
      }
    });
  }

  loadActiveEmployees(): void {
    this.loading = true;
    this.employeeService.getActiveEmployees().subscribe({
      next: (data) => {
        this.employees = data.map(emp => ({
          ...emp,
          name: `${emp.employee_first_name} ${emp.employee_last_name}`.trim()
        }));
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load active employees.';
        this.loading = false;
        console.error('Error:', err);
      }
    });
  }

  // Virtual scroll event handlers
  onVirtualScrollRowClick(event: { item: Employee, index: number }): void {
    // Handle row click - could navigate to employee details
  }

  onVirtualScrollSort(event: { column: string, direction: 'asc' | 'desc' }): void {
    this.sortColumn = event.column;
    this.sortDirection = event.direction;
    this.sortBy(event.column);
    this.cdr.markForCheck();
  }

  onVirtualScrollAction(event: { action: string, item: Employee }): void {
    switch (event.action) {
      case 'view':
        this.viewEmployee(event.item);
        break;
      case 'edit':
        this.editEmployee(event.item);
        break;
      case 'delete':
        this.handleDeleteEmployee(event.item);
        break;
    }
  }

  private viewEmployee(employee: Employee): void {
    // Implement view logic
  }

  private editEmployee(employee: Employee): void {
    // Implement edit logic
  }

  private handleDeleteEmployee(employee: Employee): void {
    // Use the existing deleteEmployee method with the employee ID
    if (employee.id) {
      this.deleteEmployee(employee.id);
    }
  }

  // TrackBy function for performance
  trackByEmployeeId(index: number, employee: Employee): any {
    return employee.id || index;
  }
}
