import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Location interfaces
export interface Location {
  id: string;
  uuid?: string; // For backward compatibility
  name: string;
  code: string;
  type: 'country' | 'state' | 'city' | 'district' | 'area' | 'zone' | 'other';
  description?: string;
  parent_id?: string;
  level: number;
  latitude?: number;
  longitude?: number;
  postal_code?: string;
  area_code?: string;
  time_zone?: string;
  currency?: string;
  language?: string;
  is_active: boolean;
  population?: number;
  area_sq_km?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  children?: Location[];
}

export interface LocationCreate {
  name: string;
  code: string;
  type: 'country' | 'state' | 'city' | 'district' | 'area' | 'zone' | 'other';
  description?: string;
  parent_id?: string;
  level: number;
  latitude?: number;
  longitude?: number;
  postal_code?: string;
  area_code?: string;
  time_zone?: string;
  currency?: string;
  language?: string;
  is_active?: boolean;
  population?: number;
  area_sq_km?: number;
}

export interface LocationUpdate {
  name?: string;
  code?: string;
  type?: 'country' | 'state' | 'city' | 'district' | 'area' | 'zone' | 'other';
  description?: string;
  parent_id?: string;
  level?: number;
  latitude?: number;
  longitude?: number;
  postal_code?: string;
  area_code?: string;
  time_zone?: string;
  currency?: string;
  language?: string;
  is_active?: boolean;
  population?: number;
  area_sq_km?: number;
}

export interface LocationStatistics {
  total_locations: number;
  active_locations: number;
  inactive_locations: number;
  locations_by_type: { [type: string]: number };
  locations_by_level: { [level: string]: number };
  popular_locations: Location[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class LocationService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/locations/`;
  private locationsSubject = new BehaviorSubject<Location[]>([]);
  public locations$ = this.locationsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all locations with optional filtering and pagination (returns APIResponse)
   */
  getLocationsWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    type?: string;
    level?: number;
    parent_id?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<Location[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Location[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.locationsSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all locations (backward compatibility) - returns Location[] directly
   */
  getLocations(skip: number = 0, limit: number = 10, filter: any = {}): Observable<Location[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search || filter.name,
      is_active: filter.is_active
    };

    return this.getLocationsWithResponse(params).pipe(
      map(response => {
        if (response.success && response.data) {
          // Add backward compatibility mapping
          return response.data.map(location => ({
            ...location,
            uuid: location.id // Map id to uuid for backward compatibility
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get location by ID
   */
  getLocationById(id: string): Observable<APIResponse<Location>> {
    return this.http.get<APIResponse<Location>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get location by UUID (backward compatibility)
   */
  getLocation(locationUuid: string): Observable<Location> {
    return this.getLocationById(locationUuid).pipe(
      map(response => {
        if (response.success && response.data) {
          return {
            ...response.data,
            uuid: response.data.id // Add legacy uuid field
          };
        }
        throw new Error('Location not found');
      })
    );
  }

  /**
   * Get all active locations for dropdown usage
   */
  getActiveLocations(): Observable<Location[]> {
    return this.getLocations(0, 1000, { is_active: true });
  }

  /**
   * Get locations without pagination (backward compatibility)
   */
  getLocationsLegacy(filter: any = {}): Observable<Location[]> {
    return this.getLocations(0, 1000, filter);
  }

  /**
   * Create new location
   */
  createLocation(location: LocationCreate): Observable<APIResponse<Location>> {
    return this.http.post<APIResponse<Location>>(this.baseUrl, location)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLocations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update location
   */
  updateLocation(id: string, location: LocationUpdate): Observable<APIResponse<Location>> {
    return this.http.put<APIResponse<Location>>(`${this.baseUrl}${id}`, location)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLocations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete location
   */
  deleteLocation(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLocations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted location
   */
  restoreLocation(id: string): Observable<APIResponse<Location>> {
    return this.http.post<APIResponse<Location>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLocations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get location statistics
   */
  getLocationStatistics(): Observable<APIResponse<LocationStatistics>> {
    return this.http.get<APIResponse<LocationStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload locations
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLocations();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Get locations for dropdown (simplified data)
   */
  getLocationsDropdown(): Observable<{ id: string; name: string; code: string; type: string }[]> {
    return this.getLocations(0, 1000, { is_active: true }).pipe(
      map(locations => {
        return locations.map(location => ({
          id: location.id,
          name: location.name,
          code: location.code,
          type: location.type
        }));
      })
    );
  }

  /**
   * Search locations by name or type (backward compatibility)
   */
  searchLocations(query: string, limit: number = 20): Observable<Location[]> {
    return this.getLocations(0, limit, { search: query });
  }

  /**
   * Refresh locations data
   */
  refreshLocations(): void {
    this.getLocationsWithResponse().subscribe();
  }

  /**
   * Clear locations cache
   */
  clearCache(): void {
    this.locationsSubject.next([]);
  }

  /**
   * Get location types
   */
  getLocationTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'country', label: 'Country', description: 'National level location' },
      { value: 'state', label: 'State/Province', description: 'State or province level' },
      { value: 'city', label: 'City', description: 'City or town level' },
      { value: 'district', label: 'District', description: 'District or county level' },
      { value: 'area', label: 'Area', description: 'Local area or neighborhood' },
      { value: 'zone', label: 'Zone', description: 'Specific zone or sector' },
      { value: 'other', label: 'Other', description: 'Other location types' }
    ];
  }

  /**
   * Get time zones
   */
  getTimeZones(): string[] {
    return [
      'UTC',
      'America/New_York',
      'America/Chicago',
      'America/Denver',
      'America/Los_Angeles',
      'Europe/London',
      'Europe/Paris',
      'Europe/Berlin',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Asia/Kolkata',
      'Asia/Dubai',
      'Australia/Sydney'
    ];
  }

  /**
   * Get currencies
   */
  getCurrencies(): string[] {
    return [
      'USD',
      'EUR',
      'GBP',
      'INR',
      'CAD',
      'AUD',
      'SGD',
      'HKD',
      'JPY',
      'CHF',
      'AED'
    ];
  }

  /**
   * Get languages
   */
  getLanguages(): string[] {
    return [
      'English',
      'Spanish',
      'French',
      'German',
      'Italian',
      'Portuguese',
      'Russian',
      'Chinese',
      'Japanese',
      'Korean',
      'Arabic',
      'Hindi'
    ];
  }

  /**
   * Get location type label
   */
  getLocationTypeLabel(type: string): string {
    const types = this.getLocationTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Build location hierarchy
   */
  buildLocationHierarchy(locations: Location[]): Location[] {
    const locationMap = new Map<string, Location>();
    const rootLocations: Location[] = [];

    // First pass: create map and initialize children arrays
    locations.forEach(location => {
      locationMap.set(location.id, { ...location, children: [] });
    });

    // Second pass: build hierarchy
    locations.forEach(location => {
      const locationWithChildren = locationMap.get(location.id)!;

      if (location.parent_id && locationMap.has(location.parent_id)) {
        const parent = locationMap.get(location.parent_id)!;
        parent.children!.push(locationWithChildren);
      } else {
        rootLocations.push(locationWithChildren);
      }
    });

    return rootLocations;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Location service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
