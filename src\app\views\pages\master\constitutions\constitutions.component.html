<!-- Constitutions Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">

        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-file-text me-2"></i>
              Constitutions Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_constitutions }} total constitutions,
              {{ statistics.active_constitutions }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Constitution
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'"
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Constitutions
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'"
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Constitutions
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'"
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">

          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-2">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search..."
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedType" (change)="onTypeFilter()">
                <option value="">All Types</option>
                <option *ngFor="let type of constitutionTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedComplianceStatus" (change)="onComplianceStatusFilter()">
                <option value="">All Compliance</option>
                <option *ngFor="let status of complianceStatuses" [value]="status.value">
                  {{ status.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedJurisdiction" (change)="onJurisdictionFilter()">
                <option value="">All Jurisdictions</option>
                <option *ngFor="let jurisdiction of jurisdictions" [value]="jurisdiction">
                  {{ jurisdiction }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedRegulatoryBody" (change)="onRegulatoryBodyFilter()">
                <option value="">All Regulatory Bodies</option>
                <option *ngFor="let body of regulatoryBodies" [value]="body">
                  {{ body }}
                </option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading constitutions...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Constitution Details</th>
                  <th>Type & Framework</th>
                  <th>Legal Information</th>
                  <th>Compliance</th>
                  <th>Document Info</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let constitution of getCurrentList(); trackBy: trackByConstitutionId">
                  <td>
                    <div>
                      <strong>{{ constitution.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ constitution.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="constitution.description">
                        {{ constitution.description }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ getConstitutionTypeLabel(constitution.type) }}</span>
                    <small class="d-block text-muted mt-1" *ngIf="constitution.legal_framework">
                      <strong>Framework:</strong> {{ constitution.legal_framework }}
                    </small>
                    <small class="d-block text-muted" *ngIf="constitution.governing_law">
                      <strong>Governing Law:</strong> {{ constitution.governing_law }}
                    </small>
                  </td>
                  <td>
                    <div *ngIf="constitution.jurisdiction || constitution.registration_number">
                      <small class="d-block text-muted" *ngIf="constitution.jurisdiction">
                        <i class="feather icon-map-pin me-1"></i>
                        {{ constitution.jurisdiction }}
                      </small>
                      <small class="d-block text-muted" *ngIf="constitution.registration_number">
                        <strong>Reg:</strong> {{ constitution.registration_number }}
                      </small>
                      <small class="d-block text-muted" *ngIf="constitution.registration_date">
                        <strong>Date:</strong> {{ constitution.registration_date | date:'MMM yyyy' }}
                      </small>
                      <small class="d-block text-muted" *ngIf="constitution.regulatory_body">
                        <strong>Body:</strong> {{ constitution.regulatory_body }}
                      </small>
                    </div>
                    <span *ngIf="!constitution.jurisdiction && !constitution.registration_number" class="text-muted fst-italic">
                      Not specified
                    </span>
                  </td>
                  <td>
                    <div class="compliance-info">
                      <span *ngIf="constitution.compliance_status" [class]="getComplianceBadgeClass(constitution.compliance_status)">
                        {{ getComplianceStatusLabel(constitution.compliance_status) }}
                      </span>
                      <small class="d-block text-muted mt-1" *ngIf="constitution.next_review_date">
                        <strong>Next Review:</strong> {{ constitution.next_review_date | date:'MMM yyyy' }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="document-info">
                      <small class="d-block text-muted" *ngIf="constitution.document_url">
                        <i class="feather icon-file-text me-1"></i>
                        <a [href]="constitution.document_url" target="_blank" class="text-decoration-none">
                          View Document
                        </a>
                      </small>
                      <small class="d-block text-muted" *ngIf="constitution.last_amended_date">
                        <strong>Last Amended:</strong> {{ constitution.last_amended_date | date:'MMM yyyy' }}
                      </small>
                      <small class="d-block text-muted" *ngIf="constitution.amendment_count">
                        <strong>Amendments:</strong> {{ constitution.amendment_count }}
                      </small>
                    </div>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(constitution.is_active)">
                      {{ getStatusText(constitution.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ constitution.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(constitution)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteConstitution(constitution)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreConstitution(constitution)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-file-text text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Constitutions' : 'No Constitutions Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No constitutions have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No constitutions match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first constitution.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Constitution
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} constitutions
            </div>
            <ngb-pagination
              [(page)]="currentPage"
              [pageSize]="pageSize"
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_constitutions }}</h3>
                      <p class="mb-0">Total Constitutions</p>
                    </div>
                    <i class="feather icon-file-text" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_constitutions }}</h3>
                      <p class="mb-0">Active Constitutions</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-secondary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_constitutions }}</h3>
                      <p class="mb-0">Inactive Constitutions</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Constitutions by Type -->
          <div *ngIf="statistics.constitutions_by_type" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Constitutions by Type</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.constitutions_by_type)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getConstitutionTypeLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-primary fs-6">{{ statistics.constitutions_by_type[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Constitutions by Compliance Status -->
          <div *ngIf="statistics.constitutions_by_compliance" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Constitutions by Compliance Status</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.constitutions_by_compliance)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getComplianceStatusLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span [class]="getComplianceBadgeClass(key) + ' fs-6'">{{ statistics.constitutions_by_compliance[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Constitutions by Jurisdiction -->
          <div *ngIf="statistics.constitutions_by_jurisdiction" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Constitutions by Jurisdiction</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.constitutions_by_jurisdiction)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ key }}</h6>
                      <small class="text-muted">Jurisdiction</small>
                    </div>
                    <span class="badge bg-info fs-6">{{ statistics.constitutions_by_jurisdiction[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Constitutions -->
          <div *ngIf="statistics.popular_constitutions?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Popular Constitutions</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Constitution</th>
                      <th>Type</th>
                      <th>Compliance</th>
                      <th>Jurisdiction</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let constitution of statistics.popular_constitutions">
                      <td>
                        <strong>{{ constitution.name }}</strong>
                        <small class="d-block text-muted">{{ constitution.code }}</small>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ getConstitutionTypeLabel(constitution.type) }}</span>
                      </td>
                      <td>
                        <span *ngIf="constitution.compliance_status" [class]="getComplianceBadgeClass(constitution.compliance_status)">
                          {{ getComplianceStatusLabel(constitution.compliance_status) }}
                        </span>
                        <span *ngIf="!constitution.compliance_status" class="text-muted">-</span>
                      </td>
                      <td>
                        {{ constitution.jurisdiction || 'Not specified' }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
