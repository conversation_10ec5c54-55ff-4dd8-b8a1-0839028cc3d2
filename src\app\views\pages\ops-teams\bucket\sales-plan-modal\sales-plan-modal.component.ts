import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-sales-plan-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './sales-plan-modal.component.html',
  styleUrl: './sales-plan-modal.component.scss'
})
export class SalesPlanModalComponent implements OnInit {
  @Input() rowId?: number;
  
  // Form data
  formData = {
    id: 0,
    quarter: '',
    flatsBooked: 0,
    averageArea: 0,
    ratePerSqFt: 0,
    saleValue: 0
  };

  // Quarter options
  quarters = [
    { value: 'q1', label: 'Q1' },
    { value: 'q2', label: 'Q2' },
    { value: 'q3', label: 'Q3' },
    { value: 'q4', label: 'Q4' },
    { value: 'q5', label: 'Q5' },
    { value: 'q6', label: 'Q6' },
    { value: 'q7', label: 'Q7' },
    { value: 'q8', label: 'Q8' },
    { value: 'q9', label: 'Q9' }
  ];

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      quarter: '',
      flatsBooked: 0,
      averageArea: 0,
      ratePerSqFt: 0,
      saleValue: 0
    };

    // If editing an existing record, populate the form
    if (this.rowId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll just set some example data
      this.formData = {
        id: this.rowId,
        quarter: 'q1',
        flatsBooked: 12,
        averageArea: 850,
        ratePerSqFt: 4500,
        saleValue: 45900000
      };
    }
  }

  // Calculate sale value based on other inputs
  calculateSaleValue() {
    this.formData.saleValue = this.formData.flatsBooked * this.formData.averageArea * this.formData.ratePerSqFt;
    return this.formData.saleValue;
  }

  // Format number with commas
  formatNumber(num: number): string {
    if (num === undefined || num === null) return '0';
    return num.toLocaleString('en-IN');
  }

  // Save changes and close the modal
  saveChanges() {
    // Calculate the sale value before saving
    this.calculateSaleValue();
    
    // Close the modal and pass the data back
    this.activeModal.close({
      quarter: this.formData.quarter,
      flatsBooked: this.formData.flatsBooked,
      averageArea: this.formData.averageArea,
      ratePerSqFt: this.formData.ratePerSqFt,
      saleValue: this.formData.saleValue
    });
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
