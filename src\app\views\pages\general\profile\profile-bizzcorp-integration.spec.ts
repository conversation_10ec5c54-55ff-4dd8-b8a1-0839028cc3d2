import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

import { ProfileComponent } from './profile.component';
import { EmployeeService, BizzCorpEmployee, Department, Designation, BizzCorpRole } from '../../../../core/services/employee.service';
import { RoleService } from '../../../../core/services/role.service';
import { environment } from '../../../../../environments/environment';

describe('ProfileComponent - BizzCorp API Integration', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let employeeService: jasmine.SpyObj<EmployeeService>;
  let roleService: jasmine.SpyObj<RoleService>;
  let httpMock: HttpTestingController;

  const mockEmployee: BizzCorpEmployee = {
    id: '1910042b-3036-40cd-97cd-d2471b5de402',
    employee_code: 'EMP001',
    first_name: 'John',
    middle_name: 'Michael',
    last_name: 'Doe',
    date_of_birth: '1990-05-15',
    gender: 'Male',
    marital_status: 'Single',
    personal_email: '<EMAIL>',
    office_email: '<EMAIL>',
    phone_no: '9876543210',
    alternet_no: '****** 567 8901',
    address: '123 Main St, New York, NY 10001',
    joining_date: '2023-01-15',
    reporting_date: '2023-01-16',
    role: 'U',
    department_id: 'dept-123',
    designation_id: 'desig-456',
    sub_role_id: 'role-789',
    office_location: 'Mumbai',
    shift_time: '9:00 AM - 6:00 PM',
    blood_group: 'O+',
    is_active: true,
    created_at: '2023-01-15T10:00:00Z',
    updated_at: '2023-01-15T10:00:00Z'
  };

  const mockDepartments: Department[] = [
    { id: 'dept-123', name: 'IT', is_active: true },
    { id: 'dept-456', name: 'HR', is_active: true }
  ];

  const mockDesignations: Designation[] = [
    { id: 'desig-456', name: 'Software Engineer', is_active: true },
    { id: 'desig-789', name: 'Senior Engineer', is_active: true }
  ];

  const mockRole: BizzCorpRole = {
    id: 'role-789',
    name: 'Developer',
    description: 'Software Developer Role',
    is_active: true
  };

  beforeEach(async () => {
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getBizzCorpEmployeeProfile',
      'getDepartmentsMasterData',
      'getDesignationsMasterData',
      'getDesignationById',
      'getRoleById'
    ]);

    const roleServiceSpy = jasmine.createSpyObj('RoleService', ['getAllRoles']);

    await TestBed.configureTestingModule({
      imports: [
        ProfileComponent,
        HttpClientTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: RoleService, useValue: roleServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;
    roleService = TestBed.inject(RoleService) as jasmine.SpyObj<RoleService>;
    httpMock = TestBed.inject(HttpTestingController);

    // Setup default spy returns
    employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(mockEmployee));
    employeeService.getDepartmentsMasterData.and.returnValue(of(mockDepartments));
    employeeService.getDesignationsMasterData.and.returnValue(of(mockDesignations));
    employeeService.getDesignationById.and.returnValue(of(mockDesignations[0]));
    employeeService.getRoleById.and.returnValue(of(mockRole));
    roleService.getAllRoles.and.returnValue(of([]));
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load BizzCorp employee profile data on init', () => {
    component.ngOnInit();

    expect(employeeService.getBizzCorpEmployeeProfile).toHaveBeenCalledWith(component['CURRENT_EMPLOYEE_ID']);
    expect(employeeService.getDepartmentsMasterData).toHaveBeenCalled();
    expect(employeeService.getDesignationsMasterData).toHaveBeenCalled();
  });

  it('should populate profile data correctly', () => {
    component.ngOnInit();

    expect(component.currentEmployee).toEqual(mockEmployee);
    expect(component.departments).toEqual(mockDepartments);
    expect(component.designations).toEqual(mockDesignations);
    expect(component.profileTableData.length).toBe(1);
    
    const profileData = component.profileTableData[0];
    expect(profileData.employeeCode).toBe(mockEmployee.employee_code);
    expect(profileData.firstName).toBe(mockEmployee.first_name);
    expect(profileData.lastName).toBe(mockEmployee.last_name);
    expect(profileData.phoneNo).toBe(mockEmployee.phone_no);
    expect(profileData.alternateNo).toBe(mockEmployee.alternet_no);
  });

  it('should resolve department name from department_id', () => {
    component.ngOnInit();

    const profileData = component.profileTableData[0];
    expect(profileData.department).toBe('IT'); // Should be resolved from department_id
  });

  it('should load designation details by ID', () => {
    component.ngOnInit();

    expect(employeeService.getDesignationById).toHaveBeenCalledWith(mockEmployee.designation_id);
  });

  it('should load role details by ID', () => {
    component.ngOnInit();

    expect(employeeService.getRoleById).toHaveBeenCalledWith(mockEmployee.sub_role_id);
  });

  it('should handle loading state', () => {
    expect(component.loading).toBe(false);
    
    component.ngOnInit();
    expect(component.loading).toBe(true);
    
    // After async operations complete
    fixture.detectChanges();
    expect(component.loading).toBe(false);
  });

  it('should handle error state', () => {
    const errorMessage = 'Failed to load employee profile';
    employeeService.getBizzCorpEmployeeProfile.and.returnValue(
      throwError(() => ({ code: 'NOT_FOUND', message: errorMessage }))
    );

    component.ngOnInit();

    expect(component.error).toBeTruthy();
    expect(component.error?.message).toBe(errorMessage);
    expect(component.loading).toBe(false);
  });

  it('should retry loading profile data', () => {
    component.retryLoadProfile();

    expect(employeeService.getBizzCorpEmployeeProfile).toHaveBeenCalled();
    expect(employeeService.getDepartmentsMasterData).toHaveBeenCalled();
    expect(employeeService.getDesignationsMasterData).toHaveBeenCalled();
  });

  it('should handle missing optional fields gracefully', () => {
    const incompleteEmployee: BizzCorpEmployee = {
      id: '123',
      employee_code: 'EMP002',
      first_name: 'Jane',
      last_name: 'Smith',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
      // Missing optional fields
    };

    employeeService.getBizzCorpEmployeeProfile.and.returnValue(of(incompleteEmployee));
    component.ngOnInit();

    const profileData = component.profileTableData[0];
    expect(profileData.middleName).toBe('');
    expect(profileData.phoneNo).toBe('');
    expect(profileData.bloodGroup).toBe('');
    expect(profileData.dateOfBirth).toBe('');
  });

  it('should update dropdown options with master data', () => {
    component.ngOnInit();

    expect(component.departmentOptions).toEqual(mockDepartments);
    expect(component.designationOptions).toEqual(mockDesignations);
  });

  it('should handle search functionality with new phone fields', () => {
    component.ngOnInit();
    
    // Set search term that matches phone number
    component.searchControl.setValue('9876543210');
    component.applyFilters();

    expect(component.filteredData.length).toBe(1);
    expect(component.filteredData[0].phoneNo).toBe('9876543210');
  });
});
