import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Department interfaces
export interface Department {
  id: string;
  name: string;
  description?: string;
  parent_id?: string;
  parent_name?: string;
  level: number;
  is_active: boolean;
  employee_count?: number;
  created_at: string;
  updated_at: string;
  children?: Department[];
}

export interface DepartmentCreate {
  name: string;
  description?: string;
  parent_id?: string;
  is_active?: boolean;
}

export interface DepartmentUpdate {
  name?: string;
  description?: string;
  parent_id?: string;
  is_active?: boolean;
}

export interface DepartmentHierarchy {
  id: string;
  name: string;
  level: number;
  children: DepartmentHierarchy[];
  employee_count: number;
}

export interface DepartmentStatistics {
  total_departments: number;
  active_departments: number;
  inactive_departments: number;
  max_depth: number;
  departments_by_level: { [level: number]: number };
  top_departments_by_employees: Department[];
}

export interface DepartmentTree {
  id: string;
  name: string;
  parent_id?: string;
  children: DepartmentTree[];
  employee_count: number;
  is_active: boolean;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/departments/`;
  private departmentsSubject = new BehaviorSubject<Department[]>([]);
  public departments$ = this.departmentsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all departments with optional filtering and pagination
   */
  getDepartments(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    parent_id?: string;
  }): Observable<APIResponse<Department[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Department[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.departmentsSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get department by ID
   */
  getDepartmentById(id: string): Observable<APIResponse<Department>> {
    return this.http.get<APIResponse<Department>>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new department
   */
  createDepartment(department: DepartmentCreate): Observable<APIResponse<Department>> {
    return this.http.post<APIResponse<Department>>(this.baseUrl, department)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDepartments();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update department
   */
  updateDepartment(id: string, department: DepartmentUpdate): Observable<APIResponse<Department>> {
    return this.http.put<APIResponse<Department>>(`${this.baseUrl}/${id}`, department)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDepartments();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Delete department
   */
  deleteDepartment(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshDepartments();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get department hierarchy
   */
  getDepartmentHierarchy(departmentId: string): Observable<APIResponse<DepartmentHierarchy>> {
    return this.http.get<APIResponse<DepartmentHierarchy>>(`${this.baseUrl}/${departmentId}/hierarchy`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get department statistics
   */
  getDepartmentStatistics(): Observable<APIResponse<DepartmentStatistics>> {
    return this.http.get<APIResponse<DepartmentStatistics>>(`${this.baseUrl}/statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get department tree structure
   */
  getDepartmentTree(): Observable<APIResponse<DepartmentTree[]>> {
    return this.http.get<APIResponse<DepartmentTree[]>>(`${this.baseUrl}/tree`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Search departments
   */
  searchDepartments(query: string, filters?: {
    is_active?: boolean;
    parent_id?: string;
    level?: number;
  }): Observable<APIResponse<Department[]>> {
    let params = new HttpParams().set('search', query);

    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key as keyof typeof filters];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Department[]>>(this.baseUrl, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get departments for dropdown (simplified data)
   */
  getDepartmentsDropdown(): Observable<{ id: string; name: string; level: number }[]> {
    return this.getDepartments({ per_page: 1000 }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map(dept => ({
            id: dept.id,
            name: dept.name,
            level: dept.level
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get active departments only
   */
  getActiveDepartments(): Observable<Department[]> {
    return this.getDepartments({ is_active: true }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get departments by parent ID
   */
  getDepartmentsByParent(parentId: string): Observable<Department[]> {
    return this.getDepartments({ parent_id: parentId }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh departments data
   */
  refreshDepartments(): void {
    this.getDepartments().subscribe();
  }

  /**
   * Clear departments cache
   */
  clearCache(): void {
    this.departmentsSubject.next([]);
  }

  /**
   * Helper method to build department hierarchy from flat list
   */
  buildHierarchy(departments: Department[]): Department[] {
    const departmentMap = new Map<string, Department>();
    const rootDepartments: Department[] = [];

    // Create a map of all departments
    departments.forEach(dept => {
      departmentMap.set(dept.id, { ...dept, children: [] });
    });

    // Build the hierarchy
    departments.forEach(dept => {
      const department = departmentMap.get(dept.id)!;

      if (dept.parent_id && departmentMap.has(dept.parent_id)) {
        const parent = departmentMap.get(dept.parent_id)!;
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(department);
      } else {
        rootDepartments.push(department);
      }
    });

    return rootDepartments;
  }

  /**
   * Get department path (breadcrumb)
   */
  getDepartmentPath(departmentId: string, departments: Department[]): string[] {
    const path: string[] = [];
    let currentDept = departments.find(d => d.id === departmentId);

    while (currentDept) {
      path.unshift(currentDept.name);
      currentDept = currentDept.parent_id
        ? departments.find(d => d.id === currentDept!.parent_id)
        : undefined;
    }

    return path;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Department service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
