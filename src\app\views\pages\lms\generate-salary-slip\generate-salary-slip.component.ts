import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbTooltipModule, NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';

// Sortable directive for table headers
export type SortColumn = 'date' | 'monthYear' | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: {[key: string]: SortDirection} = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number, v2: string | number) {
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-generate-salary-slip',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FeatherIconDirective,
    NgbTooltipModule,
    NgbPaginationModule,
    NgbdSortableHeader
  ],
  templateUrl: './generate-salary-slip.component.html',
  styleUrl: './generate-salary-slip.component.scss'
})
export class GenerateSalarySlipComponent implements OnInit {

  // Employee list and selection
  employeeList: any[] = [
    { id: 'EMP001', name: 'John Doe', code: 'JD001' },
    { id: 'EMP002', name: 'Jane Smith', code: 'JS002' },
    { id: 'EMP003', name: 'Mike Johnson', code: 'MJ003' },
    
  ];
  selectedEmployee: string = '';

  // Table rows for salary entries
  rows: any[] = [];
  allRows: any[] = []; // Store all rows for filtering

  // Pagination
  page = 1;
  pageSize = 5;
  collectionSize = 0;

  // Make Math available in template
  Math = Math;

  // Month and Year filter
  allMonths: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  months: string[] = []; // Will hold only available months
  years: number[] = [];
  availableMonths: number[] = []; // Will hold indices of available months
  selectedMonth: string = '';
  selectedYear: string = '';

  // View mode toggle
  viewMode: 'normal' | 'pdf' = 'normal';
  selectedSalarySlip: any = null;
  showSalarySlipDetails: boolean = false;

  // Current date info
  currentYear: number = new Date().getFullYear();
  currentMonth: number = new Date().getMonth(); // 0-based (0 = January, 11 = December)

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  constructor() { }

  ngOnInit(): void {
    // For testing purposes, we can simulate the current date
    // Uncomment these lines to test with a specific date
    // this.currentYear = 2025;
    // this.currentMonth = 4; // May (0-based)

    // Generate available years in descending order (newest first)
    for (let i = this.currentYear; i >= this.currentYear - 2; i--) {
      this.years.push(i);
    }

    // Populate available months based on selected year
    this.updateAvailableMonths();

    this.generateSalaryEntries();
  }

  // Update available months based on selected year
  updateAvailableMonths(selectedYear: string = ''): void {
    this.availableMonths = [];
    this.months = [];

    const year = selectedYear ? parseInt(selectedYear) : this.currentYear;

    // If selected year is current year, only show months up to current month
    const maxMonth = (year === this.currentYear) ? this.currentMonth : 11;

    // Add available months
    for (let i = 0; i <= maxMonth; i++) {
      this.availableMonths.push(i);
      this.months.push(this.allMonths[i]);
    }

    // If the currently selected month is no longer valid, reset it
    if (this.selectedMonth && parseInt(this.selectedMonth) > maxMonth) {
      this.selectedMonth = '';
    }
  }

  // Generate salary entries up to the current month and year
  generateSalaryEntries(): void {
    this.allRows = [];

    // Generate entries for the past 2 years up to the current month
    let entryId = 1;

    // Loop through years in descending order (newest first)
    for (let year = this.currentYear; year >= this.currentYear - 2; year--) {
      // Determine max month for this year
      const maxMonth = (year === this.currentYear) ? this.currentMonth : 11;

      // Loop through months for this year in descending order (newest first)
      for (let month = maxMonth; month >= 0; month--) {
        const date = new Date(year, month, 1);

        // Generate entries for each employee
        this.employeeList.forEach(employee => {
          this.allRows.push({
            id: entryId++,
            employeeId: employee.id,
            employeeName: employee.name,
            employeeCode: employee.code,
            paySlipMonth: this.getMonthName(month),
            paySlipYear: year,
            workingDays: this.getRandomNumber(20, 25),
            pl: this.getRandomNumber(0, 3),
            lwp: this.getRandomNumber(0, 2),
            compOff: this.getRandomNumber(0, 2),
            od: this.getRandomNumber(0, 3),
            wfh: this.getRandomNumber(0, 5),
            absent: this.getRandomNumber(0, 2),
            weeklyOff: this.getRandomNumber(8, 10),
            holidays: this.getRandomNumber(2, 5),
            netPay: this.getRandomNumber(45000, 75000),
            date: this.formatDate(date),
            monthYear: this.getMonthName(month) + '/' + year,
            rawDate: date // Store the raw date for sorting
          });
        });
      }
    }

    // Set collection size and refresh with pagination
    this.collectionSize = this.allRows.length;
    this.refreshSalarySlips();
  }

  // Refresh salary slips with pagination
  refreshSalarySlips(): void {
    // Apply filters first
    const filtered = this.applyFilters();
    this.collectionSize = filtered.length;

    // Slice for pagination
    this.rows = filtered.slice(
      (this.page - 1) * this.pageSize,
      (this.page - 1) * this.pageSize + this.pageSize
    );
  }

  // Apply filters to get filtered data
  applyFilters(): any[] {
    if (!this.selectedEmployee && !this.selectedMonth && !this.selectedYear) {
      return [...this.allRows];
    }

    return this.allRows.filter(row => {
      // Employee filter
      let employeeMatch = true;
      if (this.selectedEmployee) {
        employeeMatch = row.employeeId === this.selectedEmployee;
      }

      // Date filters using the new paySlipMonth and paySlipYear fields
      let dateMatch = true;
      if (this.selectedMonth && this.selectedYear) {
        const selectedMonthName = this.allMonths[parseInt(this.selectedMonth)];
        dateMatch = row.paySlipMonth === selectedMonthName && row.paySlipYear === parseInt(this.selectedYear);
      } else if (this.selectedMonth && !this.selectedYear) {
        const selectedMonthName = this.allMonths[parseInt(this.selectedMonth)];
        dateMatch = row.paySlipMonth === selectedMonthName;
      } else if (!this.selectedMonth && this.selectedYear) {
        dateMatch = row.paySlipYear === parseInt(this.selectedYear);
      }

      return employeeMatch && dateMatch;
    });
  }

  // Filter rows by employee, month and year
  filterByMonthYear(): void {
    this.page = 1; // Reset to first page when filtering
    this.refreshSalarySlips();
  }

  // Handle sorting
  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.refreshSalarySlips();
    } else {
      // Sort the filtered data first, then apply pagination
      const filtered = this.applyFilters();
      const sorted = filtered.sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });

      // Update collection size and apply pagination
      this.collectionSize = sorted.length;
      this.rows = sorted.slice(
        (this.page - 1) * this.pageSize,
        (this.page - 1) * this.pageSize + this.pageSize
      );
    }
  }

  // Format date as DD/MM/YYYY
  formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Get month name
  getMonthName(month: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month];
  }

  // Generate random number between min and max (inclusive)
  getRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // View salary slip
  viewSalarySlip(row: any): void {
    console.log('Viewing salary slip for:', row);
    this.selectedSalarySlip = row;
    this.showSalarySlipDetails = true;
    // Default to normal view when first opening
    this.viewMode = 'normal';
  }

  // Close salary slip detail view
  closeSalarySlipDetails(): void {
    this.showSalarySlipDetails = false;
    this.selectedSalarySlip = null;
  }

  // Generate salary slip (modified from download)
  generateSalarySlip(row: any): void {
    console.log('Generating salary slip for:', row);
    // In a real application, this would generate and save a salary slip
    alert(`Generating salary slip for ${row.employeeName} (${row.employeeCode}) - ${row.paySlipMonth}/${row.paySlipYear}`);
  }

  // Toggle view mode between normal and PDF
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'normal' ? 'pdf' : 'normal';
  }

  // Show details of the selected salary slip
  showDetails(row: any): void {
    this.selectedSalarySlip = row;
    this.showSalarySlipDetails = true;
  }

  // Hide salary slip details
  hideDetails(): void {
    this.selectedSalarySlip = null;
    this.showSalarySlipDetails = false;
  }
}
