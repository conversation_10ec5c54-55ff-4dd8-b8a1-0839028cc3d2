// Restricted Datepicker Component Styles

.input-group {
  .form-control {
    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
    
    &[readonly] {
      background-color: var(--bs-body-bg);
      cursor: pointer;
    }
  }
  
  .input-group-text {
    background-color: var(--bs-light);
    border-color: var(--bs-border-color);
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    
    &:hover:not(:disabled) {
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
      color: white;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    i {
      font-size: 1rem;
    }
  }
}

// Datepicker popup customization
::ng-deep {
  .ngb-dp-content {
    .ngb-dp-day {
      // Disabled dates (weekends/holidays)
      &.disabled {
        color: var(--bs-secondary) !important;
        background-color: var(--bs-light) !important;
        cursor: not-allowed !important;
        opacity: 0.5;
        
        &:hover {
          background-color: var(--bs-light) !important;
          color: var(--bs-secondary) !important;
        }
      }
      
      // Weekend styling
      &[aria-label*="Saturday"],
      &[aria-label*="Sunday"] {
        &.disabled {
          background-color: #f8f9fa !important;
          color: #6c757d !important;
          text-decoration: line-through;
        }
      }
      
      // Holiday styling
      &.holiday {
        background-color: #fff3cd !important;
        color: #856404 !important;
        border: 1px solid #ffeaa7;
        
        &.disabled {
          background-color: #f8f9fa !important;
          color: #6c757d !important;
          text-decoration: line-through;
        }
      }
    }
  }
}

// Restriction info styling
.text-muted {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  
  i {
    font-size: 0.75rem;
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .input-group {
    .input-group-text {
      padding: 0.375rem 0.5rem;
      
      i {
        font-size: 0.875rem;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .input-group {
    .input-group-text {
      background-color: var(--bs-dark);
      border-color: var(--bs-border-color);
      color: var(--bs-light);
      
      &:hover:not(:disabled) {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
        color: white;
      }
    }
  }
}
