import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormArray, ReactiveFormsModule, Validators, FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { Ng<PERSON>lass, DatePipe, CurrencyPipe } from '@angular/common';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { ReraComponent } from './rera/rera.component';
import { ProductDataService } from '../../../../../core/services/product-data.service';
import { ConstitutionService, Constitution } from '../../../../../core/services/constitution.service';
import { LocationService, Location } from '../../../../../core/services/location.service';
// import { ProductCategory } from '../product-type.model';


interface Person {
  id: number;
  connectWith: string;
  name: string;
  mobile: string;
  email: string;
}

interface Product1 {
  srNo: number;
  dt: Date;
  leadSource: string;
  code: string;
  productType: string; // CF/PF/IF
  nameCompany: string;
  projectName: string;
  status: string; // PF
  location: string;
  subLocations: string;
  contactPersonName: string;
  buildStatus: string; // BULD
  rera: string; // Default: No
  constitution: string; // Constitution field
  // Additional fields specific to Product-one
  category: string;
}

@Component({
  selector: 'app-product-one',
  templateUrl: './product-one.component.html',
  styleUrls: ['./product-one.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    NgClass,
    NgbTooltipModule,
    FeatherIconDirective,
    ReraComponent
  ]
})
export class ProductOneComponent implements OnInit {
  @Input() selectedProductSubType: string = '';

  // Event emitter to communicate with parent component for sales submission
  @Output() submitSales = new EventEmitter<any>();

  product1List: Product1[] = [];
  product1Form: FormGroup;
  isFormSubmitted: boolean = false;
  showReraForm: boolean = false;
  reraFormData: any = {};
  people: Person[] = [];
  private personIdCounter: number = 1;
  
  // Reactive form for people information
  peopleForm: FormGroup;
  peopleFormArray: FormArray;

  // Constitution dropdown data
  constitutions: Constitution[] = [];
  constitutionsLoading: boolean = false;

  // Location dropdown data
  locations: Location[] = [];
  locationsLoading: boolean = false;

  @ViewChild('reraComponent') reraComponent: ReraComponent;
  // productCategories = Object.values(ProductCategory);

  constructor(
    private formBuilder: FormBuilder,
    private productDataService: ProductDataService,
    private constitutionService: ConstitutionService,
    private locationService: LocationService
  ) {}
public dt: Date = new Date();

  ngOnInit() {
    this.initForm();
    this.initPeopleForm(); // This already adds one person form group
    // No need to call initializePeople() separately as it would add a second row
    this.loadConstitutions();
    this.loadLocations();
    // Check initial RERA value
    this.checkReraValue();

    // Subscribe to product type changes from the service
    this.productDataService.productType$.subscribe(productType => {
      if (productType) {
        // Update the product type field in the form with the actual product type name
        this.product1Form.get('productType')?.setValue(productType);

        // Make the product type field read-only
        this.product1Form.get('productType')?.disable();
      } else {
        // If no product type is selected, enable the product type field
        this.product1Form.get('productType')?.enable();
      }
    });
    
    // Subscribe to lead category and source changes
    this.productDataService.leadCategory$.subscribe(leadCategory => {
      if (leadCategory) {
        // If lead category is 'Associate', set lead source to 'Associate'
        if (leadCategory === 'Associate') {
          this.product1Form.get('leadSource')?.setValue('Associate');
        }
      }
    });
    
    this.productDataService.leadSource$.subscribe(leadSource => {
      if (leadSource) {
        // Update the lead source field in the form
        this.product1Form.get('leadSource')?.setValue(leadSource);
      }
    });
  }

  initForm() {
    // Initialize form with default values
    const currentDate = new Date();
    // Format date as DD/MM/YYYY
    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()}`;
    
    this.product1Form = this.formBuilder.group({
      productType: ['CF', Validators.required], // Added Product Type field
      srNo: [{ value: '', disabled: true }],
      dt: [{ value: formattedDate, disabled: true }],
      leadSource: [{ value: '', disabled: true }],
      code: [{ value: '', disabled: true }],
      nameCompany: ['', Validators.required],
      projectName: ['', Validators.required],
      status: ['PF'], // Default value
      location: ['', Validators.required],
      subLocations: [''],
      contactPersonName: ['', Validators.required],
      buildStatus: ['BULD'], // Default value
      rera: ['No'], // Default value
      constitution: [''], // Constitution field
      // Additional fields specific to Product-one
      category: ['CF', Validators.required]
    });
  }

  // Getter for easy access to form fields
  get form() {
    return this.product1Form.controls;
  }

  onSubmit() {
    this.isFormSubmitted = true;

    // Mark all people form fields as touched to show validation errors
    this.peopleControls.forEach(control => {
      control.markAllAsTouched();
    });

    // Stop here if main form or people form is invalid
    if (this.product1Form.invalid || this.peopleForm.invalid) {
      return;
    }

    // Check if RERA form is shown and valid
    if (this.showReraForm && this.reraComponent) {
      // Validate RERA form
      if (!this.reraComponent.isFormValid()) {
        // Set RERA form as submitted to show validation errors
        this.reraComponent.isFormSubmitted = true;
        return;
      }
    }

    // Get all form data including constitution and other fields
    const productFormData = this.getFormData();

    console.log('✅ Product1 form submitted, emitting to parent:', productFormData);

    // Emit event to parent component to trigger sales API submission
    this.submitSales.emit(productFormData);

    // Note: Form reset and local list management will be handled by parent component
    // after successful API submission to maintain consistency with the overall flow
  }

  editProduct(srNo: number) {
    // Find the product by srNo and populate the form
    const product = this.product1List.find(p => p.srNo === srNo);
    if (product) {
      this.product1Form.patchValue(product);
    }
  }

  deleteProduct(srNo: number) {
    this.product1List = this.product1List.filter(p => p.srNo !== srNo);
  }

  onCancel() {
    // Reset the form
    this.product1Form.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values
    this.checkReraValue(); // Update RERA form visibility

    // Reset RERA form data
    this.reraFormData = {};

    // Reset RERA component if it exists
    if (this.reraComponent) {
      this.reraComponent.onCancel();
    }
    
    // Reload locations to reset any filtering
    this.loadLocations();
  }

  // Handle RERA dropdown change
  onReraChange() {
    this.checkReraValue();
  }

  // Check RERA value and update form visibility
  checkReraValue() {
    const reraValue = this.product1Form.get('rera')?.value;
    this.showReraForm = reraValue === 'Yes';
  }

  /**
   * Load constitutions from API
   */
  loadConstitutions() {
    this.constitutionsLoading = true;
    this.constitutionService.getConstitutions({ is_active: true }).subscribe({
      next: (constitutions) => {
        this.constitutions = constitutions;
        this.constitutionsLoading = false;
        console.log('✅ Constitutions loaded successfully:', constitutions);
      },
      error: (error) => {
        console.error('❌ Error loading constitutions:', error);
        this.constitutionsLoading = false;
        // Keep empty array on error to prevent UI issues
        this.constitutions = [];
      }
    });
  }

  /**
   * Load locations from API
   */
  loadLocations() {
    this.locationsLoading = true;
    this.locationService.getActiveLocations().subscribe({
      next: (locations) => {
        this.locations = locations;
        this.locationsLoading = false;
        console.log('✅ Locations loaded successfully:', locations);
      },
      error: (error) => {
        console.error('❌ Error loading locations:', error);
        this.locationsLoading = false;
        // Keep empty array on error to prevent UI issues
        this.locations = [];
      }
    });
  }

  // Handle RERA form data changes
  onReraFormDataChange(formData: any) {
    this.reraFormData = formData;
    console.log('RERA form data updated:', this.reraFormData);
  }

  /**
   * Initialize the people form with FormArray
   */
  initPeopleForm(): void {
    // Create the form array
    this.peopleFormArray = this.formBuilder.array([]);
    
    // Create the main form group containing the form array
    this.peopleForm = this.formBuilder.group({
      people: this.peopleFormArray
    });
    
    // Add initial person form group
    this.addPersonFormGroup();
    
    // Initialize people array for backward compatibility
    this.people = [
      {
        id: this.personIdCounter - 1, // Use the same ID as the form group
        connectWith: '',
        name: '',
        mobile: '',
        email: ''
      }
    ];
  }
  
  /**
   * Create a form group for a person
   */
  createPersonFormGroup(person?: Person): FormGroup {
    return this.formBuilder.group({
      id: [person?.id || this.personIdCounter++],
      connectWith: [person?.connectWith || '', Validators.required],
      name: [person?.name || '', Validators.required],
      mobile: [person?.mobile || '', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      email: [person?.email || '', [Validators.email]]
    });
  }
  
  /**
   * Get the people form array
   */
  get peopleControls() {
    return (this.peopleForm.get('people') as FormArray).controls;
  }

  // Initialize people array with one default person
  initializePeople() {
    this.people = [
      {
        id: this.personIdCounter++,
        connectWith: '',
        name: '',
        mobile: '',
        email: ''
      }
    ];
  }

  // Add a new person to the people list
  addPerson() {
    // Add to the form array
    this.addPersonFormGroup();
    
    // Also maintain the people array for backward compatibility
    const newPerson: Person = {
      id: this.personIdCounter,
      connectWith: '',
      name: '',
      mobile: '',
      email: ''
    };
    this.people.push(newPerson);
    console.log('Person added:', newPerson);
  }
  
  /**
   * Add a new person form group to the form array
   */
  addPersonFormGroup(person?: Person): void {
    this.peopleFormArray.push(this.createPersonFormGroup(person));
  }

  // Remove a person from the people list by ID
  removePerson(personId: number) {
    if (this.peopleControls.length > 1) {
      // Find the index of the person in the form array
      const index = this.peopleControls.findIndex(control => control.get('id')?.value === personId);
      if (index !== -1) {
        // Remove from form array
        this.peopleFormArray.removeAt(index);
        
        // Also maintain the people array for backward compatibility
        const personIndex = this.people.findIndex(person => person.id === personId);
        if (personIndex > -1) {
          const removedPerson = this.people.splice(personIndex, 1)[0];
          console.log('Person removed:', removedPerson);
        }
      }
    }
  }

  // Track by function for ngFor
  trackByPersonId(index: number, person: Person): number {
    return person.id;
  }

  // Clear all people and reinitialize
  clearAllPeople() {
    this.people = [];
    this.personIdCounter = 1;
    this.initializePeople();
    console.log('All people cleared and reinitialized');
  }

  /**
   * Get all form data including constitution and other fields
   * This method is called by the parent component to capture product-specific data
   */
  getFormData(): any {
    if (!this.product1Form) {
      console.warn('⚠️ Product1 form is not initialized');
      return {};
    }

    // Get all form values including disabled fields
    const formData = this.product1Form.getRawValue();

    // Add component metadata
    const productFormData = {
      ...formData,
      component_type: 'product-one',
      product_type: 'Corporate Syndication',
      form_submission_date: new Date().toISOString()
    };

    // Include RERA data if available
    if (this.showReraForm && this.reraFormData) {
      productFormData.rera_details = this.reraFormData;
    }

    // Include people information from form values if available
    if (this.peopleControls && this.peopleControls.length > 0) {
      productFormData.people_details = this.peopleControls.map(control => control.value);
    }

    console.log('✅ Product1 form data captured:', productFormData);
    return productFormData;
  }

  /**
   * Check if the form is valid
   */
  isFormValid(): boolean {
    return this.product1Form ? this.product1Form.valid : false;
  }

  /**
   * Reset the form after successful submission
   * This method is called by the parent component after successful API submission
   */
  resetFormAfterSubmission(): void {
    // Reset the form
    this.product1Form.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values
    this.checkReraValue(); // Update RERA form visibility

    // Reset RERA form data
    this.reraFormData = {};

    // Reset people array and form
    this.people = [];
    this.personIdCounter = 1;
    
    // Reset the form array
    this.peopleFormArray.clear();
    this.addPersonFormGroup();
    
    // Initialize people array for backward compatibility (just one entry)
    this.people = [
      {
        id: this.personIdCounter - 1,
        connectWith: '',
        name: '',
        mobile: '',
        email: ''
      }
    ];
    
    // Reload locations to reset any filtering
    this.loadLocations();

    console.log('✅ Product1 form reset after successful submission');
  }
}