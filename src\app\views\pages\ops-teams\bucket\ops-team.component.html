<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- List View -->
        <div *ngIf="showListView">
          <!-- Header with title and add button -->
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h6 class="card-title mb-0">Operations Team</h6>
            <button class="btn btn-primary" (click)="showDetails()">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add Details
            </button>
          </div>

          <!-- Search and filter controls -->
          <div class="row mb-3">
            <div class="col-md-6 col-lg-4">
              <div class="input-group">
                <span class="input-group-text bg-light">
                  <i data-feather="search" class="icon-sm" appFeatherIcon></i>
                </span>
                <input type="text" class="form-control" placeholder="Search operations team..."
                  [formControl]="searchTerm">
              </div>
            </div>
            <div class="col-md-6 col-lg-2 d-flex align-items-center mt-2 mt-md-0">
              <div class="d-flex align-items-center">
                <span class="text-muted me-2">Show:</span>
                <select class="form-select form-select-sm" [(ngModel)]="pageSize">
                  <option [ngValue]="5">5</option>
                  <option [ngValue]="10">10</option>
                  <option [ngValue]="20">20</option>
                  <option [ngValue]="50">50</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Table -->
          <div class="table-responsive-wrapper">
            <ngx-datatable #opsTeamTable class="bootstrap" [rows]="tableItems" [columnMode]="ColumnMode.force"
              [headerHeight]="50" [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0">


              <!-- Actions Column -->
              <ngx-datatable-column name="Actions" [width]="120" [sortable]="false">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <div class="action-icons">
                    <button type="button" class="action-icon" ngbTooltip="View Details" (click)="showDetails(row.id)">
                      <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                    </button>

                    <button type="button" class="action-icon" ngbTooltip="Edit" (click)="showDetails(row.id)">
                      <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                    </button>

                    <button type="button" class="action-icon" ngbTooltip="Delete">
                      <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                    </button>
                  </div>

                </ng-template>
              </ngx-datatable-column>

              <!-- Unique ID Column -->
              <ngx-datatable-column name="Unique ID" [width]="120" [sortable]="true" prop="uniqueId">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge bg-light-primary text-primary">{{ row.uniqueId }}</span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Lead Name Column -->
              <ngx-datatable-column name="Lead Name" [width]="120" [sortable]="true" prop="leadName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.leadName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Company Column -->
              <ngx-datatable-column name="Company" [width]="150" [sortable]="true" prop="companyName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.companyName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Project Column -->
              <ngx-datatable-column name="Project" [width]="150" [sortable]="true" prop="projectName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.projectName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Product Type Column -->
              <ngx-datatable-column name="Product Type" [width]="150" [sortable]="true" prop="productType">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge bg-light text-dark">
                    {{ row.productType }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Document Status Column -->
              <ngx-datatable-column name="Document Status" [width]="150" [sortable]="true" prop="documentStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusBadgeClass(row.documentStatus)">
                    {{ row.documentStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- One Pager Status Column -->
              <ngx-datatable-column name="One Pager" [width]="120" [sortable]="true" prop="onePagerStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusBadgeClass(row.onePagerStatus)">
                    {{ row.onePagerStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- CAM Note Status Column -->
              <ngx-datatable-column name="CAM Note" [width]="120" [sortable]="true" prop="camNoteStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusBadgeClass(row.camNoteStatus)">
                    {{ row.camNoteStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Institute Selection Status Column -->
              <ngx-datatable-column name="Institute Selection" [width]="170" [sortable]="true"
                prop="instituteSelectionStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white"
                    [ngClass]="getStatusBadgeClass(row.instituteSelectionStatus)">
                    {{ row.instituteSelectionStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- In Principles Status Column -->
              <ngx-datatable-column name="In Principles" [width]="120" [sortable]="true" prop="inPrinciplesStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusBadgeClass(row.inPrinciplesStatus)">
                    {{ row.inPrinciplesStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Cashflow Status Column -->
              <ngx-datatable-column name="Cashflow" [width]="120" [sortable]="true" prop="cashflowStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusBadgeClass(row.cashflowStatus)">
                    {{ row.cashflowStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Last Updated Column -->
              <ngx-datatable-column name="Last Updated" [width]="120" [sortable]="true" prop="lastUpdated">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.lastUpdated }}
                </ng-template>
              </ngx-datatable-column>

            </ngx-datatable>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
              <span class="text-muted" *ngIf="collectionSize > 0">
                Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, collectionSize) }} of {{
                collectionSize }} entries
              </span>
            </div>
            <ngb-pagination [collectionSize]="collectionSize" [(page)]="page" [pageSize]="pageSize" [maxSize]="5"
              [rotate]="true" [boundaryLinks]="true" (pageChange)="refreshOpsTeam()"
              class="pagination-sm"></ngb-pagination>
          </div>
        </div>

        <!-- Details Form View -->
        <div *ngIf="showDetailsForm">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h6 class="card-title mb-0">
              {{ selectedItemId ? 'Edit Operations Team Details' : 'Add Operations Team Details' }}
            </h6>
            <button class="btn btn-secondary" (click)="backToList()">
              <i data-feather="arrow-left" class="icon-sm me-1" appFeatherIcon></i>
              Back to List
            </button>
          </div>

          <!-- Tabs Navigation -->
          <ul ngbNav #opsTeamNav="ngbNav" [(activeId)]="activeTabId" class="nav-tabs mb-4">
            <li [ngbNavItem]="1">
              <a ngbNavLink>Document Status</a>
              <ng-template ngbNavContent>
                <div class="card card-body border-0  ">
                  <div>
                    <h6 class="card-subtitle mb-4">Document Status</h6>

                    <!-- Search Section -->
                    <div class="row mb-4">
                      <div class="col-md-6 col-lg-4">
                        <div class="input-group">
                          <span class="input-group-text bg-light">
                            <i data-feather="search" class="icon-sm" appFeatherIcon></i>
                          </span>
                          <input type="text" class="form-control" placeholder="Search documents..."
                            [(ngModel)]="documentSearchTerm" (input)="filterDocuments()">
                        </div>
                      </div>
                    </div>

                    <!-- Document Sections -->
                    <div class="mb-4">
                      <div *ngFor="let section of documentSections" class="document-section mb-4">
                        <div class="section-header mb-3"
                          style="background-color: #f8f9fa; border-left: 4px solid #3F828B; padding: 10px 15px;">
                          <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0" style="color: #3F828B;">
                              <i data-feather="folder" class="icon-sm me-2" appFeatherIcon></i>
                              {{ section.name }}
                            </h6>
                            <div class="d-flex">
                               <!-- Bulk Actions Dropdown -->
                               <div *ngIf="getSelectedDocumentsCount() > 1" class="dropdown me-2" ngbDropdown>
                                 <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                         id="bulkActionsDropdown" ngbDropdownToggle>
                                   <i data-feather="settings" class="icon-sm me-1" appFeatherIcon></i>
                                   Bulk Actions
                                 </button>
                                 <div class="dropdown-menu" ngbDropdownMenu>
                                   <button class="dropdown-item" (click)="applyBulkStatus('pending')">
                                     Mark as Pending
                                   </button>
                                   <button class="dropdown-item" (click)="applyBulkStatus('received')">
                                    
                                     Mark as Received
                                   </button>
                                 </div>
                               </div>
                              
                              <!-- Add Follow Ups button - only shown when at least one document in this section has pending status -->
                              <button *ngIf="hasSectionPendingDocuments(section.id)" class="btn btn-sm btn-primary me-2"
                                
                                (click)="toggleFollowUpForm(section.id)">
                                <i data-feather="plus-circle" class="icon-sm me-1" appFeatherIcon></i>
                                Add Follow Ups
                              </button>
                              <!-- Show All History button - only shown when at least one document in this section has pending status -->
                              <button *ngIf="hasSectionPendingDocuments(section.id)" class="btn btn-primary btn-sm"
                               
                                (click)="openSectionFollowUpHistoryModal(section.id)">
                                <i data-feather="list" class="icon-sm me-1" appFeatherIcon></i>
                                Show All History
                              </button>
                            </div>
                          </div>
                        </div>

                        <!-- Follow-up Form - shown when button is clicked -->
                        <div *ngIf="shouldShowFollowUpForm(section.id)" class="follow-up-form mb-3 p-3 rounded-3"
                          style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                          <h6 class="mb-3" style="color: #3F828B;">Schedule Follow-up</h6>
                          <div class="row g-3">
                            <div class="col-12 col-md-4">
                              <label class="form-label">Date</label>
                              <input type="date" class="form-control" [value]="getCurrentDate()"
                                [(ngModel)]="sectionFollowUpDate" name="sectionFollowUpDate">
                            </div>
                            <div class="col-12 col-md-4">
                              <label class="form-label">Time</label>
                              <input type="time" class="form-control" [value]="getCurrentTime()"
                                [(ngModel)]="sectionFollowUpTime" name="sectionFollowUpTime">
                            </div>
                            <div class="col-12 col-md-4">
                              <label class="form-label">Notes</label>
                              <textarea class="form-control" rows="1" placeholder="Enter follow-up notes"
                                [(ngModel)]="sectionFollowUpNotes" name="sectionFollowUpNotes"></textarea>
                            </div>
                            <div class="col-12 text-end mt-2">
                              <button class="btn btn-sm" style="background-color: #3F828B; color: white;"
                                (click)="addSectionFollowUp(section.id)">
                                <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save Follow-up
                              </button>
                            </div>
                          </div>
                        </div>


                        <!-- Show table for sections -->
                        <div class="table-responsive-wrapper">
                          <ngx-datatable #documentTable class="bootstrap" [rows]="getDocumentsBySection(section.id)"
                            [columnMode]="ColumnMode.force" [headerHeight]="50" [rowHeight]="'auto'" [scrollbarH]="true"
                            [footerHeight]="0">

                            <!-- Sr No. Column -->
                            <ngx-datatable-column name="Sr No." [width]="10" [sortable]="false">
                              <ng-template let-row="row" let-rowIndex="rowIndex" ngx-datatable-cell-template>
                                <div class="text-start">
                                  <span class="fw-medium">{{ rowIndex + 1 }}</span>
                                </div>
                              </ng-template>
                            </ngx-datatable-column>

                            <!-- List Name Column -->
                            <ngx-datatable-column name="Document Name" [width]="450" [sortable]="false">
                              <ng-template let-row="row" ngx-datatable-cell-template>
                                <!-- Special handling for MahaRERA entries -->
                                <span
                                  *ngIf="row.id === '11' || row.id === '11.a' || row.id === '11.b' || row.id === '11.c' || row.id === '11.d'"
                                  class="maharera-normal">
                                  {{ row.name }}
                                </span>
                                <!-- Normal handling for other entries -->
                                <span
                                  *ngIf="row.id !== '11' && row.id !== '11.a' && row.id !== '11.b' && row.id !== '11.c' && row.id !== '11.d'"
                                  [class.maharera-cert]="row.name.includes('MahaRERA') ||
                                    row.name.includes('Architect\'s Certificate') ||
                                    row.name.includes('Engineer\'s Certificate') ||
                                    row.name.includes('CA\'s Certificate')">
                                  {{ row.name }}
                                </span>
                              </ng-template>
                            </ngx-datatable-column>

                            <!-- Checkbox Column -->
                            <ngx-datatable-column name="Checkbox" [width]="10" [sortable]="false"
                              [cellClass]="'checkbox-column'">
                              <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="form-check" style="display: flex; justify-content: center; width: 100%;">
                                  <input type="checkbox" class="form-check-input" [id]="'check' + row.id"
                                    [checked]="row.checked" (change)="toggleCheck(row.id)">
                                </div>
                              </ng-template>
                            </ngx-datatable-column>

                            <!-- Explain Date Column -->
                            <ngx-datatable-column name="Explain Date" [width]="120" [sortable]="false">
                              <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="text-center">
                                  <input type="date" class="form-control form-control-sm"
                                    [value]="row.explainDate || ''" (change)="updateExplainDate(row.id, $event)">
                                </div>
                              </ng-template>
                            </ngx-datatable-column>
                        <!-- Received Date Column (only shown when at least one document has "received" status) -->
                            <ngx-datatable-column *ngIf="showReceivedDateColumn" name="Received Date" [width]="150"
                              [sortable]="false">
                              <ng-template let-row="row" ngx-datatable-cell-template>
                                <div class="text-center">
                                  <input *ngIf="row.status === 'received'" type="date"
                                    class="form-control form-control-sm" [value]="row.receivedDate || ''"
                                    (change)="updateReceivedDate(row.id, $event)">
                                </div>
                              </ng-template>
                            </ngx-datatable-column>
                            <!-- Status Column -->
                            <ngx-datatable-column name="Status" [width]="120" [sortable]="false">
                              <ng-template let-row="row" ngx-datatable-cell-template>
                                <div>
                                  <select class="form-select form-select-sm status-select"
                                    (change)="onStatusChange(row.id, $event)" [value]="row.status">
                                    <option value="">Select Status</option>
                                    <option value="received">Received</option>
                                    <option value="pending">Pending</option>
                                  </select>

                                  <!-- Add Follow Ups button removed as per user request -->
                                </div>
                              </ng-template>
                            </ngx-datatable-column>

                         

                            <!-- Row Detail Template -->
                            <ngx-datatable-row-detail [rowHeight]="getRowHeight">
                              <ng-template let-row="row" let-expanded="expanded" ngx-datatable-row-detail-template>
                                <div class="row-detail-wrapper p-3 w-100">
                                  <!-- Follow-up Form - shown when button is clicked -->
                                  <div *ngIf="shouldShowFollowUpForm(row.id)"
                                    class="follow-up-form mb-3 p-3 rounded-3 w-100"
                                    style="background-color: #f8f9fa; border-left: 4px solid #df5316;">
                                    <h6 class="mb-3" style="color: #df5316;">Schedule Follow-up</h6>
                                    <div class="row g-3">
                                      <div class="col-12 col-md-4">
                                        <label class="form-label">Date</label>
                                        <input type="date" class="form-control" [value]="row.date || getCurrentDate()"
                                          (change)="updateDate(row.id, $event)">
                                      </div>
                                      <div class="col-12 col-md-4">
                                        <label class="form-label">Time</label>
                                        <input type="time" class="form-control" [value]="row.time || getCurrentTime()"
                                          (change)="updateTime(row.id, $event)">
                                      </div>
                                      <div class="col-12 col-md-4">
                                        <label class="form-label">Notes</label>
                                        <textarea class="form-control" rows="1" placeholder="Enter follow-up notes"
                                          [value]="row.notes || ''" (change)="updateNotes(row.id, $event)"></textarea>
                                      </div>
                                      <div class="col-12 text-end mt-2">
                                        <button class="btn btn-sm" style="background-color: #df5316; color: white;"
                                          (click)="addFollowUp(row.id)">
                                          <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save Follow-up
                                        </button>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Follow-up History Section -->
                                  <div *ngIf="hasFollowUp(row.id)"
                                    class="follow-up-history-section mb-3 w-100 full-width-section">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                      <h6 class="mb-0 text-light">Follow-up History</h6>
                                    </div>

                                    <div class="table-responsive">
                                      <table class="table table-bordered table-hover">
                                        <thead>
                                          <tr>
                                            <th>#</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Notes</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <tr *ngFor="let followUp of getLatestFollowUps(row.id, 5); let i = index">
                                            <td>{{ i + 1 }}</td>
                                            <td>{{ followUp.date }}</td>
                                            <td>{{ followUp.time }}</td>
                                            <td>{{ followUp.notes }}</td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              </ng-template>
                            </ngx-datatable-row-detail>
                          </ngx-datatable>
                        </div> <!-- End of table-responsive-wrapper -->
                      </div>
                    </div>

                    <!-- Form Buttons -->
                    <div class="text-end mt-4">
                      <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                      <button type="button" class="btn btn-primary">
                        <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                      </button>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="2">
              <a ngbNavLink>One Pager</a>
              <ng-template ngbNavContent>
                <!-- One Pager Content -->
                <div class="card card-body border-0">
                  <div>
                    <div class="one-pager-content">
                      <form class="forms-sample">
                        <div class="row">
                          <!-- Loan Request Section -->
                          <div class="col-12 mb-4">
                            <h6 class="card-subtitle mb-3">LOAN REQUEST</h6>
                            <div class="row">
                              <div class="col-md-4 mb-3">
                                <label class="form-label">Amount Requested (in crores)</label>
                                <input type="number" class="form-control" placeholder="Enter amount">
                              </div>
                            </div>
                          </div>

                          <!-- Borrower Details Section -->
                          <div class="col-12 mb-4">
                            <h6 class="card-subtitle mb-3">BORROWER DETAILS</h6>
                            <div class="row">
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">1. Name of the Borrower</label>
                                <input type="text" class="form-control" placeholder="Enter name">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">2. Constitution of the Borrower</label>
                                <input type="text" class="form-control" placeholder="Enter constitution">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">3. No of Partners</label>
                                <input type="number" class="form-control" placeholder="Enter number">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">4. Name of the group</label>
                                <input type="text" class="form-control" placeholder="Enter group name">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">5. Locality/areas in which the construction activity is
                                  concentrated</label>
                                <textarea class="form-control" rows="2" placeholder="Enter localities"></textarea>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">6. No of year in real estate</label>
                                <input type="number" class="form-control" placeholder="Enter years">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">7. Completed Projects by the group</label>
                                <textarea class="form-control" rows="2"
                                  placeholder="Enter completed projects details"></textarea>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">8. Ongoing Projects by the group</label>
                                <textarea class="form-control" rows="2"
                                  placeholder="Enter ongoing projects details"></textarea>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">9. Land Bank & Upcoming Projects</label>
                                <textarea class="form-control" rows="2"
                                  placeholder="Enter land bank details"></textarea>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">10. Brief details of other businesses of Promoters</label>
                                <textarea class="form-control" rows="2"
                                  placeholder="Enter other business details"></textarea>
                              </div>
                            </div>
                          </div>

                          <!-- Project Details Section -->
                          <div class="col-12 mb-4">
                            <h6 class="card-subtitle mb-3">PROJECT DETAILS</h6>
                            <div class="row">
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">Name of Project</label>
                                <input type="text" class="form-control" placeholder="Enter project name">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">1. Land type</label>
                                <select class="form-select">
                                  <option value="" selected disabled>Select type</option>
                                  <option value="Owned">Owned</option>
                                  <option value="JDA">JDA</option>
                                  <option value="Society Redevelopment">Society Redevelopment</option>
                                </select>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">2. Location of the project</label>
                                <input type="text" class="form-control" placeholder="Enter location">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">3. Land Area (Sq Mt)</label>
                                <input type="number" class="form-control" placeholder="Enter area">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">4a. Total Construction Area / Built Up Area</label>
                                <input type="number" class="form-control" placeholder="Enter area">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">4b. Developer's Share - Built Up Area</label>
                                <input type="number" class="form-control" placeholder="Enter area">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">4c. Total Carpet Area</label>
                                <input type="number" class="form-control" placeholder="Enter area">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">4d. Developer's Share - Carpet Area</label>
                                <input type="number" class="form-control" placeholder="Enter area">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">5. Structure of the Towers</label>
                                <input type="text" class="form-control" placeholder="Enter structure details">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">6. In terms of layout of units</label>
                                <input type="text" class="form-control" placeholder="Enter layout details">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">7. Total No. of Units</label>
                                <input type="number" class="form-control" placeholder="Enter number of units">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">8. No. of Towers</label>
                                <input type="number" class="form-control" placeholder="Enter number of towers">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">9. Commencement of Construction</label>
                                <input type="date" class="form-control">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">10. Estimated Completion of Project</label>
                                <input type="month" class="form-control">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">11. Current Physical Status</label>
                                <input type="text" class="form-control" placeholder="Enter current status">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">12. Current Approval Status</label>
                                <input type="text" class="form-control" placeholder="Enter approval status">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">13. Achieved Rate for Sold Area</label>
                                <input type="number" class="form-control" placeholder="Enter rate">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">14. Current Sale Rate for Unsold</label>
                                <input type="number" class="form-control" placeholder="Enter rate">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">15. Sold Area (Sq Ft)</label>
                                <input type="number" class="form-control" placeholder="Enter area">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16a. Value of Area Sold</label>
                                <input type="number" class="form-control" placeholder="Enter value">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16b. Amount Received - Area Sold</label>
                                <input type="number" class="form-control" placeholder="Enter amount">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16c. Receivables - Area Sold</label>
                                <input type="number" class="form-control" placeholder="0" readonly>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16d. Receivables - Area Unsold</label>
                                <input type="number" class="form-control" placeholder="Enter receivables">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16e. Total Receivables (Sold + Unsold)</label>
                                <input type="number" class="form-control" placeholder="0" readonly>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16f. Total Sales Value</label>
                                <input type="number" class="form-control" placeholder="0" readonly>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16g. Total Project Cost</label>
                                <input type="number" class="form-control" placeholder="Enter cost">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16h. Total Cost Incurred</label>
                                <input type="number" class="form-control" placeholder="Enter cost">
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16i. Cost To Be Incurred</label>
                                <input type="number" class="form-control" placeholder="0" readonly>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">16j. Profit</label>
                                <input type="number" class="form-control" placeholder="0" readonly>
                              </div>
                              <div class="col-12 col-md-4 col-lg-3 mb-3">
                                <label class="form-label">17. Profit Percentage</label>
                                <input type="text" class="form-control" placeholder="#DIV/0!" readonly>
                              </div>
                            </div>
                          </div>

                          <!-- Form Buttons -->
                          <div class="col-12 text-end">
                            <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                              <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="3">
              <a ngbNavLink>CAM Note</a>
              <ng-template ngbNavContent>
                <!-- CAM Note Content -->
                <div class="card card-body border-0">
                  <div>
                    <h6 class="card-subtitle mb-4">CAM Note</h6>

                    <!-- Nested Tabs for CAM Note -->
                    <ul ngbNav #camNoteNav="ngbNav" [(activeId)]="activeCamNoteTabId"
                      class="nav-pills nested-tabs mb-4">
                      <li [ngbNavItem]="1">
                        <a ngbNavLink>
                          <i data-feather="file-text" class="icon-sm me-2" appFeatherIcon></i> Proposal
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <!-- PROJECT INFORMATION Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="file-text"
                                      class="icon-sm me-2" appFeatherIcon></i> PROJECT INFORMATION</h6>
                                </div>
                                <div class="card-body">
                                  <p class="mb-4">Please fill in the details below for the proposal. All fields marked
                                    with * are required.</p>

                                  <!-- Project Basic Details -->
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Project Owning Entity*</label>
                                      <input type="text" class="form-control" placeholder="Enter entity name">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Project Name*</label>
                                      <input type="text" class="form-control" placeholder="Enter project name">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">MAHARERA NO</label>
                                      <input type="text" class="form-control" placeholder="Enter MAHARERA number">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Project Location*</label>
                                      <input type="text" class="form-control" placeholder="Enter location">
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- LOAN DETAILS Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="credit-card"
                                      class="icon-sm me-2" appFeatherIcon></i> LOAN DETAILS</h6>
                                </div>
                                <div class="card-body">
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Type of Loan*</label>
                                      <select class="form-select">
                                        <option value="" selected disabled>Select type</option>
                                        <option value="Construction Funding">Construction Funding</option>
                                        <option value="Inventory Funding">Inventory Funding</option>
                                      </select>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Amount Requested (INR Crs)*</label>
                                      <input type="number" class="form-control" placeholder="Enter amount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Purpose / End Use*</label>
                                      <input type="text" class="form-control" placeholder="Enter purpose">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">First Tranche (Disbursement INR Crs)*</label>
                                      <input type="number" class="form-control" placeholder="Enter amount">
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- PURPOSE BREAKDOWN Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="pie-chart"
                                      class="icon-sm me-2" appFeatherIcon></i> PURPOSE BREAKDOWN</h6>
                                </div>
                                <div class="card-body">
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Approval</label>
                                      <input type="number" class="form-control" placeholder="Enter amount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Land Cost</label>
                                      <input type="number" class="form-control" placeholder="Enter amount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Partner Exit</label>
                                      <input type="number" class="form-control" placeholder="Enter amount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Construction</label>
                                      <input type="number" class="form-control" placeholder="Enter amount">
                                    </div>
                                  </div>
 
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Takeout for other project</label>
                                      <input type="text" class="form-control" placeholder="Enter details">
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- LOAN TERMS Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="calendar"
                                      class="icon-sm me-2" appFeatherIcon></i> LOAN TERMS</h6>
                                </div>
                                <div class="card-body">
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Principal Moratorium Period (Months)*</label>
                                      <input type="number" class="form-control" placeholder="Enter period">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">EMI payment period (Months)*</label>
                                      <input type="number" class="form-control" placeholder="Enter period">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Door to Door (Total) Tenure (Months)</label>
                                      <input type="text" class="form-control" readonly>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Rate of Interest (%)*</label>
                                      <input type="number" step="0.01" class="form-control" placeholder="Enter rate">
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- COLLATERAL SECURITY OFFERED Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="shield"
                                      class="icon-sm me-2" appFeatherIcon></i> COLLATERAL SECURITY OFFERED</h6>
                                </div>
                                <div class="card-body">
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Name of the Owner*</label>
                                      <input type="text" class="form-control" placeholder="Enter owner name">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Location*</label>
                                      <input type="text" class="form-control" placeholder="Enter location">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Area*</label>
                                      <div class="input-group">
                                        <input type="number" class="form-control" placeholder="Enter area">
                                        <select class="form-select" style="max-width: 120px;">
                                          <option value="" selected disabled>Unit</option>
                                          <option value="Sq Mt">Sq Mt</option>
                                          <option value="Sq Ft">Sq Ft</option>
                                          <option value="Gunthas">Gunthas</option>
                                          <option value="Acres">Acres</option>
                                        </select>
                                      </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Nature of Security*</label>
                                      <select class="form-select">
                                        <option value="" selected disabled>Select nature</option>
                                        <option value="NA">NA</option>
                                        <option value="Agri">Agri</option>
                                        <option value="Other">Other</option>
                                      </select>
                                    </div>
                                  </div>

                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Type of Transaction*</label>
                                      <select class="form-select">
                                        <option value="" selected disabled>Select type</option>
                                        <option value="JDA">JDA</option>
                                        <option value="Owned">Owned</option>
                                      </select>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Book / Purchase Value (INR Crs)*</label>
                                      <input type="number" class="form-control" placeholder="Enter value">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Approx Value of Security as per latest valuation (INR
                                        Crs)*</label>
                                      <input type="number" class="form-control" placeholder="Enter value">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Any Loans on the Said Property?*</label>
                                      <select class="form-select">
                                        <option value="" selected disabled>Select</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                      </select>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                  <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                </button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>
 
                      <li [ngbNavItem]="2">
                        <a ngbNavLink>
                          <i data-feather="user-check" class="icon-sm me-2" appFeatherIcon></i> Initial KYC
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Number of Partners/Directors -->
                                <div class="col-12 col-md-6 col-lg-3 mb-3">
                                  <label class="form-label">No of Partners / Directors</label>
                                  <input type="text" class="form-control" [(ngModel)]="numberOfPartners"
                                         name="numberOfPartners" placeholder="Enter number of partners/directors">
                                </div>
                              </div>

                              <!-- SHAREHOLDERS / PARTNERS DETAILS Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="users"
                                        class="icon-sm me-2" appFeatherIcon></i> SHAREHOLDERS / PARTNERS DETAILS</h6>
                                    <button type="button" class="btn btn-primary btn-sm" (click)="addShareholder()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Shareholder
                                    </button>
                                  </div>
                                </div>
                                <div class="card-body">

                              <!-- Shareholder Forms -->
                              <div *ngFor="let shareholder of shareholders; let i = index" class="mb-4">
                                <div class="card shareholder-card">
                                  <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Shareholder {{i + 1}}</h6>
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            (click)="removeShareholder(i)"
                                            [disabled]="shareholders.length === 1">
                                      <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                                    </button>
                                  </div>
                                  <div class="card-body">
                                    <div class="row">
                                      <!-- Name -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.name"
                                               [name]="'shareholderName_' + i" placeholder="Enter full name">
                                      </div>

                                      <!-- % Shareholding -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">% Shareholding</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.shareholdingPercentage"
                                               [name]="'shareholdingPercentage_' + i" placeholder="Enter percentage">
                                      </div>

                                      <!-- Profit Sharing Ratio -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Profit Sharing Ratio (%)</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.profitSharingRatio"
                                               [name]="'profitSharingRatio_' + i" placeholder="Enter ratio">
                                      </div>

                                      <!-- As Per PD Date -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">As Per PD Dated</label>
                                        <input type="date" class="form-control" [(ngModel)]="shareholder.asPerPdDate"
                                               [name]="'asPerPdDate_' + i">
                                      </div>

                                      <!-- PAN -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">PAN <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.pan"
                                               [name]="'pan_' + i" placeholder="Enter PAN number" style="text-transform: uppercase;">
                                      </div>

                                      <!-- Aadhar No -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Aadhar No</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.aadharNo"
                                               [name]="'aadharNo_' + i" placeholder="Enter Aadhar number">
                                      </div>

                                      <!-- Date of Birth -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Date of Birth</label>
                                        <input type="date" class="form-control" [(ngModel)]="shareholder.dateOfBirth"
                                               [name]="'dateOfBirth_' + i">
                                      </div>

                                      <!-- Functions Handled -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Functions Handled</label>
                                        <textarea class="form-control" rows="2" [(ngModel)]="shareholder.functionsHandled"
                                                  [name]="'functionsHandled_' + i" placeholder="Describe functions and responsibilities"></textarea>
                                      </div>

                                      <!-- Years in Real Estate -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">No Of Years In Real Estate</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.yearsInRealEstate"
                                               [name]="'yearsInRealEstate_' + i" placeholder="Enter years">
                                      </div>

                                      <!-- Years in This Group -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">No Of Years In This Group</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.yearsInGroup"
                                               [name]="'yearsInGroup_' + i" placeholder="Enter years">
                                      </div>

                                      <!-- Father's Name -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Father's Name</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.fatherName"
                                               [name]="'fatherName_' + i" placeholder="Enter father's name">
                                      </div>

                                      <!-- Mother's Name -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Mother's Name</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.motherName"
                                               [name]="'motherName_' + i" placeholder="Enter mother's name">
                                      </div>

                                      <!-- Mother's Maiden Name -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Mother's Maiden Name</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.motherMaidenName"
                                               [name]="'motherMaidenName_' + i" placeholder="Enter mother's maiden name">
                                      </div>

                                      <!-- Spouse's Name -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Spouse's Name</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.spouseName"
                                               [name]="'spouseName_' + i" placeholder="Enter spouse's name">
                                      </div>

                                      <!-- Children's Names -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Children's Names</label>
                                        <textarea class="form-control" rows="2" [(ngModel)]="shareholder.childrenNames"
                                                  [name]="'childrenNames_' + i" placeholder="Enter children's names (separated by commas)"></textarea>
                                      </div>

                                      <!-- Contact Number -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Contact Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" [(ngModel)]="shareholder.contactNumber"
                                               [name]="'contactNumber_' + i" placeholder="Enter contact number">
                                      </div>

                                      <!-- Email ID -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Email ID</label>
                                        <input type="email" class="form-control" [(ngModel)]="shareholder.emailId"
                                               [name]="'emailId_' + i" placeholder="Enter email address">
                                      </div>

                                      <!-- Residential Address -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Residential Address</label>
                                        <textarea class="form-control" rows="3" [(ngModel)]="shareholder.residentialAddress"
                                                  [name]="'residentialAddress_' + i" placeholder="Enter complete residential address"></textarea>
                                      </div>

                                      <!-- Latest Net Worth -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Latest Net Worth (INR Cr)</label>
                                        <input type="text" class="form-control" [(ngModel)]="shareholder.latestNetWorth"
                                               [name]="'latestNetWorth_' + i" placeholder="Enter net worth in crores">
                                      </div>

                                      <!-- Detailed Profile -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Detailed Profile Including Educational Qualification & Business Background</label>
                                        <textarea class="form-control" rows="4" [(ngModel)]="shareholder.detailedProfile"
                                                  [name]="'detailedProfile_' + i" placeholder="Enter detailed profile including education and business background"></textarea>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                                </div>
                              </div>

                              <!-- STATEMENT SHOWING OTHER KEY PERSONS OF THE GROUP Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="user-check"
                                        class="icon-sm me-2" appFeatherIcon></i> STATEMENT SHOWING OTHER KEY PERSONS OF THE GROUP</h6>
                                    <button type="button" class="btn btn-primary btn-sm" (click)="addKeyPerson()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Key Person
                                    </button>
                                  </div>
                                </div>
                                <div class="card-body">

                              <!-- Key Person Forms -->
                              <div *ngFor="let keyPerson of keyPersons; let i = index" class="mb-4">
                                <div class="card shareholder-card">
                                  <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Key Person {{i + 1}}</h6>
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            (click)="removeKeyPerson(i)"
                                            [disabled]="keyPersons.length === 1">
                                      <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                                    </button>
                                  </div>
                                  <div class="card-body">
                                    <div class="row">
                                      <!-- SR NO -->
                                      <div class="col-12 col-md-6 col-lg-2 mb-3">
                                        <label class="form-label">SR NO</label>
                                        <input type="text" class="form-control" [value]="i + 1" readonly>
                                      </div>

                                      <!-- Name of Person -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Name of Person <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" [(ngModel)]="keyPerson.name"
                                               [name]="'keyPersonName_' + i" placeholder="Enter full name">
                                      </div>

                                      <!-- Qualification -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Qualification</label>
                                        <input type="text" class="form-control" [(ngModel)]="keyPerson.qualification"
                                               [name]="'qualification_' + i" placeholder="Enter qualification">
                                      </div>

                                      <!-- Designation -->
                                      <div class="col-12 col-md-6 col-lg-2 mb-3">
                                        <label class="form-label">Designation</label>
                                        <input type="text" class="form-control" [(ngModel)]="keyPerson.designation"
                                               [name]="'designation_' + i" placeholder="Enter designation">
                                      </div>

                                      <!-- Functions Handled -->
                                      <div class="col-12 col-md-6 col-lg-4 mb-3">
                                        <label class="form-label">Functions Handled</label>
                                        <textarea class="form-control" rows="2" [(ngModel)]="keyPerson.functionsHandled"
                                                  [name]="'functionsHandled_' + i" placeholder="Enter functions handled"></textarea>
                                      </div>

                                      <!-- No. of Years in Company/Group -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">No. of Years in this Company / Group</label>
                                        <input type="number" class="form-control" [(ngModel)]="keyPerson.yearsInCompany"
                                               [name]="'yearsInCompany_' + i" placeholder="Enter years">
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                                </div>
                              </div>

                              <!-- BANK DETAILS Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="credit-card"
                                        class="icon-sm me-2" appFeatherIcon></i> BANK DETAILS - COMPANY, DIRECTORS / PARTNERS / PROPRIETOR</h6>
                                    <button type="button" class="btn btn-primary btn-sm" (click)="addBankDetail()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Bank Detail
                                    </button>
                                  </div>
                                </div>
                                <div class="card-body">

                              <!-- Bank Detail Forms -->
                              <div *ngFor="let bankDetail of bankDetails; let i = index" class="mb-4">
                                <div class="card shareholder-card">
                                  <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Bank Detail {{i + 1}}</h6>
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            (click)="removeBankDetail(i)"
                                            [disabled]="bankDetails.length === 1">
                                      <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                                    </button>
                                  </div>
                                  <div class="card-body">
                                    <div class="row">
                                      <!-- SR NO -->
                                      <div class="col-12 col-md-6 col-lg-2 mb-3">
                                        <label class="form-label">SR NO</label>
                                        <input type="text" class="form-control" [value]="i + 1" readonly>
                                      </div>

                                      <!-- Name of Bank / HFC -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Name of Bank / HFC <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" [(ngModel)]="bankDetail.bankName"
                                               [name]="'bankName_' + i" placeholder="Enter bank/HFC name">
                                      </div>

                                      <!-- Name of Account Holder -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Name of Account Holder <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" [(ngModel)]="bankDetail.accountHolderName"
                                               [name]="'accountHolderName_' + i" placeholder="Enter account holder name">
                                      </div>

                                      <!-- Type of Account -->
                                      <div class="col-12 col-md-6 col-lg-2 mb-3">
                                        <label class="form-label">Type of Account</label>
                                        <select class="form-select" [(ngModel)]="bankDetail.accountType"
                                                [name]="'accountType_' + i">
                                          <option value="" selected disabled>Select type</option>
                                          <option value="Savings">Savings</option>
                                          <option value="Current">Current</option>
                                          <option value="CC/OD">CC/OD</option>
                                          <option value="Term Loan">Term Loan</option>
                                        </select>
                                      </div>

                                      <!-- Bank Address -->
                                      <div class="col-12 col-md-6 col-lg-4 mb-3">
                                        <label class="form-label">Bank Address</label>
                                        <textarea class="form-control" rows="2" [(ngModel)]="bankDetail.bankAddress"
                                                  [name]="'bankAddress_' + i" placeholder="Enter bank address"></textarea>
                                      </div>

                                      <!-- Account Number -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Account Number <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" [(ngModel)]="bankDetail.accountNumber"
                                               [name]="'accountNumber_' + i" placeholder="Enter account number">
                                      </div>

                                      <!-- Contact Person in Bank -->
                                      <div class="col-12 col-md-6 col-lg-3 mb-3">
                                        <label class="form-label">Contact Person in Bank</label>
                                        <input type="text" class="form-control" [(ngModel)]="bankDetail.contactPerson"
                                               [name]="'contactPerson_' + i" placeholder="Enter contact person name">
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="button" class="btn btn-secondary me-2">
                                  <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i> Cancel
                                </button>
                                <button type="button" class="btn btn-primary" (click)="saveInitialKyc()">
                                  <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                </button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="3">
                        <a ngbNavLink>
                          <i data-feather="users" class="icon-sm me-2" appFeatherIcon></i> Borrower's Details
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <!-- BORROWER'S DETAILS Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="users"
                                      class="icon-sm me-2" appFeatherIcon></i> BORROWER'S DETAILS</h6>
                                </div>
                                <div class="card-body">
                                  <!-- Basic Borrower Information -->
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Name of the Borrower</label>
                                      <input type="text" class="form-control" placeholder="Enter borrower name"
                                        [(ngModel)]="borrowerDetails.name" name="borrowerName">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Constitution of the Borrower</label>
                                      <input type="text" class="form-control" placeholder="Enter constitution"
                                        [(ngModel)]="borrowerDetails.constitution" name="borrowerConstitution">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">No of Partners / Directors</label>
                                      <input type="number" class="form-control" placeholder="Enter number"
                                        [(ngModel)]="borrowerDetails.partnersCount" name="partnersCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">GST Registration No</label>
                                      <input type="text" class="form-control" placeholder="Enter GST number"
                                        [(ngModel)]="borrowerDetails.gstNo" name="gstNo">
                                    </div>
                                  </div>

                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">CIN / LLPIN / ROF No</label>
                                      <input type="text" class="form-control" placeholder="Enter number"
                                        [(ngModel)]="borrowerDetails.cinNo" name="cinNo">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Date of Incorporation</label>
                                      <input type="date" class="form-control"
                                        [(ngModel)]="borrowerDetails.incorporationDate" name="incorporationDate">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">PAN</label>
                                      <input type="text" class="form-control" placeholder="Enter PAN"
                                        [(ngModel)]="borrowerDetails.pan" name="pan">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Company's Email ID</label>
                                      <input type="email" class="form-control" placeholder="Enter email"
                                        [(ngModel)]="borrowerDetails.email" name="companyEmail">
                                    </div>
                                  </div>

                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Company / Project Website</label>
                                      <input type="url" class="form-control" placeholder="Enter website URL"
                                        [(ngModel)]="borrowerDetails.website" name="website">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Total Experience in Real Estate</label>
                                      <input type="text" class="form-control" placeholder="Enter experience"
                                        [(ngModel)]="borrowerDetails.experience" name="experience">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Politically Exposed Person?</label>
                                      <select class="form-select" [(ngModel)]="borrowerDetails.pep" name="pep">
                                        <option value="" selected disabled>Select</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                      </select>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Any Legal OR Criminal Cases?</label>
                                      <select class="form-select" [(ngModel)]="borrowerDetails.legalCases"
                                        name="legalCases">
                                        <option value="" selected disabled>Select</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                      </select>
                                    </div>
                                  </div>

                                  <!-- Address Information -->
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 mb-3">
                                      <label class="form-label">Registered Office</label>
                                      <textarea class="form-control" rows="2" placeholder="Enter address"
                                        [(ngModel)]="borrowerDetails.registeredOffice"
                                        name="registeredOffice"></textarea>
                                    </div>
                                    <div class="col-12 col-sm-6 mb-3">
                                      <label class="form-label">Corporate Office</label>
                                      <textarea class="form-control" rows="2" placeholder="Enter address"
                                        [(ngModel)]="borrowerDetails.corporateOffice" name="corporateOffice"></textarea>
                                    </div>
                                  </div>

                                  <!-- About and History -->
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 mb-3">
                                      <label class="form-label">About The Borrower</label>
                                      <textarea class="form-control" rows="3" placeholder="Enter details"
                                        [(ngModel)]="borrowerDetails.about" name="aboutBorrower"></textarea>
                                    </div>
                                    <div class="col-12 col-sm-6 mb-3">
                                      <label class="form-label">History of any Default / Delinquency in the past 2
                                        years</label>
                                      <textarea class="form-control" rows="3" placeholder="Enter details"
                                        [(ngModel)]="borrowerDetails.defaultHistory" name="defaultHistory"></textarea>
                                    </div>
                                  </div>

                                  <!-- Team Size & Organization Structure -->
                                  <h6 class="section-title mb-3">Team Size & Organization Structure (No of People)</h6>
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Managerial Levels</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.managerial" name="managerialCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Assistant Managers</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.assistantManagers"
                                        name="assistantManagersCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Accounts Dept</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.accountsDept" name="accountsDeptCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Admin Dept</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.adminDept" name="adminDeptCount">
                                    </div>
                                  </div>

                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Sales Dept</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.salesDept" name="salesDeptCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Engineers</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.engineers" name="engineersCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Supervisors</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="borrowerDetails.teamSize.supervisors" name="supervisorsCount">
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- BACKGROUND OF THE GROUP Section -->
                              <div class="card   mb-4">
                                <div class="card-header"
                                  style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                  <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="briefcase"
                                      class="icon-sm me-2" appFeatherIcon></i> BACKGROUND OF THE GROUP</h6>
                                </div>
                                <div class="card-body">
                                  <!-- Basic Group Information -->
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Name of the group</label>
                                      <input type="text" class="form-control" placeholder="Enter group name"
                                        [(ngModel)]="groupBackground.name" name="groupName">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Years in real estate business</label>
                                      <input type="text" class="form-control" placeholder="Enter year/experience"
                                        [(ngModel)]="groupBackground.yearVentured" name="yearVentured">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Main Promoter & age</label>
                                      <input type="text" class="form-control" placeholder="Enter promoter details"
                                        [(ngModel)]="groupBackground.mainPromoter" name="mainPromoter">
                                    </div>
                                    <!-- Localities -->
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Locality in which the construction activity is
                                        concentrated</label>
                                      <textarea class="form-control" rows="2" placeholder="Enter localities"
                                        [(ngModel)]="groupBackground.localities" name="localities"></textarea>
                                    </div>
                                  </div>



                                  <!-- Completed Projects -->
                                  <h6 class="section-title mb-3">Completed Projects by the group</h6>
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">No Of Project</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="groupBackground.completedProjects.count"
                                        name="completedProjectsCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Project Size (Area in sq.ft)</label>
                                      <input type="number" class="form-control" placeholder="Enter area"
                                        [(ngModel)]="groupBackground.completedProjects.area"
                                        name="completedProjectsArea">
                                    </div>
                                  </div>

                                  <!-- Ongoing Projects -->
                                  <h6 class="section-title mb-3">Ongoing Projects by the group</h6>
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">No Of Project</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="groupBackground.ongoingProjects.count" name="ongoingProjectsCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Project Size (Area in sq.ft)</label>
                                      <input type="number" class="form-control" placeholder="Enter area"
                                        [(ngModel)]="groupBackground.ongoingProjects.area" name="ongoingProjectsArea">
                                    </div>
                                  </div>

                                  <!-- Upcoming Projects -->
                                  <h6 class="section-title mb-3">Upcoming Projects by the group</h6>
                                  <div class="row mb-4">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">No Of Project</label>
                                      <input type="number" class="form-control" placeholder="Enter count"
                                        [(ngModel)]="groupBackground.upcomingProjects.count"
                                        name="upcomingProjectsCount">
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                      <label class="form-label">Project Size (Area in sq.ft)</label>
                                      <input type="number" class="form-control" placeholder="Enter area"
                                        [(ngModel)]="groupBackground.upcomingProjects.area" name="upcomingProjectsArea">
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                  <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                </button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="4" id="other-business-details">
                        <a ngbNavLink>
                          <i data-feather="briefcase" class="icon-sm me-2" appFeatherIcon></i> Other Business's Details
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Other Business Details</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openOtherBusinessForm()">
                                      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Other Business Details
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #otherBusinessTable class="bootstrap other-business-table"
                                      [rows]="otherBusinessRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Promoter's Name Column -->
                                      <ngx-datatable-column name="PROMOTER'S NAME" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.promoterName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Company Name Column -->
                                      <ngx-datatable-column name="COMPANY NAME" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.companyName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Constitution Column -->
                                      <ngx-datatable-column name="CONSTITUTION" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.constitution }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Date of Incorporation Column -->
                                      <ngx-datatable-column name="DATE OF INCORPORATION" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.dateOfIncorporation }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- PAN Column -->
                                      <ngx-datatable-column name="PAN" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.pan }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- CIN/GST No Column -->
                                      <ngx-datatable-column name="CIN / GST NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.cinGstNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Location/Brief Address Column -->
                                      <ngx-datatable-column name="LOCATION / BRIEF ADDRESS" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.location }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Line of Activity Column -->
                                      <ngx-datatable-column name="LINE OF ACTIVITY" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.lineOfActivity }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Years of Experience Column -->
                                      <ngx-datatable-column name="NO OF YEARS' EXPERIENCE" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.yearsOfExperience }}
                                            years</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Average Annual Turnover Column -->
                                      <ngx-datatable-column name="AVERAGE ANNUAL TURNOVER" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">₹ {{
                                            formatNumber(row.avgAnnualTurnover) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Average Annual Profit Column -->
                                      <ngx-datatable-column name="AVERAGE ANNUAL PROFIT (%)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.avgAnnualProfit.toFixed(1)
                                            }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="5">
                        <a ngbNavLink>
                          <i data-feather="map-pin" class="icon-sm me-2" appFeatherIcon></i> Project Land Details
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Project Land Details</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openProjectLandDetailForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Land Detail
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #projectLandDetailTable class="bootstrap project-land-detail-table"
                                      [rows]="projectLandDetailRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="50" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Agreement Type Column -->
                                      <ngx-datatable-column name="TYPE OF AGREEMENT" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.agreementType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Document Date Column -->
                                      <ngx-datatable-column name="DOCUMENT DATE" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.documentDate | date:'dd/MM/yyyy' }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Document No Column -->
                                      <ngx-datatable-column name="DOCUMENT NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.documentNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Survey No Column -->
                                      <ngx-datatable-column name="SURVEY NO" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.surveyNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Area (Sq Mtrs) Column -->
                                      <ngx-datatable-column name="AREA (SQ MTRS)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.areaSqMtrs)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Area (Guntha/Acre) Column -->
                                      <ngx-datatable-column name="AREA (GUNTHA/ACRE)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.areaGunthaAcre.toFixed(2) }}
                                            {{ row.areaUnit }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Parties Names Column -->
                                      <ngx-datatable-column
                                        name="NAMES OF PARTIES BETWEEN WHOM AGREEMENT HAS BEEN EXECUTED" [width]="300"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.partiesNames }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Consideration Details Column -->
                                      <ngx-datatable-column
                                        name="ADDITIONAL REMARKS / CONSIDERATION DETAILS (MONETARY & AREA)"
                                        [width]="300" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.considerationDetails }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Footer Template -->
                                      <ngx-datatable-footer>
                                        <ng-template ngx-datatable-footer-template let-rowCount="rowCount"
                                          let-pageSize="pageSize" let-selectedCount="selectedCount"
                                          let-curPage="curPage" let-offset="offset">
                                          <div class="d-flex align-items-center justify-content-between w-100 p-2">
                                            <div>
                                              <strong>Total Land Parcels: {{ projectLandDetailRows.length }}</strong>
                                            </div>
                                            <div>
                                              <strong>Total Area (Sq Mtrs): {{ formatNumber(getTotalAreaSqMtrs())
                                                }}</strong>
                                            </div>
                                            <div>
                                              <strong>Total Area (Acre): {{ getTotalAreaByUnit('Acre').toFixed(2) }}
                                                Acre</strong>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-footer>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="6">
                        <a ngbNavLink>
                          <i data-feather="file" class="icon-sm me-2" appFeatherIcon></i> Sathbara & Mutation Entries
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Sathbara & Mutation Entries</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openSathbaraEntryForm()">
                                      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Entries
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #sathbaraEntriesTable class="bootstrap sathbara-entries-table"
                                      [rows]="sathbaraEntryRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Survey/Hissa No Column -->
                                      <ngx-datatable-column name="SURVEY/HISSA NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.surveyNumber }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Area Column -->
                                      <ngx-datatable-column name="AREA (SQ MTRS)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.area }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Mutation Entries Column -->
                                      <ngx-datatable-column name="MUTATION ENTRIES" [width]="200" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.mutationEntries }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Names of Owners Column -->
                                      <ngx-datatable-column name="NAMES OF OWNERS AS PER 7/12 EXTRACT" [width]="250"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.landOwnerName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Remarks Column -->
                                      <ngx-datatable-column name="REMARKS, IF ANY" [width]="200" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.remarks }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="7">
                        <a ngbNavLink>
                          <i data-feather="check-square" class="icon-sm me-2" appFeatherIcon></i> FSI Calc & Approval
                          Status
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- FSI Calc & Approval Status Content -->
                                <div class="col-12 mb-3">
                                  <label class="form-label">FSI Calculation & Approval Status</label>
                                  <textarea class="form-control" rows="4"
                                    placeholder="Enter FSI calculation & approval status"></textarea>
                                </div>
                              </div>
                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                  <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                </button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="8">
                        <a ngbNavLink>
                          <i data-feather="calendar" class="icon-sm me-2" appFeatherIcon></i> Construction & Payment
                          Schedule
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Construction & Payment Schedule Content -->
                                <div class="col-12 mb-3">
                                  <label class="form-label">Construction & Payment Schedule</label>
                                  <textarea class="form-control" rows="4"
                                    placeholder="Enter construction & payment schedule"></textarea>
                                </div>
                              </div>
                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                  <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                </button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="9" id="group-entities">
                        <a ngbNavLink>
                          <i data-feather="grid" class="icon-sm me-2" appFeatherIcon></i> Group Entities
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Group Entities</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openGroupEntityForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Group Entity
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #groupEntitiesTable class="bootstrap group-entities-table"
                                      [rows]="groupEntitiesRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Company Name Column -->
                                      <ngx-datatable-column name="COMPANY NAME" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.companyName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Constitution Column -->
                                      <ngx-datatable-column name="CONSTITUTION" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.constitution }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Date of Incorporation Column -->
                                      <ngx-datatable-column name="DATE OF INCORPORATION" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.dateOfIncorporation }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- PAN Column -->
                                      <ngx-datatable-column name="PAN" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.pan }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- CIN/GST No Column -->
                                      <ngx-datatable-column name="CIN / GST NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.cinGstNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Registered Office Address Column -->
                                      <ngx-datatable-column name="REGISTERED OFFICE ADDRESS" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.registeredOfficeAddress }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- List of Partners/Directors Column -->
                                      <ngx-datatable-column name="LIST OF PARTNERS / DIRECTORS" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.partnersDirectors }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Profit Sharing Ratio Column -->
                                      <ngx-datatable-column name="PROFIT SHARING RATIO (%)" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.profitSharingRatio
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- DIN Column -->
                                      <ngx-datatable-column name="DIN (IF APPLICABLE)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.din }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Current Projects Column -->
                                      <ngx-datatable-column name="CURRENT PROJECTS" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.currentProjects }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Action Column -->
                                      <ngx-datatable-column name="ACTION" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <button class="btn btn-sm btn-primary" (click)="openGroupEntityForm(row.id)">
                                            <i data-feather="edit-2" class="icon-xs me-1" appFeatherIcon></i> Edit
                                          </button>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="10">
                        <a ngbNavLink>
                          <i data-feather="check-circle" class="icon-sm me-2" appFeatherIcon></i> Completed Projects
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Completed Projects</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openCompletedProjectForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Completed Project
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #completedProjectsTable class="bootstrap completed-projects-table"
                                      [rows]="completedProjectRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Name Column -->
                                      <ngx-datatable-column name="PROJECT NAME" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Entity Name Column -->
                                      <ngx-datatable-column name="ENTITY NAME" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.entityName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Promoters Names Column -->
                                      <ngx-datatable-column name="PROMOTERS NAMES" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.promotersNames }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Profit Sharing Column -->
                                      <ngx-datatable-column name="PROFIT SHARING" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.profitSharing }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Development Type Column -->
                                      <ngx-datatable-column name="DEVELOPMENT TYPE" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.developmentType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Location Column -->
                                      <ngx-datatable-column name="LOCATION" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.location }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Type Column -->
                                      <ngx-datatable-column name="PROJECT TYPE" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Units Column -->
                                      <ngx-datatable-column name="TOTAL UNITS" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.totalUnits }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Construction Area Column -->
                                      <ngx-datatable-column name="CONSTRUCTION AREA" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.constructionArea }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Construction Cost Column -->
                                      <ngx-datatable-column name="CONSTRUCTION COST (₹)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.constructionCost) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sales Value Column -->
                                      <ngx-datatable-column name="SALES VALUE (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.salesValue)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Start Date Column -->
                                      <ngx-datatable-column name="START DATE" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.startDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- End Date Column -->
                                      <ngx-datatable-column name="END DATE" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.endDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Occupancy Received Column -->
                                      <ngx-datatable-column name="OCCUPANCY RECEIVED" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.occupancyReceived }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- RERA Number Column -->
                                      <ngx-datatable-column name="RERA NUMBER" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.reraNumber }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="11">
                        <a ngbNavLink>
                          <i data-feather="activity" class="icon-sm me-2" appFeatherIcon></i> Ongoing Projects
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Ongoing Projects</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openOngoingProjectForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Ongoing Project
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #ongoingProjectsTable class="bootstrap ongoing-projects-table"
                                      [rows]="ongoingProjectRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Name Column -->
                                      <ngx-datatable-column name="PROJECT NAME" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Developing Company Column -->
                                      <ngx-datatable-column name="PROJECT DEVELOPING COMPANY" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.entityName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- MAHARERA NO Column -->
                                      <ngx-datatable-column name="MAHARERA NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.reraNumber }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Location Column -->
                                      <ngx-datatable-column name="LOCATION" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.location }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- S NO & H NO / CTS NO Column -->
                                      <ngx-datatable-column name="S NO & H NO / CTS NO" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.surveyNumbers }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Promoters Names Column -->
                                      <ngx-datatable-column name="PROMOTERS' NAMES" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.promotersNames }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Structure Column -->
                                      <ngx-datatable-column name="ENTIRE PROJECT STRUCTURE" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectStructure }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Construction Approvals Column -->
                                      <ngx-datatable-column name="CURRENT CONSTRUCTION APPROVALS" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.constructionApprovals }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Latest CC Date Column -->
                                      <ngx-datatable-column name="DATE OF LATEST CC" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.latestCCDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Category Column -->
                                      <ngx-datatable-column
                                        name="NORMAL LAYOUT / SOC REDEV / SRA REDEV / MHADA REDEV / CESSED REDEV"
                                        [width]="300" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectCategory }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Ownership Type Column -->
                                      <ngx-datatable-column name="OWNED / JDA / JV" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.ownershipType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Plot Area Column -->
                                      <ngx-datatable-column name="LAND / PLOT AREA" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.plotArea }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Category Column -->
                                      <ngx-datatable-column name="CATEGORY" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.category }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sharing Details Column -->
                                      <ngx-datatable-column name="SHARING DETAILS (%)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.sharingDetails }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Area 1 Column -->
                                      <ngx-datatable-column name="AREA (SQ FT)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.area1)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Units 1 Column -->
                                      <ngx-datatable-column name="UNITS" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.units1 }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Area 2 Column -->
                                      <ngx-datatable-column name="AREA (SQ FT)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.area2)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Units 2 Column -->
                                      <ngx-datatable-column name="UNITS" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.units2 }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Area 3 Column -->
                                      <ngx-datatable-column name="AREA (SQ FT)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.area3)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Units 3 Column -->
                                      <ngx-datatable-column name="UNITS" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.units3 }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sold Area Column -->
                                      <ngx-datatable-column name="SOLD AREA (SQ FT)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.soldArea)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Unsold Area Column -->
                                      <ngx-datatable-column name="UNSOLD AREA (SQ FT)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.unsoldArea)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sold Units Column -->
                                      <ngx-datatable-column name="SOLD UNITS" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.soldUnits }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Unsold Units Column -->
                                      <ngx-datatable-column name="UNSOLD UNITS" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.unsoldUnits }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Estimated Revenue Column -->
                                      <ngx-datatable-column name="TOTAL ESTIMATED REVENUE" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.totalEstimatedRevenue) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Value of Area Unsold Column -->
                                      <ngx-datatable-column name="VALUE OF AREA UNSOLD" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.valueOfAreaUnsold) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Value of Area Sold Column -->
                                      <ngx-datatable-column name="VALUE OF AREA SOLD" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.valueOfAreaSold) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Receipts from Sold Area Column -->
                                      <ngx-datatable-column name="RECEIPTS FROM SOLD AREA" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.receiptsFromSoldArea) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Receivables from Sold Area Column -->
                                      <ngx-datatable-column name="RECEIVABLES FROM SOLD AREA" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.receivablesFromSoldArea) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Cost Column -->
                                      <ngx-datatable-column name="TOTAL" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.totalCost)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Cost Incurred Column -->
                                      <ngx-datatable-column name="COST INCURRED" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.costIncurred)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Cost To Be Incurred Column -->
                                      <ngx-datatable-column name="COST TO BE INCURRED" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.costToBeIncurred) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Promoter's Skin (Infusion) Column -->
                                      <ngx-datatable-column name="PROMOTER'S SKIN (INFUSION)" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.promoterSkinInfusion) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Profit (Rs in Crs) Column -->
                                      <ngx-datatable-column name="PROFIT (RS IN CRS)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.profitInCrores }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Profit % on Revenue Column -->
                                      <ngx-datatable-column name="PROFIT % ON REVENUE" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.profitPercentOnRevenue
                                            }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Surplus (Rs in Crs) Column -->
                                      <ngx-datatable-column name="SURPLUS (RS IN CRS)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.surplusInCrores }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Rate (Sold) Column -->
                                      <ngx-datatable-column name="RATE (SOLD)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.rateSold }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Rate (Unsold) Column -->
                                      <ngx-datatable-column name="RATE (UNSOLD)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.rateUnsold }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- % Completion Column -->
                                      <ngx-datatable-column name="% COMPLETION" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.percentCompletion }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- % Collections Column -->
                                      <ngx-datatable-column name="% COLLECTIONS" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.percentCollections
                                            }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Existing Loan Column -->
                                      <ngx-datatable-column name="EXISTING LOAN (IF ANY)" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.existingLoan }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Bank Name Column -->
                                      <ngx-datatable-column name="NAME OF BANK/FI" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.bankName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sanction Date Column -->
                                      <ngx-datatable-column name="SANCTION DATE" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.sanctionDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- ROI Column -->
                                      <ngx-datatable-column name="ROI (%)" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.rateOfInterest }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Amount Sanctioned Column -->
                                      <ngx-datatable-column name="AMOUNT SANCTIONED" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.amountSanctioned) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Amount Disbursed Column -->
                                      <ngx-datatable-column name="AMOUNT DISBURSED" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.amountDisbursed) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Current Outstanding Amount Column -->
                                      <ngx-datatable-column name="CURRENT OUTSTANDING AMOUNT" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.currentOutstandingAmount) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Morat Period Column -->
                                      <ngx-datatable-column name="MORAT PERIOD" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.moratPeriod }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Repayment Starting Date Column -->
                                      <ngx-datatable-column name="REPAYMENT STARTING DATE" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.repaymentStartingDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Start Date Column -->
                                      <ngx-datatable-column name="START" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectStartDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Launch Date Column -->
                                      <ngx-datatable-column name="LAUNCH" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectLaunchDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Current Physical Stage Column -->
                                      <ngx-datatable-column name="CURRENT PHYSICAL STAGE OF PROJECT (AS ON __/__/____)"
                                        [width]="300" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.currentPhysicalStage }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Completion Date Column -->
                                      <ngx-datatable-column name="COMPLETION" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectCompletionDate }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="12">
                        <a ngbNavLink>
                          <i data-feather="home" class="icon-sm me-2" appFeatherIcon></i> Unsold Stock & Leased Prop
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Unsold Stock & Leased Properties</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openUnsoldStockForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Property
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #unsoldStockTable class="bootstrap unsold-stock-table"
                                      [rows]="unsoldStockRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Name Column -->
                                      <ngx-datatable-column name="PROJECT NAME" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Location Column -->
                                      <ngx-datatable-column name="LOCATION" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.location }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Category Column -->
                                      <ngx-datatable-column name="CATEGORY" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.category }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Saleable Area Column -->
                                      <ngx-datatable-column name="SALEABLE AREA (sq.ft)" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.saleableArea)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Rate Column -->
                                      <ngx-datatable-column name="RATE (₹/sq.ft)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.rate) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Value Column -->
                                      <ngx-datatable-column name="VALUE (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.value)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Debt Column -->
                                      <ngx-datatable-column name="DEBT (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.debt) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Leased Properties Table -->
                            <div class="row mt-4">
                              <div class="col-12 mb-3">
                                <div class="card modern-table-card">
                                  <div class="card-body">
                                    <div class="d-flex align-items-center justify-content-between mb-4">
                                      <h6 class="card-title mb-0">LEASED PROPERTIES</h6>
                                      <button class="btn btn-primary btn-sm" (click)="openLeasedPropertyForm()">
                                        <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                        Add Leased Property
                                      </button>
                                    </div>
                                    <div class="table-responsive">
                                      <ngx-datatable #leasedPropertiesTable class="bootstrap leased-properties-table"
                                        [rows]="leasedPropertyRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                        [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                        <!-- Sr No Column -->
                                        <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                          [resizeable]="false" [canAutoResize]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <div class="text-center">{{ row.id }}</div>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Project Name Column -->
                                        <ngx-datatable-column name="PROJECT NAME" [width]="150" [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-nowrap">{{ row.projectName }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Location Column -->
                                        <ngx-datatable-column name="LOCATION" [width]="180" [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-wrap">{{ row.location }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Area Leased Column -->
                                        <ngx-datatable-column name="AREA LEASED (sq.ft)" [width]="150"
                                          [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-nowrap text-end d-block">{{ formatNumber(row.areaLeased)
                                              }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Lease Rent P.A. Column -->
                                        <ngx-datatable-column name="LEASE RENT P.A. (₹)" [width]="150"
                                          [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-nowrap text-end d-block">{{ formatNumber(row.leaseRentPA)
                                              }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Security Deposit Column -->
                                        <ngx-datatable-column name="SECURITY DEPOSIT (₹)" [width]="150"
                                          [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-nowrap text-end d-block">{{
                                              formatNumber(row.securityDeposit) }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Debt O/S Column -->
                                        <ngx-datatable-column name="DEBT O/S (₹)" [width]="150" [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-nowrap text-end d-block">{{ formatNumber(row.debtOS)
                                              }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>

                                        <!-- Market Value Column -->
                                        <ngx-datatable-column name="MARKET VALUE (₹)" [width]="150" [sortable]="false">
                                          <ng-template let-row="row" ngx-datatable-cell-template>
                                            <span class="text-nowrap text-end d-block">{{ formatNumber(row.marketValue)
                                              }}</span>
                                          </ng-template>
                                        </ngx-datatable-column>
                                      </ngx-datatable>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="13">
                        <a ngbNavLink>
                          <i data-feather="layers" class="icon-sm me-2" appFeatherIcon></i> Land Bank & Upcoming
                          Projects
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Land Bank & Upcoming Projects</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openLandBankForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add New Project
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #landBankTable class="bootstrap land-bank-table"
                                      [rows]="landBankRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="50" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Name Column -->
                                      <ngx-datatable-column name="NAME OF THE PROJECT" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Entity Name Column -->
                                      <ngx-datatable-column name="NAME OF THE ENTITY" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.entityName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Ownership Type Column -->
                                      <ngx-datatable-column name="OWNED / JDA / JV" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.ownershipType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Documentation Status Column -->
                                      <ngx-datatable-column name="DOCUMENTATION / APPROVAL STATUS" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.documentationStatus }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Location Column -->
                                      <ngx-datatable-column name="LOCATION" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.location }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Project Type Column -->
                                      <ngx-datatable-column name="RESI / COMM / MIXED" [width]="130" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.projectType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Expected Start Date Column -->
                                      <ngx-datatable-column name="EXPECTED START DATE" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.expectedStartDate | date:'dd/MM/yyyy'
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Residential Area Column -->
                                      <ngx-datatable-column name="AREA (SQ FT)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.residentialArea) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Residential Units Column -->
                                      <ngx-datatable-column name="UNITS" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.residentialUnits }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Commercial Area Column -->
                                      <ngx-datatable-column name="AREA (SQ FT)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.commercialArea)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Commercial Units Column -->
                                      <ngx-datatable-column name="UNITS" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.commercialUnits }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Mixed Area Column -->
                                      <ngx-datatable-column name="AREA (SQ FT)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.mixedArea)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Mixed Units Column -->
                                      <ngx-datatable-column name="UNITS" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.mixedUnits }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Footer Template -->
                                      <ngx-datatable-footer>
                                        <ng-template ngx-datatable-footer-template let-rowCount="rowCount"
                                          let-pageSize="pageSize" let-selectedCount="selectedCount"
                                          let-curPage="curPage" let-offset="offset">
                                          <div class="d-flex align-items-center justify-content-between w-100 p-2">
                                            <div>
                                              <strong>Total Projects: {{ landBankRows.length }}</strong>
                                            </div>
                                            <div>
                                              <strong>Total Residential Area: {{ formatNumber(getTotalResidentialArea())
                                                }} sq.ft</strong>
                                            </div>
                                            <div>
                                              <strong>Total Commercial Area: {{ formatNumber(getTotalCommercialArea())
                                                }} sq.ft</strong>
                                            </div>
                                            <div>
                                              <strong>Total Mixed Area: {{ formatNumber(getTotalMixedArea()) }}
                                                sq.ft</strong>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-footer>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Land Bank Details Table -->
                            <div class="col-12 mb-3 mt-4">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Land Bank Details</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openLandBankDetailForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add New Land Detail
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #landBankDetailTable class="bootstrap land-bank-detail-table"
                                      [rows]="landBankDetailRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="50" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Company Name Column -->
                                      <ngx-datatable-column name="COMPANY / FIRM / INDIVIDUAL" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.companyName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Location Column -->
                                      <ngx-datatable-column name="LOCATION" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.location }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Land Type Column -->
                                      <ngx-datatable-column name="AGRI/NON-AGRI" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.landType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Ownership Type Column -->
                                      <ngx-datatable-column name="OWNED / JDA" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.ownershipType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Plot Area Column -->
                                      <ngx-datatable-column name="PLOT AREA" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ formatPlotArea(row.plotArea, row.plotAreaUnit)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Acquisition Year Column -->
                                      <ngx-datatable-column name="YEAR OF ACQUISITION" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.acquisitionYear }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Purchase Value Column -->
                                      <ngx-datatable-column name="BOOK / PURCHASE VALUE" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.purchaseValue)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Market Value Column -->
                                      <ngx-datatable-column name="MARKET VALUE" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.marketValue)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Has Loan Column -->
                                      <ngx-datatable-column name="ANY LOAN OBTAINED?" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="badge rounded-pill" [ngClass]="{
                                          'bg-success': row.hasLoan === 'No',
                                          'bg-warning text-dark': row.hasLoan === 'Yes'
                                        }">{{ row.hasLoan }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Approval Status Column -->
                                      <ngx-datatable-column name="APPROVAL STATUS" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.approvalStatus }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Expected Launch Date Column -->
                                      <ngx-datatable-column name="EXPECTED LAUNCH DATE" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.expectedLaunchDate | date:'dd/MM/yyyy'
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Footer Template -->
                                      <ngx-datatable-footer>
                                        <ng-template ngx-datatable-footer-template let-rowCount="rowCount"
                                          let-pageSize="pageSize" let-selectedCount="selectedCount"
                                          let-curPage="curPage" let-offset="offset">
                                          <div class="d-flex align-items-center justify-content-between w-100 p-2">
                                            <div>
                                              <strong>Total Land Parcels: {{ landBankDetailRows.length }}</strong>
                                            </div>
                                            <div>
                                              <strong>Total Purchase Value: {{ formatNumber(getTotalPurchaseValue())
                                                }}</strong>
                                            </div>
                                            <div>
                                              <strong>Total Market Value: {{ formatNumber(getTotalMarketValue())
                                                }}</strong>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-footer>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="14">
                        <a ngbNavLink>
                          <i data-feather="clipboard" class="icon-sm me-2" appFeatherIcon></i> Projects Factsheet
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Projects Factsheet Content -->
                                <div class="col-12 mb-3">
                                  <label class="form-label">Projects Factsheet Details</label>
                                  <textarea class="form-control" rows="4"
                                    placeholder="Enter projects factsheet details"></textarea>
                                </div>
                              </div>
                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary me-2">Save</button>
                                <button type="button" class="btn btn-secondary">Cancel</button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="15">
                        <a ngbNavLink>
                          <i data-feather="shopping-bag" class="icon-sm me-2" appFeatherIcon></i> Sales MIS & Inventory
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <!-- Sales MIS & Inventory Summary Table -->
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Sales MIS & Inventory Summary</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openSalesMisSummaryForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Summary Entry
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #salesMisSummaryTable class="bootstrap" [rows]="salesMisSummaryRows"
                                      [columnMode]="ColumnMode.force" [headerHeight]="50" [rowHeight]="'auto'"
                                      [scrollbarH]="true">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Saleable Built-Up Area Column -->
                                      <ngx-datatable-column name="SALEABLE BUILT-UP AREA" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.saleableBuiltUpArea) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- MAHARERA Carpet Area (Sq.Mt.) Column -->
                                      <ngx-datatable-column name="MAHARERA CARPET AREA (Sq.Mt.)" [width]="220"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.mahareraCarpetAreaSqMt) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Carpet Area (Sq.Ft.) Column -->
                                      <ngx-datatable-column name="CARPET AREA (Sq.Ft.)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.carpetAreaSqFt)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Number of Flats Column -->
                                      <ngx-datatable-column name="NUMBER OF FLATS" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.numberOfFlats)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Number of Shops Column -->
                                      <ngx-datatable-column name="NUMBER OF SHOPS" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.numberOfShops)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Number of Offices Column -->
                                      <ngx-datatable-column name="NUMBER OF OFFICES" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.numberOfOffices) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Refuge Units Column -->
                                      <ngx-datatable-column name="REFUGE UNITS" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.refugeUnits)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Achieved For Sold Area Column Group -->
                                      <ngx-datatable-column [name]="'Achieved For Sold Area (as per Carpet Area)'"
                                        [width]="500" [sortable]="false">
                                        <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-row="row">
                                          <div class="d-flex">

                                            <div class="text-end" style="width: 140px;">
                                              <span class="text-nowrap">{{ formatNumber(row.saleRateResi) }}</span>
                                            </div>


                                            <div class="text-end" style="width: 250px;">
                                              <span class="text-nowrap">{{ formatNumber(row.saleRateGrFloorComm)
                                                }}</span>
                                            </div>


                                            <div class="text-end" style="width: 250px;">
                                              <span class="text-nowrap">{{ formatNumber(row.saleRate1stFloorComm)
                                                }}</span>
                                            </div>
                                          </div>
                                        </ng-template>

                                        <!-- Header Template for Subcolumns -->
                                        <ng-template ngx-datatable-header-template>
                                          <div class="d-flex">
                                            <div class="text-center" style="width: 500px;">
                                              <span>Achieved For Sold Area (as per Carpet Area)</span>
                                              <div class="d-flex mt-2">
                                                <div class="text-center" style="width: 140px;">Sale Rate - Resi</div>
                                                <div class="text-center" style="width: 180px;">Sale Rate - Gr. Floor
                                                  Comm</div>
                                                <div class="text-center" style="width: 180px;">Sale Rate - 1st Floor
                                                  comm</div>
                                              </div>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Target For Unsold Area Column Group -->
                                      <ngx-datatable-column [name]="'Target For Unsold Area (as per Carpet Area)'"
                                        [width]="500" [sortable]="false">
                                        <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-row="row">
                                          <div class="d-flex">
                                            <!-- Sale Rate - Resi -->
                                            <div class="text-end" style="width: 140px; padding-right: 10px;">
                                              <span class="text-nowrap">{{ formatNumber(row.saleRateResiTarget)
                                                }}</span>
                                            </div>

                                            <!-- Sale Rate - Gr. Floor Comm -->
                                            <div class="text-end" style="width: 180px; padding-right: 10px;">
                                              <span class="text-nowrap">{{ formatNumber(row.saleRateGrFloorCommTarget)
                                                }}</span>
                                            </div>

                                            <!-- Sale Rate - 1st Floor comm -->
                                            <div class="text-end" style="width: 180px; padding-right: 10px;">
                                              <span class="text-nowrap">{{ formatNumber(row.saleRate1stFloorCommTarget)
                                                }}</span>
                                            </div>
                                          </div>
                                        </ng-template>

                                        <!-- Header Template for Subcolumns -->
                                        <ng-template ngx-datatable-header-template>
                                          <div class="d-flex">
                                            <div class="text-center" style="width: 500px;">
                                              <span>Target For Unsold Area (as per Carpet Area)</span>
                                              <div class="d-flex mt-2">
                                                <div class="text-center" style="width: 140px;">Sale Rate - Resi</div>
                                                <div class="text-center" style="width: 180px;">Sale Rate - Gr. Floor
                                                  Comm</div>
                                                <div class="text-center" style="width: 180px;">Sale Rate - 1st Floor
                                                  comm</div>
                                              </div>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Sales MIS & Inventory Detailed Table -->
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Sales MIS & Inventory Details</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openSalesMisForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Entry
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #salesMisTable class="bootstrap" [rows]="salesMisRows"
                                      [columnMode]="ColumnMode.force" [headerHeight]="50" [rowHeight]="'auto'"
                                      [scrollbarH]="true" [footerHeight]="50"
                                      (activate)="onSalesMisRowActivate($event)">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="70" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Building/Wing Name Column -->
                                      <ngx-datatable-column name="BUILDING NO / WING NAME" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.buildingWingName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Floor No Column -->
                                      <ngx-datatable-column name="FLOOR NO" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.floorNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Unit No Column -->
                                      <ngx-datatable-column name="UNIT NO" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.unitNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- MAHARERA Carpet Area Column -->
                                      <ngx-datatable-column name="MAHARERA CARPET AREA" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.mahareraCarpetArea) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Carpet Area Column -->
                                      <ngx-datatable-column name="CARPET AREA" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.carpetArea)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Saleable Built Up Area Column -->
                                      <ngx-datatable-column name="SALEABLE BUILT UP AREA" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.saleableBuiltUpArea) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Unit Type Column -->
                                      <ngx-datatable-column name="TYPE OF UNIT (COMM / RESI)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.unitType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Owner Type Column -->
                                      <ngx-datatable-column name="DEVELOPER / LANDOWNER / TENANT" [width]="220"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.ownerType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Undivided Share of Land Column -->
                                      <ngx-datatable-column name="UNDIVIDED SHARE OF LAND" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.undividedShareOfLand) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Extra Carpet Area Sold To Existing Tenants Column -->
                                      <ngx-datatable-column name="EXTRA CARPET AREA SOLD TO EXISTING TENANTS"
                                        [width]="300" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.extraCarpetAreaSoldToExistingTenants) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Extra Saleable Area Sold To Existing Tenants Column -->
                                      <ngx-datatable-column name="EXTRA SALEABLE AREA SOLD TO EXISTING TENANTS"
                                        [width]="300" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.extraSaleableAreaSoldToExistingTenants) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sale Status Column -->
                                      <ngx-datatable-column name="SOLD / UNSOLD / MORTGAGED" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.saleStatus }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Buyer Name Column -->
                                      <ngx-datatable-column name="NAME OF THE BUYER" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.buyerName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Contact No Column -->
                                      <ngx-datatable-column name="CONTACT NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.contactNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Email ID Column -->
                                      <ngx-datatable-column name="EMAIL ID" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.emailId }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Date of Booking Column -->
                                      <ngx-datatable-column name="DATE OF BOOKING" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.dateOfBooking }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Date of Registration Column -->
                                      <ngx-datatable-column name="DATE OF REGISTRATION" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.dateOfRegistration }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Registration/Agreement No Column -->
                                      <ngx-datatable-column name="REGISTRATION / AGREEMENT NO" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.registrationNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Basic Cost Column -->
                                      <ngx-datatable-column name="BASIC COST" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.basicCost)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Development Cost Components Column -->
                                      <ngx-datatable-column name="DEVELOPMENT COST COMPONENTS" [width]="220"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.developmentCostComponents) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Value of Unit Column -->
                                      <ngx-datatable-column name="TOTAL VALUE OF UNIT" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.totalValueOfUnit) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Demand Raised Column -->
                                      <ngx-datatable-column name="DEMAND RAISED" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.demandRaised)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Amount Received Column -->
                                      <ngx-datatable-column name="AMOUNT RECEIVED" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.amountReceived)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Balance Column -->
                                      <ngx-datatable-column name="BALANCE" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.balance)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Stage of Construction Column -->
                                      <ngx-datatable-column name="STAGE OF CONSTRUCTION" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.stageOfConstruction }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Collections Column -->
                                      <ngx-datatable-column name="COLLECTIONS" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.collections)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Home Loan Financier's Name Column -->
                                      <ngx-datatable-column name="HOME LOAN FINANCIER'S NAME" [width]="200"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.homeLoanFinancierName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- NOC Issued By Earlier Financier Column -->
                                      <ngx-datatable-column
                                        name="NOC ISSUED BY EARLIER FINANCIER IN CASE OF BT TAKE OVER" [width]="300"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.nocIssuedByEarlierFinancier }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Type of Customer Column -->
                                      <ngx-datatable-column name="TYPE OF CUSTOMER" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.typeOfCustomer }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="16" id="sales-plan">
                        <a ngbNavLink>
                          <i data-feather="trending-up" class="icon-sm me-2" appFeatherIcon></i> Sales Plan
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Sales Plan</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openSalesPlanForm()">
                                      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Sales Plan
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #salesPlanTable class="bootstrap" [rows]="salesPlanRows"
                                      [columnMode]="ColumnMode.force" [headerHeight]="50" [rowHeight]="'auto'"
                                      [scrollbarH]="true" [footerHeight]="0">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="Sr. No." [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Particulars Column -->
                                      <ngx-datatable-column name="Particulars" [width]="250" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          {{ row.particulars }}
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 1 Column -->
                                      <ngx-datatable-column name="Q1 (Jun-23)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q1) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 2 Column -->
                                      <ngx-datatable-column name="Q2 (Sep-23)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q2) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 3 Column -->
                                      <ngx-datatable-column name="Q3 (Dec-23)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q3) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 4 Column -->
                                      <ngx-datatable-column name="Q4 (Mar-24)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q4) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 5 Column -->
                                      <ngx-datatable-column name="Q5 (Jun-24)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q5) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 6 Column -->
                                      <ngx-datatable-column name="Q6 (Sep-24)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q6) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 7 Column -->
                                      <ngx-datatable-column name="Q7 (Dec-24)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q7) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 8 Column -->
                                      <ngx-datatable-column name="Q8 (Mar-25)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q8) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Quarter 9 Column -->
                                      <ngx-datatable-column name="Q9 (Jun-25)" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ formatNumber(row.q9) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Column -->
                                      <ngx-datatable-column name="Total" [width]="140" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="sales-plan-value">{{ calculateRowTotal(row) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                  <!-- Form Buttons -->
                                  <div class="text-end mt-4">
                                    <button type="button" class="btn btn-primary">
                                      <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="17">
                        <a ngbNavLink>
                          <i data-feather="dollar-sign" class="icon-sm me-2" appFeatherIcon></i> Compiled Cost
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Project Cost Breakdown</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openCompiledCostForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Cost Item
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #compiledCostTable class="bootstrap compiled-cost-table"
                                      [rows]="compiledCostRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="50" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Particulars Column -->
                                      <ngx-datatable-column name="PARTICULARS" [width]="200" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.particulars }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Cost Column -->
                                      <ngx-datatable-column name="TOTAL COST (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.totalCost)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Cost Incurred Column -->
                                      <ngx-datatable-column [name]="'COST INCURRED TILL ' + currentDate + ' (₹)'"
                                        [width]="200" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.costIncurred)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Cost To Be Incurred Column -->
                                      <ngx-datatable-column name="COST TO BE INCURRED (₹)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.costToBeIncurred) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Percentage Column -->
                                      <ngx-datatable-column name="%" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.percentage.toFixed(2)
                                            }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Footer Template -->
                                      <ngx-datatable-footer>
                                        <ng-template ngx-datatable-footer-template let-rowCount="rowCount"
                                          let-pageSize="pageSize" let-selectedCount="selectedCount"
                                          let-curPage="curPage" let-offset="offset">
                                          <div class="d-flex align-items-center w-100 p-2">
                                            <div style="width: 260px;">
                                              <strong>Total:</strong>
                                            </div>
                                            <div style="width: 150px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalCost()) }}</strong>
                                            </div>
                                            <div style="width: 200px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalCostIncurred()) }}</strong>
                                            </div>
                                            <div style="width: 180px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalCostToBeIncurred()) }}</strong>
                                            </div>
                                            <div style="width: 80px; text-align: right;">
                                              <strong>100.00%</strong>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-footer>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Fund Infusion Table -->
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Project Funding Sources</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openFundInfusionForm()">
                                      <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Funding Source
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #fundInfusionTable class="bootstrap fund-infusion-table"
                                      [rows]="fundInfusionRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="50" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Particulars Column -->
                                      <ngx-datatable-column name="PARTICULARS" [width]="200" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.particulars }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total Column -->
                                      <ngx-datatable-column name="TOTAL (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.total)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Infusion Column -->
                                      <ngx-datatable-column name="INFUSION (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.infusion)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Infusion Till Date Column -->
                                      <ngx-datatable-column [name]="'INFUSION TILL ' + currentDate + ' (₹)'"
                                        [width]="200" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.infusionTillDate) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- To Be Infused Column -->
                                      <ngx-datatable-column name="TO BE INFUSED (₹)" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.toBeInfused)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Percentage Column -->
                                      <ngx-datatable-column name="%" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.percentage.toFixed(2)
                                            }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Footer Template -->
                                      <ngx-datatable-footer>
                                        <ng-template ngx-datatable-footer-template let-rowCount="rowCount"
                                          let-pageSize="pageSize" let-selectedCount="selectedCount"
                                          let-curPage="curPage" let-offset="offset">
                                          <div class="d-flex align-items-center w-100 p-2">
                                            <div style="width: 260px;">
                                              <strong>Total:</strong>
                                            </div>
                                            <div style="width: 150px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalFund()) }}</strong>
                                            </div>
                                            <div style="width: 150px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalInfusion()) }}</strong>
                                            </div>
                                            <div style="width: 200px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalInfusionTillDate()) }}</strong>
                                            </div>
                                            <div style="width: 150px; text-align: right;">
                                              <strong>{{ formatNumber(getTotalToBeInfused()) }}</strong>
                                            </div>
                                            <div style="width: 80px; text-align: right;">
                                              <strong>100.00%</strong>
                                            </div>
                                          </div>
                                        </ng-template>
                                      </ngx-datatable-footer>
                                    </ngx-datatable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="18" id="debt-details">
                        <a ngbNavLink>
                          <i data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i> Debt Details
                        </a>
                        <ng-template ngbNavContent>
                          <div class="row">
                            <div class="col-12 mb-3">
                              <div class="card modern-table-card">
                                <div class="card-body">
                                  <div class="d-flex align-items-center justify-content-between mb-4">
                                    <h6 class="card-title mb-0">Debt Details</h6>
                                    <button class="btn btn-primary btn-sm" (click)="openDebtDetailsForm()">
                                      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
                                      Add Debt Details
                                    </button>
                                  </div>
                                  <div class="table-responsive">
                                    <ngx-datatable #debtDetailsTable class="bootstrap debt-details-table"
                                      [rows]="debtDetailsRows" [columnMode]="ColumnMode.force" [headerHeight]="50"
                                      [rowHeight]="'auto'" [scrollbarH]="true" [footerHeight]="0" [limit]="10">
                                      <!-- Sr No Column -->
                                      <ngx-datatable-column name="SR NO" [width]="60" [sortable]="false"
                                        [resizeable]="false" [canAutoResize]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <div class="text-center">{{ row.id }}</div>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Borrower's Name Column -->
                                      <ngx-datatable-column name="BORROWER'S NAME" [width]="180" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.borrowerName }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Entity Type Column -->
                                      <ngx-datatable-column name="ENTITY / INDIVIDUAL" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.entityType }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Name of Lender Column -->
                                      <ngx-datatable-column name="NAME OF LENDER" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.nameOfLender }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Type of Facility Column -->
                                      <ngx-datatable-column name="TYPE OF FACILITY" [width]="140" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.typeOfFacility }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Loan Account No Column -->
                                      <ngx-datatable-column name="LOAN ACCOUNT NO" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.loanAccountNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Individual Guarantee Column -->
                                      <ngx-datatable-column name="INDIVIDUAL GUARANTEE" [width]="120"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.individualGuarantee }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Date of Sanction Column -->
                                      <ngx-datatable-column name="DATE OF SANCTION" [width]="130" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ formatDateForDisplay(row.dateOfSanction)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Date of Disbursement Column -->
                                      <ngx-datatable-column name="DATE OF DISBURSEMENT" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ formatDateForDisplay(row.dateOfDisbursement)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Sanctioned Amount Column -->
                                      <ngx-datatable-column name="SANCTIONED AMOUNT (IN ₹ CRS)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.sanctionedAmount) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Disbursed Amount Column -->
                                      <ngx-datatable-column name="DISBURSED AMOUNT" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.disbursedAmount) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Utilized Column -->
                                      <ngx-datatable-column name="UTILIZED" [width]="130" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.utilized)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- ROA Column -->
                                      <ngx-datatable-column name="ROA" [width]="80" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ row.roa ? row.roa.toFixed(2) :
                                            '0.00' }}%</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Repaid Column -->
                                      <ngx-datatable-column name="REPAID" [width]="130" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.repaid)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- EMI Amount Column -->
                                      <ngx-datatable-column name="EMI AMOUNT" [width]="130" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.emiAmount)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Repayment Bank Account No Column -->
                                      <ngx-datatable-column name="REPAYMENT BANK ACCOUNT NO" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.repaymentBankAccountNo }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Total EMI Column -->
                                      <ngx-datatable-column name="TOTAL EMI" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.totalEmi }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- EMI Paid Column -->
                                      <ngx-datatable-column name="EMI PAID" [width]="100" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-center d-block">{{ row.emiPaid }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Origin Fees Column -->
                                      <ngx-datatable-column name="ORIGIN FEES" [width]="130" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.originFees)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Current Outstanding Column -->
                                      <ngx-datatable-column name="CURRENT OUTSTANDING" [width]="150" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{
                                            formatNumber(row.currentOutstanding) }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Loan Tenor Column -->
                                      <ngx-datatable-column name="LOAN TENOR" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.loanTenor }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Moratorium Column -->
                                      <ngx-datatable-column name="MORATORIUM" [width]="120" [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap">{{ row.moratorium }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Details of Security Created Column -->
                                      <ngx-datatable-column name="DETAILS OF SECURITY CREATED" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.detailsOfSecurityCreated }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Overdue Amount Column -->
                                      <ngx-datatable-column name="OVERDUE AMOUNT (IN ₹)" [width]="150"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-nowrap text-end d-block">{{ formatNumber(row.overdueAmount)
                                            }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Details of Default Column -->
                                      <ngx-datatable-column name="DETAILS OF DEFAULT (IF ANY)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.detailsOfDefault }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>

                                      <!-- Remarks for Delay Column -->
                                      <ngx-datatable-column name="REMARKS FOR DELAY (IF ANY)" [width]="180"
                                        [sortable]="false">
                                        <ng-template let-row="row" ngx-datatable-cell-template>
                                          <span class="text-wrap">{{ row.remarksForDelay }}</span>
                                        </ng-template>
                                      </ngx-datatable-column>
                                    </ngx-datatable>
                                  </div>
                                  <!-- Form Buttons -->
                                  <div class="text-end mt-4">
                                    <button type="button" class="btn btn-primary">
                                      <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                      </li>

                      <li [ngbNavItem]="19">
                        <a ngbNavLink>
                          <i data-feather="map" class="icon-sm me-2" appFeatherIcon></i> Google Map
                        </a>
                        <ng-template ngbNavContent>
                          <div>
                            <form class="forms-sample">
                              <div class="row">
                                <!-- Google Map Content -->
                                <div class="col-12 mb-3">
                                  <label class="form-label">Google Map Link</label>
                                  <input type="text" class="form-control" placeholder="Enter Google Map link">
                                </div>
                              </div>
                              <!-- Form Buttons -->
                              <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                  <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                                </button>
                              </div>
                            </form>
                          </div>
                        </ng-template>
                      </li>


                      <!-- Summary Tab -->
                      <li [ngbNavItem]="20">
                        <a ngbNavLink>
                          <i data-feather="clipboard" class="icon-sm me-2" appFeatherIcon></i> Summary
                        </a>
                        <ng-template ngbNavContent>
                          <div class="cam-summary-content">
                            <!-- Proposal Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="proposalAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="proposalHeading">
                                  <button class="accordion-button" type="button" (click)="toggleProposal()"
                                    [class.collapsed]="!proposalExpanded" [attr.aria-expanded]="proposalExpanded"
                                    aria-controls="proposalCollapse">
                                    <i data-feather="file-text" class="icon-sm me-2" appFeatherIcon></i> Proposal
                                  </button>
                                </h2>
                                <div id="proposalCollapse" class="accordion-collapse collapse"
                                  [class.show]="proposalExpanded" aria-labelledby="proposalHeading">
                                  <div class="accordion-body">
                                    <!-- PROJECT INFORMATION Section -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="file-text"
                                            class="icon-sm me-2" appFeatherIcon></i> PROJECT INFORMATION</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Project Basic Details -->
                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Project Owning Entity</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Project Name</label>
                                            <input type="text" class="form-control" value="Sunrise Heights">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">MAHARERA NO</label>
                                            <input type="text" class="form-control" value="P51800033729">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Project Location</label>
                                            <input type="text" class="form-control" value="Andheri East, Mumbai">
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- LOAN DETAILS Section -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i> LOAN
                                          DETAILS</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Type of Loan</label>
                                            <input type="text" class="form-control" value="Construction Funding">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Amount Requested (INR Crs)</label>
                                            <input type="number" class="form-control" value="25">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Purpose / End Use</label>
                                            <input type="text" class="form-control" value="Construction Funding">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">First Tranche (Disbursement INR Crs)</label>
                                            <input type="number" class="form-control" value="10">
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- PURPOSE BREAKDOWN Section -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="pie-chart"
                                            class="icon-sm me-2" appFeatherIcon></i> PURPOSE BREAKDOWN</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Approval</label>
                                            <input type="text" class="form-control" value="2.5 Crores">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Land Cost</label>
                                            <input type="text" class="form-control" value="5 Crores">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Partner Exit</label>
                                            <input type="text" class="form-control" value="2.5 Crores">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Construction</label>
                                            <input type="text" class="form-control" value="15 Crores">
                                          </div>
                                        </div>

                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Takeout for other project</label>
                                            <input type="text" class="form-control" value="None">
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- LOAN TERMS Section -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="calendar"
                                            class="icon-sm me-2" appFeatherIcon></i> LOAN TERMS</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Principal Moratorium Period (Months)</label>
                                            <input type="number" class="form-control" value="6">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">EMI payment period (Months)</label>
                                            <input type="number" class="form-control" value="42">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Door to Door (Total) Tenure (Months)</label>
                                            <input type="number" class="form-control" value="48">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Rate of Interest (%)</label>
                                            <input type="number" step="0.01" class="form-control" value="12.00">
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- COLLATERAL SECURITY OFFERED Section -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="shield"
                                            class="icon-sm me-2" appFeatherIcon></i> COLLATERAL SECURITY OFFERED</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Name of the Owner</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control" value="Andheri East, Mumbai">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Area</label>
                                            <input type="text" class="form-control" value="5,000 Sq Mt">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Nature of Security</label>
                                            <input type="text" class="form-control" value="NA">
                                          </div>
                                        </div>

                                        <div class="row mb-4">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Type of Transaction</label>
                                            <input type="text" class="form-control" value="Owned">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Book / Purchase Value (INR Crs)</label>
                                            <input type="number" class="form-control" value="30">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Approx Value of Security as per latest valuation
                                              (INR Crs)</label>
                                            <input type="number" class="form-control" value="40">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Any Loans on the Said Property?</label>
                                            <select class="form-control">
                                              <option value="No" selected>No</option>
                                              <option value="Yes">Yes</option>
                                            </select>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Initial KYC Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="initialKycAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="initialKycHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleInitialKyc()"
                                    [class.collapsed]="!initialKycExpanded" [attr.aria-expanded]="initialKycExpanded"
                                    aria-controls="initialKycCollapse">
                                    <i data-feather="user-check" class="icon-sm me-2" appFeatherIcon></i> Initial KYC
                                  </button>
                                </h2>
                                <div id="initialKycCollapse" class="accordion-collapse collapse"
                                  [class.show]="initialKycExpanded" aria-labelledby="initialKycHeading">
                                  <div class="accordion-body">
                                    <!-- Initial KYC Content -->
                                    <form class="forms-sample">
                                      <div class="row">
                                        <!-- Number of Partners/Directors -->
                                        <div class="col-md-6 mb-3">
                                          <label class="form-label">No of Partners / Directors</label>
                                          <input type="text" class="form-control" [(ngModel)]="numberOfPartners"
                                                 name="numberOfPartners" placeholder="Enter number of partners/directors">
                                        </div>
                                      </div>

                                      <!-- SHAREHOLDERS / PARTNERS DETAILS Section -->
                                      <div class="card   mb-4">
                                        <div class="card-header"
                                          style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                          <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="users"
                                                class="icon-sm me-2" appFeatherIcon></i> SHAREHOLDERS / PARTNERS DETAILS</h6>
                                            <button type="button" class="btn btn-primary btn-sm" (click)="addShareholder()">
                                              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                              Add Shareholder
                                            </button>
                                          </div>
                                        </div>
                                        <div class="card-body">

                                      <!-- Shareholder Forms -->
                                      <div *ngFor="let shareholder of shareholders; let i = index" class="mb-4">
                                        <div class="card shareholder-card">
                                          <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Shareholder {{i + 1}}</h6>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    (click)="removeShareholder(i)"
                                                    [disabled]="shareholders.length === 1">
                                              <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                                            </button>
                                          </div>
                                          <div class="card-body">
                                            <div class="row">
                                              <!-- Name -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.name"
                                                       [name]="'shareholderName_' + i" placeholder="Enter full name">
                                              </div>

                                              <!-- % Shareholding -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">% Shareholding</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.shareholdingPercentage"
                                                       [name]="'shareholdingPercentage_' + i" placeholder="Enter percentage">
                                              </div>

                                              <!-- Profit Sharing Ratio -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Profit Sharing Ratio (%)</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.profitSharingRatio"
                                                       [name]="'profitSharingRatio_' + i" placeholder="Enter ratio">
                                              </div>

                                              <!-- As Per PD Date -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">As Per PD Dated</label>
                                                <input type="date" class="form-control" [(ngModel)]="shareholder.asPerPdDate"
                                                       [name]="'asPerPdDate_' + i">
                                              </div>

                                              <!-- PAN -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">PAN <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.pan"
                                                       [name]="'pan_' + i" placeholder="Enter PAN number" style="text-transform: uppercase;">
                                              </div>

                                              <!-- Aadhar No -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Aadhar No</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.aadharNo"
                                                       [name]="'aadharNo_' + i" placeholder="Enter Aadhar number">
                                              </div>

                                              <!-- Date of Birth -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Date of Birth</label>
                                                <input type="date" class="form-control" [(ngModel)]="shareholder.dateOfBirth"
                                                       [name]="'dateOfBirth_' + i">
                                              </div>

                                              <!-- Functions Handled -->
                                              <div class="col-md-12 mb-3">
                                                <label class="form-label">Functions Handled</label>
                                                <textarea class="form-control" rows="2" [(ngModel)]="shareholder.functionsHandled"
                                                          [name]="'functionsHandled_' + i" placeholder="Describe functions and responsibilities"></textarea>
                                              </div>

                                              <!-- Years in Real Estate -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">No Of Years In Real Estate</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.yearsInRealEstate"
                                                       [name]="'yearsInRealEstate_' + i" placeholder="Enter years">
                                              </div>

                                              <!-- Years in This Group -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">No Of Years In This Group</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.yearsInGroup"
                                                       [name]="'yearsInGroup_' + i" placeholder="Enter years">
                                              </div>

                                              <!-- Father's Name -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Father's Name</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.fatherName"
                                                       [name]="'fatherName_' + i" placeholder="Enter father's name">
                                              </div>

                                              <!-- Mother's Name -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Mother's Name</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.motherName"
                                                       [name]="'motherName_' + i" placeholder="Enter mother's name">
                                              </div>

                                              <!-- Mother's Maiden Name -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Mother's Maiden Name</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.motherMaidenName"
                                                       [name]="'motherMaidenName_' + i" placeholder="Enter mother's maiden name">
                                              </div>

                                              <!-- Spouse's Name -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Spouse's Name</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.spouseName"
                                                       [name]="'spouseName_' + i" placeholder="Enter spouse's name">
                                              </div>

                                              <!-- Children's Names -->
                                              <div class="col-md-12 mb-3">
                                                <label class="form-label">Children's Names</label>
                                                <textarea class="form-control" rows="2" [(ngModel)]="shareholder.childrenNames"
                                                          [name]="'childrenNames_' + i" placeholder="Enter children's names (separated by commas)"></textarea>
                                              </div>

                                              <!-- Contact Number -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Contact Number <span class="text-danger">*</span></label>
                                                <input type="tel" class="form-control" [(ngModel)]="shareholder.contactNumber"
                                                       [name]="'contactNumber_' + i" placeholder="Enter contact number">
                                              </div>

                                              <!-- Email ID -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Email ID</label>
                                                <input type="email" class="form-control" [(ngModel)]="shareholder.emailId"
                                                       [name]="'emailId_' + i" placeholder="Enter email address">
                                              </div>

                                              <!-- Residential Address -->
                                              <div class="col-md-12 mb-3">
                                                <label class="form-label">Residential Address</label>
                                                <textarea class="form-control" rows="3" [(ngModel)]="shareholder.residentialAddress"
                                                          [name]="'residentialAddress_' + i" placeholder="Enter complete residential address"></textarea>
                                              </div>

                                              <!-- Latest Net Worth -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Latest Net Worth (INR Cr)</label>
                                                <input type="text" class="form-control" [(ngModel)]="shareholder.latestNetWorth"
                                                       [name]="'latestNetWorth_' + i" placeholder="Enter net worth in crores">
                                              </div>

                                              <!-- Detailed Profile -->
                                              <div class="col-md-12 mb-3">
                                                <label class="form-label">Detailed Profile Including Educational Qualification & Business Background</label>
                                                <textarea class="form-control" rows="4" [(ngModel)]="shareholder.detailedProfile"
                                                          [name]="'detailedProfile_' + i" placeholder="Enter detailed profile including education and business background"></textarea>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                        </div>
                                      </div>

                                      <!-- STATEMENT SHOWING OTHER KEY PERSONS OF THE GROUP Section -->
                                      <div class="card   mb-4">
                                        <div class="card-header"
                                          style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                          <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="user-check"
                                                class="icon-sm me-2" appFeatherIcon></i> STATEMENT SHOWING OTHER KEY PERSONS OF THE GROUP</h6>
                                            <button type="button" class="btn btn-primary btn-sm" (click)="addKeyPerson()">
                                              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                              Add Key Person
                                            </button>
                                          </div>
                                        </div>
                                        <div class="card-body">

                                      <!-- Key Person Forms -->
                                      <div *ngFor="let keyPerson of keyPersons; let i = index" class="mb-4">
                                        <div class="card shareholder-card">
                                          <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Key Person {{i + 1}}</h6>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    (click)="removeKeyPerson(i)"
                                                    [disabled]="keyPersons.length === 1">
                                              <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                                            </button>
                                          </div>
                                          <div class="card-body">
                                            <div class="row">
                                              <!-- SR NO -->
                                              <div class="col-md-2 mb-3">
                                                <label class="form-label">SR NO</label>
                                                <input type="text" class="form-control" [value]="i + 1" readonly>
                                              </div>

                                              <!-- Name of Person -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Name of Person <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" [(ngModel)]="keyPerson.name"
                                                       [name]="'keyPersonName_' + i" placeholder="Enter full name">
                                              </div>

                                              <!-- Qualification -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Qualification</label>
                                                <input type="text" class="form-control" [(ngModel)]="keyPerson.qualification"
                                                       [name]="'qualification_' + i" placeholder="Enter qualification">
                                              </div>

                                              <!-- Designation -->
                                              <div class="col-md-2 mb-3">
                                                <label class="form-label">Designation</label>
                                                <input type="text" class="form-control" [(ngModel)]="keyPerson.designation"
                                                       [name]="'designation_' + i" placeholder="Enter designation">
                                              </div>

                                              <!-- Functions Handled -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Functions Handled</label>
                                                <textarea class="form-control" rows="2" [(ngModel)]="keyPerson.functionsHandled"
                                                          [name]="'functionsHandled_' + i" placeholder="Enter functions handled"></textarea>
                                              </div>

                                              <!-- No. of Years in Company/Group -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">No. of Years in this Company / Group</label>
                                                <input type="number" class="form-control" [(ngModel)]="keyPerson.yearsInCompany"
                                                       [name]="'yearsInCompany_' + i" placeholder="Enter years">
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                        </div>
                                      </div>

                                      <!-- BANK DETAILS Section -->
                                      <div class="card   mb-4">
                                        <div class="card-header"
                                          style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                                          <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="credit-card"
                                                class="icon-sm me-2" appFeatherIcon></i> BANK DETAILS - COMPANY, DIRECTORS / PARTNERS / PROPRIETOR</h6>
                                            <button type="button" class="btn btn-primary btn-sm" (click)="addBankDetail()">
                                              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                                              Add Bank Detail
                                            </button>
                                          </div>
                                        </div>
                                        <div class="card-body">

                                      <!-- Bank Detail Forms -->
                                      <div *ngFor="let bankDetail of bankDetails; let i = index" class="mb-4">
                                        <div class="card shareholder-card">
                                          <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Bank Detail {{i + 1}}</h6>
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    (click)="removeBankDetail(i)"
                                                    [disabled]="bankDetails.length === 1">
                                              <i data-feather="trash-2" class="icon-sm" appFeatherIcon></i>
                                            </button>
                                          </div>
                                          <div class="card-body">
                                            <div class="row">
                                              <!-- SR NO -->
                                              <div class="col-md-2 mb-3">
                                                <label class="form-label">SR NO</label>
                                                <input type="text" class="form-control" [value]="i + 1" readonly>
                                              </div>

                                              <!-- Name of Bank / HFC -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Name of Bank / HFC <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" [(ngModel)]="bankDetail.bankName"
                                                       [name]="'bankName_' + i" placeholder="Enter bank/HFC name">
                                              </div>

                                              <!-- Name of Account Holder -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Name of Account Holder <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" [(ngModel)]="bankDetail.accountHolderName"
                                                       [name]="'accountHolderName_' + i" placeholder="Enter account holder name">
                                              </div>

                                              <!-- Type of Account -->
                                              <div class="col-md-2 mb-3">
                                                <label class="form-label">Type of Account</label>
                                                <select class="form-select" [(ngModel)]="bankDetail.accountType"
                                                        [name]="'accountType_' + i">
                                                  <option value="" selected disabled>Select type</option>
                                                  <option value="Savings">Savings</option>
                                                  <option value="Current">Current</option>
                                                  <option value="CC/OD">CC/OD</option>
                                                  <option value="Term Loan">Term Loan</option>
                                                </select>
                                              </div>

                                              <!-- Bank Address -->
                                              <div class="col-md-6 mb-3">
                                                <label class="form-label">Bank Address</label>
                                                <textarea class="form-control" rows="2" [(ngModel)]="bankDetail.bankAddress"
                                                          [name]="'bankAddress_' + i" placeholder="Enter bank address"></textarea>
                                              </div>

                                              <!-- Account Number -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Account Number <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" [(ngModel)]="bankDetail.accountNumber"
                                                       [name]="'accountNumber_' + i" placeholder="Enter account number">
                                              </div>

                                              <!-- Contact Person in Bank -->
                                              <div class="col-md-3 mb-3">
                                                <label class="form-label">Contact Person in Bank</label>
                                                <input type="text" class="form-control" [(ngModel)]="bankDetail.contactPerson"
                                                       [name]="'contactPerson_' + i" placeholder="Enter contact person name">
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                        </div>
                                      </div>

                                      <!-- Form Buttons -->
                                      <div class="text-end mt-3">
                                        <button type="button" class="btn btn-secondary me-2">
                                          <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i> Cancel
                                        </button>
                                        <button type="button" class="btn btn-primary" (click)="saveInitialKyc()">
                                          <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save Initial KYC
                                        </button>
                                      </div>
                                    </form>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Borrowers Details Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="borrowersDetailsAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="borrowersDetailsHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleBorrowersDetails()" [class.collapsed]="!borrowersDetailsExpanded"
                                    [attr.aria-expanded]="borrowersDetailsExpanded"
                                    aria-controls="borrowersDetailsCollapse">
                                    <i data-feather="users" class="icon-sm me-2" appFeatherIcon></i> Borrowers Details
                                  </button>
                                </h2>
                                <div id="borrowersDetailsCollapse" class="accordion-collapse collapse"
                                  [class.show]="borrowersDetailsExpanded" aria-labelledby="borrowersDetailsHeading">
                                  <div class="accordion-body">
                                    <!-- Content will be added later -->
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Other Business's Details Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="otherBusinessDetailsAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="otherBusinessDetailsHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleOtherBusinessDetails()"
                                    [class.collapsed]="!otherBusinessDetailsExpanded"
                                    [attr.aria-expanded]="otherBusinessDetailsExpanded"
                                    aria-controls="otherBusinessDetailsCollapse">
                                    <i data-feather="briefcase" class="icon-sm me-2" appFeatherIcon></i> Other
                                    Business's Details
                                  </button>
                                </h2>
                                <div id="otherBusinessDetailsCollapse" class="accordion-collapse collapse"
                                  [class.show]="otherBusinessDetailsExpanded"
                                  aria-labelledby="otherBusinessDetailsHeading">
                                  <div class="accordion-body">
                                    <!-- Other Business Details Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="briefcase"
                                            class="icon-sm me-2" appFeatherIcon></i> OTHER BUSINESS DETAILS</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Business Details -->
                                        <div class="row">
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Promoter's Name</label>
                                            <input type="text" class="form-control" value="Rajesh Sharma">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Company Name</label>
                                            <input type="text" class="form-control"
                                              value="Sunrise Retail Solutions Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Constitution</label>
                                            <input type="text" class="form-control" value="Private Limited">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Date of Incorporation</label>
                                            <input type="text" class="form-control" value="10-Mar-2018">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">PAN</label>
                                            <input type="text" class="form-control" value="**********">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">CIN / GST No</label>
                                            <input type="text" class="form-control" value="U72200MH2018PTC123456">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Location / Brief Address</label>
                                            <input type="text" class="form-control"
                                              value="Shop No. 12, Andheri West, Mumbai, Maharashtra - 400053">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Line of Activity</label>
                                            <input type="text" class="form-control" value="Retail Chain & Distribution">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Years of Experience</label>
                                            <input type="number" class="form-control" value="5">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Avg. Annual Turnover (₹)</label>
                                            <input type="text" class="form-control" value="75,00,000">
                                          </div>
                                          <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3">
                                            <label class="form-label">Avg. Annual Profit (%)</label>
                                            <input type="text" class="form-control" value="18.5%">
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Project Land Details Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="projectLandDetailsAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="projectLandDetailsHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleProjectLandDetails()" [class.collapsed]="!projectLandDetailsExpanded"
                                    [attr.aria-expanded]="projectLandDetailsExpanded"
                                    aria-controls="projectLandDetailsCollapse">
                                    <i data-feather="map" class="icon-sm me-2" appFeatherIcon></i> Project Land Details
                                  </button>
                                </h2>
                                <div id="projectLandDetailsCollapse" class="accordion-collapse collapse"
                                  [class.show]="projectLandDetailsExpanded" aria-labelledby="projectLandDetailsHeading">
                                  <div class="accordion-body">
                                    <!-- Project Land Details Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="map"
                                            class="icon-sm me-2" appFeatherIcon></i> PROJECT LAND DETAILS</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Land Detail -->
                                        <div class="row">
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Type of Agreement</label>
                                            <input type="text" class="form-control" value="Sale Deed">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Document Date</label>
                                            <input type="text" class="form-control" value="15-May-2022">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Document No</label>
                                            <input type="text" class="form-control" value="SD/2022/1234">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Survey No</label>
                                            <input type="text" class="form-control" value="123/4A">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Area (Sq Mtrs)</label>
                                            <input type="text" class="form-control" value="25,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Area (Guntha/Acre)</label>
                                            <input type="number" step="0.01" class="form-control" value="6.18">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Area Unit</label>
                                            <select class="form-control">
                                              <option value="Acre" selected>Acre</option>
                                              <option value="Guntha">Guntha</option>
                                              <option value="Sq Mtrs">Sq Mtrs</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Names of Parties</label>
                                            <input type="text" class="form-control"
                                              value="ABC Developers Pvt Ltd and XYZ Landowners">
                                          </div>
                                          <div class="col-12 col-md-12 mb-3">
                                            <label class="form-label">Additional Remarks / Consideration Details</label>
                                            <textarea class="form-control"
                                              rows="2">Rs. 15 Crores for 6.18 Acres of land</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Sathbara & Mutation Entries Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="sathbaraEntriesAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="sathbaraEntriesHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleSathbaraEntries()" [class.collapsed]="!sathbaraEntriesExpanded"
                                    [attr.aria-expanded]="sathbaraEntriesExpanded"
                                    aria-controls="sathbaraEntriesCollapse">
                                    <i data-feather="file-text" class="icon-sm me-2" appFeatherIcon></i> Sathbara &
                                    Mutation Entries
                                  </button>
                                </h2>
                                <div id="sathbaraEntriesCollapse" class="accordion-collapse collapse"
                                  [class.show]="sathbaraEntriesExpanded" aria-labelledby="sathbaraEntriesHeading">
                                  <div class="accordion-body">
                                    <!-- Sathbara & Mutation Entries Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="file-text"
                                            class="icon-sm me-2" appFeatherIcon></i> SATHBARA & MUTATION ENTRIES</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Entry Details -->
                                        <div class="row">
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Survey/Hissa No</label>
                                            <input type="text" class="form-control" value="123/4/A">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Area (Sq Mtrs)</label>
                                            <input type="text" class="form-control" value="5000 Sq. Mt.">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Mutation Entries</label>
                                            <input type="text" class="form-control"
                                              value="Entry No. 1234 dated 15-Jun-2022">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Names of Owners</label>
                                            <input type="text" class="form-control" value="Rajesh Kumar Sharma">
                                          </div>
                                          <div class="col-12 mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control"
                                              rows="2">Mutation completed and registered</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- FSI Calc & Approval Status Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="fsiCalcAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="fsiCalcHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleFsiCalc()"
                                    [class.collapsed]="!fsiCalcExpanded" [attr.aria-expanded]="fsiCalcExpanded"
                                    aria-controls="fsiCalcCollapse">
                                    <i data-feather="check-square" class="icon-sm me-2" appFeatherIcon></i> FSI Calc &
                                    Approval Status
                                  </button>
                                </h2>
                                <div id="fsiCalcCollapse" class="accordion-collapse collapse"
                                  [class.show]="fsiCalcExpanded" aria-labelledby="fsiCalcHeading">
                                  <div class="accordion-body">
                                    <!-- Content will be added later -->
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Construction & Payment Sched Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="constructionSchedAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="constructionSchedHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleConstructionSched()" [class.collapsed]="!constructionSchedExpanded"
                                    [attr.aria-expanded]="constructionSchedExpanded"
                                    aria-controls="constructionSchedCollapse">
                                    <i data-feather="calendar" class="icon-sm me-2" appFeatherIcon></i> Construction &
                                    Payment Sched
                                  </button>
                                </h2>
                                <div id="constructionSchedCollapse" class="accordion-collapse collapse"
                                  [class.show]="constructionSchedExpanded" aria-labelledby="constructionSchedHeading">
                                  <div class="accordion-body">
                                    <!-- Content will be added later -->
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Group Entities Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="groupEntitiesAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="groupEntitiesHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleGroupEntities()" [class.collapsed]="!groupEntitiesExpanded"
                                    [attr.aria-expanded]="groupEntitiesExpanded" aria-controls="groupEntitiesCollapse">
                                    <i data-feather="grid" class="icon-sm me-2" appFeatherIcon></i> Group Entities
                                  </button>
                                </h2>
                                <div id="groupEntitiesCollapse" class="accordion-collapse collapse"
                                  [class.show]="groupEntitiesExpanded" aria-labelledby="groupEntitiesHeading">
                                  <div class="accordion-body">
                                    <!-- Group Entities Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="grid"
                                            class="icon-sm me-2" appFeatherIcon></i> GROUP ENTITIES</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Entity Details -->
                                        <div class="row">
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Company Name</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Constitution</label>
                                            <input type="text" class="form-control" value="Private Limited">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Date of Incorporation</label>
                                            <input type="text" class="form-control" value="15-Apr-2010">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">PAN</label>
                                            <input type="text" class="form-control" value="**********">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">CIN / GST No</label>
                                            <input type="text" class="form-control" value="U45200MH2010PTC123456">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Office Address</label>
                                            <input type="text" class="form-control"
                                              value="501, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Partners / Directors</label>
                                            <input type="text" class="form-control"
                                              value="Rajesh Kumar, Amit Shah, Priya Patel">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Profit Sharing Ratio</label>
                                            <input type="text" class="form-control" value="40:30:30">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">DIN</label>
                                            <input type="text" class="form-control"
                                              value="DIN12345678, DIN87654321, DIN23456789">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Current Projects</label>
                                            <input type="text" class="form-control"
                                              value="Sunrise Heights, Green Valley">
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Completed Projects Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="completedProjectsAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="completedProjectsHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleCompletedProjects()" [class.collapsed]="!completedProjectsExpanded"
                                    [attr.aria-expanded]="completedProjectsExpanded"
                                    aria-controls="completedProjectsCollapse">
                                    <i data-feather="check-circle" class="icon-sm me-2" appFeatherIcon></i> Completed
                                    Projects
                                  </button>
                                </h2>
                                <div id="completedProjectsCollapse" class="accordion-collapse collapse"
                                  [class.show]="completedProjectsExpanded" aria-labelledby="completedProjectsHeading">
                                  <div class="accordion-body">
                                    <!-- Completed Projects Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="check-circle" class="icon-sm me-2" appFeatherIcon></i>
                                          COMPLETED PROJECTS</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Project Details -->
                                        <div class="row">
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Name of the Project</label>
                                            <input type="text" class="form-control" value="Sunrise Heights">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Name of the Entity</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Promoters Names</label>
                                            <input type="text" class="form-control" value="Rajesh Kumar, Amit Shah">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Profit Sharing</label>
                                            <input type="text" class="form-control" value="60:40">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Promoters Role</label>
                                            <input type="text" class="form-control" value="Managing Director, Director">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Development Type</label>
                                            <input type="text" class="form-control" value="Owned">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control" value="Andheri East, Mumbai">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Project Type</label>
                                            <input type="text" class="form-control" value="Residential">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Project Structure</label>
                                            <input type="text" class="form-control" value="G+14 Tower">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">S No & H No / CTS No</label>
                                            <input type="text" class="form-control" value="CTS No. 123/45">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Total No of Units</label>
                                            <input type="number" class="form-control" value="120">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Construction Area</label>
                                            <input type="text" class="form-control" value="150,000 sq.ft">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Total Construction Cost (₹)</label>
                                            <input type="text" class="form-control" value="4,50,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Total Sales Value (₹)</label>
                                            <input type="text" class="form-control" value="7,50,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Unsold Units</label>
                                            <input type="number" class="form-control" value="0">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Start Date</label>
                                            <input type="text" class="form-control" value="15-Jan-2018">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">End Date</label>
                                            <input type="text" class="form-control" value="30-Jun-2021">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">TP Occupancy Received</label>
                                            <select class="form-control">
                                              <option value="Yes" selected>Yes</option>
                                              <option value="No">No</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">RERA Registration No</label>
                                            <input type="text" class="form-control" value="MH12345678">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Other Remarks</label>
                                            <textarea class="form-control"
                                              rows="2">Completed on time and fully sold</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Unsold & Leased Prop Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="unsoldStockAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="unsoldStockHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleUnsoldStock()"
                                    [class.collapsed]="!unsoldStockExpanded" [attr.aria-expanded]="unsoldStockExpanded"
                                    aria-controls="unsoldStockCollapse">
                                    <i data-feather="home" class="icon-sm me-2" appFeatherIcon></i> Unsold & Leased Prop
                                  </button>
                                </h2>
                                <div id="unsoldStockCollapse" class="accordion-collapse collapse"
                                  [class.show]="unsoldStockExpanded" aria-labelledby="unsoldStockHeading">
                                  <div class="accordion-body">
                                    <!-- Unsold Stock & Leased Prop Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="home"
                                            class="icon-sm me-2" appFeatherIcon></i> UNSOLD STOCK</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Property Information Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Property Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label class="form-label">Project Name</label>
                                            <input type="text" class="form-control" value="Tech Hub">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control"
                                              value="Bandra Kurla Complex, Mumbai">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label class="form-label">Category</label>
                                            <select class="form-control">
                                              <option value="Commercial" selected>Commercial</option>
                                              <option value="Residential">Residential</option>
                                              <option value="Mixed">Mixed</option>
                                            </select>
                                          </div>
                                        </div>

                                        <!-- Area and Value Section -->
                                        <div class="row">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Area and Value</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Saleable Area (sq.ft)</label>
                                            <input type="text" class="form-control" value="1,800">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Rate (₹/sq.ft)</label>
                                            <input type="text" class="form-control" value="15,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Value (₹)</label>
                                            <input type="text" class="form-control" value="2,70,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Debt (₹)</label>
                                            <input type="text" class="form-control" value="1,80,00,000">
                                          </div>
                                          <div class="col-12 mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control"
                                              rows="2">Premium office space with sea view</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Leased Properties Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="home"
                                            class="icon-sm me-2" appFeatherIcon></i> LEASED PROPERTIES</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Property Information Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Property Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label class="form-label">Project Name</label>
                                            <input type="text" class="form-control" value="Tech Hub Tower A">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control"
                                              value="Bandra Kurla Complex, Mumbai">
                                          </div>
                                        </div>

                                        <!-- Lease Details Section -->
                                        <div class="row">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Lease Details</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Area Leased (sq.ft)</label>
                                            <input type="text" class="form-control" value="5,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Lease Rent P.A. (₹)</label>
                                            <input type="text" class="form-control" value="60,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Security Deposit (₹)</label>
                                            <input type="text" class="form-control" value="1,20,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Debt O/S (₹)</label>
                                            <input type="text" class="form-control" value="3,50,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Market Value (₹)</label>
                                            <input type="text" class="form-control" value="7,50,00,000">
                                          </div>
                                          <div class="col-12 mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control"
                                              rows="2">Leased to XYZ Technologies for 9 years</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Land Bank & Upcoming Projects Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="landBankAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="landBankHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleLandBank()"
                                    [class.collapsed]="!landBankExpanded" [attr.aria-expanded]="landBankExpanded"
                                    aria-controls="landBankCollapse">
                                    <i data-feather="layers" class="icon-sm me-2" appFeatherIcon></i> Land Bank &
                                    Upcoming Projects
                                  </button>
                                </h2>
                                <div id="landBankCollapse" class="accordion-collapse collapse"
                                  [class.show]="landBankExpanded" aria-labelledby="landBankHeading">
                                  <div class="accordion-body">
                                    <!-- Land Bank & Upcoming Projects Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="layers"
                                            class="icon-sm me-2" appFeatherIcon></i> LAND BANK & UPCOMING PROJECTS</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Project Information Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Project Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Project Name</label>
                                            <input type="text" class="form-control" value="Green Valley Township">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Entity Name</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Ownership Type</label>
                                            <select class="form-control">
                                              <option value="Owned" selected>Owned</option>
                                              <option value="Joint Venture">Joint Venture</option>
                                              <option value="Development Rights">Development Rights</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Documentation Status</label>
                                            <input type="text" class="form-control" value="All approvals in process">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control" value="Panvel, Navi Mumbai">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Project Type</label>
                                            <select class="form-control">
                                              <option value="Residential" selected>Residential</option>
                                              <option value="Commercial">Commercial</option>
                                              <option value="Mixed">Mixed</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Expected Start Date</label>
                                            <input type="text" class="form-control" value="01-Dec-2023">
                                          </div>
                                        </div>

                                        <!-- Area Details Section -->
                                        <div class="row">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Area Details</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Residential Area (sq.ft)</label>
                                            <input type="text" class="form-control" value="2,50,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Residential Units</label>
                                            <input type="text" class="form-control" value="1,200">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Commercial Area (sq.ft)</label>
                                            <input type="number" class="form-control" value="0">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Commercial Units</label>
                                            <input type="number" class="form-control" value="0">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Mixed Area (sq.ft)</label>
                                            <input type="number" class="form-control" value="0">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Mixed Units</label>
                                            <input type="number" class="form-control" value="0">
                                          </div>
                                          <div class="col-12 mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control" rows="2">Land fully paid</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Land Bank Details Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i data-feather="map"
                                            class="icon-sm me-2" appFeatherIcon></i> LAND BANK DETAILS</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row">
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Company Name</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control" value="Panvel, Navi Mumbai">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Land Type</label>
                                            <select class="form-control">
                                              <option value="Non-Agricultural" selected>Non-Agricultural</option>
                                              <option value="Agricultural">Agricultural</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Ownership Type</label>
                                            <select class="form-control">
                                              <option value="Owned" selected>Owned</option>
                                              <option value="Joint Venture">Joint Venture</option>
                                              <option value="Development Rights">Development Rights</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Plot Area</label>
                                            <input type="number" class="form-control" value="25">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Plot Area Unit</label>
                                            <select class="form-control">
                                              <option value="Acres" selected>Acres</option>
                                              <option value="Sq.ft">Sq.ft</option>
                                              <option value="Sq.mt">Sq.mt</option>
                                              <option value="Guntha">Guntha</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Acquisition Year</label>
                                            <input type="number" class="form-control" value="2018">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Purchase Value (₹)</label>
                                            <input type="text" class="form-control" value="15,00,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Market Value (₹)</label>
                                            <input type="text" class="form-control" value="25,00,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Has Loan</label>
                                            <select class="form-control">
                                              <option value="No" selected>No</option>
                                              <option value="Yes">Yes</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Approval Status</label>
                                            <input type="text" class="form-control" value="All approvals in process">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Expected Launch Date</label>
                                            <input type="text" class="form-control" value="01-Dec-2023">
                                          </div>
                                          <div class="col-12 mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control"
                                              rows="2">Prime location near upcoming airport</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Project Factsheet Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="projectsFactsheetAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="projectsFactsheetHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleProjectsFactsheet()" [class.collapsed]="!projectsFactsheetExpanded"
                                    [attr.aria-expanded]="projectsFactsheetExpanded"
                                    aria-controls="projectsFactsheetCollapse">
                                    <i data-feather="clipboard" class="icon-sm me-2" appFeatherIcon></i> Project
                                    Factsheet
                                  </button>
                                </h2>
                                <div id="projectsFactsheetCollapse" class="accordion-collapse collapse"
                                  [class.show]="projectsFactsheetExpanded" aria-labelledby="projectsFactsheetHeading">
                                  <div class="accordion-body">
                                    <!-- Content will be added later -->
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Sales MIS & Inventory Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="salesMisAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="salesMisHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleSalesMis()"
                                    [class.collapsed]="!salesMisExpanded" [attr.aria-expanded]="salesMisExpanded"
                                    aria-controls="salesMisCollapse">
                                    <i data-feather="bar-chart-2" class="icon-sm me-2" appFeatherIcon></i> Sales MIS &
                                    Inventory
                                  </button>
                                </h2>
                                <div id="salesMisCollapse" class="accordion-collapse collapse"
                                  [class.show]="salesMisExpanded" aria-labelledby="salesMisHeading">
                                  <div class="accordion-body">
                                    <!-- Sales MIS & Inventory Summary Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="bar-chart-2" class="icon-sm me-2" appFeatherIcon></i> SALES
                                          MIS & INVENTORY SUMMARY</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row">
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Saleable Built-Up Area (sq.ft)</label>
                                            <input type="text" class="form-control" value="25,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">MAHARERA Carpet Area (Sq.Mt.)</label>
                                            <input type="text" class="form-control" value="18,500">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Carpet Area (Sq.Ft.)</label>
                                            <input type="text" class="form-control" value="1,99,132">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Number of Flats</label>
                                            <input type="number" class="form-control" value="120">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Number of Shops</label>
                                            <input type="number" class="form-control" value="15">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Number of Offices</label>
                                            <input type="number" class="form-control" value="8">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Refuge Units</label>
                                            <input type="number" class="form-control" value="4">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Achieved For Sold Area</label>
                                            <input type="text" class="form-control" value="1,50,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Target For Unsold Area</label>
                                            <input type="text" class="form-control" value="85,000">
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Sales MIS & Inventory Details Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="shopping-bag" class="icon-sm me-2" appFeatherIcon></i> SALES
                                          MIS & INVENTORY DETAILS</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Unit Information Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Unit Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Building/Wing Name</label>
                                            <input type="text" class="form-control" value="Wing A">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Floor No</label>
                                            <input type="number" class="form-control" value="1">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Unit No</label>
                                            <input type="text" class="form-control" value="101">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">MAHARERA Carpet Area</label>
                                            <input type="number" class="form-control" value="650">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Carpet Area</label>
                                            <input type="number" class="form-control" value="625">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Saleable Built-Up Area</label>
                                            <input type="number" class="form-control" value="950">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Unit Type</label>
                                            <select class="form-control">
                                              <option value="RESI" selected>RESI</option>
                                              <option value="COMM">COMM</option>
                                              <option value="MIXED">MIXED</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Owner Type</label>
                                            <select class="form-control">
                                              <option value="DEVELOPER" selected>DEVELOPER</option>
                                              <option value="CUSTOMER">CUSTOMER</option>
                                              <option value="INVESTOR">INVESTOR</option>
                                            </select>
                                          </div>
                                        </div>

                                        <!-- Sale Information Section -->
                                        <div class="row">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Sale Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Sale Status</label>
                                            <select class="form-control">
                                              <option value="SOLD" selected>SOLD</option>
                                              <option value="AVAILABLE">AVAILABLE</option>
                                              <option value="BOOKED">BOOKED</option>
                                              <option value="RESERVED">RESERVED</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Buyer Name</label>
                                            <input type="text" class="form-control" value="Rahul Sharma">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Contact No</label>
                                            <input type="text" class="form-control" value="9876543210">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Email ID</label>
                                            <input type="email" class="form-control" value="<EMAIL>">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Date of Booking</label>
                                            <input type="text" class="form-control" value="10-Jan-2023">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Date of Registration</label>
                                            <input type="text" class="form-control" value="15-Feb-2023">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Registration No</label>
                                            <input type="text" class="form-control" value="REG/2023/001">
                                          </div>
                                        </div>

                                        <!-- Financial Information Section -->
                                        <div class="row">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Financial Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Basic Cost (₹)</label>
                                            <input type="text" class="form-control" value="85,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Development Cost (₹)</label>
                                            <input type="text" class="form-control" value="15,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Total Value (₹)</label>
                                            <input type="text" class="form-control" value="1,00,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Demand Raised (₹)</label>
                                            <input type="text" class="form-control" value="1,00,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Amount Received (₹)</label>
                                            <input type="text" class="form-control" value="80,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Balance (₹)</label>
                                            <input type="text" class="form-control" value="20,00,000">
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Sales Plan Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="salesPlanAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="salesPlanHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleSalesPlan()"
                                    [class.collapsed]="!salesPlanExpanded" [attr.aria-expanded]="salesPlanExpanded"
                                    aria-controls="salesPlanCollapse">
                                    <i data-feather="trending-up" class="icon-sm me-2" appFeatherIcon></i> Sales Plan
                                  </button>
                                </h2>
                                <div id="salesPlanCollapse" class="accordion-collapse collapse"
                                  [class.show]="salesPlanExpanded" aria-labelledby="salesPlanHeading">
                                  <div class="accordion-body">
                                    <!-- Sales Plan Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="trending-up" class="icon-sm me-2" appFeatherIcon></i> SALES
                                          PLAN</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row mb-3">
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label for="summaryQuarter" class="form-label">Quarter <span
                                                class="text-danger">*</span></label>
                                            <select class="form-select" id="summaryQuarter">
                                              <option value="">Select Quarter</option>
                                              <option value="q1" selected>Q1</option>
                                              <option value="q2">Q2</option>
                                              <option value="q3">Q3</option>
                                              <option value="q4">Q4</option>
                                              <option value="q5">Q5</option>
                                              <option value="q6">Q6</option>
                                              <option value="q7">Q7</option>
                                              <option value="q8">Q8</option>
                                              <option value="q9">Q9</option>
                                            </select>
                                          </div>
                                        </div>

                                        <div class="row mb-3">
                                          <div class="col-md-4 mb-3">
                                            <label for="summaryFlatsBooked" class="form-label">No. of flats booked /
                                              sold <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryFlatsBooked" value="12"
                                              min="0">
                                          </div>
                                          <div class="col-md-4 mb-3">
                                            <label for="summaryAverageArea" class="form-label">Average area of the flat
                                              <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryAverageArea"
                                              value="850" min="0">
                                          </div>
                                          <div class="col-md-4 mb-3">
                                            <label for="summaryRatePerSqFt" class="form-label">Rate per sq. ft. <span
                                                class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryRatePerSqFt"
                                              value="4500" min="0">
                                          </div>
                                        </div>

                                        <div class="row mb-3">
                                          <div class="col-md-12">
                                            <label for="summarySaleValue" class="form-label">Sale value</label>
                                            <div class="input-group">
                                              <span class="input-group-text">₹</span>
                                              <input type="text" class="form-control" id="summarySaleValue"
                                                value="4,59,00,000" readonly>
                                            </div>
                                            <small class="form-text text-muted">Calculated automatically based on the
                                              inputs above</small>
                                          </div>
                                        </div>

                                        <div class="row mt-4">
                                          <div class="col-12 d-flex justify-content-between">
                                            <button type="button" class="btn btn-primary">
                                              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i> Add Sales
                                              Plan
                                            </button>
                                            <button type="button" class="btn btn-primary">
                                              Save & Next <i data-feather="arrow-right" class="icon-sm ms-1"
                                                appFeatherIcon></i>
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Compiled Cost Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="compiledCostAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="compiledCostHeading">
                                  <button class="accordion-button collapsed" type="button"
                                    (click)="toggleCompiledCost()" [class.collapsed]="!compiledCostExpanded"
                                    [attr.aria-expanded]="compiledCostExpanded" aria-controls="compiledCostCollapse">
                                    <i data-feather="dollar-sign" class="icon-sm me-2" appFeatherIcon></i> Compiled Cost
                                  </button>
                                </h2>
                                <div id="compiledCostCollapse" class="accordion-collapse collapse"
                                  [class.show]="compiledCostExpanded" aria-labelledby="compiledCostHeading">
                                  <div class="accordion-body">
                                    <!-- Compiled Cost Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="dollar-sign" class="icon-sm me-2" appFeatherIcon></i> PROJECT
                                          COST BREAKDOWN</h6>
                                      </div>
                                      <div class="card-body">
                                        <div class="row mb-4">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Cost Item Details</h6>
                                          </div>
                                          <div class="col-12 mb-3">
                                            <label for="summaryParticulars" class="form-label">Particulars <span
                                                class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="summaryParticulars"
                                              value="Land Cost">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label for="summaryTotalCost" class="form-label">Total Cost (₹) <span
                                                class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryTotalCost"
                                              value="50000000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label for="summaryCostIncurred" class="form-label">Cost Incurred Till Date
                                              (₹) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryCostIncurred"
                                              value="50000000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label for="summaryCostToBeIncurred" class="form-label">Cost To Be Incurred
                                              (₹) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryCostToBeIncurred"
                                              value="0">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label for="summaryPercentage" class="form-label">Percentage (%) <span
                                                class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="summaryPercentage"
                                              value="25.0" step="0.1">
                                          </div>
                                        </div>

                                        <div class="row mt-4">
                                          <div class="col-12 d-flex justify-content-between">
                                            <button type="button" class="btn btn-primary">
                                              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i> Add Cost
                                              Item
                                            </button>
                                            <button type="button" class="btn btn-primary">
                                              Save & Next <i data-feather="arrow-right" class="icon-sm ms-1"
                                                appFeatherIcon></i>
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Debt Details Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="debtDetailsAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="debtDetailsHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleDebtDetails()"
                                    [class.collapsed]="!debtDetailsExpanded" [attr.aria-expanded]="debtDetailsExpanded"
                                    aria-controls="debtDetailsCollapse">
                                    <i data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i> Debt Details
                                  </button>
                                </h2>
                                <div id="debtDetailsCollapse" class="accordion-collapse collapse"
                                  [class.show]="debtDetailsExpanded" aria-labelledby="debtDetailsHeading">
                                  <div class="accordion-body">
                                    <!-- Debt Details Card -->
                                    <div class="card   mb-4">
                                      <div class="card-header" style="background-color: #f8f9fa;">
                                        <h6 class="card-title mb-0" style="color: #3F828B;"><i
                                            data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i> DEBT
                                          DETAILS</h6>
                                      </div>
                                      <div class="card-body">
                                        <!-- Basic Information Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Basic Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Borrower's Name</label>
                                            <input type="text" class="form-control" value="ABC Developers Pvt Ltd">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Entity Type</label>
                                            <select class="form-control">
                                              <option value="ENTITY" selected>ENTITY</option>
                                              <option value="INDIVIDUAL">INDIVIDUAL</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Name of Lender</label>
                                            <input type="text" class="form-control" value="HDFC Bank">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Type of Facility</label>
                                            <select class="form-control">
                                              <option value="TERM LOAN" selected>TERM LOAN</option>
                                              <option value="CASH CREDIT">CASH CREDIT</option>
                                              <option value="OVERDRAFT">OVERDRAFT</option>
                                              <option value="WORKING CAPITAL">WORKING CAPITAL</option>
                                              <option value="CONSTRUCTION FINANCE">CONSTRUCTION FINANCE</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Loan Account No</label>
                                            <input type="text" class="form-control" value="HDFC123456789">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Individual Guarantee</label>
                                            <select class="form-control">
                                              <option value="YES" selected>YES</option>
                                              <option value="NO">NO</option>
                                            </select>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Date of Sanction</label>
                                            <input type="text" class="form-control" value="15-Jan-2023">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Date of Disbursement</label>
                                            <input type="text" class="form-control" value="01-Feb-2023">
                                          </div>
                                        </div>

                                        <!-- Financial Information Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Financial Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Sanctioned Amount (₹)</label>
                                            <input type="text" class="form-control" value="5,00,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Disbursed Amount (₹)</label>
                                            <input type="text" class="form-control" value="4,50,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Utilized (₹)</label>
                                            <input type="text" class="form-control" value="4,50,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">ROA (%)</label>
                                            <input type="number" class="form-control" value="10.5" step="0.01">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Repaid (₹)</label>
                                            <input type="text" class="form-control" value="50,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">EMI Amount (₹)</label>
                                            <input type="text" class="form-control" value="5,50,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Repayment Bank Account No</label>
                                            <input type="text" class="form-control" value="HDFC987654321">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Total EMI</label>
                                            <input type="number" class="form-control" value="60">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">EMI Paid</label>
                                            <input type="number" class="form-control" value="10">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Origin Fees (₹)</label>
                                            <input type="text" class="form-control" value="5,00,000">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Current Outstanding (₹)</label>
                                            <input type="text" class="form-control" value="4,00,00,000">
                                          </div>
                                        </div>

                                        <!-- Loan Terms & Security Section -->
                                        <div class="row mb-3">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Loan Terms & Security</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Loan Tenor</label>
                                            <input type="text" class="form-control" value="5 Years">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Moratorium</label>
                                            <input type="text" class="form-control" value="6 Months">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-6 mb-3">
                                            <label class="form-label">Details of Security Created</label>
                                            <textarea class="form-control" rows="2">Property Mortgage</textarea>
                                          </div>
                                        </div>

                                        <!-- Default Information Section -->
                                        <div class="row">
                                          <div class="col-12 mb-3">
                                            <h6 class="section-title">Default Information</h6>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                                            <label class="form-label">Overdue Amount (₹)</label>
                                            <input type="number" class="form-control" value="0">
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-5 mb-3">
                                            <label class="form-label">Details of Default (if any)</label>
                                            <textarea class="form-control" rows="2">N/A</textarea>
                                          </div>
                                          <div class="col-12 col-md-6 col-lg-4 mb-3">
                                            <label class="form-label">Remarks for Delay (if any)</label>
                                            <textarea class="form-control" rows="2">N/A</textarea>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Google Map Accordion -->
                            <div class="accordion cam-summary-accordion mb-4" id="googleMapAccordion">
                              <div class="accordion-item">
                                <h2 class="accordion-header" id="googleMapHeading">
                                  <button class="accordion-button collapsed" type="button" (click)="toggleGoogleMap()"
                                    [class.collapsed]="!googleMapExpanded" [attr.aria-expanded]="googleMapExpanded"
                                    aria-controls="googleMapCollapse">
                                    <i data-feather="map-pin" class="icon-sm me-2" appFeatherIcon></i> Google Map
                                  </button>
                                </h2>
                                <div id="googleMapCollapse" class="accordion-collapse collapse"
                                  [class.show]="googleMapExpanded" aria-labelledby="googleMapHeading">
                                  <div class="accordion-body">
                                    <!-- Content will be added later -->
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Save & Next Button at the bottom of Summary section -->
                            <div class="text-end mt-4">
                              <button type="button" class="btn btn-primary">
                                <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Submit
                              </button>
                            </div>

                          </div>
                        </ng-template>
                      </li>

                    </ul>

                    <!-- Nested Tab Content -->
                    <div [ngbNavOutlet]="camNoteNav" class="tab-content"></div>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="4">
              <a ngbNavLink>Institute Selection</a>
              <ng-template ngbNavContent>
                <!-- Institute Selection Content -->
                <div class="card card-body  border-0">
                  <div>
                    <h6 class="card-subtitle mb-4">Institute Selection</h6>

                    <div class="institute-selection-content">
                      <form class="forms-sample">
                        <div class="row">
                          <!-- Government Banks Section -->
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Government Banks</label>
                            <div class="dropdown" ngbDropdown [autoClose]="'outside'" #govtBankDropdown="ngbDropdown">
                              <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button"
                                id="govtBankDropdown" ngbDropdownToggle>
                                {{ getSelectedGovtBanksText() }}
                              </button>
                              <div class="dropdown-menu p-2" ngbDropdownMenu (click)="$event.stopPropagation()">
                                <div class="px-2 py-1" *ngFor="let bank of govtBanks">
                                  <div class="form-check text-start">
                                    <input type="checkbox" class="form-check-input" [id]="'govt-bank-' + bank.id"
                                      [checked]="isGovtBankSelected(bank.id)"
                                      (change)="onGovtBankCheckboxChange(bank.id, $event)">
                                    <label class="form-check-label" [for]="'govt-bank-' + bank.id">{{ bank.name
                                      }}</label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Private Banks Section -->
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Private Banks</label>
                            <div class="dropdown" ngbDropdown [autoClose]="'outside'"
                              #privateBankDropdown="ngbDropdown">
                              <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button"
                                id="privateBankDropdown" ngbDropdownToggle>
                                {{ getSelectedPrivateBanksText() }}
                              </button>
                              <div class="dropdown-menu p-2" ngbDropdownMenu (click)="$event.stopPropagation()">
                                <div class="px-2 py-1" *ngFor="let bank of privateBanks">
                                  <div class="form-check text-start">
                                    <input type="checkbox" class="form-check-input" [id]="'private-bank-' + bank.id"
                                      [checked]="isPrivateBankSelected(bank.id)"
                                      (change)="onPrivateBankCheckboxChange(bank.id, $event)">
                                    <label class="form-check-label" [for]="'private-bank-' + bank.id">{{ bank.name
                                      }}</label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="col-12 col-md-6 col-lg-3 mb-3">

                            <label class="form-label">NBFCs</label>
                            <div class="dropdown" ngbDropdown [autoClose]="'outside'" #nbfcDropdown="ngbDropdown">
                              <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button"
                                id="nbfcDropdown" ngbDropdownToggle>
                                {{ getSelectedNBFCsText() }}
                              </button>
                              <div class="dropdown-menu p-2" ngbDropdownMenu (click)="$event.stopPropagation()">
                                <div class="px-2 py-1" *ngFor="let nbfc of nbfcs">
                                  <div class="form-check text-start">
                                    <input type="checkbox" class="form-check-input" [id]="'nbfc-' + nbfc.id"
                                      [checked]="isNBFCSelected(nbfc.id)"
                                      (change)="onNBFCCheckboxChange(nbfc.id, $event)">
                                    <label class="form-check-label" [for]="'nbfc-' + nbfc.id">{{ nbfc.name }}</label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Corporate Consultancy Section -->
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Corporate Consultancy</label>
                            <div class="dropdown" ngbDropdown [autoClose]="'outside'"
                              #consultancyDropdown="ngbDropdown">
                              <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button"
                                id="consultancyDropdown" ngbDropdownToggle>
                                {{ getSelectedConsultanciesText() }}
                              </button>
                              <div class="dropdown-menu p-2" ngbDropdownMenu (click)="$event.stopPropagation()">
                                <div class="px-2 py-1" *ngFor="let consultancy of consultancies">
                                  <div class="form-check text-start">
                                    <input type="checkbox" class="form-check-input"
                                      [id]="'consultancy-' + consultancy.id"
                                      [checked]="isConsultancySelected(consultancy.id)"
                                      (change)="onConsultancyCheckboxChange(consultancy.id, $event)">
                                    <label class="form-check-label" [for]="'consultancy-' + consultancy.id">{{
                                      consultancy.name }}</label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Fund Houses Section -->
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Fund Houses</label>
                            <div class="dropdown" ngbDropdown [autoClose]="'outside'" #fundHouseDropdown="ngbDropdown">
                              <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button"
                                id="fundHouseDropdown" ngbDropdownToggle>
                                {{ getSelectedFundHousesText() }}
                              </button>
                              <div class="dropdown-menu p-2" ngbDropdownMenu (click)="$event.stopPropagation()">
                                <div class="px-2 py-1" *ngFor="let fundHouse of fundHouses">
                                  <div class="form-check text-start">
                                    <input type="checkbox" class="form-check-input" [id]="'fund-house-' + fundHouse.id"
                                      [checked]="isFundHouseSelected(fundHouse.id)"
                                      (change)="onFundHouseCheckboxChange(fundHouse.id, $event)">
                                    <label class="form-check-label" [for]="'fund-house-' + fundHouse.id">{{
                                      fundHouse.name }}</label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>



                        <!-- Selected Banks Table (shown only after form submission) -->
                        <div class="row mt-4" *ngIf="instituteFormSubmitted && hasAnyBankSelected()">
                          <div class="col-12">
                            <div class="card">
                              <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Selected Institutions</h6>
                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                  (click)="resetInstituteForm()">
                                  <i data-feather="edit-2" class="icon-xs me-1" appFeatherIcon></i> Edit Selections
                                </button>
                              </div>
                              <div class="card-body p-0">
                                <div class="table-responsive">
                                  <table class="table table-bordered table-hover mb-0">
                                    <thead class="table-light">
                                      <tr>
                                        <th style="width: 60px;">Sr No</th>
                                        <th>Institution Name</th>
                                        <th>Category</th>
                                        <th style="width: 120px;">Action</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <!-- Government Banks -->
                                      <ng-container *ngFor="let bank of getSelectedGovtBankDetails(); let i = index">
                                        <tr>
                                          <td class="text-center">{{i + 1}}</td>
                                          <td>{{bank.name}}</td>
                                          <td>Government Bank</td>
                                          <td>
                                            <select class="form-select form-select-sm"
                                              [(ngModel)]="bankStatuses[getBankStatusKey('govt', bank.id)].action"
                                              name="action_govt_{{bank.id}}">
                                              <option value="" selected disabled>Select</option>
                                              <option value="Yes">Yes</option>
                                              <option value="No">No</option>
                                            </select>
                                          </td>
                                        </tr>
                                      </ng-container>

                                      <!-- Private Banks -->
                                      <ng-container *ngFor="let bank of getSelectedPrivateBankDetails(); let i = index">
                                        <tr>
                                          <td class="text-center">{{getSelectedGovtBankDetails().length + i + 1}}</td>
                                          <td>{{bank.name}}</td>
                                          <td>Private Bank</td>
                                          <td>
                                            <select class="form-select form-select-sm"
                                              [(ngModel)]="bankStatuses[getBankStatusKey('private', bank.id)].action"
                                              name="action_private_{{bank.id}}">
                                              <option value="" selected disabled>Select</option>
                                              <option value="Yes">Yes</option>
                                              <option value="No">No</option>
                                            </select>
                                          </td>
                                        </tr>
                                      </ng-container>

                                      <!-- NBFCs -->
                                      <ng-container *ngFor="let nbfc of getSelectedNBFCDetails(); let i = index">
                                        <tr>
                                          <td class="text-center">{{getSelectedGovtBankDetails().length +
                                            getSelectedPrivateBankDetails().length + i + 1}}</td>
                                          <td>{{nbfc.name}}</td>
                                          <td>NBFC</td>
                                          <td>
                                            <select class="form-select form-select-sm"
                                              [(ngModel)]="bankStatuses[getBankStatusKey('nbfc', nbfc.id)].action"
                                              name="action_nbfc_{{nbfc.id}}">
                                              <option value="" selected disabled>Select</option>
                                              <option value="Yes">Yes</option>
                                              <option value="No">No</option>
                                            </select>
                                          </td>
                                        </tr>
                                      </ng-container>

                                      <!-- Corporate Consultancies -->
                                      <ng-container
                                        *ngFor="let consultancy of getSelectedConsultancyDetails(); let i = index">
                                        <tr>
                                          <td class="text-center">{{getSelectedGovtBankDetails().length +
                                            getSelectedPrivateBankDetails().length + getSelectedNBFCDetails().length + i
                                            + 1}}</td>
                                          <td>{{consultancy.name}}</td>
                                          <td>Corporate Consultancy</td>
                                          <td>
                                            <select class="form-select form-select-sm"
                                              [(ngModel)]="bankStatuses[getBankStatusKey('consultancy', consultancy.id)].action"
                                              name="action_consultancy_{{consultancy.id}}">
                                              <option value="" selected disabled>Select</option>
                                              <option value="Yes">Yes</option>
                                              <option value="No">No</option>
                                            </select>
                                          </td>
                                        </tr>
                                      </ng-container>

                                      <!-- Fund Houses -->
                                      <ng-container
                                        *ngFor="let fundHouse of getSelectedFundHouseDetails(); let i = index">
                                        <tr>
                                          <td class="text-center">{{getSelectedGovtBankDetails().length +
                                            getSelectedPrivateBankDetails().length + getSelectedNBFCDetails().length +
                                            getSelectedConsultancyDetails().length + i + 1}}</td>
                                          <td>{{fundHouse.name}}</td>
                                          <td>Fund House</td>
                                          <td>
                                            <select class="form-select form-select-sm"
                                              [(ngModel)]="bankStatuses[getBankStatusKey('fundHouse', fundHouse.id)].action"
                                              name="action_fundHouse_{{fundHouse.id}}">
                                              <option value="" selected disabled>Select</option>
                                              <option value="Yes">Yes</option>
                                              <option value="No">No</option>
                                            </select>
                                          </td>
                                        </tr>
                                      </ng-container>
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Data Explanation Section (shown only after form submission) -->
                          <div class="card   mt-4" *ngIf="instituteFormSubmitted && hasAnyBankSelected()">
                            <div class="card-header" style="background-color: #f8f9fa; border-left: 4px solid #3F828B;">
                              <h5 class="card-title mb-0" style="color: #3F828B;"><i data-feather="info"
                                  class="icon-sm me-2" appFeatherIcon></i> Data Explanation</h5>
                            </div>
                            <div class="card-body">
                              <div class="row g-3">
                                <!-- Data Explanation Dropdown -->
                                <div class="col-12 col-md-6 col-lg-3 mb-3">
                                  <label class="form-label fw-medium">Data Explanation</label>
                                  <select class="form-select form-select-sm rounded-3 border-0  "
                                    [(ngModel)]="dataExplanation" name="dataExplanation">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Yes">Yes</option>
                                    <option value="No">No</option>
                                  </select>
                                </div>

                                <!-- Status Dropdown -->
                                <div class="col-12 col-md-6 col-lg-3 mb-3">
                                  <label class="form-label fw-medium">Status</label>
                                  <select class="form-select form-select-sm rounded-3 border-0  "
                                    [(ngModel)]="dataExplanationStatus" name="dataExplanationStatus"
                                    (change)="onDataExplanationStatusChange($event)">
                                    <option value="" selected disabled>Select</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Rejected">Rejected</option>
                                    <option value="Query">Query</option>
                                  </select>
                                </div>
                              </div>

                              <!-- Follow-up Section (shown only when Status is Rejected or Query) -->
                              <div class="follow-up-container rounded-3 p-3 mt-3"
                                style="background-color: #f8f9fa; border-left: 4px solid #df5316;"
                                *ngIf="shouldShowDataExplanationFollowUp()">
                                <h6 class="mb-3 d-flex align-items-center" style="color: #df5316;">
                                  <i data-feather="calendar" class="icon-sm me-2" appFeatherIcon></i>
                                  <span>Schedule Follow-up</span>
                                </h6>
                                <div class="row g-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label fw-medium">Notes</label>
                                    <textarea class="form-control rounded-3 border-0  " rows="2"
                                      [(ngModel)]="dataExplanationNotes" name="dataExplanationNotes"
                                      placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label fw-medium">Date</label>
                                    <input type="date" class="form-control rounded-3 border-0  "
                                      [(ngModel)]="dataExplanationFollowUpDate" name="dataExplanationFollowUpDate">
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label fw-medium">Time</label>
                                    <input type="time" class="form-control rounded-3 border-0  "
                                      [(ngModel)]="dataExplanationFollowUpTime" name="dataExplanationFollowUpTime">
                                  </div>
                                  <div class="col-12 text-end mt-3">
                                    <button type="button" class="btn btn-sm btn-primary rounded-3 px-4"
                                      style="background-color: #df5316; border-color: #df5316;"
                                      (click)="submitDataExplanationFollowUp()">
                                      <i data-feather="check-circle" class="icon-xs me-1" appFeatherIcon></i> Submit
                                      Follow-up
                                    </button>
                                  </div>
                                </div>

                                <!-- Follow-up History Section -->
                                <div class="follow-up-history mt-4" *ngIf="hasDataExplanationFollowUps()">
                                  <h6 class="mb-3 d-flex align-items-center" style="color: #366F76;">
                                    <i data-feather="clock" class="icon-sm me-2" appFeatherIcon></i>
                                    <span class="text-light">Follow-up History</span>
                                  </h6>
                                  <div class="table-responsive">
                                    <table class="table table-sm mb-0 border-0">
                                      <thead>
                                        <tr class="border-bottom">
                                          <th style="width: 120px; border: none;">Date</th>
                                          <th style="width: 100px; border: none;">Time</th>
                                          <th style="width: 120px; border: none;">Status</th>
                                          <th style="border: none;">Notes</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr *ngFor="let followUp of getLatestDataExplanationFollowUps(); let i = index"
                                          [ngClass]="{'bg-light': i % 2 === 0}">
                                          <td style="border: none;">{{followUp.date}}</td>
                                          <td style="border: none;">{{followUp.time}}</td>
                                          <td style="border: none;">
                                            <span class="badge rounded-pill" [ngClass]="{
                                            'bg-success': followUp.status === 'Approved',
                                            'bg-danger': followUp.status === 'Rejected',
                                            'bg-warning text-dark': followUp.status === 'Query'
                                          }">{{followUp.status}}</span>
                                          </td>
                                          <td style="border: none;">{{followUp.notes}}</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Form Buttons (shown only before form submission) -->
                        <div class="text-end mt-4" *ngIf="!instituteFormSubmitted">
                          <button type="button" class="btn btn-primary" (click)="submitInstituteForm()">
                            <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Save & Next
                          </button>
                        </div>

                        <!-- Add New Button (shown only after form submission) -->
                        <div class="text-end mt-4" *ngIf="instituteFormSubmitted && !hasAnyBankSelected()">
                          <button type="button" class="btn btn-primary" (click)="resetInstituteForm()">
                            <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Institutions
                          </button>
                        </div>
                      </form>

                    </div>
                  </div>
                </div>
              </ng-template>
            </li>

            <li [ngbNavItem]="5">
              <a ngbNavLink>In Principles</a>
              <ng-template ngbNavContent>
                <!-- In Principles Content -->
                <div class="card card-body border-0 ">
                  <div>
                    <h6 class="card-subtitle mb-4">In Principles</h6>

                    <div class="in-principles-content">
                      <form class="forms-sample">
                        <div class="row">
                          <!-- In Principles Form Fields -->
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Project Name</label>
                            <input type="text" class="form-control" placeholder="Enter project name">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Company Name</label>
                            <input type="text" class="form-control" placeholder="Enter company name">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Main Promoter Name</label>
                            <input type="text" class="form-control" placeholder="Enter promoter name">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Institute Type</label>
                            <select class="form-select">
                              <option value="">Select institute type</option>
                              <option value="government">Government Bank</option>
                              <option value="private">Private Bank</option>
                              <option value="nbfc">NBFC</option>
                            </select>
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Institute Name</label>
                            <input type="text" class="form-control" placeholder="Enter institute name">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Facility</label>
                            <input type="text" class="form-control" placeholder="Enter facility">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Loan Amount (In Crs)</label>
                            <input type="number" class="form-control" placeholder="Enter loan amount">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Tenure</label>
                            <input type="number" class="form-control" placeholder="Enter tenure">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">ROI</label>
                            <input type="number" class="form-control" placeholder="Enter ROI">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Fees in %</label>
                            <input type="number" class="form-control" placeholder="Enter fees percentage">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">GST (Tax) %</label>
                            <input type="number" class="form-control" placeholder="Enter GST percentage">
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Initial Login Fees</label>
                            <input type="number" class="form-control" placeholder="Enter initial login fees">
                          </div>

                          <!-- Form Buttons -->
                          <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                              <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i> Submit
                            </button>
                          </div>
                        </div>
                      </form>

                      <div>
                        <div class="d-flex align-items-center p-3 border-bottom tx-16">
                          <span class="feather icon-edit icon-md me-2"></span>
                          New message
                        </div>
                      </div>
                      <div class="p-3 pb-0">
                        <div class="to">
                          <div class="row mb-3">
                            <label class="col-md-2 col-form-label">To:</label>
                            <div class="col-md-10">
                              <ng-select [items]="peoples" [multiple]=true bindLabel="name" bindValue="id"
                                [hideSelected]="true" [(ngModel)]="selectedTo">

                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                  <span class="ng-value-label"><img [src]="item.picture" width="20px" height="20px">
                                    {{item.name}}</span>
                                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                                </ng-template>

                                <ng-template ng-option-tmp let-item="item">
                                  <img [src]="item.picture" width="20px" height="20px"> {{item.name}}
                                </ng-template>

                              </ng-select>
                            </div>
                          </div>
                        </div>
                        <div class="to cc">
                          <div class="row mb-3">
                            <label class="col-md-2 col-form-label">Cc</label>
                            <div class="col-md-10">
                              <ng-select [items]="peoples" [multiple]=true bindLabel="name" bindValue="id"
                                [hideSelected]="true" [(ngModel)]="selectedCc">

                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                  <span class="ng-value-label"><img [src]="item.picture" width="20px" height="20px">
                                    {{item.name}}</span>
                                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                                </ng-template>

                                <ng-template ng-option-tmp let-item="item">
                                  <img [src]="item.picture" width="20px" height="20px"> {{item.name}}
                                </ng-template>

                              </ng-select>
                            </div>
                          </div>
                        </div>
                        <div class="subject">
                          <div class="row mb-3">
                            <label class="col-md-2 col-form-label">Subject</label>
                            <div class="col-md-10">
                              <input class="form-control" type="text">
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="px-3">
                        <div class="col-md-12">
                          <div class="mb-3">
                            <label class="form-label visually-hidden">Descriptions </label>

                            <quill-editor [(ngModel)]="messageValue" placeholder="Enter Text" [modules]="quillConfig"
                              (onSelectionChanged)="onSelectionChanged($event)"
                              (onContentChanged)="onContentChanged($event)">
                            </quill-editor>

                          </div>
                        </div>
                        <div>
                          <div class="col-md-12">
                            <button class="btn btn-primary me-1 mb-1" type="submit"> Send</button>
                            <button class="btn btn-secondary me-1 mb-1" type="button"> Cancel</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>

          </ul>

          <!-- Tab Content -->
          <div [ngbNavOutlet]="opsTeamNav" class="tab-content"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Follow-up History Modal -->
  <ng-template #followUpHistoryModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title">Follow-up History</h5>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>#</th>
              <th>Document</th>
              <th>Date</th>
              <th>Time</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let followUp of allSectionFollowUps; let i = index">
              <td>{{ i + 1 }}</td>
              <td>{{ followUp.documentName }}</td>
              <td>{{ followUp.date }}</td>
              <td>{{ followUp.time }}</td>
              <td>{{ followUp.notes }}</td>
            </tr>
            <tr *ngIf="allSectionFollowUps.length === 0">
              <td colspan="5" class="text-center">No follow-up history available</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="modal.close('Close click')">Close</button>
    </div>
  </ng-template>
  