
<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">Car Insurance Form</h4>
            <p class="text-secondary">Contract All Risk (CAR) Insurance Application</p>
          </div>
        </div>

        <!-- CAR Insurance Form -->
        <form [formGroup]="carForm" (ngSubmit)="onSubmit()">
          <div class="row">
            <div class="col-md-6 col-lg-4 mb-5">
              <label for="developerName" class="form-label"><PERSON><PERSON>per's Name</label>
              <input type="text" class="form-control" id="developerName" formControlName="developerName"
                [ngClass]="{'is-invalid': isFormSubmitted && form['developerName'].errors}"
                placeholder="Enter developer name">
              @if (isFormSubmitted && form['developerName'].errors?.required) {
                <div class="invalid-feedback"><PERSON><PERSON><PERSON>'s name is required</div>
              }
            </div>

            <div class="col-md-6  col-lg-4 mb-5">
              <label for="developerAddress" class="form-label">Developer's Address with Pin Code</label>
              <textarea class="form-control" id="developerAddress" formControlName="developerAddress" rows="3"
                [ngClass]="{'is-invalid': isFormSubmitted && form['developerAddress'].errors}"
                placeholder="Enter complete address with pin code"></textarea>
              @if (isFormSubmitted && form['developerAddress'].errors?.required) {
                <div class="invalid-feedback">Developer's address is required</div>
              }
            </div>

             <div class="col-md-6 col-lg-4 mb-5">
              <label for="siteAddress" class="form-label">Site Address with Pin Code</label>
              <textarea class="form-control" id="siteAddress" formControlName="siteAddress" rows="3"
                [ngClass]="{'is-invalid': isFormSubmitted && form['siteAddress'].errors}"
                placeholder="Enter complete site address with pin code"></textarea>
              @if (isFormSubmitted && form['siteAddress'].errors?.required) {
                <div class="invalid-feedback">Site address is required</div>
              }
            </div>
             <div class="col-md-6  col-lg-4 mb-5">
              <label for="contractorName" class="form-label">Contractor Name</label>
              <input type="text" class="form-control" id="contractorName" formControlName="contractorName"
                [ngClass]="{'is-invalid': isFormSubmitted && form['contractorName'].errors}">
              @if (isFormSubmitted && form['contractorName'].errors?.required) {
                <div class="invalid-feedback">Contractor name is required</div>
              }
            </div>

            <div class="col-md-6   col-lg-4 mb-5">
              <label for="contractorAddress" class="form-label">Contractor Address with Pin Code</label>
              <textarea class="form-control" id="contractorAddress" formControlName="contractorAddress" rows="3"
                [ngClass]="{'is-invalid': isFormSubmitted && form['contractorAddress'].errors}"
                placeholder="Enter complete address with pin code"></textarea>
              @if (isFormSubmitted && form['contractorAddress'].errors?.required) {
                <div class="invalid-feedback">Contractor address is required</div>
              }
            </div>
            

            <div class="col-md-6 col-lg-4 mb-5">
              <label for="totalProjectValue" class="form-label">Total Project Value Sum Insured for Insurance</label>
              <input type="number" class="form-control" id="totalProjectValue" formControlName="totalProjectValue"
                [ngClass]="{'is-invalid': isFormSubmitted && form['totalProjectValue'].errors}"
                placeholder="Enter amount">
              @if (isFormSubmitted && form['totalProjectValue'].errors?.required) {
                <div class="invalid-feedback">Total project value is required</div>
              }
              @if (isFormSubmitted && form['totalProjectValue'].errors?.min) {
                <div class="invalid-feedback">Amount must be greater than 0</div>
              }
            </div>

            <div class="col-md-6  col-lg-4 mb-5">
              <label for="natureOfWork" class="form-label">Nature of Work</label>
              <input type="text" class="form-control" id="natureOfWork" formControlName="natureOfWork"
                [ngClass]="{'is-invalid': isFormSubmitted && form['natureOfWork'].errors}"
                placeholder="e.g., Construction, Renovation, etc.">
              @if (isFormSubmitted && form['natureOfWork'].errors?.required) {
                <div class="invalid-feedback">Nature of work is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-5">
              <label for="numberOfFloors" class="form-label">Number of Floors</label>
              <input type="number" class="form-control" id="numberOfFloors" formControlName="numberOfFloors"
                [ngClass]="{'is-invalid': isFormSubmitted && form['numberOfFloors'].errors}"
                placeholder="Enter number of floors" min="1">
              @if (isFormSubmitted && form['numberOfFloors'].errors?.required) {
                <div class="invalid-feedback">Number of floors is required</div>
              }
              @if (isFormSubmitted && form['numberOfFloors'].errors?.min) {
                <div class="invalid-feedback">Number of floors must be at least 1</div>
              }
            </div>

            <!-- Basement Information -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label class="form-label">Basement</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="hasBasement" formControlName="hasBasement"
                  (change)="onBasementChange($event)">
                <label class="form-check-label" for="hasBasement">
                  Yes, has basement
                </label>
              </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-5" *ngIf="form['hasBasement'].value">
              <label for="numberOfBasement" class="form-label">Number of Basement</label>
              <input type="number" class="form-control" id="numberOfBasement" formControlName="numberOfBasement"
                [ngClass]="{'is-invalid': isFormSubmitted && form['numberOfBasement'].errors}"
                placeholder="Enter number of basement levels" min="1">
              @if (isFormSubmitted && form['numberOfBasement'].errors?.required) {
                <div class="invalid-feedback">Number of basement is required</div>
              }
              @if (isFormSubmitted && form['numberOfBasement'].errors?.min) {
                <div class="invalid-feedback">Number of basement must be at least 1</div>
              }
            </div>

            <!-- Podium Information -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label class="form-label">Podium</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="hasPodium" formControlName="hasPodium"
                  (change)="onPodiumChange($event)">
                <label class="form-check-label" for="hasPodium">
                  Yes, has podium
                </label>
              </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-5" *ngIf="form['hasPodium'].value">
              <label for="numberOfPodium" class="form-label">Number of Podium</label>
              <input type="number" class="form-control" id="numberOfPodium" formControlName="numberOfPodium"
                [ngClass]="{'is-invalid': isFormSubmitted && form['numberOfPodium'].errors}"
                placeholder="Enter number of podium levels" min="1">
              @if (isFormSubmitted && form['numberOfPodium'].errors?.required) {
                <div class="invalid-feedback">Number of podium is required</div>
              }
              @if (isFormSubmitted && form['numberOfPodium'].errors?.min) {
                <div class="invalid-feedback">Number of podium must be at least 1</div>
              }
            </div>

            <!-- Commercial Shops Information -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label class="form-label">Commercial Shops</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="hasCommercialShops" formControlName="hasCommercialShops"
                  (change)="onCommercialShopsChange($event)">
                <label class="form-check-label" for="hasCommercialShops">
                  Yes, has commercial shops
                </label>
              </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-5" *ngIf="form['hasCommercialShops'].value">
              <label for="numberOfShops" class="form-label">Number of Shops</label>
              <input type="number" class="form-control" id="numberOfShops" formControlName="numberOfShops"
                [ngClass]="{'is-invalid': isFormSubmitted && form['numberOfShops'].errors}"
                placeholder="Enter number of shops" min="1">
              @if (isFormSubmitted && form['numberOfShops'].errors?.required) {
                <div class="invalid-feedback">Number of shops is required</div>
              }
              @if (isFormSubmitted && form['numberOfShops'].errors?.min) {
                <div class="invalid-feedback">Number of shops must be at least 1</div>
              }
            </div>

            <!-- Project Dates -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label for="projectStartDate" class="form-label">Project Start Date (1st day of commencement of work)</label>
              <input type="date" class="form-control" id="projectStartDate" formControlName="projectStartDate"
                [ngClass]="{'is-invalid': isFormSubmitted && form['projectStartDate'].errors}">
              @if (isFormSubmitted && form['projectStartDate'].errors?.required) {
                <div class="invalid-feedback">Project start date is required</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-5">
              <label for="projectEndDate" class="form-label">Project End Date</label>
              <input type="date" class="form-control" id="projectEndDate" formControlName="projectEndDate"
                [ngClass]="{'is-invalid': isFormSubmitted && form['projectEndDate'].errors}">
              @if (isFormSubmitted && form['projectEndDate'].errors?.required) {
                <div class="invalid-feedback">Project end date is required</div>
              }
            </div>

            <!-- Bank Financer Information -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label for="bankFinancerDetails" class="form-label">Bank Financer Name and Address with Pin Code</label>
              <textarea class="form-control" id="bankFinancerDetails" formControlName="bankFinancerDetails" rows="3"
                [ngClass]="{'is-invalid': isFormSubmitted && form['bankFinancerDetails'].errors}"
                placeholder="Enter bank financer name and complete address with pin code"></textarea>
              @if (isFormSubmitted && form['bankFinancerDetails'].errors?.required) {
                <div class="invalid-feedback">Bank financer details are required</div>
              }
            </div>

            <!-- Work Progress Information -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label for="workCompletedPercentage" class="form-label">If work is already started please confirm % of work already done</label>
              <input type="number" class="form-control" id="workCompletedPercentage" formControlName="workCompletedPercentage"
                [ngClass]="{'is-invalid': isFormSubmitted && form['workCompletedPercentage'].errors}"
                placeholder="Enter percentage (0-100)" min="0" max="100">
              @if (isFormSubmitted && form['workCompletedPercentage'].errors?.min) {
                <div class="invalid-feedback">Percentage must be between 0 and 100</div>
              }
              @if (isFormSubmitted && form['workCompletedPercentage'].errors?.max) {
                <div class="invalid-feedback">Percentage must be between 0 and 100</div>
              }
            </div>

            <div class="col-md-6 col-lg-4 mb-5">
              <label for="balanceWorkPercentage" class="form-label">Balance work in %</label>
              <input type="number" class="form-control" id="balanceWorkPercentage" formControlName="balanceWorkPercentage"
                [ngClass]="{'is-invalid': isFormSubmitted && form['balanceWorkPercentage'].errors}"
                placeholder="Enter balance percentage (0-100)" min="0" max="100" readonly>
              @if (isFormSubmitted && form['balanceWorkPercentage'].errors?.min) {
                <div class="invalid-feedback">Percentage must be between 0 and 100</div>
              }
              @if (isFormSubmitted && form['balanceWorkPercentage'].errors?.max) {
                <div class="invalid-feedback">Percentage must be between 0 and 100</div>
              }
            </div>

            <!-- Site Photographs -->
            <div class="col-md-6 col-lg-4 mb-5">
              <label for="sitePhotographs" class="form-label">Site Photographs Available</label>
              <select class="form-select" id="sitePhotographs" formControlName="sitePhotographs"
                [ngClass]="{'is-invalid': isFormSubmitted && form['sitePhotographs'].errors}">
                <option value="">Select Option</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
              @if (isFormSubmitted && form['sitePhotographs'].errors?.required) {
                <div class="invalid-feedback">Please select if site photographs are available</div>
              }
            </div>
          </div>



          <!-- Submit Button -->
          <div class="row mt-4">
            <div class="col-12 text-end">
              <button type="submit" class="btn btn-primary me-2">Submit</button>
              <button type="button" class="btn btn-secondary" (click)="resetForm()">Reset</button>
            </div>
          </div>
        </form>

      </div>
    </div>
  </div>
</div>


