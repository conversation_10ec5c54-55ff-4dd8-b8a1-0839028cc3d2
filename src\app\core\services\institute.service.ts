import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Institute interfaces
export interface Institute {
  id: string;
  name: string;
  code: string;
  type: 'bank' | 'nbfc' | 'cooperative' | 'payment_bank' | 'small_finance' | 'foreign' | 'other';
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active: boolean;
  branch_count?: number;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  ifsc_code?: string;
  swift_code?: string;
  micr_code?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface InstituteCreate {
  name: string;
  code: string;
  type: 'bank' | 'nbfc' | 'cooperative' | 'payment_bank' | 'small_finance' | 'foreign' | 'other';
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  ifsc_code?: string;
  swift_code?: string;
  micr_code?: string;
}

export interface InstituteUpdate {
  name?: string;
  code?: string;
  type?: 'bank' | 'nbfc' | 'cooperative' | 'payment_bank' | 'small_finance' | 'foreign' | 'other';
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  ifsc_code?: string;
  swift_code?: string;
  micr_code?: string;
}

export interface InstituteStatistics {
  total_institutes: number;
  active_institutes: number;
  inactive_institutes: number;
  institutes_by_type: { [type: string]: number };
  total_branches: number;
  popular_institutes: Institute[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class InstituteService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/institutes/`;
  private institutesSubject = new BehaviorSubject<Institute[]>([]);
  public institutes$ = this.institutesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all institutes with optional filtering and pagination
   */
  getInstitutes(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    type?: string;
    country?: string;
    regulatory_body?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<Institute[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Institute[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.institutesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get institute by ID
   */
  getInstituteById(id: string): Observable<APIResponse<Institute>> {
    return this.http.get<APIResponse<Institute>>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new institute
   */
  createInstitute(institute: InstituteCreate): Observable<APIResponse<Institute>> {
    return this.http.post<APIResponse<Institute>>(this.baseUrl, institute)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshInstitutes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update institute
   */
  updateInstitute(id: string, institute: InstituteUpdate): Observable<APIResponse<Institute>> {
    return this.http.put<APIResponse<Institute>>(`${this.baseUrl}/${id}`, institute)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshInstitutes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete institute
   */
  deleteInstitute(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshInstitutes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted institute
   */
  restoreInstitute(id: string): Observable<APIResponse<Institute>> {
    return this.http.post<APIResponse<Institute>>(`${this.baseUrl}/${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshInstitutes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get institute statistics
   */
  getInstituteStatistics(): Observable<APIResponse<InstituteStatistics>> {
    return this.http.get<APIResponse<InstituteStatistics>>(`${this.baseUrl}/statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload institutes
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}/bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshInstitutes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Search institutes
   */
  searchInstitutes(query: string, filters?: {
    is_active?: boolean;
    type?: string;
    country?: string;
    regulatory_body?: string;
  }): Observable<APIResponse<Institute[]>> {
    let params = new HttpParams().set('search', query);

    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key as keyof typeof filters];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Institute[]>>(this.baseUrl, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get institutes for dropdown (simplified data)
   */
  getInstitutesDropdown(): Observable<{ id: string; name: string; code: string; type: string }[]> {
    return this.getInstitutes({ per_page: 1000, is_active: true }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map(institute => ({
            id: institute.id,
            name: institute.name,
            code: institute.code,
            type: institute.type
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get active institutes only
   */
  getActiveInstitutes(): Observable<Institute[]> {
    return this.getInstitutes({ is_active: true }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get institutes by type
   */
  getInstitutesByType(type: string): Observable<Institute[]> {
    return this.getInstitutes({ type }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh institutes data
   */
  refreshInstitutes(): void {
    this.getInstitutes().subscribe();
  }

  /**
   * Clear institutes cache
   */
  clearCache(): void {
    this.institutesSubject.next([]);
  }

  /**
   * Get institute types
   */
  getInstituteTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'bank', label: 'Commercial Bank', description: 'Full-service commercial banks' },
      { value: 'nbfc', label: 'NBFC', description: 'Non-Banking Financial Companies' },
      { value: 'cooperative', label: 'Cooperative Bank', description: 'Cooperative credit institutions' },
      { value: 'payment_bank', label: 'Payment Bank', description: 'Payment and remittance services' },
      { value: 'small_finance', label: 'Small Finance Bank', description: 'Small finance banks' },
      { value: 'foreign', label: 'Foreign Bank', description: 'Foreign bank branches' },
      { value: 'other', label: 'Other', description: 'Other financial institutions' }
    ];
  }

  /**
   * Get country list for dropdown
   */
  getCountryList(): string[] {
    return [
      'India',
      'United States',
      'United Kingdom',
      'Canada',
      'Australia',
      'Singapore',
      'Hong Kong',
      'Japan',
      'Germany',
      'France',
      'Switzerland',
      'Netherlands',
      'Luxembourg',
      'UAE',
      'Malaysia'
    ];
  }

  /**
   * Get regulatory bodies list
   */
  getRegulatoryBodies(): string[] {
    return [
      'RBI (India)',
      'SEBI (India)',
      'IRDAI (India)',
      'PFRDA (India)',
      'Federal Reserve (USA)',
      'FDIC (USA)',
      'Bank of England (UK)',
      'FCA (UK)',
      'OSFI (Canada)',
      'APRA (Australia)',
      'MAS (Singapore)',
      'HKMA (Hong Kong)',
      'JFSA (Japan)',
      'BaFin (Germany)',
      'ACPR (France)'
    ];
  }

  /**
   * Validate IFSC code format
   */
  validateIFSCCode(code: string): boolean {
    // IFSC format: 4 letters + 7 digits (e.g., SBIN0001234)
    const ifscRegex = /^[A-Z]{4}[0-9]{7}$/;
    return ifscRegex.test(code);
  }

  /**
   * Validate SWIFT code format
   */
  validateSWIFTCode(code: string): boolean {
    // SWIFT format: 8 or 11 characters (e.g., SBININBB or SBININBB123)
    const swiftRegex = /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
    return swiftRegex.test(code);
  }

  /**
   * Validate MICR code format
   */
  validateMICRCode(code: string): boolean {
    // MICR format: 9 digits
    const micrRegex = /^[0-9]{9}$/;
    return micrRegex.test(code);
  }

  /**
   * Get institute type label
   */
  getInstituteTypeLabel(type: string): string {
    const types = this.getInstituteTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Institute service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
