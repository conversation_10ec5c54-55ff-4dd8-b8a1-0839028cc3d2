<!-- Department Form Modal -->
<div class="modal-header">
  <h5 class="modal-title">
    <i class="feather icon-users me-2"></i>
    {{ getModalTitle() }}
  </h5>
  <button type="button" class="btn-close" (click)="cancel()" aria-label="Close"></button>
</div>

<div class="modal-body">
  <!-- Error <PERSON> -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="feather icon-alert-circle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null" aria-label="Close"></button>
  </div>

  <!-- Department Form -->
  <form [formGroup]="departmentForm" (ngSubmit)="save()">
    
    <!-- Department Name -->
    <div class="mb-3">
      <label for="departmentName" class="form-label">
        Department Name <span class="text-danger">*</span>
      </label>
      <input 
        type="text" 
        id="departmentName"
        class="form-control"
        [class.is-invalid]="hasError('name')"
        formControlName="name"
        placeholder="Enter department name"
        maxlength="100">
      
      <div *ngIf="hasError('name')" class="invalid-feedback">
        {{ getErrorMessage('name') }}
      </div>
      
      <div class="form-text">
        Enter a unique name for the department (2-100 characters)
      </div>
    </div>

    <!-- Description -->
    <div class="mb-3">
      <label for="departmentDescription" class="form-label">
        Description
      </label>
      <textarea 
        id="departmentDescription"
        class="form-control"
        [class.is-invalid]="hasError('description')"
        formControlName="description"
        placeholder="Enter department description (optional)"
        rows="3"
        maxlength="500"></textarea>
      
      <div *ngIf="hasError('description')" class="invalid-feedback">
        {{ getErrorMessage('description') }}
      </div>
      
      <div class="form-text">
        Optional description of the department's purpose and responsibilities
      </div>
    </div>

    <!-- Parent Department -->
    <div class="mb-3">
      <label for="parentDepartment" class="form-label">
        Parent Department
      </label>
      <select 
        id="parentDepartment"
        class="form-select"
        [class.is-invalid]="hasError('parent_id')"
        formControlName="parent_id">
        
        <option value="">No Parent (Root Department)</option>
        <option 
          *ngFor="let dept of getFilteredParentDepartments()" 
          [value]="dept.id">
          {{ getDepartmentLevelIndicator(dept) }}
        </option>
      </select>
      
      <div *ngIf="hasError('parent_id')" class="invalid-feedback">
        {{ getErrorMessage('parent_id') }}
      </div>
      
      <div class="form-text">
        Select a parent department to create a hierarchy. Leave empty for root-level department.
      </div>
    </div>

    <!-- Hierarchy Preview -->
    <div *ngIf="departmentForm.get('parent_id')?.value" class="mb-3">
      <div class="card bg-light">
        <div class="card-body py-2">
          <small class="text-muted">
            <i class="feather icon-info me-1"></i>
            <strong>Hierarchy Preview:</strong> 
            This department will be at level {{ getPreviewLevel() }}
          </small>
        </div>
      </div>
    </div>

    <!-- Status -->
    <div class="mb-3">
      <label class="form-label">
        Status <span class="text-danger">*</span>
      </label>
      
      <div class="form-check form-switch">
        <input 
          class="form-check-input" 
          type="checkbox" 
          id="departmentStatus"
          formControlName="is_active">
        <label class="form-check-label" for="departmentStatus">
          <span *ngIf="departmentForm.get('is_active')?.value" class="text-success">
            <i class="feather icon-check-circle me-1"></i>
            Active
          </span>
          <span *ngIf="!departmentForm.get('is_active')?.value" class="text-muted">
            <i class="feather icon-pause-circle me-1"></i>
            Inactive
          </span>
        </label>
      </div>
      
      <div class="form-text">
        Active departments are available for employee assignment and appear in dropdowns
      </div>
    </div>

    <!-- Form Summary (for edit mode) -->
    <div *ngIf="isEditMode && department" class="mb-3">
      <div class="card border-info">
        <div class="card-header bg-info text-white py-2">
          <h6 class="mb-0">
            <i class="feather icon-info me-1"></i>
            Current Department Information
          </h6>
        </div>
        <div class="card-body py-2">
          <div class="row">
            <div class="col-md-6">
              <small class="text-muted">Current Level:</small>
              <div class="fw-bold">Level {{ department.level }}</div>
            </div>
            <div class="col-md-6">
              <small class="text-muted">Employee Count:</small>
              <div class="fw-bold">{{ department.employee_count || 0 }} employees</div>
            </div>
            <div class="col-12 mt-2" *ngIf="department.created_at">
              <small class="text-muted">Created:</small>
              <div class="fw-bold">{{ department.created_at | date:'medium' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Validation Summary -->
    <div *ngIf="departmentForm.invalid && (departmentForm.dirty || departmentForm.touched)" 
         class="alert alert-warning">
      <h6 class="alert-heading">
        <i class="feather icon-alert-triangle me-1"></i>
        Please fix the following issues:
      </h6>
      <ul class="mb-0">
        <li *ngIf="hasError('name')">{{ getErrorMessage('name') }}</li>
        <li *ngIf="hasError('description')">{{ getErrorMessage('description') }}</li>
        <li *ngIf="hasError('parent_id')">{{ getErrorMessage('parent_id') }}</li>
        <li *ngIf="hasError('is_active')">{{ getErrorMessage('is_active') }}</li>
      </ul>
    </div>

  </form>
</div>

<div class="modal-footer">
  <div class="d-flex justify-content-between w-100">
    <!-- Reset Button (for edit mode) -->
    <div>
      <button 
        *ngIf="isEditMode" 
        type="button" 
        class="btn btn-outline-warning"
        (click)="reset()"
        [disabled]="saving">
        <i class="feather icon-refresh-cw me-1"></i>
        Reset
      </button>
    </div>
    
    <!-- Action Buttons -->
    <div class="d-flex gap-2">
      <button 
        type="button" 
        class="btn btn-secondary" 
        (click)="cancel()"
        [disabled]="saving">
        <i class="feather icon-x me-1"></i>
        Cancel
      </button>
      
      <button 
        type="button" 
        class="btn btn-primary" 
        (click)="save()"
        [disabled]="departmentForm.invalid || saving">
        
        <!-- Loading Spinner -->
        <span *ngIf="saving" class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </span>
        
        <!-- Save Icon -->
        <i *ngIf="!saving" class="feather icon-save me-1"></i>
        
        {{ getSaveButtonText() }}
      </button>
    </div>
  </div>
</div>
