import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class MarkAttendanceGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    console.log('🛡️ MARK ATTENDANCE GUARD - Checking access for Mark Attendance');
    console.log('🔑 Required permission: attendance:create');

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ MARK ATTENDANCE GUARD - No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Use STRICT permission checking from AuthService (no wildcards, no superuser bypass)
    const hasPermission = this.authService.hasPermission('attendance:create');

    if (!hasPermission) {
      console.log('❌ MARK ATTENDANCE GUARD - Permission DENIED');
      console.log('🔑 Required: attendance:create');
      console.log('👤 User permissions:', currentUser.permissions || []);
      console.log('📧 User email:', currentUser.email);
      console.log('🎭 User role:', currentUser.role);
      console.log('🚫 Redirecting to dashboard');
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log('✅ MARK ATTENDANCE GUARD - Permission GRANTED');
    console.log('🎯 User can access Mark Attendance functionality');
    console.log('👤 User permissions:', currentUser.permissions || []);
    return true;
  }
}
