<!doctype html>

<html lang="en">
<head>
  <meta charset="utf-8">
  <title>BizzCorp - Business Management Platform</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- PWA Meta Tags -->
  <meta name="description" content="High-performance business management application with offline capabilities">
  <meta name="theme-color" content="#2563eb">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="BizzCorp">
  <meta name="msapplication-TileColor" content="#2563eb">
  <meta name="msapplication-config" content="browserconfig.xml">

  <!-- PWA Manifest -->
  <link rel="manifest" href="manifest.json">

  <!-- P<PERSON> Icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/icon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/icon-16x16.png">
  <link rel="apple-touch-icon" href="assets/icons/icon-192x192.png">
  <link rel="apple-touch-icon" sizes="152x152" href="assets/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/icon-180x180.png">
  <link rel="apple-touch-icon" sizes="167x167" href="assets/icons/icon-167x167.png">

  <link rel="stylesheet" href="css/splash-screen.css">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
  <!-- End fonts -->
</head>
<body>

  <!-- START: Setting up theme mode on page load for the splash screen -->
  <script src="assets/js/theme-init.js"></script>
  <!-- END: Setting up theme mode on page load for the splash screen -->

  <app-root>
    <!-- START: Splash screen -->
    <!-- <div class="splash-screen">
      <div class="logo"></div>
      <div class="spinner"></div>
    </div> -->
    <!-- END: Splash screen -->
  </app-root>

</body>
</html>
