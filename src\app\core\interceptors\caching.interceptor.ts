import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheManagerService } from '../services/cache-manager.service';

export interface CacheEntry {
  response: HttpResponse<any>;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

/**
 * HTTP Caching Interceptor
 * 
 * Provides intelligent caching for GET requests to reduce redundant API calls.
 * Supports configurable TTL (Time To Live) and cache invalidation strategies.
 */
@Injectable()
export class CachingInterceptor implements HttpInterceptor {
  private cache = new Map<string, CacheEntry>();

  constructor(private cacheManager: CacheManagerService) {}
  
  // Default cache TTL configurations (in milliseconds)
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly CACHE_RULES: { [pattern: string]: number } = {
    // Master data - cache for 30 minutes (rarely changes)
    '/api/v1/master-data': 30 * 60 * 1000,
    '/api/v1/private-banks': 30 * 60 * 1000,
    '/api/v1/nbfcs': 30 * 60 * 1000,
    '/api/v1/institutes': 30 * 60 * 1000,
    '/api/v1/corporate-consultancies': 30 * 60 * 1000,
    
    // Settings and configuration - cache for 15 minutes
    '/api/v1/settings': 15 * 60 * 1000,
    '/api/v1/i18n/translations': 15 * 60 * 1000,
    '/api/v1/i18n/languages': 15 * 60 * 1000,
    
    // Calendar and holidays - cache for 10 minutes
    '/api/v1/calendar/holidays': 10 * 60 * 1000,
    '/api/v1/calendar/test/holidays': 10 * 60 * 1000,
    
    // Employee data - cache for 5 minutes (more dynamic)
    '/api/v1/employees': 5 * 60 * 1000,
    '/api/v1/users/me': 5 * 60 * 1000,
    
    // Leave types and policies - cache for 10 minutes
    '/api/v1/leave/types': 10 * 60 * 1000,
    '/api/v1/leave/policies': 10 * 60 * 1000,
    
    // Role and permission data - cache for 10 minutes
    '/api/v1/roles': 10 * 60 * 1000,
    '/api/v1/permissions': 10 * 60 * 1000,
  };

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Only cache GET requests
    if (request.method !== 'GET') {
      return next.handle(request);
    }

    // Skip caching for requests with specific headers
    if (request.headers.has('X-Skip-Cache') || request.headers.has('Cache-Control')) {
      return next.handle(request);
    }

    const cacheKey = this.getCacheKey(request);
    const cachedEntry = this.cache.get(cacheKey);

    // Check if we have a valid cached response
    if (cachedEntry && this.isCacheValid(cachedEntry)) {
      console.log(`🎯 CachingInterceptor: Cache HIT for ${request.url}`);
      this.cacheManager.recordHit();
      return of(cachedEntry.response.clone());
    }

    console.log(`🔄 CachingInterceptor: Cache MISS for ${request.url}`);
    this.cacheManager.recordMiss();

    // Make the request and cache the response
    return next.handle(request).pipe(
      tap(event => {
        if (event instanceof HttpResponse && event.status === 200) {
          const ttl = this.getTTL(request.url);
          const cacheEntry: CacheEntry = {
            response: event.clone(),
            timestamp: Date.now(),
            ttl: ttl
          };
          
          this.cache.set(cacheKey, cacheEntry);
          console.log(`💾 CachingInterceptor: Cached response for ${request.url} (TTL: ${ttl / 1000}s)`);
          
          // Clean up expired entries periodically
          this.cleanupExpiredEntries();
        }
      })
    );
  }

  /**
   * Generate a unique cache key for the request
   */
  private getCacheKey(request: HttpRequest<any>): string {
    // Include URL and query parameters in the cache key
    const url = request.urlWithParams;
    
    // Include relevant headers that might affect the response
    const relevantHeaders = ['Accept-Language', 'Authorization'];
    const headerString = relevantHeaders
      .map(header => request.headers.get(header) || '')
      .join('|');
    
    return `${request.method}:${url}:${headerString}`;
  }

  /**
   * Check if a cache entry is still valid
   */
  private isCacheValid(entry: CacheEntry): boolean {
    const now = Date.now();
    return (now - entry.timestamp) < entry.ttl;
  }

  /**
   * Get TTL for a specific URL based on cache rules
   */
  private getTTL(url: string): number {
    // Find matching cache rule
    for (const [pattern, ttl] of Object.entries(this.CACHE_RULES)) {
      if (url.includes(pattern)) {
        return ttl;
      }
    }
    
    return this.DEFAULT_TTL;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (!this.isCacheValid(entry)) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => {
      this.cache.delete(key);
    });
    
    if (keysToDelete.length > 0) {
      console.log(`🧹 CachingInterceptor: Cleaned up ${keysToDelete.length} expired cache entries`);
    }
  }

  /**
   * Clear all cached entries
   */
  public clearCache(): void {
    this.cache.clear();
    console.log('🗑️ CachingInterceptor: Cache cleared');
  }

  /**
   * Clear cache entries matching a pattern
   */
  public clearCacheByPattern(pattern: string): void {
    const keysToDelete: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => {
      this.cache.delete(key);
    });
    
    console.log(`🗑️ CachingInterceptor: Cleared ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; entries: { key: string; age: number; ttl: number }[] } {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      age: now - entry.timestamp,
      ttl: entry.ttl
    }));
    
    return {
      size: this.cache.size,
      entries
    };
  }
}
