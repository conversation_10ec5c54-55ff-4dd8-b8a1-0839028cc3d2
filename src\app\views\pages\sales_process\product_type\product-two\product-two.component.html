<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card ">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">Product 2</h4>
            <p class="text-secondary">Manage Loan Products (HL/LAP/LRD)</p>
          </div>
        </div>

        <!-- Product 2 Form -->
        <form [formGroup]="product2Form" (ngSubmit)="onSubmit()">
          <div class="row">
            <!-- Product Type field moved to the top -->
            <div class="col-md-3 mb-3">
              <label for="productType" class="form-label">Product Type</label>
              <input type="text" class="form-control" id="productType" formControlName="productType"
                [ngClass]="{'is-invalid': isFormSubmitted && form.productType.errors}">
              @if (isFormSubmitted && form.productType.errors?.required) {
                <div class="invalid-feedback">Product type is required</div>
              }
            </div>

            <div class="col-md-3 mb-3">
              <label for="Unique Code" class="form-label">Unique Code</label>
              <input type="text" class="form-control" id="Unique Code" formControlName="Unique Code" readonly>
              <small class="text-muted">Auto-generated from lead assign</small>
            </div>

            <div class="col-md-3 mb-3">
              <label for="dt" class="form-label">Date</label>
              <input type="text" class="form-control" id="dt" formControlName="dt" readonly>
              <small class="text-muted">Auto-generated</small>
            </div>

            <div class="col-md-3 mb-3">
              <label for="leadSource" class="form-label">Lead Source</label>
              <input type="text" class="form-control" id="leadSource" formControlName="leadSource" readonly>
              <small class="text-muted">Auto-generated from lead assign</small>
            </div>

        

            <div class="col-md-3 mb-3">
              <label for="nameOfCustomer" class="form-label">Customer Name</label>
              <input type="text" class="form-control" id="nameOfCustomer" formControlName="nameOfCustomer"
                [ngClass]="{'is-invalid': isFormSubmitted && form.nameOfCustomer.errors}">
              @if (isFormSubmitted && form.nameOfCustomer.errors?.required) {
                <div class="invalid-feedback">Customer name is required</div>
              }
            </div>

            <div class="col-md-3 mb-3">
              <label for="status" class="form-label">Status</label>
              <select class="form-select" id="status" formControlName="status"
                [ngClass]="{'is-invalid': isFormSubmitted && form.status.errors}">
                <option value="" selected>Select Status</option>
                <option value="SL">Salaried (SL)</option>
                <option value="SE">Self Employed (SE)</option>
              </select>
              @if (isFormSubmitted && form.status.errors?.required) {
                <div class="invalid-feedback">Status is required</div>
              }
            </div>

            <div class="col-md-3 mb-3">
              <label for="contactNo" class="form-label">Contact Number</label>
              <input type="text" class="form-control" id="contactNo" formControlName="contactNo"
                [ngClass]="{'is-invalid': isFormSubmitted && form.contactNo.errors}">
              @if (isFormSubmitted && form.contactNo.errors?.required) {
                <div class="invalid-feedback">Contact number is required</div>
              }
              @if (isFormSubmitted && form.contactNo.errors?.pattern) {
                <div class="invalid-feedback">Enter a valid 10-digit number</div>
              }
            </div>

            <div class="col-md-3 mb-3">
              <label for="mailId" class="form-label">Email</label>
              <input type="email" class="form-control" id="mailId" formControlName="mailId"
                [ngClass]="{'is-invalid': isFormSubmitted && form.mailId.errors}">
              @if (isFormSubmitted && form.mailId.errors?.required) {
                <div class="invalid-feedback">Email is required</div>
              }
              @if (isFormSubmitted && form.mailId.errors?.email) {
                <div class="invalid-feedback">Enter a valid email</div>
              }
            </div>

            <div class="col-md-3 mb-3">
              <label for="loanAmount" class="form-label">Loan Amount</label>
              <input type="number" class="form-control" id="loanAmount" formControlName="loanAmount"
                [ngClass]="{'is-invalid': isFormSubmitted && form.loanAmount.errors}">
              @if (isFormSubmitted && form.loanAmount.errors?.required) {
                <div class="invalid-feedback">Loan amount is required</div>
              }
              @if (isFormSubmitted && form.loanAmount.errors?.min) {
                <div class="invalid-feedback">Amount must be positive</div>
              }
            </div>
          </div>

          <!-- Conditional display of employee forms based on selected status and product sub-type -->
          <div class="row mt-4">
            <div class="col-md-12">
              @if (selectedStatus === 'SL') {
                <!-- Only one instance of salaried-employee with all necessary props -->
                <app-salaried-employee
                  #salariedEmployeeRef
                  [formType]="selectedProductSubType === 'Home Loan' ? 'HL' : 'LAP_LRD'"
                  [selectedProductSubType]="selectedProductSubType">
                </app-salaried-employee>
              } @else if (selectedStatus === 'SE') {
                <app-self-employee
                  #selfEmployeeRef
                  [selectedProductSubType]="selectedProductSubType">
                </app-self-employee>
              }
            </div>
          </div>

          <!-- Submit and Cancel Buttons - Only show when not in edit mode -->
          <div class="row mt-4" *ngIf="!isInEditMode">
            <div class="col-12 text-end">
              <button type="submit" class="btn btn-primary me-3" [disabled]="isFormSubmitted && product2Form.invalid">
                <i class="fas fa-save me-1"></i>Save & Submit
              </button>
              <button type="button" class="btn btn-secondary" (click)="onCancel()">
                <i class="fas fa-times me-1"></i>Cancel
              </button>
            </div>
          </div>
        </form>

      </div>
    </div>
  </div>
</div>