<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h4 class="card-title mb-0">
                <i [attr.data-feather]="isEditMode ? 'edit' : 'user-plus'" class="icon-sm me-2" appFeatherIcon></i>
                {{ isEditMode ? 'Edit User' : 'Create New User' }}
              </h4>
              <p class="text-muted mb-0">
                {{ isEditMode ? 'Update user information and settings' : 'Add a new user to the system' }}
              </p>
            </div>
            <div>
              <button 
                type="button" 
                class="btn btn-outline-secondary me-2" 
                (click)="onCancel()"
                [disabled]="submitting">
                <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i>
                Cancel
              </button>
              <button 
                type="button" 
                class="btn btn-outline-warning" 
                (click)="onReset()"
                [disabled]="submitting">
                <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
                Reset
              </button>
            </div>
          </div>
        </div>

        <div class="card-body">
          <!-- Loading State -->
          <div *ngIf="loadingUser" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading user data...</span>
            </div>
            <p class="mt-2 text-muted">Loading user data...</p>
          </div>

          <!-- Permission Check -->
          <div *ngIf="!loadingUser && !canPerformAction(isEditMode ? 'update' : 'create')" class="alert alert-warning">
            <i data-feather="alert-triangle" class="icon-sm me-2" appFeatherIcon></i>
            You don't have permission to {{ isEditMode ? 'edit' : 'create' }} users.
          </div>

          <!-- General Error -->
          <div *ngIf="formErrors.general" class="alert alert-danger">
            <i data-feather="alert-circle" class="icon-sm me-2" appFeatherIcon></i>
            {{ formErrors.general }}
          </div>

          <!-- User Form -->
          <form 
            *ngIf="!loadingUser && canPerformAction(isEditMode ? 'update' : 'create')"
            [formGroup]="userForm" 
            (ngSubmit)="onSubmit()" 
            novalidate>
            
            <div class="row">
              <!-- Basic Information -->
              <div class="col-lg-8">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i data-feather="user" class="icon-sm me-2" appFeatherIcon></i>
                      Basic Information
                    </h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <!-- Email -->
                      <div class="col-md-12 mb-3">
                        <label for="email" class="form-label">
                          Email Address <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                          <span class="input-group-text">
                            <i data-feather="mail" class="icon-sm" appFeatherIcon></i>
                          </span>
                          <input
                            type="email"
                            class="form-control"
                            id="email"
                            formControlName="email"
                            [class.is-invalid]="isFieldInvalid('email')"
                            placeholder="Enter email address"
                            autocomplete="email">
                        </div>
                        <div *ngIf="hasError('email')" class="invalid-feedback d-block">
                          {{ getError('email') }}
                        </div>
                      </div>

                      <!-- First Name -->
                      <div class="col-md-6 mb-3">
                        <label for="first_name" class="form-label">
                          First Name <span class="text-danger">*</span>
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          id="first_name"
                          formControlName="first_name"
                          [class.is-invalid]="isFieldInvalid('first_name')"
                          placeholder="Enter first name"
                          autocomplete="given-name">
                        <div *ngIf="hasError('first_name')" class="invalid-feedback">
                          {{ getError('first_name') }}
                        </div>
                      </div>

                      <!-- Last Name -->
                      <div class="col-md-6 mb-3">
                        <label for="last_name" class="form-label">
                          Last Name <span class="text-danger">*</span>
                        </label>
                        <input
                          type="text"
                          class="form-control"
                          id="last_name"
                          formControlName="last_name"
                          [class.is-invalid]="isFieldInvalid('last_name')"
                          placeholder="Enter last name"
                          autocomplete="family-name">
                        <div *ngIf="hasError('last_name')" class="invalid-feedback">
                          {{ getError('last_name') }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Password Section -->
                <div class="card mt-4">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i data-feather="lock" class="icon-sm me-2" appFeatherIcon></i>
                      {{ isEditMode ? 'Change Password (Optional)' : 'Password' }}
                    </h5>
                    <small class="text-muted">
                      {{ isEditMode ? 'Leave blank to keep current password' : 'Password must be at least 8 characters with uppercase, lowercase, number, and special character' }}
                    </small>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <!-- Password -->
                      <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">
                          Password {{ !isEditMode ? '*' : '' }}
                        </label>
                        <div class="input-group">
                          <span class="input-group-text">
                            <i data-feather="key" class="icon-sm" appFeatherIcon></i>
                          </span>
                          <input
                            type="password"
                            class="form-control"
                            id="password"
                            formControlName="password"
                            [class.is-invalid]="isFieldInvalid('password')"
                            placeholder="Enter password"
                            autocomplete="new-password">
                        </div>
                        <div *ngIf="hasError('password')" class="invalid-feedback d-block">
                          {{ getError('password') }}
                        </div>
                      </div>

                      <!-- Confirm Password -->
                      <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">
                          Confirm Password {{ !isEditMode ? '*' : '' }}
                        </label>
                        <div class="input-group">
                          <span class="input-group-text">
                            <i data-feather="check" class="icon-sm" appFeatherIcon></i>
                          </span>
                          <input
                            type="password"
                            class="form-control"
                            id="confirm_password"
                            formControlName="confirm_password"
                            [class.is-invalid]="isFieldInvalid('confirm_password')"
                            placeholder="Confirm password"
                            autocomplete="new-password">
                        </div>
                        <div *ngIf="hasError('confirm_password')" class="invalid-feedback d-block">
                          {{ getError('confirm_password') }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Settings and Permissions -->
              <div class="col-lg-4">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i data-feather="settings" class="icon-sm me-2" appFeatherIcon></i>
                      User Settings
                    </h5>
                  </div>
                  <div class="card-body">
                    <!-- Active Status -->
                    <div class="mb-4">
                      <label class="form-label">Account Status</label>
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="is_active"
                          formControlName="is_active">
                        <label class="form-check-label" for="is_active">
                          <span *ngIf="userForm.get('is_active')?.value" class="text-success">
                            <i data-feather="check-circle" class="icon-sm me-1" appFeatherIcon></i>
                            Active
                          </span>
                          <span *ngIf="!userForm.get('is_active')?.value" class="text-warning">
                            <i data-feather="x-circle" class="icon-sm me-1" appFeatherIcon></i>
                            Inactive
                          </span>
                        </label>
                      </div>
                      <small class="text-muted">
                        {{ userForm.get('is_active')?.value ? 'User can log in and access the system' : 'User cannot log in or access the system' }}
                      </small>
                    </div>

                    <!-- Super User Status -->
                    <div class="mb-4">
                      <label class="form-label">User Role</label>
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="is_superuser"
                          formControlName="is_superuser">
                        <label class="form-check-label" for="is_superuser">
                          <span *ngIf="userForm.get('is_superuser')?.value" class="text-danger">
                            <i data-feather="shield" class="icon-sm me-1" appFeatherIcon></i>
                            Super User
                          </span>
                          <span *ngIf="!userForm.get('is_superuser')?.value" class="text-secondary">
                            <i data-feather="user" class="icon-sm me-1" appFeatherIcon></i>
                            Regular User
                          </span>
                        </label>
                      </div>
                      <small class="text-muted">
                        {{ userForm.get('is_superuser')?.value ? 'User has full administrative privileges' : 'User has limited access based on assigned roles' }}
                      </small>
                    </div>

                    <!-- User Preview -->
                    <div class="border rounded p-3 bg-light">
                      <h6 class="mb-2">User Preview</h6>
                      <div class="d-flex align-items-center">
                        <div class="avatar avatar-md me-3">
                          <div class="avatar-initial bg-primary text-white rounded-circle">
                            {{ (userForm.get('first_name')?.value?.[0] || userForm.get('email')?.value?.[0] || 'U').toUpperCase() }}
                          </div>
                        </div>
                        <div>
                          <div class="fw-medium">
                            {{ (userForm.get('first_name')?.value + ' ' + userForm.get('last_name')?.value).trim() || 'Full Name' }}
                          </div>
                          <small class="text-muted">{{ userForm.get('email')?.value || '<EMAIL>' }}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                  <button 
                    type="button" 
                    class="btn btn-outline-secondary" 
                    (click)="onCancel()"
                    [disabled]="submitting">
                    <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i>
                    Cancel
                  </button>
                  <button 
                    type="button" 
                    class="btn btn-outline-warning" 
                    (click)="onReset()"
                    [disabled]="submitting">
                    <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
                    Reset
                  </button>
                  <button 
                    type="submit" 
                    class="btn btn-primary"
                    [disabled]="submitting || userForm.invalid">
                    <div 
                      *ngIf="submitting"
                      class="spinner-border spinner-border-sm me-2" 
                      role="status">
                    </div>
                    <i 
                      *ngIf="!submitting"
                      [attr.data-feather]="isEditMode ? 'save' : 'user-plus'" 
                      class="icon-sm me-1" 
                      appFeatherIcon></i>
                    {{ submitting ? 'Saving...' : (isEditMode ? 'Update User' : 'Create User') }}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
