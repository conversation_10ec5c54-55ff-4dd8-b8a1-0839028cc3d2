import { Component, OnInit, TemplateRef, ChangeDetectorRef, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FeatherIconDirective } from '../../../../../../core/feather-icon/feather-icon.directive';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormArray } from '@angular/forms';
import { NgbModal, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';

// These interfaces define the structure of loan and investment data
// They're used as a reference for the form arrays
export interface LoanDetail {
  loanType: string;
  bankName: string;
  sanctionedAmount: number;
  outstandingAmount: number;
  tenure: number;
  emiAmount: number;
  default: string;
}

export interface Investment {
  instituteName: string;
  investmentProduct: string;
  yearlyAmount: number;
  investmentMode: string;
  startDate: Date;
  endDate: Date;
  currentSavingAmount: number;
}

export interface BankAccount {
  accountType: string;
  bankName: string;
}

export interface ITRYear {
  financialYear: string;
  amount: number; // Gross amount
  netAmount?: number;
  date?: Date;
  provisional?: string; // Yes/No dropdown
}

@Component({
  selector: 'app-self-employee',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective,
    NgbModalModule
  ],
  templateUrl: './self-employee.component.html',
  styleUrl: './self-employee.component.scss'
})
export class SelfEmployeeComponent implements OnInit {
  selfEmployeeForm: FormGroup;
  coApplicantSalariedForm: FormGroup;
  coApplicantSelfEmployedForm: FormGroup;
  isFormSubmitted = false;
  isCoAppSalariedFormSubmitted = false;
  isCoAppSelfEmployedFormSubmitted = false;
  selectedProductType: string = 'HL'; // Default to Home Loan
  currentCoApplicantIndex: number = -1;

  // Store detailed co-applicant data for each co-applicant
  coApplicantDetailedData: { [index: number]: any } = {};

  // Flag to indicate if this component is being used as a co-applicant form
  @Input() isCoApplicantForm: boolean = false;
  @Input() selectedProductSubType: string = '';

  // Output event to communicate with parent component when saving co-applicant data
  @Output() coApplicantSaved = new EventEmitter<any>();

  // ViewChild references for modals
  @ViewChild('salariedModal') salariedModal: any;
  @ViewChild('selfEmployedModal') selfEmployedModal: any;

  // Dropdown options
  addressTypes = ['Owned', 'Rental', 'Parental'];
  businessPlaceOwnershipOptions = ['Owned', 'Rental', 'Parental'];
  statusOptions = ['Self Employed Professional', 'Self Employed Non Professional'];
  professionOptions = ['Doctor', 'Lawyer', 'Consultant', 'Architect', 'Accountant', 'Other'];
  businessTypeOptions = ['Trading', 'Manufacturing', 'Service'];
  yesNoOptions = ['Yes', 'No'];
  otherIncomeSourceOptions = ['Rental', 'Agriculture', 'Interest', 'Dividend', 'Capital Gains', 'Other'];
  incomeModeOptions = ['Monthly', 'Quarterly', 'Half-yearly', 'Yearly'];
  loanTypes = [
    'Personal Loan',
    'Business Loan',
    'Gold Loan',
    'Vehicle Loan',
    'Consumer Loan',
    'Home Loan',
    'Loan Against Property',
    'Lease Rental Discounting',
    'Overdraft',
    'Cash Credit',
    'Employer Society Loan',
    'Loan Against Security'
  ];
  investmentModes = ['Monthly', 'Quarterly', 'Half-yearly', 'Yearly'];
  investmentProducts = ['FD', 'RD', 'MIS', 'LIC', 'MF', 'PPF', 'NSC', 'ELSS', 'Bonds', 'Shares', 'Other'];
  accountTypes = ['Current', 'Savings'];
  financialYears: string[] = [];
  relationshipTypes = ['Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Other'];
  occupationTypes = ['Salaried Employee', 'Self Employed'];

  // Property specific options
  propertyTypes = ['Residential', 'Commercial', 'Industrial', 'Land'];

  // Property Details options
  purchaseFromOptions = ['Resale', 'Developer'];

  // Get filtered property type options based on sub product type
  get filteredPropertyTypeOptions(): string[] {
    if (this.selectedProductSubType === 'NRPL') {
      return ['Shop', 'Office'];
    }
    return ['Bunglow', 'NA Plot + Construction', 'Duplex', 'Flat'];
  }

  // Get filtered property type options based on sub product type
  get filteredPropertyTypes(): string[] {
    if (this.selectedProductSubType === 'NRPL') {
      return ['Shop', 'Office'];
    }
    return this.propertyTypes;
  }
  configurationOptions = ['1RK', '1BHK', '1.5 BHK', '2BHK', '2.5 BHK', '3BHK', '3.5 BHK', '4BHK'];
  configurationTypes = ['1 BHK', '2 BHK', '3 BHK', '4 BHK', 'Other'];

  // Property age options (1 to 40 years)
  propertyAgeOptions = Array.from({ length: 40 }, (_, i) => i + 1);

  // Bank name options for APF From
  bankNameOptions = [
    'State Bank of India', 'HDFC Bank', 'ICICI Bank', 'Axis Bank', 'Kotak Mahindra Bank',
    'Punjab National Bank', 'Bank of Baroda', 'Canara Bank', 'Union Bank of India',
    'IDFC First Bank', 'IndusInd Bank', 'Yes Bank', 'Federal Bank', 'South Indian Bank'
  ];

  // Chain agreement options (1 to 10)
  chainAgreementOptions = Array.from({ length: 10 }, (_, i) => i + 1);

  societyStatusOptions = ['Registered', 'Unregistered', 'In Process'];
  ccOptions = ['CC Available', 'OC Available', 'Not Available'];

  constructor(private formBuilder: FormBuilder, private modalService: NgbModal, private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.calculateFinancialYears();
    this.initForm();

    // Instead of dynamic import, we'll use a simpler approach
    // to ensure the form is properly initialized with the correct product type
    console.log('Self-Employee component initialized with product type:', this.selectedProductType);

    // Force update to ensure the form reflects the current product type
    this.forceUpdateFormType();
  }

  // Calculate the last three completed financial years based on current date
  calculateFinancialYears() {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // 0-11 (Jan-Dec)
    const currentYear = currentDate.getFullYear();

    // In India, financial year runs from April to March
    // Determine the last completed financial year
    let lastCompletedFYEndYear = currentYear;

    // If we're in January to March, the last completed FY ended in the previous year
    if (currentMonth < 3) { // January to March
      lastCompletedFYEndYear = currentYear - 1;
    }

    // Generate the last three completed financial years (oldest to newest)
    this.financialYears = [];
    for (let i = 2; i >= 0; i--) {
      const endYear = lastCompletedFYEndYear - i;
      const startYear = endYear - 1;
      // Format as YY-YY (e.g., 22-23, 23-24, 24-25)
      const startYearStr = startYear.toString().slice(-2);
      const endYearStr = endYear.toString().slice(-2);
      this.financialYears.push(`${startYearStr}-${endYearStr}`);
    }
  }

  initForm() {
    // Create the base form with common fields
    this.selfEmployeeForm = this.formBuilder.group({
      // Personal Information
      age: ['', [Validators.required, Validators.min(18), Validators.max(100)]],
      addressType: ['', Validators.required],
      status: ['', Validators.required],

      // Business Information
      profession: ['', Validators.required],
      businessType: ['', Validators.required],
      natureOfBusiness: ['', Validators.required],
      businessName: ['', Validators.required],
      businessPlaceOwnership: ['', Validators.required],
      businessPlace: ['', Validators.required],
      currentBusinessExperience: ['', [Validators.required, Validators.min(0)]],
      totalPastBusinessExperience: ['', [Validators.required, Validators.min(0)]],

      // Financial Information
      monthlyTurnover: ['', [Validators.required, Validators.min(0)]],
      monthlyAvgExpense: ['', [Validators.required, Validators.min(0)]],
      monthlyNetProfit: ['', [Validators.required, Validators.min(0)]],

      // ITR Information
      itrStatus: ['', Validators.required],
      itrYears: this.formBuilder.array([]),

      // Financial Information Table
      monthlyInflow: ['', [Validators.required, Validators.min(0)]],
      yearlyInflow: ['', [Validators.required, Validators.min(0)]],
      monthlyExpenses: ['', [Validators.required, Validators.min(0)]],
      yearlyExpenses: ['', [Validators.required, Validators.min(0)]],
      monthlyNetProfitTable: ['', [Validators.required, Validators.min(0)]],
      yearlyNetProfit: ['', [Validators.required, Validators.min(0)]],
      monthlyTurnoverTable: ['', [Validators.required, Validators.min(0)]],
      yearlyTurnover: ['', [Validators.required, Validators.min(0)]],

      // Other Income
      hasOtherIncome: ['', Validators.required],
      otherIncomeSource: [''],
      otherIncomeAmount: ['', Validators.min(0)],
      otherIncomeMode: [''],

      // Banking Information
      bankAccounts: this.formBuilder.array([]),

      // Business Statutory Documents
      businessStatutoryDocs: [''],

      // Property Details
      purchaseFrom: [''],
      location: [''],
      typeOfProperty: [''],
      configuration: [''],
      areaSqFt: [''],
      salable: [''],
      carpet: [''],
      ageOfProperty: [''],
      constructionStage: [''],
      approvalAuthority: [''],

      // Documents Status (Checkboxes)
      ccDocument: [false],
      ocDocument: [false],
      socRegistrationCertificate: [false],
      shareCertificate: [false],

      // Additional Property Fields
      apfFrom: [''],
      noOfChainAgreement: [''],
      agreementStatus: [''],
      currentMV: [''],
      agreementValue: [''],
      loanAmtRequired: [''],
      ocrRequired: [''],
      ocrPercentage: [''],

      // BT Fields
      ifBt: [''],
      btBankName: [''],
      currentOutstandingLoanAmount: [''],

      // LRD Specific Fields
      loiDetails: [''],
      companyNameLicensee: [''],
      leaseTenor: [''],
      startDate: [''],
      monthlyRent: [''],

      // Loan Details
      loanDetails: this.formBuilder.array([]),

      // Investment Details
      investments: this.formBuilder.array([]),

      // Co-Applicant Details
      coApplicants: this.formBuilder.array([]),
    });

    // Add Home Loan specific fields
    if (this.selectedProductType === 'HL') {
      this.selfEmployeeForm.addControl('propertySelected', this.formBuilder.control('', Validators.required));
      this.selfEmployeeForm.addControl('location', this.formBuilder.control('', Validators.required));
    } else {
      // LAP/LRD specific fields
      this.selfEmployeeForm.addControl('propertySelected', this.formBuilder.control('', Validators.required));
      this.selfEmployeeForm.addControl('location', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('propertyType', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('propertyDetails', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('propertyArea', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('configuration', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('propertyStatus', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('ucPercentage', this.formBuilder.control('', [Validators.min(0), Validators.max(100)]));
      this.selfEmployeeForm.addControl('propertyAge', this.formBuilder.control('', Validators.min(0)));
      this.selfEmployeeForm.addControl('societyStatus', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('ccOc', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('agreementValue', this.formBuilder.control('', Validators.min(0)));
      this.selfEmployeeForm.addControl('purchaseDate', this.formBuilder.control(''));
      this.selfEmployeeForm.addControl('currentMV', this.formBuilder.control('', Validators.min(0)));
      this.selfEmployeeForm.addControl('loanAmountRequired', this.formBuilder.control('', Validators.min(0)));
      this.selfEmployeeForm.addControl('ltvPercentage', this.formBuilder.control('', [Validators.min(0), Validators.max(100)]));
    }

    // Add default ITR years
    this.financialYears.forEach(year => {
      this.addITRYear(year);
    });

    // Add default bank accounts
    this.addBankAccount('Current');
    this.addBankAccount('Savings');

    // Add default empty loan detail
    this.addLoanDetail();

    // Add default empty investment
    this.addInvestment();

    // Add default empty co-applicant
    this.addCoApplicant();
  }

  // Helper method to check if we're using Home Loan form
  get isHomeLoan(): boolean {
    return this.selectedProductType === 'HL';
  }

  // Set the product type for the form
  setProductType(productType: string) {
    console.log('Setting product type to:', productType);
    this.selectedProductType = productType;
    // Trigger a change detection cycle to update all forms
    this.cd.detectChanges();
  }

  // Force update the form based on product type
  forceUpdateFormType() {
    // This method is used to trigger a change detection cycle
    // that will update the form based on the current product type
    this.cd.detectChanges();
  }

  // Synchronize product type across all forms
  syncProductType() {
    console.log('Synchronizing product type across all forms:', this.selectedProductType);

    // Re-initialize forms with the current product type
    if (this.coApplicantSalariedForm) {
      this.initCoApplicantSalariedForm();
    }

    if (this.coApplicantSelfEmployedForm) {
      this.initCoApplicantSelfEmployedForm();
    }

    // Force update to ensure the forms reflect the current product type
    this.forceUpdateFormType();
  }

  // Getters for form arrays
  get itrYearsArray() {
    return this.selfEmployeeForm.get('itrYears') as FormArray;
  }

  get bankAccountsArray() {
    return this.selfEmployeeForm.get('bankAccounts') as FormArray;
  }

  get loanDetailsArray() {
    return this.selfEmployeeForm.get('loanDetails') as FormArray;
  }

  get investmentsArray() {
    return this.selfEmployeeForm.get('investments') as FormArray;
  }

  get coApplicantsArray() {
    return this.selfEmployeeForm.get('coApplicants') as FormArray;
  }

  // Add a new ITR year
  addITRYear(year: string) {
    const itrYear = this.formBuilder.group({
      financialYear: [year, Validators.required],
      amount: ['', [Validators.required, Validators.min(0)]], // Turnover amount
      grossProfit: ['', Validators.min(0)],
      netAmount: ['', Validators.min(0)],
      date: [''],
      provisional: ['No', Validators.required] // Default to No
    });

    this.itrYearsArray.push(itrYear);
  }

  // Add a new bank account
  addBankAccount(type: string) {
    const bankAccount = this.formBuilder.group({
      accountType: [type, Validators.required],
      bankName: ['', Validators.required],
      branchName: ['', Validators.required]
    });

    this.bankAccountsArray.push(bankAccount);
  }

  // Remove a bank account
  removeBankAccount(index: number) {
    this.bankAccountsArray.removeAt(index);
  }

  // Create a bank account form group
  createBankAccount(type: string) {
    const defaultBankName = type === 'Current' ? 'HDFC Bank' : 'SBI Bank';
    const defaultBranchName = type === 'Current' ? 'Andheri Branch' : 'Bandra Branch';

    return this.formBuilder.group({
      accountType: [type, Validators.required],
      bankName: [defaultBankName, Validators.required],
      branchName: [defaultBranchName, Validators.required]
    });
  }

  // Get current accounts
  getCurrentAccounts() {
    return this.bankAccountsArray.controls.filter(account =>
      account.get('accountType')?.value === 'Current'
    );
  }

  // Get savings accounts
  getSavingsAccounts() {
    return this.bankAccountsArray.controls.filter(account =>
      account.get('accountType')?.value === 'Savings'
    );
  }

  // Get current account index in the main array
  getCurrentAccountIndex(currentIndex: number): number {
    let currentCount = 0;
    for (let i = 0; i < this.bankAccountsArray.length; i++) {
      if (this.bankAccountsArray.at(i).get('accountType')?.value === 'Current') {
        if (currentCount === currentIndex) {
          return i;
        }
        currentCount++;
      }
    }
    return -1;
  }

  // Get savings account index in the main array
  getSavingsAccountIndex(savingsIndex: number): number {
    let savingsCount = 0;
    for (let i = 0; i < this.bankAccountsArray.length; i++) {
      if (this.bankAccountsArray.at(i).get('accountType')?.value === 'Savings') {
        if (savingsCount === savingsIndex) {
          return i;
        }
        savingsCount++;
      }
    }
    return -1;
  }

  // Add a new loan detail
  addLoanDetail() {
    const loanDetail = this.formBuilder.group({
      loanType: ['', Validators.required],
      bankName: ['', Validators.required],
      sanctionedAmount: ['', [Validators.required, Validators.min(0)]],
      outstandingAmount: ['', [Validators.required, Validators.min(0)]],
      tenure: ['', [Validators.required, Validators.min(1)]],
      emiAmount: ['', [Validators.required, Validators.min(0)]],
      default: ['No', Validators.required],
      example: [''] // New field for example
    });

    this.loanDetailsArray.push(loanDetail);
  }

  // Remove a loan detail
  removeLoanDetail(index: number) {
    this.loanDetailsArray.removeAt(index);
  }

  // Check if any loan has default = "Yes" to show/hide Example column
  hasAnyLoanWithDefault(): boolean {
    return this.loanDetailsArray.controls.some(control =>
      control.get('default')?.value === 'Yes'
    );
  }

  // Add a new investment
  addInvestment() {
    const investment = this.formBuilder.group({
      instituteName: ['', Validators.required],
      investmentProduct: ['', Validators.required],
      yearlyAmount: ['', [Validators.required, Validators.min(0)]],
      investmentMode: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      currentSavingAmount: ['', [Validators.required, Validators.min(0)]]
    });

    this.investmentsArray.push(investment);
  }

  // Remove an investment
  removeInvestment(index: number) {
    this.investmentsArray.removeAt(index);
  }

  // Co-Applicant Self-Employed Bank Account Methods
  addCoAppSelfEmployedBankAccount(type: string) {
    if (!this.coApplicantSelfEmployedForm) return;

    const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
    const bankAccount = this.formBuilder.group({
      accountType: [type, Validators.required],
      bankName: ['', Validators.required],
      branchName: ['', Validators.required]
    });

    bankAccountsArray.push(bankAccount);
  }

  removeCoAppSelfEmployedBankAccount(index: number) {
    if (!this.coApplicantSelfEmployedForm) return;

    const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
    bankAccountsArray.removeAt(index);
  }

  getCoAppSelfEmployedCurrentAccounts() {
    if (!this.coApplicantSelfEmployedForm) return [];

    const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
    return bankAccountsArray.controls.filter(account =>
      account.get('accountType')?.value === 'Current'
    );
  }

  getCoAppSelfEmployedSavingsAccounts() {
    if (!this.coApplicantSelfEmployedForm) return [];

    const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
    return bankAccountsArray.controls.filter(account =>
      account.get('accountType')?.value === 'Savings'
    );
  }

  getCoAppSelfEmployedCurrentAccountIndex(currentIndex: number): number {
    if (!this.coApplicantSelfEmployedForm) return -1;

    const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
    let currentCount = 0;
    for (let i = 0; i < bankAccountsArray.length; i++) {
      if (bankAccountsArray.at(i).get('accountType')?.value === 'Current') {
        if (currentCount === currentIndex) {
          return i;
        }
        currentCount++;
      }
    }
    return -1;
  }

  getCoAppSelfEmployedSavingsAccountIndex(savingsIndex: number): number {
    if (!this.coApplicantSelfEmployedForm) return -1;

    const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
    let savingsCount = 0;
    for (let i = 0; i < bankAccountsArray.length; i++) {
      if (bankAccountsArray.at(i).get('accountType')?.value === 'Savings') {
        if (savingsCount === savingsIndex) {
          return i;
        }
        savingsCount++;
      }
    }
    return -1;
  }

  // Add a new co-applicant
  addCoApplicant() {
    const coApplicant = this.formBuilder.group({
      name: ['', Validators.required],
      relation: ['', Validators.required],
      age: ['', [Validators.required, Validators.min(18), Validators.max(100)]],
      occupation: ['', Validators.required],
      incomeSource: ['', Validators.required],
      monthlyIncome: ['', [Validators.required, Validators.min(0)]]
    });

    this.coApplicantsArray.push(coApplicant);
  }

  // Remove a co-applicant
  removeCoApplicant(index: number) {
    this.coApplicantsArray.removeAt(index);
  }

  // Populate co-applicant salaried form with stored data
  private populateCoApplicantSalariedFormWithStoredData(storedData: any) {
    if (!this.coApplicantSalariedForm || !storedData) return;

    // Populate basic form fields
    this.coApplicantSalariedForm.patchValue(storedData);

    // Populate arrays if they exist in stored data
    if (storedData.loanDetails && Array.isArray(storedData.loanDetails)) {
      const loanDetailsArray = this.coApplicantSalariedForm.get('loanDetails') as FormArray;
      loanDetailsArray.clear();
      storedData.loanDetails.forEach(() => {
        this.addCoAppSalariedLoanDetail(loanDetailsArray);
      });
      loanDetailsArray.patchValue(storedData.loanDetails);
    }

    if (storedData.investments && Array.isArray(storedData.investments)) {
      const investmentsArray = this.coApplicantSalariedForm.get('investments') as FormArray;
      investmentsArray.clear();
      storedData.investments.forEach(() => {
        this.addCoAppSalariedInvestment(investmentsArray);
      });
      investmentsArray.patchValue(storedData.investments);
    }

    if (storedData.bankAccounts && Array.isArray(storedData.bankAccounts)) {
      const bankAccountsArray = this.coApplicantSalariedForm.get('bankAccounts') as FormArray;
      bankAccountsArray.clear();
      storedData.bankAccounts.forEach((account: any) => {
        this.addCoAppSelfEmployedBankAccount(account.accountType || 'Primary');
      });
      bankAccountsArray.patchValue(storedData.bankAccounts);
    }
  }

  // Populate co-applicant self-employed form with stored data
  public populateCoApplicantSelfEmployedFormWithStoredData(storedData: any) {
    if (!this.coApplicantSelfEmployedForm || !storedData) {
      console.log('❌ Cannot populate form: form or data missing', { form: !!this.coApplicantSelfEmployedForm, data: !!storedData });
      return;
    }

    console.log('🔄 Starting to populate self-employed form with stored data:', storedData);

    try {
      // Populate basic form fields
      this.coApplicantSelfEmployedForm.patchValue(storedData);
      console.log('✅ Basic form fields populated');

      // Populate loan details array if it exists
      if (storedData.loanDetails && Array.isArray(storedData.loanDetails)) {
        const loanDetailsArray = this.coApplicantSelfEmployedForm.get('loanDetails') as FormArray;
        if (loanDetailsArray) {
          loanDetailsArray.clear();
          storedData.loanDetails.forEach(() => {
            this.addCoAppSelfEmployedLoanDetail(loanDetailsArray);
          });
          loanDetailsArray.patchValue(storedData.loanDetails);
          console.log('✅ Loan details array populated with', storedData.loanDetails.length, 'items');
        }
      }

      // Populate investments array if it exists
      if (storedData.investments && Array.isArray(storedData.investments)) {
        const investmentsArray = this.coApplicantSelfEmployedForm.get('investments') as FormArray;
        if (investmentsArray) {
          investmentsArray.clear();
          storedData.investments.forEach(() => {
            this.addCoAppSelfEmployedInvestment(investmentsArray);
          });
          investmentsArray.patchValue(storedData.investments);
          console.log('✅ Investments array populated with', storedData.investments.length, 'items');
        }
      }

      // Populate bank accounts array if it exists
      if (storedData.bankAccounts && Array.isArray(storedData.bankAccounts)) {
        const bankAccountsArray = this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray;
        if (bankAccountsArray) {
          bankAccountsArray.clear();
          storedData.bankAccounts.forEach((account: any) => {
            this.addCoAppSelfEmployedBankAccount(account.accountType || 'Current');
          });
          bankAccountsArray.patchValue(storedData.bankAccounts);
          console.log('✅ Bank accounts array populated with', storedData.bankAccounts.length, 'items');
        }
      }

      // Populate ITR Years array if it exists
      if (storedData.itrYears && Array.isArray(storedData.itrYears)) {
        const itrYearsArray = this.coApplicantSelfEmployedForm.get('itrYears') as FormArray;
        if (itrYearsArray) {
          itrYearsArray.clear();
          // Recreate ITR years with stored data
          storedData.itrYears.forEach((itrYear: any) => {
            const itrYearGroup = this.formBuilder.group({
              financialYear: [itrYear.financialYear || '', Validators.required],
              amount: [itrYear.amount || '', [Validators.required, Validators.min(0)]],
              grossProfit: [itrYear.grossProfit || '', Validators.min(0)],
              netAmount: [itrYear.netAmount || '', Validators.min(0)],
              date: [itrYear.date || ''],
              provisional: [itrYear.provisional || 'No', Validators.required]
            });
            itrYearsArray.push(itrYearGroup);
          });
          console.log('✅ ITR Years array populated with', storedData.itrYears.length, 'items');
        }
      }

      console.log('🎉 Self-employed form population completed successfully');
      console.log('📋 Final form value:', this.coApplicantSelfEmployedForm.value);

    } catch (error) {
      console.error('❌ Error populating self-employed form:', error);
    }
  }

  // Handle occupation change for co-applicant
  onCoApplicantOccupationChange(index: number, occupation: string, content: TemplateRef<any>) {
    this.currentCoApplicantIndex = index;

    // Log the current product type for debugging
    console.log('Current product type before occupation change:', this.selectedProductType);
    console.log('Is Home Loan before occupation change:', this.isHomeLoan);

    // Check if this co-applicant already has detailed data
    const hasExistingDetailedData = this.coApplicantDetailedData[index];
    console.log('Existing detailed data for co-applicant', index, ':', hasExistingDetailedData);

    // Synchronize product type across all forms first
    this.syncProductType();

    // Initialize the appropriate form based on occupation type
    if (occupation === 'Salaried Employee') {
      // Initialize the salaried form with the current product type
      this.initCoApplicantSalariedForm();

      // If there's existing detailed data, populate the form
      if (hasExistingDetailedData) {
        console.log('Populating salaried form with existing data');
        this.populateCoApplicantSalariedFormWithStoredData(hasExistingDetailedData);
      }

      // Log the form structure for debugging
      console.log('Salaried form structure:', this.coApplicantSalariedForm.value);

      // Open the modal
      this.modalService.open(content, {
        centered: true,
        backdrop: 'static',
        keyboard: false,
        size: 'xl'
      });
    } else if (occupation === 'Self Employed') {
      // Initialize the self-employed form with the current product type
      this.initCoApplicantSelfEmployedForm();

      // If there's existing detailed data, populate the form
      if (hasExistingDetailedData) {
        console.log('Populating self-employed form with existing data');
        this.populateCoApplicantSelfEmployedFormWithStoredData(hasExistingDetailedData);
      }

      // Log the form structure for debugging
      console.log('Self-employed form structure:', this.coApplicantSelfEmployedForm.value);

      // Explicitly check if the form has the required fields for Home Loan
      if (this.isHomeLoan) {
        console.log('Checking Home Loan specific fields in self-employed form');
        if (!this.coApplicantSelfEmployedForm.get('ocr')) {
          console.error('OCR field is missing from the form!');
        }
        if (!this.coApplicantSelfEmployedForm.get('ocrPercentage')) {
          console.error('OCR Percentage field is missing from the form!');
        }
        if (!this.coApplicantSelfEmployedForm.get('bankLoanAmount')) {
          console.error('Bank Loan Amount field is missing from the form!');
        }
        if (!this.coApplicantSelfEmployedForm.get('projectApf')) {
          console.error('Project APF field is missing from the form!');
        }
      }

      // Open the modal
      this.modalService.open(content, {
        centered: true,
        backdrop: 'static',
        keyboard: false,
        size: 'xl'
      });
    }

    // Force update to ensure the form reflects the current product type
    this.forceUpdateFormType();
  }

  // Initialize Co-Applicant Salaried Form
  initCoApplicantSalariedForm() {
    // Check if we're editing an existing co-applicant
    const isEditing = this.currentCoApplicantIndex >= 0 && this.currentCoApplicantIndex < this.coApplicantsArray.length;
    const coApplicant = isEditing ? this.coApplicantsArray.at(this.currentCoApplicantIndex) : null;

    // Try to extract job profile and company name from income source if it exists
    let jobProfile = '';
    let companyName = '';

    if (isEditing) {
      const incomeSource = coApplicant?.get('incomeSource')?.value || '';
      if (incomeSource) {
        // Try to parse "Company Name (Job Profile)" format
        const match = incomeSource.match(/(.+) \((.+)\)/);
        if (match && match.length === 3) {
          companyName = match[1];
          jobProfile = match[2];
        }
      }
    }

    // Create form arrays
    const loanDetailsArray = this.formBuilder.array([]);
    const investmentsArray = this.formBuilder.array([]);

    // Create bank accounts array with the same structure as the main form
    const bankAccountsArray = this.formBuilder.array([
      this.createBankAccount('Current'),
      this.createBankAccount('Savings')
    ]);

    // Add default loan detail
    this.addCoAppSalariedLoanDetail(loanDetailsArray);

    // Add default investment
    this.addCoAppSalariedInvestment(investmentsArray);

    // Determine which form to create based on the current product type
    if (this.selectedProductType === 'LAP' || this.selectedProductType === 'LRD') {
      // Create LAP/LRD specific form for co-applicant
      this.coApplicantSalariedForm = this.formBuilder.group({
        // Personal Information
        age: [isEditing ? coApplicant?.get('age')?.value : null, [Validators.required, Validators.min(18), Validators.max(100)]],
        addressType: ['', Validators.required], // Owned/Rental/Parental
        propertyOwnership: ['', Validators.required],

        // Job Information
        jobProfile: [jobProfile || '', Validators.required], // Private/Government/Semi Government/Proprietorship Company
        companyName: [companyName || '', Validators.required],
        currentJobExperience: [null, Validators.required],
        totalPastJobExperience: [null, Validators.required],
        officeDocument: ['', Validators.required], // Appointment Letter/Confirmation Letter/Salary Slip/Certificate

        // Salary Information
        salaryDetails: ['', Validators.required],
        totalGrossSalary: [isEditing ? coApplicant?.get('monthlyIncome')?.value : null, Validators.required],
        deductions: this.formBuilder.group({
          pf: [0],
          pt: [0],
          hra: [0],
          esic: [0],
          employeeLoan: [0],
          societyLoan: [0],
          other: [0]
        }),
        totalDeduction: [0],
        netSalary: [0],
        salaryMode: ['', Validators.required],

        // ITR Information
        itrAvailable: ['', Validators.required],
        formNo16: ['', Validators.required],

        // Bank Accounts
        bankAccounts: bankAccountsArray,

        // Property Information - LAP/LRD specific
        propertySelected: ['', Validators.required],
        location: ['', Validators.required],
        propertyType: ['', Validators.required],
        propertyDetails: [''],
        propertyArea: ['', [Validators.required, Validators.min(0)]],
        configuration: [''],
        status: ['', Validators.required],
        ucPercentage: ['', [Validators.min(0), Validators.max(100)]],
        propertyAge: ['', Validators.min(0)],
        societyStatus: [''],
        ccoc: [''], // CC/OC
        purchaseDate: [''],
        currentMarketValue: [5000000, [Validators.required, Validators.min(0)]],
        loanAmountRequired: [2500000, [Validators.required, Validators.min(0)]],
        ltvPercentage: [50, [Validators.required, Validators.min(0), Validators.max(100)]],

        // Additional LAP/LRD specific fields
        propertyAddress: ['', Validators.required],
        propertyPincode: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
        propertyCity: ['', Validators.required],
        propertyState: ['', Validators.required],
        rentalIncome: ['', Validators.min(0)],
        rentalAgreementTenure: ['', Validators.min(0)],
        propertyTaxPaid: ['', Validators.min(0)],
        propertyInsurance: ['', Validators.min(0)],
        propertyMaintenance: ['', Validators.min(0)],
        existingMortgage: ['No'],
        mortgageBank: [''],
        mortgageOutstandingAmount: ['', Validators.min(0)],
        mortgageEmi: ['', Validators.min(0)],
        propertyValuation: ['', [Validators.required, Validators.min(0)]],
        valuationDate: [''],
        valuationAgency: [''],
        titleClearance: ['', Validators.required],
        legalOpinion: ['', Validators.required],

        // Arrays for dynamic data
        loanDetails: loanDetailsArray,
        investments: investmentsArray,
      });
    } else {
      // Create Home Loan specific form for co-applicant
      this.coApplicantSalariedForm = this.formBuilder.group({
        // Personal Information
        age: [isEditing ? coApplicant?.get('age')?.value : null, [Validators.required, Validators.min(18), Validators.max(100)]],
        addressType: ['', Validators.required],
        residentialStatus: ['', Validators.required],

        // Job Information
        jobProfile: [jobProfile || '', Validators.required],
        companyName: [companyName || '', Validators.required],
        currentJobExperience: [null, Validators.required],
        totalPastJobExperience: [null, Validators.required],
        officeDocument: ['', Validators.required],

        // Salary Information
        salaryDetails: ['', Validators.required],
        totalGrossSalary: [isEditing ? coApplicant?.get('monthlyIncome')?.value : null, Validators.required],
        deductions: this.formBuilder.group({
          pf: [0],
          pt: [0],
          hra: [0],
          esic: [0],
          employeeLoan: [0],
          societyLoan: [0],
          other: [0]
        }),
        totalDeduction: [0],
        netSalary: [0],
        salaryMode: ['', Validators.required],

        // ITR Information
        itrAvailable: ['', Validators.required],
        formNo16: ['', Validators.required],

        // Bank Accounts
        bankAccounts: bankAccountsArray,

        // Property Information
        propertySelected: ['', Validators.required],
        location: ['', Validators.required],

        // Arrays for dynamic data
        loanDetails: loanDetailsArray,
        investments: investmentsArray,
      });
    }

    // Setup value changes for deductions
    this.setupCoAppSalariedFormValueChanges();

    this.isCoAppSalariedFormSubmitted = false;
  }

  // Initialize Co-Applicant Self-Employed Form
  public initCoApplicantSelfEmployedForm() {
    // Check if we're editing an existing co-applicant
    const isEditing = this.currentCoApplicantIndex >= 0 && this.currentCoApplicantIndex < this.coApplicantsArray.length;
    const coApplicant = isEditing ? this.coApplicantsArray.at(this.currentCoApplicantIndex) : null;

    // Get stored detailed data for this co-applicant if it exists
    const storedDetailedData = isEditing ? this.coApplicantDetailedData[this.currentCoApplicantIndex] : null;

    // Create form arrays
    const loanDetailsArray = this.formBuilder.array([]);
    const investmentsArray = this.formBuilder.array([]);
    const itrYearsArray = this.formBuilder.array([]);

    // Create bank accounts array with the same structure as the main form
    const bankAccountsArray = this.formBuilder.array([
      this.createBankAccount('Current'),
      this.createBankAccount('Savings')
    ]);

    // Add default entries for new co-applicant
    if (!isEditing || !storedDetailedData) {
      this.addCoAppSelfEmployedLoanDetail(loanDetailsArray);
    }

    // Add default investment
    this.addCoAppSelfEmployedInvestment(investmentsArray);

    // Add default ITR years with default values
    this.financialYears.forEach((year, index) => {
      const itrYear = this.formBuilder.group({
        financialYear: [year, Validators.required],
        amount: [100000 * (index + 1), [Validators.required, Validators.min(0)]], // Turnover amount with default
        grossProfit: [50000 * (index + 1), Validators.min(0)],
        netAmount: [40000 * (index + 1), Validators.min(0)],
        date: [''],
        provisional: ['No', Validators.required] // Default to No
      });
      itrYearsArray.push(itrYear as any);
    });

    // Log the current product type for debugging
    console.log('In initCoApplicantSelfEmployedForm - Product Type:', this.selectedProductType);
    console.log('Is Home Loan in initCoApplicantSelfEmployedForm:', this.isHomeLoan);

    // Determine which form to create based on the current product type
    if (this.selectedProductType === 'LAP' || this.selectedProductType === 'LRD') {
      // Create LAP/LRD specific form for co-applicant with default values
      this.coApplicantSelfEmployedForm = this.formBuilder.group({
        // Personal Information
        age: [isEditing ? coApplicant?.get('age')?.value : 30, [Validators.required, Validators.min(18), Validators.max(100)]],
        addressType: [storedDetailedData?.addressType || 'Permanent', Validators.required],
        status: [storedDetailedData?.status || 'Married', Validators.required],

        // Business Information
        profession: [storedDetailedData?.profession || 'Business Owner', Validators.required],
        businessType: [storedDetailedData?.businessType || 'Proprietorship', Validators.required],
        natureOfBusiness: [storedDetailedData?.natureOfBusiness || 'Trading', Validators.required],
        businessName: [storedDetailedData?.businessName || 'Business Name', Validators.required],
        businessPlaceOwnership: [storedDetailedData?.businessPlaceOwnership || 'Owned', Validators.required],
        businessPlace: [storedDetailedData?.businessPlace || 'Office', Validators.required],
        currentBusinessExperience: [storedDetailedData?.currentBusinessExperience || 5, [Validators.required, Validators.min(0)]],
        totalPastBusinessExperience: [storedDetailedData?.totalPastBusinessExperience || 5, [Validators.required, Validators.min(0)]],
        businessAddress: [storedDetailedData?.businessAddress || 'Business Address', Validators.required],
        businessPincode: [storedDetailedData?.businessPincode || '400001', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
        businessCity: [storedDetailedData?.businessCity || 'Mumbai', Validators.required],
        businessState: [storedDetailedData?.businessState || 'Maharashtra', Validators.required],
        gstRegistered: ['No'],
        gstNumber: [''],
        businessOwnership: [storedDetailedData?.businessOwnership || 'Owned', Validators.required], // Owned/Rented/Leased
        businessPremisesRent: ['', Validators.min(0)],
        numberOfEmployees: ['', [Validators.min(0)]],
        businessLicenseNumber: [''],
        businessRegistrationDate: [''],

        // Financial Information
        monthlyTurnover: [storedDetailedData?.monthlyTurnover || 100000, [Validators.required, Validators.min(0)]],
        monthlyAvgExpense: [storedDetailedData?.monthlyAvgExpense || 50000, [Validators.required, Validators.min(0)]],
        monthlyNetProfit: [isEditing ? coApplicant?.get('monthlyIncome')?.value : storedDetailedData?.monthlyNetProfit || 50000, [Validators.required, Validators.min(0)]],
        annualTurnover: [storedDetailedData?.annualTurnover || 1200000, [Validators.required, Validators.min(0)]],
        annualProfit: [storedDetailedData?.annualProfit || 600000, [Validators.required, Validators.min(0)]],
        profitGrowthRate: ['', [Validators.min(0), Validators.max(100)]],

        // ITR Information
        itrStatus: [storedDetailedData?.itrStatus || 'Filed', Validators.required],
        itrYears: itrYearsArray,
        taxPaid: ['', Validators.min(0)],
        taxAssessmentYear: [''],

        // Other Income
        hasOtherIncome: [storedDetailedData?.hasOtherIncome || 'No', Validators.required],
        otherIncomeSource: [''],
        otherIncomeAmount: ['', Validators.min(0)],
        otherIncomeMode: [''],

        // Banking Information
        bankAccounts: bankAccountsArray,
        averageBankBalance: ['', Validators.min(0)],
        bankStatementPeriod: [''],

        // Business Statutory Documents
        businessStatutoryDocs: [''],
        businessPanNumber: [''],
        udyamRegistration: [''],
        shopActLicense: [''],

        // Property Information - LAP/LRD specific
        propertySelected: [storedDetailedData?.propertySelected || 'Yes', Validators.required],
        location: [storedDetailedData?.location || 'Mumbai', Validators.required],
        propertyType: [storedDetailedData?.propertyType || 'Residential', Validators.required],
        propertyDetails: [''],
        propertyArea: [storedDetailedData?.propertyArea || 1000, [Validators.required, Validators.min(0)]],
        configuration: [''],
        propertyStatus: [storedDetailedData?.propertyStatus || 'Ready to Move', Validators.required],
        ucPercentage: ['', [Validators.min(0), Validators.max(100)]],
        propertyAge: ['', Validators.min(0)],
        societyStatus: [''],
        ccOc: [''], // CC/OC
        purchaseDate: [''],
        currentMV: [5000000, [Validators.required, Validators.min(0)]],
        loanAmountRequired: [2500000, [Validators.required, Validators.min(0)]],
        ltvPercentage: [50, [Validators.required, Validators.min(0), Validators.max(100)]],

        // Additional LAP/LRD specific fields
        propertyAddress: [storedDetailedData?.propertyAddress || 'Property Address', Validators.required],
        propertyPincode: [storedDetailedData?.propertyPincode || '400001', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
        propertyCity: [storedDetailedData?.propertyCity || 'Mumbai', Validators.required],
        propertyState: [storedDetailedData?.propertyState || 'Maharashtra', Validators.required],
        rentalIncome: ['', Validators.min(0)],
        rentalAgreementTenure: ['', Validators.min(0)],
        propertyTaxPaid: ['', Validators.min(0)],
        propertyInsurance: ['', Validators.min(0)],
        propertyMaintenance: ['', Validators.min(0)],
        existingMortgage: ['No'],
        mortgageBank: [''],
        mortgageOutstandingAmount: ['', Validators.min(0)],
        mortgageEmi: ['', Validators.min(0)],
        propertyValuation: [storedDetailedData?.propertyValuation || 5000000, [Validators.required, Validators.min(0)]],
        valuationDate: [''],
        valuationAgency: [''],
        titleClearance: [storedDetailedData?.titleClearance || 'Clear', Validators.required],
        legalOpinion: [storedDetailedData?.legalOpinion || 'Positive', Validators.required],

        // Loan Purpose (specific to LAP/LRD)
        loanPurpose: [storedDetailedData?.loanPurpose || 'Business Expansion', Validators.required],
        businessExpansionDetails: [''],
        debtConsolidationDetails: [''],
        workingCapitalRequirement: ['', Validators.min(0)],

        // Arrays for dynamic data
        loanDetails: loanDetailsArray,
        investments: investmentsArray,
      });
    } else {
      // Create Home Loan specific form for co-applicant with default values
      this.coApplicantSelfEmployedForm = this.formBuilder.group({
        // Personal Information
        age: [isEditing ? coApplicant?.get('age')?.value : 30, [Validators.required, Validators.min(18), Validators.max(100)]],
        addressType: [storedDetailedData?.addressType || 'Permanent', Validators.required],
        status: [storedDetailedData?.status || 'Married', Validators.required],

        // Business Information
        profession: [storedDetailedData?.profession || 'Business Owner', Validators.required],
        businessType: [storedDetailedData?.businessType || 'Proprietorship', Validators.required],
        natureOfBusiness: [storedDetailedData?.natureOfBusiness || 'Trading', Validators.required],
        businessName: [storedDetailedData?.businessName || 'Business Name', Validators.required],
        businessPlaceOwnership: [storedDetailedData?.businessPlaceOwnership || 'Owned', Validators.required],
        businessPlace: [storedDetailedData?.businessPlace || 'Office', Validators.required],
        currentBusinessExperience: [storedDetailedData?.currentBusinessExperience || 5, [Validators.required, Validators.min(0)]],
        totalPastBusinessExperience: [storedDetailedData?.totalPastBusinessExperience || 5, [Validators.required, Validators.min(0)]],

        // Financial Information
        monthlyTurnover: [storedDetailedData?.monthlyTurnover || 100000, [Validators.required, Validators.min(0)]],
        monthlyAvgExpense: [storedDetailedData?.monthlyAvgExpense || 50000, [Validators.required, Validators.min(0)]],
        monthlyNetProfit: [isEditing ? coApplicant?.get('monthlyIncome')?.value : storedDetailedData?.monthlyNetProfit || 50000, [Validators.required, Validators.min(0)]],

        // ITR Information
        itrStatus: [storedDetailedData?.itrStatus || 'Filed', Validators.required],
        itrYears: itrYearsArray,

        // Financial Information Table
        monthlyInflow: [storedDetailedData?.monthlyInflow || 100000, [Validators.required, Validators.min(0)]],
        yearlyInflow: [storedDetailedData?.yearlyInflow || 1200000, [Validators.required, Validators.min(0)]],
        monthlyExpenses: [storedDetailedData?.monthlyExpenses || 50000, [Validators.required, Validators.min(0)]],
        yearlyExpenses: [storedDetailedData?.yearlyExpenses || 600000, [Validators.required, Validators.min(0)]],
        monthlyNetProfitTable: [storedDetailedData?.monthlyNetProfitTable || 50000, [Validators.required, Validators.min(0)]],
        yearlyNetProfit: [storedDetailedData?.yearlyNetProfit || 600000, [Validators.required, Validators.min(0)]],
        monthlyTurnoverTable: [storedDetailedData?.monthlyTurnoverTable || 100000, [Validators.required, Validators.min(0)]],
        yearlyTurnover: [storedDetailedData?.yearlyTurnover || 1200000, [Validators.required, Validators.min(0)]],

        // Other Income
        hasOtherIncome: [storedDetailedData?.hasOtherIncome || 'No', Validators.required],
        otherIncomeSource: [''],
        otherIncomeAmount: ['', Validators.min(0)],
        otherIncomeMode: [''],

        // Banking Information
        bankAccounts: bankAccountsArray,

        // Business Statutory Documents
        businessStatutoryDocs: [''],

        // Property Information - Home Loan specific
        propertySelected: ['Yes', Validators.required],
        location: [storedDetailedData?.location || 'Mumbai', Validators.required],

        // Arrays for dynamic data
        loanDetails: loanDetailsArray,
        investments: investmentsArray,
      });

      // Log the form structure for debugging
      console.log('Home Loan Self-Employed Form initialized with fields:', Object.keys(this.coApplicantSelfEmployedForm.controls));
      console.log('Home Loan specific fields present:',
        'ocr' in this.coApplicantSelfEmployedForm.controls,
        'ocrPercentage' in this.coApplicantSelfEmployedForm.controls,
        'bankLoanAmount' in this.coApplicantSelfEmployedForm.controls,
        'projectApf' in this.coApplicantSelfEmployedForm.controls
      );
    }

    // TODO: Populate form with stored data if editing
    // if (isEditing && storedDetailedData) {
    //   this.populateCoApplicantSelfEmployedFormWithStoredData(storedDetailedData);
    // }

    this.isCoAppSelfEmployedFormSubmitted = false;
  }



  // Setup value changes for co-applicant salaried form
  private setupCoAppSalariedFormValueChanges() {
    const deductionsGroup = this.coApplicantSalariedForm.get('deductions');
    if (deductionsGroup) {
      deductionsGroup.valueChanges.subscribe(() => {
        this.calculateCoAppSalariedTotals();
      });
    }

    this.coApplicantSalariedForm.get('totalGrossSalary')?.valueChanges.subscribe(() => {
      this.calculateCoAppSalariedTotals();
    });

    // For LAP/LRD form, setup LTV calculation
    if (this.selectedProductType === 'LAP' || this.selectedProductType === 'LRD') {
      const currentMarketValue = this.coApplicantSalariedForm.get('currentMarketValue');
      const loanAmountRequired = this.coApplicantSalariedForm.get('loanAmountRequired');

      if (currentMarketValue && loanAmountRequired) {
        currentMarketValue.valueChanges.subscribe(() => {
          this.calculateCoAppSalariedLtv();
        });

        loanAmountRequired.valueChanges.subscribe(() => {
          this.calculateCoAppSalariedLtv();
        });
      }
    }
  }

  // Calculate totals for co-applicant salaried form
  private calculateCoAppSalariedTotals() {
    const deductionsValues = this.coApplicantSalariedForm.get('deductions')?.value || {};
    const totalDeduction: number = Object.values(deductionsValues).reduce((sum: number, val: any) => sum + (+val || 0), 0) as number;
    const grossSalary: number = +(this.coApplicantSalariedForm.get('totalGrossSalary')?.value || 0);

    this.coApplicantSalariedForm.patchValue(
      {
        totalDeduction: totalDeduction,
        netSalary: grossSalary - totalDeduction,
      },
      { emitEvent: false }
    );
  }

  // Calculate LTV for co-applicant salaried form
  private calculateCoAppSalariedLtv() {
    if (this.selectedProductType !== 'LAP' && this.selectedProductType !== 'LRD') return;

    const marketValue = +(this.coApplicantSalariedForm.get('currentMarketValue')?.value || 0);
    const loanAmount = +(this.coApplicantSalariedForm.get('loanAmountRequired')?.value || 0);

    if (marketValue > 0 && loanAmount > 0) {
      const ltvPercentage = Math.round((loanAmount / marketValue) * 100);
      this.coApplicantSalariedForm.patchValue(
        {
          ltvPercentage: ltvPercentage
        },
        { emitEvent: false }
      );
    }
  }

  // Form submission
  onSubmit() {
    this.isFormSubmitted = true;

    if (this.selfEmployeeForm.invalid) {
      return;
    }

    // Process form data
    const formData = this.selfEmployeeForm.getRawValue();

    console.log('Self Employee Form submitted:', formData);

    // Here you would typically save the data to a service
  }



  // Edit Co-Applicant Salaried Details
  editCoApplicantSalariedDetails(index: number) {
    console.log('🔧 Editing co-applicant salaried details for index:', index);
    this.currentCoApplicantIndex = index;

    // Check if detailed data exists for this co-applicant
    const hasDetailedData = this.coApplicantDetailedData[index];
    console.log('🔍 Existing detailed data for co-applicant', index, ':', hasDetailedData);

    // Initialize the salaried form
    this.initCoApplicantSalariedForm();

    // If there's detailed data, populate the form immediately after initialization
    if (hasDetailedData) {
      console.log('📝 Populating salaried form with existing detailed data');
      setTimeout(() => {
        this.populateCoApplicantSalariedFormWithStoredData(hasDetailedData);
      }, 100); // Small delay to ensure form is fully initialized
    }

    this.modalService.open(this.salariedModal, {
      centered: true,
      size: 'xl',
      backdrop: 'static'
    });
  }

  // Edit Co-Applicant Self-Employed Details
  editCoApplicantSelfEmployedDetails(index: number) {
    console.log('🔧 Editing co-applicant self-employed details for index:', index);
    this.currentCoApplicantIndex = index;

    // Check if detailed data exists for this co-applicant
    const hasDetailedData = this.coApplicantDetailedData[index];
    console.log('🔍 Existing detailed data for co-applicant', index, ':', hasDetailedData);

    // Initialize the form
    this.initCoApplicantSelfEmployedForm();

    // If there's detailed data, populate the form immediately after initialization
    if (hasDetailedData) {
      console.log('📝 Populating form with existing detailed data');
      setTimeout(() => {
        this.populateCoApplicantSelfEmployedFormWithStoredData(hasDetailedData);
      }, 100); // Small delay to ensure form is fully initialized
    }

    this.modalService.open(this.selfEmployedModal, {
      centered: true,
      size: 'xl',
      backdrop: 'static'
    });
  }

  // Save Co-Applicant Self-Employed Form
  saveCoApplicantSelfEmployedForm(modal?: any) {
    console.log('🚀 Save Co-Applicant Self-Employed Form called');
    this.isCoAppSelfEmployedFormSubmitted = true;

    console.log('Submitting Self-Employed Form with product type:', this.selectedProductType);
    console.log('Is Home Loan:', this.isHomeLoan);
    console.log('Form valid:', this.coApplicantSelfEmployedForm.valid);
    console.log('Form errors:', this.getFormValidationErrors());

    // For co-applicant forms, allow saving even if form is invalid (as per user preference)
    if (this.coApplicantSelfEmployedForm.invalid && this.isCoApplicantForm) {
      console.log('⚠️ Co-applicant form is invalid but proceeding with save (as per user preference)');
      console.log('🔍 Invalid form controls:');
      Object.keys(this.coApplicantSelfEmployedForm.controls).forEach(key => {
        const control = this.coApplicantSelfEmployedForm.get(key);
        if (control && control.invalid) {
          console.log(`❌ Invalid control: ${key}`, control.errors, control.value);
        }
      });
    } else if (this.coApplicantSelfEmployedForm.invalid) {
      console.error('Co-Applicant Self-Employed form is invalid', this.coApplicantSelfEmployedForm);
      return;
    }

    // Process form data
    const formValues = this.coApplicantSelfEmployedForm.getRawValue();

    // Store the complete detailed data for this co-applicant
    const detailedData = {
      ...formValues,
      // Convert FormArray data to proper format for storage
      bankAccounts: (this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray)?.value || [],
      loanDetails: (this.coApplicantSelfEmployedForm.get('loanDetails') as FormArray)?.value || [],
      investments: (this.coApplicantSelfEmployedForm.get('investments') as FormArray)?.value || [],
      itrYears: (this.coApplicantSelfEmployedForm.get('itrYears') as FormArray)?.value || []
    };

    // Format the income source to include business name and profession
    const incomeSource = `${formValues.businessName} (${formValues.profession})`;

    // Create basic co-applicant info
    const basicInfo = {
      age: formValues.age,
      monthlyIncome: formValues.monthlyNetProfit,
      incomeSource: incomeSource,
      detailedData: detailedData
    };

    console.log('Co-Applicant Self-Employed form saved', formValues);
    console.log('Stored detailed data:', detailedData);

    // Reset form submission state
    this.isCoAppSelfEmployedFormSubmitted = false;

    // Console log after save operation
    console.log('✅ Co-Applicant Self-Employed form save operation completed successfully');

    // If this is being used as a co-applicant form, emit the saved data to parent
    if (this.isCoApplicantForm) {
      const savedCoApplicantData = {
        index: this.currentCoApplicantIndex,
        basicInfo: basicInfo,
        detailedData: detailedData
      };
      this.coApplicantSaved.emit(savedCoApplicantData);
      console.log('📤 Emitted co-applicant saved data to parent:', savedCoApplicantData);
    } else {
      // Update the co-applicant in the main form (when used in self-employee component directly)
      if (this.currentCoApplicantIndex >= 0 && this.currentCoApplicantIndex < this.coApplicantsArray.length) {
        const coApplicant = this.coApplicantsArray.at(this.currentCoApplicantIndex);

        // Update the basic co-applicant info in the main form
        coApplicant.patchValue(basicInfo);

        // Store in the detailed data structure
        this.coApplicantDetailedData[this.currentCoApplicantIndex] = detailedData;

        console.log('📊 Updated co-applicants array:', this.coApplicantsArray.value);
        console.log('💾 All stored detailed data:', this.coApplicantDetailedData);
      }
    }

    // Keep modal open after saving (as per user preference)
    console.log('✅ Data saved successfully - modal remains open for continued editing');
  }

  // Save and Close Co-Applicant Self-Employed Form
  saveAndCloseCoApplicantSelfEmployedForm(modal: any) {
    console.log('💾 Save and Close Co-Applicant Self-Employed Form called');

    // First save the data
    this.saveCoApplicantSelfEmployedForm();

    // Then close the modal after a brief delay to ensure save completes
    setTimeout(() => {
      modal.close('Save');
      console.log('🔒 Modal closed after save and close operation');
    }, 100);
  }

  // Save Co-Applicant Salaried Form
  saveCoApplicantSalariedForm(modal: any) {
    console.log('💼 Save Co-Applicant Salaried Form called');
    this.isCoAppSalariedFormSubmitted = true;

    if (this.coApplicantSalariedForm.invalid) {
      console.error('Co-Applicant Salaried form is invalid', this.coApplicantSalariedForm);
      return;
    }

    // Process form data
    const formValues = this.coApplicantSalariedForm.getRawValue();

    // Update the co-applicant in the main form
    if (this.currentCoApplicantIndex >= 0 && this.currentCoApplicantIndex < this.coApplicantsArray.length) {
      const coApplicant = this.coApplicantsArray.at(this.currentCoApplicantIndex);

      // Format the income source to include company name and job profile
      const incomeSourceDetails = `${formValues.companyName} (${formValues.jobProfile})`;

      // Update the basic co-applicant info in the external array
      coApplicant.patchValue({
        incomeSource: incomeSourceDetails,
        monthlyIncome: formValues.netSalary || formValues.totalGrossSalary
      });

      // Store the complete detailed data for this co-applicant
      const detailedData = {
        ...formValues,
        // Convert FormArray data to proper format for storage
        bankAccounts: (this.coApplicantSalariedForm.get('bankAccounts') as FormArray)?.value || [],
        loanDetails: (this.coApplicantSalariedForm.get('loanDetails') as FormArray)?.value || [],
        investments: (this.coApplicantSalariedForm.get('investments') as FormArray)?.value || []
      };

      // Store in the detailed data structure
      this.coApplicantDetailedData[this.currentCoApplicantIndex] = detailedData;

      // Also store the detailed data within the co-applicant object for consistency
      const currentCoApplicantValue = coApplicant.value;
      coApplicant.patchValue({
        ...currentCoApplicantValue,
        detailedData: detailedData
      });

      console.log('Co-Applicant Salaried form saved', formValues);
      console.log('Stored detailed data:', this.coApplicantDetailedData[this.currentCoApplicantIndex]);

      // Reset form submission state
      this.isCoAppSalariedFormSubmitted = false;
      modal.close('Save');
    }
  }

  // Helper method to get form validation errors
  private getFormValidationErrors() {
    const errors: any = {};

    Object.keys(this.coApplicantSelfEmployedForm.controls).forEach(key => {
      const control = this.coApplicantSelfEmployedForm.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }

      // Check FormArray controls
      if (control instanceof FormArray) {
        const arrayErrors: any = {};
        control.controls.forEach((arrayControl, index) => {
          if (arrayControl.errors) {
            arrayErrors[index] = arrayControl.errors;
          }

          // Check FormGroup controls within FormArray
          if (arrayControl instanceof FormGroup) {
            const groupErrors: any = {};
            Object.keys(arrayControl.controls).forEach(groupKey => {
              const groupControl = arrayControl.get(groupKey);
              if (groupControl && groupControl.errors) {
                groupErrors[groupKey] = groupControl.errors;
              }
            });
            if (Object.keys(groupErrors).length > 0) {
              arrayErrors[index] = { ...arrayErrors[index], ...groupErrors };
            }
          }
        });
        if (Object.keys(arrayErrors).length > 0) {
          errors[key] = arrayErrors;
        }
      }
    });

    return errors;
  }

  // Add a new loan detail for co-applicant salaried form
  addCoAppSalariedLoanDetail(loanDetailsArray: FormArray) {
    const loanDetail = this.formBuilder.group({
      loanType: ['', Validators.required],
      bankName: ['', Validators.required],
      sanctionedAmount: ['', [Validators.required, Validators.min(0)]],
      outstandingAmount: ['', [Validators.required, Validators.min(0)]],
      tenure: ['', [Validators.required, Validators.min(1)]],
      emiAmount: ['', [Validators.required, Validators.min(0)]],
      default: ['No', Validators.required],
      example: [''] // New field for example
    });

    loanDetailsArray.push(loanDetail);
  }

  // Add a new investment for co-applicant salaried form
  addCoAppSalariedInvestment(investmentsArray: FormArray) {
    const investment = this.formBuilder.group({
      instituteName: ['', Validators.required],
      investmentProduct: ['', Validators.required],
      yearlyAmount: ['', [Validators.required, Validators.min(0)]],
      investmentMode: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      currentSavingAmount: ['', [Validators.required, Validators.min(0)]]
    });

    investmentsArray.push(investment);
  }

  // Add a new loan detail for co-applicant self-employed form
  addCoAppSelfEmployedLoanDetail(loanDetailsArray: FormArray) {
    const loanDetail = this.formBuilder.group({
      loanType: ['Personal Loan', Validators.required],
      bankName: ['HDFC Bank', Validators.required],
      sanctionedAmount: [500000, [Validators.required, Validators.min(0)]],
      outstandingAmount: [250000, [Validators.required, Validators.min(0)]],
      tenure: [24, [Validators.required, Validators.min(1)]],
      emiAmount: [25000, [Validators.required, Validators.min(0)]],
      default: ['No', Validators.required],
      example: [''] // New field for example
    });

    loanDetailsArray.push(loanDetail);
  }

  // Add a new investment for co-applicant self-employed form
  addCoAppSelfEmployedInvestment(investmentsArray: FormArray) {
    const investment = this.formBuilder.group({
      instituteName: ['SBI Mutual Fund', Validators.required],
      investmentProduct: ['SIP', Validators.required],
      yearlyAmount: [120000, [Validators.required, Validators.min(0)]],
      investmentMode: ['Monthly', Validators.required],
      startDate: ['2023-01-01', Validators.required],
      endDate: ['2025-12-31', Validators.required],
      currentSavingAmount: [50000, [Validators.required, Validators.min(0)]]
    });

    investmentsArray.push(investment);
  }

  // Calculate OCR Required and OCR Percentage
  calculateOCR() {
    const agreementValue = this.selfEmployeeForm.get('agreementValue')?.value || 0;
    const loanAmtRequired = this.selfEmployeeForm.get('loanAmtRequired')?.value || 0;

    // OCR Required = Agreement Value - Loan Amount Required
    const ocrRequired = agreementValue - loanAmtRequired;

    // OCR % = (OCR Required / Agreement Value) * 100
    const ocrPercentage = agreementValue > 0 ? (ocrRequired / agreementValue) * 100 : 0;

    // Update the form controls
    this.selfEmployeeForm.patchValue({
      ocrRequired: ocrRequired,
      ocrPercentage: parseFloat(ocrPercentage.toFixed(2))
    });
  }

  // Reset form
  onCancel() {
    this.selfEmployeeForm.reset();
    this.isFormSubmitted = false;

    // Clear arrays
    while (this.itrYearsArray.length > 0) {
      this.itrYearsArray.removeAt(0);
    }

    while (this.bankAccountsArray.length > 0) {
      this.bankAccountsArray.removeAt(0);
    }

    while (this.loanDetailsArray.length > 0) {
      this.loanDetailsArray.removeAt(0);
    }

    while (this.investmentsArray.length > 0) {
      this.investmentsArray.removeAt(0);
    }

    while (this.coApplicantsArray.length > 0) {
      this.coApplicantsArray.removeAt(0);
    }

    this.initForm();
  }

  // Check if any co-applicant salaried loan has default = "Yes" to show/hide Example column
  hasAnyCoAppSalariedLoanWithDefault(): boolean {
    if (!this.coApplicantSalariedForm) return false;
    const loanDetailsArray = this.coApplicantSalariedForm.get('loanDetails') as FormArray;
    if (!loanDetailsArray) return false;
    return loanDetailsArray.controls.some(control =>
      control.get('default')?.value === 'Yes'
    );
  }

  // Check if any co-applicant self-employed loan has default = "Yes" to show/hide Example column
  hasAnyCoAppSelfEmployedLoanWithDefault(): boolean {
    if (!this.coApplicantSelfEmployedForm) return false;
    const loanDetailsArray = this.coApplicantSelfEmployedForm.get('loanDetails') as FormArray;
    if (!loanDetailsArray) return false;
    return loanDetailsArray.controls.some(control =>
      control.get('default')?.value === 'Yes'
    );
  }

  // Check if any ITR year has provisional = "Yes" to show/hide additional columns
  hasAnyITRWithProvisional(): boolean {
    return this.itrYearsArray.controls.some(control =>
      control.get('provisional')?.value === 'Yes'
    );
  }

  // Check if any co-applicant ITR year has provisional = "Yes" to show/hide additional columns
  hasAnyCoAppITRWithProvisional(): boolean {
    if (!this.coApplicantSelfEmployedForm) return false;
    const itrYearsArray = this.coApplicantSelfEmployedForm.get('itrYears') as FormArray;
    if (!itrYearsArray) return false;
    return itrYearsArray.controls.some(control =>
      control.get('provisional')?.value === 'Yes'
    );
  }

  // Method to get all form data for API submission
  getFormData(): any {
    console.log('📋 SelfEmployeeComponent.getFormData() called');
    const formData: any = {};

    // Main applicant self-employed form data
    if (this.selfEmployeeForm) {
      console.log('✅ Main self-employed form exists, capturing data (valid:', this.selfEmployeeForm.valid, ')');
      formData.mainApplicant = {
        ...this.selfEmployeeForm.value,
        // Convert FormArray data to proper format
        bankAccounts: this.bankAccountsArray?.value || [],
        loanDetails: this.loanDetailsArray?.value || [],
        investments: this.investmentsArray?.value || [],
        itrYears: this.itrYearsArray?.value || []
      };

      if (!this.selfEmployeeForm.valid) {
        console.warn('⚠️ Main self-employed form is invalid but including data anyway');
        console.warn('⚠️ Form errors:', this.selfEmployeeForm.errors);
      }
    } else {
      console.warn('⚠️ Main self-employed form is not available');
    }

    // Co-applicant salaried data if exists
    if (this.coApplicantSalariedForm && this.coApplicantSalariedForm.valid) {
      console.log('✅ Co-applicant salaried form is valid, capturing data');
      formData.coApplicantSalaried = {
        ...this.coApplicantSalariedForm.value,
        // Convert FormArray data to proper format
        bankAccounts: (this.coApplicantSalariedForm.get('bankAccounts') as FormArray)?.value || [],
        loanDetails: (this.coApplicantSalariedForm.get('loanDetails') as FormArray)?.value || [],
        investments: (this.coApplicantSalariedForm.get('investments') as FormArray)?.value || [],
        itrYears: (this.coApplicantSalariedForm.get('itrYears') as FormArray)?.value || []
      };
    }

    // Co-applicants basic data
    if (this.coApplicantsArray && this.coApplicantsArray.length > 0) {
      console.log('✅ Co-applicants exist, capturing basic data');
      formData.coApplicants = this.coApplicantsArray.value;

      // Also store in the nested structure for backward compatibility
      if (!formData.salaried_employee_details) {
        formData.salaried_employee_details = {};
      }
      formData.salaried_employee_details.coApplicants = this.coApplicantsArray.value;
    }

    // Co-applicants detailed data (nested forms)
    if (Object.keys(this.coApplicantDetailedData).length > 0) {
      console.log('✅ Co-applicant detailed data exists, capturing nested forms');
      formData.coApplicantsDetailed = this.coApplicantDetailedData;
    }

    // Current co-applicant self-employed form data if exists (for immediate submission)
    if (this.coApplicantSelfEmployedForm && this.coApplicantSelfEmployedForm.valid) {
      console.log('✅ Co-applicant self-employed form is valid, capturing current data');
      formData.currentCoApplicantSelfEmployed = {
        ...this.coApplicantSelfEmployedForm.value,
        // Convert FormArray data to proper format
        bankAccounts: (this.coApplicantSelfEmployedForm.get('bankAccounts') as FormArray)?.value || [],
        loanDetails: (this.coApplicantSelfEmployedForm.get('loanDetails') as FormArray)?.value || [],
        investments: (this.coApplicantSelfEmployedForm.get('investments') as FormArray)?.value || [],
        itrYears: (this.coApplicantSelfEmployedForm.get('itrYears') as FormArray)?.value || []
      };
    }

    // Property information if available
    if (this.selfEmployeeForm.get('propertyInformation')) {
      formData.propertyInformation = this.selfEmployeeForm.get('propertyInformation')?.value;
    }

    console.log('📊 SelfEmployeeComponent form data:', formData);
    return formData;
  }

  /**
   * Populate form with existing data for editing
   * This method is called by the parent component when entering edit mode
   */
  populateFormData(formData: any): void {
    if (!formData) {
      console.warn('⚠️ No form data provided for SelfEmployeeComponent');
      return;
    }

    console.log('🔧 Populating SelfEmployeeComponent with data:', formData);
    console.log('🔍 Checking for coApplicantsDetailed:', formData.coApplicantsDetailed);
    console.log('🔍 Checking for coApplicantDetailedData:', formData.coApplicantDetailedData);

    try {
      // Populate main applicant form if available
      if (formData.mainApplicant && this.selfEmployeeForm) {
        this.selfEmployeeForm.patchValue(formData.mainApplicant);

        // Populate form arrays
        if (formData.mainApplicant.bankAccounts) {
          this.populateFormArray(this.bankAccountsArray, formData.mainApplicant.bankAccounts);
        }
        if (formData.mainApplicant.loanDetails) {
          this.populateFormArray(this.loanDetailsArray, formData.mainApplicant.loanDetails);
        }
        if (formData.mainApplicant.investments) {
          this.populateFormArray(this.investmentsArray, formData.mainApplicant.investments);
        }
        if (formData.mainApplicant.itrYears) {
          this.populateFormArray(this.itrYearsArray, formData.mainApplicant.itrYears);
        }
      }

      // Populate co-applicants data
      if (formData.coApplicants && Array.isArray(formData.coApplicants)) {
        // Clear existing co-applicants
        this.coApplicantsArray.clear();

        // Add co-applicants
        formData.coApplicants.forEach((coApplicant: any, index: number) => {
          this.addCoApplicant();

          // Store detailed data if available from coApplicantDetailedData
          if (formData.coApplicantDetailedData && formData.coApplicantDetailedData[index]) {
            this.coApplicantDetailedData[index] = formData.coApplicantDetailedData[index];
          }
          // Check for coApplicantsDetailed structure (from API response)
          else if (formData.coApplicantsDetailed && formData.coApplicantsDetailed[index]) {
            this.coApplicantDetailedData[index] = formData.coApplicantsDetailed[index];
          }
          // Also check if detailed data is stored within the co-applicant object itself
          else if (coApplicant.detailedData) {
            this.coApplicantDetailedData[index] = coApplicant.detailedData;
          }
        });

        // Patch co-applicants array
        this.coApplicantsArray.patchValue(formData.coApplicants);
      }

      // Also populate from nested structure if available (for backward compatibility)
      if (formData.salaried_employee_details?.coApplicants && Array.isArray(formData.salaried_employee_details.coApplicants)) {
        // Clear existing co-applicants if not already populated
        if (!formData.coApplicants || formData.coApplicants.length === 0) {
          this.coApplicantsArray.clear();
        }

        // Add co-applicants from nested structure
        formData.salaried_employee_details.coApplicants.forEach((coApplicant: any, index: number) => {
          // Only add if not already added from external array
          if (index >= this.coApplicantsArray.length) {
            this.addCoApplicant();
          }

          // Map the nested structure to the expected format
          const mappedCoApplicant = {
            name: coApplicant.name || '',
            relation: coApplicant.relation || '',
            age: coApplicant.age || null,
            occupation: coApplicant.occupation || '',
            incomeSource: coApplicant.incomeSource || '',
            monthlyIncome: coApplicant.monthlyIncome || null
          };

          // Update the co-applicant at this index
          if (this.coApplicantsArray.at(index)) {
            this.coApplicantsArray.at(index).patchValue(mappedCoApplicant);
          }

          // Store any additional detailed data if available
          if (coApplicant.detailedData) {
            this.coApplicantDetailedData[index] = coApplicant.detailedData;
          }
        });
      }

      console.log('✅ SelfEmployeeComponent form populated successfully');
    } catch (error) {
      console.error('❌ Error populating SelfEmployeeComponent form:', error);
    }
  }

  /**
   * Helper method to populate FormArray with data
   */
  private populateFormArray(formArray: FormArray, data: any[]): void {
    if (!formArray || !data || !Array.isArray(data)) return;

    // Clear existing items
    formArray.clear();

    // Add items based on data
    data.forEach((item: any) => {
      if (formArray === this.bankAccountsArray) {
        this.addBankAccount(item.accountType || 'Current');
      } else if (formArray === this.loanDetailsArray) {
        this.addLoanDetail();
      } else if (formArray === this.investmentsArray) {
        this.addInvestment();
      } else if (formArray === this.itrYearsArray) {
        this.addITRYear(item.year || '2023-24');
      }
    });

    // Patch values
    formArray.patchValue(data);
  }
}
