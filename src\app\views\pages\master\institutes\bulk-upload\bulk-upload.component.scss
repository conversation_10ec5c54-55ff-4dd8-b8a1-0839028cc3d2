// Bulk Upload Component Styles (Institutes)

.modal-header {
  background: linear-gradient(135deg, var(--bs-info) 0%, rgba(var(--bs-info-rgb), 0.8) 100%);
  color: white;
  border-bottom: none;
  
  .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    
    i {
      opacity: 0.9;
    }
  }
  
  .btn-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    opacity: 1;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  background: var(--bs-gray-50);
  border-top: 1px solid var(--bs-gray-200);
  padding: 1.5rem 2rem;
}

// Alert styling
.alert {
  border-radius: 8px;
  border: none;
  
  &.alert-info {
    background: rgba(var(--bs-info-rgb), 0.1);
    color: var(--bs-info-text);
    border-left: 4px solid var(--bs-info);
    
    .alert-heading {
      color: var(--bs-info-text);
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }
    
    ul {
      font-size: 0.875rem;
      padding-left: 1.2rem;
      
      li {
        margin-bottom: 0.25rem;
      }
    }
  }
  
  &.alert-danger {
    background: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
    border-left: 4px solid var(--bs-danger);
  }
}

// File upload section
.upload-section {
  h6 {
    color: var(--bs-gray-700);
    font-weight: 600;
    
    i {
      color: var(--bs-primary);
    }
  }
}

.file-input-wrapper {
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.file-input-label {
  display: block;
  cursor: pointer;
  margin: 0;
}

.upload-area {
  border: 2px dashed var(--bs-gray-300);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: var(--bs-gray-50);
  
  &:hover {
    border-color: var(--bs-primary);
    background: rgba(var(--bs-primary-rgb), 0.05);
  }
  
  &.has-file {
    border-color: var(--bs-success);
    background: rgba(var(--bs-success-rgb), 0.05);
    border-style: solid;
  }
}

.upload-placeholder {
  .upload-icon {
    font-size: 3rem;
    color: var(--bs-gray-400);
    margin-bottom: 1rem;
  }
  
  h6 {
    color: var(--bs-gray-600);
    margin-bottom: 0.5rem;
  }
  
  p {
    color: var(--bs-gray-500);
    font-size: 0.875rem;
    margin: 0;
  }
}

.file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: left;
  
  .feather {
    font-size: 2rem;
    color: var(--bs-success);
    flex-shrink: 0;
  }
  
  .file-details {
    flex-grow: 1;
    
    .file-name {
      margin: 0;
      color: var(--bs-gray-700);
      font-weight: 600;
      word-break: break-word;
    }
    
    .file-size {
      margin: 0;
      font-size: 0.875rem;
    }
  }
  
  .btn {
    flex-shrink: 0;
  }
}

// Upload results card
.card {
  border-radius: 12px;
  
  &.border-success {
    border-color: var(--bs-success) !important;
  }
  
  &.border-warning {
    border-color: var(--bs-warning) !important;
  }
  
  .card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    
    &.bg-success {
      background: var(--bs-success) !important;
    }
    
    &.bg-warning {
      background: var(--bs-warning) !important;
    }
    
    h6 {
      font-weight: 600;
      
      i {
        opacity: 0.9;
      }
    }
  }
  
  .card-body {
    h4 {
      font-weight: 700;
      margin-bottom: 0.25rem;
      
      &.text-primary {
        color: var(--bs-primary) !important;
      }
      
      &.text-success {
        color: var(--bs-success) !important;
      }
      
      &.text-danger {
        color: var(--bs-danger) !important;
      }
    }
    
    small {
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Error list styling
.error-list {
  max-height: 200px;
  overflow-y: auto;
  background: rgba(var(--bs-danger-rgb), 0.05);
  border-radius: 6px;
  padding: 1rem;
  
  .error-item {
    margin-bottom: 0.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    small {
      line-height: 1.4;
    }
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  
  &.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
    border: none;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.3);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  &.btn-secondary {
    background: var(--bs-gray-200);
    border: 1px solid var(--bs-gray-300);
    color: var(--bs-gray-700);
    
    &:hover:not(:disabled) {
      background: var(--bs-gray-300);
      border-color: var(--bs-gray-400);
      color: var(--bs-gray-800);
    }
  }
  
  &.btn-outline-primary {
    border-color: var(--bs-primary);
    color: var(--bs-primary);
    
    &:hover:not(:disabled) {
      background: var(--bs-primary);
      color: white;
    }
  }
  
  &.btn-outline-secondary {
    border-color: var(--bs-gray-400);
    color: var(--bs-gray-600);
    
    &:hover:not(:disabled) {
      background: var(--bs-gray-400);
      color: white;
    }
  }
  
  &.btn-outline-danger {
    border-color: var(--bs-danger);
    color: var(--bs-danger);
    
    &:hover:not(:disabled) {
      background: var(--bs-danger);
      color: white;
    }
  }
  
  i {
    font-size: 0.875rem;
  }
  
  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
    
    .d-flex {
      width: 100%;
      
      &.gap-2 {
        gap: 0.5rem;
      }
    }
    
    .btn {
      flex: 1;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
    }
  }
  
  .upload-area {
    padding: 1.5rem 1rem;
  }
  
  .upload-placeholder {
    .upload-icon {
      font-size: 2rem;
    }
    
    h6 {
      font-size: 0.9rem;
    }
  }
  
  .file-info {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
    
    .file-details {
      text-align: center;
    }
  }
  
  .card-body .row .col-md-4 {
    margin-bottom: 1rem;
  }
}

// Dark mode support
[data-theme="dark"] {
  .modal-header {
    background: linear-gradient(135deg, var(--bs-info) 0%, rgba(var(--bs-info-rgb), 0.9) 100%);
  }
  
  .modal-footer {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
  }
  
  .upload-area {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-600);
    
    &:hover {
      background: rgba(var(--bs-primary-rgb), 0.1);
    }
    
    &.has-file {
      background: rgba(var(--bs-success-rgb), 0.1);
    }
  }
  
  .upload-placeholder {
    .upload-icon {
      color: var(--bs-gray-500);
    }
    
    h6 {
      color: var(--bs-gray-400);
    }
    
    p {
      color: var(--bs-gray-500);
    }
  }
  
  .file-info {
    .file-details .file-name {
      color: var(--bs-gray-300);
    }
  }
  
  .card {
    background: var(--bs-dark);
    border-color: var(--bs-gray-700);
    
    .card-body {
      background: var(--bs-dark);
      color: var(--bs-gray-300);
    }
  }
  
  .error-list {
    background: rgba(var(--bs-danger-rgb), 0.1);
  }
  
  .btn-secondary {
    background: var(--bs-gray-700);
    border-color: var(--bs-gray-600);
    color: var(--bs-gray-300);
    
    &:hover:not(:disabled) {
      background: var(--bs-gray-600);
      border-color: var(--bs-gray-500);
      color: var(--bs-gray-200);
    }
  }
}

// Custom scrollbar for error list
.error-list {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 3px;
    
    &:hover {
      background: var(--bs-gray-500);
    }
  }
}
