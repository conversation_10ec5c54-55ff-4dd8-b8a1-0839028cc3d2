import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-group-entity-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './group-entity-modal.component.html',
  styleUrl: './group-entity-modal.component.scss'
})
export class GroupEntityModalComponent implements OnInit {
  @Input() rowId?: number;

  // Form data
  formData = {
    id: 0,
    companyName: '',
    constitution: '',
    dateOfIncorporation: '',
    pan: '',
    cinGstNo: '',
    registeredOfficeAddress: '',
    partnersDirectors: '',
    profitSharingRatio: '',
    din: '',
    currentProjects: ''
  };

  constructor(public activeModal: NgbActiveModal) {}

  // Mock data for group entities (in a real app, this would come from a service)
  private mockGroupEntities = [
    {
      id: 1,
      companyName: 'ABC Developers Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '2010-04-15', // Format for input field
      pan: '**********',
      cinGstNo: 'U45200MH2010PTC123456',
      registeredOfficeAddress: '501, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Amit Shah, Priya Patel',
      profitSharingRatio: '40:30:30',
      din: 'DIN12345678, DIN87654321, DIN23456789',
      currentProjects: 'Sunrise Heights, Green Valley'
    },
    {
      id: 2,
      companyName: 'ABC Infrastructure Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '2012-06-20',
      pan: '**********',
      cinGstNo: 'U45200MH2012PTC234567',
      registeredOfficeAddress: '502, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Amit Shah',
      profitSharingRatio: '60:40',
      din: 'DIN12345678, DIN87654321',
      currentProjects: 'Highway Project, Metro Station Development'
    },
    {
      id: 3,
      companyName: 'ABC Constructions LLP',
      constitution: 'LLP',
      dateOfIncorporation: '2015-09-10',
      pan: '**********',
      cinGstNo: 'AAF-1234',
      registeredOfficeAddress: '503, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Priya Patel, Sanjay Gupta',
      profitSharingRatio: '40:30:30',
      din: 'N/A',
      currentProjects: 'Commercial Complex, IT Park'
    },
    {
      id: 4,
      companyName: 'ABC Hospitality Services Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '2019-03-05',
      pan: '**********',
      cinGstNo: 'U55100MH2019PTC654321',
      registeredOfficeAddress: '504, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Sanjay Gupta',
      profitSharingRatio: '50:50',
      din: 'DIN12345678, DIN56789012',
      currentProjects: 'Luxury Hotel & Resort, Business Convention Center'
    }
  ];

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      companyName: '',
      constitution: '',
      dateOfIncorporation: '',
      pan: '',
      cinGstNo: '',
      registeredOfficeAddress: '',
      partnersDirectors: '',
      profitSharingRatio: '',
      din: '',
      currentProjects: ''
    };

    // If editing an existing record, populate the form
    if (this.rowId) {
      // Find the entity with the matching ID
      const entity = this.mockGroupEntities.find(e => e.id === this.rowId);

      if (entity) {
        // Convert date format from display format (DD-MMM-YYYY) to input field format (YYYY-MM-DD)
        // In a real app, you would get the data directly from a service in the correct format
        this.formData = {
          ...entity,
          // If the date is in DD-MMM-YYYY format, we'd need to convert it, but our mock data is already in YYYY-MM-DD format
          // dateOfIncorporation: this.convertDisplayDateToInputDate(entity.dateOfIncorporation)
        };
      }
    }
  }

  // Helper function to convert date from display format (DD-MMM-YYYY) to input field format (YYYY-MM-DD)
  private convertDisplayDateToInputDate(displayDate: string): string {
    if (!displayDate) return '';

    // This is a simplified example - in a real app, you'd use a more robust date parsing library
    const months = {
      'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
      'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    };

    // Parse a date like "15-Apr-2010"
    const parts = displayDate.split('-');
    if (parts.length !== 3) return '';

    const day = parts[0].padStart(2, '0');
    const month = months[parts[1] as keyof typeof months] || '';
    const year = parts[2];

    return `${year}-${month}-${day}`;
  }

  // Helper function to format date for display
  formatDateForDisplay(dateString: string): string {
    return this.convertInputDateToDisplayDate(dateString);
  }

  // Save changes and close the modal
  saveChanges() {
    // Format dates for display in the table
    const formattedData = {
      ...this.formData,
      dateOfIncorporation: this.formatDateForDisplay(this.formData.dateOfIncorporation)
    };

    // Close the modal and pass the data back
    this.activeModal.close(formattedData);
  }

  // Helper function to convert date from input format (YYYY-MM-DD) to display format (DD-MMM-YYYY)
  private convertInputDateToDisplayDate(inputDate: string): string {
    if (!inputDate) return '';

    const date = new Date(inputDate);
    const day = date.getDate().toString().padStart(2, '0');
    const month = new Intl.DateTimeFormat('en', { month: 'short' }).format(date);
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
