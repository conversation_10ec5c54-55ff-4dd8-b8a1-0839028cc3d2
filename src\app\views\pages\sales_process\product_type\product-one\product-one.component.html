
<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">{{ selectedProductSubType }}</h4>
            <p class="text-secondary">Manage {{ selectedProductSubType }} details</p>
          </div>

        </div>

        <form [formGroup]="product1Form" (ngSubmit)="onSubmit()">
          <div class="row">
            <!-- Product Category Selection -->
            <!-- <div class="col-12 mb-4">
              <div class="btn-group" role="group" aria-label="Product Category">
                @for (category of productCategories; track category) {
                  <input type="radio" class="btn-check" formControlName="category" [value]="category" [id]="'category-' + category">
                  <label class="btn btn-outline-primary" [for]="'category-' + category">{{ category }}</label>
                }
              </div>
              @if (isFormSubmitted && form['category'].errors?.required) {
                <div class="text-danger mt-1">Category is required</div>
              }
            </div> -->

            <!-- First Row -->
            <!-- Product Type field added at the top -->
            <div class="col-md-3 mb-3">
              <label for="productType" class="form-label">Product Type</label>
              <input type="text" class="form-control" id="productType" formControlName="productType"
                [ngClass]="{'is-invalid': isFormSubmitted && form['productType'].errors}">
              @if (isFormSubmitted && form['productType'].errors?.required) {
                <div class="invalid-feedback">Product type is required</div>
              }
            </div>


            <div class="col-md-3 mb-3">
              <label for="code" class="form-label">Code</label>
              <input type="text" class="form-control" id="code" formControlName="code" readonly>
              <small class="text-muted">Auto-generated from lead assign</small>
            </div>

            <div class="col-md-3 mb-3">
              <label for="dt" class="form-label">Date</label>
              <input type="text" class="form-control" id="dt" formControlName="dt" readonly>
              <small class="text-muted">Auto-generated</small>
            </div>

            <div class="col-md-3 mb-3">
              <label for="leadSource" class="form-label">Lead Source</label>
              <input type="text" class="form-control" id="leadSource" formControlName="leadSource" readonly>
              <small class="text-muted">Auto-generated from lead assign</small>
            </div>


            <!-- Second Row -->
            <div class="col-md-4 col-lg-3 mb-3">
              <label for="nameCompany" class="form-label">Name Company</label>
              <input type="text" class="form-control" id="nameCompany" formControlName="nameCompany"
                [ngClass]="{'is-invalid': isFormSubmitted && form['nameCompany'].errors}">
              @if (isFormSubmitted && form['nameCompany'].errors?.required) {
                <div class="invalid-feedback">Name Company is required</div>
              }
            </div>

            <div class="col-md-4 col-lg-3 mb-3">
              <label for="projectName" class="form-label">Name of Project</label>
              <input type="text" class="form-control" id="projectName" formControlName="projectName"
                [ngClass]="{'is-invalid': isFormSubmitted && form['projectName'].errors}">
              @if (isFormSubmitted && form['projectName'].errors?.required) {
                <div class="invalid-feedback">Project name is required</div>
              }
            </div>

            <div class="col-md-4  col-lg-3 mb-3">
              <label for="constitution" class="form-label">Constitution</label>
              <select class="form-select" id="constitution" formControlName="constitution"
                [ngClass]="{'is-invalid': isFormSubmitted && form['constitution'].errors}">
                <option value="" selected>Select Constitution</option>
                @if (constitutionsLoading) {
                  <option disabled>Loading constitutions...</option>
                }
                @for (constitution of constitutions; track constitution.id) {
                  <option [value]="constitution.id">{{ constitution.name }}</option>
                }
              </select>
              @if (isFormSubmitted && form['constitution'].errors?.required) {
                <div class="invalid-feedback">Constitution is required</div>
              }
            </div>

            <div class="col-md-4 col-lg-3 mb-3">
              <label for="location" class="form-label">Location</label>
              <select class="form-select" id="location" formControlName="location"
                [ngClass]="{'is-invalid': isFormSubmitted && form['location'].errors}">
                <option value="" selected>Select Location</option>
                @if (locationsLoading) {
                  <option disabled>Loading locations...</option>
                }
                @for (location of locations; track location.id) {
                  <option [value]="location.id">{{ location.name }}</option>
                }
              </select>
              @if (isFormSubmitted && form['location'].errors?.required) {
                <div class="invalid-feedback">Location is required</div>
              }
            </div>

            <!-- Third Row -->
            <div class="col-md-4 col-lg-3 mb-3">
              <label for="subLocations" class="form-label">Sub Locations</label>
              <input type="text" class="form-control" id="subLocations" formControlName="subLocations">
            </div>

            <div class="col-md-4 col-lg-3 mb-3">
              <label for="contactPersonName" class="form-label">Contact Person Name</label>
              <input type="text" class="form-control" id="contactPersonName" formControlName="contactPersonName"
                [ngClass]="{'is-invalid': isFormSubmitted && form['contactPersonName'].errors}">
              @if (isFormSubmitted && form['contactPersonName'].errors?.required) {
                <div class="invalid-feedback">Contact Person Name is required</div>
              }
            </div>

            <!-- People Information Section with Reactive Forms -->
            <div class="col-12 mb-4">
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">People Information</h6>
                  <button type="button" class="btn btn-sm" style="background-color: #df5316; color: white;" (click)="addPerson()">
                    <i class="fas fa-plus me-1"></i>Add Person
                  </button>
                </div>
                <div class="card-body" [formGroup]="peopleForm">
                  <div formArrayName="people">
                    @for (personControl of peopleControls; track $index; let i = $index) {
                      <div [formGroupName]="i" class="row mb-3 person-entry">
                        <div class="col-12 col-md-3 mb-2">
                          <label [for]="'connectWith_' + i" class="form-label">Connect With</label>
                          <select
                            class="form-select"
                            [id]="'connectWith_' + i"
                            formControlName="connectWith"
                            autocomplete="off"
                            [ngClass]="{'is-invalid': isFormSubmitted && personControl.get('connectWith')?.invalid}">
                            <option value="" selected>Select Option</option>
                            <option value="Sales Manager">Sales Manager</option>
                            <option value="Accountant">Accountant</option>
                            <option value="Sales head">Sales head</option>
                            <option value="Back Office">Back Office</option>
                            <option value="Admin">Admin</option>
                            <option value="CFO">CFO</option>
                            <option value="Staff">Staff</option>
                            <option value="Developer">Developer</option>
                            <option value="Doctor">Doctor</option>
                            <option value="Nurse">Nurse</option>
                            <option value="Teacher">Teacher</option>
                            <option value="PRICIPLE">PRICIPLE</option>
                            <option value="Dean">Dean</option>
                            <option value="Trustee">Trustee</option>
                          </select>
                          @if (isFormSubmitted && personControl.get('connectWith')?.errors?.required) {
                            <div class="invalid-feedback">Connect With is required</div>
                          }
                        </div>
                        <div class="col-12 col-md-3 mb-2">
                          <label [for]="'name_' + i" class="form-label">Name</label>
                          <input
                            type="text"
                            class="form-control"
                            [id]="'name_' + i"
                            formControlName="name"
                            autocomplete="off"
                            placeholder="Enter Name"
                            [ngClass]="{'is-invalid': isFormSubmitted && personControl.get('name')?.invalid}">
                          @if (isFormSubmitted && personControl.get('name')?.errors?.required) {
                            <div class="invalid-feedback">Name is required</div>
                          }
                        </div>
                        <div class="col-12 col-md-2 mb-2">
                          <label [for]="'mobile_' + i" class="form-label">Mobile</label>
                          <input
                            type="tel"
                            class="form-control"
                            [id]="'mobile_' + i"
                            formControlName="mobile"
                            autocomplete="off"
                            placeholder="Enter Mobile"
                            [ngClass]="{'is-invalid': isFormSubmitted && personControl.get('mobile')?.invalid}">
                          @if (isFormSubmitted && personControl.get('mobile')?.errors?.required) {
                            <div class="invalid-feedback">Mobile is required</div>
                          }
                          @if (isFormSubmitted && personControl.get('mobile')?.errors?.pattern) {
                            <div class="invalid-feedback">Mobile must be 10 digits</div>
                          }
                        </div>
                        <div class="col-12 col-md-3 mb-2">
                          <label [for]="'email_' + i" class="form-label">Email</label>
                          <input
                            type="email"
                            class="form-control"
                            [id]="'email_' + i"
                            formControlName="email"
                            autocomplete="off"
                            placeholder="Enter Email"
                            [ngClass]="{'is-invalid': isFormSubmitted && personControl.get('email')?.invalid}">
                          @if (isFormSubmitted && personControl.get('email')?.errors?.email) {
                            <div class="invalid-feedback">Please enter a valid email address</div>
                          }
                        </div>
                        @if (peopleControls.length > 1) {
                          <div class="col-12 col-md-1 mb-2 d-flex align-items-end">
                            <a href="javascript:;" class="action-icon" ngbTooltip="Delete" (click)="removePerson(personControl.get('id')?.value)">
                              <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                            </a>
                          </div>
                        }
                      </div>
                    }
                  </div>
                </div>
              </div>
            </div>

            <!-- RERA Row -->

            <div class="col-md-4 col-lg-3 mb-3">
              <label for="rera" class="form-label">RERA</label>
              <select class="form-select" id="rera" formControlName="rera" (change)="onReraChange()">
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
            </div>

          </div>

<!-- RERA Component (shown only when RERA dropdown is set to "Yes") -->
@if (showReraForm) {
  <div class="row mt-4">
    <div class="col-md-12">
      <app-rera #reraComponent (formDataChange)="onReraFormDataChange($event)"></app-rera>
    </div>
  </div>
}

          <!-- Submit and Cancel Buttons -->
          <div class="row mt-4">
            <div class="col-12 text-end">
              <button type="submit" class="btn btn-primary me-3">
                <i class="fas fa-save me-1"></i>Save & Submit
              </button>
              <button type="button" class="btn btn-secondary" (click)="onCancel()">
                <i class="fas fa-times me-1"></i>Cancel
              </button>
            </div>
          </div>
        </form>


      </div>
    </div>
  </div>
</div>
