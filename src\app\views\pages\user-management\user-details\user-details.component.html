<div class="container-fluid">
  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading user details...</span>
    </div>
    <p class="mt-2 text-muted">Loading user details...</p>
  </div>

  <!-- User Details -->
  <div *ngIf="!loading && user" class="row">
    <!-- User Header -->
    <div class="col-12 mb-4">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div class="d-flex align-items-center">
              <!-- User Avatar -->
              <div class="avatar avatar-xl me-4">
                <div class="avatar-initial bg-primary text-white rounded-circle">
                  {{ getUserInitials() }}
                </div>
              </div>
              
              <!-- User Info -->
              <div>
                <h3 class="mb-1">{{ user.full_name || 'N/A' }}</h3>
                <p class="text-muted mb-2">{{ user.email }}</p>
                <div class="d-flex gap-2">
                  <span class="badge" [class]="getUserStatusClass()">
                    <i 
                      [attr.data-feather]="user.is_deleted ? 'trash-2' : (user.is_active ? 'check-circle' : 'x-circle')" 
                      class="icon-xs me-1" 
                      appFeatherIcon></i>
                    {{ getUserStatusText() }}
                  </span>
                  <span class="badge" [class]="getUserRoleClass()">
                    <i 
                      [attr.data-feather]="user.is_superuser ? 'shield' : 'user'" 
                      class="icon-xs me-1" 
                      appFeatherIcon></i>
                    {{ getUserRoleText() }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2">
              <button 
                type="button" 
                class="btn btn-outline-secondary" 
                (click)="goBack()">
                <i data-feather="arrow-left" class="icon-sm me-1" appFeatherIcon></i>
                Back to List
              </button>
              
              <button 
                *ngIf="canPerformAction('edit')"
                type="button" 
                class="btn btn-outline-primary" 
                (click)="editUser()">
                <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
                Edit User
              </button>

              <div class="dropdown">
                <button 
                  class="btn btn-outline-secondary dropdown-toggle" 
                  type="button" 
                  data-bs-toggle="dropdown">
                  <i data-feather="more-vertical" class="icon-sm me-1" appFeatherIcon></i>
                  Actions
                </button>
                <ul class="dropdown-menu">
                  <li *ngIf="canPerformAction('activate') && !user.is_deleted">
                    <a class="dropdown-item" (click)="toggleUserStatus()">
                      <i 
                        [attr.data-feather]="user.is_active ? 'x-circle' : 'check-circle'" 
                        class="icon-xs me-2" 
                        appFeatherIcon></i>
                      {{ user.is_active ? 'Deactivate' : 'Activate' }} User
                    </a>
                  </li>
                  <li *ngIf="canPerformAction('delete') && !user.is_deleted">
                    <a class="dropdown-item text-danger" (click)="deleteUser()">
                      <i data-feather="trash-2" class="icon-xs me-2" appFeatherIcon></i>
                      Delete User
                    </a>
                  </li>
                  <li *ngIf="canPerformAction('restore') && user.is_deleted">
                    <a class="dropdown-item text-success" (click)="restoreUser()">
                      <i data-feather="refresh-cw" class="icon-xs me-2" appFeatherIcon></i>
                      Restore User
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="col-12 mb-4">
      <ul class="nav nav-tabs">
        <li class="nav-item">
          <a 
            class="nav-link" 
            [class.active]="activeTab === 'overview'"
            (click)="setActiveTab('overview')"
            href="javascript:void(0)">
            <i data-feather="user" class="icon-sm me-1" appFeatherIcon></i>
            Overview
          </a>
        </li>
        <li class="nav-item">
          <a 
            class="nav-link" 
            [class.active]="activeTab === 'roles'"
            (click)="setActiveTab('roles')"
            href="javascript:void(0)">
            <i data-feather="shield" class="icon-sm me-1" appFeatherIcon></i>
            Roles & Permissions
          </a>
        </li>
        <li class="nav-item">
          <a 
            class="nav-link" 
            [class.active]="activeTab === 'activity'"
            (click)="setActiveTab('activity')"
            href="javascript:void(0)">
            <i data-feather="activity" class="icon-sm me-1" appFeatherIcon></i>
            Activity Log
          </a>
        </li>
      </ul>
    </div>

    <!-- Tab Content -->
    <div class="col-12">
      <!-- Overview Tab -->
      <div *ngIf="activeTab === 'overview'" class="row">
        <!-- Basic Information -->
        <div class="col-lg-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
                Basic Information
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-sm-4">
                  <strong>First Name:</strong>
                </div>
                <div class="col-sm-8">
                  {{ user.first_name || 'N/A' }}
                </div>
              </div>
              <hr>
              <div class="row">
                <div class="col-sm-4">
                  <strong>Last Name:</strong>
                </div>
                <div class="col-sm-8">
                  {{ user.last_name || 'N/A' }}
                </div>
              </div>
              <hr>
              <div class="row">
                <div class="col-sm-4">
                  <strong>Email:</strong>
                </div>
                <div class="col-sm-8">
                  <a [href]="'mailto:' + user.email" class="text-decoration-none">
                    {{ user.email }}
                  </a>
                </div>
              </div>
              <hr>
              <div class="row">
                <div class="col-sm-4">
                  <strong>User ID:</strong>
                </div>
                <div class="col-sm-8">
                  <code>{{ user.id }}</code>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Status -->
        <div class="col-lg-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i data-feather="settings" class="icon-sm me-2" appFeatherIcon></i>
                Account Status
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-sm-4">
                  <strong>Status:</strong>
                </div>
                <div class="col-sm-8">
                  <span class="badge" [class]="getUserStatusClass()">
                    {{ getUserStatusText() }}
                  </span>
                </div>
              </div>
              <hr>
              <div class="row">
                <div class="col-sm-4">
                  <strong>Role:</strong>
                </div>
                <div class="col-sm-8">
                  <span class="badge" [class]="getUserRoleClass()">
                    {{ getUserRoleText() }}
                  </span>
                </div>
              </div>
              <hr>
              <div class="row">
                <div class="col-sm-4">
                  <strong>Created:</strong>
                </div>
                <div class="col-sm-8">
                  {{ formatDate(user.created_at) }}
                  <br>
                  <small class="text-muted">{{ getRelativeTime(user.created_at) }}</small>
                </div>
              </div>
              <hr *ngIf="user.updated_at">
              <div *ngIf="user.updated_at" class="row">
                <div class="col-sm-4">
                  <strong>Last Updated:</strong>
                </div>
                <div class="col-sm-8">
                  {{ formatDate(user.updated_at) }}
                  <br>
                  <small class="text-muted">{{ getRelativeTime(user.updated_at) }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i data-feather="zap" class="icon-sm me-2" appFeatherIcon></i>
                Quick Actions
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex flex-wrap gap-2">
                <button 
                  *ngIf="canPerformAction('edit')"
                  type="button" 
                  class="btn btn-outline-primary btn-sm" 
                  (click)="editUser()">
                  <i data-feather="edit" class="icon-xs me-1" appFeatherIcon></i>
                  Edit Profile
                </button>
                
                <button 
                  *ngIf="canPerformAction('activate') && !user.is_deleted"
                  type="button" 
                  class="btn btn-sm"
                  [class.btn-outline-warning]="user.is_active"
                  [class.btn-outline-success]="!user.is_active"
                  (click)="toggleUserStatus()"
                  [disabled]="activating">
                  <div 
                    *ngIf="activating"
                    class="spinner-border spinner-border-sm me-1" 
                    role="status">
                  </div>
                  <i 
                    *ngIf="!activating"
                    [attr.data-feather]="user.is_active ? 'x-circle' : 'check-circle'" 
                    class="icon-xs me-1" 
                    appFeatherIcon></i>
                  {{ user.is_active ? 'Deactivate' : 'Activate' }}
                </button>

                <button 
                  *ngIf="canPerformAction('delete') && !user.is_deleted"
                  type="button" 
                  class="btn btn-outline-danger btn-sm" 
                  (click)="deleteUser()"
                  [disabled]="deleting">
                  <div 
                    *ngIf="deleting"
                    class="spinner-border spinner-border-sm me-1" 
                    role="status">
                  </div>
                  <i 
                    *ngIf="!deleting"
                    data-feather="trash-2" 
                    class="icon-xs me-1" 
                    appFeatherIcon></i>
                  Delete User
                </button>

                <button 
                  *ngIf="canPerformAction('restore') && user.is_deleted"
                  type="button" 
                  class="btn btn-outline-success btn-sm" 
                  (click)="restoreUser()"
                  [disabled]="restoring">
                  <div 
                    *ngIf="restoring"
                    class="spinner-border spinner-border-sm me-1" 
                    role="status">
                  </div>
                  <i 
                    *ngIf="!restoring"
                    data-feather="refresh-cw" 
                    class="icon-xs me-1" 
                    appFeatherIcon></i>
                  Restore User
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Roles & Permissions Tab -->
      <div *ngIf="activeTab === 'roles'" class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i data-feather="shield" class="icon-sm me-2" appFeatherIcon></i>
                Roles & Permissions
              </h5>
            </div>
            <div class="card-body">
              <div class="alert alert-info">
                <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
                Role and permission management will be implemented in a future update.
              </div>
              
              <div class="text-center py-4">
                <i data-feather="shield" class="icon-xxl text-muted mb-3" appFeatherIcon></i>
                <h5 class="text-muted">Roles & Permissions</h5>
                <p class="text-muted">
                  This section will display user roles, permissions, and access controls.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Log Tab -->
      <div *ngIf="activeTab === 'activity'" class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i data-feather="activity" class="icon-sm me-2" appFeatherIcon></i>
                Activity Log
              </h5>
            </div>
            <div class="card-body">
              <div class="alert alert-info">
                <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
                Activity logging will be implemented in a future update.
              </div>
              
              <div class="text-center py-4">
                <i data-feather="activity" class="icon-xxl text-muted mb-3" appFeatherIcon></i>
                <h5 class="text-muted">Activity Log</h5>
                <p class="text-muted">
                  This section will display user activity history, login records, and system interactions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
