import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CompoffService, CompoffRequestResponse, CompoffApprovalRequest } from '../../../../core/services/compoff.service';
import Swal from 'sweetalert2';

/**
 * Comp-off Management Component
 * Handles admin/manager view for reviewing and approving comp-off requests
 */
@Component({
  selector: 'app-compoff-management',
  templateUrl: './compoff-management.component.html',
  styleUrls: ['./compoff-management.component.scss']
})
export class CompoffManagementComponent implements OnInit, OnDestroy {
  @ViewChild('approvalModal', { static: true }) approvalModal!: TemplateRef<any>;

  // Component state
  private destroy$ = new Subject<void>();
  loading = false;
  error: string | null = null;

  // Data
  pendingRequests: CompoffRequestResponse[] = [];
  allRequests: CompoffRequestResponse[] = [];
  selectedRequest: CompoffRequestResponse | null = null;

  // Filters
  statusFilter = 'pending'; // 'pending', 'approved', 'rejected', 'all'
  searchTerm = '';

  // Approval form
  approvalForm: FormGroup;
  submittingApproval = false;

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;

  constructor(
    private compoffService: CompoffService,
    private modalService: NgbModal,
    private fb: FormBuilder
  ) {
    this.approvalForm = this.fb.group({
      action: ['approve', Validators.required],
      comments: ['']
    });
  }

  ngOnInit(): void {
    this.loadCompoffRequests();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load comp-off requests based on current filter
   */
  loadCompoffRequests(): void {
    this.loading = true;
    this.error = null;

    const loadMethod = this.statusFilter === 'pending' 
      ? this.compoffService.getPendingCompoffRequests()
      : this.compoffService.getAllCompoffRequests(this.statusFilter === 'all' ? undefined : this.statusFilter);

    loadMethod.pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (requests) => {
        console.log('✅ Comp-off requests loaded:', requests);
        
        if (this.statusFilter === 'pending') {
          this.pendingRequests = requests;
        } else {
          this.allRequests = requests;
        }
        
        this.totalItems = requests.length;
        this.loading = false;
      },
      error: (error) => {
        console.error('❌ Error loading comp-off requests:', error);
        this.error = 'Failed to load comp-off requests. Please try again.';
        this.loading = false;
        
        // Show error message
        Swal.fire({
          icon: 'error',
          title: 'Loading Failed',
          text: this.error,
          confirmButtonText: 'OK'
        });
      }
    });
  }

  /**
   * Get filtered and paginated requests
   */
  get filteredRequests(): CompoffRequestResponse[] {
    const requests = this.statusFilter === 'pending' ? this.pendingRequests : this.allRequests;
    
    let filtered = requests;
    
    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = requests.filter(request => 
        request.employee_name?.toLowerCase().includes(term) ||
        request.employee_code?.toLowerCase().includes(term) ||
        request.reason.toLowerCase().includes(term) ||
        request.working_date.includes(term)
      );
    }
    
    // Apply pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return filtered.slice(startIndex, startIndex + this.itemsPerPage);
  }

  /**
   * Get total pages for pagination
   */
  get totalPages(): number {
    const requests = this.statusFilter === 'pending' ? this.pendingRequests : this.allRequests;
    let filtered = requests;
    
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = requests.filter(request => 
        request.employee_name?.toLowerCase().includes(term) ||
        request.employee_code?.toLowerCase().includes(term) ||
        request.reason.toLowerCase().includes(term) ||
        request.working_date.includes(term)
      );
    }
    
    return Math.ceil(filtered.length / this.itemsPerPage);
  }

  /**
   * Change status filter
   */
  onStatusFilterChange(status: string): void {
    this.statusFilter = status;
    this.currentPage = 1;
    this.loadCompoffRequests();
  }

  /**
   * Handle search
   */
  onSearch(): void {
    this.currentPage = 1;
  }

  /**
   * Clear search
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.currentPage = 1;
  }

  /**
   * Change page
   */
  onPageChange(page: number): void {
    this.currentPage = page;
  }

  /**
   * Open approval modal
   */
  openApprovalModal(request: CompoffRequestResponse): void {
    this.selectedRequest = request;
    this.approvalForm.reset({
      action: 'approve',
      comments: ''
    });
    this.modalService.open(this.approvalModal, { size: 'md' });
  }

  /**
   * Submit approval/rejection
   */
  submitApproval(): void {
    if (!this.selectedRequest || this.approvalForm.invalid) {
      return;
    }

    this.submittingApproval = true;
    const formValue = this.approvalForm.value;
    
    const approval: CompoffApprovalRequest = {
      action: formValue.action,
      comments: formValue.comments || undefined
    };

    this.compoffService.approveCompoffRequest(this.selectedRequest.id, approval).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        console.log('✅ Comp-off approval successful:', response);
        this.submittingApproval = false;
        
        // Show success message
        const actionText = formValue.action === 'approve' ? 'approved' : 'rejected';
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: `Comp-off request ${actionText} successfully!`,
          timer: 3000,
          showConfirmButton: false
        }).then(() => {
          this.modalService.dismissAll();
          this.loadCompoffRequests(); // Reload data
        });
      },
      error: (error) => {
        console.error('❌ Comp-off approval failed:', error);
        this.submittingApproval = false;
        
        // Extract error message
        let errorMessage = 'Failed to process approval. Please try again.';
        if (error?.error?.detail) {
          errorMessage = error.error.detail;
        } else if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }
        
        // Show error message
        Swal.fire({
          icon: 'error',
          title: 'Approval Failed',
          text: errorMessage,
          confirmButtonText: 'OK'
        });
      }
    });
  }

  /**
   * Quick approve request
   */
  quickApprove(request: CompoffRequestResponse): void {
    Swal.fire({
      title: 'Approve Comp-off Request?',
      html: `
        <div style="text-align: left;">
          <p><strong>Employee:</strong> ${request.employee_name || 'N/A'} (${request.employee_code || 'N/A'})</p>
          <p><strong>Working Date:</strong> ${request.working_date}</p>
          <p><strong>Reason:</strong> ${request.reason}</p>
        </div>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, Approve',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#28a745'
    }).then((result) => {
      if (result.isConfirmed) {
        const approval: CompoffApprovalRequest = {
          action: 'approve'
        };
        
        this.compoffService.approveCompoffRequest(request.id, approval).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (response) => {
            console.log('✅ Quick approval successful:', response);
            
            Swal.fire({
              icon: 'success',
              title: 'Approved!',
              text: 'Comp-off request approved successfully!',
              timer: 2000,
              showConfirmButton: false
            });
            
            this.loadCompoffRequests(); // Reload data
          },
          error: (error) => {
            console.error('❌ Quick approval failed:', error);
            
            let errorMessage = 'Failed to approve request. Please try again.';
            if (error?.error?.detail) {
              errorMessage = error.error.detail;
            } else if (error?.error?.message) {
              errorMessage = error.error.message;
            }
            
            Swal.fire({
              icon: 'error',
              title: 'Approval Failed',
              text: errorMessage,
              confirmButtonText: 'OK'
            });
          }
        });
      }
    });
  }

  /**
   * Quick reject request
   */
  quickReject(request: CompoffRequestResponse): void {
    Swal.fire({
      title: 'Reject Comp-off Request?',
      html: `
        <div style="text-align: left;">
          <p><strong>Employee:</strong> ${request.employee_name || 'N/A'} (${request.employee_code || 'N/A'})</p>
          <p><strong>Working Date:</strong> ${request.working_date}</p>
          <p><strong>Reason:</strong> ${request.reason}</p>
        </div>
      `,
      input: 'textarea',
      inputLabel: 'Rejection Reason (Optional)',
      inputPlaceholder: 'Enter reason for rejection...',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, Reject',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#dc3545'
    }).then((result) => {
      if (result.isConfirmed) {
        const approval: CompoffApprovalRequest = {
          action: 'reject',
          comments: result.value || undefined
        };
        
        this.compoffService.approveCompoffRequest(request.id, approval).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (response) => {
            console.log('✅ Quick rejection successful:', response);
            
            Swal.fire({
              icon: 'success',
              title: 'Rejected!',
              text: 'Comp-off request rejected successfully!',
              timer: 2000,
              showConfirmButton: false
            });
            
            this.loadCompoffRequests(); // Reload data
          },
          error: (error) => {
            console.error('❌ Quick rejection failed:', error);
            
            let errorMessage = 'Failed to reject request. Please try again.';
            if (error?.error?.detail) {
              errorMessage = error.error.detail;
            } else if (error?.error?.message) {
              errorMessage = error.error.message;
            }
            
            Swal.fire({
              icon: 'error',
              title: 'Rejection Failed',
              text: errorMessage,
              confirmButtonText: 'OK'
            });
          }
        });
      }
    });
  }

  /**
   * Export comp-off requests
   */
  exportRequests(): void {
    console.log('📊 Exporting comp-off requests...');
    
    const filters = {
      status: this.statusFilter === 'all' ? undefined : this.statusFilter,
      search: this.searchTerm || undefined
    };
    
    this.compoffService.exportCompoffRequests(filters).subscribe({
      next: (blob) => {
        console.log('✅ Export successful');
        
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `comp-off-requests-${new Date().toISOString().split('T')[0]}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Export Complete!',
          text: 'Comp-off requests exported successfully!',
          timer: 2000,
          showConfirmButton: false
        });
      },
      error: (error) => {
        console.error('❌ Export failed:', error);
        
        Swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'Failed to export comp-off requests. Please try again.',
          confirmButtonText: 'OK'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadCompoffRequests();
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'pending':
        return 'badge-warning';
      case 'approved':
        return 'badge-success';
      case 'rejected':
        return 'badge-danger';
      default:
        return 'badge-secondary';
    }
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  }

  /**
   * Format datetime for display
   */
  formatDateTime(dateString: string): string {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  }
}
