import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UniqueCodeService {
  // BehaviorSubject to store the generated unique code
  private uniqueCodeSubject = new BehaviorSubject<string>('');
  uniqueCode$: Observable<string> = this.uniqueCodeSubject.asObservable();

  // Store counters for different product types
  private counters: { [key: string]: number } = {
    'RS': 0, // Retail Syndication
    'CS': 0, // Corporate Syndication
    'PR': 0, // Property
    'IN': 0  // Insurance
  };

  constructor() {}

  /**
   * Generate a unique code based on product type
   * @param productType The product type (e.g., 'Retail Syndication', 'Corporate Syndication')
   * @param leadId The lead ID (e.g., 'BCS-2025-55')
   * @returns The generated unique code (e.g., 'BCS-2025-55-RS-001')
   */
  generateUniqueCode(productType: string, leadId: string): string {
    if (!productType || !leadId) {
      return '';
    }

    // Extract the product type code
    let productTypeCode = this.getProductTypeCode(productType);

    // Use the current counter value (don't increment yet)
    const currentCounter = this.counters[productTypeCode] || 0;
    
    // Format the counter as a 3-digit number
    const counterStr = (currentCounter + 1).toString().padStart(3, '0');
    
    // Generate the unique code
    const uniqueCode = `${leadId}-${productTypeCode}-${counterStr}`;
    
    // Update the BehaviorSubject
    this.uniqueCodeSubject.next(uniqueCode);
    
    console.log(`Generated unique code: ${uniqueCode} for product type: ${productType} (not yet confirmed)`); 
    return uniqueCode;
  }

  /**
   * Confirm the unique code after form submission
   * This will increment the counter for the product type
   * @param productType The product type (e.g., 'Retail Syndication', 'Corporate Syndication')
   */
  confirmUniqueCode(productType: string): void {
    if (!productType) {
      return;
    }

    // Extract the product type code
    let productTypeCode = this.getProductTypeCode(productType);

    // Increment the counter for this product type
    this.counters[productTypeCode] = (this.counters[productTypeCode] || 0) + 1;
    
    console.log(`Confirmed unique code for ${productType}. Counter incremented to: ${this.counters[productTypeCode]}`);
    
    // Update the unique code in the UI with the new counter
    this.updateUniqueCodeDisplay(productTypeCode);
  }

  /**
   * Update the unique code display with the current counter
   */
  private updateUniqueCodeDisplay(productTypeCode: string): void {
    // Get the current unique code
    const currentCode = this.uniqueCodeSubject.value;
    if (!currentCode) return;
    
    // Extract the lead ID part from the current code
    const parts = currentCode.split('-');
    if (parts.length < 3) return;
    
    const leadIdPart = parts.slice(0, -2).join('-'); // Everything except the last two parts
    
    // Format the counter as a 3-digit number
    const counterStr = this.counters[productTypeCode].toString().padStart(3, '0');
    
    // Generate the new unique code with the updated counter
    const newUniqueCode = `${leadIdPart}-${productTypeCode}-${counterStr}`;
    
    // Update the BehaviorSubject
    this.uniqueCodeSubject.next(newUniqueCode);
    
    console.log(`Updated unique code display: ${newUniqueCode}`);
  }

  /**
   * Get the product type code from the product type name
   */
  private getProductTypeCode(productType: string): string {
    if (productType.toLowerCase().includes('retail') && productType.toLowerCase().includes('syndication')) {
      return 'RS';
    } else if (productType.toLowerCase().includes('corporate') && productType.toLowerCase().includes('syndication')) {
      return 'CS';
    } else if (productType.toLowerCase().includes('property')) {
      return 'PR';
    } else if (productType.toLowerCase().includes('insurance')) {
      return 'IN';
    } else {
      // Default to CS if no match
      return 'CS';
    }
  }

  /**
   * Get the current unique code
   */
  getCurrentUniqueCode(): string {
    return this.uniqueCodeSubject.value;
  }

  /**
   * Reset the counter for a specific product type
   */
  resetCounter(productTypeCode: string): void {
    if (this.counters[productTypeCode] !== undefined) {
      this.counters[productTypeCode] = 0;
    }
  }

  /**
   * Reset all counters
   */
  resetAllCounters(): void {
    Object.keys(this.counters).forEach(key => {
      this.counters[key] = 0;
    });
  }
}