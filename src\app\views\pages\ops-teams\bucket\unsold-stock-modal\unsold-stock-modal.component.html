<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Unsold Stock / Leased Property</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #unsoldStockForm="ngForm">
    <div class="row mb-3">
      <!-- Property Information Section -->
      <div class="col-12 mb-3">
        <h6 class="section-title">Property Information</h6>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectName" class="form-label">Project Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="projectName" name="projectName" [(ngModel)]="formData.projectName" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="location" name="location" [(ngModel)]="formData.location" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
        <select class="form-select" id="category" name="category" [(ngModel)]="formData.category" required>
          <option value="">Select Category</option>
          <option value="Residential">Residential</option>
          <option value="Commercial">Commercial</option>
          <option value="Retail">Retail</option>
          <option value="Industrial">Industrial</option>
          <option value="Land">Land</option>
        </select>
      </div>

      <!-- Area and Value Section -->
      <div class="col-12 mb-3 mt-2">
        <h6 class="section-title">Area and Value</h6>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="saleableArea" class="form-label">Saleable Area (sq.ft) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="saleableArea" name="saleableArea" [(ngModel)]="formData.saleableArea" required (change)="calculateValue()">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="rate" class="form-label">Rate (₹/sq.ft) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="rate" name="rate" [(ngModel)]="formData.rate" required (change)="calculateValue()">
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="value" class="form-label">Value (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="value" name="value" [(ngModel)]="formData.value" required>
      </div>

      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="debt" class="form-label">Debt (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="debt" name="debt" [(ngModel)]="formData.debt" required>
      </div>
      Land Bank & Upcoming Projects
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="remarks" class="form-label">Remarks</label>
        <textarea class="form-control" id="remarks" name="remarks" rows="2" [(ngModel)]="formData.remarks"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="unsoldStockForm.invalid" (click)="saveChanges()">Save</button>
</div>
