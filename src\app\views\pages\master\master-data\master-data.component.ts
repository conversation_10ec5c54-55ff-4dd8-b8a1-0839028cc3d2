import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbModal, NgbPaginationModule, NgbTooltipModule, NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { MasterService } from '../../../../core/services/master.service';
import { MasterDataService } from '../../../../core/services/master-data.service';
import { ProfessionService } from '../../../../core/services/profession.service';
import { Profession, ProfessionCreate, ProfessionUpdate } from '../../../../core/models/profession.model';
import { catchError, debounceTime, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

// Sortable directive
export type SortColumn = 'name' | 'type' | 'status' | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number | null | undefined, v2: string | number | null | undefined) {
  if (v1 === null || v1 === undefined) return -1;
  if (v2 === null || v2 === undefined) return 1;
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

// Master data types
export interface MasterDataType {
  key: string;
  label: string;
  description: string;
  service: 'master' | 'master-data' | 'profession';
  endpoint?: string;
}

@Component({
  selector: 'app-master-data',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbdSortableHeader,
    NgbPaginationModule,
    NgbTooltipModule,
    NgbDropdownModule
  ],
  templateUrl: './master-data.component.html',
  styleUrl: './master-data.component.scss'
})
export class MasterDataComponent implements OnInit {
  // Master data types configuration
  masterDataTypes: MasterDataType[] = [
    {
      key: 'professions',
      label: 'Professions',
      description: 'Manage professional categories and types',
      service: 'master'
    },
    {
      key: 'private-banks',
      label: 'Private Banks',
      description: 'Manage private banking institutions',
      service: 'master-data',
      endpoint: 'private-banks'
    },
    {
      key: 'nbfcs',
      label: 'NBFCs',
      description: 'Manage Non-Banking Financial Companies',
      service: 'master-data',
      endpoint: 'nbfcs'
    },
    {
      key: 'institutes',
      label: 'Institutes',
      description: 'Manage educational and training institutes',
      service: 'master-data',
      endpoint: 'institutes'
    },
    {
      key: 'corporate-consultancies',
      label: 'Corporate Consultancies',
      description: 'Manage corporate consultancy firms',
      service: 'master-data',
      endpoint: 'corporate-consultancies'
    }
  ];

  // Current data type
  currentDataType: MasterDataType = this.masterDataTypes[0];

  // Data
  items: any[] = [];
  selectedItem: any | null = null;

  // Loading state
  loading = false;
  submitting = false;
  exporting = false;
  uploading = false;

  // Form
  itemForm: FormGroup;
  formMode: 'create' | 'edit' = 'create';
  currentItemId?: string;

  // Search
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 10;
  totalItems = 0;

  // File upload
  selectedFile: File | null = null;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  constructor(
    private masterService: MasterService,
    private masterDataService: MasterDataService,
    private professionService: ProfessionService,
    private modalService: NgbModal,
    private fb: FormBuilder
  ) {
    // Initialize form
    this.itemForm = this.fb.group({
      name: ['', [Validators.required]],
      type: [''],
      status: ['active']
    });
  }

  ngOnInit(): void {
    // Load initial data
    this.loadItems();

    // Set up search
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)
    ).subscribe(value => {
      this.page = 1;
      this.loadItems();
    });
  }

  // Switch between different master data types
  switchDataType(dataType: MasterDataType): void {
    this.currentDataType = dataType;
    this.page = 1;
    this.searchTerm.setValue('');
    this.selectedItem = null;
    this.setupFormForDataType();
    this.loadItems();
  }

  // Setup form based on data type
  setupFormForDataType(): void {
    if (this.currentDataType.key === 'professions') {
      this.itemForm = this.fb.group({
        name: ['', [Validators.required]],
        type: ['Profession', [Validators.required]],
        status: ['active', [Validators.required]]
      });
    } else {
      // For other master data types (banks, NBFCs, etc.)
      this.itemForm = this.fb.group({
        name: ['', [Validators.required]]
      });
    }
  }

  // Load items based on current data type
  loadItems(): void {
    this.loading = true;

    const searchTerm = this.searchTerm.value?.trim() || '';

    if (this.currentDataType.key === 'professions') {
      this.loadProfessions(searchTerm);
    } else {
      this.loadMasterDataItems(searchTerm);
    }
  }

  // Load professions
  private loadProfessions(searchTerm: string): void {
    // Convert page/size to skip/limit for new API format
    const skip = (this.page - 1) * this.pageSize;
    this.masterService.getProfessions(skip, this.pageSize, searchTerm)
      .pipe(
        catchError(error => {
          console.error('Error loading professions:', error);
          this.showErrorMessage('Failed to load professions. Please try again.');
          return of({ items: [], total: 0, page: 1, size: this.pageSize, pages: 0 });
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        this.items = response.items;
        this.totalItems = response.total;
      });
  }

  // Load master data items (banks, NBFCs, etc.)
  private loadMasterDataItems(searchTerm: string): void {
    const entityType = this.currentDataType.endpoint!;

    // Use the appropriate service method based on entity type
    let serviceCall;
    switch (entityType) {
      case 'private-banks':
        // Convert page to skip/limit format
        serviceCall = this.masterDataService.getPrivateBanks((this.page - 1) * this.pageSize, this.pageSize);
        break;
      case 'nbfcs':
        serviceCall = this.masterDataService.getNBFCs((this.page - 1) * this.pageSize, this.pageSize);
        break;
      case 'institutes':
        serviceCall = this.masterDataService.getInstitutes((this.page - 1) * this.pageSize, this.pageSize);
        break;
      case 'corporate-consultancies':
        serviceCall = this.masterDataService.getCorporateConsultancies((this.page - 1) * this.pageSize, this.pageSize);
        break;
      default:
        serviceCall = of([]);
    }

    serviceCall
      .pipe(
        catchError(error => {
          console.error(`Error loading ${entityType}:`, error);
          this.showErrorMessage(`Failed to load ${this.currentDataType.label.toLowerCase()}. Please try again.`);
          return of([]);
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        this.items = Array.isArray(response) ? response : [];
        this.totalItems = this.items.length;

        // Apply client-side search if needed
        if (searchTerm) {
          this.items = this.filterItems(this.items, searchTerm);
          this.totalItems = this.items.length;
        }
      });
  }

  // Filter items based on search term
  filterItems(items: any[], searchTerm: string): any[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return items;
    }

    searchTerm = searchTerm.toLowerCase().trim();

    return items.filter(item => {
      return (
        (item.name && item.name.toLowerCase().includes(searchTerm)) ||
        (item.type && item.type.toLowerCase().includes(searchTerm)) ||
        (item.status && item.status.toLowerCase().includes(searchTerm))
      );
    });
  }

  // Refresh when pagination changes
  onPageChange(page: number): void {
    this.page = page;
    this.loadItems();
  }

  // Handle sorting
  onSort({ column, direction }: SortEvent): void {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.loadItems();
    } else {
      this.items = [...this.items].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  // Open modal for creating/editing
  openItemModal(modal: TemplateRef<any>, item?: any): void {
    if (item) {
      // Edit mode
      this.formMode = 'edit';
      this.currentItemId = item.id;

      if (this.currentDataType.key === 'professions') {
        this.itemForm.patchValue({
          name: item.name,
          type: item.type,
          status: item.status
        });
      } else {
        this.itemForm.patchValue({
          name: item.name
        });
      }
    } else {
      // Create mode
      this.formMode = 'create';
      this.currentItemId = undefined;

      if (this.currentDataType.key === 'professions') {
        this.itemForm.reset({
          name: '',
          type: 'Profession',
          status: 'active'
        });
      } else {
        this.itemForm.reset({
          name: ''
        });
      }
    }

    this.modalService.open(modal, { centered: true });
  }

  // View item details
  viewItem(item: any, modal: TemplateRef<any>): void {
    if (item.id) {
      this.loading = true;

      // Get item details based on data type
      let serviceCall: any;
      if (this.currentDataType.key === 'professions') {
        serviceCall = this.masterService.getProfession(item.id);
      } else {
        serviceCall = this.masterDataService.getMasterDataItem(this.currentDataType.endpoint!, item.id);
      }

      serviceCall
        .pipe(
          catchError((error: any) => {
            console.error('Error fetching item details:', error);
            this.showErrorMessage('Failed to fetch item details. Please try again.');
            return of(null);
          }),
          finalize(() => {
            this.loading = false;
          })
        )
        .subscribe((result: any) => {
          if (result) {
            this.selectedItem = result;
            this.modalService.open(modal, { centered: true, size: 'lg' });
          }
        });
    }
  }

  // Save item (create or update)
  saveItem(): void {
    if (this.itemForm.invalid) {
      return;
    }

    this.submitting = true;

    if (this.formMode === 'create') {
      this.createItem();
    } else if (this.formMode === 'edit' && this.currentItemId) {
      this.updateItem();
    }
  }

  // Create new item
  private createItem(): void {
    const formValue = this.itemForm.value;

    let serviceCall: any;
    if (this.currentDataType.key === 'professions') {
      const newProfession: ProfessionCreate = formValue;
      serviceCall = this.masterService.createProfession(newProfession);
    } else {
      const newItem = { name: formValue.name };
      switch (this.currentDataType.endpoint) {
        case 'private-banks':
          serviceCall = this.masterDataService.createPrivateBank(newItem);
          break;
        case 'nbfcs':
          serviceCall = this.masterDataService.createNBFC(newItem);
          break;
        case 'institutes':
          serviceCall = this.masterDataService.createInstitute(newItem);
          break;
        case 'corporate-consultancies':
          serviceCall = this.masterDataService.createCorporateConsultancy(newItem);
          break;
        default:
          serviceCall = of(null);
      }
    }

    serviceCall
      .pipe(
        catchError((error: any) => {
          console.error('Error creating item:', error);
          this.showErrorMessage(`Failed to create ${this.currentDataType.label.toLowerCase().slice(0, -1)}. Please try again.`);
          return of(null);
        }),
        finalize(() => {
          this.submitting = false;
        })
      )
      .subscribe((result: any) => {
        if (result) {
          this.modalService.dismissAll();
          this.showSuccessMessage(`${this.currentDataType.label.slice(0, -1)} created successfully!`);
          this.loadItems();
        }
      });
  }

  // Update existing item
  private updateItem(): void {
    const formValue = this.itemForm.value;

    let serviceCall: any;
    if (this.currentDataType.key === 'professions') {
      const updatedProfession: ProfessionUpdate = formValue;
      serviceCall = this.masterService.updateProfession(Number(this.currentItemId!), updatedProfession);
    } else {
      const updatedItem = { name: formValue.name };
      serviceCall = this.masterDataService.updateMasterDataItem(this.currentDataType.endpoint!, this.currentItemId!, updatedItem);
    }

    serviceCall
      .pipe(
        catchError((error: any) => {
          console.error('Error updating item:', error);
          this.showErrorMessage(`Failed to update ${this.currentDataType.label.toLowerCase().slice(0, -1)}. Please try again.`);
          return of(null);
        }),
        finalize(() => {
          this.submitting = false;
        })
      )
      .subscribe((result: any) => {
        if (result) {
          this.modalService.dismissAll();
          this.showSuccessMessage(`${this.currentDataType.label.slice(0, -1)} updated successfully!`);
          this.loadItems();
        }
      });
  }

  // Delete item
  deleteItem(item: any): void {
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete ${this.currentDataType.label.toLowerCase().slice(0, -1)} "${item.name}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#000',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed && item.id) {
        let serviceCall: any;
        if (this.currentDataType.key === 'professions') {
          serviceCall = this.masterService.deleteProfession(Number(item.id));
        } else {
          serviceCall = this.masterDataService.deleteMasterDataItem(this.currentDataType.endpoint!, item.id);
        }

        serviceCall
          .pipe(
            catchError((error: any) => {
              console.error('Error deleting item:', error);
              this.showErrorMessage(`Failed to delete ${this.currentDataType.label.toLowerCase().slice(0, -1)}. Please try again.`);
              return of(null);
            })
          )
          .subscribe((result: any) => {
            if (result !== null) {
              this.showSuccessMessage(`${this.currentDataType.label.slice(0, -1)} deleted successfully!`);
              this.loadItems();
            }
          });
      }
    });
  }

  // Restore item
  restoreItem(item: any): void {
    if (!item.id) return;

    let serviceCall: any;
    if (this.currentDataType.key === 'professions') {
      serviceCall = this.masterService.restoreProfession(Number(item.id));
    } else {
      serviceCall = this.masterDataService.restoreMasterDataItem(this.currentDataType.endpoint!, item.id);
    }

    serviceCall
      .pipe(
        catchError((error: any) => {
          console.error('Error restoring item:', error);
          this.showErrorMessage(`Failed to restore ${this.currentDataType.label.toLowerCase().slice(0, -1)}. Please try again.`);
          return of(null);
        })
      )
      .subscribe((result: any) => {
        if (result) {
          this.showSuccessMessage(`${this.currentDataType.label.slice(0, -1)} restored successfully!`);
          this.loadItems();
        }
      });
  }

  // Export items
  exportItems(): void {
    this.exporting = true;

    // For now, we'll show a message that export is not available for professions
    // since the master service doesn't have an export method
    if (this.currentDataType.key === 'professions') {
      this.exporting = false;
      this.showErrorMessage('Export functionality is not available for professions at the moment.');
      return;
    }

    let serviceCall: any = this.masterDataService.exportMasterData(this.currentDataType.endpoint!);

    serviceCall
      .pipe(
        catchError((error: any) => {
          console.error('Error exporting items:', error);
          this.showErrorMessage(`Failed to export ${this.currentDataType.label.toLowerCase()}. Please try again.`);
          return of(new Blob());
        }),
        finalize(() => {
          this.exporting = false;
        })
      )
      .subscribe((blob: Blob) => {
        if (blob.size > 0) {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${this.currentDataType.key}_export_${new Date().toISOString().split('T')[0]}.xlsx`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.showSuccessMessage(`${this.currentDataType.label} exported successfully!`);
        }
      });
  }

  // File upload handling
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
    }
  }

  // Bulk upload
  bulkUpload(): void {
    if (!this.selectedFile) {
      this.showErrorMessage('Please select a file to upload.');
      return;
    }

    this.uploading = true;

    // For now, we'll show a message that bulk upload is not available for professions
    // since the master service doesn't have a bulk upload method
    if (this.currentDataType.key === 'professions') {
      this.uploading = false;
      this.selectedFile = null;
      this.showErrorMessage('Bulk upload functionality is not available for professions at the moment.');
      return;
    }

    let serviceCall: any;
    switch (this.currentDataType.endpoint) {
      case 'private-banks':
        serviceCall = this.masterDataService.bulkUploadPrivateBanks(this.selectedFile);
        break;
      case 'nbfcs':
        serviceCall = this.masterDataService.bulkUploadNBFCs(this.selectedFile);
        break;
      case 'institutes':
        serviceCall = this.masterDataService.bulkUploadInstitutes(this.selectedFile);
        break;
      case 'corporate-consultancies':
        serviceCall = this.masterDataService.bulkUploadCorporateConsultancies(this.selectedFile);
        break;
      default:
        serviceCall = of({ success: false, data: { total: 0, success_count: 0, error_count: 0, errors: [] } });
    }

    serviceCall
      .pipe(
        catchError((error: any) => {
          console.error('Error uploading file:', error);
          this.showErrorMessage('Failed to upload file. Please try again.');
          return of({ success: false, data: { total: 0, success_count: 0, error_count: 0, errors: [] } });
        }),
        finalize(() => {
          this.uploading = false;
          this.selectedFile = null;
        })
      )
      .subscribe((response: any) => {
        if (response.success && response.data) {
          const { total, success_count, error_count } = response.data;
          this.showSuccessMessage(`Upload completed! ${success_count} of ${total} records processed successfully.`);
          if (error_count > 0) {
            console.warn('Upload errors:', response.data.errors);
          }
          this.loadItems();
        } else {
          this.showErrorMessage('Upload failed. Please check the file format and try again.');
        }
      });
  }

  // Helper method to handle editing from view modal
  editFromViewModal(itemModal: TemplateRef<any>, currentModal: any): void {
    if (this.selectedItem) {
      currentModal.dismiss();
      this.openItemModal(itemModal, this.selectedItem);
    }
  }

  // Helper methods for status
  getStatusClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-success';
      case 'inactive': return 'bg-secondary';
      default: return 'bg-secondary';
    }
  }

  // Check if current data type supports certain features
  get supportsType(): boolean {
    return this.currentDataType.key === 'professions';
  }

  get supportsStatus(): boolean {
    return this.currentDataType.key === 'professions';
  }

  // Alerts
  showSuccessMessage(message: string): void {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
      timer: 2000,
      showConfirmButton: false
    });
  }

  showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message
    });
  }
}
