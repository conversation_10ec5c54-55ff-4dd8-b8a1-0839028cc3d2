<div class="modal-header">
  <h5 class="modal-title text-light">{{ landId ? 'Edit' : 'Add' }} Project Land Detail</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #projectLandForm="ngForm">
    <div class="row">
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="agreementType" class="form-label">Type of Agreement <span class="text-danger">*</span></label>
        <select class="form-select" id="agreementType" name="agreementType" [(ngModel)]="formData.agreementType" required>
          <option value="" selected disabled>Select Agreement Type</option>
          <option *ngFor="let option of agreementTypeOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="documentDate" class="form-label">Document Date <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="documentDate" name="documentDate" [(ngModel)]="formData.documentDate" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="documentNo" class="form-label">Document No <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="documentNo" name="documentNo" [(ngModel)]="formData.documentNo" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="surveyNo" class="form-label">Survey No <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="surveyNo" name="surveyNo" [(ngModel)]="formData.surveyNo" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="areaSqMtrs" class="form-label">Area (Sq Mtrs) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="areaSqMtrs" name="areaSqMtrs" [(ngModel)]="formData.areaSqMtrs" required min="0">
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="areaGunthaAcre" class="form-label">Area (Guntha/Acre) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="areaGunthaAcre" name="areaGunthaAcre" [(ngModel)]="formData.areaGunthaAcre" required min="0" step="0.01">
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="areaUnit" class="form-label">Area Unit <span class="text-danger">*</span></label>
        <select class="form-select" id="areaUnit" name="areaUnit" [(ngModel)]="formData.areaUnit" required>
          <option value="" selected disabled>Select Unit</option>
          <option *ngFor="let option of areaUnitOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>
      <div class="col-12 mb-3">
        <label for="partiesNames" class="form-label">Names of Parties Between Whom Agreement Has Been Executed <span class="text-danger">*</span></label>
        <textarea class="form-control" id="partiesNames" name="partiesNames" [(ngModel)]="formData.partiesNames" rows="2" required></textarea>
      </div>
      <div class="col-12 mb-3">
        <label for="considerationDetails" class="form-label">Additional Remarks / Consideration Details (Monetary & Area) <span class="text-danger">*</span></label>
        <textarea class="form-control" id="considerationDetails" name="considerationDetails" [(ngModel)]="formData.considerationDetails" rows="2" required></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="projectLandForm.invalid" (click)="saveChanges()">Save</button>
</div>
