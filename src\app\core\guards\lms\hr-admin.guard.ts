import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class HrAdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    // Quick authentication check
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Quick permission check - optimized for performance
    const requiredPermissions = [
      'new_year_activity:read',
      'new_year_activity:create',
      'employees:create'
    ];

    const hasAnyPermission = requiredPermissions.some(permission =>
      this.authService.hasPermission(permission)
    );

    if (!hasAnyPermission) {
      // Only log when access is denied to reduce console spam
      console.log('❌ HR ADMIN GUARD - Access denied for:', currentUser.email);
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    // Only log success in development mode or when needed for debugging
    if (typeof window !== 'undefined' && (window as any).debugGuards) {
      console.log('✅ HR ADMIN GUARD - Access granted for:', currentUser.email);
    }

    return true;
  }
}
