import { ChangeDetectorRef, Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { NgbPaginationModule, NgbTooltipModule, NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../core/feather-icon/feather-icon.directive';
import { ColumnMode, DatatableComponent, NgxDatatableModule } from '@siemens/ngx-datatable';

// Sortable directive
export type SortColumn = keyof LeadData | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Import SalesData interface from sales-list component
export interface SalesData {
  id: number;
  leadName: string;
  source: string;
  sourceType: string;
  location: string;
  companyName: string;
  projectName: string;
  status: 'active' | 'pending' | 'completed';
}

// Extended SalesData interface with additional fields
export interface LeadData extends SalesData {
  subLocation?: string;
  profession?: string;
  contactName?: string;
  mobile?: string;
  email?: string;
  reference?: string;
  team?: string;
  loginPerson?: string;
  handoverTo?: string;
  handoverEmployeeId?: string;
  productType?: string;
  createdBy?: string;
  createdAt?: string;
}

// Interface for position table data
export interface PositionData {
  id: number;
  position: 'PH' | 'TL' | 'DAO';
  name: string;
  count: number;
  employees: PositionEmployee[];
}

// Interface for position employees
export interface PositionEmployee {
  id: number;
  name: string;
  count: number;
}

// Sample data for position table - only one entry for each position type
const POSITION_DATA: PositionData[] = [
  {
    id: 1,
    position: 'PH',
    name: 'Project Head',
    count: 12,
    employees: [
      { id: 1, name: 'Rajesh Sharma', count: 4 },
      { id: 2, name: 'Priya Patel', count: 3 },
      { id: 3, name: 'Amit Singh', count: 2 },
      { id: 4, name: 'Neha Gupta', count: 3 }
    ]
  },
  {
    id: 2,
    position: 'TL',
    name: 'Team Lead',
    count: 8,
    employees: [
      { id: 1, name: 'Vikram Mehta', count: 2 },
      { id: 2, name: 'Anita Desai', count: 3 },
      { id: 3, name: 'Sunil Kumar', count: 3 }
    ]
  },
  {
    id: 3,
    position: 'DAO',
    name: 'DAO',
    count: 15,
    employees: [
      { id: 1, name: 'Rahul Verma', count: 4 },
      { id: 2, name: 'Kavita Sharma', count: 3 },
      { id: 3, name: 'Deepak Patel', count: 5 },
      { id: 4, name: 'Sanjay Gupta', count: 3 }
    ]
  }
];

// Sample data for leads (using the structure from sales-list)
const LEAD_DATA: LeadData[] = [
  {
    id: 1,
    leadName: 'L&T',
    source: 'Self',
    sourceType: 'Existing',
    location: 'Parel',
    subLocation: 'Andheri East',
    companyName: 'L&T',
    profession: 'Real Estate Developer',
    contactName: 'Rajesh Sharma',
    mobile: '+91 98765 43210',
    email: '<EMAIL>',
    reference: 'Sunil Mehta',
    team: 'In-house Team',
    loginPerson: 'Priya Patel',
    handoverTo: 'Amit Singh',
    handoverEmployeeId: 'EMP001',
    productType: 'Education Funding',
    createdBy: 'Vikram Desai',
    projectName: 'L&T Parel',
    status: 'active',
    createdAt: '15 Jun 2023'
  },
  {
    id: 2,
    leadName: 'Hiranandani',
    source: 'Self',
    sourceType: 'Existing',
    location: 'Powai',
    subLocation: 'Powai Lake',
    companyName: 'Hiranandani Developers',
    profession: 'Real Estate Developer',
    contactName: 'Anil Hiranandani',
    mobile: '+91 98765 43211',
    email: '<EMAIL>',
    reference: 'Rahul Mehta',
    team: 'Sales Team',
    loginPerson: 'Neha Sharma',
    handoverTo: 'Ravi Kumar',
    handoverEmployeeId: 'EMP002',
    productType: 'Hospital Funding',
    createdBy: 'Sanjay Gupta',
    projectName: 'Lake Enclave',
    status: 'completed',
    createdAt: '10 May 2023'
  },
  {
    id: 3,
    leadName: 'Lodha Group',
    source: 'Associates',
    sourceType: 'New',
    location: 'Lower Parel',
    subLocation: 'Worli',
    companyName: 'Lodha Developers',
    profession: 'Real Estate Developer',
    contactName: 'Vikram Lodha',
    mobile: '+91 98765 43212',
    email: '<EMAIL>',
    reference: 'Deepak Verma',
    team: 'In-house Team',
    loginPerson: 'Anita Patel',
    handoverTo: 'Suresh Menon',
    handoverEmployeeId: 'EMP003',
    productType: 'Project Funding',
    createdBy: 'Rahul Sharma',
    projectName: 'World Towers',
    status: 'pending',
    createdAt: '05 Apr 2023'
  },
  {
    id: 4,
    leadName: 'Oberoi Realty',
    source: 'Self',
    sourceType: 'Existing',
    location: 'Goregaon',
    subLocation: 'Film City',
    companyName: 'Oberoi Group',
    profession: 'Real Estate Developer',
    contactName: 'Vikas Oberoi',
    mobile: '+91 98765 43213',
    email: '<EMAIL>',
    reference: 'Ajay Singh',
    team: 'Sales Team',
    loginPerson: 'Kavita Sharma',
    handoverTo: 'Prakash Jha',
    handoverEmployeeId: 'EMP004',
    productType: 'Inventory Funding',
    createdBy: 'Manish Gupta',
    projectName: 'Sky City',
    status: 'active',
    createdAt: '12 Mar 2023'
  },
  {
    id: 5,
    leadName: 'Godrej Properties',
    source: 'Associates',
    sourceType: 'New',
    location: 'Vikhroli',
    subLocation: 'Eastern Express Highway',
    companyName: 'Godrej Group',
    profession: 'Real Estate Developer',
    contactName: 'Adi Godrej',
    mobile: '+91 98765 43214',
    email: '<EMAIL>',
    reference: 'Rajiv Mehta',
    team: 'In-house Team',
    loginPerson: 'Sunita Sharma',
    handoverTo: 'Rajesh Khanna',
    handoverEmployeeId: 'EMP005',
    productType: 'Construction Funding ',
    createdBy: 'Vivek Patel',
    projectName: 'Godrej Central',
    status: 'completed',
    createdAt: '25 Feb 2023'
  }
];

// Helper function for filtering
function search(text: string, pipe: DecimalPipe): LeadData[] {
  return LEAD_DATA.filter(lead => {
    const term = text.toLowerCase();
    return lead.leadName.toLowerCase().includes(term)
        || (lead.email ? lead.email.toLowerCase().includes(term) : false)
        || lead.location.toLowerCase().includes(term)
        || (lead.productType ? lead.productType.toLowerCase().includes(term) : false)
        || lead.companyName.toLowerCase().includes(term)
        || lead.projectName.toLowerCase().includes(term);
  });
}



@Component({
  selector: 'app-admin-lead-list',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    NgbDropdownModule,
    RouterLink,
    NgbdSortableHeader,
    NgxDatatableModule
  ],
  templateUrl: './admin-lead-list.component.html',
  styleUrl: './admin-lead-list.component.scss',
  providers: [DecimalPipe]
})
export class AdminLeadListComponent implements OnInit {
  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // NGX Datatable properties
  @ViewChild('positionTable') positionTable: DatatableComponent;
  ColumnMode = ColumnMode;

  // Position data
  positionRows = POSITION_DATA;

  // Leads data
  leads: LeadData[] = LEAD_DATA;
  filteredLeads: LeadData[] = [];

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = LEAD_DATA.length;

  // Make Math available in template
  Math = Math;

  // Search
  searchTerm = new FormControl('', { nonNullable: true });

  // Assign To roles template
  assignToUsers = [
    { id: 1, name: 'PH' },
    { id: 2, name: 'PH+TL' },
    { id: 3, name: 'PH+TL+DAO' },
    { id: 4, name: 'PH+DAO' },
    { id: 5, name: 'TL+DAO' }
  ];

  // Employee list
  employees = [
    { id: 1, name: 'Employee 1' },
    { id: 2, name: 'Employee 2' },
    { id: 3, name: 'Employee 3' },
    { id: 4, name: 'Employee 4' },
    { id: 5, name: 'Employee 5' }
  ];

  // Selected employees for each lead (multiple selection)
  selectedEmployees: { [leadId: number]: number[] } = {};

  // Show employee dropdown flag for each lead
  showEmployeeDropdown: { [leadId: number]: boolean } = {};

  // Selected roles for each lead (multiple selection)
  selectedRoles: { [leadId: number]: number[] } = {};

  // Submitted assignments
  submittedAssignments: {
    lead: LeadData;
    roles: string[];
    employeeIds: number[];
    employeeNames: string[];
    submittedAt: Date;
  }[] = [];

  constructor(private pipe: DecimalPipe, private cdr: ChangeDetectorRef) {}

  /**
   * Get CSS class for position card based on position type
   */
  getPositionCardClass(position: string): string {
    switch (position) {
      case 'PH':
        return 'position-card-primary';
      case 'TL':
        return 'position-card-success';
      case 'DAO':
        return 'position-card-warning';
      default:
        return '';
    }
  }

  /**
   * Get CSS class for position badge based on position type
   */
  getPositionBadgeClass(position: string): string {
    switch (position) {
      case 'PH':
        return 'bg-primary text-white';
      case 'TL':
        return 'bg-success text-white';
      case 'DAO':
        return 'bg-warning text-dark';
      default:
        return 'bg-secondary text-white';
    }
  }

  /**
   * Get icon name for position based on position type
   */
  getPositionIcon(position: string): string {
    switch (position) {
      case 'PH':
        return 'user-check';
      case 'TL':
        return 'users';
      case 'DAO':
        return 'briefcase';
      default:
        return 'user';
    }
  }

  /**
   * Get CSS class for count circle based on position type
   */
  getPositionCountClass(position: string): string {
    switch (position) {
      case 'PH':
        return 'count-circle-primary';
      case 'TL':
        return 'count-circle-success';
      case 'DAO':
        return 'count-circle-warning';
      default:
        return 'count-circle-secondary';
    }
  }

  /**
   * Get CSS class for progress bar based on position type
   */
  getPositionProgressClass(position: string): string {
    switch (position) {
      case 'PH':
        return 'bg-primary';
      case 'TL':
        return 'bg-success';
      case 'DAO':
        return 'bg-warning';
      default:
        return 'bg-secondary';
    }
  }

  /**
   * Calculate progress width percentage based on count
   * This is just a sample calculation - adjust as needed
   */
  getProgressWidth(count: number): number {
    // For demo purposes, we'll use a max of 20 to calculate percentage
    const maxCount = 20;
    return Math.min(100, (count / maxCount) * 100);
  }

  ngOnInit(): void {
    // Initialize filtered leads
    this.refreshLeads();

    // Subscribe to search term changes
    this.searchTerm.valueChanges.subscribe(() => {
      this.page = 1;
      this.refreshLeads();
    });
  }

  /**
   * Refresh leads based on search and pagination
   */
  refreshLeads() {
    // Filter by search term
    const filtered = search(this.searchTerm.value, this.pipe);

    // Update collection size
    this.collectionSize = filtered.length;

    // Slice for pagination
    this.filteredLeads = filtered.slice(
      (this.page - 1) * this.pageSize,
      (this.page - 1) * this.pageSize + this.pageSize
    );
  }

  /**
   * Helper function for comparing values
   */
  compare(v1: string | number | undefined, v2: string | number | undefined) {
    // Handle undefined values
    if (v1 === undefined && v2 === undefined) return 0;
    if (v1 === undefined) return -1;
    if (v2 === undefined) return 1;

    // Compare defined values
    return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
  }

  /**
   * Handle sorting
   */
  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.refreshLeads();
    } else {
      this.filteredLeads = [...this.filteredLeads].sort((a, b) => {
        const res = this.compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  /**
   * Check if a role is selected for a specific lead
   */
  isRoleSelected(leadId: number, roleId: number): boolean {
    return this.selectedRoles[leadId]?.includes(roleId) || false;
  }

  /**
   * Handle role radio button change event
   */
  onRoleRadioChange(leadId: number, roleId: number, event: Event): void {
    const radio = event.target as HTMLInputElement;

    // For radio buttons, we only need to handle the checked state
    if (radio.checked) {
      // With radio buttons, we can only select one role per lead
      // So we replace the entire array with a single value
      this.selectedRoles[leadId] = [roleId];
    } else {
      // This shouldn't happen with radio buttons, but just in case
      delete this.selectedRoles[leadId];
    }

    // Update the employee dropdown visibility
    this.updateEmployeeDropdownVisibility(leadId);

    // Trigger change detection to update the visibility of Employee and Action columns
    this.cdr.detectChanges();
  }

  /**
   * Handle assign to user checkbox change
   */
  onAssignToUserChange(leadId: number) {
    // Update the employee dropdown visibility
    this.updateEmployeeDropdownVisibility(leadId);
  }

  /**
   * Update the employee dropdown visibility based on role selection
   */
  private updateEmployeeDropdownVisibility(leadId: number): void {
    // Check if any roles are selected for this lead
    const rolesSelected = this.selectedRoles[leadId]?.length > 0;

    // Update the showEmployeeDropdown flag for this lead
    this.showEmployeeDropdown[leadId] = rolesSelected;

    // If no roles are selected, reset the selected employees
    if (!rolesSelected) {
      delete this.selectedEmployees[leadId];
    }
  }

  /**
   * Check if an employee is selected for a specific lead
   */
  isEmployeeSelected(leadId: number, employeeId: number): boolean {
    return this.selectedEmployees[leadId]?.includes(employeeId) || false;
  }

  /**
   * Handle employee checkbox change
   */
  onEmployeeCheckboxChange(leadId: number, employeeId: number, event: Event) {
    const checkbox = event.target as HTMLInputElement;

    // Initialize the array if it doesn't exist
    if (!this.selectedEmployees[leadId]) {
      this.selectedEmployees[leadId] = [];
    }

    // Add or remove the employee based on checkbox state
    if (checkbox.checked) {
      // Add the employee if it's not already in the array
      if (!this.selectedEmployees[leadId].includes(employeeId)) {
        this.selectedEmployees[leadId].push(employeeId);
      }
    } else {
      // Remove the employee
      this.selectedEmployees[leadId] = this.selectedEmployees[leadId].filter(id => id !== employeeId);

      // If no employees are selected, remove the array
      if (this.selectedEmployees[leadId].length === 0) {
        delete this.selectedEmployees[leadId];
      }
    }
  }

  /**
   * Get text to display in the employee dropdown button
   */
  getSelectedEmployeesText(leadId: number): string {
    if (!this.selectedEmployees[leadId] || this.selectedEmployees[leadId].length === 0) {
      return 'Select Employees';
    }

    if (this.selectedEmployees[leadId].length === 1) {
      const employeeId = this.selectedEmployees[leadId][0];
      const employee = this.employees.find(e => e.id === employeeId);
      return employee ? employee.name : 'Select Employees';
    }

    return `${this.selectedEmployees[leadId].length} employees selected`;
  }

  /**
   * Get the CSS class for a status badge
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-success';
      case 'pending':
        return 'bg-warning';
      case 'completed':
        return 'bg-info';
      default:
        return 'bg-secondary';
    }
  }

  /**
   * Check if any role is selected across all leads
   */
  anyRoleSelected(): boolean {
    // Check if any lead has a role selected
    return Object.keys(this.selectedRoles).length > 0;
  }

  /**
   * Check if any employees are selected for a specific lead
   */
  hasSelectedEmployees(leadId: number): boolean {
    return this.selectedEmployees[leadId]?.length > 0 || false;
  }

  /**
   * Submit role and employee assignment for a lead
   */
  submitAssignment(lead: LeadData) {
    // Get the selected roles for this lead
    const selectedRoles: string[] = [];

    // Get the selected roles
    const roleIds = this.selectedRoles[lead.id] || [];
    for (const roleId of roleIds) {
      const role = this.assignToUsers.find(u => u.id === roleId);
      if (role) {
        selectedRoles.push(role.name);
      }
    }

    // Get the selected employees for this lead
    const employeeIds = this.selectedEmployees[lead.id] || [];
    const employeeNames: string[] = [];

    for (const empId of employeeIds) {
      const employee = this.employees.find(e => e.id === empId);
      if (employee) {
        employeeNames.push(employee.name);
      }
    }

    // Create a new assignment
    const assignment = {
      lead: lead,
      roles: selectedRoles,
      employeeIds: employeeIds,
      employeeNames: employeeNames.length > 0 ? employeeNames : ['Not assigned'],
      submittedAt: new Date()
    };

    // Add the assignment to the submitted assignments
    this.submittedAssignments.push(assignment);

    // Reset the selections for this lead
    delete this.selectedRoles[lead.id];
    delete this.selectedEmployees[lead.id];
    this.showEmployeeDropdown[lead.id] = false;

    // Show a success message (you can replace this with a proper notification)
    console.log('Assignment submitted successfully:', assignment);
  }
}
