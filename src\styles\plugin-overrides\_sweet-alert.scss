// Plugin: ngx-sweetalert2
// github: https://github.com/sweetalert2/ngx-sweetalert2

.swal2-container {
  // Ensure SweetAlert2 appears above all other elements
  z-index: 10000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  .swal2-popup {
    font-size: $font-size-base;
    background: var(--#{$prefix}body-bg);
    box-shadow: var(--#{$prefix}box-shadow);
    border: 1px solid var(--#{$prefix}border-color);
    position: relative !important;
    margin: auto !important;

    &.swal2-toast {
      box-shadow: var(--#{$prefix}box-shadow);
      background: var(--#{$prefix}body-bg);
    }

    .swal2-title {
      font-size: 25px;
      line-height: 1;
      font-weight: 500;
      color: var(--#{$prefix}body-color);
      margin-bottom: 0;
    }

    .swal2-html-container {
      font-size: $font-size-base;
      color: $secondary;
      font-weight: initial;
      margin-top: 11px;
      text-decoration: none;
    }

    .swal2-actions {
      button {
        @extend .btn;
        &.swal2-confirm {
          @extend .btn-primary;
          &:focus {
            box-shadow: none;
          }
        }
        &.swal2-cancel {
          @extend .btn-danger;
          @extend .border-danger;
        }
        svg {
          width: 16px;
          height: 16px;
        }
      }
    }

    .swal2-close {
      font-size: 22px;
      &:focus {
        box-shadow: none;
      }
    }

    .swal2-timer-progress-bar {
      background: $secondary;
    }

  }

  // Custom styling for logout confirmation popup
  &.logout-confirmation-popup {
    // Ensure proper positioning for logout popup
    z-index: 10001 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(4px);

    .swal2-popup {
      position: relative !important;
      top: auto !important;
      left: auto !important;
      right: auto !important;
      bottom: auto !important;
      transform: none !important;
      margin: auto !important;
      border-radius: 16px !important;
      padding: 2rem !important;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
      border: none !important;
      background: var(--#{$prefix}body-bg) !important;
      min-width: 420px;
      max-width: 500px;
      animation: slideInScale 0.3s ease-out;
    }

    .swal2-icon {
      margin: 0 auto 1.5rem auto !important;
      border: none !important;

      &.swal2-question {
        border: 3px solid #3F828B !important;
        color: #3F828B !important;
        font-size: 2.5rem !important;
        width: 80px !important;
        height: 80px !important;
        line-height: 74px !important;
        border-radius: 50% !important;
        background: rgba(63, 130, 139, 0.1) !important;
      }
    }

    .swal2-title {
      color: var(--#{$prefix}body-color) !important;
      font-weight: 600 !important;
      font-size: 1.5rem !important;
      margin-bottom: 0.5rem !important;
      line-height: 1.3 !important;
    }

    .swal2-html-container {
      color: #6c757d !important;
      font-size: 1rem !important;
      font-weight: 400 !important;
      margin: 0 0 2rem 0 !important;
      line-height: 1.5 !important;
    }

    .swal2-actions {
      margin: 0 !important;
      gap: 1rem !important;
      justify-content: center !important;
      flex-direction: row !important;

      button {
        border-radius: 8px !important;
        font-weight: 500 !important;
        font-size: 0.95rem !important;
        padding: 0.75rem 2rem !important;
        border: none !important;
        transition: all 0.2s ease !important;
        min-width: 120px !important;
        cursor: pointer !important;

        &.swal2-confirm {
          background: linear-gradient(135deg, #dc3545, #c82333) !important;
          color: #fff !important;
          box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;

          &:hover {
            background: linear-gradient(135deg, #c82333, #bd2130) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4) !important;
          }

          &:active {
            transform: translateY(0) !important;
          }
        }

        &.swal2-cancel {
          background: #f8f9fa !important;
          color: #6c757d !important;
          border: 2px solid #e9ecef !important;

          &:hover {
            background: #e9ecef !important;
            color: #495057 !important;
            border-color: #dee2e6 !important;
            transform: translateY(-1px) !important;
          }

          &:active {
            transform: translateY(0) !important;
          }
        }
      }
    }

    .swal2-close {
      display: none !important;
    }
  }
}

// RTL fix
body.swal2-toast-shown .swal2-container.swal2-rtl {
  /*rtl:raw:
  right: auto !important;
  left: 0 !important;
  */
}

// Additional fixes for SweetAlert2 positioning issues
body.swal2-shown {
  overflow: hidden !important;

  .swal2-container {
    z-index: 10000 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    &.swal2-backdrop-show {
      background-color: rgba(0, 0, 0, 0.4) !important;
    }
  }
}

// Ensure proper cleanup when popup is dismissed
body:not(.swal2-shown) {
  overflow: auto !important;

  .swal2-container {
    display: none !important;
  }
}

// Force remove any lingering containers
.swal2-container:not(.swal2-backdrop-show) {
  display: none !important;
}

// Ensure SweetAlert2 works properly with the application layout
.swal2-popup {
  max-width: 500px !important;
  width: auto !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  margin: auto !important;
}

// Animation for smooth popup entrance
@keyframes slideInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Backdrop animation
@keyframes fadeInBackdrop {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

// Enhanced backdrop styling
.swal2-container.logout-confirmation-popup {
  animation: fadeInBackdrop 0.3s ease-out;
}

// Responsive design for mobile devices
@media (max-width: 576px) {
  .swal2-container.logout-confirmation-popup {
    .swal2-popup {
      min-width: 320px !important;
      max-width: 90vw !important;
      margin: 1rem !important;
      padding: 1.5rem !important;
    }

    .swal2-icon {
      &.swal2-question {
        width: 60px !important;
        height: 60px !important;
        line-height: 54px !important;
        font-size: 2rem !important;
      }
    }

    .swal2-title {
      font-size: 1.25rem !important;
    }

    .swal2-actions {
      flex-direction: column !important;
      gap: 0.75rem !important;

      button {
        width: 100% !important;
        min-width: auto !important;
      }
    }
  }
}

// Additional custom classes for better styling
.logout-popup-title {
  text-align: center !important;
}

.logout-popup-content {
  text-align: center !important;
}

.logout-popup-actions {
  justify-content: center !important;
  gap: 1rem !important;
}

.logout-confirm-btn {
  order: 2 !important;
}

.logout-cancel-btn {
  order: 1 !important;
}

// Focus states for better accessibility
.swal2-container.logout-confirmation-popup {
  .swal2-actions button {
    &:focus {
      outline: 2px solid #3F828B !important;
      outline-offset: 2px !important;
    }

    &.swal2-confirm:focus {
      outline-color: #dc3545 !important;
    }
  }
}

// Loading popup styling
.swal2-container.logout-loading-popup {
  z-index: 10002 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(6px);

  .swal2-popup {
    border-radius: 16px !important;
    padding: 2rem !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4) !important;
    border: none !important;
    background: var(--#{$prefix}body-bg) !important;
    min-width: 350px;
    text-align: center;

    .swal2-title {
      color: var(--#{$prefix}body-color) !important;
      font-weight: 600 !important;
      font-size: 1.25rem !important;
      margin-bottom: 1rem !important;
    }

    .swal2-html-container {
      color: #6c757d !important;
      font-size: 0.95rem !important;
      margin-bottom: 1.5rem !important;
    }

    .swal2-loader {
      border-color: #3F828B transparent #3F828B transparent !important;
      width: 40px !important;
      height: 40px !important;
    }
  }
}

// Animation for smooth popup entrance
@keyframes slideInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Backdrop animation
@keyframes fadeInBackdrop {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

// Enhanced backdrop styling
.swal2-container.logout-confirmation-popup {
  animation: fadeInBackdrop 0.3s ease-out;
}
