import { Injectable, Injector } from '@angular/core';
import { HttpRe<PERSON>, Http<PERSON><PERSON><PERSON>, HttpEvent, HttpInterceptor, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, of } from 'rxjs';
import { catchError, filter, take, switchMap, delay } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  private authService: AuthService | null = null;

  constructor(private injector: Injector) {}

  // Get authService lazily to avoid circular dependency
  private getAuthService(): AuthService {
    if (!this.authService) {
      this.authService = this.injector.get(AuthService);
    }
    return this.authService;
  }  intercept(request: HttpRequest<unknown>, next: <PERSON>tt<PERSON><PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    // 🚨 TEMPORARY BYPASS: Add mock auth token for development
    console.log('🚨 AUTH INTERCEPTOR - Adding mock token for development:', request.url);

    // Skip auth endpoints to avoid conflicts
    if (request.url.includes('/auth/login') || request.url.includes('/auth/refresh-token') || request.url.includes('/auth/register')) {
      return next.handle(request);
    }

    // Try to get real token from localStorage or use mock
    let authToken = 'Bearer mock-development-token-12345';

    // Check if there's a real user token in localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        if (user && user.access_token) {
          authToken = `Bearer ${user.access_token}`;
          console.log('🔑 Using real token from localStorage');
        }
      } catch (e) {
        console.warn('⚠️ Failed to parse stored user, using mock token');
      }
    }

    // Add authorization header for development
    const authRequest = request.clone({
      setHeaders: {
        'Authorization': authToken,
        'Content-Type': request.headers.get('Content-Type') || 'application/json',
        'Accept': 'application/json'
      }
    });

    return next.handle(authRequest);

    /* Original auth interceptor logic (commented out for development)
    // Don't set Content-Type for FormData (file uploads) - let browser set it automatically
    // Only add content-type headers if not already set and not FormData
    if (!request.headers.has('Content-Type') && !(request.body instanceof FormData)) {
      request = request.clone({
        setHeaders: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
          // ❌ REMOVED: CORS headers should NEVER be set by the client
          // These headers should only be set by the server in the response
        }
      });
    } else if (request.body instanceof FormData) {
      // For FormData, only set Accept header
      console.log('AuthInterceptor: Detected FormData upload, not setting Content-Type');
      request = request.clone({
        setHeaders: {
          'Accept': 'application/json'
          // ❌ REMOVED: CORS headers should NEVER be set by the client
        }
      });
    }

    // Skip authentication for auth endpoints to avoid infinite loops
    if (request.url.includes('/auth/login') || request.url.includes('/auth/refresh-token') || request.url.includes('/auth/register')) {
      return next.handle(request);
    }

    // Get the current user with real token
    const currentUser = this.getAuthService().currentUserValue;

    // Check if token is expired before making the request
    if (currentUser && this.getAuthService().isTokenExpired()) {
      console.log('Token is expired, attempting refresh before request');
      // Don't add expired token, let the 401 handler deal with it
    } else if (currentUser && currentUser.access_token) {
      console.log('Adding valid token to request:', request.url);
      request = this.addToken(request, currentUser.access_token);
    } else {
      console.warn('No access token available for request:', request.url);
    }

    // Send cloned request with header to the next handler
    return next.handle(request).pipe(
      catchError(error => {
        if (error instanceof HttpErrorResponse && error.status === 401) {
          // Handle 401 Unauthorized errors - token may be expired
          return this.handle401Error(request, next);
        }
        return throwError(() => error);
      })
    );
    */
  }private addToken(request: HttpRequest<any>, token: string): HttpRequest<any> {
    // Only add Authorization header, don't touch other headers
    return request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
  }  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.getAuthService().refreshToken().pipe(
        switchMap(token => {
          this.isRefreshing = false;

          if (!token) {
            // If refreshToken() returns null, the user should be logged out
            console.error('Token refresh failed - user will be logged out');
            this.getAuthService().logout(false, 'Your session has expired. Please log in again.');
            return throwError(() => new Error('Authentication failed after token refresh'));
          }

          this.refreshTokenSubject.next(token.access_token);
          return next.handle(this.addToken(request, token.access_token));
        }),
        catchError(error => {
          this.isRefreshing = false;

          // If refresh token fails, log out the user
          console.error('Error refreshing token:', error);
          this.getAuthService().logout(false, 'Your session has expired. Please log in again.');
          return throwError(() => error);
        })
      );
    } else {
      // Wait for token to be refreshed
      return this.refreshTokenSubject.pipe(
        filter(token => token !== null),
        take(1),
        switchMap(token => {
          return next.handle(this.addToken(request, token));
        })
      );
    }
  }
}
