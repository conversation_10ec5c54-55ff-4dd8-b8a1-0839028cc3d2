import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { MenuItem } from '../../views/layout/sidebar/menu.model';
import { MENU } from '../../views/layout/sidebar/menu';

@Injectable({
  providedIn: 'root'
})
export class DynamicMenuService {

  constructor(
    private authService: AuthService
  ) {}

  /**
   * Get filtered menu items based on user permissions and roles
   */
  getFilteredMenu(): MenuItem[] {
    const user = this.authService.currentUserValue;

    if (!user) {
      console.log('🧭 DynamicMenu: No user found, returning empty menu');
      return [];
    }

    console.log('🧭 DynamicMenu: Filtering menu for user:', user.email, 'Role:', user.role);

    // Use permission-based filtering directly from AuthService
    const filteredMenu = this.filterMenuItems(MENU, user);
    console.log('🧭 DynamicMenu: Filtered menu items:', filteredMenu.length);

    return filteredMenu;
  }

  /**
   * Get basic menu for when role info is not available
   */
  private getBasicMenu(): MenuItem[] {
    return MENU.filter(item =>
      item.isTitle ||
      item.label === 'Dashboard' ||
      item.label === 'LMS'
    );
  }



  /**
   * Recursively filter menu items based on permissions
   */
  private filterMenuItems(menuItems: MenuItem[], user: any): MenuItem[] {
    return menuItems.filter(item => {
      // Always show title items
      if (item.isTitle) {
        return true;
      }

      // Check if user has permission for this menu item
      if (this.hasMenuPermission(item, user)) {
        // If item has sub-items, filter them recursively
        if (item.subItems && item.subItems.length > 0) {
          const filteredSubItems = this.filterMenuItems(item.subItems, user);

          // Only show parent if it has visible sub-items
          if (filteredSubItems.length > 0) {
            return {
              ...item,
              subItems: filteredSubItems
            };
          }
          return false;
        }
        return true;
      }

      return false;
    }).map(item => {
      // Ensure we return the filtered sub-items
      if (item.subItems && item.subItems.length > 0) {
        return {
          ...item,
          subItems: this.filterMenuItems(item.subItems, user)
        };
      }
      return item;
    });
  }

  /**
   * Check if user has permission to see a menu item
   */
  private hasMenuPermission(item: MenuItem, user: any): boolean {
    // Superuser has access to everything
    if (user.is_superuser === true || user.permissions?.includes('*')) {
      return true;
    }

    // Define menu item permissions
    const menuPermissions = this.getMenuPermissions();
    const itemKey = this.getMenuItemKey(item);

    if (menuPermissions[itemKey]) {
      const requiredPermissions = menuPermissions[itemKey];

      // Check if user has any of the required permissions
      const hasPermission = requiredPermissions.some(permission =>
        this.authService.hasPermission(permission) ||
        user.permissions?.includes(permission)
      );

      if (hasPermission) {
        console.log(`✅ Menu access granted: ${item.label} (${itemKey})`);
        return true;
      } else {
        console.log(`❌ Menu access denied: ${item.label} (${itemKey}) - Missing permissions:`, requiredPermissions);
        return false;
      }
    }

    // Default role-based access for items without specific permissions
    return this.hasRoleBasedAccess(item, user);
  }

  /**
   * Get menu item key for permission mapping
   */
  private getMenuItemKey(item: MenuItem): string {
    if (item.link) {
      return item.link.replace('/', '').replace(/\//g, '_');
    }
    return (item.label || '').toLowerCase().replace(/\s+/g, '_');
  }

  /**
   * Define permissions required for each menu item
   */
  private getMenuPermissions(): { [key: string]: string[] } {
    return {
      // Main navigation
      'dashboard': [], // Available to all authenticated users
      'sales-list': ['sales:read', 'sales:manage'],
      'admin-lead-list': ['admin:access'],

      // Ops Team - Temporarily disabled for testing
      'bucket': [], // ['ops:access'],
      'login-at-institute': [], // ['ops:access'],
      'disbursement': [], // ['ops:access'],

      // LMS
      'lms_dashboard': [], // Available to all authenticated users
      'lms_apply-leave': ['leave:create'],
      'lms_approve-leaves': ['leave:approve'],
      'lms_leave-policy': ['leave:read'],
      'lms_assign-comp-off': ['leave:assign_compoff'],
      'lms_comp-off-request': ['leave:request_compoff'],
      'lms_attendance': ['attendance:read'],
      'lms_calendar': ['calendar:read', 'calendar:employee'],
      'lms_salary-slip': ['salary:read'],
      'lms_generate-salary-slip': ['salary:generate'],

      // Role & Permission Management
      'role-permission-management_roles': ['roles:read', 'roles:create', 'roles:update', 'roles:delete'],
      'role-permission-management_permissions': ['permissions:read'],

      // Master
      'master_profession': ['master:read', 'profession:read'],
      'master_associate': ['master:read', 'associates:read'],


    };
  }

  /**
   * Role-based access for items without specific permissions
   */
  private hasRoleBasedAccess(item: MenuItem, user: any): boolean {
    const userRole = user.role;

    // If no role is assigned, deny access
    if (!userRole) {
      console.warn('⚠️ No role assigned to user - denying access to:', item.label);
      return false;
    }

    // Admin has access to everything
    if (userRole === 'admin' || user.is_superuser === true) {
      return true;
    }

    // Manager access
    if (userRole === 'manager') {
      const managerAllowedItems = [
        'Dashboard', 'LMS', 'Lms', 'Attendance', 'Approve Leaves', 'Assign Comp-off'
      ];
      return managerAllowedItems.includes(item.label || '');
    }

    // For any other role from API, allow basic access to Dashboard and LMS
    const basicAllowedItems = [
      'Dashboard', 'LMS', 'Lms'
    ];

    if (basicAllowedItems.includes(item.label || '')) {
      return true;
    }

    // For other items, deny access unless user has specific permissions
    console.warn('⚠️ Role-based access denied for role:', userRole, 'item:', item.label);
    return false;
  }

  /**
   * Check if a specific route should be visible in menu
   */
  isRouteVisible(route: string): boolean {
    const user = this.authService.currentUserValue;
    if (!user) return false;

    const menuPermissions = this.getMenuPermissions();
    const routeKey = route.replace('/', '').replace(/\//g, '_');

    if (menuPermissions[routeKey]) {
      const requiredPermissions = menuPermissions[routeKey];
      return requiredPermissions.some(permission =>
        this.authService.hasPermission(permission)
      );
    }

    return true; // Default to visible if no specific permissions defined
  }

  /**
   * Get menu items count for debugging
   */
  getMenuStats(): { total: number; visible: number; hidden: number } {
    const total = this.countMenuItems(MENU);
    const filtered = this.getFilteredMenu();
    const visible = this.countMenuItems(filtered);

    return {
      total,
      visible,
      hidden: total - visible
    };
  }

  /**
   * Count total menu items recursively
   */
  private countMenuItems(items: MenuItem[]): number {
    let count = 0;
    for (const item of items) {
      if (!item.isTitle) {
        count++;
        if (item.subItems) {
          count += this.countMenuItems(item.subItems);
        }
      }
    }
    return count;
  }
}
