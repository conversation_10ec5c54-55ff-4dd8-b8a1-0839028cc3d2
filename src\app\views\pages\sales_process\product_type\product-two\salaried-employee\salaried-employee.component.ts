import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, OnChanges, SimpleChanges, OnDestroy, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgLabelTemplateDirective, NgOptionTemplateDirective, NgSelectComponent } from '@ng-select/ng-select';
import { FeatherIconDirective } from '../../../../../../core/feather-icon/feather-icon.directive';
import { SelfEmployeeComponent } from '../self-employee/self-employee.component';

// Static counter to detect multiple instances
let instanceCounter = 0;

@Component({
  selector: 'app-salaried-employee',
  templateUrl: './salaried-employee.component.html',
  styleUrls: ['./salaried-employee.component.scss'],
  imports: [ReactiveFormsModule, FormsModule, CommonModule, FeatherIconDirective, SelfEmployeeComponent, NgSelectComponent, NgOptionTemplateDirective, NgLabelTemplateDirective],
  standalone: true
})
export class SalariedEmployeeComponent implements OnInit, OnChanges, OnDestroy {
  @Input() formType: 'HL' | 'LAP_LRD' = 'HL';
  @Input() selectedProductSubType: string = '';
  @Input() isCoApplicantForm: boolean = false;

  // Add properties to track initialization state
  private initialized = false;
  private initializedProductType: string | null = null;
  private instanceId: number;
  private firstValidType: string | null = null;

  // Form and submission state
  salariedForm!: FormGroup;
  isFormSubmitted = false;

  // Co-Applicant Salaried Form
  coApplicantSalariedForm!: FormGroup;
  isCoAppFormSubmitted = false;
  currentCoApplicantIndex: number = -1;

  // Store detailed co-applicant data for each co-applicant
  coApplicantDetailedData: { [index: number]: any } = {};

  // ViewChild for the co-applicant form modal
  @ViewChild('salariedModal') salariedModal: any;
  @ViewChild('selfEmployedModal') selfEmployedModal: any;

  // ViewChild for the self-employee component
  @ViewChild(SelfEmployeeComponent) selfEmployeeComponent: SelfEmployeeComponent;

  // Dropdown options arrays
  jobProfiles = ['Govt', 'Semi Govt.', 'Private', 'LLP', 'One Person Company', 'Partnership', 'Proprietorship'];
  documentOptions = ['ID Card', 'Business Card', 'Appointment Letter', 'Confirmation Letter', 'Salary Slip', 'Salary Certificate', 'Other'];
  employmentDocumentOptions = ['Appointment Letter', 'Confirmation Letter'];
  salaryDetailsOptions = ['Bank Statement', 'Salary Slip', 'Both'];
  salaryDocumentOptions = ['Salary Slip', 'Salary Certificate'];
  salaryModes = ['Cash', 'Cheque', 'Bank Transfer'];
  yesNoOptions = ['Yes', 'No'];
  itrDocumentsOptions = ['ITR', 'Form 16', 'Both', 'NA'];
  propertyPurchasedOptions = ['Resale', 'Developer'];
  propertyTypeOptions = ['Bunglow', 'NA Plot + Construction', 'Duplex', 'Flat', 'Shop', 'Office'];
  configurationOptions = ['1RK', '1BHK', '1.5 BHK', '2BHK', '2.5 BHK', '3BHK', '3.5BHK', '4BHK'];
  propertyAgeOptions = Array.from({length: 40}, (_, i) => (i + 1).toString());
  approvalAuthorityOptions = ['Grampanchayat', 'Town Planning', 'MMRDA'];
  chainAgreementOptions = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];
  agreementStatusOptions = ['Done', 'Not Done'];
  btOptions = ['Yes', 'No'];
  loanTypes = [
    'Personal Loan',
    'Business Loan',
    'Gold Loan',
    'Vehicle Loan',
    'Consumer Loan',
    'Home Loan',
    'Loan Against Property',
    'Lease Rental Discounting',
    'Overdraft',
    'Cash Credit',
    'Employer Society Loan',
    'Loan Against Security'
  ];
  investmentProducts = ['FD', 'RD', 'MIS', 'LIP', 'MF'];
  investmentModes = ['Monthly', 'Quarterly', 'Half-yearly', 'Yearly'];
  relationshipTypes = ['Spouse', 'Parent', 'Child', 'Sibling', 'Other'];
  occupationTypes = ['Salaried Employee', 'Self Employed'];

  // Additional dropdown options for LAP/LRD form
  residentialStatuses = ['Owned', 'Rental', 'Parental'];
  propertyOwnershipOptions = ['Self Owned', 'Joint Ownership', 'Family Owned'];
  ocOptions = ['Available', 'Not Available', 'In Process'];
  jobProfileOptions = ['Govt', 'Semi Govt.', 'Private', 'LLP', 'One Person Company', 'Partnership', 'Proprietorship'];

  // Documents Status options for ng-select with checkboxes
  documentsStatusOptions = [
    { id: 'cc', name: 'CC' },
    { id: 'oc', name: 'OC' },
    { id: 'socRegistrationCertificate', name: 'SoC Registration Certificate' },
    { id: 'shareCertificate', name: 'Share Certificate' }
  ];
  selectedDocumentsStatus: string[] = [];

  constructor(
    private fb: FormBuilder,
    private modalService: NgbModal
  ) {
    this.instanceId = ++instanceCounter;
    console.log(`Constructor called for instance #${this.instanceId}`);
  }

  ngOnChanges(changes: SimpleChanges) {
    console.log(`[Instance #${this.instanceId}] ngOnChanges called with:`, changes);

    if (changes['selectedProductSubType']) {
      const newValue = changes['selectedProductSubType'].currentValue;
      const prevValue = changes['selectedProductSubType'].previousValue;

      console.log(`[Instance #${this.instanceId}] Product sub-type change detected: ${prevValue} -> ${newValue}`);

      // Store the first valid product type we receive
      if (newValue && !this.firstValidType) {
        this.firstValidType = newValue;
        console.log(`[Instance #${this.instanceId}] First valid product type set to: ${this.firstValidType}`);
      }

      // Only reinitialize if we haven't initialized yet, or if we're explicitly asked to reinitialize
      if (!this.initialized) {
        console.log(`[Instance #${this.instanceId}] Component not yet initialized, will initialize in ngOnInit`);
      } else if (newValue && newValue !== prevValue) {
        console.log(`[Instance #${this.instanceId}] Component already initialized with ${this.initializedProductType}, requested type: ${newValue}`);

        // Only reinitialize if the product types are different subgroups (HL vs LAP/LRD)
        const currentIsLapLrd = this.initializedProductType === 'LAP' || this.initializedProductType === 'LRD';
        const newIsLapLrd = newValue === 'LAP' || newValue === 'LRD';

        if (currentIsLapLrd !== newIsLapLrd) {
          console.log(`[Instance #${this.instanceId}] Re-initializing form because product group changed`);
          this.initializeFormBasedOnProductType(newValue);
          this.initializedProductType = newValue;
        } else {
          console.log(`[Instance #${this.instanceId}] Not re-initializing form because product group is the same`);
        }
      }
    }
  }

  ngOnInit(): void {
    // Decide which product type to use, prioritizing:
    // 1. First valid type we received (if any)
    // 2. Current selectedProductSubType (if valid)
    // 3. Default to 'HL'
    let initialType = this.firstValidType || this.selectedProductSubType || 'HL';
    console.log(`[Instance #${this.instanceId}] ngOnInit called, current product sub-type: ${this.selectedProductSubType}, using: ${initialType}`);

    if (!this.initialized) {
      console.log(`[Instance #${this.instanceId}] Initializing component with product type: ${initialType}`);
      this.initializeFormBasedOnProductType(initialType);
      this.setupFormValueChanges();
      this.initialized = true;
      this.initializedProductType = initialType;
    } else {
      console.log(`[Instance #${this.instanceId}] Component already initialized, skipping initialization`);
    }
  }

  ngOnDestroy(): void {
    console.log(`[Instance #${this.instanceId}] Component being destroyed`);
    // Clean up any subscriptions here if needed
  }

  private initializeFormBasedOnProductType(productType: string) {
    // Store this value as the initialized type
    this.initializedProductType = productType;

    // Always use a non-empty value
    const effectiveType = productType || 'HL';
    console.log(`[Instance #${this.instanceId}] Initializing form based on product type: ${effectiveType}`);

    // Determine which form to initialize based on product type
    if (effectiveType === 'LAP' || effectiveType === 'LRD') {
      console.log(`[Instance #${this.instanceId}] Initializing LAP/LRD Form`);
      this.initLapLrdForm();
    } else {
      console.log(`[Instance #${this.instanceId}] Initializing Home Loan Form`);
      this.initHLSalariedForm();
    }

    // Log detailed form structure after initialization
    console.log(`[Instance #${this.instanceId}] Form initialized with product type:`, effectiveType);
  }

  // Form array getters
  get bankAccountsArray() {
    return this.salariedForm.get('bankAccounts') as FormArray;
  }

  get loanDetailsArray() {
    return this.salariedForm.get('loanDetails') as FormArray;
  }

  get investmentsArray() {
    return this.salariedForm.get('investments') as FormArray;
  }

  get coApplicantsArray() {
    return this.salariedForm.get('coApplicants') as FormArray;
  }

  // Helper getter for product type
  get currentProductType(): string {
    return this.initializedProductType || 'HL';
  }

  // Form initialization methods
  private initHLSalariedForm(): void {
    this.salariedForm = this.fb.group({
      // Personal Information
      age: [null, [Validators.required, Validators.min(18), Validators.max(100)]],
      residentialStatus: ['', Validators.required],

      // Job Information
      jobProfile: ['', Validators.required],
      companyName: ['', Validators.required],
      currentJobExperience: [null, Validators.required],
      totalPastJobExperience: [null, Validators.required],
      officeDocument: ['', Validators.required],

      // Salary Information
      salaryDetails: ['', Validators.required],
      totalGrossSalary: [null, Validators.required],
      deductions: this.fb.group({
        pf: [0],
        pt: [0],
        hra: [0],
        esic: [0],
        employeeLoan: [0],
        societyLoan: [0],
        other: [0]
      }),
      totalDeduction: [0],
      netSalary: [0],
      salaryMode: ['', Validators.required],

      // ITR Information
      itrAvailable: ['', Validators.required],

      // Bank Accounts
      bankAccounts: this.fb.array([
        this.createBankAccount('Primary'),
        this.createBankAccount('Salary')
      ]),

      // Property Information
      propertyPurchased: ['', this.selectedProductSubType === 'LAP' ? [] : Validators.required],
      location: ['', Validators.required],
      typeOfProperty: ['', Validators.required],
      configuration: [''],

      // Area Information
      areaSqFeet: [null, Validators.required],
      salable: [null, Validators.required],
      carpet: [null, Validators.required],
      ageOfProperty: [''],

      // Developer Purchase Fields
      constructionStage: [null],
      approvalAuthority: [''],
      documentsStatus: [[]],
      apfFrom: [''],

      // Additional Property Fields
      noOfChainAgreement: [''],
      agreementStatus: [''],
      currentMV: [null, Validators.required],
      agreementValue: [null, Validators.required],
      loanAmtRequired: [null, Validators.required],
      ocrRequired: [null], // Auto calculated
      ocrPercentage: [null], // Auto calculated
      ltv: [null], // For LAP/LRD

      // BT Fields
      ifBt: [''],
      bankName: [''],
      currentOutstandingLoanAmount: [null],

      // Arrays for dynamic data
      loanDetails: this.fb.array([]),
      investments: this.fb.array([]),
      coApplicants: this.fb.array([]),
    });
  }

  private initLapLrdForm(): void {
    this.salariedForm = this.fb.group({
      // Personal Information
      age: [null, [Validators.required, Validators.min(18), Validators.max(100)]],
      propertyOwnership: ['', Validators.required],

      // Job Information
      jobProfile: ['', Validators.required], // Private/Government/Semi Government/Proprietorship Company
      companyName: ['', Validators.required],
      currentJobExperience: [null, Validators.required],
      totalPastJobExperience: [null, Validators.required],
      officeDocument: ['', Validators.required], // Appointment Letter/Confirmation Letter/Salary Slip/Certificate

      // Salary Information
      salaryDetails: ['', Validators.required],
      totalGrossSalary: [null, Validators.required],
      deductions: this.fb.group({
        pf: [0],
        pt: [0],
        hra: [0],
        esic: [0],
        employeeLoan: [0],
        societyLoan: [0],
        other: [0]
      }),
      totalDeduction: [0],
      netSalary: [0],
      salaryMode: ['', Validators.required],

      // ITR Information
      itrAvailable: ['', Validators.required],

      // Bank Accounts
      bankAccounts: this.fb.array([
        this.createBankAccount('Primary'),
        this.createBankAccount('Salary')
      ]),

      // Property Information - LAP/LRD specific
      propertyPurchased: ['', this.selectedProductSubType === 'LAP' ? [] : Validators.required],
      location: ['', Validators.required],
      typeOfProperty: ['', Validators.required],
      configuration: [''],

      // Area Information
      areaSqFeet: [null, Validators.required],
      salable: [null, Validators.required],
      carpet: [null, Validators.required],
      ageOfProperty: [''],

      // Developer Purchase Fields
      constructionStage: [null],
      approvalAuthority: [''],
      documentsStatus: [[]],
      apfFrom: [''],

      // Additional Property Fields
      noOfChainAgreement: [''],
      agreementStatus: [''],
      currentMV: [null, Validators.required],
      loanAmtRequired: [null, Validators.required],
      ocrRequired: [null], // Auto calculated
      ocrPercentage: [null], // Auto calculated
      ltv: [null], // For LAP/LRD

      // BT Fields
      ifBt: [''],
      bankName: [''],
      currentOutstandingLoanAmount: [null],

      // LAP/LRD Specific Fields
      // LRD Specific Fields
      loiDetails: [''],
      companyNameLicensee: [''],
      leaseTenor: [''],
      startDate: [''],
      monthlyRent: [null],

      // Arrays for dynamic data
      loanDetails: this.fb.array([]),
      investments: this.fb.array([]),
      coApplicants: this.fb.array([]),
    });
  }

  // Helper methods for creating form group templates
  private createBankAccount(type: string) {
    return this.fb.group({
      accountType: [type],
      bankName: [''],
    });
  }

  // Form array manipulation methods
  addLoanDetail() {
    const loanDetail = this.fb.group({
      loanType: [''],
      bankName: [''],
      sanctionedAmount: [null],
      disbursedAmount: [null],
      outstandingAmount: [null],
      startDate: [null],
      tenure: [null],
      emiAmount: [null],
      default: ['No'],
      example: [''], // New field for example
    });
    this.loanDetailsArray.push(loanDetail);
  }

  removeLoanDetail(index: number) {
    this.loanDetailsArray.removeAt(index);
  }

  // Check if any loan has default = "Yes" to show/hide Example column
  hasAnyLoanWithDefault(): boolean {
    return this.loanDetailsArray.controls.some(control =>
      control.get('default')?.value === 'Yes'
    );
  }

  addInvestment() {
    const investment = this.fb.group({
      instituteName: [''],
      investmentProduct: [''],
      yearlyAmount: [null],
      investmentMode: [''],
      startDate: [null],
      endDate: [null],
      currentSavingAmount: [null],
    });
    this.investmentsArray.push(investment);
  }

  removeInvestment(index: number) {
    this.investmentsArray.removeAt(index);
  }

  addCoApplicant() {
    const coApplicant = this.fb.group({
      name: [''],
      relation: [''],
      age: [null],
      occupation: [''],
      incomeSource: [''],
      monthlyIncome: [null],
    });
    this.coApplicantsArray.push(coApplicant);
  }

  removeCoApplicant(index: number) {
    this.coApplicantsArray.removeAt(index);
  }

  // Form value calculation
  private setupFormValueChanges() {
    const deductionsGroup = this.salariedForm.get('deductions');
    if (deductionsGroup) {
      deductionsGroup.valueChanges.subscribe(() => {
        this.calculateTotals();
      });
    }

    this.salariedForm.get('totalGrossSalary')?.valueChanges.subscribe(() => {
      this.calculateTotals();
    });

    // Setup auto-calculations for property fields
    this.salariedForm.get('agreementValue')?.valueChanges.subscribe(() => {
      this.calculatePropertyValues();
    });

    this.salariedForm.get('currentMV')?.valueChanges.subscribe(() => {
      this.calculatePropertyValues();
    });
  }

  private calculateTotals() {
    const deductionsValues = this.salariedForm.get('deductions')?.value || {};
    const totalDeduction: number = Object.values(deductionsValues).reduce((sum: number, val: any) => sum + (+val || 0), 0) as number;
    const grossSalary: number = +(this.salariedForm.get('totalGrossSalary')?.value || 0);

    this.salariedForm.patchValue(
      {
        totalDeduction: totalDeduction,
        netSalary: grossSalary - totalDeduction,
      },
      { emitEvent: false }
    );
  }

  private calculatePropertyValues() {
    const agreementValue = +(this.salariedForm.get('agreementValue')?.value || 0);
    const loanAmtRequired = +(this.salariedForm.get('loanAmtRequired')?.value || 0);

    // Calculate OCR Required (Agreement Value - Loan Amount Required)
    const ocrRequired = agreementValue - loanAmtRequired;

    // Calculate OCR % (OCR Required / Agreement Value * 100)
    let ocrPercentage = 0;
    if (agreementValue > 0) {
      ocrPercentage = (ocrRequired / agreementValue) * 100;
    }

    this.salariedForm.patchValue(
      {
        ocrRequired: ocrRequired > 0 ? ocrRequired : 0,
        ocrPercentage: ocrPercentage > 0 ? parseFloat(ocrPercentage.toFixed(2)) : 0
      },
      { emitEvent: false }
    );
  }

  // Modal methods
  onCoApplicantOccupationChange(index: number, occupation: string, modal: any) {
    console.log('Co-applicant occupation changed to:', occupation);
    console.log('Current product type:', this.currentProductType);

    this.currentCoApplicantIndex = index;

    // Check if this co-applicant already has detailed data
    const hasExistingDetailedData = this.coApplicantDetailedData[index];
    console.log('Existing detailed data for co-applicant', index, ':', hasExistingDetailedData);

    if (occupation === 'Salaried Employee') {
      // Initialize the salaried form with the current product type
      this.initCoApplicantSalariedForm();

      // If there's existing detailed data, populate the form
      if (hasExistingDetailedData) {
        console.log('Populating salaried form with existing data');
        this.populateCoApplicantFormWithStoredData(hasExistingDetailedData);
      }

      // Open the modal
      this.modalService.open(this.salariedModal, {
        centered: true,
        size: 'xl',
        backdrop: 'static'
      });
    } else if (occupation === 'Self Employed') {
      // For Self Employed, we need to pass the current product type to the modal
      // so it can render the correct form
      const modalRef = this.modalService.open(modal, {
        centered: true,
        size: 'xl',
        backdrop: 'static'
      });

      // After the modal is opened, we need to set the product type on the self-employee component
      modalRef.shown.subscribe(() => {
        // Find the self-employee component in the modal
        const selfEmployeeComp = document.querySelector('app-self-employee');
        if (selfEmployeeComp) {
          console.log('Found self-employee component in modal');

          // Wait for Angular to initialize the component
          setTimeout(() => {
            // Get the component instance
            if (this.selfEmployeeComponent) {
              console.log('Setting product type on self-employee component:', this.currentProductType);

              // Set the current co-applicant index
              this.selfEmployeeComponent.currentCoApplicantIndex = this.currentCoApplicantIndex;

              // IMPORTANT: Pass the stored data to the self-employee component
              if (hasExistingDetailedData) {
                // Ensure the self-employee component has the detailed data
                if (!this.selfEmployeeComponent.coApplicantDetailedData) {
                  this.selfEmployeeComponent.coApplicantDetailedData = {};
                }
                this.selfEmployeeComponent.coApplicantDetailedData[this.currentCoApplicantIndex] = hasExistingDetailedData;
                console.log('📤 Transferred detailed data to self-employee component for index:', this.currentCoApplicantIndex);
              }

              // Set the product type
              this.selfEmployeeComponent.setProductType(this.currentProductType);

              // Initialize the co-applicant self-employed form
              this.selfEmployeeComponent.initCoApplicantSelfEmployedForm();

              // If there's existing detailed data, populate the form
              if (hasExistingDetailedData) {
                console.log('🔄 Populating self-employed form with existing data:', hasExistingDetailedData);
                // Add a small delay to ensure form is fully initialized
                setTimeout(() => {
                  this.selfEmployeeComponent.populateCoApplicantSelfEmployedFormWithStoredData(hasExistingDetailedData);
                  console.log('✅ Self-employed form populated with stored data');
                }, 300);
              }

              // Force update the form
              this.selfEmployeeComponent.forceUpdateFormType();

              // Sync product type
              if (typeof this.selfEmployeeComponent.syncProductType === 'function') {
                this.selfEmployeeComponent.syncProductType();
              }
            } else {
              console.warn('Self-employee component not found');
            }
          }, 100);
        } else {
          console.warn('Self-employee component not found in modal');
        }
      });
    }
  }

  // Initialize Co-Applicant Salaried Form
  initCoApplicantSalariedForm() {
    // Check if we're editing an existing co-applicant
    const isEditing = this.currentCoApplicantIndex >= 0 && this.currentCoApplicantIndex < this.coApplicantsArray.length;
    const coApplicant = isEditing ? this.coApplicantsArray.at(this.currentCoApplicantIndex) : null;

    // Get stored detailed data for this co-applicant if it exists
    const storedDetailedData = isEditing ? this.coApplicantDetailedData[this.currentCoApplicantIndex] : null;

    // Try to extract job profile and company name from income source if it exists
    let jobProfile = '';
    let companyName = '';

    if (isEditing) {
      if (storedDetailedData) {
        // Use stored detailed data if available
        jobProfile = storedDetailedData.jobProfile || '';
        companyName = storedDetailedData.companyName || '';
      } else {
        // Fallback to parsing income source
        const incomeSource = coApplicant?.get('incomeSource')?.value || '';
        if (incomeSource) {
          // Try to parse "Company Name (Job Profile)" format
          const match = incomeSource.match(/(.+) \((.+)\)/);
          if (match && match.length === 3) {
            companyName = match[1];
            jobProfile = match[2];
          }
        }
      }
    }

    // Create form arrays and populate with stored data if editing
    const loanDetailsArray = this.fb.array([]);
    const investmentsArray = this.fb.array([]);

    // Create bank accounts array with the same structure as the main form
    const bankAccountsArray = this.fb.array([
      this.createBankAccount('Primary'),
      this.createBankAccount('Salary')
    ]);

    // Note: For editing, we'll populate the form after creation to avoid type conflicts
    // The stored data will be applied after the form is fully initialized

    // Determine which form to create based on the current product type
    if (this.isLapLrdForm) {
      // Create LAP/LRD specific form for co-applicant
      this.coApplicantSalariedForm = this.fb.group({
        // Personal Information
        age: [isEditing ? (storedDetailedData?.age || coApplicant?.get('age')?.value) : null],
        propertyOwnership: [storedDetailedData?.propertyOwnership || ''],

        // Job Information
        jobProfile: [storedDetailedData?.jobProfile || jobProfile || ''], // Private/Government/Semi Government/Proprietorship Company
        companyName: [storedDetailedData?.companyName || companyName || ''],
        currentJobExperience: [storedDetailedData?.currentJobExperience || null],
        totalPastJobExperience: [storedDetailedData?.totalPastJobExperience || null],
        officeDocument: [storedDetailedData?.officeDocument || ''], // Appointment Letter/Confirmation Letter/Salary Slip/Certificate

        // Salary Information
        salaryDetails: [storedDetailedData?.salaryDetails || ''],
        totalGrossSalary: [storedDetailedData?.totalGrossSalary || (isEditing ? coApplicant?.get('monthlyIncome')?.value : null)],
        deductions: this.fb.group({
          pf: [0],
          pt: [0],
          hra: [0],
          esic: [0],
          employeeLoan: [0],
          societyLoan: [0],
          other: [0]
        }),
        totalDeduction: [0],
        netSalary: [0],
        salaryMode: [''],

        // ITR Information
        itrAvailable: [''],

        // Bank Accounts
        bankAccounts: bankAccountsArray,

        // Property Information - LAP/LRD specific
        propertyPurchased: [''],
        location: [''],
        typeOfProperty: [''],
        configuration: [''],

        // Area Information
        areaSqFeet: [null],
        salable: [null],
        carpet: [null],
        ageOfProperty: [''],

        // Developer Purchase Fields
        constructionStage: [null],
        approvalAuthority: [''],
        documentsStatus: [[]],
        apfFrom: [''],

        // Additional Property Fields
        noOfChainAgreement: [''],
        agreementStatus: [''],
        currentMV: [null],
        loanAmtRequired: [null],
        ocrRequired: [null], // Auto calculated
        ocrPercentage: [null], // Auto calculated
        ltv: [null], // For LAP/LRD

        // BT Fields
        ifBt: [''],
        bankName: [''],
        currentOutstandingLoanAmount: [null],

        // LAP/LRD Specific Fields
        // LRD Specific Fields
        loiDetails: [''],
        companyNameLicensee: [''],
        leaseTenor: [''],
        startDate: [''],
        monthlyRent: [null],

        // Arrays for dynamic data
        loanDetails: loanDetailsArray,
        investments: investmentsArray,
      });
    } else {
      // Create Home Loan specific form for co-applicant
      this.coApplicantSalariedForm = this.fb.group({
        // Personal Information
        age: [isEditing ? (storedDetailedData?.age || coApplicant?.get('age')?.value) : null],
        residentialStatus: [storedDetailedData?.residentialStatus || ''],

        // Job Information
        jobProfile: [storedDetailedData?.jobProfile || jobProfile || ''],
        companyName: [storedDetailedData?.companyName || companyName || ''],
        currentJobExperience: [storedDetailedData?.currentJobExperience || null],
        totalPastJobExperience: [storedDetailedData?.totalPastJobExperience || null],
        officeDocument: [storedDetailedData?.officeDocument || ''],

        // Salary Information
        salaryDetails: [storedDetailedData?.salaryDetails || ''],
        totalGrossSalary: [storedDetailedData?.totalGrossSalary || (isEditing ? coApplicant?.get('monthlyIncome')?.value : null)],
        deductions: this.fb.group({
          pf: [storedDetailedData?.deductions?.pf || 0],
          pt: [storedDetailedData?.deductions?.pt || 0],
          hra: [storedDetailedData?.deductions?.hra || 0],
          esic: [storedDetailedData?.deductions?.esic || 0],
          employeeLoan: [storedDetailedData?.deductions?.employeeLoan || 0],
          societyLoan: [storedDetailedData?.deductions?.societyLoan || 0],
          other: [storedDetailedData?.deductions?.other || 0]
        }),
        totalDeduction: [storedDetailedData?.totalDeduction || 0],
        netSalary: [storedDetailedData?.netSalary || 0],
        salaryMode: [storedDetailedData?.salaryMode || ''],

        // ITR Information
        itrAvailable: [storedDetailedData?.itrAvailable || ''],

        // Bank Accounts
        bankAccounts: bankAccountsArray,

        // Property Information
        propertyPurchased: [storedDetailedData?.propertyPurchased || ''],
        location: [storedDetailedData?.location || ''],
        typeOfProperty: [storedDetailedData?.typeOfProperty || ''],
        configuration: [storedDetailedData?.configuration || ''],

        // Area Information
        areaSqFeet: [storedDetailedData?.areaSqFeet || null],
        salable: [storedDetailedData?.salable || null],
        carpet: [storedDetailedData?.carpet || null],
        ageOfProperty: [storedDetailedData?.ageOfProperty || ''],

        // Developer Purchase Fields
        constructionStage: [storedDetailedData?.constructionStage || null],
        approvalAuthority: [storedDetailedData?.approvalAuthority || ''],
        documentsStatus: [storedDetailedData?.documentsStatus || []],
        apfFrom: [storedDetailedData?.apfFrom || ''],

        // Additional Property Fields
        noOfChainAgreement: [storedDetailedData?.noOfChainAgreement || ''],
        agreementStatus: [storedDetailedData?.agreementStatus || ''],
        currentMV: [storedDetailedData?.currentMV || null],
        agreementValue: [storedDetailedData?.agreementValue || null],
        loanAmtRequired: [storedDetailedData?.loanAmtRequired || null],
        ocrRequired: [storedDetailedData?.ocrRequired || null], // Auto calculated
        ocrPercentage: [storedDetailedData?.ocrPercentage || null], // Auto calculated
        ltv: [storedDetailedData?.ltv || null], // For LAP/LRD

        // BT Fields
        ifBt: [storedDetailedData?.ifBt || ''],
        bankName: [storedDetailedData?.bankName || ''],
        currentOutstandingLoanAmount: [storedDetailedData?.currentOutstandingLoanAmount || null],

        // Arrays for dynamic data
        loanDetails: loanDetailsArray,
        investments: investmentsArray,
      });
    }

    // Setup value changes for deductions
    this.setupCoAppFormValueChanges();

    // Populate form with stored data if editing
    if (isEditing && storedDetailedData) {
      this.populateCoApplicantFormWithStoredData(storedDetailedData);
    }

    this.isCoAppFormSubmitted = false;
  }

  // Populate co-applicant form with stored data
  private populateCoApplicantFormWithStoredData(storedData: any) {
    if (!this.coApplicantSalariedForm || !storedData) return;

    // Populate basic form fields
    this.coApplicantSalariedForm.patchValue(storedData);

    // Populate arrays if they exist in stored data
    if (storedData.loanDetails && Array.isArray(storedData.loanDetails)) {
      const loanDetailsArray = this.coAppLoanDetailsArray;
      loanDetailsArray.clear();
      storedData.loanDetails.forEach(() => {
        this.addCoAppLoanDetail();
      });
      loanDetailsArray.patchValue(storedData.loanDetails);
    }

    if (storedData.investments && Array.isArray(storedData.investments)) {
      const investmentsArray = this.coAppInvestmentsArray;
      investmentsArray.clear();
      storedData.investments.forEach(() => {
        this.addCoAppInvestment();
      });
      investmentsArray.patchValue(storedData.investments);
    }

    if (storedData.bankAccounts && Array.isArray(storedData.bankAccounts)) {
      const bankAccountsArray = this.coAppBankAccountsArray;
      bankAccountsArray.clear();
      storedData.bankAccounts.forEach((account: any) => {
        bankAccountsArray.push(this.createBankAccount(account.accountType || 'Primary'));
      });
      bankAccountsArray.patchValue(storedData.bankAccounts);
    }
  }

  // Setup value changes for co-applicant form
  private setupCoAppFormValueChanges() {
    const deductionsGroup = this.coApplicantSalariedForm.get('deductions');
    if (deductionsGroup) {
      deductionsGroup.valueChanges.subscribe(() => {
        this.calculateCoAppTotals();
      });
    }

    this.coApplicantSalariedForm.get('totalGrossSalary')?.valueChanges.subscribe(() => {
      this.calculateCoAppTotals();
    });

    // Setup auto-calculations for property fields
    this.coApplicantSalariedForm.get('agreementValue')?.valueChanges.subscribe(() => {
      this.calculateCoAppPropertyValues();
    });

    this.coApplicantSalariedForm.get('currentMV')?.valueChanges.subscribe(() => {
      this.calculateCoAppPropertyValues();
    });

    this.coApplicantSalariedForm.get('ocrPercentage')?.valueChanges.subscribe(() => {
      this.calculateCoAppPropertyValues();
    });

    // For LAP/LRD form, setup LTV calculation
    if (this.isLapLrdForm) {
      const currentMarketValue = this.coApplicantSalariedForm.get('currentMarketValue');
      const loanAmountRequired = this.coApplicantSalariedForm.get('loanAmountRequired');

      if (currentMarketValue && loanAmountRequired) {
        currentMarketValue.valueChanges.subscribe(() => {
          this.calculateCoAppLtv();
        });

        loanAmountRequired.valueChanges.subscribe(() => {
          this.calculateCoAppLtv();
        });
      }
    }
  }

  // Calculate totals for co-applicant form
  private calculateCoAppTotals() {
    const deductionsValues = this.coApplicantSalariedForm.get('deductions')?.value || {};
    const totalDeduction: number = Object.values(deductionsValues).reduce((sum: number, val: any) => sum + (+val || 0), 0) as number;
    const grossSalary: number = +(this.coApplicantSalariedForm.get('totalGrossSalary')?.value || 0);

    this.coApplicantSalariedForm.patchValue(
      {
        totalDeduction: totalDeduction,
        netSalary: grossSalary - totalDeduction,
      },
      { emitEvent: false }
    );
  }

  private calculateCoAppPropertyValues() {
    const agreementValue = +(this.coApplicantSalariedForm.get('agreementValue')?.value || 0);
    const currentMV = +(this.coApplicantSalariedForm.get('currentMV')?.value || 0);
    const ocrPercentage = +(this.coApplicantSalariedForm.get('ocrPercentage')?.value || 0);

    // Calculate Loan Amount Required (Agreement Value - Current MV)
    const loanAmtRequired = agreementValue - currentMV;

    // Calculate OCR Required (OCR Percentage / Agreement Value * 100)
    let ocrRequired = 0;
    if (agreementValue > 0 && ocrPercentage > 0) {
      ocrRequired = (ocrPercentage / 100) * agreementValue;
    }

    this.coApplicantSalariedForm.patchValue(
      {
        loanAmtRequired: loanAmtRequired > 0 ? loanAmtRequired : 0,
        ocrRequired: ocrRequired
      },
      { emitEvent: false }
    );
  }

  // Calculate LTV for co-applicant form
  private calculateCoAppLtv() {
    if (!this.isLapLrdForm) return;

    const marketValue = +(this.coApplicantSalariedForm.get('currentMarketValue')?.value || 0);
    const loanAmount = +(this.coApplicantSalariedForm.get('loanAmountRequired')?.value || 0);

    if (marketValue > 0 && loanAmount > 0) {
      const ltvPercentage = Math.round((loanAmount / marketValue) * 100);
      this.coApplicantSalariedForm.patchValue(
        {
          ltvPercentage: ltvPercentage
        },
        { emitEvent: false }
      );
    }
  }

  // Co-Applicant Loan Details Methods
  addCoAppLoanDetail() {
    const loanDetail = this.fb.group({
      loanType: [''],
      bankName: [''],
      sanctionedAmount: [null],
      disbursedAmount: [null],
      outstandingAmount: [null],
      startDate: [null],
      tenure: [null],
      emiAmount: [null],
      default: ['No'],
      example: [''], // New field for example
    });
    this.coAppLoanDetailsArray.push(loanDetail);
  }

  removeCoAppLoanDetail(index: number) {
    this.coAppLoanDetailsArray.removeAt(index);
  }

  // Check if any co-applicant loan has default = "Yes" to show/hide Example column
  hasAnyCoAppLoanWithDefault(): boolean {
    if (!this.coAppLoanDetailsArray) return false;
    return this.coAppLoanDetailsArray.controls.some(control =>
      control.get('default')?.value === 'Yes'
    );
  }

  // Co-Applicant Investment Methods
  addCoAppInvestment() {
    const investment = this.fb.group({
      instituteName: [''],
      investmentProduct: [''],
      yearlyAmount: [null],
      investmentMode: [''],
      startDate: [null],
      endDate: [null],
      currentSavingAmount: [null],
    });
    this.coAppInvestmentsArray.push(investment);
  }

  removeCoAppInvestment(index: number) {
    this.coAppInvestmentsArray.removeAt(index);
  }

  // Get co-applicant form arrays
  get coAppLoanDetailsArray() {
    return this.coApplicantSalariedForm?.get('loanDetails') as FormArray;
  }

  get coAppInvestmentsArray() {
    return this.coApplicantSalariedForm?.get('investments') as FormArray;
  }

  get coAppBankAccountsArray() {
    return this.coApplicantSalariedForm?.get('bankAccounts') as FormArray;
  }

  // Edit Co-Applicant Salaried Details
  editCoApplicantSalariedDetails(index: number) {
    console.log('🔧 Editing co-applicant salaried details for index:', index);
    this.currentCoApplicantIndex = index;

    // Check if detailed data exists for this co-applicant
    const hasDetailedData = this.coApplicantDetailedData[index];
    console.log('🔍 Existing detailed data for co-applicant', index, ':', hasDetailedData);

    // Initialize the form
    this.initCoApplicantSalariedForm();

    // If there's detailed data, populate the form immediately after initialization
    if (hasDetailedData) {
      console.log('📝 Populating form with existing detailed data');
      setTimeout(() => {
        this.populateCoApplicantFormWithStoredData(hasDetailedData);
      }, 100); // Small delay to ensure form is fully initialized
    }

    this.modalService.open(this.salariedModal, {
      centered: true,
      size: 'xl',
      backdrop: 'static'
    });
  }



  // Edit Co-Applicant Details (Dynamic - detects data type)
  editCoApplicantDetails(index: number) {
    console.log('🔧 Editing co-applicant details for index:', index);

    // Get the co-applicant basic info
    const coApplicant = this.coApplicantsArray.at(index);
    const occupation = coApplicant?.get('occupation')?.value;

    // Check if detailed data exists for this co-applicant
    const hasDetailedData = this.coApplicantDetailedData[index];
    console.log('🔍 Existing detailed data for co-applicant', index, ':', hasDetailedData);
    console.log('📋 Co-applicant occupation:', occupation);

    // Always use the occupation field as the primary determinant
    if (occupation === 'Self Employed') {
      console.log('🏢 Opening Self-Employed modal for index:', index);
      this.editCoApplicantSelfEmployedDetails(index);
    } else if (occupation === 'Salaried Employee') {
      console.log('💼 Opening Salaried modal for index:', index);
      this.editCoApplicantSalariedDetails(index);
    } else {
      console.warn('⚠️ Unknown occupation type:', occupation);
      // Default to salaried if occupation is unclear
      this.editCoApplicantSalariedDetails(index);
    }
  }

  // Edit Co-Applicant Self Employed Details
  editCoApplicantSelfEmployedDetails(index: number) {
    console.log('🔧 Editing co-applicant self-employed details for index:', index);
    this.currentCoApplicantIndex = index;

    // Check if detailed data exists for this co-applicant
    const hasDetailedData = this.coApplicantDetailedData[index];
    console.log('🔍 Existing detailed data for co-applicant', index, ':', hasDetailedData);

    // Open the self employed modal
    const modalRef = this.modalService.open(this.selfEmployedModal, {
      centered: true,
      size: 'xl',
      backdrop: 'static'
    });

    // After the modal is opened, we need to pass the stored data to the self-employee component
    modalRef.shown.subscribe(() => {
      // Find the self-employee component in the modal
      const selfEmployeeComp = document.querySelector('app-self-employee');
      if (selfEmployeeComp) {
        console.log('Found self-employee component in modal for editing');

        // Wait for Angular to initialize the component
        setTimeout(() => {
          // Get the component instance
          if (this.selfEmployeeComponent) {
            console.log('Setting up self-employee component for editing index:', this.currentCoApplicantIndex);
            console.log('📋 Data to populate:', hasDetailedData);

            // Set the current co-applicant index
            this.selfEmployeeComponent.currentCoApplicantIndex = this.currentCoApplicantIndex;

            // IMPORTANT: Pass the stored data to the self-employee component
            if (hasDetailedData) {
              // Ensure the self-employee component has the detailed data
              if (!this.selfEmployeeComponent.coApplicantDetailedData) {
                this.selfEmployeeComponent.coApplicantDetailedData = {};
              }
              this.selfEmployeeComponent.coApplicantDetailedData[this.currentCoApplicantIndex] = hasDetailedData;
              console.log('📤 Transferred detailed data to self-employee component for index:', this.currentCoApplicantIndex);
            }

            // Set the product type
            this.selfEmployeeComponent.setProductType(this.currentProductType);

            // Initialize the co-applicant self-employed form (this will now use the transferred data)
            this.selfEmployeeComponent.initCoApplicantSelfEmployedForm();

            // Double-check: If there's detailed data, populate the form again
            if (hasDetailedData) {
              console.log('📝 Populating self-employed form with existing detailed data for editing');
              // Add a small delay to ensure form is fully initialized
              setTimeout(() => {
                this.selfEmployeeComponent.populateCoApplicantSelfEmployedFormWithStoredData(hasDetailedData);
                console.log('✅ Self-employed form populated with stored data for editing');
              }, 300);
            }

            // Force update the form
            this.selfEmployeeComponent.forceUpdateFormType();

            // Sync product type
            if (typeof this.selfEmployeeComponent.syncProductType === 'function') {
              this.selfEmployeeComponent.syncProductType();
            }
          } else {
            console.warn('Self-employee component not found for editing');
          }
        }, 100);
      } else {
        console.warn('Self-employee component not found in modal for editing');
      }
    });
  }

  // Handle co-applicant self-employed data saved event
  onCoApplicantSelfEmployedSaved(savedData: any) {
    console.log('📥 Received co-applicant self-employed saved data:', savedData);
    console.log('📊 Current coApplicantDetailedData before update:', this.coApplicantDetailedData);

    // Update the co-applicant in the main form
    if (savedData.index >= 0 && savedData.index < this.coApplicantsArray.length) {
      const coApplicant = this.coApplicantsArray.at(savedData.index);

      // Update the basic co-applicant info
      coApplicant.patchValue(savedData.basicInfo);

      // Store the detailed data
      this.coApplicantDetailedData[savedData.index] = savedData.detailedData;

      console.log('✅ Co-applicant self-employed data updated in salaried component');
      console.log('📊 Updated co-applicants array:', this.coApplicantsArray.value);
      console.log('💾 Updated coApplicantDetailedData:', this.coApplicantDetailedData);
      console.log('🔍 Specific data for index', savedData.index, ':', this.coApplicantDetailedData[savedData.index]);
    } else {
      console.error('❌ Invalid index or co-applicant array length:', {
        index: savedData.index,
        arrayLength: this.coApplicantsArray.length,
        savedData: savedData
      });
    }
  }

  // Save Co-Applicant Self-Employed Form
  saveCoApplicantSelfEmployedForm(modal: any) {
    console.log('🚀 Save Co-Applicant Self-Employed Form called from salaried component');

    // Set the current co-applicant index on the self-employee component
    if (this.selfEmployeeComponent) {
      this.selfEmployeeComponent.currentCoApplicantIndex = this.currentCoApplicantIndex;

      // Ensure the form is initialized before saving
      if (!this.selfEmployeeComponent.coApplicantSelfEmployedForm) {
        console.log('🔧 Initializing co-applicant self-employed form before save');
        this.selfEmployeeComponent.initCoApplicantSelfEmployedForm();
      }

      // Call the save method on the self-employee component
      this.selfEmployeeComponent.saveCoApplicantSelfEmployedForm(modal);
    } else {
      console.error('Self-employee component not found');
    }
  }

  // Save Co-Applicant Salaried Form
  saveCoApplicantSalariedForm(modal: any) {
    console.log('Save Co-Applicant Salaried Form called');
    this.isCoAppFormSubmitted = true;

    if (this.coApplicantSalariedForm.invalid) {
      console.log('Co-Applicant form is invalid', this.coApplicantSalariedForm);
      console.log('Form errors:', this.coApplicantSalariedForm.errors);
      return;
    }

    // Get the current co-applicant
    if (this.currentCoApplicantIndex >= 0 && this.currentCoApplicantIndex < this.coApplicantsArray.length) {
      const coApplicant = this.coApplicantsArray.at(this.currentCoApplicantIndex);

      // Update the co-applicant's income source and monthly income based on the salaried form
      const formValues = this.coApplicantSalariedForm.value;

      // Create a more detailed income source description
      let incomeSourceDetails = `${formValues.companyName} (${formValues.jobProfile})`;

      // Add loan count if any
      if (this.coAppLoanDetailsArray.length > 0) {
        incomeSourceDetails += ` | ${this.coAppLoanDetailsArray.length} Loans`;
      }

      // Add investment count if any
      if (this.coAppInvestmentsArray.length > 0) {
        incomeSourceDetails += ` | ${this.coAppInvestmentsArray.length} Investments`;
      }

      // Update the basic co-applicant info in the external array
      coApplicant.patchValue({
        incomeSource: incomeSourceDetails,
        monthlyIncome: formValues.netSalary || formValues.totalGrossSalary
      });

      // Store the complete detailed data for this co-applicant
      const detailedData = {
        ...formValues,
        // Convert FormArray data to proper format for storage
        bankAccounts: this.coAppBankAccountsArray?.value || [],
        loanDetails: this.coAppLoanDetailsArray?.value || [],
        investments: this.coAppInvestmentsArray?.value || []
      };

      // Store in the detailed data structure
      this.coApplicantDetailedData[this.currentCoApplicantIndex] = detailedData;

      // Also store the detailed data within the co-applicant object for consistency
      const currentCoApplicantValue = coApplicant.value;
      coApplicant.patchValue({
        ...currentCoApplicantValue,
        detailedData: detailedData
      });

      console.log('Co-Applicant salaried form saved', formValues);
      console.log('Stored detailed data:', this.coApplicantDetailedData[this.currentCoApplicantIndex]);

      // Reset form submission state
      this.isCoAppFormSubmitted = false;
      modal.close('Save');
    }
  }

  // Form submission
  onSubmit() {
    this.isFormSubmitted = true;

    if (this.salariedForm.invalid) {
      console.log('Form is invalid', this.salariedForm);
      return;
    }

    console.log('Form submitted successfully', this.salariedForm.value);
    // Add your API call here
  }

  onCancel() {
    this.salariedForm.reset();
    this.isFormSubmitted = false;
  }

  // Add this helper method for templates to check the form type
  get isLapLrdForm(): boolean {
    return this.initializedProductType === 'LAP' || this.initializedProductType === 'LRD';
  }

  get isHLForm(): boolean {
    return this.initializedProductType === 'HL' || this.initializedProductType === null;
  }

  // Helper methods to check if "Flat" is selected for showing configuration field
  get isMainApplicantFlatSelected(): boolean {
    return this.salariedForm.get('typeOfProperty')?.value === 'Flat';
  }

  get isCoApplicantFlatSelected(): boolean {
    return this.coApplicantSalariedForm.get('typeOfProperty')?.value === 'Flat';
  }



  // Helper methods to check if "Developer" is selected for showing developer-specific fields
  get isMainApplicantDeveloperSelected(): boolean {
    return this.salariedForm.get('propertyPurchased')?.value === 'Developer';
  }

  get isCoApplicantDeveloperSelected(): boolean {
    return this.coApplicantSalariedForm.get('propertyPurchased')?.value === 'Developer';
  }

  // Get filtered property type options based on sub product type
  get filteredPropertyTypeOptions(): string[] {
    if (this.selectedProductSubType === 'NRPL') {
      return ['Shop', 'Office'];
    }
    return this.propertyTypeOptions;
  }

  // Method to get all form data for API submission
  getFormData(): any {
    console.log('📋 SalariedEmployeeComponent.getFormData() called');
    const formData: any = {};

    // Main applicant salaried form data
    if (this.salariedForm) {
      console.log('✅ Main salaried form exists, capturing data (valid:', this.salariedForm.valid, ')');
      formData.mainApplicant = {
        ...this.salariedForm.value,
        // Convert FormArray data to proper format
        bankAccounts: this.bankAccountsArray?.value || [],
        loanDetails: this.loanDetailsArray?.value || [],
        investments: this.investmentsArray?.value || []
      };

      if (!this.salariedForm.valid) {
        console.warn('⚠️ Main salaried form is invalid but including data anyway');
        console.warn('⚠️ Form errors:', this.salariedForm.errors);
      }
    } else {
      console.warn('⚠️ Main salaried form is not available');
    }

    // Co-applicants basic data
    if (this.coApplicantsArray && this.coApplicantsArray.length > 0) {
      console.log('✅ Co-applicants exist, capturing basic data');
      formData.coApplicants = this.coApplicantsArray.value;

      // Also store in the nested structure for backward compatibility
      if (!formData.salaried_employee_details) {
        formData.salaried_employee_details = {};
      }
      formData.salaried_employee_details.coApplicants = this.coApplicantsArray.value;
    }

    // Co-applicants detailed data (nested forms)
    if (Object.keys(this.coApplicantDetailedData).length > 0) {
      console.log('✅ Co-applicant detailed data exists, capturing nested forms');
      console.log('📋 Co-applicant detailed data being included:', this.coApplicantDetailedData);
      formData.coApplicantsDetailed = this.coApplicantDetailedData;
    } else {
      console.log('⚠️ No co-applicant detailed data found');
      console.log('🔍 Current coApplicantDetailedData:', this.coApplicantDetailedData);
    }

    // Current co-applicant form data if exists (for immediate submission)
    if (this.coApplicantSalariedForm && this.coApplicantSalariedForm.valid) {
      console.log('✅ Co-applicant salaried form is valid, capturing current data');
      formData.currentCoApplicant = {
        ...this.coApplicantSalariedForm.value,
        // Convert FormArray data to proper format
        bankAccounts: this.coAppBankAccountsArray?.value || [],
        loanDetails: this.coAppLoanDetailsArray?.value || [],
        investments: this.coAppInvestmentsArray?.value || []
      };
    }

    // Property information if available
    if (this.salariedForm.get('propertyInformation')) {
      formData.propertyInformation = this.salariedForm.get('propertyInformation')?.value;
    }

    console.log('📊 SalariedEmployeeComponent form data:', formData);
    return formData;
  }

  /**
   * Populate form with existing data for editing
   * This method is called by the parent component when entering edit mode
   */
  populateFormData(formData: any): void {
    if (!formData) {
      console.warn('⚠️ No form data provided for SalariedEmployeeComponent');
      return;
    }

    console.log('🔧 Populating SalariedEmployeeComponent with data:', formData);
    console.log('🔍 Checking for coApplicantsDetailed:', formData.coApplicantsDetailed);
    console.log('🔍 Checking for coApplicantDetailedData:', formData.coApplicantDetailedData);

    try {
      // Populate main applicant form if available
      if (formData.mainApplicant && this.salariedForm) {
        this.salariedForm.patchValue(formData.mainApplicant);

        // Populate form arrays
        if (formData.mainApplicant.bankAccounts) {
          this.populateFormArray(this.bankAccountsArray, formData.mainApplicant.bankAccounts);
        }
        if (formData.mainApplicant.loanDetails) {
          this.populateFormArray(this.loanDetailsArray, formData.mainApplicant.loanDetails);
        }
        if (formData.mainApplicant.investments) {
          this.populateFormArray(this.investmentsArray, formData.mainApplicant.investments);
        }
      }

      // Populate co-applicants data
      if (formData.coApplicants && Array.isArray(formData.coApplicants)) {
        // Clear existing co-applicants
        this.coApplicantsArray.clear();

        // Add co-applicants
        formData.coApplicants.forEach((coApplicant: any, index: number) => {
          this.addCoApplicant();

          // Store detailed data if available from coApplicantDetailedData
          if (formData.coApplicantDetailedData && formData.coApplicantDetailedData[index]) {
            this.coApplicantDetailedData[index] = formData.coApplicantDetailedData[index];
          }
          // Check for coApplicantsDetailed structure (from API response)
          else if (formData.coApplicantsDetailed && formData.coApplicantsDetailed[index]) {
            this.coApplicantDetailedData[index] = formData.coApplicantsDetailed[index];
          }
          // Also check if detailed data is stored within the co-applicant object itself
          else if (coApplicant.detailedData) {
            this.coApplicantDetailedData[index] = coApplicant.detailedData;
          }
        });

        // Patch co-applicants array
        this.coApplicantsArray.patchValue(formData.coApplicants);
      }

      // Also populate from nested structure if available (for backward compatibility)
      if (formData.salaried_employee_details?.coApplicants && Array.isArray(formData.salaried_employee_details.coApplicants)) {
        // Clear existing co-applicants if not already populated
        if (!formData.coApplicants || formData.coApplicants.length === 0) {
          this.coApplicantsArray.clear();
        }

        // Add co-applicants from nested structure
        formData.salaried_employee_details.coApplicants.forEach((coApplicant: any, index: number) => {
          // Only add if not already added from external array
          if (index >= this.coApplicantsArray.length) {
            this.addCoApplicant();
          }

          // Map the nested structure to the expected format
          const mappedCoApplicant = {
            name: coApplicant.name || '',
            relation: coApplicant.relation || '',
            age: coApplicant.age || null,
            occupation: coApplicant.occupation || '',
            incomeSource: coApplicant.incomeSource || '',
            monthlyIncome: coApplicant.monthlyIncome || null
          };

          // Update the co-applicant at this index
          if (this.coApplicantsArray.at(index)) {
            this.coApplicantsArray.at(index).patchValue(mappedCoApplicant);
          }

          // Store any additional detailed data if available
          if (coApplicant.detailedData) {
            this.coApplicantDetailedData[index] = coApplicant.detailedData;
          }
        });
      }

      console.log('✅ SalariedEmployeeComponent form populated successfully');
    } catch (error) {
      console.error('❌ Error populating SalariedEmployeeComponent form:', error);
    }
  }

  /**
   * Helper method to populate FormArray with data
   */
  private populateFormArray(formArray: FormArray, data: any[]): void {
    if (!formArray || !data || !Array.isArray(data)) return;

    // Clear existing items
    formArray.clear();

    // Add items based on data
    data.forEach((item: any) => {
      if (formArray === this.bankAccountsArray) {
        formArray.push(this.createBankAccount(item.accountType || 'Primary'));
      } else if (formArray === this.loanDetailsArray) {
        this.addLoanDetail();
      } else if (formArray === this.investmentsArray) {
        this.addInvestment();
      }
    });

    // Patch values
    formArray.patchValue(data);
  }
}