import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  ProfessionTypeService,
  ProfessionType,
  ProfessionTypeStatistics
} from '../../../../core/services/profession-type.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { ProfessionTypeFormComponent } from './profession-type-form/profession-type-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-profession-types',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective
  ],
  templateUrl: './profession-types.component.html',
  styleUrls: ['./profession-types.component.scss']
})
export class ProfessionTypesComponent implements OnInit {
  // Data properties
  professionTypes: ProfessionType[] = [];
  deletedProfessionTypes: ProfessionType[] = [];
  statistics: ProfessionTypeStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedCategory = '';
  selectedSkillLevel = '';
  selectedEducationLevel = '';
  selectedIndustrySector = '';
  selectedCertificationRequired: 'all' | 'required' | 'not_required' = 'all';
  selectedLicenseRequired: 'all' | 'required' | 'not_required' = 'all';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedProfessionTypes: Set<string> = new Set();
  selectAll = false;

  // Filter options
  professionCategories: any[] = [];
  skillLevels: any[] = [];
  educationLevels: any[] = [];
  industrySectors: string[] = [];

  constructor(
    private professionTypeService: ProfessionTypeService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadProfessionTypes();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.professionCategories = this.professionTypeService.getProfessionCategories();
    this.skillLevels = this.professionTypeService.getSkillLevels();
    this.educationLevels = this.professionTypeService.getEducationLevels();
    this.industrySectors = this.professionTypeService.getIndustrySectors();
  }

  /**
   * Load profession types with current filters
   */
  loadProfessionTypes(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      category: this.selectedCategory || undefined,
      skill_level: this.selectedSkillLevel || undefined,
      education_level: this.selectedEducationLevel || undefined,
      industry_sector: this.selectedIndustrySector || undefined,
      certification_required: this.selectedCertificationRequired === 'all' ? undefined : this.selectedCertificationRequired === 'required',
      license_required: this.selectedLicenseRequired === 'all' ? undefined : this.selectedLicenseRequired === 'required',
      include_deleted: this.viewMode === 'deleted'
    };

    this.professionTypeService.getProfessionTypesWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedProfessionTypes = response.data.filter(pt => pt.deleted_at);
            this.professionTypes = [];
          } else {
            this.professionTypes = response.data.filter(pt => !pt.deleted_at);
            this.deletedProfessionTypes = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load profession types';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load profession types. Please try again.'
        });
      }
    });
  }

  /**
   * Load profession type statistics
   */
  loadStatistics(): void {
    this.professionTypeService.getProfessionTypeStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search profession types
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by category
   */
  onCategoryFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by skill level
   */
  onSkillLevelFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by education level
   */
  onEducationLevelFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by industry sector
   */
  onIndustrySectorFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by certification required
   */
  onCertificationRequiredFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Filter by license required
   */
  onLicenseRequiredFilter(): void {
    this.currentPage = 1;
    this.loadProfessionTypes();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedProfessionTypes.clear();
    this.selectAll = false;
    
    if (mode !== 'statistics') {
      this.loadProfessionTypes();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadProfessionTypes();
  }

  /**
   * Open create profession type modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(ProfessionTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadProfessionTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Profession type created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit profession type modal
   */
  openEditModal(professionType: ProfessionType): void {
    const modalRef = this.modalService.open(ProfessionTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.professionType = { ...professionType };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadProfessionTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Profession type updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete profession type
   */
  deleteProfessionType(professionType: ProfessionType): void {
    this.popupService.showConfirmation({
      title: 'Delete Profession Type',
      message: `Are you sure you want to delete "${professionType.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.professionTypeService.deleteProfessionType(professionType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadProfessionTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Profession type deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete profession type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore profession type
   */
  restoreProfessionType(professionType: ProfessionType): void {
    this.popupService.showConfirmation({
      title: 'Restore Profession Type',
      message: `Are you sure you want to restore "${professionType.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.professionTypeService.restoreProfessionType(professionType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadProfessionTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Profession type restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore profession type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadProfessionTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Profession types uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.professionTypeService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'profession_types_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadProfessionTypes();
    this.loadStatistics();
  }

  /**
   * Get profession category label
   */
  getProfessionCategoryLabel(category: string): string {
    return this.professionTypeService.getProfessionCategoryLabel(category);
  }

  /**
   * Get skill level label
   */
  getSkillLevelLabel(level: string): string {
    return this.professionTypeService.getSkillLevelLabel(level);
  }

  /**
   * Get education level label
   */
  getEducationLevelLabel(level: string): string {
    return this.professionTypeService.getEducationLevelLabel(level);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get skill level badge class
   */
  getSkillLevelBadgeClass(level: string): string {
    switch (level) {
      case 'entry': return 'badge bg-info';
      case 'intermediate': return 'badge bg-primary';
      case 'senior': return 'badge bg-warning';
      case 'expert': return 'badge bg-success';
      case 'executive': return 'badge bg-danger';
      default: return 'badge bg-light';
    }
  }

  /**
   * Format salary range
   */
  formatSalaryRange(professionType: ProfessionType): string {
    if (!professionType.salary_range_min && !professionType.salary_range_max) {
      return 'Not specified';
    }
    
    const currency = professionType.currency || 'USD';
    const min = professionType.salary_range_min ? `${currency} ${professionType.salary_range_min.toLocaleString()}` : '';
    const max = professionType.salary_range_max ? `${currency} ${professionType.salary_range_max.toLocaleString()}` : '';
    
    if (min && max) {
      return `${min} - ${max}`;
    } else if (min) {
      return `From ${min}`;
    } else if (max) {
      return `Up to ${max}`;
    }
    
    return 'Not specified';
  }

  /**
   * Format experience range
   */
  formatExperienceRange(professionType: ProfessionType): string {
    if (!professionType.experience_years_min && !professionType.experience_years_max) {
      return 'Not specified';
    }
    
    const min = professionType.experience_years_min || 0;
    const max = professionType.experience_years_max;
    
    if (max) {
      return `${min}-${max} years`;
    } else if (min > 0) {
      return `${min}+ years`;
    }
    
    return 'Not specified';
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): ProfessionType[] {
    return this.viewMode === 'deleted' ? this.deletedProfessionTypes : this.professionTypes;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByProfessionTypeId(index: number, professionType: ProfessionType): string {
    return professionType.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
