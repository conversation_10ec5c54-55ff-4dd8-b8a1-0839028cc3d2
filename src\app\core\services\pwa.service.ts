import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable, fromEvent } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export interface UpdateAvailableEvent {
  type: 'UPDATE_AVAILABLE';
  current: { hash: string; appData?: any };
  available: { hash: string; appData?: any };
}

/**
 * Progressive Web App Service
 *
 * Manages service worker registration, app installation prompts,
 * update notifications, and offline capabilities.
 */
@Injectable({
  providedIn: 'root'
})
export class PWAService {
  private installPromptEvent: PWAInstallPrompt | null = null;
  private isOnlineSubject = new BehaviorSubject<boolean>(true);
  private updateAvailableSubject = new BehaviorSubject<boolean>(false);
  private installableSubject = new BehaviorSubject<boolean>(false);

  // Public observables
  public readonly isOnline$ = this.isOnlineSubject.asObservable();
  public readonly updateAvailable$ = this.updateAvailableSubject.asObservable();
  public readonly installable$ = this.installableSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializePWA();
    }
  }

  /**
   * Initialize PWA functionality
   */
  private initializePWA(): void {
    this.registerServiceWorker();
    this.setupInstallPrompt();
    this.setupOnlineOfflineDetection();
    this.setupUpdateDetection();
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        console.log('🔧 PWA: Registering service worker...');

        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/',
          updateViaCache: 'none'
        });

        console.log('✅ PWA: Service worker registered:', registration);

        // Check for updates
        registration.addEventListener('updatefound', () => {
          console.log('🔄 PWA: Service worker update found');
          this.handleServiceWorkerUpdate(registration);
        });

        // Check for updates on page load
        if (registration.waiting) {
          this.updateAvailableSubject.next(true);
        }

        // Check for updates periodically
        setInterval(() => {
          registration.update();
        }, 60000); // Check every minute

      } catch (error) {
        console.error('❌ PWA: Service worker registration failed:', error);
      }
    } else {
      console.warn('⚠️ PWA: Service workers not supported');
    }
  }

  /**
   * Handle service worker updates
   */
  private handleServiceWorkerUpdate(registration: ServiceWorkerRegistration): void {
    const newWorker = registration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        console.log('🆕 PWA: New service worker installed, update available');
        this.updateAvailableSubject.next(true);
      }
    });
  }

  /**
   * Setup install prompt handling
   */
  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (event: any) => {
      console.log('📱 PWA: Install prompt available');
      event.preventDefault();
      this.installPromptEvent = event;
      this.installableSubject.next(true);
    });

    window.addEventListener('appinstalled', () => {
      console.log('✅ PWA: App installed successfully');
      this.installPromptEvent = null;
      this.installableSubject.next(false);
    });
  }

  /**
   * Setup online/offline detection
   */
  private setupOnlineOfflineDetection(): void {
    // Initial state
    this.isOnlineSubject.next(navigator.onLine);

    // Listen for online/offline events
    fromEvent(window, 'online').subscribe(() => {
      console.log('🌐 PWA: App is online');
      this.isOnlineSubject.next(true);
    });

    fromEvent(window, 'offline').subscribe(() => {
      console.log('📴 PWA: App is offline');
      this.isOnlineSubject.next(false);
    });
  }

  /**
   * Setup update detection
   */
  private setupUpdateDetection(): void {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
          console.log('🔄 PWA: Update available message received');
          this.updateAvailableSubject.next(true);
        }
      });
    }
  }

  /**
   * Prompt user to install the app
   */
  async promptInstall(): Promise<boolean> {
    if (!this.installPromptEvent) {
      console.warn('⚠️ PWA: No install prompt available');
      return false;
    }

    try {
      console.log('📱 PWA: Showing install prompt');
      await this.installPromptEvent.prompt();

      const choiceResult = await this.installPromptEvent.userChoice;
      console.log('📱 PWA: Install prompt result:', choiceResult.outcome);

      if (choiceResult.outcome === 'accepted') {
        this.installPromptEvent = null;
        this.installableSubject.next(false);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ PWA: Install prompt failed:', error);
      return false;
    }
  }

  /**
   * Apply available update
   */
  async applyUpdate(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        console.log('🔄 PWA: Applying update...');

        const registration = await navigator.serviceWorker.getRegistration();
        if (registration?.waiting) {
          // Tell the waiting service worker to skip waiting
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });

          // Reload the page to activate the new service worker
          window.location.reload();
        }
      } catch (error) {
        console.error('❌ PWA: Failed to apply update:', error);
      }
    }
  }

  /**
   * Check if app is installed
   */
  isInstalled(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  }

  /**
   * Get app installation status
   */
  getInstallationStatus(): 'installed' | 'installable' | 'not-supported' {
    if (this.isInstalled()) {
      return 'installed';
    }

    if (this.installableSubject.value) {
      return 'installable';
    }

    return 'not-supported';
  }

  /**
   * Share content using Web Share API
   */
  async share(data: { title?: string; text?: string; url?: string }): Promise<boolean> {
    if ('share' in navigator) {
      try {
        await navigator.share(data);
        return true;
      } catch (error) {
        console.error('❌ PWA: Share failed:', error);
        return false;
      }
    }

    // Fallback to clipboard
    if ('clipboard' in navigator && data.url) {
      try {
        await (navigator as any).clipboard.writeText(data.url);
        return true;
      } catch (error) {
        console.error('❌ PWA: Clipboard write failed:', error);
        return false;
      }
    }

    return false;
  }

  /**
   * Request persistent storage
   */
  async requestPersistentStorage(): Promise<boolean> {
    if ('storage' in navigator && 'persist' in navigator.storage) {
      try {
        const granted = await navigator.storage.persist();
        console.log('💾 PWA: Persistent storage granted:', granted);
        return granted;
      } catch (error) {
        console.error('❌ PWA: Persistent storage request failed:', error);
        return false;
      }
    }

    return false;
  }

  /**
   * Get storage usage estimate
   */
  async getStorageEstimate(): Promise<StorageEstimate | null> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        console.log('💾 PWA: Storage estimate:', estimate);
        return estimate;
      } catch (error) {
        console.error('❌ PWA: Storage estimate failed:', error);
        return null;
      }
    }

    return null;
  }

  /**
   * Clear app data
   */
  async clearAppData(): Promise<void> {
    try {
      // Clear caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      // Clear local storage
      localStorage.clear();
      sessionStorage.clear();

      // Clear IndexedDB (if used)
      if ('indexedDB' in window) {
        // Implementation depends on your IndexedDB usage
      }

      console.log('🧹 PWA: App data cleared');
    } catch (error) {
      console.error('❌ PWA: Failed to clear app data:', error);
    }
  }
}
