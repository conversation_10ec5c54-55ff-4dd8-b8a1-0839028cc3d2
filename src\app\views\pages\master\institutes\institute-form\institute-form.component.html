<!-- Institute Form Modal -->
<div class="modal-header">
  <h5 class="modal-title">
    <i class="feather icon-home me-2"></i>
    {{ getModalTitle() }}
  </h5>
  <button type="button" class="btn-close" (click)="cancel()" aria-label="Close"></button>
</div>

<div class="modal-body">
  <!-- Error <PERSON> -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="feather icon-alert-circle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null" aria-label="Close"></button>
  </div>

  <!-- Institute Form -->
  <form [formGroup]="instituteForm" (ngSubmit)="save()">
    
    <!-- Basic Information Section -->
    <div class="section-header">
      <h6 class="section-title">
        <i class="feather icon-info me-1"></i>
        Basic Information
      </h6>
    </div>

    <div class="row">
      <!-- Institute Name -->
      <div class="col-md-6 mb-3">
        <label for="instituteName" class="form-label">
          Institute Name <span class="text-danger">*</span>
        </label>
        <input 
          type="text" 
          id="instituteName"
          class="form-control"
          [class.is-invalid]="hasError('name')"
          formControlName="name"
          placeholder="Enter institute name"
          maxlength="100"
          (blur)="generateCodeFromName()">
        
        <div *ngIf="hasError('name')" class="invalid-feedback">
          {{ getErrorMessage('name') }}
        </div>
      </div>

      <!-- Institute Code -->
      <div class="col-md-3 mb-3">
        <label for="instituteCode" class="form-label">
          Code <span class="text-danger">*</span>
        </label>
        <input 
          type="text" 
          id="instituteCode"
          class="form-control"
          [class.is-invalid]="hasError('code')"
          formControlName="code"
          placeholder="e.g., HDFC"
          maxlength="10"
          style="text-transform: uppercase;">
        
        <div *ngIf="hasError('code')" class="invalid-feedback">
          {{ getErrorMessage('code') }}
        </div>
      </div>

      <!-- Institute Type -->
      <div class="col-md-3 mb-3">
        <label for="instituteType" class="form-label">
          Type <span class="text-danger">*</span>
        </label>
        <select 
          id="instituteType"
          class="form-select"
          [class.is-invalid]="hasError('type')"
          formControlName="type">
          
          <option *ngFor="let type of instituteTypes" [value]="type.value">
            {{ type.label }}
          </option>
        </select>
        
        <div *ngIf="hasError('type')" class="invalid-feedback">
          {{ getErrorMessage('type') }}
        </div>
        
        <div class="form-text" *ngIf="instituteForm.get('type')?.value">
          {{ getInstituteTypeDescription(instituteForm.get('type')?.value) }}
        </div>
      </div>
    </div>

    <!-- Description -->
    <div class="mb-3">
      <label for="description" class="form-label">Description</label>
      <textarea 
        id="description"
        class="form-control"
        [class.is-invalid]="hasError('description')"
        formControlName="description"
        placeholder="Enter institute description (optional)"
        rows="3"
        maxlength="500"></textarea>
      
      <div *ngIf="hasError('description')" class="invalid-feedback">
        {{ getErrorMessage('description') }}
      </div>
    </div>

    <!-- Contact Information Section -->
    <div class="section-header">
      <h6 class="section-title">
        <i class="feather icon-phone me-1"></i>
        Contact Information
      </h6>
    </div>

    <div class="row">
      <!-- Website -->
      <div class="col-md-6 mb-3">
        <label for="website" class="form-label">Website</label>
        <input 
          type="url" 
          id="website"
          class="form-control"
          [class.is-invalid]="hasError('website')"
          formControlName="website"
          placeholder="https://www.example.com"
          (blur)="formatWebsiteUrl()">
        
        <div *ngIf="hasError('website')" class="invalid-feedback">
          {{ getErrorMessage('website') }}
        </div>
      </div>

      <!-- Contact Email -->
      <div class="col-md-6 mb-3">
        <label for="contactEmail" class="form-label">Contact Email</label>
        <input 
          type="email" 
          id="contactEmail"
          class="form-control"
          [class.is-invalid]="hasError('contact_email')"
          formControlName="contact_email"
          placeholder="<EMAIL>">
        
        <div *ngIf="hasError('contact_email')" class="invalid-feedback">
          {{ getErrorMessage('contact_email') }}
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Contact Phone -->
      <div class="col-md-6 mb-3">
        <label for="contactPhone" class="form-label">Contact Phone</label>
        <input 
          type="tel" 
          id="contactPhone"
          class="form-control"
          [class.is-invalid]="hasError('contact_phone')"
          formControlName="contact_phone"
          placeholder="+91 12345 67890">
        
        <div *ngIf="hasError('contact_phone')" class="invalid-feedback">
          {{ getErrorMessage('contact_phone') }}
        </div>
      </div>
    </div>

    <!-- Address Information Section -->
    <div class="section-header">
      <h6 class="section-title">
        <i class="feather icon-map-pin me-1"></i>
        Address Information
      </h6>
    </div>

    <!-- Address -->
    <div class="mb-3">
      <label for="address" class="form-label">Address</label>
      <textarea 
        id="address"
        class="form-control"
        [class.is-invalid]="hasError('address')"
        formControlName="address"
        placeholder="Enter complete address"
        rows="2"
        maxlength="200"></textarea>
      
      <div *ngIf="hasError('address')" class="invalid-feedback">
        {{ getErrorMessage('address') }}
      </div>
    </div>

    <div class="row">
      <!-- City -->
      <div class="col-md-4 mb-3">
        <label for="city" class="form-label">City</label>
        <input 
          type="text" 
          id="city"
          class="form-control"
          [class.is-invalid]="hasError('city')"
          formControlName="city"
          placeholder="Enter city"
          maxlength="50">
        
        <div *ngIf="hasError('city')" class="invalid-feedback">
          {{ getErrorMessage('city') }}
        </div>
      </div>

      <!-- State -->
      <div class="col-md-4 mb-3">
        <label for="state" class="form-label">State</label>
        <input 
          type="text" 
          id="state"
          class="form-control"
          [class.is-invalid]="hasError('state')"
          formControlName="state"
          placeholder="Enter state"
          maxlength="50">
        
        <div *ngIf="hasError('state')" class="invalid-feedback">
          {{ getErrorMessage('state') }}
        </div>
      </div>

      <!-- Postal Code -->
      <div class="col-md-4 mb-3">
        <label for="postalCode" class="form-label">Postal Code</label>
        <input 
          type="text" 
          id="postalCode"
          class="form-control"
          [class.is-invalid]="hasError('postal_code')"
          formControlName="postal_code"
          placeholder="Enter postal code"
          maxlength="20">
        
        <div *ngIf="hasError('postal_code')" class="invalid-feedback">
          {{ getErrorMessage('postal_code') }}
        </div>
      </div>
    </div>

    <!-- Country -->
    <div class="mb-3">
      <label for="country" class="form-label">Country</label>
      <select 
        id="country"
        class="form-select"
        [class.is-invalid]="hasError('country')"
        formControlName="country">
        
        <option value="">Select Country</option>
        <option *ngFor="let country of countries" [value]="country">
          {{ country }}
        </option>
      </select>
      
      <div *ngIf="hasError('country')" class="invalid-feedback">
        {{ getErrorMessage('country') }}
      </div>
    </div>

    <!-- Banking & Regulatory Information Section -->
    <div class="section-header">
      <h6 class="section-title">
        <i class="feather icon-shield me-1"></i>
        Banking & Regulatory Information
      </h6>
    </div>

    <div class="row">
      <!-- Established Date -->
      <div class="col-md-6 mb-3">
        <label for="establishedDate" class="form-label">Established Date</label>
        <input 
          type="date" 
          id="establishedDate"
          class="form-control"
          [class.is-invalid]="hasError('established_date')"
          formControlName="established_date">
        
        <div *ngIf="hasError('established_date')" class="invalid-feedback">
          {{ getErrorMessage('established_date') }}
        </div>
      </div>

      <!-- License Number -->
      <div class="col-md-6 mb-3">
        <label for="licenseNumber" class="form-label">License Number</label>
        <input 
          type="text" 
          id="licenseNumber"
          class="form-control"
          [class.is-invalid]="hasError('license_number')"
          formControlName="license_number"
          placeholder="Enter license number"
          maxlength="50">
        
        <div *ngIf="hasError('license_number')" class="invalid-feedback">
          {{ getErrorMessage('license_number') }}
        </div>
      </div>
    </div>

    <!-- Regulatory Body -->
    <div class="mb-3">
      <label for="regulatoryBody" class="form-label">Regulatory Body</label>
      <select 
        id="regulatoryBody"
        class="form-select"
        [class.is-invalid]="hasError('regulatory_body')"
        formControlName="regulatory_body">
        
        <option value="">Select Regulatory Body</option>
        <option *ngFor="let body of regulatoryBodies" [value]="body">
          {{ body }}
        </option>
      </select>
      
      <div *ngIf="hasError('regulatory_body')" class="invalid-feedback">
        {{ getErrorMessage('regulatory_body') }}
      </div>
    </div>

    <!-- Banking Codes Section -->
    <div class="section-header">
      <h6 class="section-title">
        <i class="feather icon-credit-card me-1"></i>
        Banking Codes
      </h6>
    </div>

    <div class="row">
      <!-- IFSC Code -->
      <div class="col-md-4 mb-3">
        <label for="ifscCode" class="form-label">IFSC Code</label>
        <input 
          type="text" 
          id="ifscCode"
          class="form-control banking-code"
          [class.is-invalid]="hasError('ifsc_code')"
          formControlName="ifsc_code"
          placeholder="SBIN0001234"
          maxlength="11"
          (blur)="formatBankingCode('ifsc_code')">
        
        <div *ngIf="hasError('ifsc_code')" class="invalid-feedback">
          {{ getErrorMessage('ifsc_code') }}
        </div>
        
        <div class="form-text">
          4 letters + 7 digits (e.g., SBIN0001234)
        </div>
      </div>

      <!-- SWIFT Code -->
      <div class="col-md-4 mb-3">
        <label for="swiftCode" class="form-label">SWIFT Code</label>
        <input 
          type="text" 
          id="swiftCode"
          class="form-control banking-code"
          [class.is-invalid]="hasError('swift_code')"
          formControlName="swift_code"
          placeholder="SBININBB"
          maxlength="11"
          (blur)="formatBankingCode('swift_code')">
        
        <div *ngIf="hasError('swift_code')" class="invalid-feedback">
          {{ getErrorMessage('swift_code') }}
        </div>
        
        <div class="form-text">
          8 or 11 characters (e.g., SBININBB)
        </div>
      </div>

      <!-- MICR Code -->
      <div class="col-md-4 mb-3">
        <label for="micrCode" class="form-label">MICR Code</label>
        <input 
          type="text" 
          id="micrCode"
          class="form-control banking-code"
          [class.is-invalid]="hasError('micr_code')"
          formControlName="micr_code"
          placeholder="*********"
          maxlength="9">
        
        <div *ngIf="hasError('micr_code')" class="invalid-feedback">
          {{ getErrorMessage('micr_code') }}
        </div>
        
        <div class="form-text">
          9 digits (e.g., *********)
        </div>
      </div>
    </div>

    <!-- Status -->
    <div class="mb-3">
      <label class="form-label">
        Status <span class="text-danger">*</span>
      </label>
      
      <div class="form-check form-switch">
        <input 
          class="form-check-input" 
          type="checkbox" 
          id="instituteStatus"
          formControlName="is_active">
        <label class="form-check-label" for="instituteStatus">
          <span *ngIf="instituteForm.get('is_active')?.value" class="text-success">
            <i class="feather icon-check-circle me-1"></i>
            Active
          </span>
          <span *ngIf="!instituteForm.get('is_active')?.value" class="text-muted">
            <i class="feather icon-pause-circle me-1"></i>
            Inactive
          </span>
        </label>
      </div>
      
      <div class="form-text">
        Active institutes are available for transactions and appear in dropdowns
      </div>
    </div>

  </form>
</div>

<div class="modal-footer">
  <div class="d-flex justify-content-between w-100">
    <!-- Reset Button (for edit mode) -->
    <div>
      <button 
        *ngIf="isEditMode" 
        type="button" 
        class="btn btn-outline-warning"
        (click)="reset()"
        [disabled]="saving">
        <i class="feather icon-refresh-cw me-1"></i>
        Reset
      </button>
    </div>
    
    <!-- Action Buttons -->
    <div class="d-flex gap-2">
      <button 
        type="button" 
        class="btn btn-secondary" 
        (click)="cancel()"
        [disabled]="saving">
        <i class="feather icon-x me-1"></i>
        Cancel
      </button>
      
      <button 
        type="button" 
        class="btn btn-primary" 
        (click)="save()"
        [disabled]="instituteForm.invalid || saving">
        
        <!-- Loading Spinner -->
        <span *ngIf="saving" class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </span>
        
        <!-- Save Icon -->
        <i *ngIf="!saving" class="feather icon-save me-1"></i>
        
        {{ getSaveButtonText() }}
      </button>
    </div>
  </div>
</div>
