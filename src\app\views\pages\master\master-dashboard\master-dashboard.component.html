<div class="row">
  <!-- Page Header -->
  <div class="col-12 mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h4 class="page-title mb-1">Master Data Management</h4>
        <p class="text-muted mb-0">Manage all master data entities from a centralized dashboard</p>
      </div>
      <button class="btn btn-outline-primary" (click)="refreshStats()" [disabled]="loading">
        <i data-feather="refresh-cw" class="icon-sm me-1" [class.fa-spin]="loading" appFeatherIcon></i>
        Refresh
      </button>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="col-12 mb-4">
    <div class="row">
      <!-- Total Count Card -->
      <div class="col-md-3 mb-3">
        <div class="card summary-card">
          <div class="card-body text-center">
            <div class="summary-icon bg-primary text-white mb-3">
              <i data-feather="database" class="icon-lg" appFeatherIcon></i>
            </div>
            <h3 class="mb-1">{{ totalMasterDataCount }}</h3>
            <p class="text-muted mb-0">Total Records</p>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="col-md-9">
        <div class="card">
          <div class="card-body">
            <h6 class="card-title mb-3">Quick Overview</h6>
            <div class="row">
              <div class="col-md-4 mb-2" *ngFor="let stat of masterDataStats.slice(0, 3)">
                <div class="d-flex align-items-center">
                  <div class="stat-icon me-2" [ngClass]="getBgColorClass(stat.color)">
                    <i [attr.data-feather]="stat.icon" class="icon-sm text-white" appFeatherIcon></i>
                  </div>
                  <div>
                    <h6 class="mb-0">{{ stat.count }}</h6>
                    <small class="text-muted">{{ stat.label }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Master Data Cards -->
  <div class="col-12">
    <div class="row">
      <div class="col-lg-4 col-md-6 mb-4" *ngFor="let stat of masterDataStats">
        <div class="card master-data-card h-100" [routerLink]="stat.route" 
             [class.loading]="stat.loading" [class.error]="stat.error">
          <div class="card-body">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-start mb-3">
              <div class="master-data-icon" [ngClass]="getBgColorClass(stat.color)">
                <i [attr.data-feather]="stat.icon" class="icon-md text-white" appFeatherIcon></i>
              </div>
              <div class="text-end">
                <span *ngIf="stat.loading" class="spinner-border spinner-border-sm text-muted"></span>
                <span *ngIf="stat.error" class="badge bg-danger">Error</span>
                <span *ngIf="!stat.loading && !stat.error" class="badge" [ngClass]="getBgColorClass(stat.color)">
                  {{ stat.count }}
                </span>
              </div>
            </div>

            <!-- Content -->
            <div class="master-data-content">
              <h5 class="card-title mb-2">{{ stat.label }}</h5>
              <p class="text-muted mb-3">{{ stat.description }}</p>
              
              <!-- Stats -->
              <div class="master-data-stats" *ngIf="!stat.loading && !stat.error">
                <div class="d-flex justify-content-between align-items-center">
                  <span class="text-muted">Total Records:</span>
                  <strong [ngClass]="getColorClass(stat.color)">{{ stat.count }}</strong>
                </div>
              </div>

              <!-- Error Message -->
              <div class="alert alert-danger py-2" *ngIf="stat.error">
                <small>{{ stat.error }}</small>
              </div>
            </div>

            <!-- Footer -->
            <div class="master-data-footer mt-3">
              <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                  <i data-feather="clock" class="icon-xs me-1" appFeatherIcon></i>
                  Last updated: {{ getCurrentDate() | date:'short' }}
                </small>
                <div class="action-buttons">
                  <i data-feather="arrow-right" class="icon-sm" [ngClass]="getColorClass(stat.color)" appFeatherIcon></i>
                </div>
              </div>
            </div>
          </div>

          <!-- Loading Overlay -->
          <div class="loading-overlay" *ngIf="stat.loading">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="col-12 mt-4">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Quick Actions</h6>
        <div class="row">
          <div class="col-md-3 mb-2">
            <button class="btn btn-outline-primary w-100" routerLink="/master/master-data">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add New Record
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-outline-success w-100" routerLink="/master/master-data">
              <i data-feather="upload" class="icon-sm me-1" appFeatherIcon></i>
              Bulk Upload
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-outline-info w-100" routerLink="/master/master-data">
              <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
              Export Data
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-outline-secondary w-100" (click)="refreshStats()">
              <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
              Refresh All
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity (Placeholder) -->
  <div class="col-12 mt-4">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Recent Activity</h6>
        <div class="text-center py-4">
          <i data-feather="activity" class="icon-lg text-muted mb-3" appFeatherIcon></i>
          <p class="text-muted mb-0">Activity tracking will be available soon</p>
          <small class="text-muted">Track recent changes, additions, and modifications to master data</small>
        </div>
      </div>
    </div>
  </div>
</div>
