import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import * as ExcelJS from 'exceljs';

export interface LeaveReportSearchParams {
  from_date: string;
  to_date: string;
  leave_type?: string;
  employee_id?: string;
  status?: string;
}

export interface LeaveReportData {
  id: string;
  employee_id: string;
  employee_name: string;
  employee_code: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  days: number;
  status: string;
  reason: string;
  approved_by?: string;
  approved_date?: string;
}

export interface LeaveReportResponseData {
  total_count: number;
  items: LeaveReportData[];
}

export interface LeaveReportResponse {
  success: boolean;
  data: LeaveReportResponseData;
  message?: string;
}

export const LEAVE_TYPES = [
  { value: 'casual_leave', label: 'Casual Leave' },
  { value: 'sick_leave', label: 'Sick Leave' },
  { value: 'privilege_leave', label: 'Privilege Leave' },
  { value: 'comp_off', label: 'Comp Off' },
  { value: 'lwp', label: 'Leave Without Pay' },
  { value: 'outdoor', label: 'Outdoor' },

];

@Injectable({
  providedIn: 'root'
})
export class LeaveReportService {
  private baseUrl = `${environment.apiUrl}/api/v1/leave-report`;

  constructor(private http: HttpClient) { }

  /**
   * Search leave reports based on criteria using POST /api/v1/leave-report/search
   */
  searchLeaveReports(params: LeaveReportSearchParams): Observable<LeaveReportResponse> {
    console.log('LeaveReportService: Searching leave reports with params:', params);

    // Prepare the payload for POST request
    const payload = {
      from_date: params.from_date,
      to_date: params.to_date,
      ...(params.leave_type && { leave_type: params.leave_type }),
      ...(params.employee_id && { employee_id: params.employee_id }),
      ...(params.status && { status: params.status })
    };

    const url = `${this.baseUrl}/search`;
    console.log('LeaveReportService: API URL:', url);
    console.log('LeaveReportService: Payload:', payload);

    return this.http.post<LeaveReportResponse>(url, payload);
  }

  /**
   * Export leave report data to Excel
   */
  async exportToExcel(data: LeaveReportData[], filename: string = 'leave_report'): Promise<void> {
    console.log('LeaveReportService: Exporting to Excel:', { data, filename });

    if (!data || data.length === 0) {
      console.warn('LeaveReportService: No data to export');
      return;
    }

    // Convert data to Excel format with the specified headers
    const excelData = data.map(row => ({
      'EmployeeId': row.employee_code || '',
      'UserName': row.employee_name || '',
      'CreatedDate': row.start_date || '', // Using start_date as created date
      'FromDate': row.start_date || '',
      'ToDate': row.end_date || '',
      'Reason': row.reason || '',
      'Status': row.status || '',
      'Attendant_Type': this.getLeaveTypeCode(row.leave_type) || '',
      'ApprovedDate': row.approved_date || '',
      'ApproverRemark': '', // Not available in current data structure
      'TotalLeave': row.days || 0,
      'ApproverName': row.approved_by || ''
    }));

    // Create workbook and worksheet using ExcelJS
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Leave Report');

    // Define columns with headers and widths
    worksheet.columns = [
      { header: 'EmployeeId', key: 'EmployeeId', width: 12 },
      { header: 'UserName', key: 'UserName', width: 20 },
      { header: 'CreatedDate', key: 'CreatedDate', width: 12 },
      { header: 'StartDate', key: 'StartDate', width: 12 },
      { header: 'EndDate', key: 'EndDate', width: 12 },
      { header: 'Reason', key: 'Reason', width: 30 },
      { header: 'Status', key: 'Status', width: 12 },
      { header: 'Attendant_Type', key: 'Attendant_Type', width: 15 },
      { header: 'ApprovedDate', key: 'ApprovedDate', width: 12 },
      { header: 'ApproverRemark', key: 'ApproverRemark', width: 20 },
      { header: 'TotalLeave', key: 'TotalLeave', width: 10 },
      { header: 'ApproverName', key: 'ApproverName', width: 20 }
    ];

    // Add data rows
    worksheet.addRows(excelData);

    // Generate Excel file buffer
    const excelBuffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.xlsx`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('LeaveReportService: Excel file downloaded successfully');
    } else {
      console.error('LeaveReportService: Browser does not support file download');
    }
  }

  /**
   * Export leave report data to CSV
   */
  exportToCSV(data: LeaveReportData[], filename: string = 'leave_report'): void {
    console.log('LeaveReportService: Exporting to CSV:', { data, filename });

    if (!data || data.length === 0) {
      console.warn('LeaveReportService: No data to export');
      return;
    }

    // Define CSV headers matching the Excel format shown in the image
    const headers = [
      'EmployeeId',
      'UserName',
      'CreatedDate',
      'FromDate',
      'ToDate',
      'Reason',
      'Status',
      'Attendant_Type',
      'ApprovedDate',
      'ApproverRemark',
      'TotalLeaves',
      'ApproverName'
    ];

    // Convert data to CSV format
    const csvContent = [
      headers.join(','), // Header row
      ...data.map(row => [
        this.escapeCsvValue(row.employee_code || ''), // Employee (using employee_code)
        this.escapeCsvValue(row.employee_name || ''), // UserName (using employee_name)
        this.escapeCsvValue(row.start_date || ''), // CreatedDate
        this.escapeCsvValue(row.start_date || ''), // StartDate
        this.escapeCsvValue(row.end_date || ''), // EndDate
        this.escapeCsvValue(row.reason || ''), // Reason
        this.escapeCsvValue(row.status || ''), // Status
        this.escapeCsvValue(this.getLeaveTypeCode(row.leave_type) || ''), // Attendant_Type (using abbreviated codes)
        this.escapeCsvValue(row.approved_date || ''), // ApprovedDate
        this.escapeCsvValue(''), // ApproverRemark (not available)
        (row.days || 0).toString(), // TotalLeave (using days)
        this.escapeCsvValue(row.approved_by || '') // ApprovedBy
      ].join(','))
    ].join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('LeaveReportService: CSV file downloaded successfully');
    } else {
      console.error('LeaveReportService: Browser does not support file download');
    }
  }

  /**
   * Get abbreviated code for leave type
   */
  private getLeaveTypeCode(leaveType: string): string {
    const leaveTypeCodes: { [key: string]: string } = {
      'privilege_leave': 'PL',
      'sick_leave': 'SL',
      'casual_leave': 'CL',
      'comp_off': 'CO',
      'compensatory_off': 'CO',
      'leave_without_pay': 'LWP',

      'outdoor_duty': 'OD',

   
    };

    return leaveTypeCodes[leaveType] || leaveType;
  }

  /**
   * Escape CSV values to handle commas, quotes, and newlines
   */
  private escapeCsvValue(value: string): string {
    if (!value) return '';

    // If value contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }

    return value;
  }

  /**
   * Get attendance report (placeholder for future implementation)
   */
  getAttendanceReport(fromDate: string, toDate: string): Observable<any> {
    console.log('LeaveReportService: Getting attendance report:', { fromDate, toDate });
    
    // For now, return a mock response
    return new Observable(observer => {
      setTimeout(() => {
        observer.next({
          success: true,
          message: 'Attendance report functionality will be implemented soon',
          data: []
        });
        observer.complete();
      }, 1000);
    });
  }
}
