{"name": "nobleui-angular-demo1", "version": "3.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve", "start:prod": "ng serve --configuration=production", "start:custom-host": "ng serve --host 0.0.0.0 --allowed-hosts bizzcorp.antllp.com --allowed-hosts .antllp.com", "build": "ng build", "build:prod": "ng build --configuration=production", "build:prod:legacy": "NODE_OPTIONS=--openssl-legacy-provider ng build --configuration=production", "watch": "ng build --watch --configuration development", "rtl": "webpack --config rtl.config.js", "test": "ng test", "validate-env": "node scripts/validate-env.js", "docker:prod": "docker-compose -f docker-compose-bizzcorp.yml up -d bizzcorp-angular-prod", "docker:dev": "docker-compose -f docker-compose-bizzcorp.yml --profile dev up -d bizzcorp-angular-dev", "docker:build": "docker-compose -f docker-compose-bizzcorp.yml build", "docker:stop": "docker-compose -f docker-compose-bizzcorp.yml down", "docker:logs": "docker-compose -f docker-compose-bizzcorp.yml logs -f", "docker:clean": "docker-compose -f docker-compose-bizzcorp.yml down && docker system prune -f"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/material": "^20.0.1", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@fullcalendar/angular": "^6.1.15", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@narik/custom-validators": "^12.0.0", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@ng-select/ng-select": "^13.7.1", "@popperjs/core": "^2.11.8", "@rg-software/angular-archwizard": "^17.0.0", "@siemens/ngx-datatable": "^22.4.1", "@sweetalert2/ngx-sweetalert2": "^12.4.0", "@swimlane/ngx-datatable": "^21.0.0-alpha.1", "@types/exceljs": "^0.5.3", "animate.css": "^4.1.1", "apexcharts": "^3.53.0", "bootstrap": "^5.3.3", "exceljs": "^4.4.0", "feather-icons": "^4.29.2", "metismenujs": "^1.4.0", "ng-apexcharts": "1.11.0", "ngx-chips": "^3.0.0", "ngx-clipboard": "^16.0.0", "ngx-color-picker": "^17.0.0", "ngx-dropzone-wrapper": "^17.0.0", "ngx-highlightjs": "^12.0.0", "ngx-image-cropper": "^8.0.0", "ngx-mask": "^18.0.0", "ngx-owl-carousel-o": "^18.0.0", "ngx-quill": "^26.0.8", "ngx-scrollbar": "^15.1.3", "nxt-sortablejs": "^18.0.0", "quill": "^2.0.2", "rxjs": "~7.8.0", "sortablejs": "^1.15.3", "sweetalert2": "^11.4.8", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.1", "@angular/cli": "^18.2.1", "@angular/compiler-cli": "^18.2.0", "@angular/localize": "^18.2.0", "@types/feather-icons": "^4.29.4", "@types/jasmine": "~5.1.0", "@types/sortablejs": "^1.15.8", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "mini-css-extract-plugin": "^2.9.2", "rtlcss-webpack-plugin": "^4.0.7", "typescript": "~5.5.2", "webpack-cli": "^5.1.4"}}