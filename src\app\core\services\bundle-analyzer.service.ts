import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface BundleChunk {
  name: string;
  size: number;
  gzipSize?: number;
  loadTime: number;
  type: 'initial' | 'async' | 'vendor';
  modules: string[];
  dependencies: string[];
}

export interface BundleAnalysis {
  totalSize: number;
  totalGzipSize: number;
  chunkCount: number;
  duplicateModules: string[];
  unusedModules: string[];
  largestChunks: BundleChunk[];
  recommendations: string[];
}

export interface PerformanceMetrics {
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
}

/**
 * Bundle Analyzer Service
 * 
 * Provides comprehensive bundle analysis, performance monitoring,
 * and optimization recommendations for code splitting strategies.
 */
@Injectable({
  providedIn: 'root'
})
export class BundleAnalyzerService {
  private chunksSubject = new BehaviorSubject<BundleChunk[]>([]);
  private metricsSubject = new BehaviorSubject<PerformanceMetrics | null>(null);
  
  public chunks$ = this.chunksSubject.asObservable();
  public metrics$ = this.metricsSubject.asObservable();

  private loadedChunks = new Map<string, BundleChunk>();
  private performanceObserver?: PerformanceObserver;

  constructor() {
    console.log('📊 BundleAnalyzerService: Initialized for bundle analysis');
    this.initializeAnalysis();
  }

  /**
   * Initialize bundle analysis
   */
  private initializeAnalysis(): void {
    this.setupPerformanceMonitoring();
    this.analyzeInitialChunks();
    this.monitorChunkLoading();
  }

  /**
   * Analyze current bundle structure
   */
  analyzeBundleStructure(): Observable<BundleAnalysis> {
    return new Observable(observer => {
      const chunks = Array.from(this.loadedChunks.values());
      
      const analysis: BundleAnalysis = {
        totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
        totalGzipSize: chunks.reduce((sum, chunk) => sum + (chunk.gzipSize || 0), 0),
        chunkCount: chunks.length,
        duplicateModules: this.findDuplicateModules(chunks),
        unusedModules: this.findUnusedModules(chunks),
        largestChunks: this.getLargestChunks(chunks, 5),
        recommendations: this.generateRecommendations(chunks)
      };

      observer.next(analysis);
      observer.complete();
    });
  }

  /**
   * Monitor chunk loading performance
   */
  private monitorChunkLoading(): void {
    if ('performance' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource' && this.isJavaScriptChunk(entry.name)) {
            this.recordChunkLoad(entry as PerformanceResourceTiming);
          }
        });
      });

      this.performanceObserver.observe({ entryTypes: ['resource'] });
      console.log('📊 BundleAnalyzerService: Chunk loading monitoring enabled');
    }
  }

  /**
   * Record chunk loading metrics
   */
  private recordChunkLoad(entry: PerformanceResourceTiming): void {
    const chunkName = this.extractChunkName(entry.name);
    const size = entry.transferSize || entry.encodedBodySize || 0;
    
    const chunk: BundleChunk = {
      name: chunkName,
      size: size,
      gzipSize: entry.transferSize,
      loadTime: entry.duration,
      type: this.determineChunkType(chunkName),
      modules: [], // Would be populated from webpack stats
      dependencies: []
    };

    this.loadedChunks.set(chunkName, chunk);
    this.updateChunksSubject();

    // Log performance insights
    if (entry.duration > 1000) {
      console.warn(`⚠️ Slow chunk: ${chunkName} took ${entry.duration.toFixed(2)}ms to load`);
    } else if (entry.duration < 100) {
      console.log(`⚡ Fast chunk: ${chunkName} loaded in ${entry.duration.toFixed(2)}ms`);
    }
  }

  /**
   * Analyze initial chunks from current page
   */
  private analyzeInitialChunks(): void {
    // Analyze scripts already loaded on the page
    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src;
      if (this.isJavaScriptChunk(src)) {
        const chunkName = this.extractChunkName(src);
        
        // Estimate size from script element (rough approximation)
        const estimatedSize = 50000; // Default estimate
        
        const chunk: BundleChunk = {
          name: chunkName,
          size: estimatedSize,
          loadTime: 0, // Already loaded
          type: this.determineChunkType(chunkName),
          modules: [],
          dependencies: []
        };

        this.loadedChunks.set(chunkName, chunk);
      }
    });

    this.updateChunksSubject();
  }

  /**
   * Setup performance monitoring for Core Web Vitals
   */
  private setupPerformanceMonitoring(): void {
    // Monitor Core Web Vitals
    this.measureCoreWebVitals();
    
    // Monitor custom performance metrics
    this.measureCustomMetrics();
  }

  /**
   * Measure Core Web Vitals
   */
  private measureCoreWebVitals(): void {
    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcp) {
        this.updateMetrics({ firstContentfulPaint: fcp.startTime });
      }
    });
    fcpObserver.observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.updateMetrics({ largestContentfulPaint: lastEntry.startTime });
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const firstInput = entries[0] as any;
      const fid = firstInput.processingStart - firstInput.startTime;
      this.updateMetrics({ firstInputDelay: fid });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.updateMetrics({ cumulativeLayoutShift: clsValue });
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });
  }

  /**
   * Measure custom performance metrics
   */
  private measureCustomMetrics(): void {
    // Time to Interactive (simplified calculation)
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const tti = navigation.domInteractive - (navigation as any).navigationStart;
        this.updateMetrics({ timeToInteractive: tti });
      }
    }, 1000);
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(newMetrics: Partial<PerformanceMetrics>): void {
    const currentMetrics = this.metricsSubject.value || {
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
      timeToInteractive: 0
    };

    const updatedMetrics = { ...currentMetrics, ...newMetrics };
    this.metricsSubject.next(updatedMetrics);
  }

  /**
   * Find duplicate modules across chunks
   */
  private findDuplicateModules(chunks: BundleChunk[]): string[] {
    const moduleCount = new Map<string, number>();
    
    chunks.forEach(chunk => {
      chunk.modules.forEach(module => {
        moduleCount.set(module, (moduleCount.get(module) || 0) + 1);
      });
    });

    return Array.from(moduleCount.entries())
      .filter(([_, count]) => count > 1)
      .map(([module, _]) => module);
  }

  /**
   * Find unused modules
   */
  private findUnusedModules(chunks: BundleChunk[]): string[] {
    // This would require webpack stats to determine unused modules
    // For now, return empty array
    return [];
  }

  /**
   * Get largest chunks
   */
  private getLargestChunks(chunks: BundleChunk[], count: number): BundleChunk[] {
    return chunks
      .sort((a, b) => b.size - a.size)
      .slice(0, count);
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(chunks: BundleChunk[]): string[] {
    const recommendations: string[] = [];
    
    // Check for large chunks
    const largeChunks = chunks.filter(chunk => chunk.size > 500000);
    if (largeChunks.length > 0) {
      recommendations.push(`Consider splitting large chunks: ${largeChunks.map(c => c.name).join(', ')}`);
    }

    // Check for many small chunks
    const smallChunks = chunks.filter(chunk => chunk.size < 10000);
    if (smallChunks.length > 10) {
      recommendations.push('Consider combining small chunks to reduce HTTP requests');
    }

    // Check for slow loading chunks
    const slowChunks = chunks.filter(chunk => chunk.loadTime > 1000);
    if (slowChunks.length > 0) {
      recommendations.push(`Optimize slow-loading chunks: ${slowChunks.map(c => c.name).join(', ')}`);
    }

    return recommendations;
  }

  /**
   * Utility methods
   */
  private isJavaScriptChunk(url: string): boolean {
    return url.includes('.js') && (url.includes('chunk-') || url.includes('main-') || url.includes('polyfills-'));
  }

  private extractChunkName(url: string): string {
    const match = url.match(/([^\/]+)\.js$/);
    return match ? match[1] : 'unknown';
  }

  private determineChunkType(chunkName: string): 'initial' | 'async' | 'vendor' {
    if (chunkName.includes('main') || chunkName.includes('polyfills')) {
      return 'initial';
    } else if (chunkName.includes('vendor') || chunkName.includes('node_modules')) {
      return 'vendor';
    } else {
      return 'async';
    }
  }

  private updateChunksSubject(): void {
    const chunks = Array.from(this.loadedChunks.values());
    this.chunksSubject.next(chunks);
  }

  /**
   * Get bundle analysis summary
   */
  getBundleSummary(): {
    totalChunks: number;
    totalSize: string;
    averageLoadTime: number;
    performanceScore: number;
  } {
    const chunks = Array.from(this.loadedChunks.values());
    const totalSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
    const averageLoadTime = chunks.length > 0 
      ? chunks.reduce((sum, chunk) => sum + chunk.loadTime, 0) / chunks.length 
      : 0;

    // Simple performance score calculation
    const performanceScore = this.calculatePerformanceScore(chunks);

    return {
      totalChunks: chunks.length,
      totalSize: this.formatBytes(totalSize),
      averageLoadTime: Math.round(averageLoadTime),
      performanceScore
    };
  }

  private calculatePerformanceScore(chunks: BundleChunk[]): number {
    // Simple scoring based on chunk sizes and load times
    let score = 100;
    
    chunks.forEach(chunk => {
      if (chunk.size > 500000) score -= 10; // Large chunk penalty
      if (chunk.loadTime > 1000) score -= 5; // Slow load penalty
    });

    return Math.max(0, score);
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
