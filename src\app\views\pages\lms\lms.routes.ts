import { Routes } from '@angular/router';
import { PermissionGuard } from '../../../core/guards/permission.guard';
// LMS Specific Guards for detailed permission checking
import { ApplyLeaveGuard } from '../../../core/guards/lms/apply-leave.guard';
import { ApproveLeaveGuard } from '../../../core/guards/lms/approve-leave.guard';
import { LeavePolicyGuard } from '../../../core/guards/lms/leave-policy.guard';

import { AssignCompoffGuard } from '../../../core/guards/lms/assign-compoff.guard';
import { CompoffRequestGuard } from '../../../core/guards/lms/compoff-request.guard';
import { SalarySlipGuard } from '../../../core/guards/lms/salary-slip.guard';
import { ViewEmployeeGuard } from '../../../core/guards/lms/view-employee.guard';
import { ReportsGuard } from '../../../core/guards/lms/reports.guard';

/**
 * 🎯 LMS ACCESS CONTROL STRATEGY
 *
 * PERMISSION-BASED ACCESS CONTROL using actual API permissions:
 *
 * API PERMISSIONS:
 * 👤 EMPLOYEE: apply_leave, view_leave_balance
 * 👥 MANAGER: apply_leave, approve_leaves, assign_compoff, view_leave_balance
 * 🔑 ADMIN/HR: user_roles:read, user_roles:assign, leave_report:read, attendance_report:read, master:read
 *
 * MENU ACCESS CONTROL:
 * 👤 EMPLOYEE: Apply Leave, Leave Policy, Comp-off Request
 * 👥 MANAGER: Apply Leave, Approve Leaves, Leave Policy, Assign Comp-off, Comp-off Request
 * 🔑 ADMIN/HR: Apply Leave, HR Admin, Reports, View Employee, Mark Attendance, Assign Comp-off, Approve Leaves, Leave Policy, Comp-off Request
 *
 * Route Guards:
 * - PermissionGuard: Permission-based route access control
 * - dynamicAuthGuard: Authentication fallback
 */

export default [
    // 🏠 Dashboard - Available to ALL AUTHENTICATED USERS (no specific permissions required)
    {
        path: 'dashboard',
        loadComponent: () => import('./dashboard/dashboard.component').then(c => c.DashboardComponent),
        canActivate: [PermissionGuard],
        data: {
            // No permission requirements - available to all authenticated users
        }
    },

    // 👥 Employee List - STRICT: Only with 'employees:read' permission
    {
        path: 'dashboard/employeelist',
        loadComponent: () => import('./dashboard/employee-list/employee-list.component').then(c => c.EmployeeListComponent),
        canActivate: [ViewEmployeeGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'employees:read' // STRICT permission for viewing employee list
        }
    },

    // 📝 Apply Leave - STRICT: Only with 'leave:create' permission
    {
        path: 'apply-leave',
        loadComponent: () => import('./dashboard/apply-leave/apply-leave.component').then(c => c.ApplyLeaveComponent),
        canActivate: [ApplyLeaveGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'leave:create' // STRICT permission for applying leave
        }
    },

    // ✅ Approve Leaves - STRICT: Only with 'leave:approve' permission
    {
        path: 'approve-leaves',
        loadComponent: () => import('./dashboard/approve-leaves/approve-leaves.component').then(c => c.ApproveLeavesComponent),
        canActivate: [ApproveLeaveGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'leave:approve' // STRICT permission for approving leaves
        }
    },

    // 📋 Leave Policy - STRICT: Only with 'leave:read' permission
    {
        path: 'leave-policy',
        loadComponent: () => import('./dashboard/leave-policy/leave-policy.component').then(c => c.LeavePolicyComponent),
        canActivate: [LeavePolicyGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'leave:read' // STRICT permission for reading leave policy
        }
    },

    // 🎯 Assign Comp-off - STRICT: Only with 'leave:assign_compoff' permission
    {
        path: 'assign-comp-off',
        loadComponent: () => import('./dashboard/dashboard.component').then(c => c.DashboardComponent),
        canActivate: [AssignCompoffGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'leave:assign_compoff', // STRICT permission for assigning comp-off
            modalAction: 'assign-comp-off' // Tell dashboard to open assign comp-off modal
        }
    },



    // 🎯 Comp-off Request - STRICT: Only with 'leave:request_compoff' permission
    {
        path: 'comp-off-request',
        loadComponent: () => import('./dashboard/dashboard.component').then(c => c.DashboardComponent),
        canActivate: [CompoffRequestGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'leave:request_compoff', // STRICT permission for comp-off requests
            modalAction: 'comp-off-request' // Tell dashboard to open comp-off request modal
        }
    },

    // 📊 Attendance - Available to MANAGERS/ADMINS with attendance report permission
    {
        path: 'attendance',
        loadComponent: () => import('./dashboard/mark-attendance/mark-attendance.component').then(c => c.MarkAttendanceComponent),
        canActivate: [PermissionGuard],
        data: {
            permission: 'attendance_report:read', // API permission for viewing attendance
            menuItems: ['Mark Attendance'] // Required menu access
        }
    },

    // 👥 HR Admin - Available to users with user role management permissions
    {
        path: 'hr-admin',
        loadComponent: () => import('./dashboard/dashboard.component').then(c => c.DashboardComponent),
        canActivate: [PermissionGuard],
        data: {
            permission: 'user_roles:read', // API permission for HR admin functions
            menuItems: ['HR Admin'], // Required menu access
            modalAction: 'hr-admin' // Tell dashboard to show HR admin section
        }
    },

    // 📊 Reports - Available to users with report reading permissions
    {
        path: 'dashboard/reports',
        loadComponent: () => import('./dashboard/reports/reports.component').then(c => c.ReportsComponent),
        canActivate: [ReportsGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'leave_report:read' // STRICT permission for reports
        }
    },

    // 👤 View Employee - Available to users with master data permission
    {
        path: 'view-employee',
        loadComponent: () => import('./dashboard/dashboard.component').then(c => c.DashboardComponent),
        canActivate: [PermissionGuard],
        data: {
            permission: 'master:read', // API permission for viewing employees
            menuItems: ['View Employee'], // Required menu access
            modalAction: 'employee-list' // Tell dashboard to show employee list
        }
    },

    // ⏰ Mark Attendance - Available to users with master data permission
    {
        path: 'mark-attendance',
        loadComponent: () => import('./dashboard/mark-attendance/mark-attendance.component').then(c => c.MarkAttendanceComponent),
        canActivate: [PermissionGuard],
        data: {
            permission: 'master:read', // API permission for marking attendance
            menuItems: ['Mark Attendance'] // Required menu access
        }
    },

    // 💰 Salary Slip Generation - STRICT: Only with 'salary:read' permission (Admin only)
    {
        path: 'salary-slip',
        loadComponent: () => import('./salary-slip/salary-slip.component').then(c => c.SalarySlipComponent),
        canActivate: [SalarySlipGuard], // Dedicated guard with detailed console logging
        data: {
            permission: 'salary:read' // STRICT permission for salary slip generation (Admin only)
        }
    },

    // 💰 Generate Salary Slip - STRICT: Only with 'salary:read' permission (Admin only)
    {
        path: 'generate-salary-slip',
        loadComponent: () => import('./generate-salary-slip/generate-salary-slip.component').then(c => c.GenerateSalarySlipComponent),
        canActivate: [SalarySlipGuard], // Using same guard as salary slip since it's related functionality
        data: {
            permission: 'salary:read' // STRICT permission for salary slip generation (Admin only)
        }
    },

    // 📅 Calendar - Available to users with calendar permissions
    {
        path: 'calendar',
        loadComponent: () => import('../apps/calendar/calendar.component').then(c => c.CalendarComponent),
        // canActivate: [PermissionGuard], // Temporarily commented out for debugging
        data: {
            // permission: 'calendar:read', // Temporarily commented out for debugging
            // alternativePermissions: ['calendar:employee'] // Temporarily commented out for debugging
        }
    },



    // 🔄 Legacy routes for backward compatibility
    {
        path: 'dashboard/applyleave',
        redirectTo: 'apply-leave'
    },
    {
        path: 'dashboard/approveleaves',
        redirectTo: 'approve-leaves'
    },
    {
        path: 'dashboard/leavepolicy',
        redirectTo: 'leave-policy'
    },

    // 🏠 Default route - redirect to dashboard
    {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
    }
] as Routes;


