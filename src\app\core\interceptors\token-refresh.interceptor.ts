import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, EMPTY } from 'rxjs';
import { catchError, switchMap, filter, take, finalize } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable()
export class TokenRefreshInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Skip token refresh for auth endpoints to avoid infinite loops
    if (this.isAuthEndpoint(req.url)) {
      return next.handle(req);
    }

    // Check if token needs refresh before making the request
    if (this.shouldRefreshToken()) {
      console.log('🔄 INTERCEPTOR: Token needs refresh (≤20s remaining), refreshing before request');
      return this.handleTokenRefresh(req, next);
    }

    // Add auth header and proceed with request
    const authReq = this.addAuthHeader(req);
    
    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        // Handle 401 errors by attempting token refresh
        if (error.status === 401 && !this.isAuthEndpoint(req.url)) {
          console.log('🔄 INTERCEPTOR: 401 error received, attempting token refresh');
          return this.handleTokenRefresh(req, next);
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Check if token should be refreshed (20 seconds or less remaining)
   */
  private shouldRefreshToken(): boolean {
    // Check if user has been logged out manually
    if (localStorage.getItem('user_logged_out') === 'true') {
      return false;
    }

    const user = this.authService.currentUserValue;
    if (!user?.token_expiry || !user?.refresh_token) {
      return false;
    }

    const timeUntilExpiry = user.token_expiry - Date.now();
    const secondsUntilExpiry = Math.round(timeUntilExpiry / 1000);

    // Refresh if 20 seconds or less remaining
    const shouldRefresh = secondsUntilExpiry <= 20 && secondsUntilExpiry > 0;

    if (shouldRefresh) {
      console.log(`🔄 INTERCEPTOR: Token expires in ${secondsUntilExpiry}s, triggering refresh`);
    }

    return shouldRefresh;
  }

  /**
   * Handle token refresh process
   */
  private handleTokenRefresh(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Double-check logout state before starting refresh
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('🚪 INTERCEPTOR: User logged out, skipping token refresh');
      return throwError(() => new Error('User has been logged out'));
    }

    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      console.log('🔄 INTERCEPTOR: Starting token refresh process');

      return this.authService.refreshToken().pipe(
        switchMap((response: any) => {
          console.log('✅ INTERCEPTOR: Token refresh successful');
          this.isRefreshing = false;
          this.refreshTokenSubject.next(response.access_token);
          
          // Retry the original request with new token
          const authReq = this.addAuthHeader(req);
          return next.handle(authReq);
        }),
        catchError((error) => {
          console.error('❌ INTERCEPTOR: Token refresh failed:', error);
          this.isRefreshing = false;
          this.refreshTokenSubject.next(null);
          
          // If refresh fails, logout user
          this.authService.logout(false, 'Session expired - unable to refresh token');
          return throwError(() => error);
        }),
        finalize(() => {
          this.isRefreshing = false;
        })
      );
    } else {
      // If refresh is already in progress, wait for it to complete
      console.log('🔄 INTERCEPTOR: Refresh already in progress, waiting...');
      return this.refreshTokenSubject.pipe(
        filter(token => token !== null),
        take(1),
        switchMap(() => {
          const authReq = this.addAuthHeader(req);
          return next.handle(authReq);
        })
      );
    }
  }

  /**
   * Add Authorization header to request
   */
  private addAuthHeader(req: HttpRequest<any>): HttpRequest<any> {
    const user = this.authService.currentUserValue;
    if (user?.access_token) {
      return req.clone({
        setHeaders: {
          Authorization: `Bearer ${user.access_token}`
        }
      });
    }
    return req;
  }

  /**
   * Check if URL is an auth endpoint
   */
  private isAuthEndpoint(url: string): boolean {
    const authEndpoints = [
      '/api/v1/auth/login',
      '/api/v1/auth/refresh',
      '/api/v1/auth/logout',
      '/api/v1/auth/register'
    ];
    
    return authEndpoints.some(endpoint => url.includes(endpoint));
  }
}
