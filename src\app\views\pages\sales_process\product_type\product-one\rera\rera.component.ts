import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';

interface ReraFormData {
  reraNo: string;
  mobile: string;
  website: string;

  // Architect details
  architectCompanyName: string;
  architectName: string;
  architectMobile: string;
  architectEmail: string;

  // Engineer details
  engineerCompanyName: string;
  engineerName: string;
  engineerMobile: string;
  engineerEmail: string;

  // CA details
  caCompanyName: string;
  caName: string;
  caMobile: string;
  caEmail: string;

  // Structural Surveyor details
  ssCompanyName: string;
  ssName: string;
  ssMobile: string;
  ssEmail: string;

  // Advocate details
  advocateCompanyName: string;
  advocateName: string;
  advocateMobile: string;
  advocateEmail: string;

  // Searcher details
  searcherCompanyName: string;
  searcherName: string;
  searcherMobile: string;
  searcherEmail: string;

  // Sole Seller details
  soleSellerCompanyName: string;
  soleSellerName: string;
  soleSellerMobile: string;
  soleSellerEmail: string;

  // Channel Partner details
  channelPartnerCompanyName: string;
  channelPartnerName: string;
  channelPartnerMobile: string;
  channelPartnerEmail: string;

  // Other details
  otherCompanyName: string;
  otherName: string;
  otherMobile: string;
  otherEmail: string;

  // Encumbrance
  encumbrance: string;

  // Conditional fields for Encumbrance = YES
  nameOfInstitute: string;
  loanAmount: number;
  bankName: string;
  branch: string;
}

@Component({
  selector: 'app-rera',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './rera.component.html',
  styleUrl: './rera.component.scss'
})
export class ReraComponent implements OnInit {
  reraForm: FormGroup;
  isFormSubmitted = false;

  @Output() formDataChange = new EventEmitter<any>();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.initForm();
  }

  initForm() {
    this.reraForm = this.formBuilder.group({
      // RERA details
      reraNo: ['', Validators.required],
      mobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      website: [''],

      // Architect details
      architectCompanyName: [''],
      architectName: [''],
      architectMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      architectEmail: ['', Validators.email],

      // Engineer details
      engineerCompanyName: [''],
      engineerName: [''],
      engineerMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      engineerEmail: ['', Validators.email],

      // CA details
      caCompanyName: [''],
      caName: [''],
      caMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      caEmail: ['', Validators.email],

      // Structural Surveyor details
      ssCompanyName: [''],
      ssName: [''],
      ssMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      ssEmail: ['', Validators.email],

      // Advocate details
      advocateCompanyName: [''],
      advocateName: [''],
      advocateMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      advocateEmail: ['', Validators.email],

      // Searcher details
      searcherCompanyName: [''],
      searcherName: [''],
      searcherMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      searcherEmail: ['', Validators.email],

      // Sole Seller details
      soleSellerCompanyName: [''],
      soleSellerName: [''],
      soleSellerMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      soleSellerEmail: ['', Validators.email],

      // Channel Partner details
      channelPartnerCompanyName: [''],
      channelPartnerName: [''],
      channelPartnerMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      channelPartnerEmail: ['', Validators.email],

      // Other details
      otherCompanyName: [''],
      otherName: [''],
      otherMobile: ['', [Validators.pattern('^[0-9]{10}$')]],
      otherEmail: ['', Validators.email],

      // Encumbrance
      encumbrance: ['', Validators.required],

      // Conditional fields for Encumbrance = YES
      nameOfInstitute: [''],
      loanAmount: [''],
      bankName: [''],
      branch: ['']
    });

    // Subscribe to form value changes and emit them to parent component
    this.reraForm.valueChanges.subscribe(value => {
      this.formDataChange.emit(value);
    });

    // Subscribe to encumbrance field changes to handle conditional validation
    this.reraForm.get('encumbrance')?.valueChanges.subscribe(value => {
      this.handleEncumbranceChange(value);
    });
  }

  // Getter for easy access to form fields
  get form() {
    return this.reraForm.controls;
  }

  onSubmit() {
    this.isFormSubmitted = true;

    // Stop here if form is invalid
    if (this.reraForm.invalid) {
      return;
    }

    // Process the form data
    const formData = this.reraForm.value;
    console.log('RERA Form submitted:', formData);

    // Here you would typically save the data to a service
    // For now, we'll just log it to the console

    // Reset the form
    this.reraForm.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values
  }

  onCancel() {
    // Reset the form
    this.reraForm.reset();
    this.isFormSubmitted = false;
    this.initForm(); // Reinitialize with default values
  }

  // Method to get the current form data
  getFormData() {
    return this.reraForm.value;
  }

  // Method to check if the form is valid
  isFormValid() {
    return this.reraForm.valid;
  }

  // Handle encumbrance field changes for conditional validation
  handleEncumbranceChange(value: string) {
    const nameOfInstituteControl = this.reraForm.get('nameOfInstitute');
    const loanAmountControl = this.reraForm.get('loanAmount');
    const bankNameControl = this.reraForm.get('bankName');
    const branchControl = this.reraForm.get('branch');

    if (value === 'yes') {
      // Make fields required when encumbrance is YES
      nameOfInstituteControl?.setValidators([Validators.required]);
      loanAmountControl?.setValidators([Validators.required, Validators.min(1)]);
      bankNameControl?.setValidators([Validators.required]);
      branchControl?.setValidators([Validators.required]);
    } else {
      // Remove validators when encumbrance is NO or not selected
      nameOfInstituteControl?.clearValidators();
      loanAmountControl?.clearValidators();
      bankNameControl?.clearValidators();
      branchControl?.clearValidators();

      // Clear the values when encumbrance is not YES
      nameOfInstituteControl?.setValue('');
      loanAmountControl?.setValue('');
      bankNameControl?.setValue('');
      branchControl?.setValue('');
    }

    // Update validity for all conditional fields
    nameOfInstituteControl?.updateValueAndValidity();
    loanAmountControl?.updateValueAndValidity();
    bankNameControl?.updateValueAndValidity();
    branchControl?.updateValueAndValidity();
  }
}
