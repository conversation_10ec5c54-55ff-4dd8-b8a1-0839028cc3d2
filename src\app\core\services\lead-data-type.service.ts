import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Lead Data Type interfaces
export interface LeadDataType {
  id: string;
  name: string;
  code: string;
  description?: string;
  field_type: 'text' | 'number' | 'email' | 'tel' | 'url' | 'textarea' | 'select' | 'multiselect' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'time' | 'file' | 'image' | 'boolean' | 'json' | 'currency' | 'percentage' | 'rating';
  data_category: 'personal' | 'contact' | 'company' | 'financial' | 'behavioral' | 'demographic' | 'geographic' | 'technical' | 'custom';
  validation_rules?: ValidationRule[];
  field_options?: FieldOption[];
  default_value?: any;
  placeholder?: string;
  help_text?: string;
  is_required: boolean;
  is_unique: boolean;
  is_searchable: boolean;
  is_filterable: boolean;
  is_sortable: boolean;
  is_exportable: boolean;
  is_pii: boolean; // Personally Identifiable Information
  display_order: number;
  group_name?: string;
  conditional_logic?: ConditionalRule[];
  formatting_rules?: FormattingRule[];
  integration_mapping?: IntegrationMapping[];
  audit_settings?: AuditSettings;
  privacy_settings?: PrivacySettings;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  usage_count?: number;
}

export interface ValidationRule {
  id: string;
  type: 'required' | 'min_length' | 'max_length' | 'pattern' | 'min_value' | 'max_value' | 'custom' | 'unique' | 'email' | 'url' | 'phone' | 'date_range' | 'file_size' | 'file_type';
  value?: any;
  message: string;
  severity: 'error' | 'warning' | 'info';
  is_active: boolean;
}

export interface FieldOption {
  id: string;
  value: any;
  label: string;
  description?: string;
  color?: string;
  icon?: string;
  is_default: boolean;
  is_active: boolean;
  display_order: number;
  conditional_display?: ConditionalRule;
}

export interface ConditionalRule {
  id: string;
  field_id: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in' | 'is_empty' | 'is_not_empty';
  value: any;
  action: 'show' | 'hide' | 'enable' | 'disable' | 'require' | 'optional';
}

export interface FormattingRule {
  id: string;
  type: 'mask' | 'transform' | 'prefix' | 'suffix' | 'currency' | 'percentage' | 'date_format' | 'number_format';
  pattern?: string;
  options?: any;
}

export interface IntegrationMapping {
  id: string;
  system: string;
  field_name: string;
  mapping_type: 'direct' | 'transform' | 'lookup';
  transform_function?: string;
  is_bidirectional: boolean;
}

export interface AuditSettings {
  track_changes: boolean;
  track_access: boolean;
  retention_days?: number;
  anonymize_after_days?: number;
}

export interface PrivacySettings {
  is_sensitive: boolean;
  encryption_required: boolean;
  access_level: 'public' | 'internal' | 'restricted' | 'confidential';
  consent_required: boolean;
  retention_policy?: string;
}

export interface LeadDataTypeCreate {
  name: string;
  code: string;
  description?: string;
  field_type: 'text' | 'number' | 'email' | 'tel' | 'url' | 'textarea' | 'select' | 'multiselect' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'time' | 'file' | 'image' | 'boolean' | 'json' | 'currency' | 'percentage' | 'rating';
  data_category: 'personal' | 'contact' | 'company' | 'financial' | 'behavioral' | 'demographic' | 'geographic' | 'technical' | 'custom';
  validation_rules?: ValidationRule[];
  field_options?: FieldOption[];
  default_value?: any;
  placeholder?: string;
  help_text?: string;
  is_required?: boolean;
  is_unique?: boolean;
  is_searchable?: boolean;
  is_filterable?: boolean;
  is_sortable?: boolean;
  is_exportable?: boolean;
  is_pii?: boolean;
  display_order?: number;
  group_name?: string;
  conditional_logic?: ConditionalRule[];
  formatting_rules?: FormattingRule[];
  integration_mapping?: IntegrationMapping[];
  audit_settings?: AuditSettings;
  privacy_settings?: PrivacySettings;
  is_active?: boolean;
}

export interface LeadDataTypeUpdate {
  name?: string;
  code?: string;
  description?: string;
  field_type?: 'text' | 'number' | 'email' | 'tel' | 'url' | 'textarea' | 'select' | 'multiselect' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'time' | 'file' | 'image' | 'boolean' | 'json' | 'currency' | 'percentage' | 'rating';
  data_category?: 'personal' | 'contact' | 'company' | 'financial' | 'behavioral' | 'demographic' | 'geographic' | 'technical' | 'custom';
  validation_rules?: ValidationRule[];
  field_options?: FieldOption[];
  default_value?: any;
  placeholder?: string;
  help_text?: string;
  is_required?: boolean;
  is_unique?: boolean;
  is_searchable?: boolean;
  is_filterable?: boolean;
  is_sortable?: boolean;
  is_exportable?: boolean;
  is_pii?: boolean;
  display_order?: number;
  group_name?: string;
  conditional_logic?: ConditionalRule[];
  formatting_rules?: FormattingRule[];
  integration_mapping?: IntegrationMapping[];
  audit_settings?: AuditSettings;
  privacy_settings?: PrivacySettings;
  is_active?: boolean;
}

export interface LeadDataTypeStatistics {
  total_data_types: number;
  active_data_types: number;
  inactive_data_types: number;
  data_types_by_category: { [category: string]: number };
  data_types_by_field_type: { [type: string]: number };
  required_fields_count: number;
  pii_fields_count: number;
  most_used_data_types: LeadDataType[];
  recent_data_types: LeadDataType[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class LeadDataTypeService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/lead-data-types/`;
  private leadDataTypesSubject = new BehaviorSubject<LeadDataType[]>([]);
  public leadDataTypes$ = this.leadDataTypesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all lead data types with optional filtering and pagination (returns APIResponse)
   */
  getLeadDataTypesWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    field_type?: string;
    data_category?: string;
    is_required?: boolean;
    is_pii?: boolean;
    include_deleted?: boolean;
  }): Observable<APIResponse<LeadDataType[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<LeadDataType[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.leadDataTypesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all lead data types (backward compatibility) - returns LeadDataType[] directly
   */
  getLeadDataTypes(skip: number = 0, limit: number = 100, filter: any = {}): Observable<LeadDataType[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search || filter.name,
      is_active: filter.is_active,
      field_type: filter.field_type,
      data_category: filter.data_category
    };

    return this.getLeadDataTypesWithResponse(params).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  /**
   * Get lead data type by ID
   */
  getLeadDataTypeById(id: string): Observable<APIResponse<LeadDataType>> {
    return this.http.get<APIResponse<LeadDataType>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get a specific lead data type by ID (backward compatibility)
   */
  getLeadDataType(id: string): Observable<LeadDataType | null> {
    return this.getLeadDataTypeById(id).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return null;
      })
    );
  }

  /**
   * Get all active lead data types for dropdown usage
   */
  getActiveLeadDataTypes(): Observable<LeadDataType[]> {
    return this.getLeadDataTypes(0, 1000, { is_active: true });
  }

  /**
   * Create new lead data type
   */
  createLeadDataType(leadDataType: LeadDataTypeCreate): Observable<APIResponse<LeadDataType>> {
    return this.http.post<APIResponse<LeadDataType>>(this.baseUrl, leadDataType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadDataTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update lead data type
   */
  updateLeadDataType(id: string, leadDataType: LeadDataTypeUpdate): Observable<APIResponse<LeadDataType>> {
    return this.http.put<APIResponse<LeadDataType>>(`${this.baseUrl}${id}`, leadDataType)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadDataTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete lead data type
   */
  deleteLeadDataType(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadDataTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted lead data type
   */
  restoreLeadDataType(id: string): Observable<APIResponse<LeadDataType>> {
    return this.http.post<APIResponse<LeadDataType>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadDataTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get lead data type statistics
   */
  getLeadDataTypeStatistics(): Observable<APIResponse<LeadDataTypeStatistics>> {
    return this.http.get<APIResponse<LeadDataTypeStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload lead data types
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshLeadDataTypes();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Validate field configuration
   */
  validateFieldConfiguration(config: any): Observable<APIResponse<any>> {
    return this.http.post<APIResponse<any>>(`${this.baseUrl}validate-config`, { config })
      .pipe(catchError(this.handleError));
  }

  /**
   * Generate form schema from data types
   */
  generateFormSchema(dataTypeIds: string[]): Observable<APIResponse<any>> {
    return this.http.post<APIResponse<any>>(`${this.baseUrl}generate-schema`, { data_type_ids: dataTypeIds })
      .pipe(catchError(this.handleError));
  }

  /**
   * Refresh lead data types data
   */
  refreshLeadDataTypes(): void {
    this.getLeadDataTypesWithResponse().subscribe();
  }

  /**
   * Clear lead data types cache
   */
  clearCache(): void {
    this.leadDataTypesSubject.next([]);
  }

  /**
   * Get field types
   */
  getFieldTypes(): { value: string; label: string; description: string; icon: string }[] {
    return [
      { value: 'text', label: 'Text Input', description: 'Single line text input', icon: 'type' },
      { value: 'textarea', label: 'Text Area', description: 'Multi-line text input', icon: 'align-left' },
      { value: 'number', label: 'Number', description: 'Numeric input with validation', icon: 'hash' },
      { value: 'email', label: 'Email', description: 'Email address input with validation', icon: 'mail' },
      { value: 'tel', label: 'Phone', description: 'Phone number input', icon: 'phone' },
      { value: 'url', label: 'URL', description: 'Website URL input', icon: 'link' },
      { value: 'select', label: 'Dropdown', description: 'Single selection dropdown', icon: 'chevron-down' },
      { value: 'multiselect', label: 'Multi-Select', description: 'Multiple selection dropdown', icon: 'list' },
      { value: 'radio', label: 'Radio Buttons', description: 'Single choice radio buttons', icon: 'circle' },
      { value: 'checkbox', label: 'Checkboxes', description: 'Multiple choice checkboxes', icon: 'check-square' },
      { value: 'date', label: 'Date', description: 'Date picker', icon: 'calendar' },
      { value: 'datetime', label: 'Date & Time', description: 'Date and time picker', icon: 'clock' },
      { value: 'time', label: 'Time', description: 'Time picker', icon: 'watch' },
      { value: 'file', label: 'File Upload', description: 'File upload field', icon: 'upload' },
      { value: 'image', label: 'Image Upload', description: 'Image upload with preview', icon: 'image' },
      { value: 'boolean', label: 'Yes/No', description: 'Boolean toggle or checkbox', icon: 'toggle-left' },
      { value: 'json', label: 'JSON Editor', description: 'JSON data editor', icon: 'code' },
      { value: 'currency', label: 'Currency', description: 'Currency input with formatting', icon: 'dollar-sign' },
      { value: 'percentage', label: 'Percentage', description: 'Percentage input with validation', icon: 'percent' },
      { value: 'rating', label: 'Rating', description: 'Star rating or numeric rating', icon: 'star' }
    ];
  }

  /**
   * Get data categories
   */
  getDataCategories(): { value: string; label: string; description: string; color: string }[] {
    return [
      { value: 'personal', label: 'Personal Information', description: 'Personal details and identity', color: '#007bff' },
      { value: 'contact', label: 'Contact Information', description: 'Phone, email, address details', color: '#28a745' },
      { value: 'company', label: 'Company Information', description: 'Business and organization details', color: '#17a2b8' },
      { value: 'financial', label: 'Financial Information', description: 'Budget, revenue, financial data', color: '#ffc107' },
      { value: 'behavioral', label: 'Behavioral Data', description: 'Actions, preferences, interactions', color: '#fd7e14' },
      { value: 'demographic', label: 'Demographic Data', description: 'Age, gender, location, education', color: '#6f42c1' },
      { value: 'geographic', label: 'Geographic Data', description: 'Location, region, territory', color: '#20c997' },
      { value: 'technical', label: 'Technical Data', description: 'System, device, technical details', color: '#6c757d' },
      { value: 'custom', label: 'Custom Data', description: 'Custom business-specific fields', color: '#e83e8c' }
    ];
  }

  /**
   * Get field type label
   */
  getFieldTypeLabel(fieldType: string): string {
    const types = this.getFieldTypes();
    const typeObj = types.find(t => t.value === fieldType);
    return typeObj ? typeObj.label : fieldType;
  }

  /**
   * Get data category label
   */
  getDataCategoryLabel(category: string): string {
    const categories = this.getDataCategories();
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? categoryObj.label : category;
  }

  /**
   * Get data category color
   */
  getDataCategoryColor(category: string): string {
    const categories = this.getDataCategories();
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? categoryObj.color : '#6c757d';
  }

  /**
   * Get field type badge class
   */
  getFieldTypeBadgeClass(fieldType: string): string {
    const badgeClasses = {
      'text': 'badge bg-primary',
      'textarea': 'badge bg-primary',
      'number': 'badge bg-success',
      'email': 'badge bg-info',
      'tel': 'badge bg-info',
      'url': 'badge bg-info',
      'select': 'badge bg-warning',
      'multiselect': 'badge bg-warning',
      'radio': 'badge bg-warning',
      'checkbox': 'badge bg-warning',
      'date': 'badge bg-secondary',
      'datetime': 'badge bg-secondary',
      'time': 'badge bg-secondary',
      'file': 'badge bg-dark',
      'image': 'badge bg-dark',
      'boolean': 'badge bg-success',
      'json': 'badge bg-danger',
      'currency': 'badge bg-warning',
      'percentage': 'badge bg-info',
      'rating': 'badge bg-warning'
    };
    return badgeClasses[fieldType as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    const badgeClasses = {
      'personal': 'badge bg-primary',
      'contact': 'badge bg-success',
      'company': 'badge bg-info',
      'financial': 'badge bg-warning',
      'behavioral': 'badge bg-danger',
      'demographic': 'badge bg-secondary',
      'geographic': 'badge bg-success',
      'technical': 'badge bg-dark',
      'custom': 'badge bg-light text-dark'
    };
    return badgeClasses[category as keyof typeof badgeClasses] || 'badge bg-secondary';
  }

  /**
   * Get validation rules summary
   */
  getValidationRulesSummary(leadDataType: LeadDataType): string {
    if (!leadDataType.validation_rules?.length) {
      return 'No validation rules';
    }

    const activeRules = leadDataType.validation_rules.filter(rule => rule.is_active);
    if (activeRules.length === 0) {
      return 'No active rules';
    }

    const ruleTypes = activeRules.map(rule => rule.type);
    const uniqueTypes = [...new Set(ruleTypes)];

    if (uniqueTypes.length <= 2) {
      return uniqueTypes.join(', ');
    }

    return `${uniqueTypes.slice(0, 2).join(', ')} +${uniqueTypes.length - 2} more`;
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Lead Data Type service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
