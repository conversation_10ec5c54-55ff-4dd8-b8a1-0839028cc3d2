import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import { 
  User, 
  UserCreate, 
  UserUpdate, 
  UserFormData, 
  UserFormErrors 
} from '../../../../core/models/user.model';
import { catchError, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    FeatherIconDirective
  ],
  templateUrl: './user-form.component.html',
  styleUrls: ['./user-form.component.scss']
})
export class UserFormComponent implements OnInit {
  // Form
  userForm: FormGroup;
  formErrors: UserFormErrors = {};

  // Mode
  isEditMode = false;
  userId: string | null = null;
  currentUser: User | null = null;

  // Loading states
  loading = false;
  submitting = false;
  loadingUser = false;

  // Form validation
  submitted = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private authService: AuthService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.checkRouteParams();
  }

  /**
   * Initialize the form with validation
   */
  private initializeForm(): void {
    this.userForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      first_name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      last_name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      is_active: [true],
      is_superuser: [false],
      password: [''],
      confirm_password: ['']
    });

    // Add password validation for create mode
    this.userForm.get('password')?.setValidators([
      Validators.required,
      Validators.minLength(8),
      this.passwordStrengthValidator
    ]);

    this.userForm.get('confirm_password')?.setValidators([
      Validators.required,
      this.passwordMatchValidator.bind(this)
    ]);

    // Watch for form changes to clear errors
    this.userForm.valueChanges.subscribe(() => {
      if (this.submitted) {
        this.validateForm();
      }
    });
  }

  /**
   * Check route parameters to determine mode
   */
  private checkRouteParams(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.userId = params['id'];
        this.loadUser();
        this.updateValidatorsForEditMode();
      } else {
        this.isEditMode = false;
        this.updateValidatorsForCreateMode();
      }
    });
  }

  /**
   * Update validators for edit mode
   */
  private updateValidatorsForEditMode(): void {
    // Password is optional in edit mode
    this.userForm.get('password')?.setValidators([
      Validators.minLength(8),
      this.passwordStrengthValidator
    ]);
    this.userForm.get('confirm_password')?.setValidators([
      this.passwordMatchValidator.bind(this)
    ]);
    this.userForm.get('password')?.updateValueAndValidity();
    this.userForm.get('confirm_password')?.updateValueAndValidity();
  }

  /**
   * Update validators for create mode
   */
  private updateValidatorsForCreateMode(): void {
    // Password is required in create mode
    this.userForm.get('password')?.setValidators([
      Validators.required,
      Validators.minLength(8),
      this.passwordStrengthValidator
    ]);
    this.userForm.get('confirm_password')?.setValidators([
      Validators.required,
      this.passwordMatchValidator.bind(this)
    ]);
    this.userForm.get('password')?.updateValueAndValidity();
    this.userForm.get('confirm_password')?.updateValueAndValidity();
  }

  /**
   * Load user data for editing
   */
  private loadUser(): void {
    if (!this.userId) return;

    this.loadingUser = true;
    this.userService.getUserById(this.userId)
      .pipe(
        catchError(error => {
          console.error('Failed to load user:', error);
          this.showErrorMessage('Failed to load user data. Please try again.');
          this.router.navigate(['/user-management/users']);
          return of(null);
        }),
        finalize(() => {
          this.loadingUser = false;
        })
      )
      .subscribe(user => {
        if (user) {
          this.currentUser = user;
          this.populateForm(user);
        }
      });
  }

  /**
   * Populate form with user data
   */
  private populateForm(user: User): void {
    this.userForm.patchValue({
      email: user.email,
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      is_active: user.is_active ?? true,
      is_superuser: user.is_superuser ?? false,
      password: '',
      confirm_password: ''
    });
  }

  /**
   * Password strength validator
   */
  private passwordStrengthValidator(control: AbstractControl): {[key: string]: any} | null {
    const value = control.value;
    if (!value) return null;

    const hasNumber = /[0-9]/.test(value);
    const hasUpper = /[A-Z]/.test(value);
    const hasLower = /[a-z]/.test(value);
    const hasSpecial = /[#?!@$%^&*-]/.test(value);

    const valid = hasNumber && hasUpper && hasLower && hasSpecial;
    if (!valid) {
      return { 'passwordStrength': true };
    }
    return null;
  }

  /**
   * Password match validator
   */
  private passwordMatchValidator(control: AbstractControl): {[key: string]: any} | null {
    const password = this.userForm?.get('password')?.value;
    const confirmPassword = control.value;

    if (password && confirmPassword && password !== confirmPassword) {
      return { 'passwordMismatch': true };
    }
    return null;
  }

  /**
   * Validate entire form and set errors
   */
  private validateForm(): void {
    this.formErrors = {};

    // Email validation
    const emailControl = this.userForm.get('email');
    if (emailControl?.errors) {
      if (emailControl.errors['required']) {
        this.formErrors.email = 'Email is required';
      } else if (emailControl.errors['email']) {
        this.formErrors.email = 'Please enter a valid email address';
      }
    }

    // First name validation
    const firstNameControl = this.userForm.get('first_name');
    if (firstNameControl?.errors) {
      if (firstNameControl.errors['required']) {
        this.formErrors.first_name = 'First name is required';
      } else if (firstNameControl.errors['minlength']) {
        this.formErrors.first_name = 'First name must be at least 2 characters';
      } else if (firstNameControl.errors['maxlength']) {
        this.formErrors.first_name = 'First name cannot exceed 50 characters';
      }
    }

    // Last name validation
    const lastNameControl = this.userForm.get('last_name');
    if (lastNameControl?.errors) {
      if (lastNameControl.errors['required']) {
        this.formErrors.last_name = 'Last name is required';
      } else if (lastNameControl.errors['minlength']) {
        this.formErrors.last_name = 'Last name must be at least 2 characters';
      } else if (lastNameControl.errors['maxlength']) {
        this.formErrors.last_name = 'Last name cannot exceed 50 characters';
      }
    }

    // Password validation
    const passwordControl = this.userForm.get('password');
    if (passwordControl?.errors) {
      if (passwordControl.errors['required']) {
        this.formErrors.password = 'Password is required';
      } else if (passwordControl.errors['minlength']) {
        this.formErrors.password = 'Password must be at least 8 characters';
      } else if (passwordControl.errors['passwordStrength']) {
        this.formErrors.password = 'Password must contain uppercase, lowercase, number, and special character';
      }
    }

    // Confirm password validation
    const confirmPasswordControl = this.userForm.get('confirm_password');
    if (confirmPasswordControl?.errors) {
      if (confirmPasswordControl.errors['required']) {
        this.formErrors.confirm_password = 'Please confirm your password';
      } else if (confirmPasswordControl.errors['passwordMismatch']) {
        this.formErrors.confirm_password = 'Passwords do not match';
      }
    }
  }

  /**
   * Check if field has error
   */
  hasError(fieldName: keyof UserFormErrors): boolean {
    return !!this.formErrors[fieldName];
  }

  /**
   * Get error message for field
   */
  getError(fieldName: keyof UserFormErrors): string {
    return this.formErrors[fieldName] || '';
  }

  /**
   * Check if field is invalid
   */
  isFieldInvalid(fieldName: string): boolean {
    const field = this.userForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched || this.submitted));
  }

  /**
   * Submit form
   */
  onSubmit(): void {
    this.submitted = true;
    this.validateForm();

    if (this.userForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    if (this.isEditMode) {
      this.updateUser();
    } else {
      this.createUser();
    }
  }

  /**
   * Mark all form fields as touched
   */
  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Create new user
   */
  private createUser(): void {
    this.submitting = true;
    const formData = this.userForm.value as UserFormData;

    const userData: UserCreate = {
      email: formData.email,
      first_name: formData.first_name,
      last_name: formData.last_name,
      is_active: formData.is_active,
      is_superuser: formData.is_superuser,
      password: formData.password
    };

    this.userService.createUser(userData)
      .pipe(
        catchError(error => {
          console.error('Failed to create user:', error);
          this.handleApiError(error);
          return of(null);
        }),
        finalize(() => {
          this.submitting = false;
        })
      )
      .subscribe(user => {
        if (user) {
          this.showSuccessMessage('User created successfully!');
          this.router.navigate(['/user-management/users']);
        }
      });
  }

  /**
   * Update existing user
   */
  private updateUser(): void {
    if (!this.userId) return;

    this.submitting = true;
    const formData = this.userForm.value as UserFormData;

    const userData: UserUpdate = {
      email: formData.email,
      first_name: formData.first_name,
      last_name: formData.last_name,
      is_active: formData.is_active,
      is_superuser: formData.is_superuser
    };

    this.userService.updateUser(this.userId, userData)
      .pipe(
        catchError(error => {
          console.error('Failed to update user:', error);
          this.handleApiError(error);
          return of(null);
        }),
        finalize(() => {
          this.submitting = false;
        })
      )
      .subscribe(user => {
        if (user) {
          this.showSuccessMessage('User updated successfully!');
          this.router.navigate(['/user-management/users']);
        }
      });
  }

  /**
   * Handle API errors
   */
  private handleApiError(error: any): void {
    let errorMessage = 'An error occurred. Please try again.';

    if (error.status === 422 && error.error?.detail) {
      // Validation errors from API
      if (Array.isArray(error.error.detail)) {
        errorMessage = error.error.detail.map((d: any) => d.msg || JSON.stringify(d)).join(', ');
      } else {
        errorMessage = error.error.detail;
      }
    } else if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    this.formErrors.general = errorMessage;
    this.showErrorMessage(errorMessage);
  }

  /**
   * Cancel form and navigate back
   */
  onCancel(): void {
    this.router.navigate(['/user-management/users']);
  }

  /**
   * Reset form
   */
  onReset(): void {
    this.submitted = false;
    this.formErrors = {};
    
    if (this.isEditMode && this.currentUser) {
      this.populateForm(this.currentUser);
    } else {
      this.userForm.reset({
        is_active: true,
        is_superuser: false
      });
    }
  }

  /**
   * Check if user can perform action
   */
  canPerformAction(action: 'create' | 'update'): boolean {
    switch (action) {
      case 'create':
        return this.authService.hasPermission('users:create');
      case 'update':
        return this.authService.hasPermission('users:update');
      default:
        return false;
    }
  }

  /**
   * Show success message
   */
  private showSuccessMessage(message: string): void {
    Swal.fire({
      icon: 'success',
      title: 'Success!',
      text: message,
      timer: 3000,
      showConfirmButton: false
    });
  }

  /**
   * Show error message
   */
  private showErrorMessage(message: string): void {
    Swal.fire({
      icon: 'error',
      title: 'Error!',
      text: message,
      confirmButtonText: 'OK'
    });
  }
}
