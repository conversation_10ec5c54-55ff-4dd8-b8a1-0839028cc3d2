export interface Associate {
  id?: number;
  name: string;
  category: string;
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
  deleted_at?: string | null;
  // Additional fields from API
  company_name?: string;
  location_id?: string;
  location_name?: string;
  sub_location?: string;
  profession_type?: string;
  profession_id?: string;
  profession_name?: string;
  _originalId?: string;
}

export interface AssociateResponse {
  items: Associate[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface AssociateCreate {
  associate_name: string;
  company_name: string;
  location_id: string;
  sub_location?: string | null;
  profession_type: string;
  profession_id?: string | null;
  status: boolean;
}

export interface AssociateUpdate {
  associate_name?: string;
  company_name?: string;
  location_id?: string;
  sub_location?: string | null;
  profession_type?: string;
  profession_id?: string | null;
  status?: boolean;
}
