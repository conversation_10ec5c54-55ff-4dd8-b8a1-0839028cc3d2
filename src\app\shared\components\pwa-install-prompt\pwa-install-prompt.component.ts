import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PWAService } from '../../../core/services/pwa.service';
import { Subject, combineLatest } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

/**
 * PWA Install Prompt Component
 * 
 * Shows installation prompts and update notifications for the PWA.
 * Handles app installation, updates, and offline status.
 */
@Component({
  selector: 'app-pwa-install-prompt',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],
  template: `
    <!-- Install Prompt -->
    <div *ngIf="showInstallPrompt" class="pwa-prompt install-prompt">
      <div class="prompt-content">
        <div class="prompt-icon">
          <i class="fas fa-download"></i>
        </div>
        <div class="prompt-text">
          <h6 class="prompt-title">Install BizzCorp App</h6>
          <p class="prompt-description">
            Get faster access and work offline by installing our app on your device.
          </p>
        </div>
        <div class="prompt-actions">
          <button class="btn btn-primary btn-sm" (click)="installApp()">
            <i class="fas fa-download me-1"></i>
            Install
          </button>
          <button class="btn btn-outline-secondary btn-sm" (click)="dismissInstallPrompt()">
            <i class="fas fa-times me-1"></i>
            Not Now
          </button>
        </div>
      </div>
    </div>

    <!-- Update Available Prompt -->
    <div *ngIf="showUpdatePrompt" class="pwa-prompt update-prompt">
      <div class="prompt-content">
        <div class="prompt-icon">
          <i class="fas fa-sync-alt"></i>
        </div>
        <div class="prompt-text">
          <h6 class="prompt-title">Update Available</h6>
          <p class="prompt-description">
            A new version of the app is available with improvements and bug fixes.
          </p>
        </div>
        <div class="prompt-actions">
          <button class="btn btn-success btn-sm" (click)="updateApp()">
            <i class="fas fa-sync-alt me-1"></i>
            Update Now
          </button>
          <button class="btn btn-outline-secondary btn-sm" (click)="dismissUpdatePrompt()">
            <i class="fas fa-times me-1"></i>
            Later
          </button>
        </div>
      </div>
    </div>

    <!-- Offline Status -->
    <div *ngIf="showOfflineStatus" class="pwa-prompt offline-prompt">
      <div class="prompt-content">
        <div class="prompt-icon">
          <i class="fas fa-wifi-slash"></i>
        </div>
        <div class="prompt-text">
          <h6 class="prompt-title">You're Offline</h6>
          <p class="prompt-description">
            Some features may be limited. Changes will sync when you're back online.
          </p>
        </div>
        <div class="prompt-actions">
          <button class="btn btn-outline-info btn-sm" (click)="dismissOfflineStatus()">
            <i class="fas fa-check me-1"></i>
            Got It
          </button>
        </div>
      </div>
    </div>

    <!-- Installation Success -->
    <div *ngIf="showInstallSuccess" class="pwa-prompt success-prompt">
      <div class="prompt-content">
        <div class="prompt-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="prompt-text">
          <h6 class="prompt-title">App Installed!</h6>
          <p class="prompt-description">
            BizzCorp has been installed successfully. You can now access it from your home screen.
          </p>
        </div>
        <div class="prompt-actions">
          <button class="btn btn-success btn-sm" (click)="dismissInstallSuccess()">
            <i class="fas fa-check me-1"></i>
            Great!
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pwa-prompt {
      position: fixed;
      top: 20px;
      right: 20px;
      max-width: 400px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      border: 1px solid #e5e7eb;
      z-index: 9998;
      animation: slideIn 0.3s ease-out;
    }

    .pwa-prompt.install-prompt {
      border-left: 4px solid #3b82f6;
    }

    .pwa-prompt.update-prompt {
      border-left: 4px solid #10b981;
    }

    .pwa-prompt.offline-prompt {
      border-left: 4px solid #f59e0b;
    }

    .pwa-prompt.success-prompt {
      border-left: 4px solid #10b981;
    }

    .prompt-content {
      padding: 20px;
      display: flex;
      align-items: flex-start;
      gap: 16px;
    }

    .prompt-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }

    .install-prompt .prompt-icon {
      background: #dbeafe;
      color: #3b82f6;
    }

    .update-prompt .prompt-icon {
      background: #d1fae5;
      color: #10b981;
    }

    .offline-prompt .prompt-icon {
      background: #fef3c7;
      color: #f59e0b;
    }

    .success-prompt .prompt-icon {
      background: #d1fae5;
      color: #10b981;
    }

    .prompt-text {
      flex: 1;
      min-width: 0;
    }

    .prompt-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #111827;
    }

    .prompt-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #6b7280;
      line-height: 1.5;
    }

    .prompt-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 6px 12px;
      font-size: 13px;
      border-radius: 6px;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      font-weight: 500;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }

    .btn-primary:hover {
      background: #2563eb;
      border-color: #2563eb;
    }

    .btn-success {
      background: #10b981;
      color: white;
      border-color: #10b981;
    }

    .btn-success:hover {
      background: #059669;
      border-color: #059669;
    }

    .btn-outline-secondary {
      background: transparent;
      color: #6b7280;
      border-color: #d1d5db;
    }

    .btn-outline-secondary:hover {
      background: #f9fafb;
      color: #374151;
    }

    .btn-outline-info {
      background: transparent;
      color: #0ea5e9;
      border-color: #0ea5e9;
    }

    .btn-outline-info:hover {
      background: #0ea5e9;
      color: white;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @media (max-width: 480px) {
      .pwa-prompt {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
      }

      .prompt-content {
        padding: 16px;
        flex-direction: column;
        text-align: center;
      }

      .prompt-actions {
        justify-content: center;
      }
    }
  `]
})
export class PWAInstallPromptComponent implements OnInit, OnDestroy {
  showInstallPrompt = false;
  showUpdatePrompt = false;
  showOfflineStatus = false;
  showInstallSuccess = false;

  private destroy$ = new Subject<void>();
  private installPromptDismissed = false;
  private updatePromptDismissed = false;
  private offlineStatusDismissed = false;

  constructor(
    private pwaService: PWAService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.setupPWAPrompts();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupPWAPrompts(): void {
    // Monitor installable status
    this.pwaService.installable$
      .pipe(takeUntil(this.destroy$))
      .subscribe(installable => {
        if (installable && !this.installPromptDismissed && !this.pwaService.isInstalled()) {
          // Show install prompt after a delay
          setTimeout(() => {
            this.showInstallPrompt = true;
            this.cdr.markForCheck();
          }, 3000);
        }
      });

    // Monitor update availability
    this.pwaService.updateAvailable$
      .pipe(takeUntil(this.destroy$))
      .subscribe(updateAvailable => {
        if (updateAvailable && !this.updatePromptDismissed) {
          this.showUpdatePrompt = true;
          this.cdr.markForCheck();
        }
      });

    // Monitor online/offline status
    this.pwaService.isOnline$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isOnline => {
        if (!isOnline && !this.offlineStatusDismissed) {
          this.showOfflineStatus = true;
          this.cdr.markForCheck();
        } else if (isOnline) {
          this.showOfflineStatus = false;
          this.offlineStatusDismissed = false;
          this.cdr.markForCheck();
        }
      });
  }

  async installApp(): Promise<void> {
    const installed = await this.pwaService.promptInstall();
    
    if (installed) {
      this.showInstallPrompt = false;
      this.showInstallSuccess = true;
      
      // Auto-dismiss success message after 5 seconds
      setTimeout(() => {
        this.showInstallSuccess = false;
        this.cdr.markForCheck();
      }, 5000);
    } else {
      this.dismissInstallPrompt();
    }
    
    this.cdr.markForCheck();
  }

  dismissInstallPrompt(): void {
    this.showInstallPrompt = false;
    this.installPromptDismissed = true;
    this.cdr.markForCheck();
  }

  async updateApp(): Promise<void> {
    await this.pwaService.applyUpdate();
    this.showUpdatePrompt = false;
    this.cdr.markForCheck();
  }

  dismissUpdatePrompt(): void {
    this.showUpdatePrompt = false;
    this.updatePromptDismissed = true;
    this.cdr.markForCheck();
  }

  dismissOfflineStatus(): void {
    this.showOfflineStatus = false;
    this.offlineStatusDismissed = true;
    this.cdr.markForCheck();
  }

  dismissInstallSuccess(): void {
    this.showInstallSuccess = false;
    this.cdr.markForCheck();
  }
}
