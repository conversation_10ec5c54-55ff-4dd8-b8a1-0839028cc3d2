// Institute Form Component Styles

.modal-header {
  background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
  color: white;
  border-bottom: none;
  
  .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    
    i {
      opacity: 0.9;
    }
  }
  
  .btn-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    opacity: 1;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.modal-body {
  padding: 2rem;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-footer {
  background: var(--bs-gray-50);
  border-top: 1px solid var(--bs-gray-200);
  padding: 1.5rem 2rem;
}

// Section headers
.section-header {
  margin: 2rem 0 1rem 0;
  
  &:first-child {
    margin-top: 0;
  }
  
  .section-title {
    color: var(--bs-gray-700);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid var(--bs-primary);
    padding-bottom: 0.5rem;
    margin-bottom: 0;
    
    i {
      color: var(--bs-primary);
      font-size: 0.875rem;
    }
  }
}

// Form styling
.form-label {
  font-weight: 500;
  color: var(--bs-gray-700);
  margin-bottom: 0.5rem;
  
  .text-danger {
    color: var(--bs-danger) !important;
  }
}

.form-control,
.form-select {
  border: 1.5px solid var(--bs-gray-300);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  
  &:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
    outline: none;
  }
  
  &.is-invalid {
    border-color: var(--bs-danger);
    box-shadow: 0 0 0 3px rgba(var(--bs-danger-rgb), 0.1);
  }
  
  &::placeholder {
    color: var(--bs-gray-500);
    opacity: 0.8;
  }
}

// Banking code inputs
.banking-code {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
}

// Special input styling
input[type="url"],
input[type="email"],
input[type="tel"] {
  &:valid {
    border-color: var(--bs-success);
  }
}

input[type="date"] {
  &::-webkit-calendar-picker-indicator {
    color: var(--bs-primary);
    cursor: pointer;
  }
}

// Code input styling
input[style*="text-transform: uppercase"] {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  letter-spacing: 1px;
}

// URL input styling
input[type="url"] {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.form-text {
  font-size: 0.8rem;
  color: var(--bs-gray-600);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.invalid-feedback {
  font-size: 0.8rem;
  margin-top: 0.25rem;
  color: var(--bs-danger);
  display: block;
}

// Form switch styling
.form-check-input {
  width: 2.5rem;
  height: 1.25rem;
  border-radius: 1rem;
  background-color: var(--bs-gray-300);
  border: none;
  transition: all 0.2s ease;
  
  &:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba(var(--bs-success-rgb), 0.25);
  }
}

.form-check-label {
  margin-left: 0.5rem;
  font-weight: 500;
  
  .text-success {
    color: var(--bs-success) !important;
  }
  
  .text-muted {
    color: var(--bs-gray-600) !important;
  }
  
  i {
    font-size: 0.875rem;
  }
}

// Alert styling
.alert {
  border-radius: 8px;
  border: none;
  
  &.alert-danger {
    background: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
    border-left: 4px solid var(--bs-danger);
  }
  
  .btn-close {
    font-size: 0.8rem;
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  
  &.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
    border: none;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.3);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  &.btn-secondary {
    background: var(--bs-gray-200);
    border: 1px solid var(--bs-gray-300);
    color: var(--bs-gray-700);
    
    &:hover:not(:disabled) {
      background: var(--bs-gray-300);
      border-color: var(--bs-gray-400);
      color: var(--bs-gray-800);
    }
  }
  
  &.btn-outline-warning {
    border-color: var(--bs-warning);
    color: var(--bs-warning);
    
    &:hover:not(:disabled) {
      background: var(--bs-warning);
      color: white;
    }
  }
  
  i {
    font-size: 0.875rem;
  }
  
  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Row and column spacing
.row {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
  
  > [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
    
    .d-flex {
      width: 100%;
      
      &.gap-2 {
        gap: 0.5rem;
      }
    }
    
    .btn {
      flex: 1;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
    }
  }
  
  .form-control,
  .form-select {
    font-size: 16px; // Prevent zoom on iOS
  }
  
  .section-header {
    margin: 1.5rem 0 0.75rem 0;
    
    .section-title {
      font-size: 0.85rem;
    }
  }
  
  // Stack form fields on mobile
  .row > .col-md-3,
  .row > .col-md-4,
  .row > .col-md-6 {
    margin-bottom: 1rem;
  }
}

// Dark mode support
[data-theme="dark"] {
  .modal-header {
    background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.9) 100%);
  }
  
  .modal-footer {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
  }
  
  .section-header .section-title {
    color: var(--bs-gray-300);
  }
  
  .form-label {
    color: var(--bs-gray-300);
  }
  
  .form-control,
  .form-select {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-600);
    color: var(--bs-gray-300);
    
    &:focus {
      background: var(--bs-gray-800);
      border-color: var(--bs-primary);
      color: var(--bs-gray-300);
    }
    
    &::placeholder {
      color: var(--bs-gray-500);
    }
  }
  
  .form-text {
    color: var(--bs-gray-400);
  }
  
  .btn-secondary {
    background: var(--bs-gray-700);
    border-color: var(--bs-gray-600);
    color: var(--bs-gray-300);
    
    &:hover:not(:disabled) {
      background: var(--bs-gray-600);
      border-color: var(--bs-gray-500);
      color: var(--bs-gray-200);
    }
  }
}

// Animation for form validation
.form-control.is-invalid,
.form-select.is-invalid {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

// Focus management
.form-control:focus,
.form-select:focus,
.form-check-input:focus {
  outline: none;
}

// Custom scrollbar for modal body
.modal-body {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 3px;
    
    &:hover {
      background: var(--bs-gray-500);
    }
  }
}
