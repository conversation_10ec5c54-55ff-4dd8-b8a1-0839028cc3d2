<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">


        <!-- List View -->
        <div *ngIf="showListView">
          <!-- Header with title and add button -->
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h4 class="card-title">Disbursement</h4>
            <button class="btn btn-primary" (click)="showAddDetailsForm()">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add Details
            </button>
          </div>

          <!-- Search and filter controls -->
          <div class="row mb-3">
            <div class="col-md-6 col-lg-4">
              <div class="input-group">
                <span class="input-group-text bg-light">
                  <i data-feather="search" class="icon-sm" appFeatherIcon></i>
                </span>
                <input type="text" class="form-control" placeholder="Search disbursements..."
                  [formControl]="searchTerm">
              </div>
            </div>
            <div class="col-md-6 col-lg-2 d-flex align-items-center mt-2 mt-md-0">
              <div class="d-flex align-items-center">
                <span class="text-muted me-2">Show:</span>
                <select class="form-select form-select-sm" [(ngModel)]="pageSize"
                  (ngModelChange)="refreshDisbursements()">
                  <option [ngValue]="5">5</option>
                  <option [ngValue]="10">10</option>
                  <option [ngValue]="20">20</option>
                  <option [ngValue]="50">50</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Table -->
          <div class="table-responsive-wrapper">
            <ngx-datatable #disbursementTable class="bootstrap" [rows]="filteredDisbursements"
              [columnMode]="ColumnMode.force" [headerHeight]="50" [rowHeight]="'auto'" [scrollbarH]="true"
              [footerHeight]="0">


              <!-- Actions Column -->
              <ngx-datatable-column name="Actions" [width]="120" [sortable]="false">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <div class="action-icons">
                    <button type="button" class="action-icon" ngbTooltip="View Details" (click)="editItem(row.id)">
                      <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                    </button>

                    <button type="button" class="action-icon" ngbTooltip="Edit" (click)="editItem(row.id)">
                      <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                    </button>

                    <button type="button" class="action-icon" ngbTooltip="Delete">
                      <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                    </button>
                  </div>

                </ng-template>
              </ngx-datatable-column>

              <!-- Unique ID Column -->
              <ngx-datatable-column name="Unique ID" [width]="120" [sortable]="true" prop="uniqueId">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge bg-light-primary text-primary">{{ row.uniqueId }}</span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Lead Name Column -->
              <ngx-datatable-column name="Lead Name" [width]="120" [sortable]="true" prop="leadName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.leadName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Company Column -->
              <ngx-datatable-column name="Company" [width]="150" [sortable]="true" prop="companyName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.companyName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Project Column -->
              <ngx-datatable-column name="Project" [width]="150" [sortable]="true" prop="projectName">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ row.projectName }}
                </ng-template>
              </ngx-datatable-column>

              <!-- Product Type Column -->
              <ngx-datatable-column name="Product Type" [width]="150" [sortable]="true" prop="productType">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge bg-light text-dark">
                    {{ row.productType }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Docket Status Column -->
              <ngx-datatable-column name="Docket" [width]="100" [sortable]="true" prop="docketStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.docketStatus)">
                    {{ row.docketStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Mortgage Status Column -->
              <ngx-datatable-column name="Mortgage" [width]="100" [sortable]="true" prop="mortgageStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.mortgageStatus)">
                    {{ row.mortgageStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Report Status Column -->
              <ngx-datatable-column name="Report" [width]="100" [sortable]="true" prop="reportStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.reportStatus)">
                    {{ row.reportStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- Disbursement Status Column -->
              <ngx-datatable-column name="Disbursement" [width]="120" [sortable]="true" prop="disbursementStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.disbursementStatus)">
                    {{ row.disbursementStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <!-- File Closed Status Column -->
              <ngx-datatable-column name="File Closed" [width]="120" [sortable]="true" prop="fileClosedStatus">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(row.fileClosedStatus)">
                    {{ row.fileClosedStatus }}
                  </span>
                </ng-template>
              </ngx-datatable-column>




            </ngx-datatable>
          </div>

          <!-- Empty state -->
          <div *ngIf="filteredDisbursements.length === 0" class="text-center py-4">
            <div class="empty-state">
              <i data-feather="database" class="icon-lg mb-3" appFeatherIcon></i>
              <p class="mb-0">No disbursement records found</p>
              <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
            </div>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
              <span class="text-muted" *ngIf="collectionSize > 0">
                Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, collectionSize) }} of {{
                collectionSize }} entries
              </span>
            </div>
            <ngb-pagination [collectionSize]="collectionSize" [(page)]="page" [pageSize]="pageSize" [maxSize]="5"
              [rotate]="true" [boundaryLinks]="true" (pageChange)="refreshDisbursements()"
              class="pagination-sm"></ngb-pagination>
          </div>
        </div>

        <!-- Details Form View -->
        <div *ngIf="showDetailsForm">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h6 class="card-subtitle mb-0">
              {{ selectedItemId ? 'Edit Disbursement Details' : 'Add Disbursement Details' }}
            </h6>
            <button class="btn btn-secondary" (click)="backToList()">
              <i data-feather="arrow-left" class="icon-sm me-1" appFeatherIcon></i>
              Back to List
            </button>
          </div>

          <!-- Tab navigation -->
          <ul ngbNav #nav="ngbNav" [(activeId)]="activeTabId" class="nav-tabs">
            <li [ngbNavItem]="1">
              <a ngbNavLink>
                <i data-feather="file-text" class="icon-sm me-2" appFeatherIcon></i> Docket
              </a>
              <ng-template ngbNavContent>
                <div class="mt-4">
                  <div class="card border-0 ">
                    <div>
                      <h6 class="card-subtitle mb-4">Docket Section</h6>
                      <form>
                        <div class="row mb-4">
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Vetting List Collected</label>
                            <select class="form-select" [(ngModel)]="vettingListStatus" name="vettingListStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Original Deed Deposited with Lender</label>
                            <select class="form-select" [(ngModel)]="originalDeedStatus" name="originalDeedStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Documents to be Taken to External Lawyer</label>
                            <select class="form-select" [(ngModel)]="documentsExternalStatus"
                              name="documentsExternalStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                        </div>

                        <!-- Vetting List Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(vettingListStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="file-text" class="icon-sm me-2" appFeatherIcon></i>
                                  Vetting List Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="vettingListFollowUp.details"
                                      name="vettingListDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="vettingListFollowUp.date"
                                        name="vettingListDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="vettingListFollowUp.time"
                                        name="vettingListTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addVettingListFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="vettingListFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of vettingListFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Original Deed Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(originalDeedStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="file" class="icon-sm me-2" appFeatherIcon></i>
                                  Original Deed Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="originalDeedFollowUp.details"
                                      name="originalDeedDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="originalDeedFollowUp.date"
                                        name="originalDeedDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="originalDeedFollowUp.time"
                                        name="originalDeedTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addOriginalDeedFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="originalDeedFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of originalDeedFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Documents External Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(documentsExternalStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="briefcase" class="icon-sm me-2" appFeatherIcon></i>
                                  External Lawyer Documents Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2"
                                      [(ngModel)]="documentsExternalFollowUp.details" name="documentsExternalDetails"
                                      placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control"
                                        [(ngModel)]="documentsExternalFollowUp.date" name="documentsExternalDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control"
                                        [(ngModel)]="documentsExternalFollowUp.time" name="documentsExternalTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary"
                                    (click)="addDocumentsExternalFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="documentsExternalFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of documentsExternalFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="text-end">
                          <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                          <button type="submit" class="btn btn-primary">Save & Next</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="2">
              <a ngbNavLink>
                <i data-feather="home" class="icon-sm me-2" appFeatherIcon></i> Mortgage
              </a>
              <ng-template ngbNavContent>
                <div class="mt-4">
                  <div class="card border-0 box-shadow">
                    <div>
                      <h6 class="card-subtitle mb-4">Mortgage Section</h6>
                      <form>
                        <div class="row mb-4">
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Stamp Duty and Registration Charges Paid</label>
                            <select class="form-select" [(ngModel)]="stampDutyStatus" name="stampDutyStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Mortgage Process Completed</label>
                            <select class="form-select" [(ngModel)]="mortgageProcessStatus"
                              name="mortgageProcessStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                        </div>

                        <!-- Stamp Duty Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(stampDutyStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="clipboard" class="icon-sm me-2" appFeatherIcon></i>
                                  Stamp Duty Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="stampDutyFollowUp.details"
                                      name="stampDutyDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="stampDutyFollowUp.date"
                                        name="stampDutyDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="stampDutyFollowUp.time"
                                        name="stampDutyTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addStampDutyFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="stampDutyFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of stampDutyFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Mortgage Process Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(mortgageProcessStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="home" class="icon-sm me-2" appFeatherIcon></i>
                                  Mortgage Process Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2"
                                      [(ngModel)]="mortgageProcessFollowUp.details" name="mortgageProcessDetails"
                                      placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="mortgageProcessFollowUp.date"
                                        name="mortgageProcessDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="mortgageProcessFollowUp.time"
                                        name="mortgageProcessTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addMortgageProcessFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="mortgageProcessFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of mortgageProcessFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="text-end">
                          <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                          <button type="submit" class="btn btn-primary">Save & Next</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="3">
              <a ngbNavLink>
                <i data-feather="clock" class="icon-sm me-2" appFeatherIcon></i> Waiting for Report
              </a>
              <ng-template ngbNavContent>
                <div class="mt-4">
                  <div class="card border-0 ">
                    <div>
                      <h6 class="card-subtitle mb-4">Waiting for Report Section</h6>
                      <form>
                        <div class="row mb-4">
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Pendency List</label>
                            <select class="form-select" [(ngModel)]="pendencyListStatus" name="pendencyListStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">PDD</label>
                            <select class="form-select" [(ngModel)]="pddStatus" name="pddStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">OTC</label>
                            <select class="form-select" [(ngModel)]="otcStatus" name="otcStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                        </div>

                        <!-- Pendency List Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(pendencyListStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="check-square" class="icon-sm me-2" appFeatherIcon></i>
                                  Pendency List Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="pendencyListFollowUp.details"
                                      name="pendencyListDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="pendencyListFollowUp.date"
                                        name="pendencyListDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="pendencyListFollowUp.time"
                                        name="pendencyListTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addPendencyListFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="pendencyListFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of pendencyListFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- PDD Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(pddStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="file-text" class="icon-sm me-2" appFeatherIcon></i>
                                  PDD Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="pddFollowUp.details"
                                      name="pddDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="pddFollowUp.date"
                                        name="pddDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="pddFollowUp.time"
                                        name="pddTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addPddFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="pddFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item" *ngFor="let item of pddFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- OTC Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(otcStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="clipboard" class="icon-sm me-2" appFeatherIcon></i>
                                  OTC Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="otcFollowUp.details"
                                      name="otcDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="otcFollowUp.date"
                                        name="otcDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="otcFollowUp.time"
                                        name="otcTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addOtcFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="otcFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item" *ngFor="let item of otcFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="text-end">
                          <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                          <button type="submit" class="btn btn-primary">Save & Next</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="4">
              <a ngbNavLink>
                <i data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i> Disbursement
              </a>
              <ng-template ngbNavContent>
                <div class="mt-4">
                  <div class="card border-0">
                    <div>
                      <h6 class="card-subtitle mb-4">Disbursement Section</h6>
                      <form>
                        <div class="row mb-4">
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">Disbursement</label>
                            <select class="form-select" [(ngModel)]="disbursementStatus" name="disbursementStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                        </div>

                        <!-- Disbursement Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(disbursementStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="credit-card" class="icon-sm me-2" appFeatherIcon></i>
                                  Disbursement Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="disbursementFollowUp.details"
                                      name="disbursementDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="disbursementFollowUp.date"
                                        name="disbursementDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="disbursementFollowUp.time"
                                        name="disbursementTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addDisbursementFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="disbursementFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of disbursementFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="text-end">
                          <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                          <button type="submit" class="btn btn-primary">Save & Next</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="5">
              <a ngbNavLink>
                <i data-feather="check-circle" class="icon-sm me-2" appFeatherIcon></i> File Closed
              </a>
              <ng-template ngbNavContent>
                <div class="mt-4">
                  <div class="card border-0">
                    <div>
                      <h6 class="card-subtitle mb-4">File Closed Section</h6>
                      <form>
                        <div class="row mb-4">
                          <div class="col-12 col-md-6 col-lg-3 mb-3">
                            <label class="form-label">File Closed</label>
                            <select class="form-select" [(ngModel)]="fileClosedStatus" name="fileClosedStatus">
                              <option value="" selected disabled>Select</option>
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                              <option value="Query">Query</option>
                            </select>
                          </div>
                        </div>

                        <!-- File Closed Follow-up Section -->
                        <div class="row mb-4" *ngIf="showFollowUp(fileClosedStatus)">
                          <div class="col-12">
                            <div class="follow-up-container">
                              <div class="follow-up-header">
                                <h6 class="mb-0"><i data-feather="check-circle" class="icon-sm me-2" appFeatherIcon></i>
                                  File Closed Follow-up</h6>
                              </div>
                              <div class="follow-up-body">
                                <div class="row g-3 mb-3">
                                  <div class="col-12 col-md-6">
                                    <label class="form-label">Details</label>
                                    <textarea class="form-control" rows="2" [(ngModel)]="fileClosedFollowUp.details"
                                      name="fileClosedDetails" placeholder="Enter follow-up details"></textarea>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Date</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="calendar" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="date" class="form-control" [(ngModel)]="fileClosedFollowUp.date"
                                        name="fileClosedDate">
                                    </div>
                                  </div>
                                  <div class="col-6 col-md-3">
                                    <label class="form-label">Time</label>
                                    <div class="input-group">
                                      <span class="input-group-text"><i data-feather="clock" class="icon-xs"
                                          appFeatherIcon></i></span>
                                      <input type="time" class="form-control" [(ngModel)]="fileClosedFollowUp.time"
                                        name="fileClosedTime">
                                    </div>
                                  </div>
                                </div>
                                <div class="text-end">
                                  <button type="button" class="btn btn-primary" (click)="addFileClosedFollowUp()">
                                    <i data-feather="plus" class="icon-xs me-1" appFeatherIcon></i> Add Follow-up
                                  </button>
                                </div>
                              </div>

                              <!-- Follow-up History -->
                              <div class="follow-up-history" *ngIf="fileClosedFollowUpHistory.length > 0">
                                <div class="follow-up-history-header">
                                  <h6 class="mb-0"><i data-feather="list" class="icon-sm me-2" appFeatherIcon></i>
                                    Follow-up History</h6>
                                </div>
                                <div class="follow-up-history-body">
                                  <div class="history-timeline">
                                    <div class="history-item"
                                      *ngFor="let item of fileClosedFollowUpHistory; let i = index">
                                      <div class="history-item-badge">{{i + 1}}</div>
                                      <div class="history-item-content">
                                        <div class="history-item-header">
                                          <span class="history-date"><i data-feather="calendar" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.date}}</span>
                                          <span class="history-time"><i data-feather="clock" class="icon-xs me-1"
                                              appFeatherIcon></i> {{item.time}}</span>
                                        </div>
                                        <div class="history-item-body">
                                          {{item.details}}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="text-end">
                          <button type="button" class="btn btn-secondary me-2" (click)="backToList()">Cancel</button>
                          <button type="submit" class="btn btn-primary">Save & Next</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </ng-template>
            </li>
          </ul>

          <!-- Tab content -->
          <div [ngbNavOutlet]="nav" class="mt-2"></div>
        </div>
      </div>
    </div>
  </div>