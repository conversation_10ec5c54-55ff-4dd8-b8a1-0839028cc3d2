import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { MasterService } from '../../../../core/services/master.service';
import { MasterDataService } from '../../../../core/services/master-data.service';
import { ProfessionService } from '../../../../core/services/profession.service';
import { forkJoin, catchError, of } from 'rxjs';

interface MasterDataStats {
  key: string;
  label: string;
  description: string;
  count: number;
  icon: string;
  color: string;
  route: string;
  loading: boolean;
  error?: string;
}

@Component({
  selector: 'app-master-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FeatherIconDirective
  ],
  templateUrl: './master-dashboard.component.html',
  styleUrl: './master-dashboard.component.scss'
})
export class MasterDashboardComponent implements OnInit {
  masterDataStats: MasterDataStats[] = [
    {
      key: 'professions',
      label: 'Professions',
      description: 'Professional categories and types',
      count: 0,
      icon: 'briefcase',
      color: 'primary',
      route: '/master/profession',
      loading: true
    },
    {
      key: 'associates',
      label: 'Associates',
      description: 'Business associates and partners',
      count: 0,
      icon: 'users',
      color: 'success',
      route: '/master/associate',
      loading: true
    },
    {
      key: 'private-banks',
      label: 'Private Banks',
      description: 'Private banking institutions',
      count: 0,
      icon: 'credit-card',
      color: 'info',
      route: '/master/master-data',
      loading: true
    },
    {
      key: 'nbfcs',
      label: 'NBFCs',
      description: 'Non-Banking Financial Companies',
      count: 0,
      icon: 'dollar-sign',
      color: 'warning',
      route: '/master/master-data',
      loading: true
    },
    {
      key: 'institutes',
      label: 'Institutes',
      description: 'Educational and training institutes',
      count: 0,
      icon: 'book',
      color: 'secondary',
      route: '/master/master-data',
      loading: true
    },
    {
      key: 'corporate-consultancies',
      label: 'Corporate Consultancies',
      description: 'Corporate consultancy firms',
      count: 0,
      icon: 'trending-up',
      color: 'dark',
      route: '/master/master-data',
      loading: true
    }
  ];

  totalMasterDataCount = 0;
  loading = true;

  constructor(
    private masterService: MasterService,
    private masterDataService: MasterDataService,
    private professionService: ProfessionService
  ) {}

  ngOnInit(): void {
    this.loadMasterDataStats();
  }

  loadMasterDataStats(): void {
    this.loading = true;

    // Create observables for each master data type (using skip/limit format)
    const professions$ = this.masterService.getProfessions(0, 1000).pipe(
      catchError(error => {
        console.error('Error loading professions:', error);
        return of({ items: [], total: 0 });
      })
    );

    const associates$ = this.masterService.getAssociates(0, 1000).pipe(
      catchError(error => {
        console.error('Error loading associates:', error);
        return of({ items: [], total: 0 });
      })
    );

    const privateBanks$ = this.masterDataService.getPrivateBanks(0, 1000).pipe(
      catchError(error => {
        console.error('Error loading private banks:', error);
        return of([]);
      })
    );

    const nbfcs$ = this.masterDataService.getNBFCs(0, 1000).pipe(
      catchError(error => {
        console.error('Error loading NBFCs:', error);
        return of([]);
      })
    );

    const institutes$ = this.masterDataService.getInstitutes(0, 1000).pipe(
      catchError(error => {
        console.error('Error loading institutes:', error);
        return of([]);
      })
    );

    const corporateConsultancies$ = this.masterDataService.getCorporateConsultancies(0, 1000).pipe(
      catchError(error => {
        console.error('Error loading corporate consultancies:', error);
        return of([]);
      })
    );

    // Execute all requests in parallel
    forkJoin({
      professions: professions$,
      associates: associates$,
      privateBanks: privateBanks$,
      nbfcs: nbfcs$,
      institutes: institutes$,
      corporateConsultancies: corporateConsultancies$
    }).subscribe({
      next: (results) => {
        // Update stats with actual counts
        this.updateStats('professions', results.professions.total || results.professions.items?.length || 0);
        this.updateStats('associates', results.associates.total || results.associates.items?.length || 0);
        this.updateStats('private-banks', Array.isArray(results.privateBanks) ? results.privateBanks.length : 0);
        this.updateStats('nbfcs', Array.isArray(results.nbfcs) ? results.nbfcs.length : 0);
        this.updateStats('institutes', Array.isArray(results.institutes) ? results.institutes.length : 0);
        this.updateStats('corporate-consultancies', Array.isArray(results.corporateConsultancies) ? results.corporateConsultancies.length : 0);

        // Calculate total
        this.totalMasterDataCount = this.masterDataStats.reduce((sum, stat) => sum + stat.count, 0);

        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading master data stats:', error);
        this.loading = false;

        // Mark all as error
        this.masterDataStats.forEach(stat => {
          stat.loading = false;
          stat.error = 'Failed to load';
        });
      }
    });
  }

  private updateStats(key: string, count: number): void {
    const stat = this.masterDataStats.find(s => s.key === key);
    if (stat) {
      stat.count = count;
      stat.loading = false;
    }
  }

  getColorClass(color: string): string {
    return `text-${color}`;
  }

  getBgColorClass(color: string): string {
    return `bg-${color}`;
  }

  refreshStats(): void {
    this.masterDataStats.forEach(stat => {
      stat.loading = true;
      stat.error = undefined;
    });
    this.loadMasterDataStats();
  }

  navigateToMasterData(stat: MasterDataStats): void {
    // For master-data route, we need to pass the data type as a query parameter
    // or handle it in the component to switch to the correct data type
    if (stat.route === '/master/master-data') {
      // You can implement navigation with state or query params here
      console.log(`Navigate to ${stat.key} in master data component`);
    }
  }

  getCurrentDate(): Date {
    return new Date();
  }
}
