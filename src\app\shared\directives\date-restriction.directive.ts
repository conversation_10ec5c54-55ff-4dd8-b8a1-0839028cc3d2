import { Directive, Input, OnInit, OnD<PERSON>roy, ElementRef, Renderer2 } from '@angular/core';
import { Ngb<PERSON><PERSON>pic<PERSON>, <PERSON>bDate, NgbCalendar } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { HolidayService } from '../../core/services/holiday.service';

@Directive({
  selector: '[appDateRestriction]',
  standalone: true
})
export class DateRestrictionDirective implements OnInit, OnDestroy {
  @Input() restrictWeekends: boolean = true;
  @Input() restrictHolidays: boolean = true;
  
  private holidaysSubscription?: Subscription;
  private holidays: string[] = [];

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private holidayService: HolidayService,
    private calendar: NgbCalendar
  ) {}

  ngOnInit(): void {
    console.log('🔒 DateRestrictionDirective - Initializing date restrictions');
    
    // Load holidays if restriction is enabled
    if (this.restrictHolidays) {
      this.loadHolidays();
    }

    // Apply the date filter to the datepicker
    this.applyDateFilter();
  }

  ngOnDestroy(): void {
    if (this.holidaysSubscription) {
      this.holidaysSubscription.unsubscribe();
    }
  }

  private loadHolidays(): void {
    console.log('🎄 DateRestrictionDirective - Loading holidays for date restriction');
    
    this.holidaysSubscription = this.holidayService.getCachedHolidays().subscribe({
      next: (holidays) => {
        this.holidays = holidays.map(h => h.holiday_date);
        console.log(`✅ DateRestrictionDirective - Loaded ${this.holidays.length} holidays for filtering`);
        
        // Reapply filter after holidays are loaded
        this.applyDateFilter();
      },
      error: (error) => {
        console.error('❌ DateRestrictionDirective - Error loading holidays:', error);
        this.holidays = [];
      }
    });
  }

  private applyDateFilter(): void {
    // Create the date filter function
    const dateFilter = (date: NgbDate | null): boolean => {
      if (!date) return false;

      // Convert NgbDate to JavaScript Date
      const jsDate = new Date(date.year, date.month - 1, date.day);
      
      // Check if it's a weekend
      if (this.restrictWeekends) {
        const dayOfWeek = jsDate.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday = 0, Saturday = 6
          return false;
        }
      }

      // Check if it's a holiday
      if (this.restrictHolidays && this.holidays.length > 0) {
        const dateStr = this.formatDate(jsDate);
        if (this.holidays.includes(dateStr)) {
          return false;
        }
      }

      return true;
    };

    // Apply the filter to the element if it's a datepicker
    const element = this.el.nativeElement;
    if (element && element.markDisabled !== dateFilter) {
      element.markDisabled = dateFilter;
      console.log('✅ DateRestrictionDirective - Date filter applied to datepicker');
    }
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
