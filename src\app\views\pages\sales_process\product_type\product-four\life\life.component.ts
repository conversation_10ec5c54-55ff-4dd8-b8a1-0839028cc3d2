import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-life',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './life.component.html',
  styleUrl: './life.component.scss'
})
export class LifeComponent implements OnInit {
  @Input() selectedProductSubType: string = '';
  @Output() submitSales = new EventEmitter<any>();

  lifeForm!: FormGroup;
  isFormSubmitted = false;
  showAlcoholQuantity = false;

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm(): void {
    this.lifeForm = this.formBuilder.group({
      customerName: ['', Validators.required],
      insuredName: ['', Validators.required],
      dateOfBirth: ['', Validators.required],
      height: ['', [Validators.required, Validators.min(50), Validators.max(300)]],
      weight: ['', [Validators.required, Validators.min(20), Validators.max(300)]],
      aadharCard: ['', [Validators.required, Validators.pattern(/^[0-9]{12}$/)]],
      panCard: ['', [Validators.required, Validators.pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)]],
      sumInsured: ['', [Validators.required, Validators.min(100000)]],
      preExistingDisease: ['', Validators.required],
      bloodPressureOrDiabetes: ['', Validators.required],
      incomeTaxReturns: ['', Validators.required],
      existingTermInsurance: ['', Validators.required],
      smoker: ['', Validators.required],
      alcoholConsumption: ['', Validators.required],
      alcoholQuantity: ['']
    });
  }

  get form() {
    return this.lifeForm.controls;
  }

  onSubmit(): void {
    this.isFormSubmitted = true;

    if (this.lifeForm.valid) {
      const formData = this.lifeForm.value;
      console.log('Life Insurance Form Data:', formData);

      // Prepare data for sales submission
      const productFormData = {
        component_type: 'life',
        product_type: 'Insurance - Life',
        product_form_data: formData,
        customers: [] // Life insurance doesn't have separate customer data
      };

      // Emit the form data to parent component
      this.submitSales.emit(productFormData);

      alert('Life Insurance application submitted successfully!');
      this.resetForm();
    } else {
      console.log('Form is invalid');
      this.markFormGroupTouched();
    }
  }

  resetForm(): void {
    this.isFormSubmitted = false;
    this.showAlcoholQuantity = false;
    this.lifeForm.reset();
  }

  onAlcoholConsumptionChange(event: any): void {
    const value = event.target.value;
    this.showAlcoholQuantity = value === 'Regular';

    // Update alcohol quantity field validation based on selection
    const alcoholQuantityControl = this.lifeForm.get('alcoholQuantity');
    if (this.showAlcoholQuantity) {
      alcoholQuantityControl?.setValidators([Validators.required]);
    } else {
      alcoholQuantityControl?.clearValidators();
      alcoholQuantityControl?.setValue('');
    }
    alcoholQuantityControl?.updateValueAndValidity();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.lifeForm.controls).forEach(key => {
      const control = this.lifeForm.get(key);
      control?.markAsTouched();
    });
  }

  // Format Aadhar number input (only allow numbers)
  onAadharInput(event: any): void {
    let value = event.target.value.replace(/\D/g, ''); // Remove non-digits
    if (value.length > 12) {
      value = value.substring(0, 12); // Limit to 12 digits
    }
    event.target.value = value;
    this.lifeForm.get('aadharCard')?.setValue(value);
  }

  // Format PAN number input (uppercase and pattern)
  onPanInput(event: any): void {
    let value = event.target.value.toUpperCase();
    // Remove invalid characters based on PAN pattern
    value = value.replace(/[^A-Z0-9]/g, '');

    // Apply PAN pattern: 5 letters, 4 numbers, 1 letter
    if (value.length > 10) {
      value = value.substring(0, 10);
    }

    event.target.value = value;
    this.lifeForm.get('panCard')?.setValue(value);
  }

  // Method to get form data for sales submission
  getFormData(): any {
    if (this.lifeForm.valid) {
      const formData = this.lifeForm.value;
      return {
        component_type: 'life',
        product_type: 'Insurance - Life',
        product_form_data: formData,
        customers: [] // Life insurance doesn't have separate customer data
      };
    }
    return null;
  }

  /**
   * Populate form with existing data (for edit mode)
   */
  populateFormData(formData: any): void {
    if (!formData || !this.lifeForm) {
      console.log('⚠️ No form data or Life form not initialized');
      return;
    }

    console.log('🔧 Populating Life form with data:', formData);
    console.log('🔧 Life form current state before population:', this.lifeForm.value);

    try {
      // Populate main form with the provided data
      this.lifeForm.patchValue(formData);

      console.log('🔧 Life form state after population:', this.lifeForm.value);
      console.log('✅ Life form populated successfully');

      // Trigger any necessary validations or calculations
      this.lifeForm.updateValueAndValidity();
    } catch (error) {
      console.error('❌ Error populating Life form:', error);
      console.error('❌ Form data that caused error:', formData);
    }
  }

  /**
   * Reset form after successful submission
   */
  resetFormAfterSubmission(): void {
    console.log('🔄 Resetting Life form after successful submission');

    this.lifeForm.reset();
    this.isFormSubmitted = false;
    this.initializeForm(); // Reinitialize with default values

    console.log('✅ Life form reset completed');
  }
}
