import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Fund House interfaces
export interface FundHouse {
  id: string;
  name: string;
  code: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active: boolean;
  fund_count?: number;
  total_aum?: number; // Assets Under Management
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface FundHouseCreate {
  name: string;
  code: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
}

export interface FundHouseUpdate {
  name?: string;
  code?: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FundHouseService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/fund-houses/`;
  private fundHousesSubject = new BehaviorSubject<FundHouse[]>([]);
  public fundHouses$ = this.fundHousesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all fund houses with optional filtering and pagination
   */
  getFundHouses(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    country?: string;
    regulatory_body?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<FundHouse[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<FundHouse[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.fundHousesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get fund house by ID
   */
  getFundHouseById(id: string): Observable<APIResponse<FundHouse>> {
    return this.http.get<APIResponse<FundHouse>>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new fund house
   */
  createFundHouse(fundHouse: FundHouseCreate): Observable<APIResponse<FundHouse>> {
    return this.http.post<APIResponse<FundHouse>>(this.baseUrl, fundHouse)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshFundHouses();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update fund house
   */
  updateFundHouse(id: string, fundHouse: FundHouseUpdate): Observable<APIResponse<FundHouse>> {
    return this.http.put<APIResponse<FundHouse>>(`${this.baseUrl}/${id}`, fundHouse)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshFundHouses();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete fund house
   */
  deleteFundHouse(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshFundHouses();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted fund house
   */
  restoreFundHouse(id: string): Observable<APIResponse<FundHouse>> {
    return this.http.post<APIResponse<FundHouse>>(`${this.baseUrl}/${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshFundHouses();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Bulk upload fund houses
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}/bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshFundHouses();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Search fund houses
   */
  searchFundHouses(query: string, filters?: {
    is_active?: boolean;
    country?: string;
    regulatory_body?: string;
  }): Observable<APIResponse<FundHouse[]>> {
    let params = new HttpParams().set('search', query);

    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key as keyof typeof filters];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<FundHouse[]>>(this.baseUrl, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get fund houses for dropdown (simplified data)
   */
  getFundHousesDropdown(): Observable<{ id: string; name: string; code: string }[]> {
    return this.getFundHouses({ per_page: 1000, is_active: true }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map(fundHouse => ({
            id: fundHouse.id,
            name: fundHouse.name,
            code: fundHouse.code
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get active fund houses only
   */
  getActiveFundHouses(): Observable<FundHouse[]> {
    return this.getFundHouses({ is_active: true }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get deleted fund houses
   */
  getDeletedFundHouses(): Observable<FundHouse[]> {
    return this.getFundHouses({ include_deleted: true }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.filter(fh => fh.deleted_at);
        }
        return [];
      })
    );
  }

  /**
   * Get fund houses by country
   */
  getFundHousesByCountry(country: string): Observable<FundHouse[]> {
    return this.getFundHouses({ country }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get fund houses by regulatory body
   */
  getFundHousesByRegulatoryBody(regulatoryBody: string): Observable<FundHouse[]> {
    return this.getFundHouses({ regulatory_body: regulatoryBody }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh fund houses data
   */
  refreshFundHouses(): void {
    this.getFundHouses().subscribe();
  }

  /**
   * Clear fund houses cache
   */
  clearCache(): void {
    this.fundHousesSubject.next([]);
  }

  /**
   * Format AUM (Assets Under Management)
   */
  formatAUM(amount?: number): string {
    if (!amount) return 'Not specified';

    if (amount >= 1000000000000) {
      return `₹${(amount / 1000000000000).toFixed(2)}T`;
    } else if (amount >= 1000000000) {
      return `₹${(amount / 1000000000).toFixed(2)}B`;
    } else if (amount >= 1000000) {
      return `₹${(amount / 1000000).toFixed(2)}M`;
    } else if (amount >= 1000) {
      return `₹${(amount / 1000).toFixed(2)}K`;
    }

    return `₹${amount.toLocaleString()}`;
  }

  /**
   * Validate fund house code format
   */
  validateFundHouseCode(code: string): boolean {
    // Fund house codes should be 3-10 characters, alphanumeric, uppercase
    const codeRegex = /^[A-Z0-9]{3,10}$/;
    return codeRegex.test(code);
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  validatePhone(phone: string): boolean {
    // Basic phone validation - can be enhanced based on requirements
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  /**
   * Validate website URL format
   */
  validateWebsite(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get country list for dropdown
   */
  getCountryList(): string[] {
    return [
      'India',
      'United States',
      'United Kingdom',
      'Canada',
      'Australia',
      'Singapore',
      'Hong Kong',
      'Japan',
      'Germany',
      'France',
      'Switzerland',
      'Netherlands',
      'Luxembourg'
    ];
  }

  /**
   * Get regulatory bodies list
   */
  getRegulatoryBodies(): string[] {
    return [
      'SEBI (India)',
      'SEC (USA)',
      'FCA (UK)',
      'ASIC (Australia)',
      'MAS (Singapore)',
      'SFC (Hong Kong)',
      'JFSA (Japan)',
      'BaFin (Germany)',
      'AMF (France)',
      'FINMA (Switzerland)',
      'AFM (Netherlands)',
      'CSSF (Luxembourg)'
    ];
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Fund House service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
