// Custom styles for Product-one component
.btn-group {
  .btn-check:checked + .btn-outline-primary {
    background-color: var(--bs-primary);
    color: white;
  }
}

.badge {
  padding: 0.5em 0.75em;
}

// People Information Section Styling (matching sales-list design)
.card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
    border-radius: 8px 8px 0 0;

    h6 {
      color: #495057;
      font-weight: 600;
      margin: 0;
    }

    .btn {
      font-size: 0.875rem;
      padding: 0.375rem 0.75rem;
      border-radius: 4px;
      font-weight: 500;
      transition: all 0.2s ease-in-out;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

.person-entry {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1rem;
  margin-bottom: 1rem;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .form-control,
  .form-select {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
      border-color: #3F828B;
      box-shadow: 0 0 0 0.2rem rgba(63, 130, 139, 0.25);
      outline: none;
    }

  }

  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
    text-decoration: none;

    &:hover {
      background-color: #f8f9fa;
      transform: translateY(-1px);
    }

    .icon-sm {
      font-size: 0.875rem;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .person-entry {
    .col-md-1 {
      text-align: center;
      margin-top: 0.5rem;
    }
  }
}