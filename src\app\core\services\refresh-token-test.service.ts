import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { Observable, of, timer } from 'rxjs';
import { switchMap, tap, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class RefreshTokenTestService {

  constructor(private authService: AuthService) {
    // Make test methods available globally for console access
    (window as any).refreshTokenTest = {
      testRefresh: () => this.testRefreshToken(),
      testAutoRefresh: () => this.testAutoRefresh(),
      testExpiredToken: () => this.testExpiredToken(),
      testInvalidToken: () => this.testInvalidRefreshToken(),
      forceExpiry: (minutes: number) => this.forceTokenExpiry(minutes),
      getTokenStatus: () => this.getTokenStatus(),
      testRefreshFlow: () => this.testCompleteRefreshFlow(),
      stressTest: () => this.stressTestRefresh(),
      simulateNetworkError: () => this.simulateNetworkError(),
      testConcurrentRefresh: () => this.testConcurrentRefresh()
    };

    console.log('🧪 Refresh Token Test Service loaded');
    console.log('💡 Available console commands:');
    console.log('   - refreshTokenTest.testRefresh() - Test basic refresh');
    console.log('   - refreshTokenTest.testAutoRefresh() - Test automatic refresh');
    console.log('   - refreshTokenTest.testExpiredToken() - Test expired token handling');
    console.log('   - refreshTokenTest.forceExpiry(minutes) - Force token expiry');
    console.log('   - refreshTokenTest.getTokenStatus() - Get current token status');
    console.log('   - refreshTokenTest.testRefreshFlow() - Test complete flow');
    console.log('   - refreshTokenTest.stressTest() - Stress test refresh');
  }

  /**
   * Test basic refresh token functionality
   */
  testRefreshToken(): void {
    console.log('🧪 Testing basic refresh token functionality...');
    
    if (!this.authService.isLoggedIn()) {
      console.error('❌ User not logged in - cannot test refresh token');
      return;
    }

    const currentUser = this.authService.currentUserValue;
    if (!currentUser?.refresh_token) {
      console.error('❌ No refresh token available');
      return;
    }

    console.log('📊 Current token status before refresh:');
    this.logTokenStatus();

    this.authService.refreshToken().subscribe({
      next: (response) => {
        console.log('✅ Refresh token test successful:', response);
        console.log('📊 Token status after refresh:');
        this.logTokenStatus();
      },
      error: (error) => {
        console.error('❌ Refresh token test failed:', error);
        this.logDetailedError(error);
      }
    });
  }

  /**
   * Test automatic refresh functionality
   */
  testAutoRefresh(): void {
    console.log('🧪 Testing automatic refresh functionality...');
    
    if (!this.authService.isLoggedIn()) {
      console.error('❌ User not logged in - cannot test auto refresh');
      return;
    }

    // Force token to expire in 1 minute to trigger auto refresh
    console.log('⏰ Setting token to expire in 1 minute...');
    this.authService.setTokenExpiryForTesting(1);
    
    console.log('🔄 Auto refresh should trigger in ~30 seconds (2 minutes before expiry)');
    console.log('📊 Watch console for automatic refresh logs');
  }

  /**
   * Test expired token handling
   */
  testExpiredToken(): void {
    console.log('🧪 Testing expired token handling...');
    
    if (!this.authService.isLoggedIn()) {
      console.error('❌ User not logged in - cannot test expired token');
      return;
    }

    // Set token to expire immediately
    console.log('⏰ Setting token to expire immediately...');
    this.authService.setTokenExpiryForTesting(-1);
    
    console.log('🔄 Token is now expired, testing refresh...');
    this.testRefreshToken();
  }

  /**
   * Test invalid refresh token handling
   */
  testInvalidRefreshToken(): void {
    console.log('🧪 Testing invalid refresh token handling...');
    
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ User not logged in');
      return;
    }

    // Temporarily corrupt the refresh token
    const originalRefreshToken = currentUser.refresh_token;
    const corruptedUser = {
      ...currentUser,
      refresh_token: 'invalid_refresh_token_for_testing'
    };

    // Update user with corrupted token
    localStorage.setItem('currentUser', JSON.stringify(corruptedUser));
    (this.authService as any).currentUserSubject.next(corruptedUser);

    console.log('🔄 Testing refresh with invalid token...');
    this.authService.refreshToken().subscribe({
      next: (response) => {
        console.log('⚠️ Unexpected success with invalid token:', response);
      },
      error: (error) => {
        console.log('✅ Expected failure with invalid token:', error.status);
        
        // Restore original token
        const restoredUser = {
          ...currentUser,
          refresh_token: originalRefreshToken
        };
        localStorage.setItem('currentUser', JSON.stringify(restoredUser));
        (this.authService as any).currentUserSubject.next(restoredUser);
        
        console.log('🔄 Original refresh token restored');
      }
    });
  }

  /**
   * Force token expiry for testing
   */
  forceTokenExpiry(minutes: number): void {
    console.log(`🧪 Forcing token expiry in ${minutes} minutes...`);
    this.authService.setTokenExpiryForTesting(minutes);
    this.logTokenStatus();
  }

  /**
   * Get current token status
   */
  getTokenStatus(): any {
    const status = this.authService.getTokenStatus();
    console.log('📊 Current Token Status:', status);
    return status;
  }

  /**
   * Test complete refresh flow
   */
  testCompleteRefreshFlow(): void {
    console.log('🧪 Testing complete refresh token flow...');
    
    if (!this.authService.isLoggedIn()) {
      console.error('❌ User not logged in');
      return;
    }

    console.log('1️⃣ Initial token status:');
    this.logTokenStatus();

    console.log('2️⃣ Testing manual refresh...');
    this.authService.refreshToken().subscribe({
      next: (response) => {
        console.log('✅ Manual refresh successful');
        console.log('3️⃣ Token status after manual refresh:');
        this.logTokenStatus();

        console.log('4️⃣ Setting up auto refresh test...');
        this.authService.setTokenExpiryForTesting(1);
        console.log('⏰ Auto refresh should trigger in ~30 seconds');
      },
      error: (error) => {
        console.error('❌ Manual refresh failed:', error);
      }
    });
  }

  /**
   * Stress test refresh functionality
   */
  stressTestRefresh(): void {
    console.log('🧪 Starting refresh token stress test...');
    
    if (!this.authService.isLoggedIn()) {
      console.error('❌ User not logged in');
      return;
    }

    let successCount = 0;
    let errorCount = 0;
    const totalTests = 5;

    console.log(`🔄 Running ${totalTests} consecutive refresh tests...`);

    const runTest = (testNumber: number) => {
      console.log(`📝 Test ${testNumber}/${totalTests}:`);
      
      this.authService.refreshToken().subscribe({
        next: (response) => {
          successCount++;
          console.log(`✅ Test ${testNumber} successful`);
          
          if (testNumber < totalTests) {
            setTimeout(() => runTest(testNumber + 1), 1000);
          } else {
            console.log(`🎉 Stress test completed: ${successCount}/${totalTests} successful, ${errorCount} errors`);
          }
        },
        error: (error) => {
          errorCount++;
          console.error(`❌ Test ${testNumber} failed:`, error.status);
          
          if (testNumber < totalTests) {
            setTimeout(() => runTest(testNumber + 1), 1000);
          } else {
            console.log(`🏁 Stress test completed: ${successCount}/${totalTests} successful, ${errorCount} errors`);
          }
        }
      });
    };

    runTest(1);
  }

  /**
   * Test concurrent refresh requests
   */
  testConcurrentRefresh(): void {
    console.log('🧪 Testing concurrent refresh requests...');
    
    if (!this.authService.isLoggedIn()) {
      console.error('❌ User not logged in');
      return;
    }

    console.log('🔄 Sending 3 concurrent refresh requests...');
    
    const requests = [
      this.authService.refreshToken(),
      this.authService.refreshToken(),
      this.authService.refreshToken()
    ];

    requests.forEach((request, index) => {
      request.subscribe({
        next: (response) => {
          console.log(`✅ Concurrent request ${index + 1} successful`);
        },
        error: (error) => {
          console.error(`❌ Concurrent request ${index + 1} failed:`, error.status);
        }
      });
    });
  }

  /**
   * Simulate network error during refresh
   */
  simulateNetworkError(): void {
    console.log('🧪 Simulating network error during refresh...');
    console.log('⚠️ This test requires manual network disconnection');
    console.log('💡 Try: Developer Tools → Network → Offline, then run refreshTokenTest.testRefresh()');
  }

  /**
   * Log current token status
   */
  private logTokenStatus(): void {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ No user logged in');
      return;
    }

    const now = Date.now();
    const expiry = currentUser.token_expiry || 0;
    const timeUntilExpiry = Math.round((expiry - now) / (1000 * 60));

    console.log('📊 Token Status:', {
      hasAccessToken: !!currentUser.access_token,
      hasRefreshToken: !!currentUser.refresh_token,
      tokenExpiry: new Date(expiry).toLocaleString(),
      timeUntilExpiry: `${timeUntilExpiry} minutes`,
      isExpired: expiry <= now,
      isExpiringSoon: expiry <= now + (5 * 60 * 1000)
    });
  }

  /**
   * Log detailed error information
   */
  private logDetailedError(error: any): void {
    console.error('📊 Detailed Error Information:', {
      status: error.status,
      statusText: error.statusText,
      message: error.message,
      url: error.url,
      error: error.error
    });
  }
}
