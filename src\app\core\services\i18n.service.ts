import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Internationalization interfaces based on OpenAPI specification
 */
export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

export interface HelloResponse {
  message: string;
  name: string;
  language: string;
  timestamp: string;
}

export interface TranslationsResponse {
  [key: string]: string;
}

/**
 * Internationalization Service
 * Handles multi-language support, translations, and localized content.
 * Provides methods for greeting endpoints and translation management.
 */
@Injectable({
  providedIn: 'root'
})
export class I18nService {
  private baseUrl = `${environment.apiUrl}/api/v1/i18n`;
  
  // Current language state
  private currentLanguageSubject = new BehaviorSubject<string>('en');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();
  
  // Cached translations
  private translationsCache = new Map<string, TranslationsResponse>();

  constructor(private http: HttpClient) {
    // Initialize with browser language or default to English
    const browserLang = navigator.language.split('-')[0];
    this.setLanguage(browserLang || 'en');
  }

  /**
   * Get current language
   * @returns Current language code
   */
  getCurrentLanguage(): string {
    return this.currentLanguageSubject.value;
  }

  /**
   * Set current language
   * @param language Language code (e.g., 'en', 'es', 'fr')
   */
  setLanguage(language: string): void {
    this.currentLanguageSubject.next(language);
    // Store in localStorage for persistence
    localStorage.setItem('preferred_language', language);
  }

  /**
   * Hello world endpoint with internationalization
   * GET /api/v1/i18n/hello
   * @param name Name to greet (default: 'World')
   * @returns Observable of greeting response
   */
  getHello(name: string = 'World'): Observable<HelloResponse> {
    const params = new HttpParams().set('name', name);
    
    // Add Accept-Language header for internationalization
    const headers = {
      'Accept-Language': this.getCurrentLanguage()
    };

    return this.http.get<APIResponse<HelloResponse>>(`${this.baseUrl}/hello`, { 
      params, 
      headers 
    }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('getHello'))
    );
  }

  /**
   * Get translations for common messages
   * GET /api/v1/i18n/translations
   * @param language Optional language code (uses current language if not provided)
   * @param useCache Whether to use cached translations (default: true)
   * @returns Observable of translations object
   */
  getTranslations(language?: string, useCache: boolean = true): Observable<TranslationsResponse> {
    const lang = language || this.getCurrentLanguage();
    
    // Check cache first if enabled
    if (useCache && this.translationsCache.has(lang)) {
      return new Observable(observer => {
        observer.next(this.translationsCache.get(lang)!);
        observer.complete();
      });
    }

    // Add Accept-Language header for internationalization
    const headers = {
      'Accept-Language': lang
    };

    return this.http.get<APIResponse<TranslationsResponse>>(`${this.baseUrl}/translations`, { 
      headers 
    }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format');
      }),
      tap(translations => {
        // Cache the translations
        this.translationsCache.set(lang, translations);
      }),
      catchError(this.handleError('getTranslations', {}))
    );
  }

  /**
   * Get a specific translation by key
   * @param key Translation key
   * @param defaultValue Default value if translation not found
   * @param language Optional language code
   * @returns Observable of translated string
   */
  getTranslation(key: string, defaultValue?: string, language?: string): Observable<string> {
    return this.getTranslations(language).pipe(
      map(translations => {
        return translations[key] || defaultValue || key;
      })
    );
  }

  /**
   * Get multiple translations by keys
   * @param keys Array of translation keys
   * @param language Optional language code
   * @returns Observable of translations object with requested keys
   */
  getTranslationsByKeys(keys: string[], language?: string): Observable<TranslationsResponse> {
    return this.getTranslations(language).pipe(
      map(translations => {
        const result: TranslationsResponse = {};
        keys.forEach(key => {
          result[key] = translations[key] || key;
        });
        return result;
      })
    );
  }

  /**
   * Clear translations cache
   * @param language Optional specific language to clear (clears all if not provided)
   */
  clearTranslationsCache(language?: string): void {
    if (language) {
      this.translationsCache.delete(language);
    } else {
      this.translationsCache.clear();
    }
  }

  /**
   * Preload translations for a specific language
   * @param language Language code to preload
   * @returns Observable of preloaded translations
   */
  preloadTranslations(language: string): Observable<TranslationsResponse> {
    return this.getTranslations(language, false);
  }

  /**
   * Get supported languages (this would typically come from a configuration endpoint)
   * @returns Array of supported language codes
   */
  getSupportedLanguages(): string[] {
    // This could be fetched from an API endpoint in the future
    return ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko', 'ar', 'hi'];
  }

  /**
   * Get language display names
   * @returns Object mapping language codes to display names
   */
  getLanguageDisplayNames(): { [key: string]: string } {
    return {
      'en': 'English',
      'es': 'Español',
      'fr': 'Français',
      'de': 'Deutsch',
      'it': 'Italiano',
      'pt': 'Português',
      'zh': '中文',
      'ja': '日本語',
      'ko': '한국어',
      'ar': 'العربية',
      'hi': 'हिन्दी'
    };
  }

  /**
   * Format a message with parameters (simple interpolation)
   * @param template Message template with {param} placeholders
   * @param params Parameters to interpolate
   * @returns Formatted message
   */
  formatMessage(template: string, params: { [key: string]: any }): string {
    let result = template;
    Object.keys(params).forEach(key => {
      const placeholder = `{${key}}`;
      result = result.replace(new RegExp(placeholder, 'g'), params[key]);
    });
    return result;
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
