<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card inner-card">
      <div class="card-body p-0">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">Self-Employed Details</h4>
            <p class="text-secondary">Manage self-employed information</p>
          </div>
        </div>

        <form [formGroup]="selfEmployeeForm" (ngSubmit)="onSubmit()">
          <!-- Personal Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Personal Information</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="age" class="form-label">Age</label>
              <input type="number" class="form-control" id="age" formControlName="age"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('age')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('age')?.errors?.required) {
                <div class="invalid-feedback">Age is required</div>
              }
              @if (isFormSubmitted && (selfEmployeeForm.get('age')?.errors?.min || selfEmployeeForm.get('age')?.errors?.max)) {
                <div class="invalid-feedback">Age must be between 18 and 100</div>
              }
            </div>



            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="addressType" class="form-label">Address Type</label>
              <select class="form-select" id="addressType" formControlName="addressType"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('addressType')?.errors}">
                <option value="" selected>Select Address Type</option>
                @for (type of addressTypes; track type) {
                  <option [value]="type">{{ type }}</option>
                }
              </select>
              @if (isFormSubmitted && selfEmployeeForm.get('addressType')?.errors?.required) {
                <div class="invalid-feedback">Address type is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="status" class="form-label">Status</label>
              <select class="form-select" id="status" formControlName="status"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('status')?.errors}">
                <option value="" selected>Select Status</option>
                @for (option of statusOptions; track option) {
                  <option [value]="option">{{ option }}</option>
                }
              </select>
              @if (isFormSubmitted && selfEmployeeForm.get('status')?.errors?.required) {
                <div class="invalid-feedback">Status is required</div>
              }
            </div>
          </div>

          <!-- Business Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Business Information</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="profession" class="form-label">Profession</label>
              <select class="form-select" id="profession" formControlName="profession"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('profession')?.errors}">
                <option value="" selected>Select Profession</option>
                @for (option of professionOptions; track option) {
                  <option [value]="option">{{ option }}</option>
                }
              </select>
              @if (isFormSubmitted && selfEmployeeForm.get('profession')?.errors?.required) {
                <div class="invalid-feedback">Profession is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="businessType" class="form-label">Type of Business</label>
              <select class="form-select" id="businessType" formControlName="businessType"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('businessType')?.errors}">
                <option value="" selected>Select Business Type</option>
                @for (option of businessTypeOptions; track option) {
                  <option [value]="option">{{ option }}</option>
                }
              </select>
              @if (isFormSubmitted && selfEmployeeForm.get('businessType')?.errors?.required) {
                <div class="invalid-feedback">Business type is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="natureOfBusiness" class="form-label">Nature of Business</label>
              <input type="text" class="form-control" id="natureOfBusiness" formControlName="natureOfBusiness"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('natureOfBusiness')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('natureOfBusiness')?.errors?.required) {
                <div class="invalid-feedback">Nature of business is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="businessName" class="form-label">Business Name</label>
              <input type="text" class="form-control" id="businessName" formControlName="businessName"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('businessName')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('businessName')?.errors?.required) {
                <div class="invalid-feedback">Business name is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="businessPlaceOwnership" class="form-label">Business Place Ownership</label>
              <select class="form-select" id="businessPlaceOwnership" formControlName="businessPlaceOwnership"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('businessPlaceOwnership')?.errors}">
                <option value="" selected>Select Ownership</option>
                @for (option of businessPlaceOwnershipOptions; track option) {
                  <option [value]="option">{{ option }}</option>
                }
              </select>
              @if (isFormSubmitted && selfEmployeeForm.get('businessPlaceOwnership')?.errors?.required) {
                <div class="invalid-feedback">Business place ownership is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="businessPlace" class="form-label">Business Location</label>
              <input type="text" class="form-control" id="businessPlace" formControlName="businessPlace"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('businessPlace')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('businessPlace')?.errors?.required) {
                <div class="invalid-feedback">Business location is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="currentBusinessExperience" class="form-label">Current Business Experience (Years)</label>
              <input type="number" class="form-control" id="currentBusinessExperience" formControlName="currentBusinessExperience"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('currentBusinessExperience')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('currentBusinessExperience')?.errors?.required) {
                <div class="invalid-feedback">Current business experience is required</div>
              }
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="totalPastBusinessExperience" class="form-label">Total Past Business Experience (Years)</label>
              <input type="number" class="form-control" id="totalPastBusinessExperience" formControlName="totalPastBusinessExperience"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('totalPastBusinessExperience')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('totalPastBusinessExperience')?.errors?.required) {
                <div class="invalid-feedback">Total past business experience is required</div>
              }
            </div>
          </div>



          <!-- ITR Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">ITR Information</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="itrStatus" class="form-label">ITR Status - ITR Since</label>
              <input type="text" class="form-control" id="itrStatus" formControlName="itrStatus"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('itrStatus')?.errors}">
              @if (isFormSubmitted && selfEmployeeForm.get('itrStatus')?.errors?.required) {
                <div class="invalid-feedback">ITR status is required</div>
              }
            </div>

            <div class="col-12">
              <label class="form-label">Last 3 years - Financial Year</label>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>FY</th>
                      <th>Provisional</th>
                      <th *ngIf="hasAnyITRWithProvisional()">Turnover</th>
                      <th *ngIf="hasAnyITRWithProvisional()">Gross Profit</th>
                      <th *ngIf="hasAnyITRWithProvisional()">Net Profit</th>
                      <th *ngIf="hasAnyITRWithProvisional()">Filing Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (itrYear of itrYearsArray.controls; track $index) {
                      <tr [formGroup]="$any(itrYear)">
                        <td>
                          <span>20{{ itrYear.get('financialYear')?.value }}</span>
                          <input type="hidden" formControlName="financialYear">
                        </td>
                        <td>
                          <select class="form-select form-select-sm" formControlName="provisional">
                            <option value="Yes">Yes</option>
                            <option value="No">No</option>
                          </select>
                        </td>
                        <td *ngIf="hasAnyITRWithProvisional()">
                          <input type="number" class="form-control form-control-sm" placeholder="Turnover amount" formControlName="amount"
                            [ngClass]="{'is-invalid': isFormSubmitted && itrYear.get('amount')?.errors}">
                          @if (isFormSubmitted && itrYear.get('amount')?.errors?.required) {
                            <div class="invalid-feedback d-block">Turnover is required</div>
                          }
                        </td>
                        <td *ngIf="hasAnyITRWithProvisional()">
                          <input type="number" class="form-control form-control-sm" placeholder="Gross profit amount" formControlName="grossProfit">
                        </td>
                        <td *ngIf="hasAnyITRWithProvisional()">
                          <input type="number" class="form-control form-control-sm" placeholder="Net profit amount" formControlName="netAmount">
                        </td>
                        <td *ngIf="hasAnyITRWithProvisional()">
                          <input type="date" class="form-control form-control-sm" formControlName="date">
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Financial Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Financial Information - Last 3 Years</h6>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 25%;" class="fw-medium">Financial Metrics</th>
                      <th style="width: 37.5%;" class="fw-medium text-center">Monthly</th>
                      <th style="width: 37.5%;" class="fw-medium text-center">Yearly</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="fw-medium bg-light">Inflow</td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="monthlyInflow"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('monthlyInflow')?.errors}"
                          placeholder="Enter monthly inflow">
                        @if (isFormSubmitted && selfEmployeeForm.get('monthlyInflow')?.errors?.required) {
                          <div class="invalid-feedback">Monthly inflow is required</div>
                        }
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="yearlyInflow"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('yearlyInflow')?.errors}"
                          placeholder="Enter yearly inflow">
                        @if (isFormSubmitted && selfEmployeeForm.get('yearlyInflow')?.errors?.required) {
                          <div class="invalid-feedback">Yearly inflow is required</div>
                        }
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-medium bg-light">Average Expenses</td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="monthlyExpenses"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('monthlyExpenses')?.errors}"
                          placeholder="Enter monthly expenses">
                        @if (isFormSubmitted && selfEmployeeForm.get('monthlyExpenses')?.errors?.required) {
                          <div class="invalid-feedback">Monthly expenses is required</div>
                        }
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="yearlyExpenses"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('yearlyExpenses')?.errors}"
                          placeholder="Enter yearly expenses">
                        @if (isFormSubmitted && selfEmployeeForm.get('yearlyExpenses')?.errors?.required) {
                          <div class="invalid-feedback">Yearly expenses is required</div>
                        }
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-medium bg-light">Net Profit</td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="monthlyNetProfitTable"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('monthlyNetProfitTable')?.errors}"
                          placeholder="Enter monthly net profit">
                        @if (isFormSubmitted && selfEmployeeForm.get('monthlyNetProfitTable')?.errors?.required) {
                          <div class="invalid-feedback">Monthly net profit is required</div>
                        }
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="yearlyNetProfit"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('yearlyNetProfit')?.errors}"
                          placeholder="Enter yearly net profit">
                        @if (isFormSubmitted && selfEmployeeForm.get('yearlyNetProfit')?.errors?.required) {
                          <div class="invalid-feedback">Yearly net profit is required</div>
                        }
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-medium bg-light">Turnover as per Books</td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="monthlyTurnoverTable"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('monthlyTurnoverTable')?.errors}"
                          placeholder="Enter monthly turnover">
                        @if (isFormSubmitted && selfEmployeeForm.get('monthlyTurnoverTable')?.errors?.required) {
                          <div class="invalid-feedback">Monthly turnover is required</div>
                        }
                      </td>
                      <td>
                        <input type="number" class="form-control form-control-sm" formControlName="yearlyTurnover"
                          [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('yearlyTurnover')?.errors}"
                          placeholder="Enter yearly turnover">
                        @if (isFormSubmitted && selfEmployeeForm.get('yearlyTurnover')?.errors?.required) {
                          <div class="invalid-feedback">Yearly turnover is required</div>
                        }
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Other Income Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Other Income Details</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="hasOtherIncome" class="form-label">Other Income</label>
              <select class="form-select" id="hasOtherIncome" formControlName="hasOtherIncome"
                [ngClass]="{'is-invalid': isFormSubmitted && selfEmployeeForm.get('hasOtherIncome')?.errors}">
                <option value="" selected>Select Option</option>
                @for (option of yesNoOptions; track option) {
                  <option [value]="option">{{ option }}</option>
                }
              </select>
              @if (isFormSubmitted && selfEmployeeForm.get('hasOtherIncome')?.errors?.required) {
                <div class="invalid-feedback">Please select an option</div>
              }
            </div>

            @if (selfEmployeeForm.get('hasOtherIncome')?.value === 'Yes') {
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="otherIncomeSource" class="form-label">Source</label>
                <select class="form-select" id="otherIncomeSource" formControlName="otherIncomeSource">
                  <option value="" selected>Select Source</option>
                  @for (source of otherIncomeSourceOptions; track source) {
                    <option [value]="source">{{ source }}</option>
                  }
                </select>
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="otherIncomeAmount" class="form-label">Amount</label>
                <input type="number" class="form-control" id="otherIncomeAmount" formControlName="otherIncomeAmount">
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="otherIncomeMode" class="form-label">Mode</label>
                <select class="form-select" id="otherIncomeMode" formControlName="otherIncomeMode">
                  <option value="" selected>Select Mode</option>
                  @for (mode of incomeModeOptions; track mode) {
                    <option [value]="mode">{{ mode }}</option>
                  }
                </select>
              </div>
            }
          </div>

          <!-- Banking Information Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary fw-semibold mb-0">Banking Information</h6>
              <small class="text-muted mb-3 d-block">Enter bank account details</small>
            </div>

            <!-- Current Accounts Section -->
            <div class="col-12 col-md-6 mb-4">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="text-secondary mb-0">Current Accounts</h6>
                <button type="button" class="btn btn-sm btn-outline-primary" (click)="addBankAccount('Current')">
                  <i class="bi bi-plus-circle me-1"></i> Add Account
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-bordered table-sm">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 15%;">Action</th>
                      <th>Bank Name</th>
                      <th>Branch Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (bankAccount of getCurrentAccounts(); track $index) {
                      <tr [formGroup]="$any(bankAccount)">
                        <td class="text-center">
                          <a href="javascript:void(0);" (click)="removeBankAccount(getCurrentAccountIndex($index))"
                            title="Delete" [class.disabled]="getCurrentAccounts().length <= 1">
                            <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                          </a>
                        </td>
                        <td>
                          <input type="text" class="form-control form-control-sm" formControlName="bankName"
                            [ngClass]="{'is-invalid': isFormSubmitted && bankAccount.get('bankName')?.errors}"
                            placeholder="Enter bank name">
                          <input type="hidden" formControlName="accountType">
                          @if (isFormSubmitted && bankAccount.get('bankName')?.errors?.required) {
                            <div class="invalid-feedback">Bank name is required</div>
                          }
                        </td>
                        <td>
                          <input type="text" class="form-control form-control-sm" formControlName="branchName"
                            [ngClass]="{'is-invalid': isFormSubmitted && bankAccount.get('branchName')?.errors}"
                            placeholder="Enter branch name">
                          @if (isFormSubmitted && bankAccount.get('branchName')?.errors?.required) {
                            <div class="invalid-feedback">Branch name is required</div>
                          }
                        </td>
                      </tr>
                    }
                    @if (getCurrentAccounts().length === 0) {
                      <tr>
                        <td colspan="3" class="text-center text-muted py-3">
                          <i class="bi bi-info-circle me-2"></i>No current accounts added
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Savings Accounts Section -->
            <div class="col-12 col-md-6 mb-4">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="text-secondary mb-0">Savings Accounts</h6>
                <button type="button" class="btn btn-sm btn-outline-primary" (click)="addBankAccount('Savings')">
                  <i class="bi bi-plus-circle me-1"></i> Add Account
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-bordered table-sm">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 15%;">Action</th>
                      <th>Bank Name</th>
                      <th>Branch Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (bankAccount of getSavingsAccounts(); track $index) {
                      <tr [formGroup]="$any(bankAccount)">
                        <td class="text-center">
                          <a href="javascript:void(0);" (click)="removeBankAccount(getSavingsAccountIndex($index))"
                            title="Delete" [class.disabled]="getSavingsAccounts().length <= 1">
                            <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                          </a>
                        </td>
                        <td>
                          <input type="text" class="form-control form-control-sm" formControlName="bankName"
                            [ngClass]="{'is-invalid': isFormSubmitted && bankAccount.get('bankName')?.errors}"
                            placeholder="Enter bank name">
                          <input type="hidden" formControlName="accountType">
                          @if (isFormSubmitted && bankAccount.get('bankName')?.errors?.required) {
                            <div class="invalid-feedback">Bank name is required</div>
                          }
                        </td>
                        <td>
                          <input type="text" class="form-control form-control-sm" formControlName="branchName"
                            [ngClass]="{'is-invalid': isFormSubmitted && bankAccount.get('branchName')?.errors}"
                            placeholder="Enter branch name">
                          @if (isFormSubmitted && bankAccount.get('branchName')?.errors?.required) {
                            <div class="invalid-feedback">Branch name is required</div>
                          }
                        </td>
                      </tr>
                    }
                    @if (getSavingsAccounts().length === 0) {
                      <tr>
                        <td colspan="3" class="text-center text-muted py-3">
                          <i class="bi bi-info-circle me-2"></i>No savings accounts added
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Business Statutory Documents Section -->
          <div class="row mb-4">
            <div class="col-12">
              <h6 class="text-primary mb-3">Business Statutory Documents</h6>
            </div>

            <div class="col-12 col-md-6 mb-3">
              <label for="businessStatutoryDocs" class="form-label">Document Details</label>
              <textarea class="form-control" id="businessStatutoryDocs" formControlName="businessStatutoryDocs" rows="3"></textarea>
            </div>
          </div>

         

          <!-- Loan Details Section -->
          <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="text-primary fw-semibold mb-0">Ongoing Loan Details</h6>
                <small class="text-muted">Excluding salary deduction</small>
              </div>
              <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addLoanDetail()">
                <i class="bi bi-plus-circle me-1"></i> Add Loan
              </button>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <div class="card shadow-sm rounded-3 border-0 mb-4">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead class="bg-light">
                        <tr>
                          <th style="width: 5%;" class="fw-medium text-center">Action</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '17%' : '17%'" class="fw-medium">Loan Type</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '16%' : '17%'" class="fw-medium">Bank Name</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '13%' : '15%'" class="fw-medium">Sanctioned Amt</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '13%' : '15%'" class="fw-medium">O/S Amt</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '10%' : '12%'" class="fw-medium">Tenure</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '10%' : '12%'" class="fw-medium">EMI Amt</th>
                          <th [style.width]="hasAnyLoanWithDefault() ? '6%' : '7%'" class="fw-medium">Default</th>
                          <th *ngIf="hasAnyLoanWithDefault()" style="width: 10%;" class="fw-medium">Example</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if (loanDetailsArray.length === 0) {
                          <tr>
                            <td [attr.colspan]="hasAnyLoanWithDefault() ? 9 : 8" class="text-center py-4 text-muted">
                              <div class="d-flex justify-content-center align-items-center">
                                <i class="bi bi-info-circle me-2"></i>
                                <span>No data found</span>
                              </div>
                            </td>
                          </tr>
                        } @else {
                          @for (loanDetail of loanDetailsArray.controls; track $index) {
                            <tr [formGroup]="$any(loanDetail)">
                              <td class="text-center">
                                <a href="javascript:void(0);"  (click)="removeLoanDetail($index)" title="Delete">
                                  <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                                </a>
                              </td>
                              <td>
                                <select class="form-select form-select-sm rounded-2" formControlName="loanType"
                                  [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('loanType')?.errors}">
                                  <option value="" selected>Select</option>
                                  @for (type of loanTypes; track type) {
                                    <option [value]="type">{{ type }}</option>
                                  }
                                </select>
                              </td>
                              <td>
                                <input type="text" class="form-control form-control-sm rounded-2" formControlName="bankName"
                                  [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('bankName')?.errors}">
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="sanctionedAmount"
                                  [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('sanctionedAmount')?.errors}">
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="outstandingAmount"
                                  [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('outstandingAmount')?.errors}">
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="tenure"
                                  [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('tenure')?.errors}">
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="emiAmount"
                                  [ngClass]="{'is-invalid': isFormSubmitted && loanDetail.get('emiAmount')?.errors}">
                              </td>
                              <td>
                                <select class="form-select form-select-sm rounded-2" formControlName="default">
                                  <option value="Yes">Yes</option>
                                  <option value="No">No</option>
                                </select>
                              </td>
                              <td *ngIf="hasAnyLoanWithDefault()">
                                <input
                                  *ngIf="loanDetail.get('default')?.value === 'Yes'"
                                  type="text"
                                  class="form-control form-control-sm rounded-2"
                                  formControlName="example"
                                  placeholder="Enter example">
                                <span *ngIf="loanDetail.get('default')?.value !== 'Yes'" class="text-muted">-</span>
                              </td>
                            </tr>
                          }
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Investment Details Section -->
          <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="text-primary fw-semibold mb-0">Investment Details</h6>
                <small class="text-muted">FD/RD/MIS/LIC/MF</small>
              </div>
              <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addInvestment()">
                <i class="bi bi-plus-circle me-1"></i> Add Investment
              </button>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <div class="card shadow-sm rounded-3 border-0 mb-4">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead class="bg-light">
                        <tr>
                          <th style="width: 5%;" class="fw-medium text-center">Action</th>
                          <th style="width: 15%;" class="fw-medium">Inv. Product</th>
                          <th style="width: 15%;" class="fw-medium">Institute Name</th>
                          <th style="width: 13%;" class="fw-medium">Inv. Yearly Amt</th>
                          <th style="width: 13%;" class="fw-medium">Inv. Mode</th>
                          <th style="width: 13%;" class="fw-medium">Start Dt</th>
                          <th style="width: 13%;" class="fw-medium">End Dt</th>
                          <th style="width: 13%;" class="fw-medium">Current Saving Amt</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if (investmentsArray.length === 0) {
                          <tr>
                            <td colspan="8" class="text-center py-4 text-muted">
                              <div class="d-flex justify-content-center align-items-center">
                                <i class="bi bi-info-circle me-2"></i>
                                <span>No data found</span>
                              </div>
                            </td>
                          </tr>
                        } @else {
                          @for (investment of investmentsArray.controls; track $index) {
                            <tr [formGroup]="$any(investment)">
                              <td class="text-center">
                                <a href="javascript:void(0);" (click)="removeInvestment($index)" title="Delete">
                                  <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                                </a>
                              </td>
                              <td>
                                <select class="form-select form-select-sm rounded-2" formControlName="investmentProduct"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('investmentProduct')?.errors}">
                                  <option value="" selected>Select</option>
                                  @for (product of investmentProducts; track product) {
                                    <option [value]="product">{{ product }}</option>
                                  }
                                </select>
                              </td>
                              <td>
                                <input type="text" class="form-control form-control-sm rounded-2" formControlName="instituteName"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('instituteName')?.errors}">
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="yearlyAmount"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('yearlyAmount')?.errors}">
                              </td>
                              <td>
                                <select class="form-select form-select-sm rounded-2" formControlName="investmentMode"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('investmentMode')?.errors}">
                                  <option value="" selected>Select</option>
                                  @for (mode of investmentModes; track mode) {
                                    <option [value]="mode">{{ mode }}</option>
                                  }
                                </select>
                              </td>
                              <td>
                                <input type="date" class="form-control form-control-sm rounded-2" formControlName="startDate"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('startDate')?.errors}">
                              </td>
                              <td>
                                <input type="date" class="form-control form-control-sm rounded-2" formControlName="endDate"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('endDate')?.errors}">
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="currentSavingAmount"
                                  [ngClass]="{'is-invalid': isFormSubmitted && investment.get('currentSavingAmount')?.errors}">
                              </td>
                            </tr>
                          }
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>



          <!-- Co-Applicants Section -->
          <div class="row mb-4" *ngIf="!isCoApplicantForm">
            <div class="col-12 d-flex justify-content-between align-items-center mb-3">
              <div>
                <h6 class="text-primary fw-semibold mb-0">Co-Applicants</h6>
                <small class="text-muted">Add all co-applicant information</small>
              </div>
              <button type="button" class="btn btn-sm btn-primary rounded-2" (click)="addCoApplicant()">
                <i class="bi bi-plus-circle me-1"></i> Add Co-Applicant
              </button>
            </div>

            <div class="col-12">
              <div class="table-responsive">
                <div class="card shadow-sm rounded-3 border-0 mb-4">
                  <div class="table-responsive">
                    <table class="table mb-0">
                      <thead class="bg-light">
                        <tr>
                          <th style="width: 5%;" class="fw-medium text-center">Action</th>
                          <th style="width: 17%;" class="fw-medium">Name</th>
                          <th style="width: 15%;" class="fw-medium">Relation</th>
                          <th style="width: 9%;" class="fw-medium">Age</th>
                          <th style="width: 17%;" class="fw-medium">Occupation</th>
                          <th style="width: 17%;" class="fw-medium">Income Source</th>
                          <th style="width: 20%;" class="fw-medium">Monthly Income</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if (coApplicantsArray.length === 0) {
                          <tr>
                            <td colspan="7" class="text-center py-4 text-muted">
                              <div class="d-flex justify-content-center align-items-center">
                                <i class="bi bi-info-circle me-2"></i>
                                <span>No data found</span>
                              </div>
                            </td>
                          </tr>
                        } @else {
                          @for (coApplicant of coApplicantsArray.controls; track $index) {
                            <tr [formGroup]="$any(coApplicant)">
                              <td class="text-center">
                                <div class="d-flex justify-content-center">
                                  <a href="javascript:void(0);" class="action-icon me-2" (click)="editCoApplicantSelfEmployedDetails($index)" title="Edit">
                                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                                  </a>
                                  <a href="javascript:void(0);" class="action-icon" (click)="removeCoApplicant($index)" title="Delete">
                                    <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                                  </a>
                                </div>
                              </td>
                              <td>
                                <input type="text" class="form-control form-control-sm rounded-2" formControlName="name"
                                  [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('name')?.errors}">
                              </td>
                              <td>
                                <select class="form-select form-select-sm rounded-2" formControlName="relation"
                                  [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('relation')?.errors}">
                                  <option value="" selected>Select</option>
                                  @for (type of relationshipTypes; track type) {
                                    <option [value]="type">{{ type }}</option>
                                  }
                                </select>
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="age"
                                  [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('age')?.errors}">
                              </td>
                              <td>
                                <select class="form-select form-select-sm rounded-2" formControlName="occupation"
                                  [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('occupation')?.errors}"
                                  (change)="onCoApplicantOccupationChange($index, coApplicant.get('occupation')?.value,
                                    coApplicant.get('occupation')?.value === 'Salaried Employee' ? salariedModal : selfEmployedModal)">
                                  <option value="" selected>Select</option>
                                  @for (type of occupationTypes; track type) {
                                    <option [value]="type">{{ type }}</option>
                                  }
                                </select>
                                <!-- Hidden debug info -->
                                <div class="d-none">
                                  <small>Product Type: {{ selectedProductType }}</small>
                                  <small>Is Home Loan: {{ isHomeLoan }}</small>
                                </div>
                              </td>
                              <td>
                                <div class="d-flex align-items-center">
                                  <input type="text" class="form-control form-control-sm rounded-2 me-2" formControlName="incomeSource"
                                    [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('incomeSource')?.errors}">
                                  <button *ngIf="coApplicant.get('occupation')?.value === 'Salaried Employee'"
                                    type="button" class="btn btn-sm btn-outline-primary"
                                    (click)="editCoApplicantSalariedDetails($index)">
                                    <i class="bi bi-pencil-square"></i>
                                  </button>
                                  <button *ngIf="coApplicant.get('occupation')?.value === 'Self Employed'"
                                    type="button" class="btn btn-sm btn-outline-primary"
                                    (click)="editCoApplicantSelfEmployedDetails($index)">
                                    <i class="bi bi-pencil-square"></i>
                                  </button>
                                </div>
                              </td>
                              <td>
                                <input type="number" class="form-control form-control-sm rounded-2" formControlName="monthlyIncome"
                                  [ngClass]="{'is-invalid': isFormSubmitted && coApplicant.get('monthlyIncome')?.errors}">
                              </td>
                            </tr>
                          }
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

           <!-- Property Details Section -->
          <div class="row mb-4" *ngIf="!isCoApplicantForm">
            <div class="col-12">
              <h6 class="text-primary mb-3">Property Information</h6>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="purchaseFrom" class="form-label">Purchase From</label>
              <select class="form-select" id="purchaseFrom" formControlName="purchaseFrom">
                <option value="" selected>Select Option</option>
                <option value="Resale">Resale</option>
                <option value="Developer">Developer</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="location" class="form-label">Location</label>
              <input type="text" class="form-control" id="location" formControlName="location"
                placeholder="Enter location">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="typeOfProperty" class="form-label">Type Of Property</label>
              <select class="form-select" id="typeOfProperty" formControlName="typeOfProperty">
                <option value="" selected>Select Property Type</option>
                @if (selectedProductSubType === 'NRPL') {
                  <option value="Shop">Shop</option>
                  <option value="Office">Office</option>
                } @else {
                  <option value="Bunglow">Bunglow</option>
                  <option value="NA Plot + Construction">NA Plot + Construction</option>
                  <option value="Duplex">Duplex</option>
                  <option value="Flat">Flat</option>
                }
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selfEmployeeForm.get('typeOfProperty')?.value === 'Flat'">
              <label for="configuration" class="form-label">Configuration (Only for Flat)</label>
              <select class="form-select" id="configuration" formControlName="configuration">
                <option value="" selected>Select Configuration</option>
                <option value="1RK">1RK</option>
                <option value="1BHK">1BHK</option>
                <option value="1.5 BHK">1.5 BHK</option>
                <option value="2BHK">2BHK</option>
                <option value="2.5 BHK">2.5 BHK</option>
                <option value="3BHK">3BHK</option>
                <option value="3.5 BHK">3.5 BHK</option>
                <option value="4BHK">4BHK</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="areaSqFt" class="form-label">Area (Sq.ft)</label>
              <input type="number" class="form-control" id="areaSqFt" formControlName="areaSqFt"
                placeholder="Enter area in sq.ft">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="salable" class="form-label">Salable</label>
              <input type="number" class="form-control" id="salable" formControlName="salable"
                placeholder="Enter salable area">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="carpet" class="form-label">Carpet</label>
              <input type="number" class="form-control" id="carpet" formControlName="carpet"
                placeholder="Enter carpet area">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ageOfProperty" class="form-label">Age of Property</label>
              <select class="form-select" id="ageOfProperty" formControlName="ageOfProperty">
                <option value="" selected>Select Age</option>
                <option value="Ready Possession">Ready Possession</option>
                @for (year of propertyAgeOptions; track year) {
                  <option [value]="year">{{ year }} {{ year === 1 ? 'Year' : 'Years' }}</option>
                }
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selfEmployeeForm.get('purchaseFrom')?.value === 'Developer'">
              <label for="constructionStage" class="form-label">Construction Stage (%)</label>
              <input type="number" class="form-control" id="constructionStage" formControlName="constructionStage"
                min="0" max="100" placeholder="Enter percentage (0-100)">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="approvalAuthority" class="form-label">Approval Authority</label>
              <select class="form-select" id="approvalAuthority" formControlName="approvalAuthority">
                <option value="" selected>Select Authority</option>
                <option value="Grampanchayat">Grampanchayat</option>
                <option value="Town Planning">Town Planning</option>
                <option value="MMRDA">MMRDA</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label class="form-label">Documents Status (Checkbox)</label>
              <div class="form-check-group">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="ccDocument" formControlName="ccDocument">
                  <label class="form-check-label" for="ccDocument">CC</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="ocDocument" formControlName="ocDocument">
                  <label class="form-check-label" for="ocDocument">OC</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="socRegistrationCertificate" formControlName="socRegistrationCertificate">
                  <label class="form-check-label" for="socRegistrationCertificate">SoC Registration Certificate</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="shareCertificate" formControlName="shareCertificate">
                  <label class="form-check-label" for="shareCertificate">Share Certificate</label>
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selfEmployeeForm.get('purchaseFrom')?.value === 'Developer'">
              <label for="apfFrom" class="form-label">APF From</label>
              <select class="form-select" id="apfFrom" formControlName="apfFrom">
                <option value="" selected>Select Bank</option>
                @for (bank of bankNameOptions; track bank) {
                  <option [value]="bank">{{ bank }}</option>
                }
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="noOfChainAgreement" class="form-label">No of Chain Agreement</label>
              <select class="form-select" id="noOfChainAgreement" formControlName="noOfChainAgreement">
                <option value="" selected>Select Number</option>
                @for (number of chainAgreementOptions; track number) {
                  <option [value]="number">{{ number }}</option>
                }
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="agreementStatus" class="form-label">Agreement Status</label>
              <select class="form-select" id="agreementStatus" formControlName="agreementStatus">
                <option value="" selected>Select Status</option>
                <option value="Done">Done</option>
                <option value="Not Done">Not Done</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="currentMV" class="form-label">Current M.V.</label>
              <input type="number" class="form-control" id="currentMV" formControlName="currentMV"
                placeholder="Enter current market value">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="agreementValue" class="form-label">Agreement Value</label>
              <input type="number" class="form-control" id="agreementValue" formControlName="agreementValue"
                placeholder="Enter agreement value" (input)="calculateOCR()">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="loanAmtRequired" class="form-label">Loan Amount</label>
              <input type="number" class="form-control" id="loanAmtRequired" formControlName="loanAmtRequired"
                placeholder="Enter loan amount required" (input)="calculateOCR()">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ocrRequired" class="form-label">OCR Required (Auto Calculated)</label>
              <input type="number" class="form-control" id="ocrRequired" formControlName="ocrRequired"
                readonly placeholder="Auto calculated">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ocrPercentage" class="form-label">OCR % (Auto Calculated)</label>
              <input type="number" class="form-control" id="ocrPercentage" formControlName="ocrPercentage"
                readonly placeholder="Auto calculated">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="ifBt" class="form-label">IF BT</label>
              <select class="form-select" id="ifBt" formControlName="ifBt">
                <option value="" selected>Select Option</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
            </div>
    
            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selfEmployeeForm.get('ifBt')?.value === 'Yes'">
              <label for="btBankName" class="form-label">Bank Name</label>
              <select class="form-select" id="btBankName" formControlName="btBankName">
                <option value="" selected>Select Bank</option>
                @for (bank of bankNameOptions; track bank) {
                  <option [value]="bank">{{ bank }}</option>
                 }
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3" *ngIf="selfEmployeeForm.get('ifBt')?.value === 'Yes'">
              <label for="currentOutstandingLoanAmount" class="form-label">Current Outstanding Loan Amount</label>
              <input type="number" class="form-control" id="currentOutstandingLoanAmount" formControlName="currentOutstandingLoanAmount"
                placeholder="Enter outstanding amount">
            </div>

            <!-- LRD Specific Fields -->
            <ng-container *ngIf="selectedProductSubType === 'LRD'">
              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="loiDetails" class="form-label">LOI Details</label>
                <input type="text" class="form-control" id="loiDetails" formControlName="loiDetails"
                  placeholder="Enter LOI details">
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="companyNameLicensee" class="form-label">Company Name (licensee)</label>
                <input type="text" class="form-control" id="companyNameLicensee" formControlName="companyNameLicensee"
                  placeholder="Enter company name">
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="leaseTenor" class="form-label">Lease Tenor</label>
                <input type="text" class="form-control" id="leaseTenor" formControlName="leaseTenor"
                  placeholder="Enter lease tenor">
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="startDate" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="startDate" formControlName="startDate">
              </div>

              <div class="col-12 col-md-6 col-lg-3 mb-3">
                <label for="monthlyRent" class="form-label">Monthly Rent</label>
                <input type="number" class="form-control" id="monthlyRent" formControlName="monthlyRent"
                  placeholder="Enter monthly rent amount">
              </div>
            </ng-container>



          </div>




        </form>
      </div>
    </div>
  </div>
</div>

<!-- Salaried Employee Modal Template -->
<ng-template #salariedModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Co-Applicant Salaried Employee Details</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss('Cross click')" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="coApplicantSalariedForm">
      <!-- Personal Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Personal Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppAge" class="form-label">Age</label>
          <input type="number" class="form-control" id="coAppAge" formControlName="age"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('age')?.errors}">
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('age')?.errors?.required) {
            <div class="invalid-feedback">Age is required</div>
          }
          @if (isCoAppSalariedFormSubmitted && (coApplicantSalariedForm.get('age')?.errors?.min || coApplicantSalariedForm.get('age')?.errors?.max)) {
            <div class="invalid-feedback">Age must be between 18 and 100</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppAddressType" class="form-label">Address Type</label>
          <select class="form-select" id="coAppAddressType" formControlName="addressType"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('addressType')?.errors}">
            <option value="" selected>Select Address Type</option>
            @for (type of addressTypes; track type) {
              <option [value]="type">{{ type }}</option>
            }
          </select>
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('addressType')?.errors?.required) {
            <div class="invalid-feedback">Address type is required</div>
          }
        </div>

        @if (isHomeLoan) {
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppResidentialStatus" class="form-label">Residential Status</label>
            <input type="text" class="form-control" id="coAppResidentialStatus" formControlName="residentialStatus"
              [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('residentialStatus')?.errors}">
            @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('residentialStatus')?.errors?.required) {
              <div class="invalid-feedback">Residential status is required</div>
            }
          </div>
        } @else {
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppPropertyOwnership" class="form-label">Property Ownership</label>
            <input type="text" class="form-control" id="coAppPropertyOwnership" formControlName="propertyOwnership"
              [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('propertyOwnership')?.errors}">
            @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('propertyOwnership')?.errors?.required) {
              <div class="invalid-feedback">Property ownership is required</div>
            }
          </div>
        }
      </div>

      <!-- Job Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Job Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppJobProfile" class="form-label">Employer Constitution</label>
          <select class="form-select" id="coAppJobProfile" formControlName="jobProfile"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('jobProfile')?.errors}">
            <option value="" selected>Select Employer Constitution</option>
            <option value="Govt">Govt</option>
            <option value="Semi Govt.">Semi Govt.</option>
            <option value="Private">Private</option>
            <option value="LLP">LLP</option>
            <option value="One Person Company">One Person Company</option>
            <option value="Partnership">Partnership</option>
            <option value="Proprietorship">Proprietorship</option>
          </select>
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('jobProfile')?.errors?.required) {
            <div class="invalid-feedback">Employer constitution is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppCompanyName" class="form-label">Company Name</label>
          <input type="text" class="form-control" id="coAppCompanyName" formControlName="companyName"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('companyName')?.errors}">
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('companyName')?.errors?.required) {
            <div class="invalid-feedback">Company name is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppCurrentJobExperience" class="form-label">Current Job Experience (Years)</label>
          <input type="number" class="form-control" id="coAppCurrentJobExperience" formControlName="currentJobExperience"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('currentJobExperience')?.errors}">
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('currentJobExperience')?.errors?.required) {
            <div class="invalid-feedback">Current job experience is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppTotalPastJobExperience" class="form-label">Total Past Job Experience (Years)</label>
          <input type="number" class="form-control" id="coAppTotalPastJobExperience" formControlName="totalPastJobExperience"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('totalPastJobExperience')?.errors}">
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('totalPastJobExperience')?.errors?.required) {
            <div class="invalid-feedback">Total past job experience is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppOfficeDocument" class="form-label">Employment Documents</label>
          <select class="form-select" id="coAppOfficeDocument" formControlName="officeDocument"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('officeDocument')?.errors}">
            <option value="" selected>Select Document</option>
            <option value="Appointment Letter">Appointment Letter</option>
            <option value="Confirmation Letter">Confirmation Letter</option>
          </select>
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('officeDocument')?.errors?.required) {
            <div class="invalid-feedback">Employment document is required</div>
          }
        </div>
      </div>

      <!-- Salary Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Salary Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSalaryDetails" class="form-label">Salary Documents</label>
          <select class="form-select" id="coAppSalaryDetails" formControlName="salaryDetails"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('salaryDetails')?.errors}">
            <option value="" selected>Select Salary Documents</option>
            <option value="Salary Slip">Salary Slip</option>
            <option value="Salary Certificate">Salary Certificate</option>
          </select>
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('salaryDetails')?.errors?.required) {
            <div class="invalid-feedback">Salary documents are required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppTotalGrossSalary" class="form-label">Total Gross Salary</label>
          <input type="number" class="form-control" id="coAppTotalGrossSalary" formControlName="totalGrossSalary"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('totalGrossSalary')?.errors}">
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('totalGrossSalary')?.errors?.required) {
            <div class="invalid-feedback">Total gross salary is required</div>
          }
        </div>

       

        <div class="col-12">
          <h6 class="text-primary mb-3">Deductions</h6>
        </div>

        <div formGroupName="deductions">
          <div class="row">
            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppPf" class="form-label">PF</label>
              <input type="number" class="form-control" id="coAppPf" formControlName="pf">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppPt" class="form-label">PT</label>
              <input type="number" class="form-control" id="coAppPt" formControlName="pt">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppHra" class="form-label">HRA</label>
              <input type="number" class="form-control" id="coAppHra" formControlName="hra">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppEsic" class="form-label">ESIC</label>
              <input type="number" class="form-control" id="coAppEsic" formControlName="esic">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppEmployeeLoan" class="form-label">Employee Loan</label>
              <input type="number" class="form-control" id="coAppEmployeeLoan" formControlName="employeeLoan">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppSocietyLoan" class="form-label">Society Loan</label>
              <input type="number" class="form-control" id="coAppSocietyLoan" formControlName="societyLoan">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-3">
              <label for="coAppOther" class="form-label">Other</label>
              <input type="number" class="form-control" id="coAppOther" formControlName="other">
            </div>
          </div>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppTotalDeduction" class="form-label">Total Deduction</label>
          <input type="number" class="form-control" id="coAppTotalDeduction" formControlName="totalDeduction" readonly>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppNetSalary" class="form-label">Net Salary</label>
          <input type="number" class="form-control" id="coAppNetSalary" formControlName="netSalary" readonly>
        </div>
         <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSalaryMode" class="form-label">Salary Mode</label>
          <select class="form-select" id="coAppSalaryMode" formControlName="salaryMode"
            [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('salaryMode')?.errors}">
            <option value="" selected>Select Salary Mode</option>
            <option value="Cash">Cash</option>
            <option value="Cheque">Cheque</option>
            <option value="Bank Transfer">Bank Transfer</option>
          </select>
          @if (isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('salaryMode')?.errors?.required) {
            <div class="invalid-feedback">Salary mode is required</div>
          }
        </div>
      </div>

      <!-- Banking Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary fw-semibold mb-0">Banking Information</h6>
          <small class="text-muted mb-3 d-block">Enter bank account details</small>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <table class="table table-bordered">
              <tbody formArrayName="bankAccounts">
                @for (bankAccount of coApplicantSalariedForm.get('bankAccounts')?.value; track $index) {
                  <tr [formGroupName]="$index">
                    <th style="width: 30%;">{{ bankAccount.accountType }} Account</th>
                    <td>
                      <input type="text" class="form-control" formControlName="bankName"
                        [ngClass]="{'is-invalid': isCoAppSalariedFormSubmitted && coApplicantSalariedForm.get('bankAccounts')?.get($index.toString())?.get('bankName')?.errors}">
                      <input type="hidden" formControlName="accountType">
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Loan Details Section -->
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
          <div>
            <h6 class="text-primary fw-semibold mb-0">Ongoing Loan Details</h6>
            <small class="text-muted">Excluding salary deduction</small>
          </div>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <div class="card shadow-sm rounded-3 border-0 mb-4">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '17%' : '17%'" class="fw-medium">Loan Type</th>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '16%' : '17%'" class="fw-medium">Bank Name</th>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '13%' : '15%'" class="fw-medium">Sanctioned Amt</th>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '13%' : '15%'" class="fw-medium">O/S Amt</th>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '10%' : '12%'" class="fw-medium">Tenure</th>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '10%' : '12%'" class="fw-medium">EMI Amt</th>
                      <th [style.width]="hasAnyCoAppSalariedLoanWithDefault() ? '11%' : '12%'" class="fw-medium">Default</th>
                      <th *ngIf="hasAnyCoAppSalariedLoanWithDefault()" style="width: 10%;" class="fw-medium">Example</th>
                    </tr>
                  </thead>
                  <tbody formArrayName="loanDetails">
                    @if (coApplicantSalariedForm.get('loanDetails')?.value?.length === 0) {
                      <tr>
                        <td [attr.colspan]="hasAnyCoAppSalariedLoanWithDefault() ? 8 : 7" class="text-center py-4 text-muted">
                          <div class="d-flex justify-content-center align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>No data found</span>
                          </div>
                        </td>
                      </tr>
                    } @else {
                      @for (loanDetail of coApplicantSalariedForm.get('loanDetails')?.value; track $index) {
                        <tr [formGroupName]="$index">
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="loanType">
                              <option value="" selected>Select</option>
                              @for (type of loanTypes; track type) {
                                <option [value]="type">{{ type }}</option>
                              }
                            </select>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="bankName">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="sanctionedAmount">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="outstandingAmount">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="tenure">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="emiAmount">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="default">
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                            </select>
                          </td>
                          <td *ngIf="hasAnyCoAppSalariedLoanWithDefault()">
                            <input
                              *ngIf="loanDetail.get('default')?.value === 'Yes'"
                              type="text"
                              class="form-control form-control-sm rounded-2"
                              formControlName="example"
                              placeholder="Enter example">
                            <span *ngIf="loanDetail.get('default')?.value !== 'Yes'" class="text-muted">-</span>
                          </td>
                        </tr>
                      }
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Investment Details Section -->
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
          <div>
            <h6 class="text-primary fw-semibold mb-0">Investment Details</h6>
            <small class="text-muted">FD/RD/MIS/LIC/MF</small>
          </div>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <div class="card shadow-sm rounded-3 border-0 mb-4">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 15%;" class="fw-medium">Inv. Product</th>
                      <th style="width: 15%;" class="fw-medium">Institute Name</th>
                      <th style="width: 13%;" class="fw-medium">Inv. Yearly Amt</th>
                      <th style="width: 13%;" class="fw-medium">Inv. Mode</th>
                      <th style="width: 13%;" class="fw-medium">Start Dt</th>
                      <th style="width: 13%;" class="fw-medium">End Dt</th>
                      <th style="width: 18%;" class="fw-medium">Current Saving Amt</th>
                    </tr>
                  </thead>
                  <tbody formArrayName="investments">
                    @if (coApplicantSalariedForm.get('investments')?.value?.length === 0) {
                      <tr>
                        <td colspan="7" class="text-center py-4 text-muted">
                          <div class="d-flex justify-content-center align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>No data found</span>
                          </div>
                        </td>
                      </tr>
                    } @else {
                      @for (investment of coApplicantSalariedForm.get('investments')?.value; track $index) {
                        <tr [formGroupName]="$index">
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="investmentProduct">
                              <option value="" selected>Select</option>
                              @for (product of investmentProducts; track product) {
                                <option [value]="product">{{ product }}</option>
                              }
                            </select>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="instituteName">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="yearlyAmount">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="investmentMode">
                              <option value="" selected>Select</option>
                              @for (mode of investmentModes; track mode) {
                                <option [value]="mode">{{ mode }}</option>
                              }
                            </select>
                          </td>
                          <td>
                            <input type="date" class="form-control form-control-sm rounded-2" formControlName="startDate">
                          </td>
                          <td>
                            <input type="date" class="form-control form-control-sm rounded-2" formControlName="endDate">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="currentSavingAmount">
                          </td>
                        </tr>
                      }
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('Cancel')">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="saveCoApplicantSalariedForm(modal)">Save</button>
  </div>
</ng-template>

<!-- Self Employed Modal Template -->
<ng-template #selfEmployedModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Co-Applicant Self-Employed Details</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss('Cross click')" aria-label="Close"></button>
  </div>
  <div class="modal-body">
    <!-- Debug information - hidden in production -->
    <div class="alert alert-info mb-3">
      <small>Current Product Type: <strong>{{ selectedProductType }}</strong></small>
      <br>
      <small>Is Home Loan: <strong>{{ isHomeLoan }}</strong></small>
    </div>

    <form [formGroup]="coApplicantSelfEmployedForm">
      <!-- Personal Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Personal Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfAge" class="form-label">Age</label>
          <input type="number" class="form-control" id="coAppSelfAge" formControlName="age"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('age')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('age')?.errors?.required) {
            <div class="invalid-feedback">Age is required</div>
          }
          @if (isCoAppSelfEmployedFormSubmitted && (coApplicantSelfEmployedForm.get('age')?.errors?.min || coApplicantSelfEmployedForm.get('age')?.errors?.max)) {
            <div class="invalid-feedback">Age must be between 18 and 100</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfAddressType" class="form-label">Address Type</label>
          <select class="form-select" id="coAppSelfAddressType" formControlName="addressType"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('addressType')?.errors}">
            <option value="" selected>Select Address Type</option>
            @for (type of addressTypes; track type) {
              <option [value]="type">{{ type }}</option>
            }
          </select>
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('addressType')?.errors?.required) {
            <div class="invalid-feedback">Address type is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfStatus" class="form-label">Status</label>
          <select class="form-select" id="coAppSelfStatus" formControlName="status"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('status')?.errors}">
            <option value="" selected>Select Status</option>
            @for (option of statusOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('status')?.errors?.required) {
            <div class="invalid-feedback">Status is required</div>
          }
        </div>
      </div>

      <!-- Business Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Business Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfProfession" class="form-label">Profession</label>
          <select class="form-select" id="coAppSelfProfession" formControlName="profession"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('profession')?.errors}">
            <option value="" selected>Select Profession</option>
            @for (option of professionOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('profession')?.errors?.required) {
            <div class="invalid-feedback">Profession is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfBusinessType" class="form-label">Type of Business</label>
          <select class="form-select" id="coAppSelfBusinessType" formControlName="businessType"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessType')?.errors}">
            <option value="" selected>Select Business Type</option>
            @for (option of businessTypeOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessType')?.errors?.required) {
            <div class="invalid-feedback">Business type is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfNatureOfBusiness" class="form-label">Nature of Business</label>
          <input type="text" class="form-control" id="coAppSelfNatureOfBusiness" formControlName="natureOfBusiness"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('natureOfBusiness')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('natureOfBusiness')?.errors?.required) {
            <div class="invalid-feedback">Nature of business is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfBusinessName" class="form-label">Business Name</label>
          <input type="text" class="form-control" id="coAppSelfBusinessName" formControlName="businessName"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessName')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessName')?.errors?.required) {
            <div class="invalid-feedback">Business name is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfBusinessPlaceOwnership" class="form-label">Business Place Ownership</label>
          <select class="form-select" id="coAppSelfBusinessPlaceOwnership" formControlName="businessPlaceOwnership"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessPlaceOwnership')?.errors}">
            <option value="" selected>Select Ownership</option>
            @for (option of businessPlaceOwnershipOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessPlaceOwnership')?.errors?.required) {
            <div class="invalid-feedback">Business place ownership is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfBusinessPlace" class="form-label">Business Location</label>
          <input type="text" class="form-control" id="coAppSelfBusinessPlace" formControlName="businessPlace"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessPlace')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessPlace')?.errors?.required) {
            <div class="invalid-feedback">Business location is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfCurrentBusinessExperience" class="form-label">Current Business Experience (Years)</label>
          <input type="number" class="form-control" id="coAppSelfCurrentBusinessExperience" formControlName="currentBusinessExperience"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('currentBusinessExperience')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('currentBusinessExperience')?.errors?.required) {
            <div class="invalid-feedback">Current business experience is required</div>
          }
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfTotalPastBusinessExperience" class="form-label">Total Past Business Experience (Years)</label>
          <input type="number" class="form-control" id="coAppSelfTotalPastBusinessExperience" formControlName="totalPastBusinessExperience"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('totalPastBusinessExperience')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('totalPastBusinessExperience')?.errors?.required) {
            <div class="invalid-feedback">Total past business experience is required</div>
          }
        </div>

        <!-- Additional LAP/LRD specific business fields -->
        @if (!isHomeLoan) {
          <div class="col-12">
            <h6 class="text-primary mb-3 mt-3">Additional Business Details</h6>
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessAddress" class="form-label">Business Address</label>
            <input type="text" class="form-control" id="coAppSelfBusinessAddress" formControlName="businessAddress"
              [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessAddress')?.errors}">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessPincode" class="form-label">Business Pincode</label>
            <input type="text" class="form-control" id="coAppSelfBusinessPincode" formControlName="businessPincode"
              [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessPincode')?.errors}">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessCity" class="form-label">Business City</label>
            <input type="text" class="form-control" id="coAppSelfBusinessCity" formControlName="businessCity"
              [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessCity')?.errors}">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessState" class="form-label">Business State</label>
            <input type="text" class="form-control" id="coAppSelfBusinessState" formControlName="businessState"
              [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessState')?.errors}">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfGstRegistered" class="form-label">GST Registered</label>
            <select class="form-select" id="coAppSelfGstRegistered" formControlName="gstRegistered">
              <option value="Yes">Yes</option>
              <option value="No">No</option>
            </select>
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfGstNumber" class="form-label">GST Number</label>
            <input type="text" class="form-control" id="coAppSelfGstNumber" formControlName="gstNumber">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessOwnership" class="form-label">Business Premises Ownership</label>
            <select class="form-select" id="coAppSelfBusinessOwnership" formControlName="businessOwnership"
              [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('businessOwnership')?.errors}">
              <option value="" selected>Select Option</option>
              <option value="Owned">Owned</option>
              <option value="Rented">Rented</option>
              <option value="Leased">Leased</option>
            </select>
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessPremisesRent" class="form-label">Business Premises Rent (Monthly)</label>
            <input type="number" class="form-control" id="coAppSelfBusinessPremisesRent" formControlName="businessPremisesRent">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfNumberOfEmployees" class="form-label">Number of Employees</label>
            <input type="number" class="form-control" id="coAppSelfNumberOfEmployees" formControlName="numberOfEmployees">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessLicenseNumber" class="form-label">Business License Number</label>
            <input type="text" class="form-control" id="coAppSelfBusinessLicenseNumber" formControlName="businessLicenseNumber">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfBusinessRegistrationDate" class="form-label">Business Registration Date</label>
            <input type="date" class="form-control" id="coAppSelfBusinessRegistrationDate" formControlName="businessRegistrationDate">
          </div>
        }
      </div>



      <!-- ITR Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">ITR Information</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfItrStatus" class="form-label">ITR Status - ITR Since</label>
          <input type="text" class="form-control" id="coAppSelfItrStatus" formControlName="itrStatus"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('itrStatus')?.errors}">
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('itrStatus')?.errors?.required) {
            <div class="invalid-feedback">ITR status is required</div>
          }
        </div>

        <div class="col-12">
          <label class="form-label">Last 3 years - Financial Year</label>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>FY</th>
                  <th>Provisional</th>
                  <th *ngIf="hasAnyCoAppITRWithProvisional()">Turnover</th>
                  <th *ngIf="hasAnyCoAppITRWithProvisional()">Gross Profit</th>
                  <th *ngIf="hasAnyCoAppITRWithProvisional()">Net Profit</th>
                  <th *ngIf="hasAnyCoAppITRWithProvisional()">Filing Date</th>
                </tr>
              </thead>
              <tbody formArrayName="itrYears">
                @for (itrYear of coApplicantSelfEmployedForm.get('itrYears')?.value; track $index) {
                  <tr [formGroupName]="$index">
                    <td>
                      <span>20{{ itrYear.financialYear }}</span>
                      <input type="hidden" formControlName="financialYear">
                    </td>
                    <td>
                      <select class="form-select form-select-sm" formControlName="provisional">
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                      </select>
                    </td>
                    <td *ngIf="hasAnyCoAppITRWithProvisional()">
                      <input type="number" class="form-control form-control-sm" placeholder="Turnover amount" formControlName="amount"
                        [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('itrYears')?.get($index.toString())?.get('amount')?.errors}">
                    </td>
                    <td *ngIf="hasAnyCoAppITRWithProvisional()">
                      <input type="number" class="form-control form-control-sm" placeholder="Gross profit amount" formControlName="grossProfit">
                    </td>
                    <td *ngIf="hasAnyCoAppITRWithProvisional()">
                      <input type="number" class="form-control form-control-sm" placeholder="Net profit amount" formControlName="netAmount">
                    </td>
                    <td *ngIf="hasAnyCoAppITRWithProvisional()">
                      <input type="date" class="form-control form-control-sm" formControlName="date">
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Financial Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Financial Information - Last 3 Years</h6>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead class="bg-light">
                <tr>
                  <th style="width: 25%;" class="fw-medium">Financial Metrics</th>
                  <th style="width: 37.5%;" class="fw-medium text-center">Monthly</th>
                  <th style="width: 37.5%;" class="fw-medium text-center">Yearly</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="fw-medium bg-light">Inflow</td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="monthlyInflow"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyInflow')?.errors}"
                      placeholder="Enter monthly inflow">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyInflow')?.errors?.required) {
                      <div class="invalid-feedback">Monthly inflow is required</div>
                    }
                  </td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="yearlyInflow"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyInflow')?.errors}"
                      placeholder="Enter yearly inflow">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyInflow')?.errors?.required) {
                      <div class="invalid-feedback">Yearly inflow is required</div>
                    }
                  </td>
                </tr>
                <tr>
                  <td class="fw-medium bg-light">Average Expenses</td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="monthlyExpenses"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyExpenses')?.errors}"
                      placeholder="Enter monthly expenses">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyExpenses')?.errors?.required) {
                      <div class="invalid-feedback">Monthly expenses is required</div>
                    }
                  </td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="yearlyExpenses"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyExpenses')?.errors}"
                      placeholder="Enter yearly expenses">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyExpenses')?.errors?.required) {
                      <div class="invalid-feedback">Yearly expenses is required</div>
                    }
                  </td>
                </tr>
                <tr>
                  <td class="fw-medium bg-light">Net Profit</td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="monthlyNetProfitTable"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyNetProfitTable')?.errors}"
                      placeholder="Enter monthly net profit">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyNetProfitTable')?.errors?.required) {
                      <div class="invalid-feedback">Monthly net profit is required</div>
                    }
                  </td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="yearlyNetProfit"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyNetProfit')?.errors}"
                      placeholder="Enter yearly net profit">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyNetProfit')?.errors?.required) {
                      <div class="invalid-feedback">Yearly net profit is required</div>
                    }
                  </td>
                </tr>
                <tr>
                  <td class="fw-medium bg-light">Turnover as per Books</td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="monthlyTurnoverTable"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyTurnoverTable')?.errors}"
                      placeholder="Enter monthly turnover">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('monthlyTurnoverTable')?.errors?.required) {
                      <div class="invalid-feedback">Monthly turnover is required</div>
                    }
                  </td>
                  <td>
                    <input type="number" class="form-control form-control-sm" formControlName="yearlyTurnover"
                      [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyTurnover')?.errors}"
                      placeholder="Enter yearly turnover">
                    @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('yearlyTurnover')?.errors?.required) {
                      <div class="invalid-feedback">Yearly turnover is required</div>
                    }
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Other Income Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Other Income Details</h6>
        </div>

        <div class="col-12 col-md-6 col-lg-3 mb-3">
          <label for="coAppSelfHasOtherIncome" class="form-label">Other Income</label>
          <select class="form-select" id="coAppSelfHasOtherIncome" formControlName="hasOtherIncome"
            [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('hasOtherIncome')?.errors}">
            <option value="" selected>Select Option</option>
            @for (option of yesNoOptions; track option) {
              <option [value]="option">{{ option }}</option>
            }
          </select>
          @if (isCoAppSelfEmployedFormSubmitted && coApplicantSelfEmployedForm.get('hasOtherIncome')?.errors?.required) {
            <div class="invalid-feedback">Please select an option</div>
          }
        </div>

        @if (coApplicantSelfEmployedForm.get('hasOtherIncome')?.value === 'Yes') {
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfOtherIncomeSource" class="form-label">Source</label>
            <select class="form-select" id="coAppSelfOtherIncomeSource" formControlName="otherIncomeSource">
              <option value="" selected>Select Source</option>
              @for (source of otherIncomeSourceOptions; track source) {
                <option [value]="source">{{ source }}</option>
              }
            </select>
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfOtherIncomeAmount" class="form-label">Amount</label>
            <input type="number" class="form-control" id="coAppSelfOtherIncomeAmount" formControlName="otherIncomeAmount">
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="coAppSelfOtherIncomeMode" class="form-label">Mode</label>
            <select class="form-select" id="coAppSelfOtherIncomeMode" formControlName="otherIncomeMode">
              <option value="" selected>Select Mode</option>
              @for (mode of incomeModeOptions; track mode) {
                <option [value]="mode">{{ mode }}</option>
              }
            </select>
          </div>
        }
      </div>

      <!-- Banking Information Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary fw-semibold mb-0">Banking Information</h6>
          <small class="text-muted mb-3 d-block">Enter bank account details</small>
        </div>

        <!-- Current Accounts Section -->
        <div class="col-12 col-md-6 mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="text-secondary mb-0">Current Accounts</h6>
            <button type="button" class="btn btn-sm btn-outline-primary" (click)="addCoAppSelfEmployedBankAccount('Current')">
              <i class="bi bi-plus-circle me-1"></i> Add Account
            </button>
          </div>
          <div class="table-responsive">
            <table class="table table-bordered table-sm">
              <thead class="bg-light">
                <tr>
                  <th style="width: 15%;">Action</th>
                  <th>Bank Name</th>
                  <th>Branch Name</th>
                </tr>
              </thead>
              <tbody>
                @for (bankAccount of getCoAppSelfEmployedCurrentAccounts(); track $index) {
                  <tr [formGroup]="$any(bankAccount)">
                    <td class="text-center">
                      <a href="javascript:void(0);" (click)="removeCoAppSelfEmployedBankAccount(getCoAppSelfEmployedCurrentAccountIndex($index))"
                        title="Delete" [class.disabled]="getCoAppSelfEmployedCurrentAccounts().length <= 1">
                        <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                      </a>
                    </td>
                    <td>
                      <input type="text" class="form-control form-control-sm" formControlName="bankName"
                        [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && bankAccount.get('bankName')?.errors}"
                        placeholder="Enter bank name">
                      <input type="hidden" formControlName="accountType">
                      @if (isCoAppSelfEmployedFormSubmitted && bankAccount.get('bankName')?.errors?.required) {
                        <div class="invalid-feedback">Bank name is required</div>
                      }
                    </td>
                    <td>
                      <input type="text" class="form-control form-control-sm" formControlName="branchName"
                        [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && bankAccount.get('branchName')?.errors}"
                        placeholder="Enter branch name">
                      @if (isCoAppSelfEmployedFormSubmitted && bankAccount.get('branchName')?.errors?.required) {
                        <div class="invalid-feedback">Branch name is required</div>
                      }
                    </td>
                  </tr>
                }
                @if (getCoAppSelfEmployedCurrentAccounts().length === 0) {
                  <tr>
                    <td colspan="3" class="text-center text-muted py-3">
                      <i class="bi bi-info-circle me-2"></i>No current accounts added
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>

        <!-- Savings Accounts Section -->
        <div class="col-12 col-md-6 mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="text-secondary mb-0">Savings Accounts</h6>
            <button type="button" class="btn btn-sm btn-outline-primary" (click)="addCoAppSelfEmployedBankAccount('Savings')">
              <i class="bi bi-plus-circle me-1"></i> Add Account
            </button>
          </div>
          <div class="table-responsive">
            <table class="table table-bordered table-sm">
              <thead class="bg-light">
                <tr>
                  <th style="width: 15%;">Action</th>
                  <th>Bank Name</th>
                  <th>Branch Name</th>
                </tr>
              </thead>
              <tbody>
                @for (bankAccount of getCoAppSelfEmployedSavingsAccounts(); track $index) {
                  <tr [formGroup]="$any(bankAccount)">
                    <td class="text-center">
                      <a href="javascript:void(0);" (click)="removeCoAppSelfEmployedBankAccount(getCoAppSelfEmployedSavingsAccountIndex($index))"
                        title="Delete" [class.disabled]="getCoAppSelfEmployedSavingsAccounts().length <= 1">
                        <i data-feather="trash" class="icon-sm" appFeatherIcon></i>
                      </a>
                    </td>
                    <td>
                      <input type="text" class="form-control form-control-sm" formControlName="bankName"
                        [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && bankAccount.get('bankName')?.errors}"
                        placeholder="Enter bank name">
                      <input type="hidden" formControlName="accountType">
                      @if (isCoAppSelfEmployedFormSubmitted && bankAccount.get('bankName')?.errors?.required) {
                        <div class="invalid-feedback">Bank name is required</div>
                      }
                    </td>
                    <td>
                      <input type="text" class="form-control form-control-sm" formControlName="branchName"
                        [ngClass]="{'is-invalid': isCoAppSelfEmployedFormSubmitted && bankAccount.get('branchName')?.errors}"
                        placeholder="Enter branch name">
                      @if (isCoAppSelfEmployedFormSubmitted && bankAccount.get('branchName')?.errors?.required) {
                        <div class="invalid-feedback">Branch name is required</div>
                      }
                    </td>
                  </tr>
                }
                @if (getCoAppSelfEmployedSavingsAccounts().length === 0) {
                  <tr>
                    <td colspan="3" class="text-center text-muted py-3">
                      <i class="bi bi-info-circle me-2"></i>No savings accounts added
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Business Statutory Documents Section -->
      <div class="row mb-4">
        <div class="col-12">
          <h6 class="text-primary mb-3">Business Statutory Documents</h6>
        </div>

        <div class="col-12 col-md-6 mb-3">
          <label for="coAppSelfBusinessStatutoryDocs" class="form-label">Document Details</label>
          <textarea class="form-control" id="coAppSelfBusinessStatutoryDocs" formControlName="businessStatutoryDocs" rows="3"></textarea>
        </div>
      </div>



      <!-- Loan Details Section -->
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
          <div>
            <h6 class="text-primary fw-semibold mb-0">Ongoing Loan Details</h6>
            <small class="text-muted">Excluding salary deduction</small>
          </div>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <div class="card shadow-sm rounded-3 border-0 mb-4">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '17%' : '17%'" class="fw-medium">Loan Type</th>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '16%' : '17%'" class="fw-medium">Bank Name</th>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '13%' : '15%'" class="fw-medium">Sanctioned Amt</th>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '13%' : '15%'" class="fw-medium">O/S Amt</th>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '10%' : '12%'" class="fw-medium">Tenure</th>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '10%' : '12%'" class="fw-medium">EMI Amt</th>
                      <th [style.width]="hasAnyCoAppSelfEmployedLoanWithDefault() ? '11%' : '12%'" class="fw-medium">Default</th>
                      <th *ngIf="hasAnyCoAppSelfEmployedLoanWithDefault()" style="width: 10%;" class="fw-medium">Example</th>
                    </tr>
                  </thead>
                  <tbody formArrayName="loanDetails">
                    @if (coApplicantSelfEmployedForm.get('loanDetails')?.value?.length === 0) {
                      <tr>
                        <td [attr.colspan]="hasAnyCoAppSelfEmployedLoanWithDefault() ? 8 : 7" class="text-center py-4 text-muted">
                          <div class="d-flex justify-content-center align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>No data found</span>
                          </div>
                        </td>
                      </tr>
                    } @else {
                      @for (loanDetail of coApplicantSelfEmployedForm.get('loanDetails')?.value; track $index) {
                        <tr [formGroupName]="$index">
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="loanType">
                              <option value="" selected>Select</option>
                              @for (type of loanTypes; track type) {
                                <option [value]="type">{{ type }}</option>
                              }
                            </select>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="bankName">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="sanctionedAmount">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="outstandingAmount">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="tenure">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="emiAmount">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="default">
                              <option value="Yes">Yes</option>
                              <option value="No">No</option>
                            </select>
                          </td>
                          <td *ngIf="hasAnyCoAppSelfEmployedLoanWithDefault()">
                            <input
                              *ngIf="loanDetail.get('default')?.value === 'Yes'"
                              type="text"
                              class="form-control form-control-sm rounded-2"
                              formControlName="example"
                              placeholder="Enter example">
                            <span *ngIf="loanDetail.get('default')?.value !== 'Yes'" class="text-muted">-</span>
                          </td>
                        </tr>
                      }
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Investment Details Section -->
      <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
          <div>
            <h6 class="text-primary fw-semibold mb-0">Investment Details</h6>
            <small class="text-muted">FD/RD/MIS/LIC/MF</small>
          </div>
        </div>

        <div class="col-12">
          <div class="table-responsive">
            <div class="card shadow-sm rounded-3 border-0 mb-4">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead class="bg-light">
                    <tr>
                      <th style="width: 15%;" class="fw-medium">Inv. Product</th>
                      <th style="width: 15%;" class="fw-medium">Institute Name</th>
                      <th style="width: 13%;" class="fw-medium">Inv. Yearly Amt</th>
                      <th style="width: 13%;" class="fw-medium">Inv. Mode</th>
                      <th style="width: 13%;" class="fw-medium">Start Dt</th>
                      <th style="width: 13%;" class="fw-medium">End Dt</th>
                      <th style="width: 18%;" class="fw-medium">Current Saving Amt</th>
                    </tr>
                  </thead>
                  <tbody formArrayName="investments">
                    @if (coApplicantSelfEmployedForm.get('investments')?.value?.length === 0) {
                      <tr>
                        <td colspan="7" class="text-center py-4 text-muted">
                          <div class="d-flex justify-content-center align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            <span>No data found</span>
                          </div>
                        </td>
                      </tr>
                    } @else {
                      @for (investment of coApplicantSelfEmployedForm.get('investments')?.value; track $index) {
                        <tr [formGroupName]="$index">
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="investmentProduct">
                              <option value="" selected>Select</option>
                              @for (product of investmentProducts; track product) {
                                <option [value]="product">{{ product }}</option>
                              }
                            </select>
                          </td>
                          <td>
                            <input type="text" class="form-control form-control-sm rounded-2" formControlName="instituteName">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="yearlyAmount">
                          </td>
                          <td>
                            <select class="form-select form-select-sm rounded-2" formControlName="investmentMode">
                              <option value="" selected>Select</option>
                              @for (mode of investmentModes; track mode) {
                                <option [value]="mode">{{ mode }}</option>
                              }
                            </select>
                          </td>
                          <td>
                            <input type="date" class="form-control form-control-sm rounded-2" formControlName="startDate">
                          </td>
                          <td>
                            <input type="date" class="form-control form-control-sm rounded-2" formControlName="endDate">
                          </td>
                          <td>
                            <input type="number" class="form-control form-control-sm rounded-2" formControlName="currentSavingAmount">
                          </td>
                        </tr>
                      }
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div> 

    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('Cancel')">Cancel</button>
    <button type="button" class="btn btn-outline-primary me-2" (click)="saveCoApplicantSelfEmployedForm()">Save</button>
    
  </div>
</ng-template>
