import { Routes } from '@angular/router';
import { dynamicAuthGuard } from '../../../../core/guards/dynamic-auth.guard';

export default [
    {
        path: '',
        loadComponent: () => import('./login-at-institute.component').then(c => c.<PERSON>gin<PERSON>tInstituteComponent),
        canActivate: [dynamicAuthGuard],
        data: {
            permissions: ['ops:access']
        }
    }
] as Routes;
