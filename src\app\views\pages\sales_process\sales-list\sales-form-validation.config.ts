/**
 * Sales Form Validation Configuration
 * Centralized validation rules and error messages for the sales form
 */

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  email?: boolean;
  min?: number;
  max?: number;
  custom?: (value: any, formData?: any) => boolean;
}

export interface FieldValidation {
  rules: ValidationRule;
  errorMessages: {
    required?: string;
    minLength?: string;
    maxLength?: string;
    pattern?: string;
    email?: string;
    min?: string;
    max?: string;
    custom?: string;
  };
  conditionalRequired?: (formData: any) => boolean;
}

export interface SalesFormValidationConfig {
  [fieldName: string]: FieldValidation;
}

/**
 * Comprehensive validation configuration for sales form fields
 */
export const SALES_FORM_VALIDATION: SalesFormValidationConfig = {
  // Core required fields
  selectedLeadCategory: {
    rules: { required: true },
    errorMessages: {
      required: 'Lead Category is required'
    }
  },

  selectedSource: {
    rules: { required: true },
    errorMessages: {
      required: 'Source is required'
    }
  },

  selectedLeadDataType: {
    rules: { required: true },
    errorMessages: {
      required: 'Lead Data Type is required when Lead Category is "Lead Data"'
    },
    conditionalRequired: (formData) => 
      formData.selectedLeadCategory === 'Lead Data' || formData.selectedLeadCategory === 'Lead data'
  },

  // Associate Category specific fields
  associateNameCategory: {
    rules: { required: true, minLength: 2, maxLength: 100 },
    errorMessages: {
      required: 'Associate Name is required when Lead Category is "Associate"',
      minLength: 'Associate Name must be at least 2 characters long',
      maxLength: 'Associate Name cannot exceed 100 characters'
    },
    conditionalRequired: (formData) => formData.selectedLeadCategory === 'Associate'
  },

  // Company field validation
  company: {
    rules: { minLength: 2, maxLength: 200 },
    errorMessages: {
      required: 'Company name is required for this lead type',
      minLength: 'Company name must be at least 2 characters long',
      maxLength: 'Company name cannot exceed 200 characters'
    },
    conditionalRequired: (formData) => 
      formData.selectedLeadCategory === 'Lead Data' && 
      formData.selectedLeadDataType && 
      ['Construction Funding', 'Hospital Funding', 'Education Funding'].includes(formData.selectedLeadDataType)
  },

  // Education/Hospital Funding specific fields
  selectedConstitution: {
    rules: { required: true },
    errorMessages: {
      required: 'Constitution is required for this funding type'
    },
    conditionalRequired: (formData) => 
      ['Education Funding', 'Hospital Funding'].includes(formData.selectedLeadDataType)
  },

  selectedBoardAffiliation: {
    rules: { required: true },
    errorMessages: {
      required: 'Board Affiliation is required for Education Funding'
    },
    conditionalRequired: (formData) => formData.selectedLeadDataType === 'Education Funding'
  },

  universityAffiliation: {
    rules: { required: true, minLength: 3, maxLength: 200 },
    errorMessages: {
      required: 'University Affiliation is required for Education Funding',
      minLength: 'University Affiliation must be at least 3 characters long',
      maxLength: 'University Affiliation cannot exceed 200 characters'
    },
    conditionalRequired: (formData) => formData.selectedLeadDataType === 'Education Funding'
  },

  // People form validation
  'people.connectWith': {
    rules: { required: true },
    errorMessages: {
      required: 'Connect With is required'
    }
  },

  'people.name': {
    rules: { required: true, minLength: 2, maxLength: 100 },
    errorMessages: {
      required: 'Name is required',
      minLength: 'Name must be at least 2 characters long',
      maxLength: 'Name cannot exceed 100 characters'
    }
  },

  'people.mobile': {
    rules: { required: true, pattern: '^[0-9]{10}$' },
    errorMessages: {
      required: 'Mobile number is required',
      pattern: 'Mobile number must be exactly 10 digits'
    }
  },

  'people.email': {
    rules: { email: true, maxLength: 255 },
    errorMessages: {
      email: 'Please enter a valid email address',
      maxLength: 'Email cannot exceed 255 characters'
    }
  }
};

/**
 * Business validation rules that depend on multiple fields
 */
export const BUSINESS_VALIDATION_RULES = {
  // At least one person must be added
  minimumPeopleRequired: {
    validate: (formData: any) => formData.people && formData.people.length > 0,
    errorMessage: 'At least one person contact is required'
  },

  // Associate source validation when not Associate category
  associateSourceValidation: {
    validate: (formData: any) => {
      if (formData.selectedSource === 'Associate' && formData.selectedLeadCategory !== 'Associate') {
        return formData.associateName && 
               formData.associateCompanyName && 
               formData.associateLocation && 
               formData.associateSubLocation;
      }
      return true;
    },
    errorMessage: 'All Associate fields are required when Source is "Associate"'
  }
};

/**
 * Field dependency rules - fields that should be validated together
 */
export const FIELD_DEPENDENCIES = {
  leadDataTypeFields: {
    triggerField: 'selectedLeadDataType',
    dependentFields: ['selectedConstitution', 'selectedBoardAffiliation', 'universityAffiliation'],
    condition: (triggerValue: string) => ['Education Funding', 'Hospital Funding'].includes(triggerValue)
  },

  associateCategoryFields: {
    triggerField: 'selectedLeadCategory',
    dependentFields: ['associateNameCategory'],
    condition: (triggerValue: string) => triggerValue === 'Associate'
  }
};

/**
 * Validation error severity levels
 */
export enum ValidationSeverity {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  severity: ValidationSeverity;
  value?: any;
}

/**
 * Helper function to get validation rules for a specific field
 */
export function getFieldValidation(fieldName: string): FieldValidation | null {
  return SALES_FORM_VALIDATION[fieldName] || null;
}

/**
 * Helper function to check if a field is conditionally required
 */
export function isFieldRequired(fieldName: string, formData: any): boolean {
  const validation = getFieldValidation(fieldName);
  if (!validation) return false;

  if (validation.conditionalRequired) {
    return validation.conditionalRequired(formData);
  }

  return validation.rules.required || false;
}

/**
 * Helper function to validate a single field
 */
export function validateField(fieldName: string, value: any, formData: any): ValidationError[] {
  const validation = getFieldValidation(fieldName);
  if (!validation) return [];

  const errors: ValidationError[] = [];
  const rules = validation.rules;
  const messages = validation.errorMessages;

  // Check if field is required
  const isRequired = isFieldRequired(fieldName, formData);
  if (isRequired && (!value || (typeof value === 'string' && value.trim() === ''))) {
    errors.push({
      field: fieldName,
      message: messages.required || `${fieldName} is required`,
      severity: ValidationSeverity.ERROR,
      value
    });
    return errors; // If required and empty, no need to check other rules
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return errors;
  }

  // String validations
  if (typeof value === 'string') {
    if (rules.minLength && value.trim().length < rules.minLength) {
      errors.push({
        field: fieldName,
        message: messages.minLength || `Minimum length is ${rules.minLength} characters`,
        severity: ValidationSeverity.ERROR,
        value
      });
    }

    if (rules.maxLength && value.trim().length > rules.maxLength) {
      errors.push({
        field: fieldName,
        message: messages.maxLength || `Maximum length is ${rules.maxLength} characters`,
        severity: ValidationSeverity.ERROR,
        value
      });
    }

    if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
      errors.push({
        field: fieldName,
        message: messages.pattern || 'Invalid format',
        severity: ValidationSeverity.ERROR,
        value
      });
    }

    if (rules.email && !isValidEmail(value)) {
      errors.push({
        field: fieldName,
        message: messages.email || 'Please enter a valid email address',
        severity: ValidationSeverity.ERROR,
        value
      });
    }
  }

  // Number validations
  if (typeof value === 'number') {
    if (rules.min !== undefined && value < rules.min) {
      errors.push({
        field: fieldName,
        message: messages.min || `Minimum value is ${rules.min}`,
        severity: ValidationSeverity.ERROR,
        value
      });
    }

    if (rules.max !== undefined && value > rules.max) {
      errors.push({
        field: fieldName,
        message: messages.max || `Maximum value is ${rules.max}`,
        severity: ValidationSeverity.ERROR,
        value
      });
    }
  }

  // Custom validation
  if (rules.custom && !rules.custom(value, formData)) {
    errors.push({
      field: fieldName,
      message: messages.custom || 'Invalid value',
      severity: ValidationSeverity.ERROR,
      value
    });
  }

  return errors;
}

/**
 * Helper function to validate email format
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
