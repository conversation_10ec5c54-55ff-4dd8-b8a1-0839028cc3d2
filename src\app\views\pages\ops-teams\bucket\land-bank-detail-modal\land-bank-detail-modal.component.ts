import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LandBankDetailRow } from '../ops-team.component';

@Component({
  selector: 'app-land-bank-detail-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './land-bank-detail-modal.component.html',
  styleUrl: './land-bank-detail-modal.component.scss'
})
export class LandBankDetailModalComponent implements OnInit {
  @Input() landId?: number;

  // Form data
  formData: LandBankDetailRow = {
    id: 0,
    companyName: '',
    location: '',
    landType: '',
    ownershipType: '',
    plotArea: 0,
    plotAreaUnit: '',
    acquisitionYear: '',
    purchaseValue: 0,
    marketValue: 0,
    hasLoan: '',
    approvalStatus: '',
    expectedLaunchDate: '',
    remarks: ''
  };

  // Land type options
  landTypeOptions = [
    { value: 'Agricultural', label: 'Agricultural' },
    { value: 'Non-Agricultural', label: 'Non-Agricultural' },
    { value: 'Industrial', label: 'Industrial' },
    { value: 'Commercial', label: 'Commercial' },
    { value: 'Residential', label: 'Residential' }
  ];

  // Ownership type options
  ownershipTypeOptions = [
    { value: 'Owned', label: 'Owned' },
    { value: 'JDA', label: 'JDA (Joint Development Agreement)' },
    { value: 'JV', label: 'JV (Joint Venture)' },
    { value: 'Leased', label: 'Leased' }
  ];

  // Plot area unit options
  plotAreaUnitOptions = [
    { value: 'Sq Ft', label: 'Sq Ft' },
    { value: 'Sq Mt', label: 'Sq Mt' },
    { value: 'Acres', label: 'Acres' },
    { value: 'Hectares', label: 'Hectares' },
    { value: 'Guntha', label: 'Guntha' }
  ];

  // Has loan options
  hasLoanOptions = [
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
  ];

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      companyName: '',
      location: '',
      landType: '',
      ownershipType: '',
      plotArea: 0,
      plotAreaUnit: '',
      acquisitionYear: '',
      purchaseValue: 0,
      marketValue: 0,
      hasLoan: '',
      approvalStatus: '',
      expectedLaunchDate: '',
      remarks: ''
    };

    // If editing an existing record, populate the form
    if (this.landId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll use mock data based on the ID
      if (this.landId === 1) {
        this.formData = {
          id: 1,
          companyName: 'ABC Developers Pvt Ltd',
          location: 'Panvel, Navi Mumbai',
          landType: 'Non-Agricultural',
          ownershipType: 'Owned',
          plotArea: 25,
          plotAreaUnit: 'Acres',
          acquisitionYear: '2018',
          purchaseValue: *********,
          marketValue: *********,
          hasLoan: 'No',
          approvalStatus: 'All approvals in process',
          expectedLaunchDate: '2023-12-01',
          remarks: 'Prime location near upcoming airport'
        };
      } else if (this.landId === 2) {
        this.formData = {
          id: 2,
          companyName: 'ABC Commercial Properties Pvt Ltd',
          location: 'Bandra Kurla Complex, Mumbai',
          landType: 'Commercial',
          ownershipType: 'JDA',
          plotArea: 5,
          plotAreaUnit: 'Acres',
          acquisitionYear: '2020',
          purchaseValue: *********,
          marketValue: *********,
          hasLoan: 'Yes',
          approvalStatus: 'Approvals pending',
          expectedLaunchDate: '2024-03-15',
          remarks: 'Premium commercial location'
        };
      }
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Close the modal and pass the data back
    this.activeModal.close(this.formData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
