import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TemplateDownloadService {

  private readonly templatesPath = 'assets/templates/hr-admin/';

  constructor(private http: HttpClient) {}

  /**
   * Download employee bulk upload template
   */
  downloadEmployeeBulkUploadTemplate(): Promise<boolean> {
    return this.downloadTemplateWithPromise('New Employee.xlsx', 'New_Employee_Template.xlsx');
  }

  /**
   * Download leave application template
   */
  downloadLeaveApplicationTemplate(): void {
    this.downloadTemplate('leave-application-template.xlsx', 'Leave_Application_Template.xlsx');
  }

  /**
   * Download attendance import template
   */
  downloadAttendanceImportTemplate(): void {
    this.downloadTemplate('attendance-import-template.xlsx', 'Attendance_Import_Template.xlsx');
  }

  /**
   * Download salary import template
   */
  downloadSalaryImportTemplate(): void {
    this.downloadTemplate('salary-import-template.xlsx', 'Salary_Import_Template.xlsx');
  }

  /**
   * Download new year activity template
   */
  downloadNewYearActivityTemplate(): Promise<boolean> {
    return this.downloadTemplateWithPromise('new_year_activities.xlsx', 'New_Year_Activities_Template.xlsx');
  }

  /**
   * Generic template download method
   */
  private downloadTemplate(templateFileName: string, downloadFileName: string): void {
    const templateUrl = `${this.templatesPath}${templateFileName}`;

    console.log(`📥 Downloading template: ${templateFileName}`);
    console.log(`📁 Template URL: ${templateUrl}`);

    // First try to download via HTTP to check if file exists and is valid
    this.http.get(templateUrl, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel'
      }
    }).subscribe({
      next: (blob) => {
        console.log(`✅ Template file found and downloaded: ${templateFileName}`);
        console.log(`📊 File size: ${blob.size} bytes`);
        console.log(`📋 File type: ${blob.type}`);

        // Create blob URL and download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = downloadFileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up blob URL
        window.URL.revokeObjectURL(url);

        console.log(`✅ Template download completed: ${downloadFileName}`);
      },
      error: (error) => {
        console.error(`❌ Failed to download template: ${templateFileName}`, error);
        console.error(`🔍 Error details:`, {
          status: error.status,
          statusText: error.statusText,
          url: templateUrl
        });

        // Show user-friendly error message
        alert(`Failed to download template: ${templateFileName}\n\nError: ${error.status} - ${error.statusText}\n\nPlease check if the template file exists and is accessible.`);
      }
    });
  }

  /**
   * Download template with Promise return for better async handling
   */
  private downloadTemplateWithPromise(templateFileName: string, downloadFileName: string): Promise<boolean> {
    const templateUrl = `${this.templatesPath}${templateFileName}`;

    console.log(`📥 Downloading template: ${templateFileName}`);
    console.log(`📁 Template URL: ${templateUrl}`);

    return new Promise((resolve, reject) => {
      // First try direct download method (more reliable for static files)
      try {
        this.downloadTemplateDirect(templateFileName, downloadFileName);
        console.log(`✅ Direct download initiated: ${downloadFileName}`);

        // Give a small delay to ensure download starts, then resolve
        setTimeout(() => {
          resolve(true);
        }, 500);

      } catch (directError) {
        console.error(`❌ Direct download failed: ${templateFileName}`, directError);

        // Fallback to HTTP download
        console.log('🔄 Attempting HTTP download fallback...');
        this.http.get(templateUrl, {
          responseType: 'blob',
          headers: {
            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/octet-stream'
          }
        }).subscribe({
          next: (blob) => {
            console.log(`✅ HTTP template file downloaded: ${templateFileName}`);
            console.log(`📊 File size: ${blob.size} bytes`);
            console.log(`📋 File type: ${blob.type}`);

            // Ensure proper MIME type for Excel files
            const properBlob = new Blob([blob], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            // Create blob URL and download
            const url = window.URL.createObjectURL(properBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = downloadFileName;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up blob URL
            window.URL.revokeObjectURL(url);

            console.log(`✅ HTTP template download completed: ${downloadFileName}`);
            resolve(true);
          },
          error: (httpError) => {
            console.error(`❌ HTTP download also failed: ${templateFileName}`, httpError);
            reject(httpError);
          }
        });
      }
    });
  }

  /**
   * Direct download method (fallback)
   */
  private downloadTemplateDirect(templateFileName: string, downloadFileName: string): void {
    const templateUrl = `${this.templatesPath}${templateFileName}`;

    console.log(`📥 Direct download attempt: ${templateFileName}`);
    console.log(`📁 Direct URL: ${templateUrl}`);

    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = templateUrl;
    link.download = downloadFileName;
    link.style.display = 'none';

    // Set additional attributes for better Excel file handling
    link.setAttribute('type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    link.setAttribute('target', '_blank');

    // Add to DOM, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log(`✅ Direct download initiated: ${downloadFileName}`);
  }

  /**
   * Download template via HTTP (for dynamic templates)
   */
  downloadTemplateViaHttp(templateFileName: string, downloadFileName: string): Observable<Blob> {
    const templateUrl = `${this.templatesPath}${templateFileName}`;
    
    return this.http.get(templateUrl, { 
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    });
  }

  /**
   * Download template with HTTP and save
   */
  downloadAndSaveTemplate(templateFileName: string, downloadFileName: string): void {
    console.log(`📥 Downloading template via HTTP: ${templateFileName}`);
    
    this.downloadTemplateViaHttp(templateFileName, downloadFileName).subscribe({
      next: (blob) => {
        console.log(`✅ Template downloaded successfully: ${downloadFileName}`);
        
        // Create blob URL and download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = downloadFileName;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up blob URL
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error(`❌ Failed to download template: ${templateFileName}`, error);
        
        // Fallback to direct download
        console.log('🔄 Falling back to direct download...');
        this.downloadTemplate(templateFileName, downloadFileName);
      }
    });
  }

  /**
   * Get list of available templates
   */
  getAvailableTemplates(): Array<{name: string, fileName: string, description: string}> {
    return [
      {
        name: 'Employee Bulk Upload',
        fileName: 'New Employee.xlsx',
        description: 'Template for uploading multiple employees at once'
      },
      {
        name: 'New Year Activities',
        fileName: 'new_year_activities.xlsx',
        description: 'Template for uploading new year activities'
      },
      {
        name: 'Leave Application',
        fileName: 'leave-application-template.xlsx',
        description: 'Template for bulk leave applications'
      },
      {
        name: 'Attendance Import',
        fileName: 'attendance-import-template.xlsx',
        description: 'Template for importing attendance data'
      },
      {
        name: 'Salary Import',
        fileName: 'salary-import-template.xlsx',
        description: 'Template for importing salary information'
      }
    ];
  }

  /**
   * Check if template exists
   */
  checkTemplateExists(templateFileName: string): Observable<boolean> {
    const templateUrl = `${this.templatesPath}${templateFileName}`;
    
    return new Observable(observer => {
      this.http.head(templateUrl).subscribe({
        next: () => {
          observer.next(true);
          observer.complete();
        },
        error: () => {
          observer.next(false);
          observer.complete();
        }
      });
    });
  }


}
