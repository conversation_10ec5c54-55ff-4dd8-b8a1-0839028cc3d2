import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { BreadcrumbComponent, BreadcrumbItem } from '../shared/breadcrumb/breadcrumb.component';
import { AttendanceService, AttendanceMarkRequest } from '../../../../../core/services/attendance.service';
import { EmployeeService, Employee as ApiEmployee } from '../../../../../core/services/employee.service';

// Employee interface for attendance component
export interface Employee {
  id: string; // UUID from API
  code: string; // employee_code from API
  name: string; // computed from first_name + last_name
}



@Component({
  selector: 'app-mark-attendance',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FeatherIconDirective,
    BreadcrumbComponent
  ],
  templateUrl: './mark-attendance.component.html',
  styleUrl: './mark-attendance.component.scss'
})
export class MarkAttendanceComponent implements OnInit {
  // Breadcrumb items
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', route: '/lms/dashboard' },
    { label: 'Mark Attendance' }
  ];

  // Employee data
  employees: Employee[] = [];
  isLoadingEmployees = false;

  // Form controls
  employeeCode = new FormControl('', { nonNullable: true });
  attendanceDate = new FormControl(this.getCurrentDate(), { nonNullable: true });
  checkInTime = new FormControl('', { nonNullable: true });
  checkOutTime = new FormControl('', { nonNullable: true });

  // Success message flags
  showSuccessMessage = false;
  showBulkUploadSuccessMessage = false;

  // File upload properties
  selectedFile: File | null = null;

  // Loading states
  isDownloadingTemplate = false;
  isUploadingFile = false;
  isSubmittingAttendance = false;

  constructor(
    private attendanceService: AttendanceService,
    private employeeService: EmployeeService
  ) {}

  ngOnInit(): void {
    this.loadEmployees();
  }

  // Load employees from API
  loadEmployees(): void {
    console.log('🗓️ MarkAttendanceComponent: Loading employees from API...');
    this.isLoadingEmployees = true;

    this.employeeService.getAllEmployees().subscribe({
      next: (apiEmployees) => {
        console.log('✅ MarkAttendanceComponent: Employees loaded:', apiEmployees);

        // Transform API employees to component format
        this.employees = apiEmployees.map(emp => this.transformApiEmployee(emp));

        console.log('🔄 MarkAttendanceComponent: Transformed employees:', this.employees);
        this.isLoadingEmployees = false;
      },
      error: (error) => {
        console.error('❌ MarkAttendanceComponent: Failed to load employees:', error);
        this.isLoadingEmployees = false;

        // Fallback to sample data if API fails
        this.employees = [
          { id: '1', code: 'EMP001', name: 'John Doe' },
          { id: '2', code: 'EMP002', name: 'Jane Smith' },
          { id: '3', code: 'EMP003', name: 'Robert Johnson' },
          { id: '4', code: 'EMP004', name: 'Emily Davis' },
          { id: '5', code: 'EMP005', name: 'Michael Wilson' }
        ];
        console.log('🔄 MarkAttendanceComponent: Using fallback employee data');
      }
    });
  }

  // Transform API employee to component Employee interface
  private transformApiEmployee(apiEmp: any): Employee {
    console.log('🔄 MarkAttendanceComponent: Transforming employee:', apiEmp);

    const firstName = apiEmp.employee_first_name || apiEmp.first_name || '';
    const lastName = apiEmp.employee_last_name || apiEmp.last_name || '';
    const fullName = `${firstName} ${lastName}`.trim();

    const transformed = {
      id: apiEmp.id || apiEmp.uuid || '',
      code: apiEmp.employee_code || apiEmp.code || '',
      name: fullName || `Employee ${apiEmp.id || apiEmp.employee_code || 'Unknown'}`
    };

    console.log('✅ MarkAttendanceComponent: Transformed employee:', transformed);
    return transformed;
  }

  // Get current date in YYYY-MM-DD format
  getCurrentDate(): string {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Check if form is valid
  isFormValid(): boolean {
    const hasAllFields = this.employeeCode.value !== '' &&
                        this.attendanceDate.value !== '' &&
                        this.checkInTime.value !== '' &&
                        this.checkOutTime.value !== '';

    if (!hasAllFields) {
      return false;
    }

    // Validate that check-out time is after check-in time
    const checkIn = this.checkInTime.value;
    const checkOut = this.checkOutTime.value;

    if (checkIn && checkOut) {
      const checkInTime = new Date(`2000-01-01T${checkIn}:00`);
      const checkOutTime = new Date(`2000-01-01T${checkOut}:00`);

      if (checkOutTime <= checkInTime) {
        console.warn('⚠️ MarkAttendanceComponent: Check-out time must be after check-in time');
        return false;
      }
    }

    return true;
  }

  // Check if time values are valid (check-out after check-in)
  isTimeValid(): boolean {
    const checkIn = this.checkInTime.value;
    const checkOut = this.checkOutTime.value;

    if (!checkIn || !checkOut) {
      return true; // Don't show error if times are not set
    }

    const checkInTime = new Date(`2000-01-01T${checkIn}:00`);
    const checkOutTime = new Date(`2000-01-01T${checkOut}:00`);

    return checkOutTime > checkInTime;
  }

  // Add new attendance record using API
  addAttendance() {
    if (!this.isFormValid()) {
      console.warn('⚠️ MarkAttendanceComponent: Form is not valid');
      return;
    }

    // Find employee
    const employee = this.employees.find(e => e.code === this.employeeCode.value);
    if (!employee) {
      console.warn('⚠️ MarkAttendanceComponent: Employee not found for code:', this.employeeCode.value);
      console.warn('⚠️ MarkAttendanceComponent: Available employees:', this.employees);
      return;
    }

    console.log('✅ MarkAttendanceComponent: Found employee:', employee);

    // Prepare attendance data for API
    const attendanceData: AttendanceMarkRequest = {
      employee_code: employee.code, // API expects employee_code, not employee_id
      attendance_date: this.attendanceDate.value,
      check_in_time: this.checkInTime.value,
      check_out_time: this.checkOutTime.value
    };

    console.log('🗓️ MarkAttendanceComponent: Marking attendance with data:', attendanceData);

    // Set loading state
    this.isSubmittingAttendance = true;

    // Use the AttendanceService to mark attendance
    this.attendanceService.markAttendance(attendanceData).subscribe({
      next: (response) => {
        console.log('✅ MarkAttendanceComponent: Attendance marked successfully:', response);

        // Reset form
        this.employeeCode.setValue('');
        this.checkInTime.setValue('');
        this.checkOutTime.setValue('');

        // Show success message
        this.showSuccessMessage = true;

        // Hide success message after 3 seconds
        setTimeout(() => {
          this.showSuccessMessage = false;
        }, 3000);

        // Reset loading state
        this.isSubmittingAttendance = false;
      },
      error: (error) => {
        console.error('❌ MarkAttendanceComponent: Failed to mark attendance:', error);

        // Reset loading state
        this.isSubmittingAttendance = false;

        // Enhanced error handling
        let errorMessage = 'Failed to mark attendance. Please try again.';

        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        // Show error message
        alert(`Failed to mark attendance: ${errorMessage}`);
      }
    });
  }

  // Handle file selection for bulk upload
  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Validate file type
      const allowedTypes = [
        'text/csv',
        'application/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      const allowedExtensions = ['.csv', '.xls', '.xlsx'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        alert('Please select a valid CSV or Excel file (.csv, .xls, .xlsx)');
        input.value = ''; // Clear the input
        this.selectedFile = null;
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        alert('File size must be less than 10MB');
        input.value = ''; // Clear the input
        this.selectedFile = null;
        return;
      }

      this.selectedFile = file;
      console.log('MarkAttendanceComponent: File selected:', {
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.type
      });
    } else {
      this.selectedFile = null;
    }
  }

  // Upload bulk attendance data using API
  uploadBulkAttendance() {
    if (!this.selectedFile) {
      console.warn('⚠️ MarkAttendanceComponent: No file selected for upload');
      return;
    }

    console.log('🗓️ MarkAttendanceComponent: Uploading bulk attendance file:', {
      fileName: this.selectedFile.name,
      fileSize: this.selectedFile.size,
      fileType: this.selectedFile.type
    });

    // Set loading state
    this.isUploadingFile = true;

    // Use the AttendanceService to upload file to API
    this.attendanceService.uploadBulkAttendance(this.selectedFile).subscribe({
      next: (response) => {
        console.log('✅ MarkAttendanceComponent: Bulk upload successful:', response);

        // Reset file input
        this.selectedFile = null;
        const fileInput = document.getElementById('bulkAttendanceFile') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }

        // Show success message
        this.showBulkUploadSuccessMessage = true;

        // Hide success message after 3 seconds
        setTimeout(() => {
          this.showBulkUploadSuccessMessage = false;
        }, 3000);

        // Reset loading state
        this.isUploadingFile = false;
      },
      error: (error) => {
        console.error('MarkAttendanceComponent: Bulk upload failed:', error);

        // Reset loading state
        this.isUploadingFile = false;

        // Enhanced error handling
        let errorMessage = 'Failed to upload attendance data. Please try again.';

        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        // Show error message
        alert(`Upload failed: ${errorMessage}`);

        console.error('Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });
      }
    });
  }

  // Download bulk template for attendance upload from API (Swagger-style)
  downloadBulkTemplate() {
    console.log('MarkAttendanceComponent: Downloading attendance template (Swagger-style)...');

    // Set loading state
    this.isDownloadingTemplate = true;

    try {
      // Use the new Swagger-style download method
      this.attendanceService.downloadTemplateSwaggerStyle('attendance_template.csv');

      console.log('MarkAttendanceComponent: Template download initiated');

      // Show success message briefly
      setTimeout(() => {
        console.log('MarkAttendanceComponent: Download should have started...');
      }, 2000);

    } catch (error) {
      console.error('MarkAttendanceComponent: Download failed:', error);
      alert('Failed to download template. Please try again.');
    } finally {
      // Reset loading state after a delay
      setTimeout(() => {
        this.isDownloadingTemplate = false;
      }, 2000);
    }
  }
}
