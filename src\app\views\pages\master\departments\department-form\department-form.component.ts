import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { 
  DepartmentService, 
  Department, 
  DepartmentCreate, 
  DepartmentUpdate 
} from '../../../../../core/services/department.service';
import { PopupDesignService } from '../../../../../core/services/popup-design.service';

@Component({
  selector: 'app-department-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FeatherIconDirective
  ],
  templateUrl: './department-form.component.html',
  styleUrls: ['./department-form.component.scss']
})
export class DepartmentFormComponent implements OnInit {
  @Input() isEditMode = false;
  @Input() department: Department | null = null;
  @Input() parentDepartments: Department[] = [];

  departmentForm!: FormGroup;
  saving = false;
  error: string | null = null;

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private popupService: PopupDesignService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  /**
   * Initialize the form with validation
   */
  private initializeForm(): void {
    this.departmentForm = this.fb.group({
      name: [
        this.department?.name || '', 
        [
          Validators.required, 
          Validators.minLength(2),
          Validators.maxLength(100),
          Validators.pattern(/^[a-zA-Z0-9\s\-&().,]+$/)
        ]
      ],
      description: [
        this.department?.description || '', 
        [
          Validators.maxLength(500)
        ]
      ],
      parent_id: [
        this.department?.parent_id || '', 
        []
      ],
      is_active: [
        this.department?.is_active ?? true, 
        [Validators.required]
      ]
    });

    // Add custom validator to prevent circular hierarchy
    if (this.isEditMode && this.department) {
      this.departmentForm.get('parent_id')?.addValidators(
        this.circularHierarchyValidator.bind(this)
      );
    }
  }

  /**
   * Custom validator to prevent circular hierarchy
   */
  private circularHierarchyValidator(control: any) {
    if (!this.department || !control.value) {
      return null;
    }

    // Prevent setting self as parent
    if (control.value === this.department.id) {
      return { circularHierarchy: { message: 'Department cannot be its own parent' } };
    }

    // Prevent setting child departments as parent
    const selectedParent = this.parentDepartments.find(d => d.id === control.value);
    if (selectedParent && this.isChildDepartment(selectedParent, this.department.id)) {
      return { circularHierarchy: { message: 'Cannot set a child department as parent' } };
    }

    return null;
  }

  /**
   * Check if a department is a child of another department
   */
  private isChildDepartment(potentialChild: Department, parentId: string): boolean {
    // This is a simplified check - in a real implementation, you'd need to
    // traverse the full hierarchy to check for circular references
    return potentialChild.parent_id === parentId;
  }

  /**
   * Get form control for template access
   */
  getFormControl(controlName: string) {
    return this.departmentForm.get(controlName);
  }

  /**
   * Check if form control has error
   */
  hasError(controlName: string, errorType?: string): boolean {
    const control = this.getFormControl(controlName);
    if (!control) return false;

    if (errorType) {
      return control.hasError(errorType) && (control.dirty || control.touched);
    }
    
    return control.invalid && (control.dirty || control.touched);
  }

  /**
   * Get error message for form control
   */
  getErrorMessage(controlName: string): string {
    const control = this.getFormControl(controlName);
    if (!control || !control.errors) return '';

    const errors = control.errors;

    if (errors['required']) {
      return `${this.getFieldLabel(controlName)} is required.`;
    }
    
    if (errors['minlength']) {
      return `${this.getFieldLabel(controlName)} must be at least ${errors['minlength'].requiredLength} characters.`;
    }
    
    if (errors['maxlength']) {
      return `${this.getFieldLabel(controlName)} cannot exceed ${errors['maxlength'].requiredLength} characters.`;
    }
    
    if (errors['pattern']) {
      return `${this.getFieldLabel(controlName)} contains invalid characters.`;
    }
    
    if (errors['circularHierarchy']) {
      return errors['circularHierarchy'].message;
    }

    return 'Invalid input.';
  }

  /**
   * Get field label for error messages
   */
  private getFieldLabel(controlName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Department name',
      description: 'Description',
      parent_id: 'Parent department',
      is_active: 'Status'
    };
    return labels[controlName] || controlName;
  }

  /**
   * Get filtered parent departments (exclude current department and its children)
   */
  getFilteredParentDepartments(): Department[] {
    if (!this.isEditMode || !this.department) {
      return this.parentDepartments;
    }

    return this.parentDepartments.filter(dept => {
      // Exclude current department
      if (dept.id === this.department!.id) {
        return false;
      }
      
      // Exclude child departments (simplified check)
      if (dept.parent_id === this.department!.id) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Get department level indicator for dropdown
   */
  getDepartmentLevelIndicator(department: Department): string {
    return '—'.repeat(department.level) + ' ' + department.name;
  }

  /**
   * Save department
   */
  save(): void {
    if (this.departmentForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.saving = true;
    this.error = null;

    const formValue = this.departmentForm.value;
    
    // Clean up form data
    const departmentData = {
      name: formValue.name.trim(),
      description: formValue.description?.trim() || undefined,
      parent_id: formValue.parent_id || undefined,
      is_active: formValue.is_active
    };

    const operation = this.isEditMode 
      ? this.departmentService.updateDepartment(this.department!.id, departmentData as DepartmentUpdate)
      : this.departmentService.createDepartment(departmentData as DepartmentCreate);

    operation.subscribe({
      next: (response) => {
        if (response.success) {
          this.activeModal.close('saved');
        } else {
          this.error = response.error || 'Failed to save department.';
          this.saving = false;
        }
      },
      error: (error) => {
        this.error = error.message;
        this.saving = false;
        
        this.popupService.showError({
          title: 'Save Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Mark all form controls as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.departmentForm.controls).forEach(key => {
      const control = this.departmentForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Reset form
   */
  reset(): void {
    this.departmentForm.reset();
    this.initializeForm();
    this.error = null;
  }

  /**
   * Cancel and close modal
   */
  cancel(): void {
    if (this.departmentForm.dirty) {
      this.popupService.showConfirmation({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        confirmText: 'Yes, Cancel',
        cancelText: 'Continue Editing'
      }).then((result) => {
        if (result.isConfirmed) {
          this.activeModal.dismiss('cancelled');
        }
      });
    } else {
      this.activeModal.dismiss('cancelled');
    }
  }

  /**
   * Preview department hierarchy level
   */
  getPreviewLevel(): number {
    const parentId = this.departmentForm.get('parent_id')?.value;
    if (!parentId) {
      return 0;
    }
    
    const parent = this.parentDepartments.find(d => d.id === parentId);
    return parent ? parent.level + 1 : 0;
  }

  /**
   * Get modal title
   */
  getModalTitle(): string {
    return this.isEditMode ? 'Edit Department' : 'Create New Department';
  }

  /**
   * Get save button text
   */
  getSaveButtonText(): string {
    if (this.saving) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update Department' : 'Create Department';
  }
}
