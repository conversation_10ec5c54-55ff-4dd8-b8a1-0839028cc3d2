<!-- Analytics Dashboard Header -->
<div class="analytics-header-section">
  <div class="container-fluid">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <div class="page-header">
          <h4 class="page-title mb-1">
            <i data-feather="bar-chart-2" class="icon-md me-2" appFeatherIcon></i>
            Role & Permission Analytics
          </h4>
          <p class="page-subtitle text-muted mb-0">
            Comprehensive insights into your role and permission management system
          </p>
        </div>
      </div>
      <div class="col-md-4 text-end">
        <div class="header-actions">
          <button class="btn btn-outline-light me-2" (click)="refreshAnalytics()" [disabled]="loading">
            <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
            Refresh Data
          </button>
          <button class="btn btn-light" routerLink="../roles">
            <i data-feather="arrow-left" class="icon-sm me-1" appFeatherIcon></i>
            Back to Roles
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="loading-section">
  <div class="container-fluid">
    <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
      <div class="text-center">
        <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
          <span class="visually-hidden">Loading analytics...</span>
        </div>
        <p class="text-muted">Analyzing role and permission data...</p>
      </div>
    </div>
  </div>
</div>

<!-- Analytics Content -->
<div *ngIf="!loading" class="analytics-content-section">
  <div class="container-fluid">
    
    <!-- Key Metrics Row -->
    <div class="metrics-section mb-4">
      <div class="row g-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
          <div class="metric-card">
            <div class="metric-icon bg-primary">
              <i data-feather="shield" appFeatherIcon></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-number">{{ analytics.totalRoles }}</h3>
              <p class="metric-label">Total Roles</p>
              <small class="metric-detail text-success">
                {{ analytics.activeRoles }} active, {{ analytics.deletedRoles }} deleted
              </small>
            </div>
          </div>
        </div>
        
        <div class="col-xl-3 col-lg-6 col-md-6">
          <div class="metric-card">
            <div class="metric-icon bg-info">
              <i data-feather="users" appFeatherIcon></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-number">{{ analytics.totalUsers }}</h3>
              <p class="metric-label">Total Users</p>
              <small class="metric-detail text-info">
                Across all active roles
              </small>
            </div>
          </div>
        </div>
        
        <div class="col-xl-3 col-lg-6 col-md-6">
          <div class="metric-card">
            <div class="metric-icon bg-warning">
              <i data-feather="key" appFeatherIcon></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-number">{{ analytics.totalPermissions }}</h3>
              <p class="metric-label">Total Permissions</p>
              <small class="metric-detail text-warning">
                Avg {{ analytics.averagePermissionsPerRole }} per role
              </small>
            </div>
          </div>
        </div>
        
        <div class="col-xl-3 col-lg-6 col-md-6">
          <div class="metric-card">
            <div class="metric-icon" [class]="'bg-' + getHealthScoreColor()">
              <i data-feather="activity" appFeatherIcon></i>
            </div>
            <div class="metric-content">
              <h3 class="metric-number">{{ getHealthScore() }}%</h3>
              <p class="metric-label">Health Score</p>
              <small class="metric-detail" [class]="'text-' + getHealthScoreColor()">
                {{ getHealthScoreText() }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Analysis Row -->
    <div class="row g-4 mb-4">
      
      <!-- Permission Distribution Chart -->
      <div class="col-lg-6">
        <div class="analytics-card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i data-feather="pie-chart" class="icon-sm me-2" appFeatherIcon></i>
              Permission Distribution
            </h5>
          </div>
          <div class="card-body">
            <div class="permission-distribution">
              <div class="distribution-chart">
                <div class="chart-container">
                  <div class="donut-chart">
                    <div class="donut-center">
                      <span class="donut-number">{{ analytics.totalPermissions }}</span>
                      <span class="donut-label">Permissions</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="distribution-legend">
                <div class="legend-item" *ngFor="let item of permissionDistribution">
                  <div class="legend-color" [style.background-color]="item.color"></div>
                  <div class="legend-content">
                    <span class="legend-label">{{ item.category }}</span>
                    <span class="legend-value">{{ item.count }} ({{ item.percentage }}%)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Health Analysis -->
      <div class="col-lg-6">
        <div class="analytics-card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i data-feather="heart" class="icon-sm me-2" appFeatherIcon></i>
              System Health Analysis
            </h5>
          </div>
          <div class="card-body">
            <div class="health-analysis">
              
              <!-- Health Score Circle -->
              <div class="health-score-circle mb-4">
                <div class="circle-progress" [attr.data-score]="getHealthScore()">
                  <div class="circle-content">
                    <span class="score-number">{{ getHealthScore() }}%</span>
                    <span class="score-label">{{ getHealthScoreText() }}</span>
                  </div>
                </div>
              </div>

              <!-- Health Indicators -->
              <div class="health-indicators">
                <div class="indicator-item">
                  <div class="indicator-icon" [class]="analytics.rolesWithoutUsers === 0 ? 'text-success' : 'text-warning'">
                    <i data-feather="users" appFeatherIcon></i>
                  </div>
                  <div class="indicator-content">
                    <span class="indicator-label">Roles without Users</span>
                    <span class="indicator-value">{{ analytics.rolesWithoutUsers }}</span>
                  </div>
                </div>
                
                <div class="indicator-item">
                  <div class="indicator-icon" [class]="analytics.rolesWithoutPermissions === 0 ? 'text-success' : 'text-warning'">
                    <i data-feather="key" appFeatherIcon></i>
                  </div>
                  <div class="indicator-content">
                    <span class="indicator-label">Roles without Permissions</span>
                    <span class="indicator-value">{{ analytics.rolesWithoutPermissions }}</span>
                  </div>
                </div>
                
                <div class="indicator-item">
                  <div class="indicator-icon" [class]="analytics.averagePermissionsPerRole >= 3 ? 'text-success' : 'text-info'">
                    <i data-feather="bar-chart" appFeatherIcon></i>
                  </div>
                  <div class="indicator-content">
                    <span class="indicator-label">Avg Permissions per Role</span>
                    <span class="indicator-value">{{ analytics.averagePermissionsPerRole }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Tables Row -->
    <div class="row g-4">
      
      <!-- Top Roles by Users -->
      <div class="col-lg-6">
        <div class="analytics-card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i data-feather="trending-up" class="icon-sm me-2" appFeatherIcon></i>
              Top Roles by User Count
            </h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Role Name</th>
                    <th class="text-center">Users</th>
                    <th class="text-center">Permissions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let role of topRolesByUsers; let i = index">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="rank-badge me-2">{{ i + 1 }}</div>
                        <span class="role-name">{{ role.roleName }}</span>
                      </div>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-primary-subtle text-primary">{{ role.userCount }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-warning-subtle text-warning">{{ role.permissionCount }}</span>
                    </td>
                  </tr>
                  <tr *ngIf="topRolesByUsers.length === 0">
                    <td colspan="3" class="text-center text-muted py-4">
                      No role data available
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Recently Created Roles -->
      <div class="col-lg-6">
        <div class="analytics-card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i data-feather="clock" class="icon-sm me-2" appFeatherIcon></i>
              Recently Created Roles
            </h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Role Name</th>
                    <th class="text-center">Created</th>
                    <th class="text-center">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let role of recentlyCreatedRoles">
                    <td>
                      <span class="role-name">{{ role.roleName }}</span>
                    </td>
                    <td class="text-center">
                      <small class="text-muted">{{ role.createdAt | date:'MMM d, y' }}</small>
                    </td>
                    <td class="text-center">
                      <span class="badge" [class]="role.isActive ? 'bg-success' : 'bg-danger'">
                        {{ role.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="recentlyCreatedRoles.length === 0">
                    <td colspan="3" class="text-center text-muted py-4">
                      No recent roles found
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
