<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a routerLink=".">Advanced UI</a></li>
    <li class="breadcrumb-item active" aria-current="page">Nxt-SortableJs</li>
  </ol>
</nav>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">Nxt-SortableJs</h4>
        <p class="text-secondary">Reorderable drag-and-drop lists for modern browsers and touch devices. Read the <a href="https://liquid-js.github.io/nxt-components/demo/sortablejs" target="_blank"> Official Nxt-SortableJs Documentation </a>for a full list of instructions and other options.</p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Simple list</h6>
        <ul class="list-group" [nxtSortablejs]="simpleItems">
          @for (item of simpleItems; track item) {
            <li class="list-group-item">{{ item }}</li>
          }
        </ul>
        <pre class="mb-0 mt-3"> {{ simpleItems | json }} </pre>
      </div>
    </div>
  </div>
  <div class="col-md-6 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Handle</h6>
        <ul class="list-group" [nxtSortablejs]="simpleItems" [config]="{ handle: '.handle' }">
          @for (item of simpleItems; track item) {
            <li class="list-group-item"> 
              <i data-feather="move" appFeatherIcon class="icon-sm handle me-2"></i> 
              {{ item }}
            </li>
          }
        </ul>
        <pre class="mb-0 mt-3"> {{ simpleItems | json }} </pre>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Transfer between lists</h6>
        <p class="text-secondary mb-3">These lists are connected together. You can drag / drop elements across the lists.</p>
        <div class="row">
          <div class="col">
            <ul class="list-group" [nxtSortablejs]="sharedItems1" [config]="sharedOptions">
              @for (item of sharedItems1; track item) {
                <li class="list-group-item list-group-item-primary">{{ item }}</li>
              }
            </ul>
            <pre class="mb-0 mt-3"> {{ sharedItems1 | json }} </pre>
          </div>
          <div class="col">
            <ul class="list-group" [nxtSortablejs]="sharedItems2" [config]="sharedOptions">
              @for (item of sharedItems2; track item) {
                <li class="list-group-item list-group-item-info">{{ item }}</li>
              }
            </ul>
            <pre class="mb-0 mt-3"> {{ sharedItems2 | json }} </pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Disabling Sorting</h6>
        <p class="text-secondary mb-3">Try sorting the list on the left. It is not possible because it has it's sort option set to false. However, you can still drag from the list on the left to the list on the right.</p>
        <div class="row">
          <div class="col">
            <ul class="list-group" [nxtSortablejs]="disablingSortingItems1" [config]="disablingSorting1Options">
              @for (item of disablingSortingItems1; track item) {
                <li class="list-group-item list-group-item-primary">{{ item }}</li>
              }
            </ul>
            <pre class="mb-0 mt-3"> {{ disablingSortingItems1 | json }} </pre>
          </div>
          <div class="col">
            <ul class="list-group" [nxtSortablejs]="disablingSortingItems2" [config]="disablingSorting2Options">
              @for (item of disablingSortingItems2; track item) {
                <li class="list-group-item list-group-item-info">{{ item }}</li>
              }
            </ul>
            <pre class="mb-0 mt-3"> {{ disablingSortingItems2 | json }} </pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 grid-margin grid-margin-md-0 stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Disabled Optons</h6>
        <p class="text-secondary mb-3">The 'Item 3' is disabled.</p>
        <ul class="list-group" [nxtSortablejs]="draggableItems" [config]="draggableOptions">
          @for (item of draggableItems; track item) {
            <li class="list-group-item" [class.draggable]="item.draggable" [class.disabled]="!item.draggable">{{ item.text }}</li>
          }
        </ul>
        <pre class="mb-0 mt-3"> {{ draggableItems | json }} </pre>
      </div>
    </div>
  </div>
  <div class="col-md-6 stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title mb-3">Events</h6>
        <p class="text-secondary mb-3">You can use the options' <code>onUpdate</code> method to track the changes.</p>
        <ul class="list-group" [nxtSortablejs]="eventItems" [config]="eventOptions">
          @for (item of eventItems; track item) {
            <li class="list-group-item"> Item {{ item }}</li>
          }
        </ul>
        <pre class="mb-0 mt-3"> Updated {{ eventUpdateCounter }} times </pre>
      </div>
    </div>
  </div>
</div>