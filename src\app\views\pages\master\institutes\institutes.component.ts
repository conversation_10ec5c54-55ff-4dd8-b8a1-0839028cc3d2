import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  InstituteService,
  Institute,
  InstituteStatistics
} from '../../../../core/services/institute.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { InstituteFormComponent } from './institute-form/institute-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-institutes',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    InstituteFormComponent,
    BulkUploadComponent
  ],
  templateUrl: './institutes.component.html',
  styleUrls: ['./institutes.component.scss']
})
export class InstitutesComponent implements OnInit {
  // Data properties
  institutes: Institute[] = [];
  deletedInstitutes: Institute[] = [];
  statistics: InstituteStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedType = '';
  selectedCountry = '';
  selectedRegulatoryBody = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedInstitutes: Set<string> = new Set();
  selectAll = false;

  // Filter options
  instituteTypes: any[] = [];
  countries: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private instituteService: InstituteService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadInstitutes();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.instituteTypes = this.instituteService.getInstituteTypes();
    this.countries = this.instituteService.getCountryList();
    this.regulatoryBodies = this.instituteService.getRegulatoryBodies();
  }

  /**
   * Load institutes with current filters
   */
  loadInstitutes(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      type: this.selectedType || undefined,
      country: this.selectedCountry || undefined,
      regulatory_body: this.selectedRegulatoryBody || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.instituteService.getInstitutes(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedInstitutes = response.data.filter(inst => inst.deleted_at);
            this.institutes = [];
          } else {
            this.institutes = response.data.filter(inst => !inst.deleted_at);
            this.deletedInstitutes = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load institutes';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load institutes. Please try again.'
        });
      }
    });
  }

  /**
   * Load institute statistics
   */
  loadStatistics(): void {
    this.instituteService.getInstituteStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search institutes
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadInstitutes();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadInstitutes();
  }

  /**
   * Filter by type
   */
  onTypeFilter(): void {
    this.currentPage = 1;
    this.loadInstitutes();
  }

  /**
   * Filter by country
   */
  onCountryFilter(): void {
    this.currentPage = 1;
    this.loadInstitutes();
  }

  /**
   * Filter by regulatory body
   */
  onRegulatoryBodyFilter(): void {
    this.currentPage = 1;
    this.loadInstitutes();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedInstitutes.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadInstitutes();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadInstitutes();
  }

  /**
   * Open create institute modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(InstituteFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadInstitutes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Institute created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit institute modal
   */
  openEditModal(institute: Institute): void {
    const modalRef = this.modalService.open(InstituteFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.institute = { ...institute };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadInstitutes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Institute updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete institute
   */
  deleteInstitute(institute: Institute): void {
    this.popupService.showConfirmation({
      title: 'Delete Institute',
      message: `Are you sure you want to delete "${institute.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.instituteService.deleteInstitute(institute.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadInstitutes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Institute deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete institute.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore institute
   */
  restoreInstitute(institute: Institute): void {
    this.popupService.showConfirmation({
      title: 'Restore Institute',
      message: `Are you sure you want to restore "${institute.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.instituteService.restoreInstitute(institute.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadInstitutes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Institute restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore institute.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadInstitutes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Institutes uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.instituteService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'institutes_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Toggle institute selection
   */
  toggleSelection(instituteId: string): void {
    if (this.selectedInstitutes.has(instituteId)) {
      this.selectedInstitutes.delete(instituteId);
    } else {
      this.selectedInstitutes.add(instituteId);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    const currentList = this.getCurrentList();

    if (this.selectAll) {
      this.selectedInstitutes.clear();
    } else {
      currentList.forEach(institute => this.selectedInstitutes.add(institute.id));
    }
    this.selectAll = !this.selectAll;
  }

  /**
   * Update select all state
   */
  private updateSelectAllState(): void {
    const currentList = this.getCurrentList();
    this.selectAll = currentList.length > 0 &&
      currentList.every(institute => this.selectedInstitutes.has(institute.id));
  }

  /**
   * Bulk delete selected institutes
   */
  bulkDelete(): void {
    if (this.selectedInstitutes.size === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select institutes to delete.'
      });
      return;
    }

    this.popupService.showConfirmation({
      title: 'Bulk Delete',
      message: `Are you sure you want to delete ${this.selectedInstitutes.size} selected institutes?`,
      confirmText: 'Yes, Delete All',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.popupService.showInfo({
          title: 'Feature Coming Soon',
          message: 'Bulk delete functionality will be implemented in the next update.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadInstitutes();
    this.loadStatistics();
  }

  /**
   * Get institute type label
   */
  getInstituteTypeLabel(type: string): string {
    return this.instituteService.getInstituteTypeLabel(type);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): Institute[] {
    return this.viewMode === 'deleted' ? this.deletedInstitutes : this.institutes;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByInstituteId(index: number, institute: Institute): string {
    return institute.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
