import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef, ViewChildren } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { MasterService } from '../../../../core/services/master.service';
import { Profession, ProfessionCreate, ProfessionUpdate } from '../../../../core/models/profession.model';
import { catchError, debounceTime, finalize, of } from 'rxjs';
import Swal from 'sweetalert2';

// Sortable directive
export type SortColumn = 'name' | 'type' | 'status' | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Helper function for sorting
function compare(v1: string | number | null | undefined, v2: string | number | null | undefined) {
  // Handle null/undefined values
  if (v1 === null || v1 === undefined) return -1;
  if (v2 === null || v2 === undefined) return 1;

  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-profession',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective,
    FormsModule,
    ReactiveFormsModule,
    NgbdSortableHeader,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './profession.component.html',
  styleUrl: './profession.component.scss'
})
export class ProfessionComponent implements OnInit {  // Data
  professions: Profession[] = [];
  selectedProfession: Profession | null = null;

  // Loading state
  loading = false;
  submitting = false;
  deleting = false;
  restoring = false;
  deletingProfessionId: number | null = null;
  restoringProfessionId: number | null = null;

  // Form
  professionForm: FormGroup;
  formMode: 'create' | 'edit' = 'create';
  currentProfessionId?: number;

  // Search
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 10;
  totalItems = 0;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  constructor(
    private masterService: MasterService,
    private modalService: NgbModal,
    private fb: FormBuilder
  ) {
    // Initialize form
    this.professionForm = this.fb.group({
      name: ['', [Validators.required]],
      type: ['Profession', [Validators.required]],
      status: ['active', [Validators.required]]
    });
  }
    ngOnInit(): void {
    // Load initial data
    this.loadProfessions();

    // Set up search
    this.searchTerm.valueChanges.pipe(
      debounceTime(300)  // Add debounce to prevent too many API calls
    ).subscribe(value => {
      this.page = 1;
      this.loadProfessions();
    });
  }

  /**
   * Force reload data after CRUD operations
   */
  forceReloadData(): void {
    console.log('🔄 Force reloading profession data...');

    // Clear service cache first
    this.masterService.refreshProfessionCache();

    // Reset pagination to first page
    this.page = 1;

    // Reload data
    this.loadProfessions();
  }

  /**
   * Reset form state and variables
   */
  resetFormState(): void {
    console.log('🧹 Resetting form state...');
    this.formMode = 'create';
    this.currentProfessionId = undefined;
    this.submitting = false;
    this.professionForm.reset();
  }
    loadProfessions() {
    console.log('🔍 Loading professions...', {
      page: this.page,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm.value,
      timestamp: new Date().toISOString()
    });
    this.loading = true;

    // If search term is present, we'll handle filtering in the component
    // for more responsive search experience
    const shouldUseClientSideFiltering = this.searchTerm.value && this.searchTerm.value.trim() !== '';

    // Convert page/size to skip/limit for new API format
    const skip = (this.page - 1) * this.pageSize;
    this.masterService.getProfessions(skip, this.pageSize, shouldUseClientSideFiltering ? '' : this.searchTerm.value)
      .pipe(
        catchError(error => {
          console.error('Error loading professions:', error);
          this.showErrorMessage('Failed to load professions. Please try again.');
          return of({ items: [], total: 0, page: 1, size: this.pageSize, pages: 0 });
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(response => {
        if (shouldUseClientSideFiltering) {
          // Apply client-side filtering
          const filteredProfessions = this.filterProfessions(response.items, this.searchTerm.value);
          this.professions = filteredProfessions;
          this.totalItems = filteredProfessions.length;
        } else {
          // Use server response as is
          this.professions = response.items;
          this.totalItems = response.total;
        }
      });
  }

  // Refresh when pagination changes
  onPageChange(page: number) {
    this.page = page;
    this.loadProfessions();
  }

  // Handle sorting
  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      // Reset to original order
      this.loadProfessions();
    } else {
      this.professions = [...this.professions].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }
    // Open modal for creating/editing
  openProfessionModal(modal: TemplateRef<any>, profession?: Profession) {
    if (profession) {
      // Edit mode
      this.formMode = 'edit';
      this.currentProfessionId = profession.id;
      this.professionForm.patchValue({
        name: profession.name,
        type: this.convertTypeToDisplayValue(profession.type),
        status: profession.status
      });
    } else {
      // Create mode
      this.formMode = 'create';
      this.currentProfessionId = undefined;
      this.professionForm.reset({
        name: '',
        type: 'Profession',
        status: 'active'
      });
    }

    this.modalService.open(modal, { centered: true });
  }
    // View profession details
  viewProfession(profession: Profession, modal: TemplateRef<any>) {
    // Get the full profession details
    if (profession.id) {
      this.loading = true;

      this.masterService.getProfession(profession.id)
        .pipe(
          catchError(error => {
            console.error('Error fetching profession details:', error);
            let errorMessage = 'Failed to fetch profession details. Please try again.';

            if (error && error.message && error.message.includes('No UUID mapping found')) {
              errorMessage = 'The data has changed. Please refresh the page and try again.';
            }

            this.showErrorMessage(errorMessage);
            return of(null);
          }),
          finalize(() => {
            this.loading = false;
          })
        )
        .subscribe(result => {
          if (result) {
            this.selectedProfession = result;
            console.log('Selected profession for view modal:', this.selectedProfession);
            this.modalService.open(modal, { centered: true, size: 'lg' });
          }
        });
    }
  }

  // Save profession (create or update)
  saveProfession() {
    if (this.professionForm.invalid) {
      return;
    }

    this.submitting = true;

    if (this.formMode === 'create') {
      // Create new profession
      const formValue = this.professionForm.value;
      const newProfession: ProfessionCreate = {
        ...formValue,
        type: this.convertTypeToApiValue(formValue.type)
      };

      this.masterService.createProfession(newProfession)
        .pipe(
          catchError(error => {
            console.error('Error creating profession:', error);
            this.showErrorMessage('Failed to create profession. Please try again.');
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            // Close modal and reset state
            this.modalService.dismissAll();
            this.resetFormState();
            this.showSuccessMessage('Profession created successfully!');

            // Force reload with a slight delay to ensure modal is closed
            setTimeout(() => {
              console.log('🔄 Forcing reload after create...');
              this.forceReloadData();
            }, 200);
          }
        });
    } else if (this.formMode === 'edit' && this.currentProfessionId) {
    // Update existing profession
      const formValue = this.professionForm.value;
      const updatedProfession: ProfessionUpdate = {
        ...formValue,
        type: this.convertTypeToApiValue(formValue.type)
      };

      this.masterService.updateProfession(this.currentProfessionId, updatedProfession)
        .pipe(
          catchError(error => {
            console.error('Error updating profession:', error);
            let errorMessage = 'Failed to update profession. Please try again.';

            if (error.message && error.message.includes('No UUID mapping found')) {
              errorMessage = 'The data has changed. Please refresh the page and try again.';
            }

            this.showErrorMessage(errorMessage);
            return of(null);
          }),
          finalize(() => {
            this.submitting = false;
          })
        )
        .subscribe(result => {
          if (result) {
            // Close modal and reset state
            this.modalService.dismissAll();
            this.resetFormState();
            this.showSuccessMessage('Profession updated successfully!');

            // Force reload with a slight delay to ensure modal is closed
            setTimeout(() => {
              console.log('🔄 Forcing reload after update...');
              this.forceReloadData();
            }, 200);
          }
        });
    }
  }

  // Delete profession
  deleteProfession(profession: Profession) {
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete profession "${profession.name}"?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#000',
      cancelButtonColor: '#6c757d',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed && profession.id) {
        // Set loading state for this specific profession
        this.deleting = true;
        this.deletingProfessionId = profession.id;

        this.masterService.deleteProfession(profession.id)
          .pipe(
            catchError(error => {
              console.error('Error deleting profession:', error);
              let errorMessage = 'Failed to delete profession. Please try again.';

              if (error.message && error.message.includes('No UUID mapping found')) {
                errorMessage = 'The data has changed. Please refresh the page and try again.';
              }

              this.showErrorMessage(errorMessage);
              return of(null);
            }),
            finalize(() => {
              // Reset loading state
              this.deleting = false;
              this.deletingProfessionId = null;
            })
          )
          .subscribe(result => {
            if (result !== null) {
              this.showSuccessMessage('Profession deleted successfully!');
              this.forceReloadData();
            }
          });
      }
    });
  }

  // Restore profession
  restoreProfession(profession: Profession) {
    if (!profession.id) return;

    // Set loading state for this specific profession
    this.restoring = true;
    this.restoringProfessionId = profession.id;

    this.masterService.restoreProfession(profession.id)
      .pipe(
        catchError(error => {
          console.error('Error restoring profession:', error);
          let errorMessage = 'Failed to restore profession. Please try again.';

          if (error.message && error.message.includes('No UUID mapping found')) {
            errorMessage = 'The data has changed. Please refresh the page and try again.';
          }

          this.showErrorMessage(errorMessage);
          return of(null);
        }),
        finalize(() => {
          // Reset loading state
          this.restoring = false;
          this.restoringProfessionId = null;
        })
      )
      .subscribe(result => {
        if (result) {
          this.showSuccessMessage('Profession restored successfully!');
          this.forceReloadData();
        }
      });
  }

  // Filter professions based on search term
  filterProfessions(professions: Profession[], searchTerm: string): Profession[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return professions;
    }

    searchTerm = searchTerm.toLowerCase().trim();

    return professions.filter(profession => {
      return (
        (profession.name && profession.name.toLowerCase().includes(searchTerm)) ||
        (profession.type && profession.type.toLowerCase().includes(searchTerm)) ||
        (profession.status && profession.status.toLowerCase().includes(searchTerm))
      );
    });
  }

  // Helper methods for status
  getStatusClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-success';
      case 'inactive': return 'bg-secondary';
      default: return 'bg-secondary';
    }
  }

  // Helper method to handle editing from view modal
  editFromViewModal(professionModal: TemplateRef<any>, currentModal: any) {
    if (this.selectedProfession) {
      // First dismiss the current modal
      currentModal.dismiss();
      // Then open the edit modal with the selected profession
      this.openProfessionModal(professionModal, this.selectedProfession);
    }
  }

  // Helper method to convert display type to API value
  private convertTypeToApiValue(displayType: string): string {
    switch (displayType) {
      case 'Profession':
        return 'professional';
      case 'Non Profession':
        return 'non_professional';
      default:
        return displayType; // Return as-is if already in API format
    }
  }

  // Helper method to convert API type to display value
  private convertTypeToDisplayValue(apiType: string): string {
    switch (apiType) {
      case 'professional':
      case 'profession':
        return 'Profession';
      case 'non_professional':
      case 'non_profession':
      case 'non-profession':
        return 'Non Profession';
      default:
        return apiType; // Return as-is if unknown format
    }
  }

  // Alerts
  showSuccessMessage(message: string) {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
      timer: 2000,
      showConfirmButton: false
    });
  }

  showErrorMessage(message: string) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message
    });
  }
}
