<div class="row">
  <!-- Month and Year Filter Card -->
  <div class="col-md-12 grid-margin stretch-card mb-3">
    <div class="card filter-card">
      <div class="card-body">
        <h6 class="card-title mb-3">Filter by Month and Year</h6>
        <div class="row align-items-end">
          <div class="col-12 col-md-3 col-lg-2 mb-md-0">
            <label class="form-label">Year</label>
            <select
              class="form-select"
              name="selectedYear"
              [(ngModel)]="selectedYear"
              (change)="updateAvailableMonths(selectedYear)">
              <option value="">All Years</option>
              <option *ngFor="let year of years" [value]="year">{{ year }}</option>
            </select>
          </div>
          <div class="col-12 col-md-3 col-lg-2 mb-md-0">
            <label class="form-label">Month</label>
            <select
              class="form-select"
              name="selectedMonth"
              [(ngModel)]="selectedMonth">
              <option value="">All Months</option>
              <option *ngFor="let month of months; let i = index" [value]="availableMonths[i]">{{ month }}</option>
            </select>
          </div>

          <div class="col-12 col-md-3 col-lg-2">
            <button class="btn btn-primary w-100" (click)="filterByMonthYear()">
            
              View
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Salary Slip Card -->
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Salary Slip Generation</h6>
        </div>

        <!-- Employee Information -->
        <div class="employee-info mb-4">
          <div class="row">
            <div class="col-md-4">
              <p><strong>Employee ID:</strong> {{ employee.id }}</p>
            </div>
            <div class="col-md-4">
              <p><strong>Name:</strong> {{ employee.name }}</p>
            </div>
            <div class="col-md-4">
              <p><strong>Department:</strong> {{ employee.department }}</p>
            </div>
          </div>
        </div>

        <!-- Salary Details Modal/Overlay -->
        <div *ngIf="showSalarySlipDetails" class="salary-slip-details">
          <div class="salary-slip-modal">
            <div class="modal-header d-flex justify-content-between align-items-center">
              <h5 class="modal-title">Salary Slip - {{ selectedSalarySlip.monthYear }}</h5>
              <div class="d-flex align-items-center">
                <!-- View mode toggle -->
                <div class="btn-group me-3">
                  <button 
                    [class.active]="viewMode === 'normal'" 
                    class="btn btn-sm" 
                    [class.btn-primary]="viewMode === 'normal'"
                    [class.btn-outline-primary]="viewMode !== 'normal'"
                    (click)="viewMode = 'normal'">
                    <i data-feather="list" class="icon-sm me-1" appFeatherIcon></i> Normal View
                  </button>
                  <button 
                    [class.active]="viewMode === 'pdf'" 
                    class="btn btn-sm" 
                    [class.btn-primary]="viewMode === 'pdf'"
                    [class.btn-outline-primary]="viewMode !== 'pdf'"
                    (click)="viewMode = 'pdf'">
                    <i data-feather="file-text" class="icon-sm me-1" appFeatherIcon></i> PDF View
                  </button>
                </div>
                <button class="btn btn-sm btn-outline-secondary" (click)="closeSalarySlipDetails()">
                  <i data-feather="x" class="icon-sm" appFeatherIcon></i>
                </button>
              </div>
            </div>
            <div class="modal-body">
              <!-- Normal View -->
              <div *ngIf="viewMode === 'normal'" class="normal-view">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex justify-content-between mb-4">
                      <div>
                        <h4>SALARY SLIP</h4>
                        <p class="text-muted">For the month of {{ selectedSalarySlip.monthYear }}</p>
                      </div>
                      <div>
                        <img src="assets/images/brand-logo/brand-logo.png" alt="Company Logo" height="60">
                      </div>
                    </div>
                    
                    <div class="row mb-4">
                      <div class="col-md-6">
                        <h6>Employee Details</h6>
                        <table class="table table-sm table-borderless">
                          <tbody>
                            <tr>
                              <td><strong>Employee ID:</strong></td>
                              <td>{{ employee.id }}</td>
                            </tr>
                            <tr>
                              <td><strong>Name:</strong></td>
                              <td>{{ employee.name }}</td>
                            </tr>
                            <tr>
                              <td><strong>Department:</strong></td>
                              <td>{{ employee.department }}</td>
                            </tr>
                            <tr>
                              <td><strong>Designation:</strong></td>
                              <td>Senior Developer</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div class="col-md-6">
                        <h6>Payment Details</h6>
                        <table class="table table-sm table-borderless">
                          <tbody>
                            <tr>
                              <td><strong>Pay Period:</strong></td>
                              <td>{{ selectedSalarySlip.monthYear }}</td>
                            </tr>
                            <tr>
                              <td><strong>Payment Date:</strong></td>
                              <td>{{ selectedSalarySlip.date }}</td>
                            </tr>
                            <tr>
                              <td><strong>Payment Method:</strong></td>
                              <td>Direct Deposit</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    
                    <div class="row mb-4">
                      <div class="col-md-12">
                        <h6>Earnings</h6>
                        <table class="table table-bordered">
                          <thead class="table-light">
                            <tr>
                              <th>Description</th>
                              <th class="text-end">Amount</th>
                            </tr>
                          </thead>
                          <tbody>                            <tr>
                              <td>Basic Salary</td>
                              <td class="text-end">₹4,500.00</td>
                            </tr>
                            <tr>
                              <td>House Rent Allowance</td>
                              <td class="text-end">₹750.00</td>
                            </tr>
                            <tr>
                              <td>Transport Allowance</td>
                              <td class="text-end">₹250.00</td>
                            </tr>
                            <tr>
                              <td>Medical Allowance</td>
                              <td class="text-end">₹200.00</td>
                            </tr>
                            <tr>
                              <td>Special Allowance</td>
                              <td class="text-end">₹300.00</td>
                            </tr>
                            <tr>
                              <td><strong>Total Earnings</strong></td>
                              <td class="text-end"><strong>₹6,000.00</strong></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    
                    <div class="row mb-4">
                      <div class="col-md-12">
                        <h6>Deductions</h6>
                        <table class="table table-bordered">
                          <thead class="table-light">
                            <tr>
                              <th>Description</th>
                              <th class="text-end">Amount</th>
                            </tr>
                          </thead>
                          <tbody>                            <tr>
                              <td>Income Tax</td>
                              <td class="text-end">₹450.00</td>
                            </tr>
                            <tr>
                              <td>Provident Fund</td>
                              <td class="text-end">₹300.00</td>
                            </tr>
                            <tr>
                              <td>Health Insurance</td>
                              <td class="text-end">₹150.00</td>
                            </tr>
                            <tr>
                              <td>Professional Tax</td>
                              <td class="text-end">₹100.00</td>
                            </tr>
                            <tr>
                              <td><strong>Total Deductions</strong></td>
                              <td class="text-end"><strong>₹1,000.00</strong></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    
                    <div class="row">
                      <div class="col-md-12">
                        <div class="card bg-light">
                          <div class="card-body">                            <div class="d-flex justify-content-between">
                              <h5>Net Pay:</h5>
                              <h5>₹5,000.00</h5>
                            </div>
                            <p class="mb-0 text-muted">Five thousand rupees only</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- PDF View -->
              <div *ngIf="viewMode === 'pdf'" class="pdf-view">
                <div class="card">
                  <div class="card-body p-0">
                    <!-- PDF Viewer Mock -->
                    <div class="pdf-container">
                      <div class="pdf-toolbar bg-light p-2 d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                          <button class="btn btn-sm btn-outline-secondary me-2">
                            <i data-feather="zoom-in" class="icon-sm" appFeatherIcon></i>
                          </button>
                          <button class="btn btn-sm btn-outline-secondary me-2">
                            <i data-feather="zoom-out" class="icon-sm" appFeatherIcon></i>
                          </button>
                          <span>100%</span>
                        </div>
                        <div>
                          <button class="btn btn-sm btn-primary" (click)="downloadSalarySlip(selectedSalarySlip)">
                            <i data-feather="download" class="icon-sm me-1" appFeatherIcon></i> Download
                          </button>
                        </div>
                      </div>
                      <div class="pdf-content p-4 bg-white">
                        <!-- This would be replaced with an actual PDF viewer in a real app -->
                        <div class="text-center pdf-mock">
                          <img src="assets/images/brand-logo/brand-logo.png" alt="Company Logo" height="60" class="mb-4">
                          <h4 class="mb-3">SALARY SLIP</h4>
                          <p class="text-muted mb-4">For the month of {{ selectedSalarySlip.monthYear }}</p>
                          
                          <div class="pdf-mockup">
                            <div class="watermark">PDF PREVIEW</div>
                            <div class="d-flex justify-content-between mb-4">
                              <div class="text-start">
                                <p><strong>Employee ID:</strong> {{ employee.id }}</p>
                                <p><strong>Name:</strong> {{ employee.name }}</p>
                                <p><strong>Department:</strong> {{ employee.department }}</p>
                              </div>
                              <div class="text-end">
                                <p><strong>Payment Date:</strong> {{ selectedSalarySlip.date }}</p>
                                <p><strong>Pay Period:</strong> {{ selectedSalarySlip.monthYear }}</p>
                              </div>
                            </div>
                            
                            <table class="table table-bordered pdf-table">
                              <thead class="table-light">
                                <tr>
                                  <th colspan="2">Earnings</th>
                                  <th colspan="2">Deductions</th>
                                </tr>
                                <tr>
                                  <th>Description</th>
                                  <th>Amount</th>
                                  <th>Description</th>
                                  <th>Amount</th>
                                </tr>
                              </thead>
                              <tbody>                                <tr>
                                  <td>Basic Salary</td>
                                  <td>₹4,500.00</td>
                                  <td>Income Tax</td>
                                  <td>₹450.00</td>
                                </tr>
                                <tr>
                                  <td>House Rent Allowance</td>
                                  <td>₹750.00</td>
                                  <td>Provident Fund</td>
                                  <td>₹300.00</td>
                                </tr>
                                <tr>
                                  <td>Transport Allowance</td>
                                  <td>₹250.00</td>
                                  <td>Health Insurance</td>
                                  <td>₹150.00</td>
                                </tr>
                                <tr>
                                  <td>Medical Allowance</td>
                                  <td>₹200.00</td>
                                  <td>Professional Tax</td>
                                  <td>₹100.00</td>
                                </tr>
                                <tr>
                                  <td>Special Allowance</td>
                                  <td>₹300.00</td>
                                  <td></td>
                                  <td></td>
                                </tr>
                                <tr>
                                  <td><strong>Total Earnings</strong></td>
                                  <td><strong>₹6,000.00</strong></td>
                                  <td><strong>Total Deductions</strong></td>
                                  <td><strong>₹1,000.00</strong></td>
                                </tr>
                              </tbody>
                            </table>
                              <div class="net-pay mt-4 p-3 bg-light text-center">
                              <h5>Net Pay: ₹5,000.00</h5>
                              <p class="mb-0">Five thousand rupees only</p>
                            </div>
                            
                            <div class="signature mt-5 pt-5">
                              <div class="row">
                                <div class="col-6 text-center">
                                  <div class="signature-line"></div>
                                  <p>Employee Signature</p>
                                </div>
                                <div class="col-6 text-center">
                                  <div class="signature-line"></div>
                                  <p>Employer Signature</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Salary Entries Table -->
        <div class="table-responsive">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">Actions</th>
                <th scope="col" sortable="date" (sort)="onSort($event)">Date</th>
                <th scope="col" sortable="monthYear" (sort)="onSort($event)">Month/Year</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of rows">
                <td class="action-icons">
                  <a href="javascript:;" class="action-icon" (click)="viewSalarySlip(row)" ngbTooltip="View Salary Slip">
                    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                  </a>
                  <a href="javascript:;" class="action-icon" (click)="downloadSalarySlip(row)" ngbTooltip="Download Salary Slip">
                    <i data-feather="download" class="icon-sm" appFeatherIcon></i>
                  </a>
                </td>
                <td><span class="badge bg-light-primary text-primary">{{ row.date }}</span></td>
                <td>{{ row.monthYear }}</td>
              </tr>

              <!-- Empty state -->
              <tr *ngIf="rows.length === 0">
                <td colspan="3" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="database" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No salary records found</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3">
          <div>
            <span class="text-muted" *ngIf="rows.length > 0">
              Showing 1 to {{ rows.length }} of {{ rows.length }} entries
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
