import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface HttpErrorHandlerOptions {
  skipErrorInterceptor?: boolean;
  showSuccessMessage?: boolean;
  successMessage?: string;
  customErrorHandler?: (error: any) => void;
}

@Injectable({
  providedIn: 'root'
})
export class HttpErrorHandlerService {

  constructor(private http: HttpClient) {}

  /**
   * Make a GET request with custom error handling options
   */
  get<T>(url: string, options?: {
    headers?: HttpHeaders | { [header: string]: string | string[] };
    params?: HttpParams | { [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean> };
    errorHandling?: HttpErrorHandlerOptions;
  }): Observable<T> {
    const headers = this.buildHeaders(options?.headers, options?.errorHandling);
    
    return this.http.get<T>(url, {
      headers,
      params: options?.params
    });
  }

  /**
   * Make a POST request with custom error handling options
   */
  post<T>(url: string, body: any, options?: {
    headers?: HttpHeaders | { [header: string]: string | string[] };
    errorHandling?: HttpErrorHandlerOptions;
  }): Observable<T> {
    const headers = this.buildHeaders(options?.headers, options?.errorHandling);
    
    return this.http.post<T>(url, body, { headers });
  }

  /**
   * Make a PUT request with custom error handling options
   */
  put<T>(url: string, body: any, options?: {
    headers?: HttpHeaders | { [header: string]: string | string[] };
    errorHandling?: HttpErrorHandlerOptions;
  }): Observable<T> {
    const headers = this.buildHeaders(options?.headers, options?.errorHandling);
    
    return this.http.put<T>(url, body, { headers });
  }

  /**
   * Make a PATCH request with custom error handling options
   */
  patch<T>(url: string, body: any, options?: {
    headers?: HttpHeaders | { [header: string]: string | string[] };
    errorHandling?: HttpErrorHandlerOptions;
  }): Observable<T> {
    const headers = this.buildHeaders(options?.headers, options?.errorHandling);
    
    return this.http.patch<T>(url, body, { headers });
  }

  /**
   * Make a DELETE request with custom error handling options
   */
  delete<T>(url: string, options?: {
    headers?: HttpHeaders | { [header: string]: string | string[] };
    errorHandling?: HttpErrorHandlerOptions;
  }): Observable<T> {
    const headers = this.buildHeaders(options?.headers, options?.errorHandling);
    
    return this.http.delete<T>(url, { headers });
  }

  /**
   * Build headers with error handling options
   */
  private buildHeaders(
    existingHeaders?: HttpHeaders | { [header: string]: string | string[] },
    errorHandling?: HttpErrorHandlerOptions
  ): HttpHeaders {
    let headers = existingHeaders instanceof HttpHeaders 
      ? existingHeaders 
      : new HttpHeaders(existingHeaders || {});

    // Add skip interceptor header if requested
    if (errorHandling?.skipErrorInterceptor) {
      headers = headers.set('X-Skip-Error-Interceptor', 'true');
    }

    return headers;
  }

  /**
   * Create error handling options for common scenarios
   */
  static createOptions(options: {
    skipErrorInterceptor?: boolean;
    showSuccessMessage?: boolean;
    successMessage?: string;
  }): { errorHandling: HttpErrorHandlerOptions } {
    return {
      errorHandling: {
        skipErrorInterceptor: options.skipErrorInterceptor,
        showSuccessMessage: options.showSuccessMessage,
        successMessage: options.successMessage
      }
    };
  }

  /**
   * Predefined options for common use cases
   */
  static readonly OPTIONS = {
    // Skip automatic error handling - component will handle errors manually
    MANUAL_ERROR_HANDLING: { errorHandling: { skipErrorInterceptor: true } },
    
    // Show success message after successful operation
    WITH_SUCCESS_MESSAGE: (message: string) => ({ 
      errorHandling: { showSuccessMessage: true, successMessage: message } 
    }),
    
    // Silent operation - no automatic error or success messages
    SILENT: { errorHandling: { skipErrorInterceptor: true, showSuccessMessage: false } }
  };
}
