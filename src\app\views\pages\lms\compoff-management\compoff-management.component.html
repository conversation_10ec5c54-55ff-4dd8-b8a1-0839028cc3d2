<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h4 class="mb-1">Comp-off Management</h4>
          <p class="text-muted mb-0">Review and approve comp-off requests</p>
        </div>
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary" (click)="refresh()" [disabled]="loading">
            <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
            Refresh
          </button>
          <button class="btn btn-success" (click)="exportRequests()" [disabled]="loading">
            <i class="fas fa-download"></i>
            Export
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <!-- Status Filter -->
            <div class="col-md-3">
              <label class="form-label">Status Filter</label>
              <select class="form-select" [(ngModel)]="statusFilter" (change)="onStatusFilterChange(statusFilter)">
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="all">All</option>
              </select>
            </div>

            <!-- Search -->
            <div class="col-md-6">
              <label class="form-label">Search</label>
              <div class="input-group">
                <input 
                  type="text" 
                  class="form-control" 
                  placeholder="Search by employee name, code, reason, or date..."
                  [(ngModel)]="searchTerm"
                  (keyup.enter)="onSearch()"
                >
                <button class="btn btn-outline-secondary" type="button" (click)="onSearch()">
                  <i class="fas fa-search"></i>
                </button>
                <button class="btn btn-outline-secondary" type="button" (click)="clearSearch()" *ngIf="searchTerm">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <!-- Results Count -->
            <div class="col-md-3 text-end">
              <label class="form-label d-block">&nbsp;</label>
              <span class="text-muted">
                Showing {{ filteredRequests.length }} of {{ totalItems }} requests
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="row" *ngIf="loading">
    <div class="col-12">
      <div class="card">
        <div class="card-body text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-3 mb-0">Loading comp-off requests...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div class="row" *ngIf="error && !loading">
    <div class="col-12">
      <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        {{ error }}
        <button class="btn btn-sm btn-outline-danger ms-3" (click)="refresh()">
          Try Again
        </button>
      </div>
    </div>
  </div>

  <!-- Requests Table -->
  <div class="row" *ngIf="!loading && !error">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <!-- Empty State -->
          <div *ngIf="filteredRequests.length === 0" class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No comp-off requests found</h5>
            <p class="text-muted mb-0">
              <span *ngIf="statusFilter === 'pending'">There are no pending comp-off requests at the moment.</span>
              <span *ngIf="statusFilter !== 'pending'">No requests match your current filters.</span>
            </p>
          </div>

          <!-- Requests Table -->
          <div *ngIf="filteredRequests.length > 0" class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Employee</th>
                  <th>Working Date</th>
                  <th>Reason</th>
                  <th>Status</th>
                  <th>Requested Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let request of filteredRequests">
                  <td>
                    <div>
                      <strong>{{ request.employee_name || 'N/A' }}</strong>
                      <br>
                      <small class="text-muted">{{ request.employee_code || 'N/A' }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ formatDate(request.working_date) }}</span>
                  </td>
                  <td>
                    <span class="text-truncate d-inline-block" style="max-width: 200px;" [title]="request.reason">
                      {{ request.reason }}
                    </span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="getStatusBadgeClass(request.status)">
                      {{ request.status | titlecase }}
                    </span>
                  </td>
                  <td>
                    <small>{{ formatDateTime(request.requested_at) }}</small>
                  </td>
                  <td>
                    <div class="btn-group" role="group">
                      <!-- Pending Actions -->
                      <ng-container *ngIf="request.status === 'pending'">
                        <button 
                          class="btn btn-sm btn-success" 
                          (click)="quickApprove(request)"
                          title="Quick Approve"
                        >
                          <i class="fas fa-check"></i>
                        </button>
                        <button 
                          class="btn btn-sm btn-danger" 
                          (click)="quickReject(request)"
                          title="Quick Reject"
                        >
                          <i class="fas fa-times"></i>
                        </button>
                        <button 
                          class="btn btn-sm btn-outline-primary" 
                          (click)="openApprovalModal(request)"
                          title="Detailed Review"
                        >
                          <i class="fas fa-edit"></i>
                        </button>
                      </ng-container>

                      <!-- Approved/Rejected Actions -->
                      <ng-container *ngIf="request.status !== 'pending'">
                        <button 
                          class="btn btn-sm btn-outline-info" 
                          (click)="openApprovalModal(request)"
                          title="View Details"
                        >
                          <i class="fas fa-eye"></i>
                        </button>
                      </ng-container>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <nav *ngIf="totalPages > 1" aria-label="Comp-off requests pagination">
            <ul class="pagination justify-content-center mb-0">
              <li class="page-item" [class.disabled]="currentPage === 1">
                <button class="page-link" (click)="onPageChange(currentPage - 1)" [disabled]="currentPage === 1">
                  Previous
                </button>
              </li>
              <li 
                class="page-item" 
                *ngFor="let page of [].constructor(totalPages); let i = index"
                [class.active]="currentPage === i + 1"
              >
                <button class="page-link" (click)="onPageChange(i + 1)">
                  {{ i + 1 }}
                </button>
              </li>
              <li class="page-item" [class.disabled]="currentPage === totalPages">
                <button class="page-link" (click)="onPageChange(currentPage + 1)" [disabled]="currentPage === totalPages">
                  Next
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Approval Modal -->
<ng-template #approvalModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      <i class="fas fa-clipboard-check me-2"></i>
      Review Comp-off Request
    </h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
  </div>

  <div class="modal-body" *ngIf="selectedRequest">
    <!-- Request Details -->
    <div class="card mb-3">
      <div class="card-header">
        <h6 class="mb-0">Request Details</h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <strong>Employee:</strong><br>
            {{ selectedRequest.employee_name || 'N/A' }}
            <small class="text-muted d-block">{{ selectedRequest.employee_code || 'N/A' }}</small>
          </div>
          <div class="col-md-6">
            <strong>Working Date:</strong><br>
            <span class="badge bg-info">{{ formatDate(selectedRequest.working_date) }}</span>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-12">
            <strong>Reason:</strong><br>
            {{ selectedRequest.reason }}
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-6">
            <strong>Current Status:</strong><br>
            <span class="badge" [ngClass]="getStatusBadgeClass(selectedRequest.status)">
              {{ selectedRequest.status | titlecase }}
            </span>
          </div>
          <div class="col-md-6">
            <strong>Requested Date:</strong><br>
            <small>{{ formatDateTime(selectedRequest.requested_at) }}</small>
          </div>
        </div>

        <!-- Approval Details (if already processed) -->
        <div *ngIf="selectedRequest.status !== 'pending'" class="row mt-3">
          <div class="col-12">
            <div class="alert" [ngClass]="selectedRequest.status === 'approved' ? 'alert-success' : 'alert-danger'">
              <strong>{{ selectedRequest.status === 'approved' ? 'Approved' : 'Rejected' }} by:</strong>
              {{ selectedRequest.approved_by_name || 'N/A' }}
              <br>
              <strong>Date:</strong> {{ formatDateTime(selectedRequest.approved_at || '') }}
              <div *ngIf="selectedRequest.rejection_reason" class="mt-2">
                <strong>Comments:</strong> {{ selectedRequest.rejection_reason }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Approval Form (only for pending requests) -->
    <form [formGroup]="approvalForm" *ngIf="selectedRequest.status === 'pending'">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Your Decision</h6>
        </div>
        <div class="card-body">
          <!-- Action Selection -->
          <div class="mb-3">
            <label class="form-label">Action <span class="text-danger">*</span></label>
            <div class="form-check">
              <input class="form-check-input" type="radio" formControlName="action" value="approve" id="approve">
              <label class="form-check-label text-success" for="approve">
                <i class="fas fa-check me-1"></i>
                Approve Request
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" formControlName="action" value="reject" id="reject">
              <label class="form-check-label text-danger" for="reject">
                <i class="fas fa-times me-1"></i>
                Reject Request
              </label>
            </div>
          </div>

          <!-- Comments -->
          <div class="mb-3">
            <label class="form-label">Comments (Optional)</label>
            <textarea 
              class="form-control" 
              formControlName="comments" 
              rows="3"
              placeholder="Add any comments or reasons for your decision..."
            ></textarea>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
      Close
    </button>
    <button 
      *ngIf="selectedRequest?.status === 'pending'"
      type="button" 
      class="btn btn-primary" 
      (click)="submitApproval()"
      [disabled]="approvalForm.invalid || submittingApproval"
    >
      <span *ngIf="submittingApproval" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ approvalForm.get('action')?.value === 'approve' ? 'Approve' : 'Reject' }} Request
    </button>
  </div>
</ng-template>
