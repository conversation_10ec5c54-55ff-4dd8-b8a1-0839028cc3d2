// Sources Component Styles

.sources-component {
  .card-title {
    font-weight: 600;
    color: var(--bs-dark);
  }

  .nav-tabs {
    .nav-link {
      border: none;
      color: var(--bs-secondary);
      font-weight: 500;
      padding: 0.75rem 1.5rem;
      
      &.active {
        background-color: var(--bs-primary);
        color: white;
        border-radius: 0.375rem;
      }
      
      &:hover:not(.active) {
        background-color: var(--bs-light);
        color: var(--bs-primary);
      }
    }
  }

  .table {
    th {
      font-weight: 600;
      color: var(--bs-dark);
      border-bottom: 2px solid var(--bs-border-color);
      padding: 1rem 0.75rem;
    }

    td {
      padding: 1rem 0.75rem;
      vertical-align: middle;
    }

    .performance-metrics,
    .campaign-integration {
      font-size: 0.875rem;
      
      .badge {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
      }
    }
  }

  .badge {
    font-size: 0.75rem;
    font-weight: 500;
    
    &.bg-light {
      border: 1px solid var(--bs-border-color);
    }
    
    // Source type specific badge colors
    &.bg-primary {
      background-color: #0d6efd !important;
    }
    
    &.bg-success {
      background-color: #198754 !important;
    }
    
    &.bg-info {
      background-color: #0dcaf0 !important;
      color: var(--bs-dark) !important;
    }
    
    &.bg-warning {
      background-color: #ffc107 !important;
      color: var(--bs-dark) !important;
    }
    
    &.bg-danger {
      background-color: #dc3545 !important;
    }
    
    &.bg-secondary {
      background-color: #6c757d !important;
    }
  }

  .dropdown-menu {
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    .dropdown-item {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      
      &:hover {
        background-color: var(--bs-light);
      }
      
      &.text-danger:hover {
        background-color: var(--bs-danger);
        color: white;
      }
      
      &.text-success:hover {
        background-color: var(--bs-success);
        color: white;
      }
    }
  }

  .input-group {
    .input-group-text {
      background-color: var(--bs-light);
      border-color: var(--bs-border-color);
      color: var(--bs-secondary);
    }
  }

  .form-select {
    border-color: var(--bs-border-color);
    
    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }

  .form-check-input {
    &:checked {
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
    }
    
    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }

  .btn {
    font-weight: 500;
    
    &.btn-outline-secondary {
      border-color: var(--bs-border-color);
      
      &:hover {
        background-color: var(--bs-secondary);
        border-color: var(--bs-secondary);
      }
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .spinner-border {
    width: 2rem;
    height: 2rem;
  }

  .alert {
    border: none;
    border-radius: 0.5rem;
    
    &.alert-danger {
      background-color: rgba(var(--bs-danger-rgb), 0.1);
      color: var(--bs-danger);
    }
  }

  .card {
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    &.bg-primary,
    &.bg-success,
    &.bg-warning,
    &.bg-info {
      border: none;
      
      .card-body {
        padding: 1.5rem;
      }
    }
  }

  .text-muted {
    color: var(--bs-secondary) !important;
  }

  // Source specific styles
  .performance-metrics {
    .badge {
      margin-bottom: 0.25rem;
    }
  }

  .campaign-integration {
    .badge {
      margin-bottom: 0.25rem;
    }
  }

  // Source category color coding
  .category-digital {
    border-left: 4px solid #007bff;
  }

  .category-traditional {
    border-left: 4px solid #6c757d;
  }

  .category-word-of-mouth {
    border-left: 4px solid #28a745;
  }

  .category-paid {
    border-left: 4px solid #ffc107;
  }

  .category-organic {
    border-left: 4px solid #20c997;
  }

  .category-partnership {
    border-left: 4px solid #fd7e14;
  }

  .category-internal {
    border-left: 4px solid #6f42c1;
  }

  // High performing source highlighting
  .high-performance {
    background-color: rgba(var(--bs-success-rgb), 0.05);
    border-left: 3px solid var(--bs-success);
  }

  // Low performing source highlighting
  .low-performance {
    background-color: rgba(var(--bs-warning-rgb), 0.05);
    border-left: 3px solid var(--bs-warning);
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .d-flex.gap-2 {
      flex-direction: column;
      gap: 0.5rem !important;
      
      .btn {
        width: 100%;
      }
    }
    
    .table-responsive {
      font-size: 0.875rem;
    }
    
    .nav-tabs {
      .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
      }
    }
    
    .row.mb-3 {
      .col-md-2 {
        margin-bottom: 0.5rem;
      }
    }
  }

  @media (max-width: 576px) {
    .card-title {
      font-size: 1.25rem;
    }
    
    .table {
      th, td {
        padding: 0.5rem;
      }
    }
    
    .badge {
      font-size: 0.7rem;
      padding: 0.25rem 0.5rem;
    }
  }
}

// Animation for loading states
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table tbody tr {
  animation: fadeIn 0.3s ease-in-out;
}

// Custom scrollbar for table
.table-responsive {
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bs-light);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
    border-radius: 4px;
    
    &:hover {
      background: var(--bs-dark);
    }
  }
}

// Special styling for complex data
.performance-metrics,
.campaign-integration {
  max-width: 200px;
  
  .badge {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: inline-block;
  }
}

// Source type icons
.source-type-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  opacity: 0.7;
}

// Selection highlighting
.table tbody tr.selected {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

// Hover effects
.table tbody tr:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

// Attribution model specific styling
.attribution-first-touch {
  color: #007bff;
}

.attribution-last-touch {
  color: #28a745;
}

.attribution-linear {
  color: #6f42c1;
}

.attribution-time-decay {
  color: #fd7e14;
}

.attribution-position-based {
  color: #dc3545;
}

.attribution-custom {
  color: #20c997;
}
