import { <PERSON><PERSON><PERSON><PERSON>, NgIf, CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, AfterViewInit, ViewChild } from '@angular/core';

import { FullCalendarModule, FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions, DateSelectArg, EventClickArg, EventApi } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';
import { Draggable } from '@fullcalendar/interaction'; // for dateClick
import { CalendarService, MyCalendarResponse, CalendarData } from '../../../../core/services/calendar.service';
import { NewYearActivityService, NewYearActivity } from '../../../../core/services/new-year-activity.service';
import { LeaveService, Leave } from '../../../../core/services/leave.service';
import { AuthService } from '../../../../core/services/auth.service';
import { environment } from '../../../../../environments/environment';
import { forkJoin, of, Observable } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [
    NgStyle,
    NgIf,
    CommonModule,
    FullCalendarModule
  ],
  templateUrl: './calendar.component.html',
  styleUrl: './calendar.component.scss'
})
export class CalendarComponent implements OnInit, AfterViewInit {

  @ViewChild('externalEvents', {static: true}) externalEvents: ElementRef;
  @ViewChild('calendar') calendarComponent: FullCalendarComponent;

  calendarOptions: CalendarOptions = {
    plugins: [
      dayGridPlugin,
      timeGridPlugin,
      listPlugin,
      interactionPlugin
    ],
    headerToolbar: {
      left: 'customPrev,customToday,customNext',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
    },
    customButtons: {
      customPrev: {
        text: '‹',
        hint: 'Previous Month',
        click: () => {
          if (!this.isLoadingData && !this.loading) {
            console.log('📅 FullCalendar: Custom Previous button clicked');
            this.goToPrevMonth();
          }
        }
      },
      customNext: {
        text: '›',
        hint: 'Next Month',
        click: () => {
          if (!this.isLoadingData && !this.loading) {
            console.log('📅 FullCalendar: Custom Next button clicked');
            this.goToNextMonth();
          }
        }
      },
      customToday: {
        text: 'Today',
        hint: 'Go to Today',
        click: () => {
          if (!this.isLoadingData && !this.loading) {
            console.log('📅 FullCalendar: Custom Today button clicked');
            this.goToToday();
          }
        }
      }
    },
    initialView: 'dayGridMonth',
    initialDate: new Date(), // Set to current date to show current month
    events: [], // Will be populated from API
    weekends: true,
    editable: false, // Disable editing for employee calendar
    selectable: false, // Disable selection for employee calendar
    selectMirror: false,
    dayMaxEvents: true,
    height: 'auto',
    aspectRatio: 1.35,
    eventClick: this.handleEventClick.bind(this),
    eventsSet: this.handleEvents.bind(this),
    viewDidMount: this.handleViewDidMount.bind(this),
    // Enhanced navigation options
    navLinks: false, // Disable date/week links
    nowIndicator: true, // Show current time indicator
    fixedWeekCount: false, // Allow variable week count
    showNonCurrentDates: true, // Show dates from adjacent months
    // Business hours configuration
    businessHours: {
      daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
      startTime: '09:00',
      endTime: '18:00'
    },
    // Event rendering options
    eventDisplay: 'block',
    displayEventTime: false, // Hide time for all-day events
    eventOrder: 'start,-duration,allDay,title', // Sort events properly
    // Performance and navigation optimizations
    lazyFetching: false, // Disable lazy fetching since we handle it manually
    eventSourceFailure: (error: any) => {
      console.error('📅 CalendarComponent: Event source failure:', error);
      this.error = 'Failed to load calendar events';
    },
    // Loading state handling
    loading: (isLoading: boolean) => {
      console.log(`📅 CalendarComponent: FullCalendar loading state: ${isLoading}`);
      // Note: We handle loading states manually for better control
    },
    /* you can update a remote database when these fire:
    eventAdd:
    eventChange:
    eventRemove:
    */
  };
  currentEvents: EventApi[] = [];
  loading: boolean = false;
  error: string | null = null;
  currentCalendarData: CalendarData | null = null;
  employeeInfo: { id: string; code: string; name: string } | null = null;
  currentDisplayMonth: { year: number; month: number } | null = null;
  isLoadingData: boolean = false; // Prevent multiple simultaneous loads

  // New properties for holidays and leave data
  holidays: NewYearActivity[] = [];
  userLeaves: Leave[] = [];
  isLoadingHolidays: boolean = false;
  isLoadingLeaves: boolean = false;
  holidaysError: string | null = null;
  leavesError: string | null = null;

  // Navigation state to prevent automatic refreshes during navigation
  private isNavigating: boolean = false;

  // BizzCorp Calendar API events storage
  bizzCorpCalendarEvents: any[] = [];

  // Pending events to be applied when calendar is ready
  pendingEvents: any[] = [];

  // Simplified data properties - no caching mechanisms

  // Loading state for correct month display in UI
  loadingMonthInfo: { year: number; month: number; month_name: string } | null = null;

  constructor(
    private calendarService: CalendarService,
    private newYearActivityService: NewYearActivityService,
    private leaveService: LeaveService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    console.log('📅 CalendarComponent: Initializing...');
    console.log('🔍 DEBUG: Calendar component starting initialization');
    console.log('📅 CalendarComponent: FullCalendar plugins loaded:', {
      dayGrid: !!dayGridPlugin,
      timeGrid: !!timeGridPlugin,
      list: !!listPlugin,
      interaction: !!interactionPlugin
    });

    // Set initial display month to current month
    const now = new Date();
    this.currentDisplayMonth = {
      year: now.getFullYear(),
      month: now.getMonth() + 1
    };

    console.log(`📅 CalendarComponent: Initial month set to ${this.currentDisplayMonth.year}-${this.currentDisplayMonth.month.toString().padStart(2, '0')}`);
    console.log('🔍 DEBUG: Current user from auth service:', this.authService.currentUserValue?.email);

    // Note: Initial data loading will be handled by ngAfterViewInit when calendar is ready
    console.log('📅 CalendarComponent: Deferring initial data load until calendar is ready');

    // Test leave API directly on initialization
    this.testLeaveApiDirectly();

    // For external-events dragging (keep for backward compatibility)
    if (this.externalEvents?.nativeElement) {
      new Draggable(this.externalEvents.nativeElement, {
        itemSelector: '.fc-event',
        eventData: function(eventEl) {
          return {
            title: eventEl.innerText,
            backgroundColor: eventEl.getAttribute('bgColor'),
            borderColor: eventEl.getAttribute('bdColor')
          };
        }
      });
    }
  }

  /**
   * Test leave API directly to diagnose issues
   */
  private testLeaveApiDirectly(): void {
    console.log('🧪 CalendarComponent: Testing leave API directly...');

    // First check authentication status
    const currentUser = this.authService.currentUserValue;
    const isLoggedIn = this.authService.isLoggedIn();
    const tokenStatus = this.authService.getTokenStatus();

    console.log('🔐 Authentication Status:', {
      isLoggedIn,
      hasUser: !!currentUser,
      userEmail: currentUser?.email,
      userRole: currentUser?.role,
      tokenStatus
    });

    if (!isLoggedIn || !currentUser) {
      console.error('❌ User not authenticated - cannot test leave API');
      return;
    }

    // Test the leave service directly
    this.leaveService.getMyLeaves().subscribe({
      next: (response) => {
        console.log('✅ Direct leave API test - Success:', response);
        console.log('📊 Direct leave API test - Response type:', typeof response);
        console.log('📊 Direct leave API test - Is array:', Array.isArray(response));

        if (response && typeof response === 'object') {
          console.log('📊 Direct leave API test - Response structure:', {
            hasSuccess: 'success' in response,
            successValue: (response as any).success,
            hasData: 'data' in response,
            dataType: typeof (response as any).data,
            isDataArray: Array.isArray((response as any).data),
            dataLength: Array.isArray((response as any).data) ? (response as any).data.length : 'N/A'
          });

          // If it's a successful API response with data
          if ((response as any).success && Array.isArray((response as any).data)) {
            console.log(`📋 Direct leave API test - Found ${(response as any).data.length} leave records`);
            if ((response as any).data.length > 0) {
              console.log('📋 Direct leave API test - First leave record:', (response as any).data[0]);
            }
          }
        }
      },
      error: (error) => {
        console.error('❌ Direct leave API test - Error:', error);
        console.error('❌ Direct leave API test - Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url,
          error: error.error
        });
      }
    });
  }

  ngAfterViewInit(): void {
    console.log('📅 CalendarComponent: AfterViewInit - Calendar component reference:', !!this.calendarComponent);

    // Ensure calendar is properly initialized and load initial data for CURRENT MONTH
    setTimeout(() => {
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        console.log('📅 CalendarComponent: Calendar API initialized:', !!calendarApi);
        console.log('📅 CalendarComponent: Current view type:', calendarApi.view?.type);

        // Get current date (today) to ensure we show current month
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;

        console.log(`📅 CalendarComponent: Initializing calendar for CURRENT month: ${currentYear}-${currentMonth.toString().padStart(2, '0')}`);

        // Force calendar to show current month immediately
        const currentDate = new Date(currentYear, currentMonth - 1, 1);
        calendarApi.gotoDate(currentDate);
        calendarApi.changeView('dayGridMonth', currentDate);
        calendarApi.render();

        // Set current display month
        this.currentDisplayMonth = { year: currentYear, month: currentMonth };

        // IMMEDIATELY update the title using direct DOM manipulation
        this.updateCalendarTitleDirectly(currentYear, currentMonth);

        // Apply any pending events that were waiting for calendar to be ready
        if (this.pendingEvents.length > 0) {
          console.log(`🔄 CalendarComponent: Applying ${this.pendingEvents.length} pending events`);
          this.updateCalendarEvents(this.pendingEvents);
          this.pendingEvents = []; // Clear pending events
        }

        // Load initial data for the current month
        console.log(`🔄 CalendarComponent: Loading initial data for CURRENT month: ${currentYear}-${currentMonth.toString().padStart(2, '0')}`);
        this.loadCalendarData(currentYear, currentMonth);

        console.log(`✅ CalendarComponent: Calendar initialized and showing current month: ${this.getMonthName(currentMonth)} ${currentYear}`);
      } else {
        console.warn('⚠️ CalendarComponent: Calendar component not available after view init');
      }
    }, 200);
  }

  /**
   * SIMPLIFIED: Load calendar data with direct API-to-display flow
   * No caching, no complex logic - just clear, load, and display
   */
  loadCalendarData(year?: number, month?: number, forceRefresh: boolean = false): void {
    const targetYear = year !== undefined ? year : new Date().getFullYear();
    const targetMonth = month !== undefined ? month : (new Date().getMonth() + 1);

    console.log(`🚀 SIMPLIFIED: Loading calendar data for ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);
    console.log('🔍 DEBUG: Calendar service available:', !!this.calendarService);
    console.log('🔍 DEBUG: Auth service current user:', this.authService.currentUserValue?.email);

    // STEP 1: IMMEDIATELY clear all existing data and events
    this.clearAllCalendarData();

    // STEP 2: IMMEDIATELY update tracking and calendar view
    this.updateCalendarViewAndTracking(targetYear, targetMonth);

    // STEP 3: Set loading states
    this.setLoadingStates(true);

    // STEP 4: Make API call and display results directly
    this.makeDirectApiCall(targetYear, targetMonth);
  }

  /**
   * STEP 1: Clear all calendar data and events immediately
   */
  private clearAllCalendarData(): void {
    console.log(`🗑️ SIMPLIFIED: Clearing all calendar data and events`);

    // Clear all data arrays
    this.bizzCorpCalendarEvents = [];
    this.holidays = [];
    this.userLeaves = [];
    this.currentCalendarData = null;

    // Clear calendar display immediately
    this.updateCalendarEvents([]);

    console.log(`✅ SIMPLIFIED: All calendar data cleared`);
  }

  /**
   * STEP 2: Update calendar view and tracking immediately
   */
  private updateCalendarViewAndTracking(year: number, month: number): void {
    console.log(`📍 SIMPLIFIED: Updating calendar view and tracking to ${year}-${month}`);

    // Update tracking immediately
    this.currentDisplayMonth = { year, month };

    // Update calendar view if available
    if (this.calendarComponent) {
      try {
        const calendarApi = this.calendarComponent.getApi();
        const targetDate = new Date(year, month - 1, 1);

        // Clear existing events from calendar
        const existingEvents = calendarApi.getEvents();
        existingEvents.forEach(event => event.remove());

        // Update calendar view
        calendarApi.gotoDate(targetDate);
        calendarApi.changeView('dayGridMonth', targetDate);
        calendarApi.render();

        console.log(`📅 SIMPLIFIED: Calendar view updated to ${this.getMonthName(month)} ${year}`);
      } catch (error) {
        console.warn('⚠️ SIMPLIFIED: Could not update calendar view:', error);
      }
    }
  }

  /**
   * STEP 3: Set loading states
   */
  private setLoadingStates(loading: boolean): void {
    this.loading = loading;
    this.isLoadingData = loading;
    this.isLoadingHolidays = loading;
    this.isLoadingLeaves = loading;
    this.error = null;
    this.holidaysError = null;
    this.leavesError = null;

    if (loading) {
      console.log(`⏳ SIMPLIFIED: Loading states set to true`);
    } else {
      console.log(`✅ SIMPLIFIED: Loading states set to false`);
    }
  }

  /**
   * STEP 4: Make direct API call and display results
   */
  private makeDirectApiCall(year: number, month: number): void {
    console.log(`🌐 SIMPLIFIED: Making direct API call for ${year}-${month}`);
    console.log('🔍 DEBUG: About to call calendarService.getMyCalendar');

    this.calendarService.getMyCalendar(year, month).subscribe({
      next: (response) => {
        console.log(`📡 SIMPLIFIED: API response received for ${year}-${month}:`, response);
        console.log('🔍 DEBUG: Response success:', response?.success);
        console.log('🔍 DEBUG: Response data available:', !!response?.data);
        this.processApiResponseDirectly(response, year, month);
      },
      error: (error) => {
        console.error(`❌ SIMPLIFIED: API call failed for ${year}-${month}:`, error);
        console.error('🔍 DEBUG: Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url,
          error: error.error
        });
        this.handleApiError(error);
      }
    });
  }

  /**
   * Process API response and display events directly
   */
  private processApiResponseDirectly(response: any, year: number, month: number): void {
    console.log(`🔄 SIMPLIFIED: Processing API response for ${year}-${month}`);

    if (response.success && response.data && response.data.calendar_data) {
      // Convert API data to calendar events
      const events = this.calendarService.convertToFullCalendarEvents(response.data.calendar_data);

      console.log(`📊 SIMPLIFIED: Converted ${events.length} events for ${year}-${month}`);

      // Store events
      this.bizzCorpCalendarEvents = events;

      // Display events immediately
      this.updateCalendarEvents(events);

      console.log(`✅ SIMPLIFIED: Events displayed for ${year}-${month}`);
    } else {
      console.warn(`⚠️ SIMPLIFIED: Invalid API response for ${year}-${month}:`, response);
      this.updateCalendarEvents([]);
    }

    // Clear loading states
    this.setLoadingStates(false);
  }

  /**
   * Handle API errors
   */
  private handleApiError(error: any): void {
    console.error(`❌ SIMPLIFIED: API error:`, error);
    this.error = 'Failed to load calendar data. Please try again.';
    this.updateCalendarEvents([]);
    this.setLoadingStates(false);
  }

  // REMOVED: All complex caching and fallback logic for simplified direct API-to-display flow

  /**
   * Process calendar data from the calendar API
   */
  private processCalendarData(response: any, targetYear: number, targetMonth: number): void {
    if (response.success && response.data) {
      this.currentCalendarData = response.data.calendar_data;
      this.employeeInfo = {
        id: response.data.employee_id,
        code: response.data.employee_code,
        name: response.data.employee_name
      };

      console.log(`✅ Calendar data processed for ${this.employeeInfo.name} (${targetYear}-${targetMonth.toString().padStart(2, '0')})`);
      if (this.currentCalendarData) {
        this.validateCalendarData(this.currentCalendarData);
      }
    } else {
      console.error('❌ Invalid calendar API response:', response);
      this.error = 'Failed to load calendar data';
    }
  }

  /**
   * Process holidays data from the new year activities API with month filtering
   */
  private processHolidaysData(response: any, targetYear: number, targetMonth: number): void {
    console.log(`🔍 CalendarComponent: Processing holidays data for ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);

    if (response.success && response.data) {
      const allHolidays = Array.isArray(response.data) ? response.data : [];
      console.log(`📊 CalendarComponent: Total holidays available: ${allHolidays.length}`);

      // Log all available holidays for debugging
      console.log('🔍 CalendarComponent: All available holidays:', allHolidays.map((h: any) => ({
        id: h.id,
        name: h.holiday || h.activity_name,
        date: h.holiday_date || h.date,
        raw: h
      })));

      // Filter holidays for the target month and year
      this.holidays = allHolidays.filter((holiday: any) => {
        // Check multiple possible date fields
        const dateField = holiday.holiday_date || holiday.date;
        if (!dateField) {
          console.warn(`⚠️ Holiday missing date field:`, holiday);
          return false;
        }

        try {
          const holidayDate = new Date(dateField);

          // Validate the date
          if (isNaN(holidayDate.getTime())) {
            console.warn(`⚠️ Invalid holiday date: ${dateField}`, holiday);
            return false;
          }

          const holidayYear = holidayDate.getFullYear();
          const holidayMonth = holidayDate.getMonth() + 1; // JS months are 0-based

          const isInTargetMonth = holidayYear === targetYear && holidayMonth === targetMonth;

          console.log(`🔍 Holiday check: ${holiday.holiday || holiday.activity_name} on ${dateField} -> Year: ${holidayYear}, Month: ${holidayMonth}, Target: ${targetYear}-${targetMonth}, Match: ${isInTargetMonth}`);

          if (isInTargetMonth) {
            console.log(`✅ Holiday included: ${holiday.holiday || holiday.activity_name} on ${dateField}`);
          }

          return isInTargetMonth;
        } catch (error) {
          console.error(`❌ Error parsing holiday date: ${dateField}`, error, holiday);
          return false;
        }
      });

      console.log(`✅ Holidays data processed: ${this.holidays.length} holidays for ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);
      this.holidaysError = null;
    } else {
      console.error('❌ Invalid holidays API response:', response);
      this.holidays = [];
      this.holidaysError = 'Failed to load holidays';
    }
  }

  /**
   * Process leave data from the leave API with month filtering
   */
  private processLeaveData(response: any, targetYear: number, targetMonth: number): void {
    console.log(`🔍 CalendarComponent: Processing leave API response for ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);

    if (response.success && response.data) {
      const allLeaves = Array.isArray(response.data) ? response.data : [];
      console.log(`📊 CalendarComponent: Total leave records available: ${allLeaves.length}`);

      // Filter leaves that overlap with the target month
      this.userLeaves = allLeaves.filter((leave: any) => {
        if (!leave.start_date || !leave.end_date) {
          console.warn(`⚠️ Leave record ${leave.id} missing dates:`, leave);
          return false;
        }

        const leaveStart = new Date(leave.start_date);
        const leaveEnd = new Date(leave.end_date);

        // Create date range for the target month
        const monthStart = new Date(targetYear, targetMonth - 1, 1); // JS months are 0-based
        const monthEnd = new Date(targetYear, targetMonth, 0); // Last day of target month

        // Check if leave overlaps with target month
        const overlaps = leaveStart <= monthEnd && leaveEnd >= monthStart;

        if (overlaps) {
          console.log(`✅ Leave included: ${leave.leave_type} (${leave.status}) from ${leave.start_date} to ${leave.end_date}`);
        }

        return overlaps;
      });

      console.log(`✅ Leave data processed: ${this.userLeaves.length} leave records for ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);

      // Log detailed leave information for debugging
      if (this.userLeaves.length > 0) {
        console.log('📋 CalendarComponent: Filtered leave records details:');
        this.userLeaves.forEach((leave, index) => {
          console.log(`  ${index + 1}. ID: ${leave.id}, Type: ${leave.leave_type}, Status: ${leave.status}, Start: ${leave.start_date}, End: ${leave.end_date}`);
        });

        // Log status distribution
        const statusCounts = this.userLeaves.reduce((acc, leave) => {
          const status = leave.status?.toUpperCase() || 'UNKNOWN';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        console.log('📊 CalendarComponent: Leave status distribution for current month:', statusCounts);
      } else {
        console.log(`ℹ️ CalendarComponent: No leave records found for ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);
      }

      this.leavesError = null;
    } else {
      console.error('❌ Invalid leave API response:', response);
      console.log('🔍 Response structure check:', {
        hasSuccess: 'success' in response,
        successValue: response.success,
        hasData: 'data' in response,
        dataType: typeof response.data,
        isDataArray: Array.isArray(response.data)
      });
      this.userLeaves = [];
      this.leavesError = 'Failed to load leave records';
    }
  }

  /**
   * Combine calendar events, holidays, and leave records into FullCalendar events
   * Now prioritizes BizzCorp unified calendar events when available
   */
  private combineAndUpdateEvents(): void {
    console.log('🔄 CalendarComponent: Starting combineAndUpdateEvents...');
    console.log(`📊 CalendarComponent: Current data state - Holidays: ${this.holidays.length}, Leaves: ${this.userLeaves.length}`);

    // Check if we have BizzCorp unified calendar events
    if (this.bizzCorpCalendarEvents && this.bizzCorpCalendarEvents.length > 0) {
      console.log(`🌟 CalendarComponent: Using BizzCorp unified calendar events (${this.bizzCorpCalendarEvents.length} events)`);
      this.updateCalendarWithBizzCorpEvents();
      return;
    }

    // Fallback to legacy method for backward compatibility
    console.log('🔄 CalendarComponent: Using legacy event combination method');
    const allEvents: any[] = [];

    // Add calendar events (attendance data)
    if (this.currentCalendarData) {
      const calendarEvents = this.calendarService.convertToFullCalendarEvents(this.currentCalendarData);
      allEvents.push(...calendarEvents);
      console.log(`📅 CalendarComponent: Added ${calendarEvents.length} calendar events`);
    }

    // Add holiday events
    const holidayEvents = this.convertHolidaysToEvents();
    allEvents.push(...holidayEvents);
    console.log(`🎉 CalendarComponent: Added ${holidayEvents.length} holiday events`);

    // Add leave events
    const leaveEvents = this.convertLeavesToEvents();
    allEvents.push(...leaveEvents);
    console.log(`🏖️ CalendarComponent: Added ${leaveEvents.length} leave events`);

    console.log(`📊 CalendarComponent: Combined ${allEvents.length} total events (Calendar: ${this.currentCalendarData ? 'loaded' : 'none'}, Holidays: ${holidayEvents.length}, Leaves: ${leaveEvents.length})`);

    // Log event details for debugging
    if (holidayEvents.length > 0) {
      console.log('🎉 Holiday events details:', holidayEvents.map(e => ({ title: e.title, date: e.date })));
    }
    if (leaveEvents.length > 0) {
      console.log('🏖️ Leave events details:', leaveEvents.map(e => ({ title: e.title, start: e.start, end: e.end })));
    }

    // Update calendar with all events
    this.updateCalendarEvents(allEvents);

    // Force calendar refresh to ensure events are displayed
    this.forceCalendarRefresh();

    this.validateStatusAndPropertyDisplay();

    // Log comprehensive summary for debugging
    this.logCalendarDataSummary(allEvents, holidayEvents, leaveEvents);
  }

  handleDateSelect(_selectInfo: DateSelectArg) {
    // Disabled for employee calendar - employees can't create events
    console.log('📅 CalendarComponent: Date selection disabled for employee calendar');
  }

  /**
   * Convert holidays to FullCalendar events
   */
  private convertHolidaysToEvents(): any[] {
    console.log(`🔄 CalendarComponent: Converting ${this.holidays.length} holidays to calendar events...`);

    const events = this.holidays.map((holiday, index) => {
      const dateField = holiday.holiday_date || holiday.date;
      const title = holiday.holiday || holiday.activity_name || 'Holiday';

      console.log(`🎉 Converting holiday ${index + 1}: ${title} on ${dateField}`);

      if (!dateField) {
        console.warn(`⚠️ Holiday ${title} has no date field, skipping`);
        return null;
      }

      try {
        // Validate date
        const testDate = new Date(dateField);
        if (isNaN(testDate.getTime())) {
          console.warn(`⚠️ Holiday ${title} has invalid date: ${dateField}, skipping`);
          return null;
        }

        const event = {
          id: `holiday-${holiday.id || index}`,
          title: title,
          date: dateField,
          allDay: true,
          backgroundColor: '#dc3545', // Red background for holidays
          borderColor: '#dc3545',
          textColor: '#ffffff',
          classNames: ['holiday-event'],
          extendedProps: {
            type: 'holiday',
            description: holiday.description || 'Public Holiday',
            source: 'new_year_activity',
            originalData: holiday
          }
        };

        console.log(`✅ Holiday event created:`, event);
        return event;
      } catch (error) {
        console.error(`❌ Error creating holiday event for ${title}:`, error);
        return null;
      }
    }).filter(event => event !== null); // Remove null events

    console.log(`✅ CalendarComponent: Created ${events.length} holiday events from ${this.holidays.length} holidays`);
    return events;
  }

  /**
   * Convert leave records to FullCalendar events
   */
  private convertLeavesToEvents(): any[] {
    console.log(`🔄 CalendarComponent: Converting ${this.userLeaves.length} leave records to calendar events...`);
    const events: any[] = [];
    const skippedLeaves: any[] = [];

    this.userLeaves.forEach((leave, index) => {
      console.log(`🔍 Processing leave ${index + 1}:`, {
        id: leave.id,
        type: leave.leave_type,
        status: leave.status,
        start_date: leave.start_date,
        end_date: leave.end_date,
        hasStartDate: !!leave.start_date,
        hasEndDate: !!leave.end_date
      });

      if (leave.start_date && leave.end_date) {
        // Create event for leave period
        const event = {
          id: `leave-${leave.id}`,
          title: `${leave.leave_type || 'Leave'} - ${leave.status || 'Pending'}`,
          start: leave.start_date,
          end: this.addDayToDate(leave.end_date), // FullCalendar end date is exclusive
          allDay: true,
          backgroundColor: this.getLeaveColor(leave.status),
          borderColor: this.getLeaveColor(leave.status),
          textColor: '#ffffff',
          classNames: ['leave-event'],
          extendedProps: {
            type: 'leave',
            leaveType: leave.leave_type,
            status: leave.status,
            reason: leave.reason,
            appliedDate: leave.created_at,
            approvedBy: leave.approved_by,
            source: 'leave_api',
            'data-status': leave.status?.toUpperCase() || 'UNKNOWN'
          }
        };

        events.push(event);
        console.log(`✅ Created calendar event for leave ${leave.id}:`, event);
      } else {
        skippedLeaves.push({
          id: leave.id,
          reason: !leave.start_date ? 'Missing start_date' : 'Missing end_date',
          leave: leave
        });
        console.warn(`⚠️ Skipped leave ${leave.id}: Missing ${!leave.start_date ? 'start_date' : 'end_date'}`);
      }
    });

    console.log(`📊 CalendarComponent: Leave conversion summary:`);
    console.log(`  - Total leaves processed: ${this.userLeaves.length}`);
    console.log(`  - Events created: ${events.length}`);
    console.log(`  - Leaves skipped: ${skippedLeaves.length}`);

    if (skippedLeaves.length > 0) {
      console.log('⚠️ Skipped leaves details:', skippedLeaves);
    }

    // Log events by status for verification
    const eventsByStatus = events.reduce((acc, event) => {
      const status = event.extendedProps.status?.toUpperCase() || 'UNKNOWN';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    console.log('📊 Calendar events by status:', eventsByStatus);

    return events;
  }

  /**
   * Get color for leave events based on status
   * Supports ALL possible leave statuses with proper color coding
   */
  private getLeaveColor(status: string): string {
    const normalizedStatus = status?.toUpperCase() || 'UNKNOWN';

    switch (normalizedStatus) {
      case 'APPROVED':
        return '#28a745'; // Green - Approved leaves
      case 'PENDING':
        return '#ffc107'; // Yellow - Pending leaves
      case 'REJECTED':
      case 'DECLINED': // Handle both REJECTED and DECLINED statuses
        return '#dc3545'; // Red - Rejected/Declined leaves
      case 'CANCELLED':
        return '#6c757d'; // Gray - Cancelled leaves
      case 'UNKNOWN':
      case '':
      case null:
      case undefined:
        console.warn(`⚠️ CalendarComponent: Unknown leave status encountered: "${status}"`);
        return '#17a2b8'; // Teal - Unknown status for visibility
      default:
        console.warn(`⚠️ CalendarComponent: Unhandled leave status: "${status}"`);
        return '#007bff'; // Blue - Default for any other status
    }
  }

  /**
   * Add one day to a date string (for FullCalendar end date)
   */
  private addDayToDate(dateString: string): string {
    const date = new Date(dateString);
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get count of leaves by status for the legend display
   */
  getLeaveCountByStatus(status: string): number {
    if (!this.userLeaves || this.userLeaves.length === 0) {
      return 0;
    }

    const normalizedStatus = status.toUpperCase();
    return this.userLeaves.filter(leave => {
      const leaveStatus = leave.status?.toUpperCase() || '';
      // Handle both REJECTED and DECLINED as rejected status
      if (normalizedStatus === 'REJECTED') {
        return leaveStatus === 'REJECTED' || leaveStatus === 'DECLINED';
      }
      return leaveStatus === normalizedStatus;
    }).length;
  }

  /**
   * Log comprehensive summary of calendar data for debugging
   */
  private logCalendarDataSummary(allEvents: any[], holidayEvents: any[], leaveEvents: any[]): void {
    console.log('📊 ===== CALENDAR DATA SUMMARY =====');
    console.log(`📅 Total Events on Calendar: ${allEvents.length}`);
    console.log(`🎉 Holiday Events: ${holidayEvents.length}`);
    console.log(`🏖️ Leave Events: ${leaveEvents.length}`);
    console.log(`📋 Calendar/Attendance Events: ${allEvents.length - holidayEvents.length - leaveEvents.length}`);

    // Leave status breakdown
    console.log('📊 Leave Status Breakdown:');
    console.log(`  ✅ Approved: ${this.getLeaveCountByStatus('APPROVED')}`);
    console.log(`  ⏳ Pending: ${this.getLeaveCountByStatus('PENDING')}`);
    console.log(`  ❌ Rejected: ${this.getLeaveCountByStatus('REJECTED')}`);
    console.log(`  🚫 Cancelled: ${this.getLeaveCountByStatus('CANCELLED')}`);

    // Data source status
    console.log('📡 Data Source Status:');
    console.log(`  🎉 Holidays: ${this.holidays.length} loaded, Error: ${this.holidaysError || 'None'}`);
    console.log(`  🏖️ Leaves: ${this.userLeaves.length} loaded, Error: ${this.leavesError || 'None'}`);
    console.log(`  📅 Calendar: ${this.currentCalendarData ? 'Loaded' : 'Not loaded'}, Error: ${this.error || 'None'}`);

    // Event details for debugging
    if (leaveEvents.length > 0) {
      console.log('🔍 Leave Events Details:');
      leaveEvents.forEach((event, index) => {
        console.log(`  ${index + 1}. ${event.title} (${event.start} to ${event.end}) - Status: ${event.extendedProps.status}`);
      });
    } else {
      console.warn('⚠️ No leave events found! This might indicate an issue with leave data processing.');
    }

    if (holidayEvents.length > 0) {
      console.log('🔍 Holiday Events Details:');
      holidayEvents.forEach((event, index) => {
        console.log(`  ${index + 1}. ${event.title} (${event.date})`);
      });
    }

    console.log('📊 ===== END CALENDAR SUMMARY =====');
  }

  handleEventClick(clickInfo: EventClickArg) {
    console.log('📅 CalendarComponent: Event clicked:', clickInfo.event);
    console.log('📋 CalendarComponent: Event extended properties:', clickInfo.event.extendedProps);

    const event = clickInfo.event;
    const extendedProps = event.extendedProps;

    // Handle different event types - check both 'type' and 'event_type' for compatibility
    const eventType = extendedProps.type || extendedProps.event_type;

    if (eventType === 'holiday') {
      this.showHolidayDetails(event, extendedProps);
    } else if (eventType === 'leave') {
      this.showLeaveDetails(event, extendedProps);
    } else {
      this.showCalendarEventDetails(event, extendedProps);
    }
  }

  /**
   * Show holiday event details
   */
  private showHolidayDetails(event: any, extendedProps: any): void {
    let eventDetails = `
      <div class="event-details holiday-details">
        <h5>🎉 ${event.title}</h5>
        <p><strong>Date:</strong> ${event.startStr}</p>
        <p><strong>Type:</strong> Public Holiday</p>
    `;

    if (extendedProps.description) {
      eventDetails += `<p><strong>Description:</strong> ${extendedProps.description}</p>`;
    }

    // Show holiday status if available
    if (extendedProps.status) {
      eventDetails += `<p><strong>Status:</strong> ${extendedProps.status}</p>`;
    }

    // Show if it's optional holiday
    if (extendedProps.is_optional !== null && extendedProps.is_optional !== undefined) {
      eventDetails += `<p><strong>Optional Holiday:</strong> ${extendedProps.is_optional ? 'Yes' : 'No'}</p>`;
    }

    // Show region code if available
    if (extendedProps.region_code) {
      eventDetails += `<p><strong>Region:</strong> ${extendedProps.region_code}</p>`;
    }

    eventDetails += `<p><strong>Source:</strong> BizzCorp Calendar</p>`;
    eventDetails += '</div>';

    alert(eventDetails.replace(/<[^>]*>/g, '\n').replace(/\n+/g, '\n').trim());
  }

  /**
   * Show leave event details
   */
  private showLeaveDetails(event: any, extendedProps: any): void {
    let eventDetails = `
      <div class="event-details leave-details">
        <h5>🏖️ ${event.title}</h5>
        <p><strong>Period:</strong> ${event.startStr} to ${event.endStr || event.startStr}</p>
        <p><strong>Type:</strong> ${extendedProps.leaveType || 'Leave'}</p>
        <p><strong>Status:</strong> ${extendedProps.status || 'Unknown'}</p>
    `;

    if (extendedProps.reason) {
      eventDetails += `<p><strong>Reason:</strong> ${extendedProps.reason}</p>`;
    }

    if (extendedProps.appliedDate) {
      eventDetails += `<p><strong>Applied Date:</strong> ${extendedProps.appliedDate}</p>`;
    }

    if (extendedProps.approvedBy) {
      eventDetails += `<p><strong>Approved By:</strong> ${extendedProps.approvedBy}</p>`;
    }

    eventDetails += `<p><strong>Source:</strong> Leave Management System</p>`;
    eventDetails += '</div>';

    alert(eventDetails.replace(/<[^>]*>/g, '\n').replace(/\n+/g, '\n').trim());
  }

  /**
   * Show calendar/attendance event details (original functionality)
   */
  private showCalendarEventDetails(event: any, extendedProps: any): void {
    let eventDetails = `
      <div class="event-details">
        <h5>${event.title}</h5>
        <p><strong>Date:</strong> ${event.startStr}</p>
        <p><strong>Day of Week:</strong> ${this.getDayName(extendedProps.day_of_week)}</p>
        <p><strong>Event Type:</strong> ${extendedProps.event_type}</p>
        <p><strong>Status:</strong> ${extendedProps.status}</p>
        <p><strong>Primary Status:</strong> ${extendedProps.primary_status}</p>
    `;

    // Day-level information
    if (extendedProps.is_weekend) {
      eventDetails += `<p><strong>Weekend:</strong> Yes</p>`;
    }

    if (extendedProps.has_attendance) {
      eventDetails += `<p><strong>Has Attendance:</strong> Yes</p>`;
    }

    if (extendedProps.has_leave) {
      eventDetails += `<p><strong>Has Leave:</strong> Yes</p>`;
    }

    if (extendedProps.has_holiday) {
      eventDetails += `<p><strong>Has Holiday:</strong> Yes</p>`;
    }

    // Event description
    if (extendedProps.description) {
      eventDetails += `<p><strong>Description:</strong> ${extendedProps.description}</p>`;
    }

    // Attendance-related information
    if (extendedProps.check_in_time) {
      eventDetails += `<p><strong>Check In:</strong> ${extendedProps.check_in_time}</p>`;
    }

    if (extendedProps.check_out_time) {
      eventDetails += `<p><strong>Check Out:</strong> ${extendedProps.check_out_time}</p>`;
    }

    if (extendedProps.working_hours) {
      eventDetails += `<p><strong>Working Hours:</strong> ${extendedProps.working_hours} hours</p>`;
    }

    if (extendedProps.overtime_hours) {
      eventDetails += `<p><strong>Overtime Hours:</strong> ${extendedProps.overtime_hours} hours</p>`;
    }

    // Leave-related information
    if (extendedProps.leave_type) {
      eventDetails += `<p><strong>Leave Type:</strong> ${extendedProps.leave_type}</p>`;
    }

    if (extendedProps.leave_days) {
      eventDetails += `<p><strong>Leave Days:</strong> ${extendedProps.leave_days}</p>`;
    }

    if (extendedProps.leave_reason) {
      eventDetails += `<p><strong>Leave Reason:</strong> ${extendedProps.leave_reason}</p>`;
    }

    // Holiday-related information
    if (extendedProps.is_optional !== null && extendedProps.is_optional !== undefined) {
      eventDetails += `<p><strong>Optional Holiday:</strong> ${extendedProps.is_optional ? 'Yes' : 'No'}</p>`;
    }

    if (extendedProps.region_code) {
      eventDetails += `<p><strong>Region Code:</strong> ${extendedProps.region_code}</p>`;
    }

    eventDetails += '</div>';

    alert(eventDetails.replace(/<[^>]*>/g, '\n').replace(/\n+/g, '\n').trim());
  }

  /**
   * Helper method to get day name from day number
   */
  private getDayName(dayOfWeek: number): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek] || 'Unknown';
  }

  /**
   * Validate calendar data completeness and log comprehensive information
   */
  private validateCalendarData(calendarData: CalendarData): void {
    console.log('🔍 CalendarComponent: Validating calendar data completeness...');

    // Basic structure validation
    console.log(`📅 Calendar Data: ${calendarData.month_name} ${calendarData.year} (Month ${calendarData.month})`);
    console.log(`📊 Total days: ${calendarData.days.length}`);

    // Analyze day-level data
    let weekendCount = 0;
    let weekdayCount = 0;
    let statusCounts: { [key: string]: number } = {};
    let eventTypeCounts: { [key: string]: number } = {};
    let daysWithAttendance = 0;
    let daysWithLeave = 0;
    let daysWithHoliday = 0;

    calendarData.days.forEach(day => {
      // Count weekends vs weekdays
      if (day.is_weekend) {
        weekendCount++;
      } else {
        weekdayCount++;
      }

      // Count primary statuses
      statusCounts[day.primary_status] = (statusCounts[day.primary_status] || 0) + 1;

      // Count day properties
      if (day.has_attendance) daysWithAttendance++;
      if (day.has_leave) daysWithLeave++;
      if (day.has_holiday) daysWithHoliday++;

      // Count event types
      day.events.forEach(event => {
        eventTypeCounts[event.event_type] = (eventTypeCounts[event.event_type] || 0) + 1;
      });
    });

    // Log comprehensive statistics
    console.log('📊 Calendar Data Analysis:');
    console.log(`   Weekends: ${weekendCount}, Weekdays: ${weekdayCount}`);
    console.log(`   Days with attendance: ${daysWithAttendance}`);
    console.log(`   Days with leave: ${daysWithLeave}`);
    console.log(`   Days with holiday: ${daysWithHoliday}`);
    console.log(`   Primary status distribution:`, statusCounts);
    console.log(`   Event type distribution:`, eventTypeCounts);

    // Validate data integrity
    const totalEvents = Object.values(eventTypeCounts).reduce((sum, count) => sum + count, 0);
    console.log(`   Total events: ${totalEvents}`);

    // Check for potential data issues
    if (calendarData.days.length === 0) {
      console.warn('⚠️ No days found in calendar data');
    }

    if (totalEvents === 0) {
      console.warn('⚠️ No events found in calendar data');
    }

    // Validate weekend detection
    const expectedWeekends = this.calculateExpectedWeekends(calendarData.year, calendarData.month);
    if (weekendCount !== expectedWeekends) {
      console.warn(`⚠️ Weekend count mismatch: found ${weekendCount}, expected ${expectedWeekends}`);
    }

    console.log('✅ Calendar data validation complete');
  }

  /**
   * Calculate expected number of weekends in a month
   */
  private calculateExpectedWeekends(year: number, month: number): number {
    const daysInMonth = new Date(year, month, 0).getDate();
    let weekendCount = 0;

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday or Saturday
        weekendCount++;
      }
    }

    return weekendCount;
  }

  /**
   * Test navigation across multiple months to verify data integrity
   */
  testComprehensiveNavigation(): void {
    console.log('🧪 CalendarComponent: Starting comprehensive navigation test...');

    const currentYear = new Date().getFullYear();
    const testMonths = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]; // All 12 months
    let testIndex = 0;

    const testNextMonth = () => {
      if (testIndex >= testMonths.length) {
        console.log('✅ Comprehensive navigation test completed successfully!');
        return;
      }

      const month = testMonths[testIndex];
      console.log(`🧪 Testing month ${month} (${this.getMonthName(month)}) of ${currentYear}...`);

      // Load data for the test month
      this.loadCalendarData(currentYear, month);

      // Schedule next test after a delay to allow API call to complete
      testIndex++;
      setTimeout(testNextMonth, 2000); // 2 second delay between tests
    };

    // Start the test sequence
    testNextMonth();
  }

  /**
   * Helper method to get month name from month number
   */
  getMonthName(month: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1] || 'Unknown';
  }

  /**
   * Validate that all status and property displays are working correctly
   */
  validateStatusAndPropertyDisplay(): void {
    console.log('🔍 CalendarComponent: Validating status and property display...');

    if (!this.currentCalendarData) {
      console.warn('⚠️ No calendar data available for validation');
      return;
    }

    const validationResults = {
      totalDays: this.currentCalendarData.days.length,
      statusValidation: this.validatePrimaryStatuses(),
      propertyValidation: this.validateDayProperties(),
      weekendValidation: this.validateWeekendDetection(),
      eventValidation: this.validateEventDisplay()
    };

    console.log('📊 Status and Property Validation Results:', validationResults);

    // Check for any validation failures
    const hasIssues = Object.values(validationResults).some(result =>
      typeof result === 'object' && result.hasOwnProperty('issues') && result.issues > 0
    );

    if (hasIssues) {
      console.warn('⚠️ Some validation issues found - check the detailed results above');
    } else {
      console.log('✅ All status and property validations passed successfully!');
    }
  }

  /**
   * Validate primary status consistency
   */
  private validatePrimaryStatuses(): any {
    const statusCounts: { [key: string]: number } = {};
    let issues = 0;

    this.currentCalendarData!.days.forEach(day => {
      statusCounts[day.primary_status] = (statusCounts[day.primary_status] || 0) + 1;

      // Validate status consistency with day properties
      if (day.primary_status === 'holiday' && !day.has_holiday) {
        console.warn(`⚠️ Status inconsistency on ${day.date}: primary_status is 'holiday' but has_holiday is false`);
        issues++;
      }

      if (day.primary_status === 'leave' && !day.has_leave) {
        console.warn(`⚠️ Status inconsistency on ${day.date}: primary_status is 'leave' but has_leave is false`);
        issues++;
      }

      if (day.primary_status === 'weekend' && !day.is_weekend) {
        console.warn(`⚠️ Status inconsistency on ${day.date}: primary_status is 'weekend' but is_weekend is false`);
        issues++;
      }
    });

    return { statusCounts, issues };
  }

  /**
   * Validate day properties (has_attendance, has_leave, has_holiday)
   */
  private validateDayProperties(): any {
    let attendanceDays = 0;
    let leaveDays = 0;
    let holidayDays = 0;
    let issues = 0;

    this.currentCalendarData!.days.forEach(day => {
      if (day.has_attendance) attendanceDays++;
      if (day.has_leave) leaveDays++;
      if (day.has_holiday) holidayDays++;

      // Validate that properties match events
      const hasAttendanceEvent = day.events.some(event => event.event_type === 'attendance');
      const hasLeaveEvent = day.events.some(event => event.event_type === 'leave');
      const hasHolidayEvent = day.events.some(event => event.event_type === 'holiday');

      if (day.has_attendance && !hasAttendanceEvent) {
        console.warn(`⚠️ Property mismatch on ${day.date}: has_attendance is true but no attendance event found`);
        issues++;
      }

      if (day.has_leave && !hasLeaveEvent) {
        console.warn(`⚠️ Property mismatch on ${day.date}: has_leave is true but no leave event found`);
        issues++;
      }

      if (day.has_holiday && !hasHolidayEvent) {
        console.warn(`⚠️ Property mismatch on ${day.date}: has_holiday is true but no holiday event found`);
        issues++;
      }
    });

    return { attendanceDays, leaveDays, holidayDays, issues };
  }

  /**
   * Validate weekend detection accuracy
   */
  private validateWeekendDetection(): any {
    let weekendCount = 0;
    let issues = 0;

    this.currentCalendarData!.days.forEach(day => {
      const date = new Date(day.date);
      const dayOfWeek = date.getDay();
      const isActualWeekend = dayOfWeek === 0 || dayOfWeek === 6; // Sunday or Saturday

      if (day.is_weekend) weekendCount++;

      if (day.is_weekend !== isActualWeekend) {
        console.warn(`⚠️ Weekend detection error on ${day.date}: marked as ${day.is_weekend ? 'weekend' : 'weekday'} but actual day is ${isActualWeekend ? 'weekend' : 'weekday'}`);
        issues++;
      }
    });

    const expectedWeekends = this.calculateExpectedWeekends(
      this.currentCalendarData!.year,
      this.currentCalendarData!.month
    );

    return { weekendCount, expectedWeekends, issues };
  }

  /**
   * Validate event display properties
   */
  private validateEventDisplay(): any {
    let totalEvents = 0;
    let eventsWithColors = 0;
    let eventsWithTitles = 0;
    let issues = 0;

    this.currentCalendarData!.days.forEach(day => {
      day.events.forEach(event => {
        totalEvents++;

        if (event.color) eventsWithColors++;
        if (event.title) eventsWithTitles++;

        // Validate required properties
        if (!event.color) {
          console.warn(`⚠️ Event missing color on ${day.date}: ${event.title || 'Untitled'}`);
          issues++;
        }

        if (!event.title) {
          console.warn(`⚠️ Event missing title on ${day.date}: ${event.event_type}`);
          issues++;
        }

        if (!event.event_type) {
          console.warn(`⚠️ Event missing type on ${day.date}: ${event.title || 'Untitled'}`);
          issues++;
        }
      });
    });

    return { totalEvents, eventsWithColors, eventsWithTitles, issues };
  }

  handleEvents(events: EventApi[]) {
    this.currentEvents = events;
  }

  handleViewDidMount(info: any) {
    console.log('📅 CalendarComponent: View mounted:', info.view.type, info.view.title);

    try {
      // Extract year and month from the view
      const viewDate = info.view.currentStart;
      const year = viewDate.getFullYear();
      const month = viewDate.getMonth() + 1;

      console.log(`📅 CalendarComponent: View mounted for ${year}-${month.toString().padStart(2, '0')}`);

      // Check if we're currently navigating - if so, skip automatic loading
      if (this.isNavigating) {
        console.log(`🚫 CalendarComponent: Navigation in progress - skipping automatic data loading for view mount`);
        return;
      }

      // DISABLE automatic data loading during navigation to prevent override
      // Only sync tracking if this is the initial load
      if (!this.currentDisplayMonth) {
        console.log(`📅 CalendarComponent: Initial view mount - setting display month to ${year}-${month.toString().padStart(2, '0')}`);
        this.currentDisplayMonth = { year, month };

        // Always load fresh data for initial view
        console.log(`� CalendarComponent: Loading fresh initial data for ${year}-${month.toString().padStart(2, '0')}`);
        this.loadCalendarData(year, month);
      } else {
        console.log(`📅 CalendarComponent: View mount during navigation - maintaining current display month: ${this.currentDisplayMonth.year}-${this.currentDisplayMonth.month}`);
        // Do NOT override currentDisplayMonth or load data during navigation
      }
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Error handling view mount:', error);
    }
  }



  /**
   * Navigate to previous month - SMOOTH (no full refresh)
   */
  goToPrevMonth(): void {
    console.log('🔄 SMOOTH: goToPrevMonth()');

    // Calculate previous month
    const currentDate = new Date();
    let targetYear = this.currentDisplayMonth?.year || currentDate.getFullYear();
    let targetMonth = this.currentDisplayMonth?.month || (currentDate.getMonth() + 1);

    targetMonth--;
    if (targetMonth < 1) {
      targetMonth = 12;
      targetYear--;
    }

    console.log(`📅 SMOOTH: Navigating to previous month: ${targetYear}-${targetMonth}`);

    // Use smooth navigation without full refresh
    this.navigateToMonthSmooth(targetYear, targetMonth, 'previous');
  }

  /**
   * Navigate to next month - SMOOTH (no full refresh)
   */
  goToNextMonth(): void {
    console.log('🔄 SMOOTH: goToNextMonth()');

    // Calculate next month
    const currentDate = new Date();
    let targetYear = this.currentDisplayMonth?.year || currentDate.getFullYear();
    let targetMonth = this.currentDisplayMonth?.month || (currentDate.getMonth() + 1);

    targetMonth++;
    if (targetMonth > 12) {
      targetMonth = 1;
      targetYear++;
    }

    console.log(`📅 SMOOTH: Navigating to next month: ${targetYear}-${targetMonth}`);

    // Use smooth navigation without full refresh
    this.navigateToMonthSmooth(targetYear, targetMonth, 'next');
  }

  /**
   * Smooth navigation to a specific month without full refresh
   */
  private navigateToMonthSmooth(targetYear: number, targetMonth: number, direction: string): void {
    console.log(`🚀 SMOOTH: Navigating to ${targetYear}-${targetMonth.toString().padStart(2, '0')} (${direction})`);

    if (!this.isCalendarReady()) {
      console.error('❌ SMOOTH: Calendar not ready for navigation');
      return;
    }

    // Set loading state for navigation
    this.isLoadingData = true;

    try {
      const calendarApi = this.calendarComponent!.getApi();

      // Smoothly navigate calendar view to target month
      const targetDate = new Date(targetYear, targetMonth - 1, 1);
      console.log(`📅 SMOOTH: Moving calendar view to ${targetDate.toDateString()}`);

      // Use FullCalendar's smooth navigation
      calendarApi.gotoDate(targetDate);

      // Update tracking
      this.currentDisplayMonth = { year: targetYear, month: targetMonth };

      // Load data for new month without clearing existing events immediately
      this.loadMonthDataSmooth(targetYear, targetMonth);

    } catch (error) {
      console.error('❌ SMOOTH: Navigation failed:', error);
      this.isLoadingData = false;
    }
  }

  /**
   * Load month data smoothly without clearing all events first
   */
  private loadMonthDataSmooth(year: number, month: number): void {
    console.log(`📡 SMOOTH: Loading data for ${year}-${month.toString().padStart(2, '0')}`);

    this.calendarService.getMyCalendar(year, month).subscribe({
      next: (response) => {
        console.log(`✅ SMOOTH: Data received for ${year}-${month}`);

        if (response.success && response.data?.calendar_data) {
          // Convert API data to events
          const newEvents = this.calendarService.convertToFullCalendarEvents(response.data.calendar_data);
          console.log(`📊 SMOOTH: Converted ${newEvents.length} events`);

          // Smoothly replace events (clear old, add new)
          this.replaceCalendarEventsSmooth(newEvents);

          // Update title
          this.updateCalendarTitleDirectly(year, month);

        } else {
          console.warn(`⚠️ SMOOTH: Invalid response for ${year}-${month}`);
          this.replaceCalendarEventsSmooth([]);
        }

        this.isLoadingData = false;
      },
      error: (error) => {
        console.error(`❌ SMOOTH: Failed to load data for ${year}-${month}:`, error);
        this.isLoadingData = false;
      }
    });
  }

  /**
   * Replace calendar events smoothly without full refresh
   */
  private replaceCalendarEventsSmooth(newEvents: any[]): void {
    if (!this.isCalendarReady()) {
      console.error('❌ SMOOTH: Cannot replace events - calendar not ready');
      return;
    }

    try {
      const calendarApi = this.calendarComponent!.getApi();

      // Remove existing events
      const existingEvents = calendarApi.getEvents();
      existingEvents.forEach(event => event.remove());

      // Add new events
      newEvents.forEach(event => {
        calendarApi.addEvent(event);
      });

      console.log(`✅ SMOOTH: Replaced with ${newEvents.length} events`);

    } catch (error) {
      console.error('❌ SMOOTH: Failed to replace events:', error);
    }
  }

  /**
   * Navigate to today - SIMPLIFIED
   */
  goToToday(): void {
    console.log('🔄 SMOOTH: goToToday()');

    // Get current date
    const currentDate = new Date();
    const targetYear = currentDate.getFullYear();
    const targetMonth = currentDate.getMonth() + 1;

    console.log(`📅 SMOOTH: Navigating to today: ${targetYear}-${targetMonth}`);

    // Use smooth navigation
    this.navigateToMonthSmooth(targetYear, targetMonth, 'today');
  }



  /**
   * Force reload calendar data for current month (clears cache)
   */
  forceReloadCurrentMonth(): void {
    // Force fresh data fetch without cache
    console.log('🔄 CalendarComponent: Force reloading with fresh data');

    if (this.currentDisplayMonth) {
      console.log(`🔄 CalendarComponent: Force reloading data for ${this.currentDisplayMonth.year}-${this.currentDisplayMonth.month.toString().padStart(2, '0')}`);
      this.loadCalendarData(this.currentDisplayMonth.year, this.currentDisplayMonth.month);
    } else if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      const currentDate = calendarApi.getDate();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      console.log(`🔄 CalendarComponent: Force reloading data for current view: ${year}-${month.toString().padStart(2, '0')}`);
      this.loadCalendarData(year, month);
    } else {
      // Use current date as fallback but be explicit about it
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      console.log(`🔄 CalendarComponent: Force reloading data for current date: ${year}-${month}`);
      this.loadCalendarData(year, month);
    }
  }

  /**
   * Test navigation functionality - for debugging
   */
  testNavigation(): void {
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      const currentDate = calendarApi.getDate();

      console.log('🧪 CalendarComponent: Testing navigation functionality');
      console.log('   Current calendar date:', currentDate);
      console.log('   Current view type:', calendarApi.view?.type);
      console.log('   Current view title:', calendarApi.view?.title);
      console.log('   Current display month:', this.currentDisplayMonth);
      console.log('   Current calendar data month:', this.currentCalendarData?.year, this.currentCalendarData?.month);

      // Test going to next month
      console.log('🧪 Testing next month navigation...');
      calendarApi.next();
    } else {
      console.warn('⚠️ CalendarComponent: Calendar component not available for testing');
    }
  }

  /**
   * Update calendar events with proper error handling and fallbacks
   */
  private updateCalendarEvents(events: any[]): void {
    console.log(`🔄 CalendarComponent: Updating calendar with ${events.length} events`);

    // Debug: Log first few events to see their structure
    if (events.length > 0) {
      console.log('🔍 CalendarComponent: Sample events to add:', events.slice(0, 3).map(e => ({
        id: e.id,
        title: e.title,
        start: e.start,
        backgroundColor: e.backgroundColor,
        textColor: e.textColor
      })));
    }

    // Check if calendar component is ready
    if (!this.isCalendarReady()) {
      console.warn('⚠️ CalendarComponent: Calendar not ready yet, deferring event update...');
      // Store events to be applied when calendar is ready
      this.pendingEvents = events;
      // Try again after a short delay
      setTimeout(() => {
        if (this.isCalendarReady()) {
          console.log('🔄 CalendarComponent: Calendar now ready, applying deferred events');
          this.updateCalendarEvents(events);
        } else {
          console.error('❌ CalendarComponent: Calendar still not ready after delay');
        }
      }, 200); // Increased delay for better reliability
      return;
    }

    // Method 1: Try using calendar API if available
    if (this.calendarComponent) {
      try {
        const calendarApi = this.calendarComponent.getApi();

        if (!calendarApi) {
          console.error('❌ CalendarComponent: Calendar API not available - cannot update events');
          return;
        }

        console.log('📅 CalendarComponent: Removing existing events...');

        // Remove all existing events
        const existingEvents = calendarApi.getEvents();
        existingEvents.forEach(event => event.remove());

        console.log(`📅 CalendarComponent: Removed ${existingEvents.length} existing events`);

        console.log('📅 CalendarComponent: Adding new events...');
        let addedCount = 0;

        events.forEach((event, index) => {
          try {
            const eventToAdd = {
              ...event,
              id: event.id || `event-${index}-${Date.now()}` // Ensure unique ID
            };

            console.log(`📅 CalendarComponent: Adding event ${index + 1}/${events.length}:`, {
              id: eventToAdd.id,
              title: eventToAdd.title,
              start: eventToAdd.start,
              backgroundColor: eventToAdd.backgroundColor
            });

            calendarApi.addEvent(eventToAdd);
            addedCount++;
            console.log(`✅ CalendarComponent: Successfully added event ${index + 1}`);
          } catch (eventError) {
            console.warn(`⚠️ CalendarComponent: Failed to add event ${index}:`, eventError, event);
          }
        });

        console.log(`✅ CalendarComponent: Successfully added ${addedCount}/${events.length} events via API`);

        // Verify events were actually added
        const finalEventCount = calendarApi.getEvents().length;
        console.log(`📊 CalendarComponent: Calendar now has ${finalEventCount} total events`);

        if (finalEventCount === 0 && events.length > 0) {
          console.error('❌ CalendarComponent: No events visible after adding - possible date format issue');
          // Log current calendar view date
          const currentDate = calendarApi.getDate();
          console.log('📅 CalendarComponent: Current calendar view date:', currentDate);
        }

        // Force calendar to re-render after updating events
        setTimeout(() => {
          calendarApi.render();
          console.log('🔄 CalendarComponent: Calendar re-rendered after event update');
        }, 50);

      } catch (error) {
        console.error('❌ CalendarComponent: Calendar API update failed:', error);
      }
    } else {
      console.error('❌ CalendarComponent: Calendar component not ready - cannot update events');
      // Store events for later application
      this.pendingEvents = events;
    }
  }

  /**
   * Check if calendar is ready for operations
   */
  private isCalendarReady(): boolean {
    if (!this.calendarComponent) {
      return false;
    }

    try {
      const calendarApi = this.calendarComponent.getApi();
      return !!calendarApi;
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Error checking calendar readiness:', error);
      return false;
    }
  }

  /**
   * Force calendar to refresh and re-render events
   */
  private forceCalendarRefresh(): void {
    if (this.calendarComponent) {
      try {
        const calendarApi = this.calendarComponent.getApi();
        if (calendarApi) {
          console.log('🔄 CalendarComponent: Forcing calendar refresh...');
          // Don't use refetchEvents() since we manually manage events
          // Just render to ensure events are displayed
          calendarApi.render();
          console.log('✅ CalendarComponent: Calendar refresh completed');
        }
      } catch (error) {
        console.warn('⚠️ CalendarComponent: Failed to force calendar refresh:', error);
      }
    }
  }

  /**
   * Debug method to log current month data and force refresh
   */
  debugCurrentMonthData(): void {
    console.log('🐛 CalendarComponent: DEBUG - Current Month Data Analysis');
    console.log('📅 Current Display Month:', this.currentDisplayMonth);
    console.log('🎉 Holidays Data:', this.holidays);
    console.log('🏖️ Leaves Data:', this.userLeaves);
    console.log('📊 Calendar Data:', this.currentCalendarData);

    if (this.currentDisplayMonth) {
      const { year, month } = this.currentDisplayMonth;
      console.log(`🔍 Analyzing data for ${year}-${month.toString().padStart(2, '0')}:`);

      // Check holiday filtering
      const holidaysInMonth = this.holidays.filter((holiday: any) => {
        const dateField = holiday.holiday_date || holiday.date;
        if (!dateField) return false;
        const holidayDate = new Date(dateField);
        return holidayDate.getFullYear() === year && (holidayDate.getMonth() + 1) === month;
      });
      console.log(`🎉 Holidays in current month: ${holidaysInMonth.length}`, holidaysInMonth);

      // Test holiday event creation
      const holidayEvents = this.convertHolidaysToEvents();
      console.log(`🎉 Holiday events created: ${holidayEvents.length}`, holidayEvents);

      // Check leave filtering
      const leavesInMonth = this.userLeaves.filter((leave: any) => {
        if (!leave.start_date || !leave.end_date) return false;
        const leaveStart = new Date(leave.start_date);
        const leaveEnd = new Date(leave.end_date);
        const monthStart = new Date(year, month - 1, 1);
        const monthEnd = new Date(year, month, 0);
        return leaveStart <= monthEnd && leaveEnd >= monthStart;
      });
      console.log(`🏖️ Leaves in current month: ${leavesInMonth.length}`, leavesInMonth);

      // Force refresh
      console.log('🔄 Forcing calendar refresh...');
      this.combineAndUpdateEvents();
    }
  }

  /**
   * Test holiday display across multiple months
   */
  testHolidayDisplay(): void {
    console.log('🧪 CalendarComponent: Testing holiday display across months...');

    // Get all raw holiday data
    this.newYearActivityService.getAllActivities().subscribe({
      next: (response) => {
        console.log('🎉 Raw holiday API response:', response);

        if (response.success && response.data) {
          const allHolidays = Array.isArray(response.data) ? response.data : [];
          console.log(`📊 Total holidays available: ${allHolidays.length}`);

          // Group holidays by month
          const holidaysByMonth: { [key: string]: any[] } = {};

          allHolidays.forEach((holiday: any) => {
            const dateField = holiday.holiday_date || holiday.date;
            if (dateField) {
              try {
                const holidayDate = new Date(dateField);
                if (!isNaN(holidayDate.getTime())) {
                  const monthKey = `${holidayDate.getFullYear()}-${(holidayDate.getMonth() + 1).toString().padStart(2, '0')}`;
                  if (!holidaysByMonth[monthKey]) {
                    holidaysByMonth[monthKey] = [];
                  }
                  holidaysByMonth[monthKey].push({
                    name: holiday.holiday || holiday.activity_name,
                    date: dateField,
                    original: holiday
                  });
                }
              } catch (error) {
                console.warn('Invalid holiday date:', dateField, holiday);
              }
            }
          });

          console.log('🎉 Holidays grouped by month:', holidaysByMonth);

          // Test current month filtering
          if (this.currentDisplayMonth) {
            const currentKey = `${this.currentDisplayMonth.year}-${this.currentDisplayMonth.month.toString().padStart(2, '0')}`;
            const currentMonthHolidays = holidaysByMonth[currentKey] || [];
            console.log(`🎉 Holidays for current month (${currentKey}):`, currentMonthHolidays);
          }
        }
      },
      error: (error) => {
        console.error('❌ Error testing holiday display:', error);
      }
    });
  }

  /**
   * Add visual loading indicator to calendar
   */
  private addLoadingIndicator(): void {
    try {
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const calendarEl = calendarApi.el;

        if (calendarEl && !calendarEl.querySelector('.calendar-loading-overlay')) {
          const loadingOverlay = document.createElement('div');
          loadingOverlay.className = 'calendar-loading-overlay';
          loadingOverlay.innerHTML = `
            <div class="calendar-loading-content">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2 text-muted">Loading calendar data...</p>
            </div>
          `;
          loadingOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 8px;
          `;

          const loadingContent = loadingOverlay.querySelector('.calendar-loading-content') as HTMLElement;
          if (loadingContent) {
            loadingContent.style.cssText = `
              text-align: center;
              padding: 20px;
            `;
          }

          calendarEl.style.position = 'relative';
          calendarEl.appendChild(loadingOverlay);
          console.log('📅 CalendarComponent: Loading indicator added');
        }
      }
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Failed to add loading indicator:', error);
    }
  }

  /**
   * Remove visual loading indicator from calendar
   */
  private removeLoadingIndicator(): void {
    try {
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const calendarEl = calendarApi.el;

        if (calendarEl) {
          const loadingOverlay = calendarEl.querySelector('.calendar-loading-overlay');
          if (loadingOverlay) {
            loadingOverlay.remove();
            console.log('📅 CalendarComponent: Loading indicator removed');
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Failed to remove loading indicator:', error);
    }
  }

  /**
   * Process BizzCorp Calendar API response and extract all event types
   */
  private processBizzCorpCalendarData(response: any, year: number, month: number): void {
    console.log(`🔄 CalendarComponent: Processing BizzCorp Calendar data for ${year}-${month.toString().padStart(2, '0')}`);
    console.log(`🎯 CalendarComponent: Target navigation month: ${year}-${month}, API response month: ${response.data?.calendar_data?.year}-${response.data?.calendar_data?.month}`);

    if (!response.data || !response.data.calendar_data || !response.data.calendar_data.days) {
      console.error('❌ CalendarComponent: Invalid BizzCorp Calendar data structure:', response);
      this.currentCalendarData = null;
      this.holidays = [];
      this.userLeaves = [];
      return;
    }

    const calendarData = response.data.calendar_data;
    const days = calendarData.days;

    console.log(`📊 CalendarComponent: Processing ${days.length} days from BizzCorp Calendar API`);
    console.log(`📅 CalendarComponent: Calendar period: ${calendarData.month_name} ${calendarData.year}`);
    console.log(`🔍 CalendarComponent: API returned data for year=${calendarData.year}, month=${calendarData.month}, requested year=${year}, month=${month}`);

    // Verify we got data for the correct month
    if (calendarData.year !== year || calendarData.month !== month) {
      console.warn(`⚠️ CalendarComponent: API returned data for ${calendarData.year}-${calendarData.month} but requested ${year}-${month}`);
    }

    // Store calendar data for reference
    this.currentCalendarData = calendarData;

    // Extract and categorize events from all days
    this.extractEventsFromBizzCorpData(days, year, month);

    // NOW update the tracking variable since data is successfully processed
    this.currentDisplayMonth = { year, month };
    console.log(`📍 CalendarComponent: Updated currentDisplayMonth to ${year}-${month} after successful data processing`);

    console.log(`✅ CalendarComponent: BizzCorp Calendar data processed - Holidays: ${this.holidays.length}, Leaves: ${this.userLeaves.length}, Total Events: ${this.bizzCorpCalendarEvents.length}`);
  }

  /**
   * Extract and categorize events from BizzCorp Calendar days data
   */
  private extractEventsFromBizzCorpData(days: any[], year: number, month: number): void {
    console.log(`🔍 CalendarComponent: Extracting events from ${days.length} days for ${year}-${month.toString().padStart(2, '0')}`);
    console.log(`🗑️ CalendarComponent: BEFORE extraction - existing events: ${this.bizzCorpCalendarEvents.length}`);

    // Reset arrays - CRITICAL for preventing old data from persisting
    this.holidays = [];
    this.userLeaves = [];
    this.bizzCorpCalendarEvents = []; // Store all events for calendar display
    console.log(`✅ CalendarComponent: Arrays reset - bizzCorpCalendarEvents: ${this.bizzCorpCalendarEvents.length}`);

    let totalEvents = 0;
    let holidayCount = 0;
    let leaveCount = 0;
    let weekendCount = 0;
    let absentCount = 0;
    let attendanceCount = 0;

    days.forEach((day: any, index: number) => {
      const dayDate = day.date;
      const primaryStatus = day.primary_status;
      const dayEvents = day.events || [];

      if (index < 5) { // Log first 5 days for debugging
        console.log(`📅 Processing day ${dayDate}: primary_status="${primaryStatus}", events=${dayEvents.length}`);
        if (dayEvents.length > 0) {
          console.log(`   Events for ${dayDate}:`, dayEvents.map((e: any) => ({ type: e.event_type, title: e.title })));
        }
      }

      // Process explicit events for this day
      dayEvents.forEach((event: any) => {
        totalEvents++;

        // Categorize events by type
        switch (event.event_type) {
          case 'holiday':
            holidayCount++;
            this.holidays.push({
              id: event.id,
              date: event.date,
              holiday: event.title,
              holiday_date: event.date,
              holiday_month: new Date(event.date).getMonth() + 1,
              holiday_day: new Date(event.date).getDate()
            } as any);
            break;

          case 'leave':
            leaveCount++;
            this.userLeaves.push({
              id: event.id,
              employee_id: this.employeeInfo?.id || '',
              start_date: event.date,
              end_date: event.date,
              leave_type: event.title,
              status: event.status,
              reason: event.description || '',
              days: 1,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            } as any);
            break;

          case 'weekend':
            weekendCount++;
            break;

          case 'absent':
            absentCount++;
            break;

          case 'attendance':
          case 'present':
            attendanceCount++;
            break;
        }

        // Add to calendar events array for FullCalendar display
        this.bizzCorpCalendarEvents.push(this.transformBizzCorpEventToFullCalendar(event, day));
      });

      // Handle days with primary_status but no explicit events
      if (dayEvents.length === 0 && primaryStatus) {
        totalEvents++;

        // Create implicit event based on primary_status
        const implicitEvent = this.createImplicitEventFromPrimaryStatus(day);
        if (implicitEvent) {
          this.bizzCorpCalendarEvents.push(implicitEvent);

          // Categorize implicit events
          switch (primaryStatus) {
            case 'weekend':
              weekendCount++;
              break;
            case 'absent':
              absentCount++;
              break;
            case 'holiday':
              holidayCount++;
              break;
          }
        }
      }
    });

    console.log(`📊 CalendarComponent: Event extraction summary for ${year}-${month}:`);
    console.log(`   Total events: ${totalEvents}`);
    console.log(`   Holidays: ${holidayCount}`);
    console.log(`   Leaves: ${leaveCount}`);
    console.log(`   Weekends: ${weekendCount}`);
    console.log(`   Absent: ${absentCount}`);
    console.log(`   Attendance: ${attendanceCount}`);
    console.log(`   Calendar events for display: ${this.bizzCorpCalendarEvents.length}`);

    // Debug: Log sample events to verify they're for the correct month
    if (this.bizzCorpCalendarEvents.length > 0) {
      const sampleEvents = this.bizzCorpCalendarEvents.slice(0, 5);
      console.log(`🔍 CalendarComponent: Sample events for ${year}-${month}:`,
        sampleEvents.map(e => ({
          title: e.title,
          start: e.start,
          type: e.extendedProps?.event_type
        }))
      );

      // Verify all events are for the correct month
      const wrongMonthEvents = this.bizzCorpCalendarEvents.filter(event => {
        const eventDate = new Date(event.start);
        return eventDate.getFullYear() !== year || (eventDate.getMonth() + 1) !== month;
      });

      if (wrongMonthEvents.length > 0) {
        console.error(`❌ CalendarComponent: Found ${wrongMonthEvents.length} events for wrong month!`, wrongMonthEvents);
      } else {
        console.log(`✅ CalendarComponent: All ${this.bizzCorpCalendarEvents.length} events are for correct month ${year}-${month}`);
      }
    }
  }

  /**
   * Transform BizzCorp event to FullCalendar-compatible format
   */
  private transformBizzCorpEventToFullCalendar(event: any, day: any): any {
    return {
      id: event.id || `event-${event.date}-${event.event_type}`,
      title: event.title,
      start: event.date,
      backgroundColor: event.color,
      textColor: event.text_color,
      borderColor: event.color,
      extendedProps: {
        event_type: event.event_type,
        status: event.status,
        description: event.description,
        primary_status: day.primary_status,
        check_in_time: event.check_in_time,
        check_out_time: event.check_out_time,
        working_hours: event.working_hours,
        overtime_hours: event.overtime_hours,
        leave_type: event.leave_type,
        leave_days: event.leave_days,
        leave_reason: event.leave_reason,
        is_optional: event.is_optional,
        region_code: event.region_code
      }
    };
  }

  /**
   * Create implicit event from primary_status when no explicit events exist
   */
  private createImplicitEventFromPrimaryStatus(day: any): any | null {
    const primaryStatus = day.primary_status;
    const dayDate = day.date;

    switch (primaryStatus) {
      case 'weekend':
        return {
          id: `weekend-${dayDate}`,
          title: 'Weekend',
          start: dayDate,
          backgroundColor: '#9E9E9E',
          textColor: '#FFFFFF',
          borderColor: '#9E9E9E',
          extendedProps: {
            event_type: 'weekend',
            status: 'weekend',
            description: 'Weekend day',
            primary_status: primaryStatus
          }
        };

      case 'absent':
        return {
          id: `absent-${dayDate}`,
          title: 'Absent',
          start: dayDate,
          backgroundColor: '#F44336',
          textColor: '#FFFFFF',
          borderColor: '#F44336',
          extendedProps: {
            event_type: 'absent',
            status: 'absent',
            description: 'Absent day',
            primary_status: primaryStatus
          }
        };

      case 'holiday':
        return {
          id: `holiday-${dayDate}`,
          title: 'Holiday',
          start: dayDate,
          backgroundColor: '#FF5722',
          textColor: '#FFFFFF',
          borderColor: '#FF5722',
          extendedProps: {
            event_type: 'holiday',
            status: 'holiday',
            description: 'Holiday',
            primary_status: primaryStatus
          }
        };

      default:
        return null;
    }
  }

  /**
   * Update calendar with all events from BizzCorp Calendar API
   */
  private updateCalendarWithBizzCorpEvents(): void {
    console.log(`🔄 CalendarComponent: Updating calendar with ${this.bizzCorpCalendarEvents.length} BizzCorp events`);
    console.log(`📍 CalendarComponent: Current display month: ${this.currentDisplayMonth?.year}-${this.currentDisplayMonth?.month}`);

    if (!this.bizzCorpCalendarEvents || this.bizzCorpCalendarEvents.length === 0) {
      console.log('📅 CalendarComponent: No BizzCorp events to display - clearing calendar');
      this.updateCalendarEvents([]);
      return;
    }

    // Debug: Log what events we're about to display
    console.log(`🔍 CalendarComponent: About to display events:`,
      this.bizzCorpCalendarEvents.slice(0, 3).map(e => ({
        title: e.title,
        start: e.start,
        type: e.extendedProps?.event_type
      }))
    );

    // Log event types for debugging
    const eventTypes = this.bizzCorpCalendarEvents.reduce((acc: any, event: any) => {
      const type = event.extendedProps?.event_type || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    console.log('📊 CalendarComponent: Event types breakdown:', eventTypes);

    // Log sample events for debugging
    const sampleEvents = this.bizzCorpCalendarEvents.slice(0, 3);
    console.log('🔍 CalendarComponent: Sample events:', sampleEvents.map(e => ({
      title: e.title,
      date: e.start,
      type: e.extendedProps?.event_type,
      color: e.backgroundColor
    })));

    // Update calendar with all events
    this.updateCalendarEvents(this.bizzCorpCalendarEvents);

    // Force calendar refresh to ensure events are displayed
    this.forceCalendarRefresh();

    console.log('✅ CalendarComponent: Calendar updated with BizzCorp events');
  }

  /**
   * Get current display month from calendar API or tracking variable
   */
  private getCurrentDisplayMonth(calendarApi: any): { year: number; month: number } {
    // Try to get from tracking variable first
    if (this.currentDisplayMonth) {
      console.log(`📅 CalendarComponent: Using tracked display month: ${this.currentDisplayMonth.year}-${this.currentDisplayMonth.month.toString().padStart(2, '0')}`);
      return this.currentDisplayMonth;
    }

    // Fallback to calendar API current date
    try {
      const currentDate = calendarApi.getDate();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;

      console.log(`📅 CalendarComponent: Using calendar API date: ${year}-${month.toString().padStart(2, '0')}`);
      return { year, month };
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Failed to get date from calendar API, using current date:', error);

      // Final fallback to current date
      const now = new Date();
      return {
        year: now.getFullYear(),
        month: now.getMonth() + 1
      };
    }
  }

  /**
   * Perform comprehensive month navigation with data loading and view updates
   */
  private performMonthNavigation(targetYear: number, targetMonth: number, targetDate: Date, direction: 'previous' | 'next' | 'today'): void {
    console.log(`🔄 CalendarComponent: Performing ${direction} month navigation to ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);

    try {
      const calendarApi = this.calendarComponent!.getApi();

      // Set loading state to prevent multiple navigations
      this.setNavigationLoadingState(true);

      // Clear existing events immediately for better UX
      this.clearCalendarEvents();

      // Update tracking variable
      this.currentDisplayMonth = { year: targetYear, month: targetMonth };
      console.log(`📅 CalendarComponent: Updated currentDisplayMonth to ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);

      // Update calendar view based on direction
      this.updateCalendarView(calendarApi, targetDate, direction);

      // Load fresh data for the new month
      setTimeout(() => {
        this.loadCalendarDataWithNavigation(targetYear, targetMonth, direction);
      }, 150); // Small delay to ensure view update completes

    } catch (error) {
      console.error(`❌ CalendarComponent: Error during ${direction} navigation:`, error);
      this.error = `Failed to navigate to ${direction === 'today' ? 'current month' : direction + ' month'}. Please try again.`;
      this.setNavigationLoadingState(false);
    }
  }

  /**
   * Update calendar view based on navigation direction
   */
  private updateCalendarView(calendarApi: any, targetDate: Date, direction: 'previous' | 'next' | 'today'): void {
    try {
      switch (direction) {
        case 'today':
          calendarApi.today();
          break;
        case 'previous':
        case 'next':
          calendarApi.gotoDate(targetDate);
          break;
      }

      // Force calendar to render the new view
      setTimeout(() => {
        calendarApi.render();
        console.log(`✅ CalendarComponent: Calendar view updated for ${direction} navigation`);
      }, 50);

    } catch (error) {
      console.error(`❌ CalendarComponent: Error updating calendar view for ${direction}:`, error);
    }
  }

  /**
   * Load calendar data specifically for navigation with enhanced error handling and fresh API calls
   */
  private loadCalendarDataWithNavigation(year: number, month: number, direction: string): void {
    console.log(`🌐 CalendarComponent: Loading fresh calendar data for ${direction} navigation: ${year}-${month.toString().padStart(2, '0')}`);
    console.log(`🔄 CalendarComponent: Making fresh API call to /api/v1/calendar/my-calendar?year=${year}&month=${month}`);

    // Clear any cached data to ensure fresh fetch
    this.clearNavigationCache();

    // Reset data arrays to ensure clean state
    this.bizzCorpCalendarEvents = [];
    this.holidays = [];
    this.userLeaves = [];

    // Set comprehensive loading states
    this.loading = true;
    this.isLoadingData = true;
    this.isLoadingHolidays = true;
    this.isLoadingLeaves = true;
    this.error = null;
    this.holidaysError = null;
    this.leavesError = null;

    // Use the enhanced loadCalendarData method with force refresh
    // This ensures fresh API call and proper data processing
    this.loadCalendarData(year, month, true);

    // Monitor the loading completion
    const checkLoadingComplete = () => {
      if (!this.loading && !this.isLoadingData) {
        console.log(`✅ CalendarComponent: Fresh data loading completed for ${direction} navigation to ${year}-${month.toString().padStart(2, '0')}`);
        console.log(`📊 CalendarComponent: Loaded ${this.bizzCorpCalendarEvents.length} events for the new month`);

        // Finalize navigation
        this.finalizeNavigation(year, month);
        return;
      }

      // Check again after a short delay
      setTimeout(checkLoadingComplete, 100);
    };

    // Start monitoring after a brief delay to allow loading to start
    setTimeout(checkLoadingComplete, 200);

    // Set a timeout to handle stuck loading states
    setTimeout(() => {
      if (this.isLoadingData || this.loading) {
        console.warn(`⚠️ CalendarComponent: Navigation data loading timeout for ${direction} to ${year}-${month.toString().padStart(2, '0')}`);
        this.handleNavigationError(year, month, direction, 'Request timeout');
        this.clearNavigationLoadingStates();
      }
    }, 15000); // 15 second timeout for navigation
  }

  /**
   * Set navigation loading state and update UI accordingly
   */
  private setNavigationLoadingState(isLoading: boolean): void {
    this.isLoadingData = isLoading;

    if (isLoading) {
      console.log('🔄 CalendarComponent: Navigation loading started');
      this.addLoadingIndicator();
      this.updateNavigationButtonStates(true);
    } else {
      console.log('✅ CalendarComponent: Navigation loading completed');
      this.removeLoadingIndicator();
      this.updateNavigationButtonStates(false);
    }
  }

  /**
   * Update navigation button states (disabled/enabled)
   */
  private updateNavigationButtonStates(disabled: boolean): void {
    try {
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const calendarEl = calendarApi.el;

        if (calendarEl) {
          // Find and update custom navigation buttons
          const prevButton = calendarEl.querySelector('.fc-customPrev-button');
          const nextButton = calendarEl.querySelector('.fc-customNext-button');
          const todayButton = calendarEl.querySelector('.fc-customToday-button');

          [prevButton, nextButton, todayButton].forEach(button => {
            if (button) {
              if (disabled) {
                button.setAttribute('disabled', 'true');
                button.classList.add('fc-button-disabled');
              } else {
                button.removeAttribute('disabled');
                button.classList.remove('fc-button-disabled');
              }
            }
          });

          console.log(`📅 CalendarComponent: Navigation buttons ${disabled ? 'disabled' : 'enabled'}`);
        }
      }
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Error updating navigation button states:', error);
    }
  }

  /**
   * Clear all events from calendar immediately
   */
  private clearCalendarEvents(): void {
    try {
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const existingEvents = calendarApi.getEvents();

        existingEvents.forEach((event: any) => event.remove());
        console.log(`🗑️ CalendarComponent: Cleared ${existingEvents.length} existing events`);
      }
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Error clearing calendar events:', error);
    }
  }

  /**
   * Update calendar title to reflect current month/year
   */
  private updateCalendarTitle(year: number, month: number): void {
    try {
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const targetDate = new Date(year, month - 1, 1);

        // Ensure calendar is showing the correct month and force title update
        calendarApi.gotoDate(targetDate);
        calendarApi.changeView('dayGridMonth', targetDate);

        // Force immediate calendar render to update title
        calendarApi.render();

        console.log(`📅 CalendarComponent: Calendar title immediately updated to ${this.getMonthName(month)} ${year}`);
      }
    } catch (error) {
      console.warn('⚠️ CalendarComponent: Error updating calendar title:', error);
    }
  }

  /**
   * Enhanced refresh calendar method with navigation support
   */
  refreshCalendar(): void {
    console.log('🔄 CalendarComponent: Manual calendar refresh requested');

    if (this.isLoadingData || this.loading) {
      console.log('⏳ CalendarComponent: Already loading, skipping refresh');
      return;
    }

    try {
      // Prioritize currentDisplayMonth to maintain navigation state
      if (this.currentDisplayMonth) {
        console.log(`🔄 CalendarComponent: Refreshing data for tracked month: ${this.currentDisplayMonth.year}-${this.currentDisplayMonth.month}`);

        // Clear existing events
        this.clearCalendarEvents();

        // Reload data for tracked month
        this.loadCalendarData(this.currentDisplayMonth.year, this.currentDisplayMonth.month);

      } else if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const currentDate = calendarApi.getDate();
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth() + 1;

        console.log(`🔄 CalendarComponent: Refreshing data for current view: ${year}-${month.toString().padStart(2, '0')}`);

        // Clear existing events
        this.clearCalendarEvents();

        // Update tracking
        this.currentDisplayMonth = { year, month };

        // Reload data for current month
        this.loadCalendarData(year, month);
      } else {
        // Fallback to current date but be explicit about it
        const now = new Date();
        const fallbackYear = now.getFullYear();
        const fallbackMonth = now.getMonth() + 1;
        console.log(`🔄 CalendarComponent: Calendar not available, using current date for refresh: ${fallbackYear}-${fallbackMonth}`);
        this.currentDisplayMonth = { year: fallbackYear, month: fallbackMonth };
        this.loadCalendarData(fallbackYear, fallbackMonth);
      }
    } catch (error) {
      console.error('❌ CalendarComponent: Error during calendar refresh:', error);
      this.error = 'Failed to refresh calendar. Please try again.';
    }
  }



  // REMOVED: shouldLoadDataForMonth() method to ensure fresh data loading on every navigation

  /**
   * Clear navigation-related cache to ensure fresh data loading
   */
  private clearNavigationCache(): void {
    console.log('🗑️ CalendarComponent: Clearing navigation cache for fresh data fetch');

    // Clear calendar service cache if available
    if (this.calendarService && typeof this.calendarService.clearCache === 'function') {
      this.calendarService.clearCache();
    }

    console.log('✅ CalendarComponent: Navigation cache cleared');
  }

  /**
   * Handle navigation errors with appropriate user feedback
   */
  private handleNavigationError(year: number, month: number, direction: string, error: any): void {
    console.error(`❌ CalendarComponent: Navigation error for ${direction} to ${year}-${month}:`, error);

    // Set appropriate error message based on error type
    if (typeof error === 'string') {
      this.error = `Failed to load ${direction} month: ${error}`;
    } else if (error.status === 0) {
      this.error = `Network error loading ${direction} month. Please check your connection and try again.`;
    } else if (error.status >= 500) {
      this.error = `Server error loading ${direction} month. Please try again later.`;
    } else if (error.status === 401 || error.status === 403) {
      this.error = `Authentication error loading ${direction} month. Please refresh the page and try again.`;
    } else {
      this.error = `Failed to load ${direction} month data. Please try again.`;
    }

    // Clear calendar events on error
    this.updateCalendarEvents([]);

    // Reset arrays
    this.bizzCorpCalendarEvents = [];
    this.holidays = [];
    this.userLeaves = [];
  }

  /**
   * Clear all navigation loading states
   */
  private clearNavigationLoadingStates(): void {
    this.loading = false;
    this.isLoadingData = false;
    this.isLoadingHolidays = false;
    this.isLoadingLeaves = false;
    this.setNavigationLoadingState(false);
    this.removeLoadingIndicator();

    console.log('✅ CalendarComponent: All navigation loading states cleared');
  }

  /**
   * Simple navigation: change parameters and fetch fresh data
   */
  private navigateToMonth(year: number, month: number): void {
    console.log(`🔄 CalendarComponent: Navigating to ${year}-${month}`);

    // Prevent navigation if already loading
    if (this.isLoadingData || this.loading || this.isNavigating) {
      console.warn(`⚠️ CalendarComponent: Cannot navigate to ${year}-${month} - already loading or navigating`);
      return;
    }

    // Set navigation flag to prevent automatic refreshes
    this.isNavigating = true;
    console.log('🚫 CalendarComponent: Navigation flag set - blocking automatic refreshes');

    // Clear existing events immediately for better UX
    this.clearCalendarEvents();
    this.bizzCorpCalendarEvents = [];

    // Update tracking FIRST to prevent any automatic refresh from overriding
    this.currentDisplayMonth = { year, month };
    console.log(`📍 CalendarComponent: Updated currentDisplayMonth to ${year}-${month}`);

    // IMMEDIATELY update calendar view to target month for instant visual feedback
    if (this.calendarComponent) {
      const calendarApi = this.calendarComponent.getApi();
      const targetDate = new Date(year, month - 1, 1); // Date constructor uses 0-based months

      // Use multiple methods to ensure the calendar view updates immediately
      calendarApi.gotoDate(targetDate);
      calendarApi.changeView('dayGridMonth', targetDate);

      // Force immediate render to show the month change
      setTimeout(() => {
        calendarApi.render();
        console.log(`📅 CalendarComponent: Calendar view immediately updated to ${targetDate.toDateString()}`);
        console.log(`📅 CalendarComponent: Calendar now showing ${this.getMonthName(month)} ${year}`);
      }, 10);
    }

    // Fetch fresh data with new parameters - MUST use exact parameters
    console.log(`🌐 CalendarComponent: Calling loadCalendarData(${year}, ${month}, true)`);
    this.loadCalendarData(year, month, true);
  }

  /**
   * FORCE immediate calendar view update with DOM title manipulation
   * This method ensures both the calendar view AND title update immediately
   */
  private forceCalendarViewUpdate(year: number, month: number): void {
    console.log(`🚀 CalendarComponent: FORCING immediate calendar view update to ${this.getMonthName(month)} ${year}`);

    try {
      if (!this.calendarComponent) {
        console.warn('⚠️ CalendarComponent: Cannot force view update - calendar component not available');
        return;
      }

      const calendarApi = this.calendarComponent.getApi();
      const targetDate = new Date(year, month - 1, 1); // FullCalendar uses 0-based months

      // Step 1: AGGRESSIVELY update calendar view using FullCalendar API
      console.log(`📅 CalendarComponent: AGGRESSIVELY updating calendar view to ${targetDate.toDateString()}`);

      // Clear all existing events IMMEDIATELY to prevent old events from showing
      const existingEvents = calendarApi.getEvents();
      existingEvents.forEach(event => event.remove());
      console.log(`🗑️ CalendarComponent: Cleared ${existingEvents.length} existing events before view change`);

      // Force calendar to the new date
      calendarApi.gotoDate(targetDate);
      calendarApi.changeView('dayGridMonth', targetDate);

      // Force immediate render to ensure view change takes effect
      calendarApi.render();
      console.log(`📅 CalendarComponent: Calendar view AGGRESSIVELY updated and rendered for ${this.getMonthName(month)} ${year}`);

      // IMMEDIATELY update tracking to ensure consistency
      this.currentDisplayMonth = { year, month };
      console.log(`📍 CalendarComponent: Updated currentDisplayMonth tracking to ${year}-${month}`);

      // Step 2: IMMEDIATELY update the title using DOM manipulation
      this.updateCalendarTitleDirectly(year, month);

      // Step 3: Verify and log the update
      requestAnimationFrame(() => {
        const currentView = calendarApi.view;
        const currentCalendarDate = calendarApi.getDate();

        console.log(`📊 CalendarComponent: Calendar internal date: ${currentCalendarDate.toDateString()}`);
        console.log(`📊 CalendarComponent: Calendar view title: ${currentView.title}`);
        console.log(`✅ CalendarComponent: Calendar view IMMEDIATELY updated to ${this.getMonthName(month)} ${year}`);

        // Double-check title update worked
        this.verifyTitleUpdate(year, month);
      });

    } catch (error) {
      console.error('❌ CalendarComponent: Error forcing calendar view update:', error);
    }
  }

  /**
   * Update calendar title directly using DOM manipulation
   * This bypasses FullCalendar's title update mechanism
   */
  private updateCalendarTitleDirectly(year: number, month: number): void {
    try {
      const expectedTitle = `${this.getMonthName(month)} ${year}`;
      console.log(`🎯 CalendarComponent: Setting calendar title directly to: "${expectedTitle}"`);

      // Find the calendar title element in the DOM
      const titleElement = document.querySelector('.fc-toolbar-title');

      if (titleElement) {
        titleElement.textContent = expectedTitle;
        console.log(`✅ CalendarComponent: Title updated directly via DOM to: "${expectedTitle}"`);
      } else {
        console.warn('⚠️ CalendarComponent: Could not find calendar title element in DOM');

        // Try alternative selectors
        const altTitleElement = document.querySelector('.fc-header-title h2') ||
                               document.querySelector('.fc-center h2') ||
                               document.querySelector('h2.fc-toolbar-title');

        if (altTitleElement) {
          altTitleElement.textContent = expectedTitle;
          console.log(`✅ CalendarComponent: Title updated via alternative selector to: "${expectedTitle}"`);
        } else {
          console.error('❌ CalendarComponent: Could not find any calendar title element');
        }
      }
    } catch (error) {
      console.error('❌ CalendarComponent: Error updating title directly:', error);
    }
  }

  /**
   * Verify that the title update was successful
   */
  private verifyTitleUpdate(year: number, month: number): void {
    try {
      const expectedTitle = `${this.getMonthName(month)} ${year}`;
      const titleElement = document.querySelector('.fc-toolbar-title');

      if (titleElement) {
        const actualTitle = titleElement.textContent;
        if (actualTitle === expectedTitle) {
          console.log(`✅ CalendarComponent: Title verification successful: "${actualTitle}"`);
        } else {
          console.warn(`⚠️ CalendarComponent: Title mismatch - Expected: "${expectedTitle}", Actual: "${actualTitle}"`);
        }
      }
    } catch (error) {
      console.error('❌ CalendarComponent: Error verifying title update:', error);
    }
  }

  /**
   * Ensure calendar title is properly updated to match the target month
   */
  private ensureCalendarTitleUpdate(calendarApi: any, year: number, month: number): void {
    try {
      const expectedTitle = `${this.getMonthName(month)} ${year}`;
      const currentView = calendarApi.view;
      const currentTitle = currentView.title;

      console.log(`🔍 CalendarComponent: Checking title - Expected: "${expectedTitle}", Current: "${currentTitle}"`);

      if (currentTitle !== expectedTitle) {
        console.log(`⚠️ CalendarComponent: Title mismatch detected, forcing update...`);

        // Try multiple approaches to force title update
        const targetDate = new Date(year, month - 1, 1);

        // Approach 1: Re-navigate to the date
        calendarApi.gotoDate(targetDate);

        // Approach 2: Force view change
        calendarApi.changeView('dayGridMonth', targetDate);

        // Approach 3: Multiple renders
        calendarApi.render();

        // Check again after forcing update
        setTimeout(() => {
          const updatedTitle = calendarApi.view.title;
          console.log(`📊 CalendarComponent: Title after force update: "${updatedTitle}"`);

          if (updatedTitle === expectedTitle) {
            console.log(`✅ CalendarComponent: Title successfully updated to "${expectedTitle}"`);
          } else {
            console.warn(`⚠️ CalendarComponent: Title still incorrect after force update. Expected: "${expectedTitle}", Got: "${updatedTitle}"`);
          }
        }, 50);
      } else {
        console.log(`✅ CalendarComponent: Title is correct: "${currentTitle}"`);
      }
    } catch (error) {
      console.error('❌ CalendarComponent: Error ensuring title update:', error);
    }
  }

  /**
   * Finalize navigation by updating calendar view and title
   */
  private finalizeNavigation(year: number, month: number): void {
    try {
      // Final verification that calendar view is synchronized with the data
      if (this.calendarComponent) {
        const calendarApi = this.calendarComponent.getApi();
        const targetDate = new Date(year, month - 1, 1);

        // Final check that calendar is showing the correct month
        calendarApi.gotoDate(targetDate);
        calendarApi.changeView('dayGridMonth', targetDate);
        calendarApi.render();

        console.log(`✅ CalendarComponent: Navigation finalized - Calendar showing ${this.getMonthName(month)} ${year}`);
        console.log(`📊 CalendarComponent: Final state - Events: ${this.bizzCorpCalendarEvents.length}, Holidays: ${this.holidays.length}, Leaves: ${this.userLeaves.length}`);
      }

    } catch (error) {
      console.warn('⚠️ CalendarComponent: Error finalizing navigation:', error);
    }
  }

  /**
   * Verify and log the API URL format for debugging
   */
  private verifyApiUrl(year: number, month: number, direction: string): void {
    const baseUrl = `${environment.apiUrl}/api/v1/calendar/my-calendar`;
    const expectedUrl = `${baseUrl}?year=${year}&month=${month}`;

    console.log(`🔍 CalendarComponent: API URL Verification for ${direction} navigation:`);
    console.log(`📍 Expected URL: ${expectedUrl}`);
    console.log(`📅 Year: ${year} (type: ${typeof year})`);
    console.log(`📅 Month: ${month} (type: ${typeof month}) - Should be 1-12 range`);
    console.log(`✅ Month validation: ${month >= 1 && month <= 12 ? 'VALID' : 'INVALID'}`);

    // Additional validation
    if (month < 1 || month > 12) {
      console.error(`❌ CalendarComponent: Invalid month value ${month} for API call!`);
    }
    if (!Number.isInteger(year) || year < 2020 || year > 2030) {
      console.error(`❌ CalendarComponent: Invalid year value ${year} for API call!`);
    }
  }

}
