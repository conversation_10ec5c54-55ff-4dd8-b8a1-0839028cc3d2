import { Component, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modern-popups',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="row">
      <div class="col-xl-10 main-content ps-xl-4 pe-xl-5">
        <h1 class="page-title">Modern Popups & Modals</h1>
        <p class="lead">Enhanced popup and modal designs with modern UI/UX patterns, smooth animations, and improved user experience.</p>

        <hr>

        <!-- Success Popups -->
        <h4 class="mb-4">Success Notifications</h4>
        <div class="row mb-5">
          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Success Alert</h6>
                <p class="text-muted text-center mb-3">Standard success notification with timer</p>
                <button class="btn btn-success" (click)="showSuccessAlert()">
                  <i class="feather icon-check me-1"></i>
                  Show Success
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Success Toast</h6>
                <p class="text-muted text-center mb-3">Non-intrusive toast notification</p>
                <button class="btn btn-success" (click)="showSuccessToast()">
                  <i class="feather icon-bell me-1"></i>
                  Show Toast
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Custom Success</h6>
                <p class="text-muted text-center mb-3">Success with custom content</p>
                <button class="btn btn-success" (click)="showCustomSuccess()">
                  <i class="feather icon-award me-1"></i>
                  Show Custom
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Error & Warning Popups -->
        <h4 class="mb-4">Error & Warning Alerts</h4>
        <div class="row mb-5">
          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Error Alert</h6>
                <p class="text-muted text-center mb-3">Critical error notification</p>
                <button class="btn btn-danger" (click)="showErrorAlert()">
                  <i class="feather icon-alert-circle me-1"></i>
                  Show Error
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Warning Alert</h6>
                <p class="text-muted text-center mb-3">Warning with confirmation</p>
                <button class="btn btn-warning" (click)="showWarningAlert()">
                  <i class="feather icon-alert-triangle me-1"></i>
                  Show Warning
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Validation Error</h6>
                <p class="text-muted text-center mb-3">Form validation errors</p>
                <button class="btn btn-warning" (click)="showValidationError()">
                  <i class="feather icon-x-circle me-1"></i>
                  Show Validation
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Confirmation & Info Popups -->
        <h4 class="mb-4">Confirmation & Information</h4>
        <div class="row mb-5">
          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Confirmation</h6>
                <p class="text-muted text-center mb-3">Action confirmation dialog</p>
                <button class="btn btn-primary" (click)="showConfirmation()">
                  <i class="feather icon-help-circle me-1"></i>
                  Show Confirmation
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Info Alert</h6>
                <p class="text-muted text-center mb-3">Informational message</p>
                <button class="btn btn-info" (click)="showInfoAlert()">
                  <i class="feather icon-info me-1"></i>
                  Show Info
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Loading</h6>
                <p class="text-muted text-center mb-3">Loading state popup</p>
                <button class="btn btn-secondary" (click)="showLoading()">
                  <i class="feather icon-loader me-1"></i>
                  Show Loading
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modern Modal -->
        <h4 class="mb-4">Modern Modals</h4>
        <div class="row mb-5">
          <div class="col-md-6 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Sample Form Modal</h6>
                <p class="text-muted text-center mb-3">Modern modal with enhanced styling</p>
                <button class="btn btn-primary" (click)="showSampleModal()">
                  <i class="feather icon-edit me-1"></i>
                  Open Modal
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-3">
            <div class="card h-100">
              <div class="card-body d-flex flex-column align-items-center">
                <h6 class="card-title text-center">Large Content Modal</h6>
                <p class="text-muted text-center mb-3">Large modal with scrollable content</p>
                <button class="btn btn-primary" (click)="showLargeModal()">
                  <i class="feather icon-maximize me-1"></i>
                  Open Large Modal
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sample Modal Template -->
    <ng-template #sampleModal let-modal>
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="feather icon-user me-2"></i>
          User Information
        </h5>
        <button type="button" class="btn-close" (click)="modal.dismiss()" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label">First Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" placeholder="Enter first name">
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label">Last Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" placeholder="Enter last name">
            </div>
            <div class="col-12 mb-3">
              <label class="form-label">Email Address</label>
              <input type="email" class="form-control" placeholder="Enter email address">
            </div>
            <div class="col-12 mb-3">
              <label class="form-label">Department</label>
              <select class="form-select">
                <option value="">Select Department</option>
                <option value="sales">Sales</option>
                <option value="marketing">Marketing</option>
                <option value="hr">Human Resources</option>
                <option value="it">Information Technology</option>
              </select>
            </div>
            <div class="col-12 mb-3">
              <label class="form-label">Comments</label>
              <textarea class="form-control" rows="3" placeholder="Enter any additional comments"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
          <i class="feather icon-x me-1"></i>
          Cancel
        </button>
        <button type="button" class="btn btn-primary" (click)="modal.close('save')">
          <i class="feather icon-save me-1"></i>
          Save Changes
        </button>
      </div>
    </ng-template>

    <!-- Large Modal Template -->
    <ng-template #largeModal let-modal>
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="feather icon-file-text me-2"></i>
          Detailed Information
        </h5>
        <button type="button" class="btn-close" (click)="modal.dismiss()" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-12 mb-4">
            <h6>Section 1: Basic Information</h6>
            <p>This is a large modal with extensive content to demonstrate scrolling behavior and responsive design.</p>
            <div class="row">
              <div class="col-md-4 mb-3">
                <label class="form-label">Field 1</label>
                <input type="text" class="form-control">
              </div>
              <div class="col-md-4 mb-3">
                <label class="form-label">Field 2</label>
                <input type="text" class="form-control">
              </div>
              <div class="col-md-4 mb-3">
                <label class="form-label">Field 3</label>
                <input type="text" class="form-control">
              </div>
            </div>
          </div>

          <div class="col-12 mb-4">
            <h6>Section 2: Additional Details</h6>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Description</label>
                <textarea class="form-control" rows="4"></textarea>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">Notes</label>
                <textarea class="form-control" rows="4"></textarea>
              </div>
            </div>
          </div>

          <div class="col-12 mb-4">
            <h6>Section 3: Configuration</h6>
            <div class="row">
              <div class="col-md-3 mb-3">
                <label class="form-label">Option 1</label>
                <select class="form-select">
                  <option>Select...</option>
                  <option>Option A</option>
                  <option>Option B</option>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Option 2</label>
                <select class="form-select">
                  <option>Select...</option>
                  <option>Option X</option>
                  <option>Option Y</option>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Date</label>
                <input type="date" class="form-control">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Time</label>
                <input type="time" class="form-control">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
          <i class="feather icon-x me-1"></i>
          Cancel
        </button>
        <button type="button" class="btn btn-primary" (click)="modal.close('save')">
          <i class="feather icon-check me-1"></i>
          Apply Changes
        </button>
      </div>
    </ng-template>
  `,
  styles: [`
    .card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: 1px solid rgba(0,0,0,0.1);
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .btn {
      transition: all 0.2s ease;
    }

    .btn:hover {
      transform: translateY(-1px);
    }
  `]
})
export class ModernPopupsComponent {

  constructor(
    private popupDesignService: PopupDesignService,
    private modalService: NgbModal
  ) {}

  showSuccessAlert() {
    this.popupDesignService.showSuccess({
      title: 'Operation Successful!',
      message: 'Your data has been saved successfully.',
      timer: 3000,
      showProgressBar: true
    });
  }

  showSuccessToast() {
    this.popupDesignService.showToast({
      type: 'success',
      title: 'Success!',
      message: 'Operation completed successfully',
      timer: 3000
    });
  }

  showCustomSuccess() {
    this.popupDesignService.showSuccess({
      title: 'Welcome!',
      html: `
        <div class="text-center">
          <div class="mb-3">
            <i class="feather icon-check-circle" style="font-size: 3rem; color: #05a34a;"></i>
          </div>
          <p class="mb-2">Your account has been created successfully!</p>
          <small class="text-muted">You can now access all features of the application.</small>
        </div>
      `,
      confirmText: 'Get Started',
      timer: 5000
    });
  }

  showErrorAlert() {
    this.popupDesignService.showError({
      title: 'Operation Failed',
      message: 'An error occurred while processing your request. Please try again.',
      confirmText: 'Try Again'
    });
  }

  showWarningAlert() {
    this.popupDesignService.showWarning({
      title: 'Are you sure?',
      message: 'This action cannot be undone. Do you want to continue?',
      confirmText: 'Yes, Continue',
      cancelText: 'Cancel',
      showCancel: true
    }).then((result) => {
      if (result.isConfirmed) {
        this.popupDesignService.showToast({
          type: 'info',
          title: 'Action Confirmed',
          message: 'The operation has been executed.'
        });
      }
    });
  }

  showValidationError() {
    this.popupDesignService.showWarning({
      title: 'Form Validation Errors',
      html: `
        <div class="text-start">
          <p class="mb-3">Please fix the following errors:</p>
          <ul class="list-unstyled">
            <li class="mb-2"><i class="feather icon-x-circle text-danger me-2"></i>Email address is required</li>
            <li class="mb-2"><i class="feather icon-x-circle text-danger me-2"></i>Password must be at least 8 characters</li>
            <li class="mb-2"><i class="feather icon-x-circle text-danger me-2"></i>Please select a department</li>
          </ul>
        </div>
      `,
      confirmText: 'Fix Issues',
      width: '500px'
    });
  }

  showConfirmation() {
    this.popupDesignService.showConfirmation({
      title: 'Delete Item?',
      message: 'Are you sure you want to delete this item? This action cannot be undone.',
      confirmText: 'Yes, Delete',
      cancelText: 'Keep It'
    }).then((result) => {
      if (result.isConfirmed) {
        this.popupDesignService.showSuccess({
          title: 'Deleted!',
          message: 'The item has been deleted successfully.',
          timer: 2000
        });
      }
    });
  }

  showInfoAlert() {
    this.popupDesignService.showInfo({
      title: 'Information',
      html: `
        <div class="text-start">
          <p class="mb-3">Here are some important details:</p>
          <div class="alert alert-info mb-3">
            <i class="feather icon-info me-2"></i>
            <strong>Tip:</strong> You can use keyboard shortcuts to navigate faster.
          </div>
          <ul class="mb-0">
            <li>Press <kbd>Ctrl+S</kbd> to save</li>
            <li>Press <kbd>Ctrl+Z</kbd> to undo</li>
            <li>Press <kbd>Esc</kbd> to close dialogs</li>
          </ul>
        </div>
      `,
      confirmText: 'Got it!',
      width: '450px'
    });
  }

  showLoading() {
    this.popupDesignService.showLoading(
      'Processing Request',
      'Please wait while we process your data...'
    );

    // Simulate processing time
    setTimeout(() => {
      this.popupDesignService.close();
      this.popupDesignService.showSuccess({
        title: 'Complete!',
        message: 'Your request has been processed successfully.',
        timer: 2000
      });
    }, 3000);
  }

  showSampleModal() {
    const modalRef = this.modalService.open(this.sampleModal, {
      size: 'lg',
      centered: true,
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'save') {
        this.popupDesignService.showSuccess({
          title: 'Saved!',
          message: 'User information has been saved successfully.',
          timer: 2000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  showLargeModal() {
    const modalRef = this.modalService.open(this.largeModal, {
      size: 'xl',
      centered: true,
      scrollable: true
    });

    modalRef.result.then((result) => {
      if (result === 'save') {
        this.popupDesignService.showSuccess({
          title: 'Applied!',
          message: 'Changes have been applied successfully.',
          timer: 2000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  // Template references
  @ViewChild('sampleModal') sampleModal!: TemplateRef<any>;
  @ViewChild('largeModal') largeModal!: TemplateRef<any>;
}
