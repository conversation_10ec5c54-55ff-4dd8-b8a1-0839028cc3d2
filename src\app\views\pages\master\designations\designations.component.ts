import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  DesignationService,
  Designation,
  DesignationStatistics,
  DesignationLevel
} from '../../../../core/services/designation.service';
import { DepartmentService } from '../../../../core/services/department.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { DesignationFormComponent } from './designation-form/designation-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-designations',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    DesignationFormComponent,
    BulkUploadComponent
  ],
  templateUrl: './designations.component.html',
  styleUrls: ['./designations.component.scss']
})
export class DesignationsComponent implements OnInit {
  // Data properties
  designations: Designation[] = [];
  statistics: DesignationStatistics | null = null;
  levels: DesignationLevel[] = [];
  departments: any[] = [];

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'list' | 'levels' | 'statistics' = 'list';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedLevel = '';
  selectedDepartment = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedDesignations: Set<string> = new Set();
  selectAll = false;

  constructor(
    private designationService: DesignationService,
    private departmentService: DepartmentService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadDesignations();
    this.loadStatistics();
    this.loadLevels();
    this.loadDepartments();
  }

  /**
   * Load designations with current filters
   */
  loadDesignations(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      level: this.selectedLevel ? parseInt(this.selectedLevel) : undefined,
      department_id: this.selectedDepartment || undefined
    };

    this.designationService.getDesignations(params).subscribe({
      next: (response) => {
        if (response.success) {
          this.designations = response.data;
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load designations';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load designations. Please try again.'
        });
      }
    });
  }

  /**
   * Load designation statistics
   */
  loadStatistics(): void {
    this.designationService.getDesignationStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Load designation levels
   */
  loadLevels(): void {
    this.designationService.getDesignationLevels().subscribe({
      next: (response) => {
        if (response.success) {
          this.levels = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load levels:', error);
      }
    });
  }

  /**
   * Load departments for filter
   */
  loadDepartments(): void {
    this.departmentService.getActiveDepartments().subscribe({
      next: (departments) => {
        this.departments = departments;
        this.cdr.markForCheck();
      },
      error: (error) => {
        console.error('Failed to load departments:', error);
      }
    });
  }

  /**
   * Search designations
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadDesignations();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadDesignations();
  }

  /**
   * Filter by level
   */
  onLevelFilter(): void {
    this.currentPage = 1;
    this.loadDesignations();
  }

  /**
   * Filter by department
   */
  onDepartmentFilter(): void {
    this.currentPage = 1;
    this.loadDesignations();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'list' | 'levels' | 'statistics'): void {
    this.viewMode = mode;
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadDesignations();
  }

  /**
   * Open create designation modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(DesignationFormComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;
    modalRef.componentInstance.departments = this.departments;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadDesignations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Designation created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit designation modal
   */
  openEditModal(designation: Designation): void {
    const modalRef = this.modalService.open(DesignationFormComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.designation = { ...designation };
    modalRef.componentInstance.departments = this.departments;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadDesignations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Designation updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete designation
   */
  deleteDesignation(designation: Designation): void {
    this.popupService.showConfirmation({
      title: 'Delete Designation',
      message: `Are you sure you want to delete "${designation.name}"? This action cannot be undone.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.designationService.deleteDesignation(designation.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadDesignations();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Designation deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete designation.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadDesignations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Designations uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.designationService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'designations_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Toggle designation selection
   */
  toggleSelection(designationId: string): void {
    if (this.selectedDesignations.has(designationId)) {
      this.selectedDesignations.delete(designationId);
    } else {
      this.selectedDesignations.add(designationId);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    if (this.selectAll) {
      this.selectedDesignations.clear();
    } else {
      this.designations.forEach(designation => this.selectedDesignations.add(designation.id));
    }
    this.selectAll = !this.selectAll;
  }

  /**
   * Update select all state
   */
  private updateSelectAllState(): void {
    this.selectAll = this.designations.length > 0 &&
      this.designations.every(designation => this.selectedDesignations.has(designation.id));
  }

  /**
   * Bulk delete selected designations
   */
  bulkDelete(): void {
    if (this.selectedDesignations.size === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select designations to delete.'
      });
      return;
    }

    this.popupService.showConfirmation({
      title: 'Bulk Delete',
      message: `Are you sure you want to delete ${this.selectedDesignations.size} selected designations?`,
      confirmText: 'Yes, Delete All',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.popupService.showInfo({
          title: 'Feature Coming Soon',
          message: 'Bulk delete functionality will be implemented in the next update.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadDesignations();
    this.loadStatistics();
    this.loadLevels();
  }

  /**
   * Get level name
   */
  getLevelName(level: number): string {
    return this.designationService.getLevelName(level);
  }

  /**
   * Format salary range
   */
  formatSalaryRange(min?: number, max?: number): string {
    return this.designationService.formatSalaryRange(min, max);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Track by function for ngFor performance
   */
  trackByDesignationId(index: number, designation: Designation): string {
    return designation.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
