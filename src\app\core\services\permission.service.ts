import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AuthService } from './auth.service';
import { PermissionErrorService } from './permission-error.service';
import { PermissionAuditService } from './permission-audit.service';
import { PermissionCacheService } from './permission-cache.service';

export interface PermissionCheck {
  hasPermission: boolean;
  missingPermissions: string[];
  reason?: string;
}

export interface PermissionError {
  code: string;
  message: string;
  requiredPermissions: string[];
  userPermissions: string[];
}

@Injectable({
  providedIn: 'root'
})
export class PermissionService {
  private permissionsSubject = new BehaviorSubject<string[]>([]);
  public permissions$ = this.permissionsSubject.asObservable();

  constructor(
    private authService: AuthService,
    private errorService: PermissionErrorService,
    private auditService: PermissionAuditService,
    private cacheService: PermissionCacheService
  ) {
    // Subscribe to user changes and update permissions
    this.authService.currentUser$.subscribe(user => {
      const permissions = user?.permissions || [];
      this.permissionsSubject.next(permissions);
    });
  }

  /**
   * Check if user has a specific permission
   * @param permission The permission to check
   * @returns boolean indicating if user has the permission
   */
  hasPermission(permission: string): boolean {
    const user = this.authService.currentUserValue;
    if (!user) {
      console.log(`❌ PERMISSION CHECK: No user logged in for permission: ${permission}`);
      this.auditService.logPermissionCheck(
        'PERMISSION_CHECK',
        permission,
        [permission],
        [],
        false,
        { errorMessage: 'No user logged in' }
      );
      return false;
    }

    const userPermissions = user.permissions || [];

    // Check for wildcard permission (admin override)
    if (userPermissions.includes('*')) {
      console.log(`✅ PERMISSION CHECK: User has wildcard permission (*) for: ${permission}`);
      this.auditService.logPermissionCheck(
        'PERMISSION_CHECK',
        permission,
        [permission],
        userPermissions,
        true,
        { metadata: { grantReason: 'wildcard_permission' } }
      );
      return true;
    }

    // Check for exact permission match
    const hasExactPermission = userPermissions.includes(permission);

    console.log(`🔍 PERMISSION CHECK: ${permission}`);
    console.log(`  👤 User: ${user.email}`);
    console.log(`  🔑 User permissions:`, userPermissions);
    console.log(`  ✅ Has permission: ${hasExactPermission}`);

    // Log audit entry
    this.auditService.logPermissionCheck(
      'PERMISSION_CHECK',
      permission,
      [permission],
      userPermissions,
      hasExactPermission
    );

    return hasExactPermission;
  }

  /**
   * Check if user has any of the specified permissions
   * @param permissions Array of permissions to check
   * @returns boolean indicating if user has any of the permissions
   */
  hasAnyPermission(permissions: string[]): boolean {
    if (!permissions || permissions.length === 0) {
      return true; // No permissions required
    }

    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * Check if user has all of the specified permissions
   * @param permissions Array of permissions to check
   * @returns boolean indicating if user has all permissions
   */
  hasAllPermissions(permissions: string[]): boolean {
    if (!permissions || permissions.length === 0) {
      return true; // No permissions required
    }

    return permissions.every(permission => this.hasPermission(permission));
  }

  /**
   * Detailed permission check with error information
   * @param permissions Array of permissions to check
   * @param requireAll Whether all permissions are required (default: false - any permission is sufficient)
   * @returns PermissionCheck object with detailed information
   */
  checkPermissions(permissions: string[], requireAll: boolean = false): PermissionCheck {
    if (!permissions || permissions.length === 0) {
      return {
        hasPermission: true,
        missingPermissions: [],
        reason: 'No permissions required'
      };
    }

    const user = this.authService.currentUserValue;
    if (!user) {
      return {
        hasPermission: false,
        missingPermissions: permissions,
        reason: 'User not authenticated'
      };
    }

    const userPermissions = user.permissions || [];

    // Check for wildcard permission
    if (userPermissions.includes('*')) {
      return {
        hasPermission: true,
        missingPermissions: [],
        reason: 'User has wildcard permission (*)'
      };
    }

    const missingPermissions = permissions.filter(permission =>
      !userPermissions.includes(permission)
    );

    const hasPermission = requireAll
      ? missingPermissions.length === 0
      : missingPermissions.length < permissions.length;

    return {
      hasPermission,
      missingPermissions,
      reason: hasPermission
        ? 'Permission check passed'
        : `Missing required permissions: ${missingPermissions.join(', ')}`
    };
  }

  /**
   * Get current user permissions
   * @returns Array of user permissions
   */
  getUserPermissions(): string[] {
    const user = this.authService.currentUserValue;
    return user?.permissions || [];
  }

  /**
   * Check if user is admin (has wildcard permission or is_superuser)
   * @returns boolean indicating if user is admin
   */
  isAdmin(): boolean {
    const user = this.authService.currentUserValue;
    if (!user) return false;

    return user.is_superuser === true || this.hasPermission('*');
  }

  /**
   * Generate permission error for logging/debugging
   * @param requiredPermissions Array of required permissions
   * @param code Error code
   * @param message Error message
   * @returns PermissionError object
   */
  createPermissionError(
    requiredPermissions: string[],
    code: string = 'PERMISSION_DENIED',
    message: string = 'Access denied'
  ): PermissionError {
    return {
      code,
      message,
      requiredPermissions,
      userPermissions: this.getUserPermissions()
    };
  }

  /**
   * Validate API call permissions before making the request
   * @param endpoint API endpoint
   * @param requiredPermissions Required permissions for the endpoint
   * @throws Error if user doesn't have required permissions
   */
  validateApiPermissions(endpoint: string, requiredPermissions: string[]): void {
    const permissionCheck = this.checkPermissions(requiredPermissions);

    if (!permissionCheck.hasPermission) {
      const error = this.createPermissionError(
        requiredPermissions,
        'API_PERMISSION_DENIED',
        `Access denied for API endpoint: ${endpoint}`
      );

      console.error('🚫 API Permission Error:', error);
      throw new Error(`Insufficient permissions for ${endpoint}: ${permissionCheck.reason}`);
    }
  }

  /**
   * Log permission check for audit purposes
   * @param action The action being performed
   * @param permissions Required permissions
   * @param result Whether permission was granted
   */
  private logPermissionCheck(action: string, permissions: string[], result: boolean): void {
    const user = this.authService.currentUserValue;
    const logEntry = {
      timestamp: new Date().toISOString(),
      user: user?.email || 'anonymous',
      action,
      requiredPermissions: permissions,
      userPermissions: this.getUserPermissions(),
      result: result ? 'GRANTED' : 'DENIED'
    };

    console.log('📋 Permission Audit Log:', logEntry);

    // In a real application, you might want to send this to an audit service
    // this.auditService.logPermissionCheck(logEntry);
  }

  /**
   * Check permissions with audit logging
   * @param action Description of the action being performed
   * @param permissions Required permissions
   * @param requireAll Whether all permissions are required
   * @returns boolean indicating if permission is granted
   */
  checkWithAudit(action: string, permissions: string[], requireAll: boolean = false): boolean {
    const result = requireAll
      ? this.hasAllPermissions(permissions)
      : this.hasAnyPermission(permissions);

    this.logPermissionCheck(action, permissions, result);
    return result;
  }

  /**
   * Check permissions with enhanced error handling
   * @param action Description of the action being performed
   * @param permissions Required permissions
   * @param context Additional context for error handling
   * @param requireAll Whether all permissions are required
   * @returns boolean indicating if permission is granted
   */
  checkWithErrorHandling(
    action: string,
    permissions: string[],
    context?: { route?: string; component?: string },
    requireAll: boolean = false
  ): boolean {
    const result = requireAll
      ? this.hasAllPermissions(permissions)
      : this.hasAnyPermission(permissions);

    if (!result) {
      this.errorService.handlePermissionError(
        action,
        permissions,
        this.getUserPermissions(),
        context
      );
    }

    this.logPermissionCheck(action, permissions, result);
    return result;
  }

  /**
   * Require permissions or throw error
   * @param action Description of the action being performed
   * @param permissions Required permissions
   * @param context Additional context for error handling
   * @param requireAll Whether all permissions are required
   * @throws Error if permissions are not met
   */
  requirePermissions(
    action: string,
    permissions: string[],
    context?: { route?: string; component?: string },
    requireAll: boolean = false
  ): void {
    const hasPermission = this.checkWithErrorHandling(action, permissions, context, requireAll);

    if (!hasPermission) {
      throw new Error(`Permission denied for action: ${action}`);
    }
  }

  /**
   * Refresh user permissions from API
   * This method can be used to trigger a permission refresh without page reload
   * @returns Observable that completes when permissions are refreshed
   */
  refreshPermissions(): Observable<boolean> {
    // Clear permission-related cache entries
    this.cacheService.invalidatePermissionCache();

    // This will be implemented when we integrate with DynamicPermissionLoaderService
    // For now, we'll trigger a user data refresh through AuthService
    return new Observable(observer => {
      this.authService.getCurrentUser().subscribe({
        next: () => {
          observer.next(true);
          observer.complete();
        },
        error: (error) => {
          console.error('Failed to refresh permissions:', error);
          observer.next(false);
          observer.complete();
        }
      });
    });
  }

  /**
   * Get cached permission check result
   * @param permission The permission to check
   * @returns Cached result or null if not cached
   */
  getCachedPermissionCheck(permission: string): boolean | null {
    const user = this.authService.currentUserValue;
    if (!user) return null;

    const cacheKey = `permission:${user.id}:${permission}`;
    return this.cacheService.getSync<boolean>(cacheKey);
  }

  /**
   * Cache permission check result
   * @param permission The permission that was checked
   * @param result The result of the permission check
   */
  cachePermissionCheck(permission: string, result: boolean): void {
    const user = this.authService.currentUserValue;
    if (!user) return;

    const cacheKey = `permission:${user.id}:${permission}`;
    // Cache for 2 minutes for permission checks
    this.cacheService.set(cacheKey, result, 2 * 60 * 1000);
  }

  /**
   * Clear user-specific permission cache
   */
  clearUserPermissionCache(): void {
    const user = this.authService.currentUserValue;
    if (user) {
      this.cacheService.invalidateUserCache(user.id);
    }
  }
}
