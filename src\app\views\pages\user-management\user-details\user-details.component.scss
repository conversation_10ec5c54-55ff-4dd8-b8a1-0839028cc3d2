// User Details Component Styles

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;

  .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.5rem;

    .card-title {
      color: #495057;
      font-weight: 600;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Avatar styles
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;

  &.avatar-xl {
    width: 4rem;
    height: 4rem;
    font-size: 1.5rem;
  }

  .avatar-initial {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-weight: 600;
    border-radius: 50%;
  }
}

// Badge styles
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;

  &.bg-success {
    background-color: #198754 !important;
    color: #fff;
  }

  &.bg-warning {
    background-color: #ffc107 !important;
    color: #000;
  }

  &.bg-danger {
    background-color: #dc3545 !important;
    color: #fff;
  }

  &.bg-secondary {
    background-color: #6c757d !important;
    color: #fff;
  }
}

// Navigation tabs
.nav-tabs {
  border-bottom: 2px solid #dee2e6;

  .nav-item {
    margin-bottom: -2px;

    .nav-link {
      border: none;
      border-bottom: 2px solid transparent;
      color: #6c757d;
      font-weight: 500;
      padding: 1rem 1.5rem;
      transition: all 0.15s ease-in-out;

      &:hover {
        border-color: transparent;
        color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
      }

      &.active {
        color: #0d6efd;
        background-color: transparent;
        border-bottom-color: #0d6efd;
      }
    }
  }
}

// Button styles
.btn {
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  &.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;

    &:hover {
      color: #fff;
      background-color: #0d6efd;
      border-color: #0d6efd;
    }
  }

  &.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;

    &:hover {
      color: #fff;
      background-color: #6c757d;
      border-color: #6c757d;
    }
  }

  &.btn-outline-success {
    color: #198754;
    border-color: #198754;

    &:hover {
      color: #fff;
      background-color: #198754;
      border-color: #198754;
    }
  }

  &.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;

    &:hover {
      color: #000;
      background-color: #ffc107;
      border-color: #ffc107;
    }
  }

  &.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;

    &:hover {
      color: #fff;
      background-color: #dc3545;
      border-color: #dc3545;
    }
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Dropdown styles
.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 0.5rem 0;

  .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: #f8f9fa;
    }

    &.text-danger:hover {
      background-color: rgba(220, 53, 69, 0.1);
      color: #dc3545 !important;
    }

    &.text-success:hover {
      background-color: rgba(25, 135, 84, 0.1);
      color: #198754 !important;
    }
  }
}

// Information display styles
.row {
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

hr {
  margin: 1rem 0;
  color: #dee2e6;
  border: 0;
  border-top: 1px solid;
  opacity: 0.25;
}

code {
  color: #e83e8c;
  background-color: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

// Alert styles
.alert {
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;

  &.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
  }
}

// Loading spinner
.spinner-border {
  &.text-primary {
    color: #0d6efd !important;
  }
}

// Empty state styles
.icon-xxl {
  width: 4rem;
  height: 4rem;
  stroke-width: 1;
}

// Quick actions section
.d-flex.flex-wrap.gap-2 {
  gap: 0.5rem !important;

  .btn {
    margin-bottom: 0.5rem;
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .d-flex.justify-content-between.align-items-start {
    flex-direction: column;
    gap: 1rem;

    .d-flex.gap-2 {
      align-self: stretch;
      justify-content: stretch;

      .btn,
      .dropdown {
        flex: 1;
      }
    }
  }

  .avatar.avatar-xl {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .nav-tabs .nav-item .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .d-flex.align-items-center {
    flex-direction: column;
    text-align: center;
    gap: 1rem;

    .avatar {
      margin-bottom: 0 !important;
    }
  }

  .d-flex.flex-wrap.gap-2 {
    flex-direction: column;

    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }

  .row {
    .col-sm-4,
    .col-sm-8 {
      margin-bottom: 0.5rem;
    }

    .col-sm-4 {
      font-weight: 600;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #212529;
    color: #fff;

    .card-header {
      border-bottom-color: rgba(255, 255, 255, 0.125);
    }
  }

  .nav-tabs {
    border-bottom-color: rgba(255, 255, 255, 0.125);

    .nav-item .nav-link {
      color: #adb5bd;

      &:hover {
        color: #86b7fe;
        background-color: rgba(134, 183, 254, 0.1);
      }

      &.active {
        color: #86b7fe;
        border-bottom-color: #86b7fe;
      }
    }
  }

  hr {
    color: rgba(255, 255, 255, 0.125);
  }

  code {
    color: #f1618c;
    background-color: #343a40;
  }

  .alert.alert-info {
    color: #6edff6;
    background-color: #055160;
    border-color: #0c7075;
  }
}
