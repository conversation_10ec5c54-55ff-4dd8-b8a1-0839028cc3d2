import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule, NgbDropdownModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { NgxDatatableModule, ColumnMode, SortType } from '@swimlane/ngx-datatable';

// Sortable directive
export type SortColumn = keyof InstituteLoginData | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Institute Login data interface
export interface InstituteLoginData {
  id: number;
  uniqueId: string;
  leadName: string;
  companyName: string;
  projectName: string;
  productType: string;
  instituteType: string;
  legalStatus: string;
  technicalStatus: string;
  creditStatus: string;
  cashflowStatus: string;
  finalSanctionStatus: string;
  createdDate: string;
  lastUpdated: string;
}

// Sample data
const INSTITUTE_LOGIN_DATA: InstituteLoginData[] = [
  {
    id: 1,
    uniqueId: 'BC-2025-001',
    leadName: 'L&T',
    companyName: 'L&T',
    projectName: 'L&T Parel',
    productType: 'CF/PF/IF',
    instituteType: 'Government Banks',
    legalStatus: 'Completed',
    technicalStatus: 'In Progress',
    creditStatus: 'Pending',
    cashflowStatus: 'Pending',
    finalSanctionStatus: 'Pending',
    createdDate: '15 Jun 2023',
    lastUpdated: '22 Aug 2023'
  },
  {
    id: 2,
    uniqueId: 'BC-2025-002',
    leadName: 'Hiranandani',
    companyName: 'Hiranandani Developers',
    projectName: 'Lake Enclave',
    productType: 'HL/LAP/LRD/NRPL',
    instituteType: 'Private Banks',
    legalStatus: 'Completed',
    technicalStatus: 'Completed',
    creditStatus: 'Completed',
    cashflowStatus: 'In Progress',
    finalSanctionStatus: 'Pending',
    createdDate: '10 May 2023',
    lastUpdated: '15 Jul 2023'
  },
  {
    id: 3,
    uniqueId: 'BC-2025-003',
    leadName: 'Lodha Group',
    companyName: 'Lodha Developers',
    projectName: 'World Towers',
    productType: 'Insurance',
    instituteType: 'NBFCs',
    legalStatus: 'In Progress',
    technicalStatus: 'Pending',
    creditStatus: 'Pending',
    cashflowStatus: 'Pending',
    finalSanctionStatus: 'Pending',
    createdDate: '05 Apr 2023',
    lastUpdated: '20 Jun 2023'
  },
  {
    id: 4,
    uniqueId: 'BC-2025-004',
    leadName: 'Oberoi Realty',
    companyName: 'Oberoi Group',
    projectName: 'Sky City',
    productType: 'Property',
    instituteType: 'Corporate Consultancy',
    legalStatus: 'Completed',
    technicalStatus: 'Completed',
    creditStatus: 'Completed',
    cashflowStatus: 'Completed',
    finalSanctionStatus: 'Completed',
    createdDate: '12 Mar 2023',
    lastUpdated: '18 May 2023'
  },
  {
    id: 5,
    uniqueId: 'BC-2025-005',
    leadName: 'Godrej Properties',
    companyName: 'Godrej Group',
    projectName: 'Godrej Central',
    productType: 'CF/PF/IF',
    instituteType: 'Fund Houses',
    legalStatus: 'Completed',
    technicalStatus: 'Completed',
    creditStatus: 'In Progress',
    cashflowStatus: 'Pending',
    finalSanctionStatus: 'Pending',
    createdDate: '20 Feb 2023',
    lastUpdated: '10 Apr 2023'
  }
];

// Helper function for filtering
function search(text: string, pipe: DecimalPipe): InstituteLoginData[] {
  return INSTITUTE_LOGIN_DATA.filter(item => {
    const term = text.toLowerCase();
    return item.uniqueId.toLowerCase().includes(term)
        || item.leadName.toLowerCase().includes(term)
        || item.companyName.toLowerCase().includes(term)
        || item.projectName.toLowerCase().includes(term)
        || item.productType.toLowerCase().includes(term)
        || item.instituteType.toLowerCase().includes(term)
        || item.legalStatus.toLowerCase().includes(term)
        || item.technicalStatus.toLowerCase().includes(term)
        || item.creditStatus.toLowerCase().includes(term)
        || item.cashflowStatus.toLowerCase().includes(term)
        || item.finalSanctionStatus.toLowerCase().includes(term);
  });
}

// Helper function for sorting
function compare(v1: string | number, v2: string | number) {
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

@Component({
  selector: 'app-login-at-institute',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    NgbDropdownModule,
    NgbNavModule,
    FeatherIconDirective,
    NgbdSortableHeader,
    NgxDatatableModule
  ],
  templateUrl: './login-at-institute.component.html',
  styleUrl: './login-at-institute.component.scss',
  providers: [DecimalPipe]
})
export class LoginAtInstituteComponent implements OnInit {
  // View mode flag
  showListView = true;
  showDetailsForm = false;
  selectedItemId: number | null = null;

  // Original data
  instituteLoginData = INSTITUTE_LOGIN_DATA;

  // Filtered data
  filteredInstitutes: InstituteLoginData[] = [];

  // Search filter
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 5;
  collectionSize = INSTITUTE_LOGIN_DATA.length;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // NgxDatatable properties
  ColumnMode = ColumnMode;
  SortType = SortType;

  // Active tab IDs
  activeTabId = 1;
  activeAgencyTabId = 1; // For nested tabs in Agency Finalization

  // Institute login credentials tracking
  loginCredentials: { [key: number]: { username: string, password: string, notes: string } } = {};

  // Selected institutes tracking
  selectedInstitutes: number[] = [];

  // Institute types
  instituteTypes = [
    { id: 1, name: 'Government Banks' },
    { id: 2, name: 'Private Banks' },
    { id: 3, name: 'NBFCs' },
    { id: 4, name: 'Corporate Consultancy' },
    { id: 5, name: 'Fund Houses' }
  ];

  // Form fields
  initialFeesPayment: string = '';
  cibilMailStatus: string = '';
  finalSanctionReceived: string = '';
  cashFlowReceived: string = '';
  cashflowExplanation: string = '';
  goAheadReceiver: string = '';

  constructor(private pipe: DecimalPipe) {
    this.refreshInstitutes();
  }

  ngOnInit(): void {
    // Set up search filter
    this.searchTerm.valueChanges.subscribe(() => {
      this.page = 1;
      this.refreshInstitutes();
    });
  }

  refreshInstitutes() {
    // Filter by search term
    const filtered = search(this.searchTerm.value, this.pipe);
    this.collectionSize = filtered.length;

    // Slice for pagination
    this.filteredInstitutes = filtered.slice(
      (this.page - 1) * this.pageSize,
      (this.page - 1) * this.pageSize + this.pageSize
    );
  }

  onSort({ column, direction }: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.refreshInstitutes();
    } else {
      this.filteredInstitutes = [...this.filteredInstitutes].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  // NgxDatatable methods for pagination and sorting
  onPageChange(event: any) {
    this.page = event.page || event.offset + 1;
    this.pageSize = event.pageSize || event.limit;
    this.refreshInstitutes();
  }

  onTableSort(event: any) {
    const { column, newValue } = event.sorts[0];
    const direction = newValue === 'asc' ? 'asc' : 'desc';

    // Sort the data
    const filtered = search(this.searchTerm.value, this.pipe);

    // Always sort the data
    const sorted = [...filtered].sort((a, b) => {
      // Safely access properties using string indexing with type assertion
      const propA = a[column.prop as keyof InstituteLoginData] as string | number;
      const propB = b[column.prop as keyof InstituteLoginData] as string | number;
      const res = compare(propA, propB);
      return direction === 'asc' ? res : -res;
    });

    this.filteredInstitutes = sorted.slice(
      (this.page - 1) * this.pageSize,
      (this.page - 1) * this.pageSize + this.pageSize
    );
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-success';
      case 'in progress': return 'bg-warning';
      case 'pending': return 'bg-secondary';
      default: return 'bg-info';
    }
  }

  // Toggle between list view and details form
  showAddDetailsForm() {
    this.showListView = false;
    this.showDetailsForm = true;
    this.selectedItemId = null;
    this.resetForm();
  }

  editItem(id: number) {
    this.showListView = false;
    this.showDetailsForm = true;
    this.selectedItemId = id;
    this.loadItemData(id);
  }

  backToList() {
    this.showListView = true;
    this.showDetailsForm = false;
    this.selectedItemId = null;
    this.refreshInstitutes();
  }

  // Load item data for editing
  loadItemData(id: number) {
    const item = this.instituteLoginData.find(d => d.id === id);
    if (item) {
      // Set form values based on the selected item
      this.initialFeesPayment = this.mapStatusToFormValue(item.legalStatus);
      this.cibilMailStatus = this.mapStatusToFormValue(item.technicalStatus);
      this.finalSanctionReceived = this.mapStatusToFormValue(item.finalSanctionStatus);
      this.cashFlowReceived = this.mapStatusToFormValue(item.cashflowStatus);
      this.cashflowExplanation = this.mapStatusToFormValue(item.creditStatus);
      this.goAheadReceiver = this.mapStatusToFormValue(item.creditStatus);
    }
  }

  // Map status from data to form value
  mapStatusToFormValue(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'Yes';
      case 'in progress': return 'Query';
      case 'pending': return 'No';
      default: return '';
    }
  }

  // Reset form to default values
  resetForm() {
    this.initialFeesPayment = '';
    this.cibilMailStatus = '';
    this.finalSanctionReceived = '';
    this.cashFlowReceived = '';
    this.cashflowExplanation = '';
    this.goAheadReceiver = '';

    // Reset selected institutes
    this.selectedInstitutes = [];

    // Reset login credentials
    this.loginCredentials = {};
  }

  // Add or update login credentials
  saveCredentials(instituteId: number, username: string, password: string, notes: string): void {
    this.loginCredentials[instituteId] = {
      username: username,
      password: password,
      notes: notes
    };
  }

  // Check if credentials exist for an institute
  hasCredentials(instituteId: number): boolean {
    return this.loginCredentials[instituteId] !== undefined;
  }

  // Get credentials for an institute
  getCredentials(instituteId: number): { username: string, password: string, notes: string } | null {
    return this.loginCredentials[instituteId] || null;
  }
}
