import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-other-business-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  templateUrl: './other-business-modal.component.html',
  styleUrl: './other-business-modal.component.scss'
})
export class OtherBusinessModalComponent implements OnInit {
  @Input() rowId?: number;

  // Form data
  formData = {
    id: 0,
    promoterName: '',
    companyName: '',
    constitution: '',
    dateOfIncorporation: '',
    pan: '',
    cinGstNo: '',
    location: '',
    lineOfActivity: '',
    yearsOfExperience: 0,
    avgAnnualTurnover: 0,
    avgAnnualProfit: 0
  };

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize with default values
    this.formData = {
      id: 0,
      promoterName: '',
      companyName: '',
      constitution: '',
      dateOfIncorporation: '',
      pan: '',
      cinGstNo: '',
      location: '',
      lineOfActivity: '',
      yearsOfExperience: 0,
      avgAnnualTurnover: 0,
      avgAnnualProfit: 0
    };

    // If editing an existing record, populate the form
    if (this.rowId) {
      // In a real application, you would fetch the record from a service
      // For now, we'll use mock data based on the ID
      if (this.rowId === 1) {
        this.formData = {
          id: 1,
          promoterName: 'Rajesh Sharma',
          companyName: 'Sunrise Retail Solutions Pvt Ltd',
          constitution: 'Private Limited',
          dateOfIncorporation: '2018-03-10', // Format for input field
          pan: '**********',
          cinGstNo: 'U72200MH2018PTC123456',
          location: 'Shop No. 12, Andheri West, Mumbai, Maharashtra - 400053',
          lineOfActivity: 'Retail Chain & Distribution',
          yearsOfExperience: 5,
          avgAnnualTurnover: 7500000,
          avgAnnualProfit: 18.5
        };
      } else if (this.rowId === 2) {
        this.formData = {
          id: 2,
          promoterName: 'Priya Mehta & Vikram Mehta',
          companyName: 'Green Earth Exports LLP',
          constitution: 'LLP',
          dateOfIncorporation: '2019-06-22', // Format for input field
          pan: '**********',
          cinGstNo: 'AAF-1234',
          location: 'Plot 45, MIDC Industrial Area, Pune, Maharashtra - 411057',
          lineOfActivity: 'Export of Organic Products',
          yearsOfExperience: 4,
          avgAnnualTurnover: 12000000,
          avgAnnualProfit: 22.3
        };
      } else if (this.rowId === 3) {
        this.formData = {
          id: 3,
          promoterName: 'Arun Kumar',
          companyName: 'Tech Innovations Lab India Pvt Ltd',
          constitution: 'Private Limited',
          dateOfIncorporation: '2020-01-05', // Format for input field
          pan: '**********',
          cinGstNo: 'U72900KA2020PTC098765',
          location: 'No. 23, Tech Park, Whitefield, Bangalore, Karnataka - 560066',
          lineOfActivity: 'Software Development & IT Services',
          yearsOfExperience: 3,
          avgAnnualTurnover: 9000000,
          avgAnnualProfit: 25.7
        };
      }
    }
  }

  // Format date from YYYY-MM-DD to DD-MMM-YYYY format
  formatDateForDisplay(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const day = date.getDate().toString().padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${day}-${month}-${year}`;
    } catch (error) {
      return dateString; // Return original string if parsing fails
    }
  }

  // Save changes and close the modal
  saveChanges() {
    // Format dates for display in the table
    const formattedData = {
      ...this.formData,
      dateOfIncorporation: this.formatDateForDisplay(this.formData.dateOfIncorporation)
    };

    // Close the modal and pass the data back
    this.activeModal.close(formattedData);
  }

  // Cancel and close the modal
  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
