import { Routes } from '@angular/router';
import { SalesPermissionGuard } from '../../../core/guards/sales-permission.guard';

export default [
    {
        path: '',
        loadComponent: () => {
            console.log('🔄 Loading SalesListComponent...');
            return import('./sales-list/sales-list.component').then(c => {
                console.log('✅ SalesListComponent loaded successfully');
                return c.SalesListComponent;
            }).catch(error => {
                console.error('❌ Error loading SalesListComponent:', error);
                throw error;
            });
        },
        canActivate: [SalesPermissionGuard],
        data: {
            permissions: ['sales:read', 'sales:manage'] // Require sales read or manage permissions
        }
    },
    {
        path: 'add-sales',
        loadComponent: () => import('./sales-list/sales-list.component').then(c => c.SalesListComponent),
        canActivate: [SalesPermissionGuard],
        data: {
            showAddSalesForm: true,
            permissions: ['sales:create', 'sales:manage'] // Require sales create or manage permissions
        }
    },
    {
        path: 'view/:id',
        loadComponent: () => import('./view-lead-details/view-lead-details.component').then(c => c.ViewLeadDetailsComponent),
        canActivate: [SalesPermissionGuard],
        data: {
            permissions: ['sales:read', 'sales:manage'] // Require sales read or manage permissions
        }
    },

] as Routes;