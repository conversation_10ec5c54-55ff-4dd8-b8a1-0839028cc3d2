import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Source interfaces
export interface Source {
  id: string;
  name: string;
  code: string;
  description?: string;
  source_type: 'website' | 'social_media' | 'email' | 'referral' | 'advertising' | 'event' | 'direct' | 'partner' | 'cold_outreach' | 'content_marketing' | 'seo' | 'ppc' | 'affiliate' | 'print' | 'radio' | 'tv' | 'other';
  source_category: 'digital' | 'traditional' | 'word_of_mouth' | 'paid' | 'organic' | 'partnership' | 'internal';
  attribution_model: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based' | 'custom';
  tracking_parameters?: TrackingParameter[];
  conversion_tracking?: ConversionTracking;
  cost_tracking?: CostTracking;
  campaign_integration?: CampaignIntegration[];
  analytics_integration?: AnalyticsIntegration;
  quality_score?: number;
  lead_score_modifier?: number;
  auto_assignment_rules?: AutoAssignmentRule[];
  follow_up_templates?: FollowUpTemplate[];
  tags?: string[];
  color_code?: string;
  icon?: string;
  is_active: boolean;
  is_default: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  leads_count?: number;
  conversion_rate?: number;
  total_cost?: number;
  cost_per_lead?: number;
  roi?: number;
}

export interface TrackingParameter {
  id: string;
  parameter_name: string;
  parameter_value?: string;
  is_required: boolean;
  description?: string;
}

export interface ConversionTracking {
  track_conversions: boolean;
  conversion_goals?: ConversionGoal[];
  attribution_window_days: number;
  cross_device_tracking: boolean;
  offline_conversion_tracking: boolean;
}

export interface ConversionGoal {
  id: string;
  name: string;
  type: 'lead_created' | 'opportunity_created' | 'deal_closed' | 'revenue_generated' | 'custom_event';
  value?: number;
  currency?: string;
  weight: number;
}

export interface CostTracking {
  track_costs: boolean;
  cost_model: 'cpc' | 'cpm' | 'cpa' | 'fixed' | 'percentage' | 'custom';
  cost_per_unit?: number;
  currency?: string;
  budget_limit?: number;
  auto_budget_alerts: boolean;
}

export interface CampaignIntegration {
  id: string;
  platform: string;
  campaign_id?: string;
  campaign_name?: string;
  integration_type: 'api' | 'webhook' | 'manual' | 'import';
  sync_frequency: 'real_time' | 'hourly' | 'daily' | 'weekly' | 'manual';
  last_sync?: string;
  is_active: boolean;
}

export interface AnalyticsIntegration {
  google_analytics?: {
    tracking_id?: string;
    view_id?: string;
    custom_dimensions?: { [key: string]: string };
  };
  facebook_pixel?: {
    pixel_id?: string;
    custom_events?: string[];
  };
  linkedin_insight?: {
    partner_id?: string;
  };
  custom_tracking?: {
    platform: string;
    tracking_code?: string;
    parameters?: { [key: string]: any };
  }[];
}

export interface AutoAssignmentRule {
  id: string;
  name: string;
  condition: any;
  assignee_id?: string;
  team_id?: string;
  priority: number;
  is_active: boolean;
}

export interface FollowUpTemplate {
  id: string;
  name: string;
  template_type: 'email' | 'sms' | 'call' | 'task';
  delay_hours: number;
  template_content?: string;
  is_active: boolean;
}

export interface SourceCreate {
  name: string;
  code: string;
  description?: string;
  source_type: 'website' | 'social_media' | 'email' | 'referral' | 'advertising' | 'event' | 'direct' | 'partner' | 'cold_outreach' | 'content_marketing' | 'seo' | 'ppc' | 'affiliate' | 'print' | 'radio' | 'tv' | 'other';
  source_category: 'digital' | 'traditional' | 'word_of_mouth' | 'paid' | 'organic' | 'partnership' | 'internal';
  attribution_model: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based' | 'custom';
  tracking_parameters?: TrackingParameter[];
  conversion_tracking?: ConversionTracking;
  cost_tracking?: CostTracking;
  campaign_integration?: CampaignIntegration[];
  analytics_integration?: AnalyticsIntegration;
  quality_score?: number;
  lead_score_modifier?: number;
  auto_assignment_rules?: AutoAssignmentRule[];
  follow_up_templates?: FollowUpTemplate[];
  tags?: string[];
  color_code?: string;
  icon?: string;
  is_active?: boolean;
  is_default?: boolean;
  display_order?: number;
}

export interface SourceUpdate {
  name?: string;
  code?: string;
  description?: string;
  source_type?: 'website' | 'social_media' | 'email' | 'referral' | 'advertising' | 'event' | 'direct' | 'partner' | 'cold_outreach' | 'content_marketing' | 'seo' | 'ppc' | 'affiliate' | 'print' | 'radio' | 'tv' | 'other';
  source_category?: 'digital' | 'traditional' | 'word_of_mouth' | 'paid' | 'organic' | 'partnership' | 'internal';
  attribution_model?: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based' | 'custom';
  tracking_parameters?: TrackingParameter[];
  conversion_tracking?: ConversionTracking;
  cost_tracking?: CostTracking;
  campaign_integration?: CampaignIntegration[];
  analytics_integration?: AnalyticsIntegration;
  quality_score?: number;
  lead_score_modifier?: number;
  auto_assignment_rules?: AutoAssignmentRule[];
  follow_up_templates?: FollowUpTemplate[];
  tags?: string[];
  color_code?: string;
  icon?: string;
  is_active?: boolean;
  is_default?: boolean;
  display_order?: number;
}

export interface SourceStatistics {
  total_sources: number;
  active_sources: number;
  inactive_sources: number;
  sources_by_type: { [type: string]: number };
  sources_by_category: { [category: string]: number };
  total_leads: number;
  total_conversions: number;
  total_cost: number;
  average_cost_per_lead: number;
  average_conversion_rate: number;
  top_performing_sources: Source[];
  recent_sources: Source[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SourceService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/sources/`;
  private sourcesSubject = new BehaviorSubject<Source[]>([]);
  public sources$ = this.sourcesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all sources with optional filtering and pagination (returns APIResponse)
   */
  getSourcesWithResponse(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    source_type?: string;
    source_category?: string;
    attribution_model?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<Source[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<Source[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.sourcesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get all sources (backward compatibility) - returns Source[] directly
   */
  getSources(skip: number = 0, limit: number = 100, filter: any = {}): Observable<Source[]> {
    const params = {
      page: Math.floor(skip / limit) + 1,
      per_page: limit,
      search: filter.search || filter.name,
      is_active: filter.is_active,
      source_type: filter.source_type,
      source_category: filter.source_category
    };

    return this.getSourcesWithResponse(params).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      })
    );
  }

  /**
   * Get source by ID
   */
  getSourceById(id: string): Observable<APIResponse<Source>> {
    return this.http.get<APIResponse<Source>>(`${this.baseUrl}${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all active sources for dropdown usage
   */
  getActiveSources(): Observable<Source[]> {
    return this.getSources(0, 1000, { is_active: true });
  }

  /**
   * Create new source
   */
  createSource(source: SourceCreate): Observable<APIResponse<Source>> {
    return this.http.post<APIResponse<Source>>(this.baseUrl, source)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSources();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update source
   */
  updateSource(id: string, source: SourceUpdate): Observable<APIResponse<Source>> {
    return this.http.put<APIResponse<Source>>(`${this.baseUrl}${id}`, source)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSources();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete source
   */
  deleteSource(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSources();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted source
   */
  restoreSource(id: string): Observable<APIResponse<Source>> {
    return this.http.post<APIResponse<Source>>(`${this.baseUrl}${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSources();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get source statistics
   */
  getSourceStatistics(): Observable<APIResponse<SourceStatistics>> {
    return this.http.get<APIResponse<SourceStatistics>>(`${this.baseUrl}statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload sources
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshSources();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Sync campaign data
   */
  syncCampaignData(sourceId: string, platformId: string): Observable<APIResponse<any>> {
    return this.http.post<APIResponse<any>>(`${this.baseUrl}${sourceId}/sync-campaign`, { platform_id: platformId })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get attribution report
   */
  getAttributionReport(params?: {
    source_ids?: string[];
    date_from?: string;
    date_to?: string;
    attribution_model?: string;
  }): Observable<APIResponse<any>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => httpParams = httpParams.append(key, v.toString()));
          } else {
            httpParams = httpParams.set(key, value.toString());
          }
        }
      });
    }

    return this.http.get<APIResponse<any>>(`${this.baseUrl}attribution-report`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Refresh sources data
   */
  refreshSources(): void {
    this.getSourcesWithResponse().subscribe();
  }

  /**
   * Clear sources cache
   */
  clearCache(): void {
    this.sourcesSubject.next([]);
  }

  /**
   * Get source types
   */
  getSourceTypes(): { value: string; label: string; description: string; icon: string }[] {
    return [
      { value: 'website', label: 'Website', description: 'Direct website visits and organic traffic', icon: 'globe' },
      { value: 'social_media', label: 'Social Media', description: 'Facebook, LinkedIn, Twitter, Instagram', icon: 'share-2' },
      { value: 'email', label: 'Email Marketing', description: 'Email campaigns and newsletters', icon: 'mail' },
      { value: 'referral', label: 'Referral', description: 'Customer and partner referrals', icon: 'users' },
      { value: 'advertising', label: 'Online Advertising', description: 'Google Ads, Facebook Ads, display ads', icon: 'target' },
      { value: 'event', label: 'Events', description: 'Trade shows, webinars, conferences', icon: 'calendar' },
      { value: 'direct', label: 'Direct', description: 'Direct contact, walk-ins, phone calls', icon: 'phone' },
      { value: 'partner', label: 'Partner', description: 'Channel partners and affiliates', icon: 'handshake' },
      { value: 'cold_outreach', label: 'Cold Outreach', description: 'Cold calls, cold emails, prospecting', icon: 'phone-call' },
      { value: 'content_marketing', label: 'Content Marketing', description: 'Blog posts, whitepapers, ebooks', icon: 'file-text' },
      { value: 'seo', label: 'SEO', description: 'Search engine optimization', icon: 'search' },
      { value: 'ppc', label: 'PPC', description: 'Pay-per-click advertising', icon: 'credit-card' },
      { value: 'affiliate', label: 'Affiliate', description: 'Affiliate marketing programs', icon: 'link-2' },
      { value: 'print', label: 'Print Media', description: 'Newspapers, magazines, brochures', icon: 'printer' },
      { value: 'radio', label: 'Radio', description: 'Radio advertisements and sponsorships', icon: 'radio' },
      { value: 'tv', label: 'Television', description: 'TV commercials and sponsorships', icon: 'tv' },
      { value: 'other', label: 'Other', description: 'Other sources not listed above', icon: 'more-horizontal' }
    ];
  }

  /**
   * Get source categories
   */
  getSourceCategories(): { value: string; label: string; description: string; color: string }[] {
    return [
      { value: 'digital', label: 'Digital', description: 'Online and digital channels', color: '#007bff' },
      { value: 'traditional', label: 'Traditional', description: 'Traditional media and offline channels', color: '#6c757d' },
      { value: 'word_of_mouth', label: 'Word of Mouth', description: 'Referrals and recommendations', color: '#28a745' },
      { value: 'paid', label: 'Paid', description: 'Paid advertising and promotions', color: '#ffc107' },
      { value: 'organic', label: 'Organic', description: 'Natural and unpaid traffic', color: '#20c997' },
      { value: 'partnership', label: 'Partnership', description: 'Partner and affiliate channels', color: '#fd7e14' },
      { value: 'internal', label: 'Internal', description: 'Internal sales and marketing efforts', color: '#6f42c1' }
    ];
  }

  /**
   * Get attribution models
   */
  getAttributionModels(): { value: string; label: string; description: string }[] {
    return [
      { value: 'first_touch', label: 'First Touch', description: 'Credit to the first interaction' },
      { value: 'last_touch', label: 'Last Touch', description: 'Credit to the last interaction before conversion' },
      { value: 'linear', label: 'Linear', description: 'Equal credit to all touchpoints' },
      { value: 'time_decay', label: 'Time Decay', description: 'More credit to recent interactions' },
      { value: 'position_based', label: 'Position Based', description: 'More credit to first and last interactions' },
      { value: 'custom', label: 'Custom', description: 'Custom attribution model' }
    ];
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Source service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
