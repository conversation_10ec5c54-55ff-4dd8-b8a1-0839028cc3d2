import { Directive, ElementRef, Input, OnInit, OnDestroy, Renderer2 } from '@angular/core';
import { NgControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HolidayService } from '../services/holiday.service';
import { DateFilterService } from '../services/date-filter.service';

@Directive({
  selector: '[appRestrictedDatePicker]',
  standalone: true
})
export class RestrictedDatePickerDirective implements OnInit, OnDestroy {
  @Input() restrictWeekends = true;
  @Input() restrictHolidays = true;
  @Input() showTooltips = true;

  private destroy$ = new Subject<void>();
  private holidays: string[] = [];

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private ngControl: NgControl,
    private holidayService: HolidayService,
    private dateFilterService: DateFilterService
  ) {}

  ngOnInit(): void {
    console.log('📅 RESTRICTED DATE PICKER - Initializing directive');
    
    // Load holidays
    this.loadHolidays();
    
    // Set up date input restrictions
    this.setupDateRestrictions();
    
    // Add CSS classes for styling
    this.addStyling();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadHolidays(): void {
    this.holidayService.getCachedHolidays()
      .pipe(takeUntil(this.destroy$))
      .subscribe(holidays => {
        this.holidays = holidays.map(h => h.holiday_date);
        console.log('📅 RESTRICTED DATE PICKER - Holidays loaded:', this.holidays.length);
      });
  }

  private setupDateRestrictions(): void {
    const inputElement = this.el.nativeElement;
    
    // Set min date to today to prevent past dates
    const today = new Date().toISOString().split('T')[0];
    this.renderer.setAttribute(inputElement, 'min', today);
    
    // Add event listeners
    this.renderer.listen(inputElement, 'input', (event) => {
      this.validateSelectedDate(event.target.value);
    });

    this.renderer.listen(inputElement, 'change', (event) => {
      this.validateSelectedDate(event.target.value);
    });

    // If using Angular forms, watch for value changes
    if (this.ngControl && this.ngControl.valueChanges) {
      this.ngControl.valueChanges
        .pipe(takeUntil(this.destroy$))
        .subscribe(value => {
          if (value) {
            this.validateSelectedDate(value);
          }
        });
    }
  }

  private validateSelectedDate(dateValue: string): void {
    if (!dateValue) return;

    try {
      const selectedDate = new Date(dateValue);
      
      // Check if it's a valid date
      if (isNaN(selectedDate.getTime())) {
        return;
      }

      let isRestricted = false;
      let restrictionReason = '';

      // Check weekend restriction
      if (this.restrictWeekends && this.holidayService.isWeekend(selectedDate)) {
        isRestricted = true;
        restrictionReason = 'Weekends are not allowed';
      }

      // Check holiday restriction
      if (this.restrictHolidays && !isRestricted) {
        const dateStr = selectedDate.toISOString().split('T')[0];
        if (this.holidays.includes(dateStr)) {
          isRestricted = true;
          restrictionReason = 'Holidays are not allowed';
        }
      }

      if (isRestricted) {
        console.log(`🚫 RESTRICTED DATE PICKER - Blocked date: ${dateValue} (${restrictionReason})`);
        
        // Clear the input
        this.clearInput();
        
        // Show warning message
        this.showWarningMessage(restrictionReason);
        
        // Add error styling
        this.addErrorStyling();
      } else {
        // Remove error styling if date is valid
        this.removeErrorStyling();
      }
    } catch (error) {
      console.error('📅 RESTRICTED DATE PICKER - Error validating date:', error);
    }
  }

  private clearInput(): void {
    const inputElement = this.el.nativeElement;
    inputElement.value = '';
    
    // Update form control if available
    if (this.ngControl && this.ngControl.control) {
      this.ngControl.control.setValue('');
    }
  }

  private showWarningMessage(message: string): void {
    if (!this.showTooltips) return;

    // Create or update warning message element
    let warningElement = this.el.nativeElement.parentElement?.querySelector('.date-restriction-warning');
    
    if (!warningElement) {
      warningElement = this.renderer.createElement('div');
      this.renderer.addClass(warningElement, 'date-restriction-warning');
      this.renderer.setStyle(warningElement, 'color', '#dc3545');
      this.renderer.setStyle(warningElement, 'font-size', '12px');
      this.renderer.setStyle(warningElement, 'margin-top', '4px');
      this.renderer.insertAfter(warningElement, this.el.nativeElement);
    }
    
    this.renderer.setProperty(warningElement, 'textContent', message);
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (warningElement && warningElement.parentElement) {
        this.renderer.removeChild(warningElement.parentElement, warningElement);
      }
    }, 3000);
  }

  private addErrorStyling(): void {
    this.renderer.addClass(this.el.nativeElement, 'is-invalid');
    this.renderer.setStyle(this.el.nativeElement, 'border-color', '#dc3545');
  }

  private removeErrorStyling(): void {
    this.renderer.removeClass(this.el.nativeElement, 'is-invalid');
    this.renderer.removeStyle(this.el.nativeElement, 'border-color');
  }

  private addStyling(): void {
    // Add custom CSS class for restricted date picker
    this.renderer.addClass(this.el.nativeElement, 'restricted-date-picker');
    
    // Add cursor style to indicate restrictions
    this.renderer.setStyle(this.el.nativeElement, 'cursor', 'pointer');
  }
}

// CSS styles that should be added to global styles or component styles
export const RESTRICTED_DATE_PICKER_STYLES = `
.restricted-date-picker {
  position: relative;
}

.restricted-date-picker:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.restricted-date-picker.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.date-restriction-warning {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Calendar date styling for different date types */
.calendar-date {
  position: relative;
  cursor: pointer;
}

.calendar-date.weekend {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
}

.calendar-date.holiday {
  background-color: #fff3cd !important;
  color: #856404 !important;
  cursor: not-allowed !important;
}

.calendar-date.available {
  background-color: #ffffff;
  color: #212529;
  cursor: pointer;
}

.calendar-date.available:hover {
  background-color: #e9ecef;
}

.calendar-date.selected {
  background-color: #007bff !important;
  color: #ffffff !important;
}

/* Legend styles */
.calendar-legend {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #dee2e6;
}

.legend-color.available {
  background-color: #ffffff;
}

.legend-color.weekend {
  background-color: #f8f9fa;
}

.legend-color.holiday {
  background-color: #fff3cd;
}
`;
