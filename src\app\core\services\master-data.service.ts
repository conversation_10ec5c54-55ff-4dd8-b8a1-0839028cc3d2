import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Master Data interfaces based on OpenAPI specification
 */
export interface MasterDataItem {
  id: string;
  name: string;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface MasterDataCreate {
  name: string;
}

export interface BulkUploadError {
  row: number;
  field?: string;
  error: string;
  value?: string;
}

export interface BulkUploadResponse {
  success: boolean;
  data: {
    total: number;
    success_count: number;
    error_count: number;
    errors: BulkUploadError[];
  };
  error?: any;
  meta?: any;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * Master Data Service
 * Handles various master data entities including banks, NBFCs,
 * institutes, and corporate consultancies.
 */
@Injectable({
  providedIn: 'root'
})
export class MasterDataService {
  private baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // Private Banks Management

  /**
   * Get all private banks
   * GET /api/v1/private-banks/
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of private banks list
   */
  getPrivateBanks(skip: number = 0, limit: number = 10, includeDeleted: boolean = false): Observable<MasterDataItem[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<MasterDataItem[]>>(`${this.baseUrl}/api/v1/private-banks/`, { params }).pipe(
      map(response => response.success && response.data ? response.data : []),
      catchError(this.handleError('getPrivateBanks', []))
    );
  }

  /**
   * Create a new private bank
   * POST /api/v1/private-banks/
   * @param bank Private bank data
   * @returns Observable of created private bank
   */
  createPrivateBank(bank: MasterDataCreate): Observable<MasterDataItem> {
    return this.http.post<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/private-banks/`, bank).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create private bank');
      }),
      catchError(this.handleError<MasterDataItem>('createPrivateBank'))
    );
  }

  /**
   * Bulk upload private banks
   * POST /api/v1/private-banks/bulk-upload
   * @param file File containing private bank data
   * @returns Observable of bulk upload response
   */
  bulkUploadPrivateBanks(file: File): Observable<BulkUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/api/v1/private-banks/bulk-upload`, formData).pipe(
      catchError(this.handleError<BulkUploadResponse>('bulkUploadPrivateBanks'))
    );
  }

  // NBFCs Management

  /**
   * Get all NBFCs
   * GET /api/v1/nbfcs/
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of NBFCs list
   */
  getNBFCs(skip: number = 0, limit: number = 10, includeDeleted: boolean = false): Observable<MasterDataItem[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<MasterDataItem[]>>(`${this.baseUrl}/api/v1/nbfcs/`, { params }).pipe(
      map(response => response.success && response.data ? response.data : []),
      catchError(this.handleError('getNBFCs', []))
    );
  }

  /**
   * Create a new NBFC
   * POST /api/v1/nbfcs/
   * @param nbfc NBFC data
   * @returns Observable of created NBFC
   */
  createNBFC(nbfc: MasterDataCreate): Observable<MasterDataItem> {
    return this.http.post<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/nbfcs/`, nbfc).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create NBFC');
      }),
      catchError(this.handleError<MasterDataItem>('createNBFC'))
    );
  }

  /**
   * Bulk upload NBFCs
   * POST /api/v1/nbfcs/bulk-upload
   * @param file File containing NBFC data
   * @returns Observable of bulk upload response
   */
  bulkUploadNBFCs(file: File): Observable<BulkUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/api/v1/nbfcs/bulk-upload`, formData).pipe(
      catchError(this.handleError<BulkUploadResponse>('bulkUploadNBFCs'))
    );
  }

  // Institutes Management

  /**
   * Get all institutes
   * GET /api/v1/institutes/
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of institutes list
   */
  getInstitutes(skip: number = 0, limit: number = 10, includeDeleted: boolean = false): Observable<MasterDataItem[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<MasterDataItem[]>>(`${this.baseUrl}/api/v1/institutes/`, { params }).pipe(
      map(response => response.success && response.data ? response.data : []),
      catchError(this.handleError('getInstitutes', []))
    );
  }

  /**
   * Create a new institute
   * POST /api/v1/institutes/
   * @param institute Institute data
   * @returns Observable of created institute
   */
  createInstitute(institute: MasterDataCreate): Observable<MasterDataItem> {
    return this.http.post<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/institutes/`, institute).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create institute');
      }),
      catchError(this.handleError<MasterDataItem>('createInstitute'))
    );
  }

  /**
   * Bulk upload institutes
   * POST /api/v1/institutes/bulk-upload
   * @param file File containing institute data
   * @returns Observable of bulk upload response
   */
  bulkUploadInstitutes(file: File): Observable<BulkUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/api/v1/institutes/bulk-upload`, formData).pipe(
      catchError(this.handleError<BulkUploadResponse>('bulkUploadInstitutes'))
    );
  }

  // Corporate Consultancies Management

  /**
   * Get all corporate consultancies
   * GET /api/v1/corporate-consultancies/
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of corporate consultancies list
   */
  getCorporateConsultancies(skip: number = 0, limit: number = 10, includeDeleted: boolean = false): Observable<MasterDataItem[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<MasterDataItem[]>>(`${this.baseUrl}/api/v1/corporate-consultancies/`, { params }).pipe(
      map(response => response.success && response.data ? response.data : []),
      catchError(this.handleError('getCorporateConsultancies', []))
    );
  }

  /**
   * Create a new corporate consultancy
   * POST /api/v1/corporate-consultancies/
   * @param consultancy Corporate consultancy data
   * @returns Observable of created corporate consultancy
   */
  createCorporateConsultancy(consultancy: MasterDataCreate): Observable<MasterDataItem> {
    return this.http.post<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/corporate-consultancies/`, consultancy).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create corporate consultancy');
      }),
      catchError(this.handleError<MasterDataItem>('createCorporateConsultancy'))
    );
  }

  /**
   * Bulk upload corporate consultancies
   * POST /api/v1/corporate-consultancies/bulk-upload
   * @param file File containing corporate consultancy data
   * @returns Observable of bulk upload response
   */
  bulkUploadCorporateConsultancies(file: File): Observable<BulkUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/api/v1/corporate-consultancies/bulk-upload`, formData).pipe(
      catchError(this.handleError<BulkUploadResponse>('bulkUploadCorporateConsultancies'))
    );
  }

  // Fund Houses Management

  /**
   * Get all fund houses
   * GET /api/v1/fund-houses/
   * @param skip Number of records to skip
   * @param limit Maximum number of records to return
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of fund houses list
   */
  getFundHouses(skip: number = 0, limit: number = 10, includeDeleted: boolean = false): Observable<MasterDataItem[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<MasterDataItem[]>>(`${this.baseUrl}/api/v1/fund-houses/`, { params }).pipe(
      map(response => response.success && response.data ? response.data : []),
      catchError(this.handleError('getFundHouses', []))
    );
  }

  /**
   * Create a new fund house
   * POST /api/v1/fund-houses/
   * @param fundHouse Fund house data
   * @returns Observable of created fund house
   */
  createFundHouse(fundHouse: MasterDataCreate): Observable<MasterDataItem> {
    return this.http.post<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/fund-houses/`, fundHouse).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to create fund house');
      }),
      catchError(this.handleError<MasterDataItem>('createFundHouse'))
    );
  }

  /**
   * Bulk upload fund houses
   * POST /api/v1/fund-houses/bulk-upload
   * @param file File containing fund house data
   * @returns Observable of bulk upload response
   */
  bulkUploadFundHouses(file: File): Observable<BulkUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<BulkUploadResponse>(`${this.baseUrl}/api/v1/fund-houses/bulk-upload`, formData).pipe(
      catchError(this.handleError<BulkUploadResponse>('bulkUploadFundHouses'))
    );
  }

  // Generic CRUD operations for master data entities

  /**
   * Get a specific master data item by ID
   * @param entityType Entity type (private-banks, nbfcs, institutes, corporate-consultancies)
   * @param id Item ID
   * @param includeDeleted Whether to include soft-deleted records
   * @returns Observable of master data item
   */
  getMasterDataItem(entityType: string, id: string, includeDeleted: boolean = false): Observable<MasterDataItem> {
    const params = new HttpParams().set('include_deleted', includeDeleted.toString());

    return this.http.get<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/${entityType}/${id}`, { params }).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(`${entityType} item not found`);
      }),
      catchError(this.handleError<MasterDataItem>('getMasterDataItem'))
    );
  }

  /**
   * Update a master data item
   * @param entityType Entity type (private-banks, nbfcs, institutes, corporate-consultancies)
   * @param id Item ID
   * @param data Updated data
   * @returns Observable of updated master data item
   */
  updateMasterDataItem(entityType: string, id: string, data: MasterDataCreate): Observable<MasterDataItem> {
    return this.http.put<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/${entityType}/${id}`, data).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(`Failed to update ${entityType} item`);
      }),
      catchError(this.handleError<MasterDataItem>('updateMasterDataItem'))
    );
  }

  /**
   * Delete a master data item (soft delete)
   * @param entityType Entity type (private-banks, nbfcs, institutes, corporate-consultancies)
   * @param id Item ID
   * @param hardDelete Whether to permanently delete
   * @returns Observable of operation result
   */
  deleteMasterDataItem(entityType: string, id: string, hardDelete: boolean = false): Observable<boolean> {
    const params = new HttpParams().set('hard_delete', hardDelete.toString());

    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/api/v1/${entityType}/${id}`, { params }).pipe(
      map((response: APIResponse<boolean>) => {
        if (response.success) {
          return response.data;
        }
        throw new Error(`Failed to delete ${entityType} item`);
      }),
      catchError(this.handleError<boolean>('deleteMasterDataItem'))
    );
  }

  /**
   * Restore a soft-deleted master data item
   * @param entityType Entity type (private-banks, nbfcs, institutes, corporate-consultancies)
   * @param id Item ID
   * @returns Observable of restored master data item
   */
  restoreMasterDataItem(entityType: string, id: string): Observable<MasterDataItem> {
    return this.http.post<APIResponse<MasterDataItem>>(`${this.baseUrl}/api/v1/${entityType}/${id}/restore`, {}).pipe(
      map((response: APIResponse<MasterDataItem>) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(`Failed to restore ${entityType} item`);
      }),
      catchError(this.handleError<MasterDataItem>('restoreMasterDataItem'))
    );
  }

  /**
   * Export master data to Excel
   * @param entityType Entity type (private-banks, nbfcs, institutes, corporate-consultancies)
   * @returns Observable of Excel file blob
   */
  exportMasterData(entityType: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/api/v1/${entityType}/export`, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    }).pipe(
      catchError(this.handleError<Blob>('exportMasterData'))
    );
  }

  /**
   * Search master data items
   * @param entityType Entity type (private-banks, nbfcs, institutes, corporate-consultancies)
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching master data items
   */
  searchMasterData(entityType: string, query: string, limit: number = 20): Observable<MasterDataItem[]> {
    const params = new HttpParams()
      .set('q', query)
      .set('limit', limit.toString());

    return this.http.get<APIResponse<MasterDataItem[]>>(`${this.baseUrl}/api/v1/${entityType}/search`, { params }).pipe(
      map(response => response.success && response.data ? response.data : []),
      catchError(this.handleError('searchMasterData', []))
    );
  }

  // Convenience methods for specific entity types

  /**
   * Search private banks
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching private banks
   */
  searchPrivateBanks(query: string, limit: number = 20): Observable<MasterDataItem[]> {
    return this.searchMasterData('private-banks', query, limit);
  }

  /**
   * Search NBFCs
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching NBFCs
   */
  searchNBFCs(query: string, limit: number = 20): Observable<MasterDataItem[]> {
    return this.searchMasterData('nbfcs', query, limit);
  }

  /**
   * Search institutes
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching institutes
   */
  searchInstitutes(query: string, limit: number = 20): Observable<MasterDataItem[]> {
    return this.searchMasterData('institutes', query, limit);
  }

  /**
   * Search corporate consultancies
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching corporate consultancies
   */
  searchCorporateConsultancies(query: string, limit: number = 20): Observable<MasterDataItem[]> {
    return this.searchMasterData('corporate-consultancies', query, limit);
  }

  /**
   * Search fund houses
   * @param query Search query
   * @param limit Maximum number of results
   * @returns Observable of matching fund houses
   */
  searchFundHouses(query: string, limit: number = 20): Observable<MasterDataItem[]> {
    return this.searchMasterData('fund-houses', query, limit);
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
