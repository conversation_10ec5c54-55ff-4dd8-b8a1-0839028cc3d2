//
// Root
//

// Note: Custom variable values only support SassScript inside `#{}`.

// Light mode
@include color-mode(light) {
  --#{$prefix}app-bg: #{$app-bg};

  --#{$prefix}box-shadow: #{$box-shadow};
  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};
  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};



  --#{$prefix}sidebar-box-shadow-color: #{$sidebar-box-shadow-color};
  --#{$prefix}sidebar-color: #{$sidebar-color};
  --#{$prefix}sidebar-color-rgb: #{to-rgb($sidebar-color)};

  --#{$prefix}navbar-box-shadow-color: #{$navbar-box-shadow-color};
  --#{$prefix}navbar-item-color: #{$navbar-item-color};
}

// Dark mode
@include color-mode(dark) {
  --#{$prefix}app-bg: #{$app-bg-dark};

  --#{$prefix}box-shadow: #{$box-shadow-dark};
  --#{$prefix}box-shadow-sm: #{$box-shadow-sm-dark};
  --#{$prefix}box-shadow-lg: #{$box-shadow-lg-dark};



  --#{$prefix}sidebar-box-shadow-color: #{$sidebar-box-shadow-color-dark};
  --#{$prefix}sidebar-color: #{$sidebar-color-dark};
  --#{$prefix}sidebar-color-rgb: #{to-rgb($sidebar-color-dark)};

  --#{$prefix}navbar-box-shadow-color: #{$navbar-box-shadow-color-dark};
  --#{$prefix}navbar-item-color: #{$navbar-item-color-dark};
}