import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

interface CustomerData {
  id: number;
  customerName: string;
  contactNo: string;
  resiAddress: string;
}

interface PropertyData {
  UniqueCode: string;
  category: string;
  customers: CustomerData[];
  requiredDeal: string;
  dealFrom: string;
  propertyDescription: string;
  configuration: string;
  location: string;
  sublocation: string;
  googleLink: string;
  propertyPhotos: string;
  pricePerUnit: string;
  lumpSumPrice: number;
  autoCalculated: number;
  negotiationIfAny: string;
  // JV Syndication specific fields
  dpRoad: string;
  plotShape: string;
  jvDepositAmount: number;
  jvDepositType: string;
  onnAmount: number;
  jvRatio: string;
  totalPotentialOfPlot: number;
  totalProjectCosting: number;
  totalProjectRevenue: number;
  netSurplus: number;
  projectFunding: number;
}

interface DropdownOption {
  value: string;
  label: string;
}

@Component({
  selector: 'app-property',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    CommonModule
  ],
  templateUrl: './property.component.html',
  styleUrl: './property.component.scss'
})
export class PropertyComponent implements OnInit {
  @Input() selectedProductSubType: string = '';
  @Output() submitSales = new EventEmitter<any>();

  propertyForm: FormGroup;
  isFormSubmitted: boolean = false;
  propertyList: PropertyData[] = [];
  nextUniqueCode: number = 1;

  // Customer management
  customers: CustomerData[] = [];
  nextCustomerId: number = 1;
  currentCustomer: CustomerData = {
    id: 1,
    customerName: '',
    contactNo: '',
    resiAddress: ''
  };

  // Conditional dropdown options
  dealFromOptions: DropdownOption[] = [];
  propertyDescriptionOptions: DropdownOption[] = [];
  configurationOptions: DropdownOption[] = [];
  pricePerUnitOptions: DropdownOption[] = [];

  // Deal From options based on Required Deal selection
  dealFromOptionsMap: { [key: string]: DropdownOption[] } = {
    'Purchase': [{ value: 'Purchaser', label: 'Purchaser' }],
    'Sale': [{ value: 'Seller', label: 'Seller' }],
    'Lease_Rental': [
      { value: 'Licencee', label: 'Licencee' },
      { value: 'Licensor', label: 'Licensor' }
    ],
    'JV_Syndication': [
      { value: 'From_Developer', label: 'From Developer' },
      { value: 'Landowner', label: 'Landowner' }
    ]
  };

  // Property Description options based on Required Deal selection
  propertyDescriptionOptionsMap: { [key: string]: DropdownOption[] } = {
    'Purchase': [
      { value: 'Bunglow', label: 'Bunglow' },
      { value: 'Land', label: 'Land' },
      { value: 'TDR', label: 'TDR' },
      { value: 'Duplex', label: 'Duplex' },
      { value: 'Villa', label: 'Villa' },
      { value: 'Shop', label: 'Shop' },
      { value: 'Office', label: 'Office' },
      { value: 'Flat', label: 'Flat' }
    ],
    'Sale': [
      { value: 'Bunglow', label: 'Bunglow' },
      { value: 'Land', label: 'Land' },
      { value: 'TDR', label: 'TDR' },
      { value: 'Duplex', label: 'Duplex' },
      { value: 'Villa', label: 'Villa' },
      { value: 'Shop', label: 'Shop' },
      { value: 'Office', label: 'Office' },
      { value: 'Flat', label: 'Flat' }
    ],
    'Lease_Rental': [
      { value: 'Bunglow', label: 'Bunglow' },
      { value: 'Land', label: 'Land' },
      { value: 'Duplex', label: 'Duplex' },
      { value: 'Villa', label: 'Villa' },
      { value: 'Shop', label: 'Shop' },
      { value: 'Office', label: 'Office' },
      { value: 'Flat', label: 'Flat' }
    ],
    'JV_Syndication': [
      { value: 'Land', label: 'Land' }
    ]
  };

  // Configuration options based on Property Description selection
  configurationOptionsMap: { [key: string]: DropdownOption[] } = {
    'Flat': [
      { value: '1RK', label: '1RK' },
      { value: '1BHK', label: '1BHK' },
      { value: '1.5BHK', label: '1.5 BHK' },
      { value: '2BHK', label: '2BHK' },
      { value: '2.5BHK', label: '2.5 BHK' },
      { value: '3BHK', label: '3BHK' },
      { value: '3.5BHK', label: '3.5 BHK' },
      { value: '4BHK', label: '4BHK' }
    ],
    'Land': [
      { value: 'NA', label: 'NA' },
      { value: 'Green_Land', label: 'Green Land' }
    ]
  };

  // Price Per Unit options based on Property Description selection
  pricePerUnitOptionsMap: { [key: string]: DropdownOption[] } = {
    'Flat': [
      { value: 'Sq_Ft', label: 'Sq. Ft' },
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ],
    'Shop': [
      { value: 'Sq_Ft', label: 'Sq. Ft' },
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ],
    'Office': [
      { value: 'Sq_Ft', label: 'Sq. Ft' },
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ],
    'Bunglow': [
      { value: 'Sq_Ft', label: 'Sq. Ft' },
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ],
    'Duplex': [
      { value: 'Sq_Ft', label: 'Sq. Ft' },
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ],
    'Villa': [
      { value: 'Sq_Ft', label: 'Sq. Ft' },
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ],
    'Land': [
      { value: 'Sq_Mtr', label: 'Sq. Mtr' },
      { value: 'Ghuntha', label: 'Ghuntha' },
      { value: 'Acar', label: 'Acar' },
      { value: 'Hector', label: 'Hector' }
    ],
    'TDR': [
      { value: 'Sq_Mtr', label: 'Sq. Mtr' }
    ]
  };

  constructor(private formBuilder: FormBuilder) {
    console.log('🏗️ PropertyComponent constructor called');
    console.log('🏗️ Initial customers array:', this.customers);
    this.initForm();
  }

  ngOnInit(): void {
    console.log('🚀 PropertyComponent ngOnInit called');
    console.log('🚀 Customers array on init:', this.customers);
    console.log('🚀 Current customer on init:', this.currentCustomer);
  }

  // Getter for easy access to form controls
  get form() {
    return this.propertyForm.controls;
  }

  initForm() {
    this.propertyForm = this.formBuilder.group({
      UniqueCode: [this.generateUniqueCode()],
      category: [''],
      requiredDeal: [''],
      dealFrom: [''],
      propertyDescription: [''],
      configuration: [''],
      location: [''],
      sublocation: [''],
      googleLink: [''],
      propertyPhotos: [''],
      pricePerUnit: [''],
      lumpSumPrice: [0],
      autoCalculated: [{ value: 0, disabled: true }],
      negotiationIfAny: [''],
      // JV Syndication specific fields
      dpRoad: [''],
      plotShape: [''],
      jvDepositAmount: [0],
      jvDepositType: [''],
      onnAmount: [0],
      jvRatio: [''],
      totalPotentialOfPlot: [0],
      totalProjectCosting: [0],
      totalProjectRevenue: [0],
      netSurplus: [{ value: 0, disabled: true }],
      projectFunding: [0]
    });

    // Subscribe to requiredDeal changes to update conditional dropdowns
    this.propertyForm.get('requiredDeal')?.valueChanges.subscribe(value => {
      this.onRequiredDealChange(value);
    });

    // Subscribe to propertyDescription changes to update configuration dropdown
    this.propertyForm.get('propertyDescription')?.valueChanges.subscribe(value => {
      this.onPropertyDescriptionChange(value);
    });
  }

  // Generate Sr. No
  generateUniqueCode(): string {
    const currentYear = new Date().getFullYear();
    return `PROP-${currentYear}-${this.nextUniqueCode.toString().padStart(3, '0')}`;
  }

  // Handle Required Deal change to update conditional dropdowns
  onRequiredDealChange(selectedDeal: string) {
    // Update Deal From options
    this.dealFromOptions = this.dealFromOptionsMap[selectedDeal] || [];

    // Update Property Description options
    this.propertyDescriptionOptions = this.propertyDescriptionOptionsMap[selectedDeal] || [];

    // Reset the dependent fields when Required Deal changes
    this.propertyForm.patchValue({
      dealFrom: '',
      propertyDescription: '',
      configuration: '',
      location: '',
      sublocation: '',
      googleLink: '',
      propertyPhotos: '',
      pricePerUnit: '',
      lumpSumPrice: 0,
      autoCalculated: 0,
      negotiationIfAny: '',
      // Reset JV Syndication fields
      dpRoad: '',
      plotShape: '',
      jvDepositAmount: 0,
      jvDepositType: '',
      onnAmount: 0,
      jvRatio: '',
      totalPotentialOfPlot: 0,
      totalProjectCosting: 0,
      totalProjectRevenue: 0,
      netSurplus: 0,
      projectFunding: 0
    });

    // Reset conditional dropdowns
    this.configurationOptions = [];
    this.pricePerUnitOptions = [];
  }

  // Handle Property Description change to update configuration dropdown
  onPropertyDescriptionChange(selectedProperty: string) {
    // Update Configuration options based on property description
    this.configurationOptions = this.configurationOptionsMap[selectedProperty] || [];

    // Update Price Per Unit options based on property description
    this.pricePerUnitOptions = this.pricePerUnitOptionsMap[selectedProperty] || [];

    // Reset dependent fields when property description changes
    this.propertyForm.patchValue({
      configuration: '',
      pricePerUnit: '',
      lumpSumPrice: 0,
      autoCalculated: 0,
      negotiationIfAny: ''
    });
  }

  // Handle Lump Sum Price change to auto-calculate
  onLumpSumPriceChange() {
    const lumpSumPrice = this.propertyForm.get('lumpSumPrice')?.value || 0;
    // For now, auto-calculated is same as lump sum price
    // You can modify this calculation logic as per business requirements
    const autoCalculated = lumpSumPrice;

    this.propertyForm.patchValue({
      autoCalculated: autoCalculated
    });
  }

  // Calculate Net Surplus for JV Syndication
  calculateNetSurplus() {
    const totalProjectRevenue = this.propertyForm.get('totalProjectRevenue')?.value || 0;
    const totalProjectCosting = this.propertyForm.get('totalProjectCosting')?.value || 0;
    const netSurplus = totalProjectRevenue - totalProjectCosting;

    this.propertyForm.patchValue({
      netSurplus: netSurplus
    });
  }

  // Handle Total Project Revenue change
  onTotalProjectRevenueChange() {
    this.calculateNetSurplus();
  }

  // Handle Total Project Costing change
  onTotalProjectCostingChange() {
    this.calculateNetSurplus();
  }

  // Create empty customer object
  createEmptyCustomer(): CustomerData {
    return {
      id: this.nextCustomerId,
      customerName: '',
      contactNo: '',
      resiAddress: ''
    };
  }

  // Add or update customer in the list
  addCustomer() {
    console.log('🔍 Processing customer:', this.currentCustomer);
    console.log('🔍 Current customers before operation:', this.customers);

    // Check if this is an update (customer with same ID already exists)
    const existingCustomerIndex = this.customers.findIndex(customer => customer.id === this.currentCustomer.id);

    if (existingCustomerIndex !== -1) {
      // Update existing customer
      this.customers[existingCustomerIndex] = { ...this.currentCustomer };
      console.log('✅ Customer updated successfully:', this.currentCustomer);
    } else {
      // Add new customer to the list
      this.customers.push({ ...this.currentCustomer });
      console.log('✅ Customer added successfully:', this.currentCustomer);

      // Only increment ID for new customers
      this.nextCustomerId++;
    }

    console.log('🔍 Current customers after operation:', this.customers);
    console.log('✅ Total customers:', this.customers.length);

    // Reset current customer for next entry
    this.currentCustomer = this.createEmptyCustomer();
  }

  // Remove customer from the list
  removeCustomer(customerId: number) {
    this.customers = this.customers.filter(customer => customer.id !== customerId);
    console.log('Customer removed, remaining customers:', this.customers);
  }

  // Edit customer (populate current customer form with selected customer data for editing)
  editCustomer(customer: CustomerData) {
    console.log('📝 Editing customer:', customer);

    // Populate the current customer form with the selected customer's data
    this.currentCustomer = { ...customer };

    // Note: The customer remains in the list. When "Add Customer" is clicked,
    // it will either update the existing customer (if ID matches) or add as new

    console.log('📝 Customer data loaded into form for editing:', this.currentCustomer);
  }

  // Get appropriate button text based on whether we're adding or editing
  getCustomerButtonText(): string {
    const existingCustomer = this.customers.find(customer => customer.id === this.currentCustomer.id);
    return existingCustomer ? 'Update Customer' : 'Add Customer';
  }

  // Get appropriate button icon based on whether we're adding or editing
  getCustomerButtonIcon(): string {
    const existingCustomer = this.customers.find(customer => customer.id === this.currentCustomer.id);
    return existingCustomer ? 'icon-edit' : 'icon-plus';
  }

  // Refresh customer data to ensure it's properly maintained
  refreshCustomerData(): void {
    console.log('🔄 Refreshing customer data...');
    console.log('🔄 Current customers before refresh:', this.customers);

    // Ensure customers array is properly maintained
    if (!this.customers) {
      this.customers = [];
    }

    // Log the refreshed state
    console.log('🔄 Current customers after refresh:', this.customers);
    console.log('🔄 Total customers after refresh:', this.customers.length);
  }

  onSubmit() {
    this.isFormSubmitted = true;

    console.log('🚀 Property form onSubmit called');
    console.log('🔍 Customers at submit time:', this.customers);
    console.log('🔍 Number of customers:', this.customers.length);
    console.log('🔍 Current customer at submit:', this.currentCustomer);

    // Force refresh customer data to ensure it's captured
    this.refreshCustomerData();

    // Get all form data including customer data
    const productFormData = this.getFormData();

    console.log('✅ Property form submitted, emitting to parent:', productFormData);

    // Emit event to parent component to trigger sales API submission
    this.submitSales.emit(productFormData);

    // Note: Form reset and local list management will be handled by parent component
    // after successful API submission to maintain consistency with the overall flow
  }

  resetForm() {
    this.propertyForm.reset();
    this.isFormSubmitted = false;
    this.nextUniqueCode++;

    // Reset customer data
    this.customers = [];
    this.nextCustomerId = 1;
    this.currentCustomer = this.createEmptyCustomer();

    // Reset conditional dropdowns
    this.dealFromOptions = [];
    this.propertyDescriptionOptions = [];
    this.configurationOptions = [];
    this.pricePerUnitOptions = [];

    this.initForm(); // Reinitialize with new Sr. No
  }

  /**
   * Get all form data including customer data
   * This method is called by the parent component to capture product-specific data
   */
  getFormData(): any {
    if (!this.propertyForm) {
      console.warn('⚠️ Property form is not initialized');
      return {};
    }

    // Get all form values including disabled fields
    const formData = this.propertyForm.getRawValue();

    // Debug logging for customer data
    console.log('🔍 Current customers array:', this.customers);
    console.log('🔍 Customers array length:', this.customers.length);
    console.log('🔍 Current customer being edited:', this.currentCustomer);

    // Ensure customers array is properly cloned and included
    const customersData = this.customers.map(customer => ({
      id: customer.id,
      customerName: customer.customerName || '',
      contactNo: customer.contactNo || '',
      resiAddress: customer.resiAddress || ''
    }));

    // If no customers in the array but current customer has data, include it
    if (customersData.length === 0 && this.currentCustomer &&
        (this.currentCustomer.customerName || this.currentCustomer.contactNo || this.currentCustomer.resiAddress)) {
      console.log('🔍 No customers in array but current customer has data, including it');
      customersData.push({
        id: this.currentCustomer.id,
        customerName: this.currentCustomer.customerName || '',
        contactNo: this.currentCustomer.contactNo || '',
        resiAddress: this.currentCustomer.resiAddress || ''
      });
    }

    // Add component metadata and customer data
    const productFormData = {
      ...formData,
      customers: customersData,
      component_type: 'property',
      product_type: 'Property',
      form_submission_date: new Date().toISOString(),
      // Also include current customer being edited if any data exists
      currentCustomer: this.currentCustomer,
      // Include total customer count for debugging
      totalCustomers: customersData.length
    };

    console.log('📊 PropertyComponent form data:', productFormData);
    console.log('📊 Customers in form data:', productFormData.customers);
    console.log('📊 Number of customers in form data:', productFormData.customers.length);
    return productFormData;
  }



  /**
   * Populate form with existing data (for edit mode)
   */
  populateFormData(formData: any): void {
    if (!formData || !this.propertyForm) {
      console.log('⚠️ No form data or Property form not initialized');
      return;
    }

    console.log('🔧 Populating Property form with data:', formData);
    console.log('🔧 Property form current state before population:', this.propertyForm.value);
    console.log('🔧 Customers data received:', formData.customers);

    try {
      // Populate main form with the provided data (excluding customers)
      const formDataWithoutCustomers = { ...formData };
      delete formDataWithoutCustomers.customers; // Remove customers from form data
      this.propertyForm.patchValue(formDataWithoutCustomers);

      // Populate customers if available
      if (formData.customers && Array.isArray(formData.customers)) {
        console.log('🔧 Processing customers array:', formData.customers);
        console.log('🔧 Number of customers to populate:', formData.customers.length);

        // Clear existing customers first
        this.customers = [];

        // Map each customer from the saved data
        this.customers = formData.customers.map((customer: any, index: number) => {
          const mappedCustomer = {
            id: customer.id || (index + 1),
            customerName: customer.customerName || '',
            contactNo: customer.contactNo || '',
            resiAddress: customer.resiAddress || ''
          };

          console.log(`🔧 Mapped customer ${index + 1}:`, mappedCustomer);
          return mappedCustomer;
        });

        // Update next customer ID to avoid conflicts
        this.nextCustomerId = Math.max(...this.customers.map(c => c.id)) + 1;

        console.log('✅ Property customers populated successfully:', this.customers);
        console.log('✅ Total customers populated:', this.customers.length);
        console.log('✅ Next customer ID set to:', this.nextCustomerId);
      } else {
        console.log('ℹ️ No customers data found or invalid format');
        this.customers = [];
      }

      // Reset current customer for new entries
      this.currentCustomer = this.createEmptyCustomer();

      console.log('🔧 Property form state after population:', this.propertyForm.value);
      console.log('🔧 Final customers state:', this.customers);
      console.log('✅ Property form populated successfully');

      // Trigger any necessary validations or calculations
      this.propertyForm.updateValueAndValidity();
    } catch (error) {
      console.error('❌ Error populating Property form:', error);
      console.error('❌ Form data that caused error:', formData);
      console.error('❌ Customers data that caused error:', formData.customers);
    }
  }

  /**
   * Reset form after successful submission
   */
  resetFormAfterSubmission(): void {
    console.log('🔄 Resetting Property form after successful submission');

    this.resetForm(); // Use existing reset method

    console.log('✅ Property form reset completed');
  }
}
