<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Group Entity</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #groupEntityForm="ngForm">
    <div class="row mb-3">
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="companyName" class="form-label">Company Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="companyName" name="companyName" [(ngModel)]="formData.companyName" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="constitution" class="form-label">Constitution <span class="text-danger">*</span></label>
        <select class="form-select" id="constitution" name="constitution" [(ngModel)]="formData.constitution" required>
          <option value="" selected disabled>Select Constitution</option>
          <option value="Private Limited">Private Limited</option>
          <option value="Public Limited">Public Limited</option>
          <option value="LLP">LLP</option>
          <option value="Partnership">Partnership</option>
          <option value="Proprietorship">Proprietorship</option>
          <option value="HUF">HUF</option>
        </select>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="dateOfIncorporation" class="form-label">Date of Incorporation <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="dateOfIncorporation" name="dateOfIncorporation" [(ngModel)]="formData.dateOfIncorporation" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="pan" class="form-label">PAN <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="pan" name="pan" [(ngModel)]="formData.pan" required maxlength="10" placeholder="**********">
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="cinGstNo" class="form-label">CIN / GST No</label>
        <input type="text" class="form-control" id="cinGstNo" name="cinGstNo" [(ngModel)]="formData.cinGstNo">
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="registeredOfficeAddress" class="form-label">Office Address <span class="text-danger">*</span></label>
        <textarea class="form-control" id="registeredOfficeAddress" name="registeredOfficeAddress" [(ngModel)]="formData.registeredOfficeAddress" rows="2" required></textarea>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="partnersDirectors" class="form-label">Partners / Directors <span class="text-danger">*</span></label>
        <textarea class="form-control" id="partnersDirectors" name="partnersDirectors" [(ngModel)]="formData.partnersDirectors" rows="2" required></textarea>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="profitSharingRatio" class="form-label">Profit Sharing Ratio</label>
        <input type="text" class="form-control" id="profitSharingRatio" name="profitSharingRatio" [(ngModel)]="formData.profitSharingRatio" placeholder="e.g. 40:30:30">
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="din" class="form-label">DIN</label>
        <input type="text" class="form-control" id="din" name="din" [(ngModel)]="formData.din" placeholder="Director Identification Numbers">
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="currentProjects" class="form-label">Current Projects</label>
        <textarea class="form-control" id="currentProjects" name="currentProjects" [(ngModel)]="formData.currentProjects" rows="2"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="groupEntityForm.invalid" (click)="saveChanges()">Save</button>
</div>
