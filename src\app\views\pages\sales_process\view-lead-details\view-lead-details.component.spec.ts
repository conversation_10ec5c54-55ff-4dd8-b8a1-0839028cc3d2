import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router, convertToParamMap } from '@angular/router';
import { of } from 'rxjs';
import { ViewLeadDetailsComponent } from './view-lead-details.component';
import { NgbNavModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { CommonModule } from '@angular/common';
import { RouterTestingModule } from '@angular/router/testing';

describe('ViewLeadDetailsComponent', () => {
  let component: ViewLeadDetailsComponent;
  let fixture: ComponentFixture<ViewLeadDetailsComponent>;
  let mockActivatedRoute: any;
  let mockRouter: any;

  beforeEach(async () => {
    // Create mock for ActivatedRoute
    mockActivatedRoute = {
      paramMap: of(convertToParamMap({ id: '1' }))
    };

    // Create mock for Router
    mockRouter = {
      navigate: jasmine.createSpy('navigate')
    };

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        RouterTestingModule,
        NgbNavModule,
        NgbTooltipModule,
        ViewLeadDetailsComponent
      ],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ViewLeadDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load lead data based on route parameter', () => {
    // The component should have loaded lead data with ID 1
    expect(component.leadId).toBe(1);
    expect(component.leadData).toBeDefined();
    expect(component.leadData?.id).toBe(1);
  });

  it('should set active tab to 1 by default', () => {
    expect(component.activeTab).toBe(1);
  });

  it('should return correct status class based on status', () => {
    expect(component.getStatusClass('active')).toBe('bg-success');
    expect(component.getStatusClass('pending')).toBe('bg-warning');
    expect(component.getStatusClass('completed')).toBe('bg-info');
    expect(component.getStatusClass('unknown')).toBe('bg-secondary');
  });

  it('should navigate back to sales list if lead is not found', () => {
    // Create a new instance with a non-existent ID
    mockActivatedRoute.paramMap = of(convertToParamMap({ id: '999' }));
    
    fixture = TestBed.createComponent(ViewLeadDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    
    // Should attempt to navigate back to sales list
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/sales-list']);
  });

  it('should navigate back to sales list if no ID is provided', () => {
    // Create a new instance with no ID
    mockActivatedRoute.paramMap = of(convertToParamMap({}));
    
    fixture = TestBed.createComponent(ViewLeadDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    
    // Should attempt to navigate back to sales list
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/sales-list']);
  });
});
