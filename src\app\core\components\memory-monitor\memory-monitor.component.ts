import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, interval } from 'rxjs';
import { MemoryManagerService, MemoryUsage, MemoryLeak, MemoryStats } from '../../services/memory-manager.service';
import { FeatherIconDirective } from '../../feather-icon/feather-icon.directive';
import { environment } from '../../../../environments/environment';

/**
 * Memory Monitor Component
 * 
 * Provides real-time memory usage monitoring and leak detection
 * for development and debugging purposes.
 */
@Component({
  selector: 'app-memory-monitor',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective
  ],
  template: `
    <div class="memory-monitor-panel" *ngIf="isVisible">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">
            <i data-feather="cpu" appFeatherIcon class="icon-sm me-2"></i>
            Memory Monitor
          </h6>
          <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" (click)="forceGC()" title="Force Garbage Collection">
              <i data-feather="trash-2" appFeatherIcon class="icon-sm"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary" (click)="toggleVisibility()">
              <i data-feather="x" appFeatherIcon class="icon-sm"></i>
            </button>
          </div>
        </div>
        
        <div class="card-body">
          <!-- Memory Usage -->
          <div class="row mb-3" *ngIf="currentMemoryUsage">
            <div class="col-md-4">
              <div class="metric-card">
                <div class="metric-value">{{ formatBytes(currentMemoryUsage.usedJSHeapSize) }}</div>
                <div class="metric-label">Used Memory</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="metric-card">
                <div class="metric-value">{{ formatBytes(currentMemoryUsage.totalJSHeapSize) }}</div>
                <div class="metric-label">Total Heap</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="metric-card">
                <div class="metric-value">{{ getMemoryUsagePercentage() }}%</div>
                <div class="metric-label">Usage</div>
              </div>
            </div>
          </div>

          <!-- Memory Statistics -->
          <div class="row mb-3" *ngIf="memoryStats">
            <div class="col-md-6">
              <div class="metric-card">
                <div class="metric-value">{{ formatBytes(memoryStats.peakUsage.usedJSHeapSize) }}</div>
                <div class="metric-label">Peak Usage</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="metric-card">
                <div class="metric-value">{{ formatBytes(memoryStats.averageUsage) }}</div>
                <div class="metric-label">Average Usage</div>
              </div>
            </div>
          </div>

          <!-- Memory Usage Chart (Simple Bar) -->
          <div class="row mb-3" *ngIf="currentMemoryUsage">
            <div class="col-12">
              <div class="memory-usage-bar">
                <div class="usage-bar" 
                     [style.width.%]="getMemoryUsagePercentage()"
                     [ngClass]="getUsageBarClass()">
                </div>
              </div>
              <small class="text-muted">
                {{ formatBytes(currentMemoryUsage.usedJSHeapSize) }} / 
                {{ formatBytes(currentMemoryUsage.jsHeapSizeLimit) }}
              </small>
            </div>
          </div>

          <!-- Memory Leaks -->
          <div class="row mb-3" *ngIf="memoryLeaks.length > 0">
            <div class="col-12">
              <h6 class="text-warning">
                <i data-feather="alert-triangle" appFeatherIcon class="icon-sm me-1"></i>
                Memory Leaks Detected ({{ memoryLeaks.length }})
              </h6>
              <div class="leak-list">
                <div *ngFor="let leak of memoryLeaks" 
                     class="leak-item"
                     [ngClass]="'leak-' + leak.severity">
                  <div class="leak-header">
                    <span class="leak-type">{{ leak.type }}</span>
                    <span class="leak-component">{{ leak.component }}</span>
                    <span class="leak-severity badge" [ngClass]="getSeverityBadgeClass(leak.severity)">
                      {{ leak.severity }}
                    </span>
                  </div>
                  <div class="leak-description">{{ leak.description }}</div>
                  <div class="leak-time">
                    Detected: {{ formatTime(leak.detected) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recommendations -->
          <div class="row mb-3" *ngIf="memoryStats?.recommendations.length">
            <div class="col-12">
              <h6>
                <i data-feather="lightbulb" appFeatherIcon class="icon-sm me-1"></i>
                Recommendations
              </h6>
              <ul class="list-unstyled">
                <li *ngFor="let recommendation of memoryStats.recommendations" class="text-info mb-1">
                  <i data-feather="arrow-right" appFeatherIcon class="icon-sm me-1"></i>
                  {{ recommendation }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Actions -->
          <div class="row">
            <div class="col-12">
              <div class="btn-group w-100" role="group">
                <button class="btn btn-sm btn-outline-success" (click)="refreshStats()">
                  <i data-feather="refresh-cw" appFeatherIcon class="icon-sm me-1"></i>
                  Refresh
                </button>
                <button class="btn btn-sm btn-outline-warning" (click)="clearLeaks()">
                  <i data-feather="x-circle" appFeatherIcon class="icon-sm me-1"></i>
                  Clear Leaks
                </button>
                <button class="btn btn-sm btn-outline-danger" (click)="forceGC()">
                  <i data-feather="trash-2" appFeatherIcon class="icon-sm me-1"></i>
                  Force GC
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toggle Button (when panel is hidden) -->
    <button class="memory-toggle-btn btn btn-warning btn-sm" 
            *ngIf="!isVisible"
            (click)="toggleVisibility()"
            title="Show Memory Monitor">
      <i data-feather="cpu" appFeatherIcon class="icon-sm"></i>
    </button>
  `,
  styles: [`
    .memory-monitor-panel {
      position: fixed;
      top: 80px;
      right: 20px;
      width: 450px;
      z-index: 1040;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-height: 80vh;
      overflow-y: auto;
    }

    .memory-toggle-btn {
      position: fixed;
      top: 80px;
      right: 20px;
      z-index: 1040;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .metric-card {
      text-align: center;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background: #f8f9fa;
      margin-bottom: 10px;
    }

    .metric-value {
      font-size: 1.2rem;
      font-weight: bold;
      color: #495057;
    }

    .metric-label {
      font-size: 0.8rem;
      color: #6c757d;
      margin-top: 4px;
    }

    .memory-usage-bar {
      width: 100%;
      height: 20px;
      background-color: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 5px;
    }

    .usage-bar {
      height: 100%;
      transition: width 0.3s ease;
    }

    .usage-bar.low { background-color: #28a745; }
    .usage-bar.medium { background-color: #ffc107; }
    .usage-bar.high { background-color: #fd7e14; }
    .usage-bar.critical { background-color: #dc3545; }

    .leak-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .leak-item {
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 4px;
      border-left: 4px solid;
    }

    .leak-item.leak-low { border-left-color: #28a745; background-color: #d4edda; }
    .leak-item.leak-medium { border-left-color: #ffc107; background-color: #fff3cd; }
    .leak-item.leak-high { border-left-color: #fd7e14; background-color: #ffeaa7; }
    .leak-item.leak-critical { border-left-color: #dc3545; background-color: #f8d7da; }

    .leak-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .leak-type {
      font-weight: bold;
      text-transform: uppercase;
      font-size: 0.8rem;
    }

    .leak-component {
      font-style: italic;
      color: #6c757d;
    }

    .leak-description {
      font-size: 0.9rem;
      margin-bottom: 4px;
    }

    .leak-time {
      font-size: 0.8rem;
      color: #6c757d;
    }

    @media (max-width: 768px) {
      .memory-monitor-panel {
        width: 90%;
        right: 5%;
      }
    }
  `]
})
export class MemoryMonitorComponent implements OnInit, OnDestroy {
  isVisible = false;
  currentMemoryUsage: MemoryUsage | null = null;
  memoryStats: MemoryStats | null = null;
  memoryLeaks: MemoryLeak[] = [];
  
  private destroy$ = new Subject<void>();

  constructor(private memoryManager: MemoryManagerService) {}

  ngOnInit(): void {
    // Subscribe to memory usage updates
    this.memoryManager.memoryUsage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(usage => {
        this.currentMemoryUsage = usage;
      });

    // Subscribe to memory leaks
    this.memoryManager.memoryLeaks$
      .pipe(takeUntil(this.destroy$))
      .subscribe(leaks => {
        this.memoryLeaks = leaks;
      });

    // Refresh stats periodically
    interval(10000) // Every 10 seconds
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.refreshStats();
      });

    // Initial stats load
    this.refreshStats();

    // Only show in development environment
    this.isVisible = !environment.production;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleVisibility(): void {
    this.isVisible = !this.isVisible;
  }

  refreshStats(): void {
    this.memoryManager.getMemoryStats().subscribe(stats => {
      this.memoryStats = stats;
    });
  }

  clearLeaks(): void {
    this.memoryLeaks = [];
  }

  forceGC(): void {
    this.memoryManager.forceGarbageCollection();
  }

  getMemoryUsagePercentage(): number {
    if (!this.currentMemoryUsage) return 0;
    return Math.round((this.currentMemoryUsage.usedJSHeapSize / this.currentMemoryUsage.jsHeapSizeLimit) * 100);
  }

  getUsageBarClass(): string {
    const percentage = this.getMemoryUsagePercentage();
    if (percentage < 50) return 'low';
    if (percentage < 70) return 'medium';
    if (percentage < 85) return 'high';
    return 'critical';
  }

  getSeverityBadgeClass(severity: string): string {
    switch (severity) {
      case 'low': return 'bg-success';
      case 'medium': return 'bg-warning';
      case 'high': return 'bg-danger';
      case 'critical': return 'bg-dark';
      default: return 'bg-secondary';
    }
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatTime(timestamp: number): string {
    return new Date(timestamp).toLocaleTimeString();
  }
}
