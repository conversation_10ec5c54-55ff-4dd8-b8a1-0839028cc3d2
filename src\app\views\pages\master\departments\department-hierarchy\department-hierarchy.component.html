<!-- Department Hierarchy Modal -->
<div class="modal-header">
  <h5 class="modal-title">
    <i class="feather icon-git-branch me-2"></i>
    Department Hierarchy: {{ departmentName }}
  </h5>
  <button type="button" class="btn-close" (click)="close()" aria-label="Close"></button>
</div>

<div class="modal-body">
  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading hierarchy...</span>
    </div>
    <p class="mt-2 text-muted">Loading department hierarchy...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="alert alert-danger">
    <i class="feather icon-alert-circle me-2"></i>
    {{ error }}
  </div>

  <!-- Hierarchy Display -->
  <div *ngIf="hierarchy && !loading && !error">
    <div class="hierarchy-container">
      <div class="hierarchy-item root-item">
        <div class="hierarchy-content">
          <i class="feather icon-home hierarchy-icon"></i>
          <span class="hierarchy-name">{{ hierarchy.name }}</span>
          <span class="hierarchy-meta">
            Level {{ hierarchy.level }} • {{ hierarchy.employee_count }} employees
          </span>
        </div>
      </div>

      <!-- Recursive hierarchy display -->
      <div *ngIf="hierarchy.children && hierarchy.children.length > 0" class="hierarchy-children">
        <ng-container *ngFor="let child of hierarchy.children">
          <ng-container *ngTemplateOutlet="hierarchyTemplate; context: { $implicit: child, level: 1 }"></ng-container>
        </ng-container>
      </div>
    </div>

    <!-- No children message -->
    <div *ngIf="!hierarchy.children || hierarchy.children.length === 0" class="text-center py-3">
      <i class="feather icon-info text-muted" style="font-size: 2rem;"></i>
      <h6 class="mt-2 text-muted">No Sub-departments</h6>
      <p class="text-muted mb-0">This department has no child departments.</p>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="close()">
    <i class="feather icon-x me-1"></i>
    Close
  </button>
</div>

<!-- Recursive Template for Hierarchy -->
<ng-template #hierarchyTemplate let-item let-level="level">
  <div class="hierarchy-item" [style.margin-left.rem]="level * 1.5">
    <div class="hierarchy-content">
      <span class="hierarchy-connector">{{ getLevelIndicator(level) }}</span>
      <i class="feather icon-folder hierarchy-icon"></i>
      <span class="hierarchy-name">{{ item.name }}</span>
      <span class="hierarchy-meta">
        Level {{ item.level }} • {{ item.employee_count }} employees
      </span>
    </div>
  </div>

  <!-- Recursive children -->
  <div *ngIf="item.children && item.children.length > 0">
    <ng-container *ngFor="let child of item.children">
      <ng-container *ngTemplateOutlet="hierarchyTemplate; context: { $implicit: child, level: level + 1 }"></ng-container>
    </ng-container>
  </div>
</ng-template>
