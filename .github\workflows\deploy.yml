name: Deploy Bizz Corp to Server

on:
  push:
    branches:
      - master  # Trigger on pushes to the master branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout Code
      uses: actions/checkout@v3

    - name: Set Up SSH
      env:
        SERVER_PRIVATE_KEY: ${{ secrets.SERVER_PRIVATE_KEY }}
      run: |
        mkdir -p ~/.ssh
        echo "$SERVER_PRIVATE_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ************* >> ~/.ssh/known_hosts

    - name: Sync Bizzcorp Files to Server
      run: |
        rsync -avz -e "ssh -o StrictHostKeyChecking=no" --exclude '.git' ./ \
        root@*************:/home/<USER>/public_html/bizz-corp-frontend

    - name: Run Docker Compose for Bizz Corp
      run: |
        ssh -o StrictHostKeyChecking=no root@************* << 'EOF'
        cd /home/<USER>/public_html/bizz-corp-frontend
        docker-compose -f docker-compose-bizzcorp.yml down
        docker-compose -f docker-compose-bizzcorp.yml up --build -d
        EOF
