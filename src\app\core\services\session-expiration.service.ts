import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import Swal from 'sweetalert2';

@Injectable({
  providedIn: 'root'
})
export class SessionExpirationService {
  private warningShown = false;
  private checkInterval?: any;
  private lastExtensionTime = 0;
  private extensionCooldownMs = 60000; // 1 minute cooldown after extension

  constructor(private authService: AuthService) {
    console.log('🔄 SessionExpirationService initialized');
    this.startSessionMonitoring();
  }

  /**
   * Start monitoring session expiration
   */
  private startSessionMonitoring(): void {
    console.log('🔄 Starting session monitoring - adaptive checking for 30-minute tokens');

    // Adaptive monitoring interval based on expected token duration
    const currentUser = this.authService.currentUserValue;
    let checkInterval = 60000; // Default: 1 minute for 30-minute tokens

    if (currentUser?.token_expiry) {
      const secondsUntilExpiry = Math.round((currentUser.token_expiry - Date.now()) / 1000);

      if (secondsUntilExpiry <= 300) { // Last 5 minutes - check every 30 seconds
        checkInterval = 30000;
      } else if (secondsUntilExpiry <= 900) { // Last 15 minutes - check every 45 seconds
        checkInterval = 45000;
      } else { // More than 15 minutes - check every minute
        checkInterval = 60000;
      }

      console.log(`🕐 Token expires in ${Math.round(secondsUntilExpiry/60)} minutes, checking every ${checkInterval/1000}s`);
    }

    this.checkInterval = setInterval(() => {
      this.checkSessionExpiration();
    }, checkInterval);

    // Also check immediately
    setTimeout(() => {
      this.checkSessionExpiration();
    }, 10000); // Check after 10 seconds to allow app to fully load
  }

  /**
   * Stop monitoring session expiration
   */
  stopSessionMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    this.warningShown = false;
  }

  /**
   * Check if session is about to expire and show warning
   */
  private checkSessionExpiration(): void {
    if (!this.authService.isLoggedIn()) {
      console.log('🔍 Session check: User not logged in, skipping');
      return;
    }

    const currentUser = this.authService.currentUserValue;
    if (!currentUser?.token_expiry) {
      console.log('🔍 Session check: No token expiry found, skipping');
      return;
    }

    const timeUntilExpiry = currentUser.token_expiry - Date.now();
    const secondsUntilExpiry = Math.round(timeUntilExpiry / 1000);
    const minutesUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60));

    // Check if token will expire soon and show user choice popup
    const isExpiringSoon = this.authService.isTokenExpiringSoon();

    // Check if we're in cooldown period after recent extension
    const timeSinceLastExtension = Date.now() - this.lastExtensionTime;
    const inCooldownPeriod = timeSinceLastExtension < this.extensionCooldownMs;

    if (inCooldownPeriod) {
      console.log(`🔄 COOLDOWN: ${Math.round((this.extensionCooldownMs - timeSinceLastExtension) / 1000)}s remaining after recent extension`);
    }

    // Show warning popup to give user control (even though auto-refresh is happening)
    // But respect cooldown period to prevent popup spam after successful extension
    if (isExpiringSoon && !this.warningShown && !inCooldownPeriod) {
      console.log('⚠️ POPUP TRIGGER: Showing user choice popup (auto-refresh also active)');
      this.showSessionExpirationWarning();
    } else if (isExpiringSoon && !this.warningShown && inCooldownPeriod) {
      console.log('🔄 POPUP SUPPRESSED: In cooldown period after recent session extension');
    } else if (this.authService.isTokenExpired()) {
      console.log('❌ TOKEN EXPIRED: Logging out user');
      this.authService.logout(false, 'Your session has expired');
    }
  }

  /**
   * Show session expiration warning with option to extend session
   */
  private showSessionExpirationWarning(): void {
    this.warningShown = true;

    // Calculate actual time remaining
    const currentUser = this.authService.currentUserValue;
    const timeUntilExpiry = currentUser?.token_expiry ? currentUser.token_expiry - Date.now() : 0;
    const secondsUntilExpiry = Math.round(timeUntilExpiry / 1000);
    const minutesUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60));
    const hoursUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60 * 60));
    const daysUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60 * 60 * 24));

    // Dynamic message based on time remaining
    let warningText: string;
    let autoCloseTimer: number;

    if (secondsUntilExpiry <= 90) { // For short tokens (1-2 minutes)
      warningText = `Your session will expire in ${secondsUntilExpiry} seconds. Click "Extend Session".`;
      autoCloseTimer = 20000; // 20 seconds to decide
    } else if (minutesUntilExpiry <= 5) { // For medium tokens (2-5 minutes)
      warningText = `Your session will expire in ${minutesUntilExpiry} minute(s). Click "Extend Session".`;
      autoCloseTimer = 60000; // 1 minute for medium sessions
    } else if (minutesUntilExpiry <= 30) { // For 30-minute tokens (5-30 minutes)
      warningText = `Your session will expire in ${minutesUntilExpiry} minutes. Would you like to extend your session?`;
      autoCloseTimer = 180000; // 3 minutes for 30-minute tokens
    } else if (hoursUntilExpiry <= 24) { // For daily tokens (30 minutes to 24 hours)
      if (hoursUntilExpiry >= 1) {
        warningText = `Your session will expire in ${hoursUntilExpiry} hour(s). Would you like to extend your session?`;
      } else {
        warningText = `Your session will expire in ${minutesUntilExpiry} minutes. Would you like to extend your session?`;
      }
      autoCloseTimer = 600000; // 10 minutes for daily tokens
    } else if (daysUntilExpiry <= 7) { // For weekly tokens (1-7 days)
      warningText = `Your session will expire in ${daysUntilExpiry} day(s). Would you like to extend your session?`;
      autoCloseTimer = 1800000; // 30 minutes for weekly tokens
    } else if (daysUntilExpiry <= 30) { // For monthly tokens (7-30 days)
      warningText = `Your session will expire in ${daysUntilExpiry} day(s). Would you like to extend your session?`;
      autoCloseTimer = 3600000; // 1 hour for monthly tokens
    } else { // For very long tokens (2+ months)
      warningText = `Your session will expire in ${daysUntilExpiry} day(s). Would you like to extend your session for another period?`;
      autoCloseTimer = 7200000; // 2 hours for very long tokens (2+ months)
    }



    Swal.fire({
      icon: 'warning',
      title: 'Session Expiring Soon',
      text: warningText,
      showCancelButton: true,
      confirmButtonText: 'Extend Session',
      cancelButtonText: 'Logout Now',
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#dc3545',
      allowOutsideClick: false,
      allowEscapeKey: false,
      timer: autoCloseTimer,
      timerProgressBar: true,
      customClass: {
        popup: 'session-warning-popup'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // User wants to extend session - use refresh token
        console.log('👤 USER CHOICE: Extend Session - using refresh token to get new access token');
        this.extendSessionWithRefreshToken();
      } else if (result.isDismissed) {
        // User chose to logout or timer expired
        if (result.dismiss === Swal.DismissReason.cancel) {
          console.log('👤 USER CHOICE: Logout Now - terminating session');
          this.authService.logout(false, 'User chose to logout');
        } else {
          console.log('⏰ POPUP TIMEOUT: User did not respond, logging out immediately');
          console.log('❌ AUTO-LOGOUT: Session expired due to user inactivity');
          this.authService.logout(false, 'Session expired - no response to expiration warning');
        }
      }
      this.warningShown = false;
    });
  }

  /**
   * Extend session using refresh token when user clicks "Continue Session"
   */
  private extendSessionWithRefreshToken(): void {
    console.log('🔄 User clicked Continue Session - extending with refresh token');

    // Check if user has been logged out manually
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('🚪 User has been logged out, cannot extend session');
      this.showSessionExtensionError('User has been logged out. Please log in again.');
      return;
    }

    // First, check if we have the necessary tokens
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ No current user found');
      this.showSessionExtensionError('No user session found. Please log in again.');
      return;
    }

    if (!currentUser.refresh_token) {
      console.error('❌ No refresh token available');
      this.showSessionExtensionError('No refresh token available. Please log in again.');
      return;
    }

    // Show loading indicator
    Swal.fire({
      title: 'Extending Session...',
      // text: 'Using refresh token to extend your session.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    console.log('📡 Using refresh token to extend session...');

    this.authService.refreshToken().subscribe({
      next: (response) => {
        console.log('✅ Session extended successfully with refresh token:', response);

        // Close loading and show success
        Swal.close();

        // Calculate new expiry time for success message
        const newUser = this.authService.currentUserValue;
        const newTimeUntilExpiry = newUser?.token_expiry ? newUser.token_expiry - Date.now() : 0;
        const newSecondsUntilExpiry = Math.round(newTimeUntilExpiry / 1000);
        const newMinutesUntilExpiry = Math.round(newSecondsUntilExpiry / 60);

        let successText: string;
        if (newSecondsUntilExpiry > 300) { // More than 5 minutes
          successText = `Session extended!`;
        } else {
          successText = `Session extended!`;
        }

        Swal.fire({
          icon: 'success',
          title: 'Session Extended!',
          text: successText,
          timer: 3000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end'
        });

        // Reset warning state and set cooldown period
        this.resetWarningState();
        this.lastExtensionTime = Date.now();
        console.log(`🔄 EXTENSION COOLDOWN: Set ${this.extensionCooldownMs / 1000}s cooldown to prevent popup spam`);
      },
      error: (error) => {
        console.error('❌ Failed to extend session with refresh token:', error);

        // Close loading and show error
        Swal.close();

        this.handleSessionExtensionError(error);
      }
    });
  }

  /**
   * Manual session extension (called when user explicitly requests it)
   * Note: Auto-refresh also happens in background, this is for user-initiated refresh
   */
  private extendSession(): void {
    console.log('🔄 User manually requested session extension');

    // Check if user has been logged out manually
    if (localStorage.getItem('user_logged_out') === 'true') {
      console.log('🚪 User has been logged out, cannot extend session');
      this.showSessionExtensionError('User has been logged out. Please log in again.');
      return;
    }

    // First, check if we have the necessary tokens
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ No current user found');
      this.showSessionExtensionError('No user session found. Please log in again.');
      return;
    }

    if (!currentUser.refresh_token) {
      console.error('❌ No refresh token available');
      this.showSessionExtensionError('No refresh token available. Please log in again.');
      return;
    }

    // Show loading indicator
    Swal.fire({
      title: 'Refreshing Session...',
      text: 'Please wait while we refresh your session.',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    console.log('📡 Making manual refresh token request to API...');

    this.authService.refreshToken().subscribe({
      next: (response) => {
        console.log('✅ Manual session refresh successful:', response);

        // Close loading and show success
        Swal.close();

        // Calculate new expiry time for success message
        const newUser = this.authService.currentUserValue;
        const newTimeUntilExpiry = newUser?.token_expiry ? newUser.token_expiry - Date.now() : 0;
        const newSecondsUntilExpiry = Math.round(newTimeUntilExpiry / 1000);

        const successText = `Session refreshed! `;

        Swal.fire({
          icon: 'success',
          title: 'Session Refreshed',
          text: successText,
          timer: 2000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end'
        });

        // Reset warning state
        this.resetWarningState();
      },
      error: (error) => {
        console.error('❌ Failed to manually refresh session:', error);

        // Close loading and show error
        Swal.close();

        this.handleSessionExtensionError(error);
      }
    });
  }

  /**
   * Handle session extension errors with detailed messaging
   */
  private handleSessionExtensionError(error: any): void {
    let errorTitle = 'Session Extension Failed';
    let errorMessage = 'Unable to extend your session. You will be logged out.';
    let showRetryOption = false;

    // Check for specific error types
    if (error.status === 0) {
      errorTitle = 'Network Error';
      errorMessage = 'Unable to connect to the server. Please check your internet connection and try again.';
      showRetryOption = true;
    } else if (error.status === 401) {
      errorTitle = 'Session Expired';
      errorMessage = 'Your session has already expired. Please log in again.';
    } else if (error.status === 403) {
      errorTitle = 'Access Denied';
      errorMessage = 'You do not have permission to extend the session.';
    } else if (error.status === 422) {
      errorTitle = 'Invalid Token';
      errorMessage = 'Your refresh token is invalid or expired. Please log in again.';
    } else if (error.status >= 500) {
      errorTitle = 'Server Error';
      errorMessage = 'The server is experiencing issues. Please try again in a few moments.';
      showRetryOption = true;
    } else if (error.error?.message) {
      errorMessage = error.error.message;
    }

    if (showRetryOption) {
      Swal.fire({
        icon: 'error',
        title: errorTitle,
        text: errorMessage,
        showCancelButton: true,
        confirmButtonText: 'Retry',
        cancelButtonText: 'Logout',
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#dc3545'
      }).then((result) => {
        if (result.isConfirmed) {
          // Retry session extension
          this.extendSession();
        } else {
          this.authService.logout(false, 'Failed to extend session');
        }
      });
    } else {
      this.showSessionExtensionError(errorMessage, errorTitle);
    }
  }

  /**
   * Show session extension error and logout
   */
  private showSessionExtensionError(message: string, title: string = 'Session Extension Failed'): void {
    Swal.fire({
      icon: 'error',
      title: title,
      text: message,
      confirmButtonText: 'OK',
      confirmButtonColor: '#dc3545'
    }).then(() => {
      this.authService.logout(false, 'Failed to extend session');
    });
  }

  /**
   * Show session expired notification
   */
  showSessionExpiredNotification(reason?: string): void {
    const message = reason || 'Your session has expired. Please log in again.';
    
    Swal.fire({
      icon: 'warning',
      title: 'Session Expired',
      text: message,
      confirmButtonText: 'Go to Login',
      confirmButtonColor: '#3085d6',
      allowOutsideClick: false,
      allowEscapeKey: false,
      customClass: {
        popup: 'session-expired-popup'
      }
    }).then(() => {
      // Redirect to login is handled by AuthService
    });
  }

  /**
   * Reset warning state (call when user performs an action)
   */
  resetWarningState(): void {
    this.warningShown = false;
  }

  /**
   * Reset extension cooldown (for testing purposes)
   */
  resetExtensionCooldown(): void {
    this.lastExtensionTime = 0;
    console.log('🔄 Extension cooldown reset - popup can appear immediately');
  }

  /**
   * Check if user is active and reset warning if needed
   */
  onUserActivity(): void {
    // Reset warning if user is active and session is still valid
    if (this.authService.isLoggedIn() && !this.authService.isTokenExpiringSoon()) {
      this.resetWarningState();
    }
  }

  /**
   * Manually extend session (for testing or manual triggers)
   */
  manualExtendSession(): void {
    console.log('🔄 Manual session extension requested');
    this.extendSessionWithRefreshToken();
  }

  /**
   * Force show session expiration warning (for testing)
   */
  forceShowWarning(): void {
    console.log('⚠️ Forcing session expiration warning for testing');
    this.warningShown = false;
    this.resetExtensionCooldown(); // Reset cooldown to allow forced warning
    this.showSessionExpirationWarning();
  }

  /**
   * Get current session status for debugging
   */
  getSessionStatus(): any {
    const currentUser = this.authService.currentUserValue;
    const isLoggedIn = this.authService.isLoggedIn();
    const isExpired = this.authService.isTokenExpired();
    const isExpiringSoon = this.authService.isTokenExpiringSoon();

    const timeUntilExpiryMs = currentUser?.token_expiry ? currentUser.token_expiry - Date.now() : 0;
    const timeUntilExpirySeconds = Math.round(timeUntilExpiryMs / 1000);
    const timeUntilExpiryMinutes = Math.round(timeUntilExpiryMs / (1000 * 60));

    // Calculate cooldown status
    const timeSinceLastExtension = Date.now() - this.lastExtensionTime;
    const inCooldownPeriod = timeSinceLastExtension < this.extensionCooldownMs;
    const cooldownRemainingMs = inCooldownPeriod ? this.extensionCooldownMs - timeSinceLastExtension : 0;
    const cooldownRemainingSeconds = Math.round(cooldownRemainingMs / 1000);

    const status = {
      isLoggedIn,
      isExpired,
      isExpiringSoon,
      warningShown: this.warningShown,
      tokenExpiry: currentUser?.token_expiry ? new Date(currentUser.token_expiry).toLocaleString() : 'N/A',
      timeUntilExpirySeconds: timeUntilExpirySeconds > 0 ? `${timeUntilExpirySeconds}s` : 'Expired',
      timeUntilExpiryMinutes: timeUntilExpiryMinutes > 0 ? `${timeUntilExpiryMinutes}m` : 'Expired',
      monitoringActive: !!this.checkInterval,
      extensionCooldown: {
        inCooldownPeriod,
        cooldownRemainingSeconds: cooldownRemainingSeconds > 0 ? `${cooldownRemainingSeconds}s` : 'None',
        lastExtensionTime: this.lastExtensionTime > 0 ? new Date(this.lastExtensionTime).toLocaleString() : 'Never'
      }
    };

    console.log('📊 Session Status:', status);
    return status;
  }
}
