<!-- Institutes Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-home me-2"></i>
              Institutes Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_institutes }} total institutes, 
              {{ statistics.active_institutes }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button *ngIf="viewMode === 'active'" class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Institute
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'active'" 
                    (click)="setViewMode('active')">
              <i class="feather icon-check-circle me-1"></i>
              Active Institutes
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'deleted'" 
                    (click)="setViewMode('deleted')">
              <i class="feather icon-trash-2 me-1"></i>
              Deleted Institutes
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode !== 'statistics'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-3">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search institutes..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2" *ngIf="viewMode === 'active'">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedType" (change)="onTypeFilter()">
                <option value="">All Types</option>
                <option *ngFor="let type of instituteTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedCountry" (change)="onCountryFilter()">
                <option value="">All Countries</option>
                <option *ngFor="let country of countries" [value]="country">
                  {{ country }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedRegulatoryBody" (change)="onRegulatoryBodyFilter()">
                <option value="">All Regulatory Bodies</option>
                <option *ngFor="let body of regulatoryBodies" [value]="body">
                  {{ body }}
                </option>
              </select>
            </div>
            <div class="col-md-1" *ngIf="viewMode === 'active'">
              <button class="btn btn-outline-danger w-100" 
                      [disabled]="selectedInstitutes.size === 0"
                      (click)="bulkDelete()">
                <i class="feather icon-trash-2"></i>
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading institutes...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40" *ngIf="viewMode === 'active'">
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Institute</th>
                  <th>Type</th>
                  <th>Contact</th>
                  <th>Location</th>
                  <th>Banking Codes</th>
                  <th>Branches</th>
                  <th *ngIf="viewMode === 'active'">Status</th>
                  <th *ngIf="viewMode === 'deleted'">Deleted</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let institute of getCurrentList(); trackBy: trackByInstituteId">
                  <td *ngIf="viewMode === 'active'">
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedInstitutes.has(institute.id)"
                           (change)="toggleSelection(institute.id)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ institute.name }}</strong>
                      <small class="d-block text-muted">
                        Code: {{ institute.code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="institute.description">
                        {{ institute.description }}
                      </small>
                      <small class="d-block text-muted" *ngIf="institute.website">
                        <i class="feather icon-globe me-1"></i>
                        <a [href]="institute.website" target="_blank" class="text-decoration-none">
                          {{ institute.website }}
                        </a>
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ getInstituteTypeLabel(institute.type) }}</span>
                    <small class="d-block text-muted" *ngIf="institute.regulatory_body">
                      {{ institute.regulatory_body }}
                    </small>
                  </td>
                  <td>
                    <div *ngIf="institute.contact_email || institute.contact_phone">
                      <small class="d-block text-muted" *ngIf="institute.contact_email">
                        <i class="feather icon-mail me-1"></i>
                        {{ institute.contact_email }}
                      </small>
                      <small class="d-block text-muted" *ngIf="institute.contact_phone">
                        <i class="feather icon-phone me-1"></i>
                        {{ institute.contact_phone }}
                      </small>
                    </div>
                    <span *ngIf="!institute.contact_email && !institute.contact_phone" class="text-muted fst-italic">
                      No contact info
                    </span>
                  </td>
                  <td>
                    <div *ngIf="institute.city || institute.country">
                      <small class="d-block text-muted" *ngIf="institute.city">
                        <i class="feather icon-map-pin me-1"></i>
                        {{ institute.city }}
                      </small>
                      <small class="d-block text-muted" *ngIf="institute.country">
                        {{ institute.country }}
                      </small>
                    </div>
                    <span *ngIf="!institute.city && !institute.country" class="text-muted fst-italic">
                      Not specified
                    </span>
                  </td>
                  <td>
                    <div class="banking-codes">
                      <small class="d-block text-muted" *ngIf="institute.ifsc_code">
                        <strong>IFSC:</strong> {{ institute.ifsc_code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="institute.swift_code">
                        <strong>SWIFT:</strong> {{ institute.swift_code }}
                      </small>
                      <small class="d-block text-muted" *ngIf="institute.micr_code">
                        <strong>MICR:</strong> {{ institute.micr_code }}
                      </small>
                      <span *ngIf="!institute.ifsc_code && !institute.swift_code && !institute.micr_code" 
                            class="text-muted fst-italic">
                        No codes
                      </span>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-primary">
                      {{ institute.branch_count || 0 }} branches
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'active'">
                    <span [class]="getStatusBadgeClass(institute.is_active)">
                      {{ getStatusText(institute.is_active) }}
                    </span>
                  </td>
                  <td *ngIf="viewMode === 'deleted'">
                    <small class="text-muted">
                      {{ institute.deleted_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item" (click)="openEditModal(institute)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'active'"><hr class="dropdown-divider"></li>
                        <li *ngIf="viewMode === 'active'">
                          <button class="dropdown-item text-danger" (click)="deleteInstitute(institute)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                        <li *ngIf="viewMode === 'deleted'">
                          <button class="dropdown-item text-success" (click)="restoreInstitute(institute)">
                            <i class="feather icon-refresh-cw me-2"></i>
                            Restore
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="getCurrentList().length === 0" class="text-center py-5">
              <i class="feather icon-home text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">
                {{ viewMode === 'deleted' ? 'No Deleted Institutes' : 'No Institutes Found' }}
              </h5>
              <p class="text-muted">
                <span *ngIf="viewMode === 'deleted'">
                  No institutes have been deleted yet.
                </span>
                <span *ngIf="viewMode === 'active' && searchTerm">
                  No institutes match your search criteria.
                </span>
                <span *ngIf="viewMode === 'active' && !searchTerm">
                  Get started by creating your first institute.
                </span>
              </p>
              <button *ngIf="viewMode === 'active' && !searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Institute
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} institutes
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <!-- Summary Cards -->
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_institutes }}</h3>
                      <p class="mb-0">Total Institutes</p>
                    </div>
                    <i class="feather icon-home" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_institutes }}</h3>
                      <p class="mb-0">Active Institutes</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_institutes }}</h3>
                      <p class="mb-0">Inactive Institutes</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_branches }}</h3>
                      <p class="mb-0">Total Branches</p>
                    </div>
                    <i class="feather icon-git-branch" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Institute Types Distribution -->
          <div *ngIf="statistics.institutes_by_type" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Institutes by Type</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div *ngFor="let key of getObjectKeys(statistics.institutes_by_type)" class="col-md-4 mb-3">
                  <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ getInstituteTypeLabel(key) }}</h6>
                      <small class="text-muted">{{ key }}</small>
                    </div>
                    <span class="badge bg-primary fs-6">{{ statistics.institutes_by_type[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Institutes -->
          <div *ngIf="statistics.popular_institutes?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Popular Institutes</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Institute</th>
                      <th>Type</th>
                      <th>Location</th>
                      <th>Branches</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let institute of statistics.popular_institutes">
                      <td>
                        <strong>{{ institute.name }}</strong>
                        <small class="d-block text-muted">{{ institute.code }}</small>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ getInstituteTypeLabel(institute.type) }}</span>
                      </td>
                      <td>{{ institute.city || 'N/A' }}, {{ institute.country || 'N/A' }}</td>
                      <td>
                        <span class="badge bg-primary">{{ institute.branch_count || 0 }}</span>
                      </td>
                      <td>
                        <span [class]="getStatusBadgeClass(institute.is_active)">
                          {{ getStatusText(institute.is_active) }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
