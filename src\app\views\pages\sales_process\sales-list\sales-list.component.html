<div class="row" *ngIf="showSalesForm">
  <div class="col-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Add New Sales</h6>
          <button class="btn btn-secondary" (click)="toggleSalesForm()">
            <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i>
            Back to List
          </button>
        </div>
        <form class="forms-sample" #salesForm="ngForm">
          <div class="row">

            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="leadId" class="form-label">Lead Id</label>
              <input type="text" class="form-control" id="leadId" [(ngModel)]="leadId" name="leadId" readonly autocomplete="off">
            </div>



            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="leadcategory" class="form-label">Lead Category <span class="text-danger">*</span></label>
              <select class="form-select" id="leadcategory" [(ngModel)]="selectedLeadCategory" name="leadCategory" autocomplete="off" (ngModelChange)="onLeadCategoryOrSourceChange()" required>
                <option value="" selected>
                  {{ leadCategoriesLoading ? 'Loading categories...' : 'Select Option' }}
                </option>
                <option *ngFor="let category of leadCategoriesFromApi" [value]="category.id">{{ category.name }}</option>
              </select>
              <div class="invalid-feedback" *ngIf="!selectedLeadCategory">
                Lead Category is required
              </div>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isLeadCategorySelected('Lead Data') || isLeadCategorySelected('Lead data')">
              <label for="leaddatatype" class="form-label">Lead Data Type <span class="text-danger">*</span></label>
              <select class="form-select" id="leaddatatype" [(ngModel)]="selectedLeadDataType" name="leadDataType" (change)="onLeadDataTypeChange()" autocomplete="off" required>
                <option value="" selected>
                  {{ leadDataTypesLoading ? 'Loading lead data types...' : 'Select Option' }}
                </option>
                <!-- Use API data if available, otherwise fallback to hardcoded values -->
                <ng-container *ngIf="leadDataTypesFromApi.length > 0; else hardcodedLeadDataTypes">
                  <option *ngFor="let leadDataType of leadDataTypesFromApi" [value]="leadDataType.name">
                    {{ leadDataType.name }}{{ leadDataType.code ? ' (' + leadDataType.code + ')' : '' }}
                  </option>
                </ng-container>
                <!-- Fallback to hardcoded values if API fails -->
                <ng-template #hardcodedLeadDataTypes>
                  <option value="Construction Funding">Construction Funding (CF)</option>
                  <option value="Inventory Funding">Inventory Funding (IF)</option>
                  <option value="Project Funding">Project Funding (PF)</option>
                  <option value="Hospital Funding">Hospital Funding (HF)</option>
                  <option value="Education Funding">Education Funding (EF)</option>
                  <option value="Home Loan">Home Loan (HL)</option>
                  <option value="Loan Against Property">Loan Against Property (LAP)</option>
                  <option value="Lease Rental Discounting">Lease Rental Discounting (LRD)</option>
                  <option value="Non Residential Property Loan">Non Residential Property Loan (NRPL)</option>
                  <option value="Property">Property</option>
                  <option value="Contractor All Risk">Contractor All Risk (CAR)</option>
                  <option value="Life Insurance">Life Insurance (LI)</option>
                </ng-template>
              </select>
              <div class="invalid-feedback" *ngIf="!selectedLeadDataType && (isLeadCategorySelected('Lead Data') || isLeadCategorySelected('Lead data'))">
                Lead Data Type is required when Lead Category is "Lead Data"
              </div>
            </div>





            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="shouldShowSourceDropdown()">
              <label for="source" class="form-label">Source <span class="text-danger">*</span></label>
              <select class="form-select" id="source" [(ngModel)]="selectedSource" name="source" aria-label="Default select example" autocomplete="off" placeholder="Enter Source" (ngModelChange)="onLeadCategoryOrSourceChange(); onSourceChange()" required>
                <option value="">
                  {{ sourcesLoading ? 'Loading sources...' : 'Select Option' }}
                </option>
                <option *ngFor="let source of sourcesFromApi" [value]="source.id">{{ source.name }}</option>
              </select>
              <div class="invalid-feedback" *ngIf="!selectedSource">
                Source is required
              </div>
            </div>
 <!-- Fields for Lead Category: Associate - With search functionality like in edit form -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isLeadCategorySelected('Associate')">
              <label for="associateNameCategory" class="form-label">Associate Name <span class="text-danger">*</span></label>
              <div class="position-relative">
                <input
                  type="text"
                  class="form-control"
                  id="associateNameCategory"
                  [(ngModel)]="associateNameCategory"
                  name="associateNameCategory"
                  (input)="onAssociateNameCategoryInput($event)"
                  autocomplete="off"
                  placeholder="Search Associate Name"
                  (focus)="onAssociateNameCategoryFocus()"
                  [disabled]="associatesLoading"
                  required
                  minlength="2">

                <!-- Dropdown for filtered associates -->
                <div class="dropdown-menu show w-100" *ngIf="filteredAssociates.length > 0" style="max-height: 200px; overflow-y: auto;">
                  <div class="dropdown-item-text text-muted small" *ngIf="associatesLoading">
                    Loading associates...
                  </div>
                  <button
                    type="button"
                    class="dropdown-item"
                    *ngFor="let associate of filteredAssociates | slice:0:10"
                    (click)="onAssociateSelectForCategory(associate)">
                    <div class="fw-bold">{{ associate.associate_name }}</div>
                    <div class="text-muted small">{{ associate.company_name }} - {{ associate.location_name }}</div>
                  </button>
                  <div class="dropdown-item-text text-muted small" *ngIf="filteredAssociates.length === 0 && !associatesLoading">
                    No associates found
                  </div>
                </div>
              </div>
              <div class="invalid-feedback" *ngIf="shouldShowFieldValidation('associateNameCategory', associateNameCategory)">
                <div *ngFor="let error of getFieldValidationErrors('associateNameCategory', associateNameCategory)">
                  {{ error }}
                </div>
              </div>
            </div>
            <!-- Fields for when Source is "Associate" but Lead Category is NOT Associate -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isSourceSelected('Associate') && !isLeadCategorySelected('Associate')">
              <label for="associateName" class="form-label">Associate Name</label>
              <div class="position-relative">
                <input
                  type="text"
                  class="form-control"
                  id="associateName"
                  [formControl]="associateSearchTerm"
                  autocomplete="off"
                  placeholder="Search Associate Name"
                  (focus)="onAssociateFieldFocus()"
                  [disabled]="associatesLoading">

                <!-- Dropdown for filtered associates -->
                <div class="dropdown-menu show w-100" *ngIf="filteredAssociates.length > 0" style="max-height: 200px; overflow-y: auto;">
                  <div class="dropdown-item-text text-muted small" *ngIf="associatesLoading">
                    Loading associates...
                  </div>
                  <button
                    type="button"
                    class="dropdown-item"
                    *ngFor="let associate of filteredAssociates | slice:0:10"
                    (click)="onAssociateSelect(associate)">
                    <div class="fw-bold">{{ associate.associate_name }}</div>
                    <div class="text-muted small">{{ associate.company_name }} - {{ associate.location_name }}</div>
                  </button>
                  <div class="dropdown-item-text text-muted small" *ngIf="filteredAssociates.length === 0 && !associatesLoading">
                    No associates found
                  </div>
                </div>
              </div>
            </div>
            <!-- Company Name field - shown for both Associate source and Lead Category -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isSourceSelected('Associate') || isLeadCategorySelected('Associate')">
              <label for="company" class="form-label">Company Name
                <span class="text-danger" *ngIf="isLeadCategorySelected('Lead Data') && selectedLeadDataType && ['Construction Funding', 'Hospital Funding', 'Education Funding'].includes(selectedLeadDataType)">*</span>
              </label>
              <input type="text" class="form-control" id="company" [(ngModel)]="company" name="company" autocomplete="off" placeholder="Enter Company Name"
                     [required]="isLeadCategorySelected('Lead Data') && selectedLeadDataType && ['Construction Funding', 'Hospital Funding', 'Education Funding'].includes(selectedLeadDataType)"
                     minlength="2">
              <div class="invalid-feedback" *ngIf="isLeadCategorySelected('Lead Data') && selectedLeadDataType && ['Construction Funding', 'Hospital Funding', 'Education Funding'].includes(selectedLeadDataType) && (!company || company.trim().length < 2)">
                <span *ngIf="!company">Company name is required for {{ selectedLeadDataType }}</span>
                <span *ngIf="company && company.trim().length < 2">Company name must be at least 2 characters long</span>
              </div>
            </div>





            <!-- Associate Location fields - only shown when Source is Associate but Lead Category is NOT Associate -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isSourceSelected('Associate') && !isLeadCategorySelected('Associate')">
              <label for="associateLocation" class="form-label">Associate Location</label>
              <select class="form-select" id="associateLocation" [(ngModel)]="associateLocation" name="associateLocation" autocomplete="off">
                <option value="" selected>
                  {{ locationsLoading ? 'Loading locations...' : 'Select Associate Location' }}
                </option>
                <option *ngFor="let location of locations" [value]="location.name">
                  {{ location.name }}
                </option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isSourceSelected('Associate') && !isLeadCategorySelected('Associate')">
              <label for="associateSubLocation" class="form-label">Associate Sub Location</label>
              <input type="text" class="form-control" id="associateSubLocation" [(ngModel)]="associateSubLocation" name="associateSubLocation" autocomplete="off" placeholder="Enter Associate Sub Location">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isSourceSelected('Associate') && !isLeadCategorySelected('Associate')">
              <label for="associateProfessionType" class="form-label">Profession Type</label>
              <select class="form-select" id="associateProfessionType" [(ngModel)]="associateProfessionType" name="associateProfessionType" autocomplete="off" [disabled]="professionTypesLoading" (ngModelChange)="onAssociateProfessionTypeChange()">
                <option value="" selected>
                  {{ professionTypesLoading ? 'Loading profession types...' : 'Select Profession Type' }}
                </option>
                <option *ngFor="let professionType of professionTypes" [value]="professionType.value">
                  {{ professionType.label }}
                </option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isSourceSelected('Associate') && !isLeadCategorySelected('Associate')">
              <label for="associateProfession" class="form-label">Profession</label>
              <select class="form-select" id="associateProfession" [(ngModel)]="associateProfession" name="associateProfession" autocomplete="off" [disabled]="!associateProfessionType || professionsLoading">
                <option value="" selected>
                  {{ !associateProfessionType ? 'Select Profession Type first' :
                     (professionsLoading ? 'Loading professions...' : 'Select Profession') }}
                </option>
                <option *ngFor="let profession of filteredAssociateProfessions" [value]="profession.name">
                  {{ profession.name }}
                </option>
              </select>
            </div>



            <!-- Lead Information Section for Associate Source -->
            <div class="col-12 mb-4" *ngIf="isSourceSelected('Associate') && !isLeadCategorySelected('Associate')">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">Lead Information</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="associateLeadName" class="form-label">Lead Name</label>
                      <input type="text" class="form-control" id="associateLeadName" [(ngModel)]="associateLeadName" name="associateLeadName" autocomplete="off" placeholder="Enter Lead Name">
                    </div>

                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="associateLeadLocation" class="form-label">Lead Location</label>
                      <select class="form-select" id="associateLeadLocation" [(ngModel)]="associateLeadLocation" name="associateLeadLocation" autocomplete="off">
                        <option value="">Select Lead Location</option>
                        <option *ngFor="let location of locations" [value]="location.name">{{ location.name }}</option>
                      </select>
                    </div>

                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="associateLeadSublocation" class="form-label">Lead Sub Location</label>
                      <input type="text" class="form-control" id="associateLeadSublocation" [(ngModel)]="associateLeadSublocation" name="associateLeadSublocation" autocomplete="off" placeholder="Enter Lead Sub Location">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Lead Information Section for Self Source -->
            <div class="col-12 mb-4" *ngIf="isSourceSelected('Self')">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">Lead Information</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="leadName" class="form-label">Lead Name</label>
                      <input type="text" class="form-control" id="leadName" [(ngModel)]="leadName" name="leadName" autocomplete="off" placeholder="Enter Lead Name">
                    </div>

                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="leadLocation" class="form-label">Lead Location</label>
                      <select class="form-select" id="leadLocation" [(ngModel)]="leadLocation" name="leadLocation" autocomplete="off">
                        <option value="">Select Location</option>
                        <option *ngFor="let location of locations" [value]="location.name">{{ location.name }}</option>
                      </select>
                    </div>

                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="leadSublocation" class="form-label">Lead Sub Location</label>
                      <input type="text" class="form-control" id="leadSublocation" [(ngModel)]="leadSublocation" name="leadSublocation" autocomplete="off" placeholder="Enter Lead Sub Location">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="location" class="form-label">Location</label>
              <select class="form-select" id="location" [(ngModel)]="selectedLocation" name="location" autocomplete="off">
                <option value="" selected>
                  {{ locationsLoading ? 'Loading locations...' : 'Select Location' }}
                </option>
                <option *ngFor="let location of locations" [value]="location.name">
                  {{ location.name }}
                </option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="sublocation" class="form-label">Sub Location</label>
              <input type="text" class="form-control" id="sublocation" [(ngModel)]="subLocation" name="subLocation" autocomplete="off" placeholder="Enter Location Name">
            </div> -->

            <!-- Constitution Field - Shows for both Education Funding and Hospital Funding -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="selectedLeadDataType === 'Education Funding' || selectedLeadDataType === 'Hospital Funding'">
              <label for="constitution" class="form-label">Constitution <span class="text-danger">*</span></label>
              <select class="form-select" id="constitution" [(ngModel)]="selectedConstitution" name="constitution" autocomplete="off" [disabled]="constitutionsLoading" required>
                <option value="" selected>{{ constitutionsLoading ? 'Loading...' : 'Select Constitution' }}</option>
                <option *ngFor="let constitution of constitutions" [value]="constitution.id">
                  {{ constitution.name }}
                </option>
              </select>
              <div class="invalid-feedback" *ngIf="!selectedConstitution && (selectedLeadDataType === 'Education Funding' || selectedLeadDataType === 'Hospital Funding')">
                Constitution is required for {{ selectedLeadDataType }}
              </div>
            </div>

            <!-- Board Affiliation Field - Shows only for Education Funding -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="selectedLeadDataType === 'Education Funding'">
              <label for="boardAffiliation" class="form-label">Board Affiliation <span class="text-danger">*</span></label>
              <select class="form-select" id="boardAffiliation" [(ngModel)]="selectedBoardAffiliation" name="boardAffiliation" autocomplete="off" required>
                <option value="" selected>
                  {{ boardAffiliationsLoading ? 'Loading board affiliations...' : 'Select Board' }}
                </option>
                <!-- Use API data if available, otherwise fallback to hardcoded values -->
                <ng-container *ngIf="boardAffiliationsFromApi.length > 0; else hardcodedBoardAffiliations">
                  <option *ngFor="let boardAffiliation of boardAffiliationsFromApi" [value]="boardAffiliation.name">
                    {{ boardAffiliation.name }}
                  </option>
                </ng-container>
                <!-- Fallback to hardcoded values if API fails -->
                <ng-template #hardcodedBoardAffiliations>
                  <option value="State Board">State Board</option>
                  <option value="CBSE">CBSE</option>
                  <option value="ICSE">ICSE</option>
                  <option value="IGCSE">IGCSE</option>
                </ng-template>
              </select>
              <div class="invalid-feedback" *ngIf="!selectedBoardAffiliation && selectedLeadDataType === 'Education Funding'">
                Board Affiliation is required for Education Funding
              </div>
            </div>

            <!-- University Affiliation Field - Shows only for Education Funding -->
            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="selectedLeadDataType === 'Education Funding'">
              <label for="universityAffiliation" class="form-label">University Affiliation <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="universityAffiliation" [(ngModel)]="universityAffiliation" name="universityAffiliation" autocomplete="off" placeholder="Enter University Name" required minlength="3">
              <div class="invalid-feedback" *ngIf="selectedLeadDataType === 'Education Funding' && (!universityAffiliation || universityAffiliation.trim().length < 3)">
                <span *ngIf="!universityAffiliation">University Affiliation is required for Education Funding</span>
                <span *ngIf="universityAffiliation && universityAffiliation.trim().length < 3">University Affiliation must be at least 3 characters long</span>
              </div>
            </div>



            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isLeadCategorySelected('Associate')">
              <label for="associateLocationCategory" class="form-label">Associate Location</label>
              <select class="form-select" id="associateLocationCategory" [(ngModel)]="associateLocationCategory" name="associateLocationCategory" autocomplete="off">
                <option value="">Select Associate Location</option>
                <option *ngFor="let location of locations" [value]="location.name">{{ location.name }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isLeadCategorySelected('Associate')">
              <label for="associateSubLocationCategory" class="form-label">Associate Sub Location</label>
              <input type="text" class="form-control" id="associateSubLocationCategory" [(ngModel)]="associateSubLocationCategory" name="associateSubLocationCategory" autocomplete="off" placeholder="Enter Associate Sub Location">
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isLeadCategorySelected('Associate')">
              <label for="professionalCategory" class="form-label">Professional Type</label>
              <select class="form-select" id="professionalCategory" [(ngModel)]="professionalCategory" name="professionalCategory" autocomplete="off" [disabled]="professionTypesLoading" (ngModelChange)="onProfessionalCategoryTypeChange()">
                <option value="" selected>
                  {{ professionTypesLoading ? 'Loading profession types...' : 'Select Professional' }}
                </option>
                <option *ngFor="let professionType of professionTypes" [value]="professionType.value">
                  {{ professionType.label }}
                </option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4" *ngIf="isLeadCategorySelected('Associate')">
              <label for="professionCategory" class="form-label">Profession</label>
              <select class="form-select" id="professionCategory" [(ngModel)]="professionCategory" name="professionCategory" autocomplete="off" [disabled]="!professionalCategory || professionsLoading">
                <option value="" selected>
                  {{ !professionalCategory ? 'Select Professional Type first' :
                     (professionsLoading ? 'Loading professions...' : 'Select Profession') }}
                </option>
                <option *ngFor="let profession of filteredCategoryProfessions" [value]="profession.name">
                  {{ profession.name }}
                </option>
              </select>
            </div>

            <!-- Lead Information Section - Always visible -->
            <div class="col-12 mb-4">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">Lead Information</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="leadNameCategory" class="form-label">Lead Name</label>
                      <input type="text" class="form-control" id="leadNameCategory" [(ngModel)]="leadNameCategory" name="leadNameCategory" autocomplete="off" placeholder="Enter Lead Name">
                    </div>

                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="leadLocationCategory" class="form-label">Lead Location</label>
                      <select class="form-select" id="leadLocationCategory" [(ngModel)]="leadLocationCategory" name="leadLocationCategory" autocomplete="off">
                        <option value="">Select Lead Location</option>
                        <option *ngFor="let location of locations" [value]="location.name">{{ location.name }}</option>
                      </select>
                    </div>

                    <div class="col-12 col-md-6 col-lg-4 mb-3">
                      <label for="leadSubLocationCategory" class="form-label">Lead Sub Location</label>
                      <input type="text" class="form-control" id="leadSubLocationCategory" [(ngModel)]="leadSubLocationCategory" name="leadSubLocationCategory" autocomplete="off" placeholder="Enter Lead Sub Location">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Multiple People Section with Reactive Forms -->
            <div class="col-12 mb-4">
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">People Information</h6>
                  <button type="button" class="btn btn-sm" style="background-color: #df5316; color: white;" (click)="addPerson()">
                    <i class="fas fa-plus me-1"></i>Add Person
                  </button>
                </div>
                <div class="card-body" [formGroup]="peopleForm">
                  <div formArrayName="people">
                    <div *ngFor="let personControl of peopleControls; let i = index" [formGroupName]="i" class="row mb-3 person-entry">
                      <div class="col-12 col-md-3 mb-2">
                        <label [for]="'connectWith_' + i" class="form-label">Connect With</label>
                        <select
                          class="form-select"
                          [id]="'connectWith_' + i"
                          formControlName="connectWith"
                          autocomplete="off"
                          [ngClass]="{'is-invalid': personControl.get('connectWith')?.invalid && personControl.get('connectWith')?.touched}">
                          <option value="" selected>
                            {{ connectWithLoading ? 'Loading connect with...' : 'Select Option' }}
                          </option>
                          <option *ngFor="let connectWith of connectWithList" [value]="connectWith.name">
                            {{ connectWith.name }}
                          </option>
                        </select>
                        <div *ngIf="personControl.get('connectWith')?.invalid && personControl.get('connectWith')?.touched" class="invalid-feedback">
                          Connect With is required
                        </div>
                      </div>
                      <div class="col-12 col-md-3 mb-2">
                        <label [for]="'name_' + i" class="form-label">Name</label>
                        <input
                          type="text"
                          class="form-control"
                          [id]="'name_' + i"
                          formControlName="name"
                          autocomplete="off"
                          placeholder="Enter Name"
                          [ngClass]="{'is-invalid': personControl.get('name')?.invalid && personControl.get('name')?.touched}">
                        <div *ngIf="personControl.get('name')?.invalid && personControl.get('name')?.touched" class="invalid-feedback">
                          Name is required
                        </div>
                      </div>
                      <div class="col-12 col-md-3 mb-2">
                        <label [for]="'mobile_' + i" class="form-label">Mobile</label>
                        <input
                          type="tel"
                          class="form-control"
                          [id]="'mobile_' + i"
                          formControlName="mobile"
                          autocomplete="off"
                          placeholder="Enter Mobile"
                          [ngClass]="{'is-invalid': personControl.get('mobile')?.invalid && personControl.get('mobile')?.touched}">
                        <div *ngIf="personControl.get('mobile')?.invalid && personControl.get('mobile')?.touched" class="invalid-feedback">
                          <span *ngIf="personControl.get('mobile')?.errors?.['required']">Mobile is required</span>
                          <span *ngIf="personControl.get('mobile')?.errors?.['pattern']">Mobile must be 10 digits</span>
                        </div>
                      </div>
                      <div class="col-12 col-md-3 mb-2">
                        <label [for]="'email_' + i" class="form-label">Email (Optional)</label>
                        <input
                          type="email"
                          class="form-control"
                          [id]="'email_' + i"
                          formControlName="email"
                          autocomplete="off"
                          placeholder="Enter Email (Optional)"
                          [ngClass]="{'is-invalid': personControl.get('email')?.invalid && personControl.get('email')?.touched}">
                        <div *ngIf="personControl.get('email')?.invalid && personControl.get('email')?.touched" class="invalid-feedback">
                          <span *ngIf="personControl.get('email')?.errors?.['email']">Please enter a valid email address</span>
                        </div>
                      </div>
                      <div class="col-12 col-md-1 mb-2 d-flex align-items-end" *ngIf="peopleControls.length > 1">
                        <a href="javascript:;" class="action-icon" ngbTooltip="Delete" (click)="removePerson(personControl.get('id')?.value)">
                          <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>



            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="handover" class="form-label">Handover to (Optional)</label>
              <select class="form-select" id="handover" [(ngModel)]="selectedHandover" name="handover" autocomplete="off">
                <option value="" selected>
                  {{ employeesLoading ? 'Loading employees...' : 'Select Handover Person' }}
                </option>
                <option *ngFor="let person of employees" [value]="person.value">{{ person.label }}</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="status" class="form-label">Status</label>
              <select class="form-select" id="status" [(ngModel)]="selectedStatus" name="status" autocomplete="off">
                <option value="Hot">Hot</option>
                <option value="Cold">Cold</option>
                <option value="Warm">Warm</option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="product_type" class="form-label">Product Type (Optional) </label>
              <select class="form-select" id="product_type" [(ngModel)]="selectedProductType" name="productType" (change)="onProductTypeChange()" autocomplete="off">
                <option value="" selected>
                  {{ productTypesLoading ? 'Loading product types...' : 'Select Option' }}
                </option>
                <option *ngFor="let type of productTypesFromApi" [value]="type.id">
                  {{ type.name }}
                </option>
              </select>
            </div>

            <div class="col-12 col-md-6 col-lg-3 mb-4">
              <label for="product_subtype" class="form-label">Sub Product Type (Optional)</label>
              <select class="form-select" id="product_subtype" [(ngModel)]="selectedProductSubType" name="productSubType" (change)="onProductSubTypeChange()" autocomplete="off" [disabled]="!selectedProductType">
                <option value="" selected>
                  {{ subProductTypesLoading ? 'Loading sub types...' : (!selectedProductType ? 'Select Product Type first' : 'Select Option') }}
                </option>
                <option *ngFor="let subType of availableSubTypes" [value]="subType.value">{{ subType.label }}</option>
              </select>
            </div>



          </div>



          <!-- Submit and Cancel Buttons - Only show when no product form is active -->
          <div class="row mt-4" *ngIf="!showProductOne && !showProductTwo && !showCar && !showLife && !showProperty">
            <div class="col-12 d-flex justify-content-end gap-2">
              <button type="button" class="btn btn-secondary" (click)="toggleSalesForm()">
                <i data-feather="x" class="icon-sm me-1" appFeatherIcon></i>
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" (click)="saveSalesData()" [disabled]="isSubmitting">
                <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                <i *ngIf="!isSubmitting" data-feather="check" class="icon-sm me-1" appFeatherIcon></i>
                {{ isSubmitting ? 'Submitting...' : 'Submit' }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Product Components - Only one will be visible based on Product Type and Sub Product Type selection -->
<!-- These forms are shown only when the sales form is active -->


<div *ngIf="showSalesForm && showProductOne">
  <app-product-one #productOneRef [selectedProductSubType]="getSelectedSubProductTypeName()" (submitSales)="onProductFormSubmit($event)"></app-product-one>
</div>

<div *ngIf="showSalesForm && showProductTwo">
  <app-product-two #productTwoRef [selectedProductSubType]="getSelectedSubProductTypeName()" (submitSales)="onProductFormSubmit($event)"></app-product-two>
</div>

<div *ngIf="showSalesForm && showCar">
  <app-car #carRef [selectedProductSubType]="getSelectedSubProductTypeName()" (submitSales)="onProductFormSubmit($event)"></app-car>
</div>

<div *ngIf="showSalesForm && showLife">
  <app-life #lifeRef [selectedProductSubType]="getSelectedSubProductTypeName()" (submitSales)="onProductFormSubmit($event)"></app-life>
</div>

<div *ngIf="showSalesForm && showProperty">
  <app-property #propertyRef [selectedProductSubType]="getSelectedSubProductTypeName()" (submitSales)="onProductFormSubmit($event)"></app-property>
</div>





<div class="row" *ngIf="!showSalesForm">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title and add button -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Sales List</h6>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm" (click)="forceRefreshSalesList()" title="Refresh Sales List">
              <i data-feather="refresh-cw"></i> Refresh
            </button>
            <button class="btn btn-primary" (click)="toggleSalesForm()">
              Data Insertion
            </button>
          </div>
        </div>

        <!-- Search and filter controls -->
        <div class="row mb-3">
          <div class="col-md-6 col-lg-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input
                type="text"
                class="form-control"
                placeholder="Search sales..."
                [formControl]="searchTerm"
              >
              <button
                class="btn btn-outline-secondary"
                type="button"
                (click)="clearSearch()"
                *ngIf="searchTerm.value"
                title="Clear search">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
          </div>
          <div class="col-md-6 col-lg-2 d-flex align-items-center mt-2 mt-md-0">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChange()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading Spinner -->
        <div *ngIf="salesLoading" class="d-flex justify-content-center align-items-center py-5">
          <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-muted mb-0">Loading sales list...</p>
          </div>
        </div>

        <!-- Table Container with Loading Overlay -->
        <div class="table-container" *ngIf="!salesLoading">
          <!-- Pagination Loading Overlay -->
          <div *ngIf="paginationLoading" class="pagination-loading-overlay">
            <div class="text-center">
              <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
                <span class="visually-hidden">Loading...</span>
              </div>
              <div class="mt-3 text-muted fw-medium">Loading page data...</div>
            </div>
          </div>

          <!-- Table -->
          <div class="table-responsive">
            <table class="table table-hover table-striped modern-table" [class.opacity-25]="paginationLoading">
            <thead>
              <tr>
                <th scope="col">Actions</th>
                <th scope="col" sortable="leadId" (sort)="onSort($event)">Lead ID</th>
                <th scope="col" sortable="status" (sort)="onSort($event)">Status</th>
                <th scope="col" sortable="source" (sort)="onSort($event)">Source</th>
                <th scope="col" sortable="companyName" (sort)="onSort($event)">Company Name</th>
                <!-- <th scope="col" sortable="sourceType" (sort)="onSort($event)">Source Type</th> -->
                <th scope="col" sortable="location" (sort)="onSort($event)">Location</th>

                <!-- <th scope="col" sortable="projectName" (sort)="onSort($event)">Project Name</th> -->
                <th scope="col" sortable="productType" (sort)="onSort($event)">Product Type</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let sale of filteredSales; trackBy: trackBySaleId">
                <td class="action-icons">
                  <a [routerLink]="['/sales-list/view', sale.id]" class="action-icon" ngbTooltip="View Details">
                    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                  </a>

                  <a href="javascript:;" class="action-icon" ngbTooltip="Delete" (click)="deleteSale(sale)">
                    <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                  </a>
                </td>
                <td><span class="badge bg-light-primary text-primary">{{ sale.leadId }}</span></td>
                <td>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(sale.status)">
                    {{ sale.status | titlecase }}
                  </span>
                </td>

                <td>{{ sale.source }}</td>
                <td>{{ sale.companyName }}</td>
                <!-- <td>
                  <span class="badge bg-light text-dark">
                    {{ sale.sourceType }}
                  </span>
                </td> -->
                <td>{{ sale.location }}</td>

                <!-- <td>{{ sale.projectName }}</td> -->
                <td>
                  <span class="badge bg-light text-dark">
                    {{ sale.productType }}
                  </span>
                </td>
              </tr>

              <!-- Empty state -->
              <tr *ngIf="filteredSales.length === 0">
                <td colspan="7" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="database" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No sales records found</p>
                    <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          </div>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!salesLoading">
          <div>
            <span class="text-muted" *ngIf="collectionSize > 0">
              Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, collectionSize) }} of {{ collectionSize }} entries
              <span *ngIf="paginationLoading" class="ms-2">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Loading...
              </span>
            </span>
          </div>
          <ngb-pagination
            [collectionSize]="collectionSize"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            [disabled]="paginationLoading"
            (pageChange)="handlePageChange($event)"
            class="pagination-sm"
          ></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>
