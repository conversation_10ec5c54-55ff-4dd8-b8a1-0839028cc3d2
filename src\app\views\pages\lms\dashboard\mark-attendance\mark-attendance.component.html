<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Mark Attendance</h6>
        </div>
        <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
        <!-- Add Attendance Form -->
        <div class="row">
          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="employeeCode" class="form-label">Employee Code</label>
            <select
              id="employeeCode"
              class="form-select"
              [formControl]="employeeCode"
              [disabled]="isLoadingEmployees"
            >
              <option value="">
                {{ isLoadingEmployees ? 'Loading employees...' : 'Select Employee' }}
              </option>
              <option *ngFor="let employee of employees" [value]="employee.code">
                {{ employee.code }} - {{ employee.name }}
              </option>
            </select>
            <!-- Loading indicator -->
            <div *ngIf="isLoadingEmployees" class="mt-1">
              <small class="text-muted">
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                Loading employees from API...
              </small>
            </div>
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="attendanceDate" class="form-label">Attendance Date</label>
            <input
              type="date"
              class="form-control"
              id="attendanceDate"
              [formControl]="attendanceDate"
            >
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="checkInTime" class="form-label">Check-In Time</label>
            <input
              type="time"
              class="form-control"
              id="checkInTime"
              [formControl]="checkInTime"
            >
          </div>

          <div class="col-12 col-md-6 col-lg-3 mb-3">
            <label for="checkOutTime" class="form-label">Check-Out Time</label>
            <input
              type="time"
              class="form-control"
              id="checkOutTime"
              [formControl]="checkOutTime"
            >
          </div>

          <!-- Validation message for time -->
          <div class="col-12 mb-2" *ngIf="checkInTime.value && checkOutTime.value && !isTimeValid()">
            <div class="alert alert-warning py-2">
              <i data-feather="alert-triangle" class="icon-sm me-1" appFeatherIcon></i>
              Check-out time must be after check-in time.
            </div>
          </div>

          <div class="col-12 mb-3">
            <button
              class="btn btn-primary"
              [disabled]="!isFormValid() || isSubmittingAttendance"
              (click)="addAttendance()"
            >
              <span *ngIf="isSubmittingAttendance" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              <i *ngIf="!isSubmittingAttendance" data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              {{ isSubmittingAttendance ? 'Adding...' : 'Add Attendance' }}
            </button>
          </div>
        </div>

        <!-- Success message -->
        <div class="alert alert-success mt-3" *ngIf="showSuccessMessage">
          <i data-feather="check-circle" class="icon-sm me-1" appFeatherIcon></i>
          Attendance marked successfully!
        </div>

        <!-- Divider -->
        <hr class="my-4">

        <!-- Bulk Attendance Upload Section -->
        <div class="row">
          <div class="col-12 mb-3">
            <h6 class="card-title">Bulk Attendance Upload</h6>

          </div>

          <div class="col-12 col-md-6 mb-3">
            <label for="bulkAttendanceFile" class="form-label">Select File</label>
            <input
              type="file"
              class="form-control"
              id="bulkAttendanceFile"
              accept=".csv, .xlsx, .xls"
              (change)="onFileSelected($event)"
            >
            <small class="form-text text-muted">Supported formats: CSV, Excel (.xlsx, .xls)</small>
          </div>

          <div class="col-12 mb-3">
            <button
              class="btn btn-primary me-2"
              [disabled]="!selectedFile || isUploadingFile"
              (click)="uploadBulkAttendance()"
            >
              <span *ngIf="isUploadingFile" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              <i *ngIf="!isUploadingFile" data-feather="upload" class="icon-sm me-1" appFeatherIcon></i>
              {{ isUploadingFile ? 'Uploading...' : 'Upload Bulk Attendance' }}
            </button>
            <button
              class="btn btn-primary"
              [disabled]="isDownloadingTemplate"
              (click)="downloadBulkTemplate()"
            >
              <span *ngIf="isDownloadingTemplate" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              <i *ngIf="!isDownloadingTemplate" data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
              {{ isDownloadingTemplate ? 'Downloading...' : 'Download Template' }}
            </button>
          </div>

          <!-- Bulk upload success message -->
          <div class="col-12">
            <div class="alert alert-success mt-2" *ngIf="showBulkUploadSuccessMessage">
              <i data-feather="check-circle" class="icon-sm me-1" appFeatherIcon></i>
              Bulk attendance data uploaded successfully!
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
