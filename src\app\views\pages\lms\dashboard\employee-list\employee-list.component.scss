

// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  thead {
    background-color: rgba(var(--bs-primary-rgb), 0.05);

    th {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 12px 10px;
      border-top: none;
      border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.08);
      }

      &.asc:after, &.desc:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      &.asc:after {
        border-bottom: 4px solid var(--bs-primary);
      }

      &.desc:after {
        border-top: 4px solid var(--bs-primary);
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.02);
      }

      td {
        vertical-align: middle;
        padding: 12px 10px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
      }
    }
  }
}



// Empty state styling
.empty-state {
  padding: 20px;
  color: var(--bs-gray-600);

  i {
    color: var(--bs-gray-400);
  }
}

// Pagination styling
.form-select-sm {
  min-width: 60px;
  height: 30px;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

// Pagination component styling
:host ::ng-deep {
  .pagination {
    margin-bottom: 0;

    .page-link {
      color: var(--bs-primary);
      background-color: #fff;
      border: 1px solid #dee2e6;

      &:hover {
        z-index: 2;
        color: var(--bs-primary);
        background-color: #e9ecef;
        border-color: #dee2e6;
      }

      &:focus {
        z-index: 3;
        color: var(--bs-primary);
        background-color: #e9ecef;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
      }
    }

    .active > .page-link {
      z-index: 3;
      color: #fff;
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
    }
  }
}