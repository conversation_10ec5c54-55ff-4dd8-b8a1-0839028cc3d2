import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface CSSModule {
  id: string;
  href: string;
  condition?: () => boolean;
  priority: 'critical' | 'high' | 'medium' | 'low';
  preload?: boolean;
}

/**
 * Dynamic CSS Loader Service
 * 
 * Loads CSS modules dynamically based on component requirements,
 * reducing initial bundle size and improving performance.
 */
@Injectable({
  providedIn: 'root'
})
export class DynamicCSSLoaderService {
  private renderer: Renderer2;
  private loadedModules = new Set<string>();
  private loadingModules = new Set<string>();
  private loadedSubject = new BehaviorSubject<string[]>([]);

  public loaded$ = this.loadedSubject.asObservable();

  // CSS modules that can be loaded dynamically
  private cssModules: CSSModule[] = [
    {
      id: 'highlight-js',
      href: 'highlight.js/styles/atom-one-light.css',
      priority: 'medium',
      condition: () => document.querySelector('code[class*="language-"]') !== null
    },
    {
      id: 'owl-carousel',
      href: 'ngx-owl-carousel-o/lib/styles/scss/owl.carousel.scss',
      priority: 'low',
      condition: () => document.querySelector('owl-carousel-o') !== null
    },
    {
      id: 'owl-carousel-theme',
      href: 'ngx-owl-carousel-o/lib/styles/scss/owl.theme.default.scss',
      priority: 'low',
      condition: () => document.querySelector('owl-carousel-o') !== null
    },
    {
      id: 'animate-css',
      href: 'animate.css/animate.min.css',
      priority: 'low',
      condition: () => document.querySelector('[class*="animate__"]') !== null
    },
    {
      id: 'ng-select',
      href: '@ng-select/ng-select/themes/default.theme.css',
      priority: 'high',
      condition: () => document.querySelector('ng-select') !== null
    },
    {
      id: 'dropzone',
      href: 'dropzone/dist/min/dropzone.min.css',
      priority: 'medium',
      condition: () => document.querySelector('.dropzone') !== null
    },
    {
      id: 'quill',
      href: 'quill/dist/quill.snow.css',
      priority: 'medium',
      condition: () => document.querySelector('quill-editor') !== null
    },
    {
      id: 'archwizard',
      href: '@rg-software/angular-archwizard/styles/archwizard.css',
      priority: 'medium',
      condition: () => document.querySelector('aw-wizard') !== null
    },
    {
      id: 'ngx-datatable',
      href: '@siemens/ngx-datatable/index.css',
      priority: 'high',
      condition: () => document.querySelector('ngx-datatable') !== null
    },
    {
      id: 'ngx-datatable-bootstrap',
      href: '@siemens/ngx-datatable/themes/bootstrap.css',
      priority: 'high',
      condition: () => document.querySelector('ngx-datatable') !== null
    },
    {
      id: 'ngx-datatable-icons',
      href: '@siemens/ngx-datatable/assets/icons.css',
      priority: 'high',
      condition: () => document.querySelector('ngx-datatable') !== null
    }
  ];

  constructor(private rendererFactory: RendererFactory2) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    console.log('🎨 DynamicCSSLoaderService: Initialized');
  }

  /**
   * Load a CSS module by ID
   */
  loadModule(moduleId: string): Promise<void> {
    if (this.loadedModules.has(moduleId)) {
      console.log(`✅ DynamicCSSLoaderService: Module ${moduleId} already loaded`);
      return Promise.resolve();
    }

    if (this.loadingModules.has(moduleId)) {
      console.log(`⏳ DynamicCSSLoaderService: Module ${moduleId} already loading`);
      return Promise.resolve();
    }

    const module = this.cssModules.find(m => m.id === moduleId);
    if (!module) {
      console.warn(`⚠️ DynamicCSSLoaderService: Module ${moduleId} not found`);
      return Promise.reject(new Error(`Module ${moduleId} not found`));
    }

    return this.loadCSSFile(module);
  }

  /**
   * Load multiple CSS modules
   */
  loadModules(moduleIds: string[]): Promise<void[]> {
    const loadPromises = moduleIds.map(id => this.loadModule(id));
    return Promise.all(loadPromises);
  }

  /**
   * Load CSS modules based on DOM conditions
   */
  loadConditionalModules(): void {
    const modulesToLoad = this.cssModules.filter(module => {
      if (!module.condition) return false;
      if (this.loadedModules.has(module.id)) return false;
      if (this.loadingModules.has(module.id)) return false;
      
      return module.condition();
    });

    console.log(`🔍 DynamicCSSLoaderService: Found ${modulesToLoad.length} modules to load conditionally`);

    modulesToLoad.forEach(module => {
      this.loadCSSFile(module).catch(error => {
        console.error(`❌ DynamicCSSLoaderService: Failed to load conditional module ${module.id}:`, error);
      });
    });
  }

  /**
   * Preload critical CSS modules
   */
  preloadCriticalModules(): void {
    const criticalModules = this.cssModules.filter(m => m.priority === 'critical');
    
    console.log(`🚀 DynamicCSSLoaderService: Preloading ${criticalModules.length} critical modules`);
    
    criticalModules.forEach(module => {
      this.loadCSSFile(module).catch(error => {
        console.error(`❌ DynamicCSSLoaderService: Failed to preload critical module ${module.id}:`, error);
      });
    });
  }

  /**
   * Load high priority modules after initial load
   */
  loadHighPriorityModules(): void {
    setTimeout(() => {
      const highPriorityModules = this.cssModules.filter(m => 
        m.priority === 'high' && !this.loadedModules.has(m.id)
      );
      
      console.log(`⚡ DynamicCSSLoaderService: Loading ${highPriorityModules.length} high priority modules`);
      
      highPriorityModules.forEach((module, index) => {
        setTimeout(() => {
          this.loadCSSFile(module).catch(error => {
            console.error(`❌ DynamicCSSLoaderService: Failed to load high priority module ${module.id}:`, error);
          });
        }, index * 100);
      });
    }, 1000);
  }

  /**
   * Load medium and low priority modules during idle time
   */
  loadLowPriorityModules(): void {
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        this.loadRemainingModules();
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        this.loadRemainingModules();
      }, 5000);
    }
  }

  /**
   * Check if a module is loaded
   */
  isModuleLoaded(moduleId: string): boolean {
    return this.loadedModules.has(moduleId);
  }

  /**
   * Get loading statistics
   */
  getLoadingStats(): {
    totalModules: number;
    loadedModules: number;
    loadingModules: number;
    loadedModuleIds: string[];
  } {
    return {
      totalModules: this.cssModules.length,
      loadedModules: this.loadedModules.size,
      loadingModules: this.loadingModules.size,
      loadedModuleIds: Array.from(this.loadedModules)
    };
  }

  /**
   * Load a CSS file
   */
  private loadCSSFile(module: CSSModule): Promise<void> {
    return new Promise((resolve, reject) => {
      this.loadingModules.add(module.id);
      
      console.log(`📥 DynamicCSSLoaderService: Loading ${module.id} (${module.priority} priority)`);

      const link = this.renderer.createElement('link');
      this.renderer.setAttribute(link, 'rel', 'stylesheet');
      this.renderer.setAttribute(link, 'type', 'text/css');
      this.renderer.setAttribute(link, 'href', this.resolveModulePath(module.href));
      this.renderer.setAttribute(link, 'data-module-id', module.id);

      // Handle load success
      this.renderer.listen(link, 'load', () => {
        this.loadingModules.delete(module.id);
        this.loadedModules.add(module.id);
        
        console.log(`✅ DynamicCSSLoaderService: Loaded ${module.id}`);
        
        this.updateLoadedSubject();
        resolve();
      });

      // Handle load error
      this.renderer.listen(link, 'error', (error) => {
        this.loadingModules.delete(module.id);
        
        console.error(`❌ DynamicCSSLoaderService: Failed to load ${module.id}:`, error);
        reject(error);
      });

      // Append to head
      this.renderer.appendChild(document.head, link);
    });
  }

  /**
   * Load remaining medium and low priority modules
   */
  private loadRemainingModules(): void {
    const remainingModules = this.cssModules.filter(m => 
      (m.priority === 'medium' || m.priority === 'low') && 
      !this.loadedModules.has(m.id) &&
      !this.loadingModules.has(m.id)
    );

    console.log(`😴 DynamicCSSLoaderService: Loading ${remainingModules.length} remaining modules during idle time`);

    remainingModules.forEach((module, index) => {
      setTimeout(() => {
        this.loadCSSFile(module).catch(error => {
          console.error(`❌ DynamicCSSLoaderService: Failed to load remaining module ${module.id}:`, error);
        });
      }, index * 200);
    });
  }

  /**
   * Resolve module path (can be enhanced for CDN, versioning, etc.)
   */
  private resolveModulePath(href: string): string {
    // For now, assume modules are in node_modules
    // This can be enhanced to support CDN, versioning, etc.
    if (href.startsWith('http')) {
      return href;
    }
    return `node_modules/${href}`;
  }

  /**
   * Update the loaded modules subject
   */
  private updateLoadedSubject(): void {
    this.loadedSubject.next(Array.from(this.loadedModules));
  }

  /**
   * Unload a CSS module (for testing/cleanup)
   */
  unloadModule(moduleId: string): void {
    const link = document.querySelector(`link[data-module-id="${moduleId}"]`);
    if (link) {
      this.renderer.removeChild(document.head, link);
      this.loadedModules.delete(moduleId);
      this.updateLoadedSubject();
      console.log(`🗑️ DynamicCSSLoaderService: Unloaded ${moduleId}`);
    }
  }
}
