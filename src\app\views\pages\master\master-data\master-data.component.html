<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title and data type selector -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <div class="d-flex align-items-center">
            <h6 class="card-title mb-0 me-3">Master Data Management</h6>
            <!-- Data type selector -->
            <div class="dropdown">
              <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i data-feather="database" class="icon-sm me-1" appFeatherIcon></i>
                {{ currentDataType.label }}
              </button>
              <ul class="dropdown-menu">
                <li *ngFor="let dataType of masterDataTypes">
                  <a class="dropdown-item" 
                     [class.active]="dataType.key === currentDataType.key"
                     (click)="switchDataType(dataType)">
                    <i data-feather="chevron-right" class="icon-sm me-2" appFeatherIcon></i>
                    {{ dataType.label }}
                    <small class="text-muted d-block">{{ dataType.description }}</small>
                  </a>
                </li>
              </ul>
            </div>
          </div>
          
          <div class="d-flex gap-2">
            <!-- Export button -->
            <button class="btn btn-outline-success btn-sm" 
                    (click)="exportItems()" 
                    [disabled]="exporting || items.length === 0">
              <span *ngIf="exporting" class="spinner-border spinner-border-sm me-1" role="status"></span>
              <i *ngIf="!exporting" data-feather="download" class="icon-sm me-1" appFeatherIcon></i>
              Export
            </button>
            
            <!-- Bulk upload button -->
            <div class="btn-group">
              <button class="btn btn-outline-info btn-sm" 
                      data-bs-toggle="modal" 
                      data-bs-target="#bulkUploadModal">
                <i data-feather="upload" class="icon-sm me-1" appFeatherIcon></i>
                Bulk Upload
              </button>
            </div>
            
            <!-- Add new button -->
            <button class="btn btn-primary btn-sm" (click)="openItemModal(itemModal)">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add New {{ currentDataType.label.slice(0, -1) }}
            </button>
          </div>
        </div>

        <!-- Current data type info -->
        <div class="alert alert-info mb-3">
          <div class="d-flex align-items-center">
            <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
            <div>
              <strong>{{ currentDataType.label }}</strong>
              <p class="mb-0 small">{{ currentDataType.description }}</p>
            </div>
          </div>
        </div>

        <!-- Search and filter -->
        <div class="row mb-3">
          <div class="col-12 col-md-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input type="text" class="form-control" [formControl]="searchTerm"
                placeholder="Search by name{{ supportsType ? ', type' : '' }}{{ supportsStatus ? ', or status' : '' }}...">
              <button *ngIf="searchTerm.value" class="input-group-text bg-light text-danger"
                (click)="searchTerm.setValue(''); loadItems()">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
            <small class="text-muted" *ngIf="searchTerm.value">
              Searching for: "{{ searchTerm.value }}"
            </small>
          </div>

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="loadItems()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col">Actions</th>
                <th scope="col" sortable="name" (sort)="onSort($event)">Name</th>
                <th scope="col" sortable="type" (sort)="onSort($event)" *ngIf="supportsType">Type</th>
                <th scope="col" sortable="status" (sort)="onSort($event)" *ngIf="supportsStatus">Status</th>
                <th scope="col">Created</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of items">
                <td class="action-icons">
                  <button type="button" class="action-icon text-info" ngbTooltip="View"
                    (click)="viewItem(item, viewItemModal)">
                    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Edit"
                    (click)="openItemModal(itemModal, item)">
                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Delete" (click)="deleteItem(item)">
                    <i data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                  </button>

                  <button *ngIf="item.deleted_at" type="button" class="action-icon text-success"
                    ngbTooltip="Restore" (click)="restoreItem(item)">
                    <i data-feather="refresh-cw" class="icon-sm" appFeatherIcon></i>
                  </button>
                </td>
                
                <td>{{ item.name }}</td>
                
                <td *ngIf="supportsType">
                  <span class="badge bg-light text-dark">
                    {{ item.type }}
                  </span>
                </td>
                
                <td *ngIf="supportsStatus">
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(item.status)">
                    {{ item.status | titlecase }}
                  </span>
                </td>
                
                <td>
                  <small class="text-muted">{{ item.created_at | date:'short' }}</small>
                </td>
              </tr>
              
              <tr *ngIf="items.length === 0">
                <td [attr.colspan]="4 + (supportsType ? 1 : 0) + (supportsStatus ? 1 : 0)" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="database" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No {{ currentDataType.label.toLowerCase() }} found</p>
                    <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!loading && totalItems > 0">
          <div>
            <span class="text-muted">Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems)
              }} of {{ totalItems }} entries</span>
          </div>
          <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="pageSize" [maxSize]="5"
            [rotate]="true" [boundaryLinks]="true" (pageChange)="onPageChange($event)"
            class="pagination-sm"></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Item Modal (Create/Edit) -->
<ng-template #itemModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">{{ formMode === 'create' ? 'Add New' : 'Edit' }} {{ currentDataType.label.slice(0, -1) }}</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="itemForm">
      <div class="mb-3">
        <label for="name" class="form-label">{{ currentDataType.label.slice(0, -1) }} Name</label>
        <input type="text" class="form-control" id="name" formControlName="name" 
               [placeholder]="'Enter ' + currentDataType.label.toLowerCase().slice(0, -1) + ' name'">
        <div *ngIf="itemForm.get('name')?.invalid && itemForm.get('name')?.touched" class="text-danger">
          Name is required
        </div>
      </div>

      <div class="mb-3" *ngIf="supportsType">
        <label for="type" class="form-label">Type</label>
        <select class="form-select" id="type" formControlName="type">
          <option value="Profession">Profession</option>
          <option value="Non Profession">Non Profession</option>
        </select>
      </div>

      <div class="mb-3" *ngIf="supportsStatus">
        <label for="status" class="form-label">Status</label>
        <select class="form-select" id="status" formControlName="status">
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    <button type="button" class="btn btn-primary" [disabled]="itemForm.invalid || submitting"
      (click)="saveItem()">
      <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
      {{ formMode === 'create' ? 'Create' : 'Update' }}
    </button>
  </div>
</ng-template>

<!-- View Item Modal -->
<ng-template #viewItemModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">View {{ currentDataType.label.slice(0, -1) }}</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body" *ngIf="selectedItem">
    <div class="card">
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Name:</div>
          <div class="col-md-8">{{ selectedItem.name }}</div>
        </div>

        <div class="row mb-3" *ngIf="supportsType && selectedItem.type">
          <div class="col-md-4 fw-bold">Type:</div>
          <div class="col-md-8">{{ selectedItem.type }}</div>
        </div>

        <div class="row mb-3" *ngIf="supportsStatus && selectedItem.status">
          <div class="col-md-4 fw-bold">Status:</div>
          <div class="col-md-8">
            <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(selectedItem.status)">
              {{ selectedItem.status | titlecase }}
            </span>
          </div>
        </div>

        <div class="row mb-3" *ngIf="selectedItem.created_at">
          <div class="col-md-4 fw-bold">Created:</div>
          <div class="col-md-8">{{ selectedItem.created_at | date:'medium' }}</div>
        </div>

        <div class="row mb-3" *ngIf="selectedItem.updated_at">
          <div class="col-md-4 fw-bold">Last Updated:</div>
          <div class="col-md-8">{{ selectedItem.updated_at | date:'medium' }}</div>
        </div>

        <div class="row mb-3" *ngIf="selectedItem.deleted_at">
          <div class="col-md-4 fw-bold">Deleted:</div>
          <div class="col-md-8">{{ selectedItem.deleted_at | date:'medium' }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Close</button>
    <button type="button" class="btn btn-primary" (click)="editFromViewModal(itemModal, modal)">
      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
      Edit
    </button>
  </div>
</ng-template>

<!-- Bulk Upload Modal -->
<div class="modal fade" id="bulkUploadModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Bulk Upload {{ currentDataType.label }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-info">
          <i data-feather="info" class="icon-sm me-2" appFeatherIcon></i>
          Upload an Excel file (.xlsx) containing {{ currentDataType.label.toLowerCase() }} data.
        </div>

        <div class="mb-3">
          <label for="fileInput" class="form-label">Select File</label>
          <input type="file" class="form-control" id="fileInput"
                 accept=".xlsx,.xls" (change)="onFileSelected($event)">
          <div class="form-text">
            Supported formats: .xlsx, .xls
          </div>
        </div>

        <div *ngIf="selectedFile" class="alert alert-success">
          <i data-feather="file" class="icon-sm me-2" appFeatherIcon></i>
          Selected file: {{ selectedFile.name }}
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary"
                [disabled]="!selectedFile || uploading"
                (click)="bulkUpload()" data-bs-dismiss="modal">
          <span *ngIf="uploading" class="spinner-border spinner-border-sm me-1" role="status"></span>
          <i *ngIf="!uploading" data-feather="upload" class="icon-sm me-1" appFeatherIcon></i>
          Upload
        </button>
      </div>
    </div>
  </div>
</div>
