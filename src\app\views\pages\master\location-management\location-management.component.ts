import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  LocationService,
  Location,
  LocationStatistics
} from '../../../../core/services/location.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { LocationFormComponent } from './location-form/location-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-location-management',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective,
    LocationFormComponent,
    BulkUploadComponent
  ],
  templateUrl: './location-management.component.html',
  styleUrls: ['./location-management.component.scss']
})
export class LocationManagementComponent implements OnInit {
  // Data properties
  locations: Location[] = [];
  deletedLocations: Location[] = [];
  statistics: LocationStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedType = '';
  selectedLevel: number | null = null;
  selectedParent = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedLocations: Set<string> = new Set();
  selectAll = false;

  // Filter options
  locationTypes: any[] = [];
  timeZones: string[] = [];
  currencies: string[] = [];
  languages: string[] = [];

  constructor(
    private locationService: LocationService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadLocations();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.locationTypes = this.locationService.getLocationTypes();
    this.timeZones = this.locationService.getTimeZones();
    this.currencies = this.locationService.getCurrencies();
    this.languages = this.locationService.getLanguages();
  }

  /**
   * Load locations with current filters
   */
  loadLocations(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      type: this.selectedType || undefined,
      level: this.selectedLevel || undefined,
      parent_id: this.selectedParent || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.locationService.getLocationsWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedLocations = response.data.filter(l => l.deleted_at);
            this.locations = [];
          } else {
            this.locations = response.data.filter(l => !l.deleted_at);
            this.deletedLocations = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load locations';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load locations. Please try again.'
        });
      }
    });
  }

  /**
   * Load location statistics
   */
  loadStatistics(): void {
    this.locationService.getLocationStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search locations
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadLocations();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadLocations();
  }

  /**
   * Filter by type
   */
  onTypeFilter(): void {
    this.currentPage = 1;
    this.loadLocations();
  }

  /**
   * Filter by level
   */
  onLevelFilter(): void {
    this.currentPage = 1;
    this.loadLocations();
  }

  /**
   * Filter by parent
   */
  onParentFilter(): void {
    this.currentPage = 1;
    this.loadLocations();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedLocations.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadLocations();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadLocations();
  }

  /**
   * Open create location modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(LocationFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadLocations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Location created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit location modal
   */
  openEditModal(location: Location): void {
    const modalRef = this.modalService.open(LocationFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.location = { ...location };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadLocations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Location updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete location
   */
  deleteLocation(location: Location): void {
    this.popupService.showConfirmation({
      title: 'Delete Location',
      message: `Are you sure you want to delete "${location.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.locationService.deleteLocation(location.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadLocations();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Location deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete location.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore location
   */
  restoreLocation(location: Location): void {
    this.popupService.showConfirmation({
      title: 'Restore Location',
      message: `Are you sure you want to restore "${location.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.locationService.restoreLocation(location.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadLocations();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Location restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore location.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadLocations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Locations uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.locationService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'locations_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadLocations();
    this.loadStatistics();
  }

  /**
   * Get location type label
   */
  getLocationTypeLabel(type: string): string {
    return this.locationService.getLocationTypeLabel(type);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Format coordinates
   */
  formatCoordinates(location: Location): string {
    if (location.latitude && location.longitude) {
      return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
    }
    return 'Not specified';
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): Location[] {
    return this.viewMode === 'deleted' ? this.deletedLocations : this.locations;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByLocationId(index: number, location: Location): string {
    return location.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
