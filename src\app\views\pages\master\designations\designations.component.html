<!-- Designations Management Component -->
<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="card-title mb-1">
              <i class="feather icon-award me-2"></i>
              Designations Management
            </h4>
            <p class="text-muted mb-0" *ngIf="statistics">
              {{ statistics.total_designations }} total designations, 
              {{ statistics.active_designations }} active
            </p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" (click)="downloadTemplate()">
              <i class="feather icon-download me-1"></i>
              Template
            </button>
            <button class="btn btn-outline-primary" (click)="openBulkUploadModal()">
              <i class="feather icon-upload me-1"></i>
              Bulk Upload
            </button>
            <button class="btn btn-outline-secondary" (click)="refresh()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-1"></i>
              Refresh
            </button>
            <button class="btn btn-primary" (click)="openCreateModal()">
              <i class="feather icon-plus me-1"></i>
              Add Designation
            </button>
          </div>
        </div>

        <!-- View Mode Tabs -->
        <ul class="nav nav-tabs mb-3">
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'list'" 
                    (click)="setViewMode('list')">
              <i class="feather icon-list me-1"></i>
              List View
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'levels'" 
                    (click)="setViewMode('levels')">
              <i class="feather icon-layers me-1"></i>
              Levels View
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="viewMode === 'statistics'" 
                    (click)="setViewMode('statistics')">
              <i class="feather icon-bar-chart-2 me-1"></i>
              Statistics
            </button>
          </li>
        </ul>

        <!-- List View -->
        <div *ngIf="viewMode === 'list'">
          
          <!-- Search and Filters -->
          <div class="row mb-3">
            <div class="col-md-3">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="feather icon-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search designations..." 
                       [(ngModel)]="searchTerm" (input)="onSearch()">
              </div>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedStatus" (change)="onStatusFilter()">
                <option value="all">All Status</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" [(ngModel)]="selectedLevel" (change)="onLevelFilter()">
                <option value="">All Levels</option>
                <option *ngFor="let level of levels" [value]="level.level">
                  Level {{ level.level }} - {{ level.name }}
                </option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select" [(ngModel)]="selectedDepartment" (change)="onDepartmentFilter()">
                <option value="">All Departments</option>
                <option *ngFor="let dept of departments" [value]="dept.id">
                  {{ dept.name }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <button class="btn btn-outline-danger w-100" 
                      [disabled]="selectedDesignations.size === 0"
                      (click)="bulkDelete()">
                <i class="feather icon-trash-2 me-1"></i>
                Delete Selected
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div *ngIf="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading designations...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="error && !loading" class="alert alert-danger">
            <i class="feather icon-alert-circle me-2"></i>
            {{ error }}
          </div>

          <!-- Data Table -->
          <div *ngIf="!loading && !error" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="40">
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectAll" (change)="toggleSelectAll()">
                  </th>
                  <th>Designation Name</th>
                  <th>Level</th>
                  <th>Department</th>
                  <th>Salary Range</th>
                  <th>Employees</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let designation of designations; trackBy: trackByDesignationId">
                  <td>
                    <input type="checkbox" class="form-check-input" 
                           [checked]="selectedDesignations.has(designation.id)"
                           (change)="toggleSelection(designation.id)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ designation.name }}</strong>
                      <small class="d-block text-muted" *ngIf="designation.description">
                        {{ designation.description }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">
                      Level {{ designation.level }}
                    </span>
                    <small class="d-block text-muted">
                      {{ getLevelName(designation.level) }}
                    </small>
                  </td>
                  <td>
                    <span *ngIf="designation.department_name" class="text-muted">
                      {{ designation.department_name }}
                    </span>
                    <span *ngIf="!designation.department_name" class="text-muted fst-italic">
                      No Department
                    </span>
                  </td>
                  <td>
                    <small class="text-muted">
                      {{ formatSalaryRange(designation.salary_range_min, designation.salary_range_max) }}
                    </small>
                  </td>
                  <td>
                    <span class="badge bg-light text-dark">
                      {{ designation.employee_count || 0 }} employees
                    </span>
                  </td>
                  <td>
                    <span [class]="getStatusBadgeClass(designation.is_active)">
                      {{ getStatusText(designation.is_active) }}
                    </span>
                  </td>
                  <td>
                    <small class="text-muted">
                      {{ designation.created_at | date:'short' }}
                    </small>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                              type="button" data-bs-toggle="dropdown">
                        <i class="feather icon-more-horizontal"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li>
                          <button class="dropdown-item" (click)="openEditModal(designation)">
                            <i class="feather icon-edit me-2"></i>
                            Edit
                          </button>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                          <button class="dropdown-item text-danger" (click)="deleteDesignation(designation)">
                            <i class="feather icon-trash-2 me-2"></i>
                            Delete
                          </button>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="designations.length === 0" class="text-center py-5">
              <i class="feather icon-award text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3 text-muted">No Designations Found</h5>
              <p class="text-muted">
                {{ searchTerm ? 'No designations match your search criteria.' : 'Get started by creating your first designation.' }}
              </p>
              <button *ngIf="!searchTerm" class="btn btn-primary" (click)="openCreateModal()">
                <i class="feather icon-plus me-1"></i>
                Create Designation
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalItems > pageSize" class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              Showing {{ (currentPage - 1) * pageSize + 1 }} to 
              {{ Math.min(currentPage * pageSize, totalItems) }} of {{ totalItems }} designations
            </div>
            <ngb-pagination 
              [(page)]="currentPage" 
              [pageSize]="pageSize" 
              [collectionSize]="totalItems"
              [maxSize]="5"
              [rotate]="true"
              (pageChange)="onPageChange($event)">
            </ngb-pagination>
          </div>
        </div>

        <!-- Levels View -->
        <div *ngIf="viewMode === 'levels'">
          <div *ngIf="levels.length > 0" class="row">
            <div *ngFor="let level of levels" class="col-md-4 mb-3">
              <div class="card h-100">
                <div class="card-header bg-primary text-white">
                  <h6 class="mb-0">
                    <i class="feather icon-layers me-2"></i>
                    Level {{ level.level }} - {{ level.name }}
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-6">
                      <div class="text-center">
                        <h4 class="text-primary">{{ level.designation_count }}</h4>
                        <small class="text-muted">Designations</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="text-center">
                        <h4 class="text-success" *ngIf="level.avg_salary">
                          ₹{{ level.avg_salary | number:'1.0-0' }}
                        </h4>
                        <h4 class="text-muted" *ngIf="!level.avg_salary">N/A</h4>
                        <small class="text-muted">Avg Salary</small>
                      </div>
                    </div>
                  </div>
                  <p class="text-muted mt-2 mb-0">{{ level.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="levels.length === 0" class="text-center py-5">
            <i class="feather icon-layers text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">No Levels Data</h5>
            <p class="text-muted">Create designations to see level information.</p>
          </div>
        </div>

        <!-- Statistics View -->
        <div *ngIf="viewMode === 'statistics'">
          <div *ngIf="statistics" class="row">
            <div class="col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.total_designations }}</h3>
                      <p class="mb-0">Total Designations</p>
                    </div>
                    <i class="feather icon-award" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.active_designations }}</h3>
                      <p class="mb-0">Active Designations</p>
                    </div>
                    <i class="feather icon-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.inactive_designations }}</h3>
                      <p class="mb-0">Inactive Designations</p>
                    </div>
                    <i class="feather icon-pause-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h3 class="mb-1">{{ statistics.levels_count }}</h3>
                      <p class="mb-0">Total Levels</p>
                    </div>
                    <i class="feather icon-layers" style="font-size: 2rem; opacity: 0.7;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Popular Designations -->
          <div *ngIf="statistics.popular_designations?.length > 0" class="card mt-3">
            <div class="card-header">
              <h6 class="card-title mb-0">Popular Designations</h6>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Designation</th>
                      <th>Level</th>
                      <th>Department</th>
                      <th>Employee Count</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let designation of statistics.popular_designations">
                      <td>{{ designation.name }}</td>
                      <td>
                        <span class="badge bg-info">Level {{ designation.level }}</span>
                      </td>
                      <td>{{ designation.department_name || 'N/A' }}</td>
                      <td>
                        <span class="badge bg-light text-dark">{{ designation.employee_count || 0 }}</span>
                      </td>
                      <td>
                        <span [class]="getStatusBadgeClass(designation.is_active)">
                          {{ getStatusText(designation.is_active) }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
