import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '../services/auth.service';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class LmsDashboardGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router,
    private http: HttpClient
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {

    console.log('🛡️ LmsDashboardGuard checking access for:', state.url);

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    console.log('👤 Current user:', currentUser.email);

    // Fetch user roles from the correct endpoint
    return this.fetchUserRoles().pipe(
      map((roles: any[]) => {
        console.log('✅ User roles fetched successfully:', roles);

        // Check if user has any roles (basic access control)
        if (!roles || roles.length === 0) {
          console.log('❌ User has no roles assigned, denying access');
          this.router.navigate(['/auth/login']);
          return false;
        }

        // Update the current user with role information for better integration
        this.updateUserWithRoleInfo(roles);

        console.log('✅ User has roles, allowing access to LMS dashboard');
        return true;
      }),
      catchError((error) => {
        console.error('❌ Failed to fetch user roles - access denied:', error);
        console.log('🚨 No fallback mechanism - user must have valid roles from API');

        // Show error message and redirect to login
        alert('Unable to load user roles. Please contact your administrator or try logging in again.');
        this.router.navigate(['/auth/login']);
        return of(false);
      })
    );
  }

  /**
   * Fetch user roles from the correct API endpoint
   */
  private fetchUserRoles(): Observable<any[]> {
    const apiUrl = `${environment.apiUrl}/api/v1/user-roles/me/roles`;

    console.log('🔄 LmsDashboardGuard: Fetching user roles from:', apiUrl);

    // Get current user token for authentication
    const currentUser = this.authService.currentUserValue;
    if (!currentUser?.access_token) {
      console.error('❌ LmsDashboardGuard: No access token available');
      return throwError(() => new Error('No access token available'));
    }

    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${currentUser.access_token}`
    };

    console.log('🔑 LmsDashboardGuard: Using token:', currentUser.access_token.substring(0, 20) + '...');

    return this.http.get<any>(apiUrl, { headers }).pipe(
      map((response: any) => {
        console.log('📋 LmsDashboardGuard: API Response:', response);

        if (response.success && response.data && Array.isArray(response.data)) {
          console.log('✅ LmsDashboardGuard: Found roles in response.data');
          return response.data;
        } else if (Array.isArray(response)) {
          console.log('✅ LmsDashboardGuard: Found roles as direct array');
          return response;
        } else {
          console.warn('⚠️ LmsDashboardGuard: Unexpected API response format:', response);
          return [];
        }
      }),
      catchError((error) => {
        console.error('❌ LmsDashboardGuard: API Error:', error);
        console.error('❌ LmsDashboardGuard: Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        throw error;
      })
    );
  }



  /**
   * Update current user with role information for better integration
   */
  private updateUserWithRoleInfo(roles: any[]): void {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) return;

    try {
      // Extract role names and permissions
      const roleNames = roles.map(role => role.name);
      const allPermissions = new Set<string>();

      roles.forEach(role => {
        if (role.permissions && Array.isArray(role.permissions)) {
          role.permissions.forEach((permission: string) => allPermissions.add(permission));
        }
      });

      // Determine primary role - NO DEFAULT
      let primaryRole = null;
      if (roleNames.some(name => ['admin', 'role_admin', 'role_superuser'].includes(name))) {
        primaryRole = 'admin';
      } else if (roleNames.some(name => ['manager', 'role_manager'].includes(name))) {
        primaryRole = 'manager';
      } else if (roleNames.length > 0) {
        // Use the first role name as-is from API
        primaryRole = roleNames[0];
      }

      if (!primaryRole) {
        console.error('❌ No valid primary role found in API response');
        return false;
      }

      // Update user object
      currentUser.roles = roleNames;
      currentUser.role = primaryRole;
      currentUser.permissions = Array.from(allPermissions);

      console.log('🔄 LmsDashboardGuard: Updated user with role info:', {
        primaryRole,
        roleNames,
        permissionCount: allPermissions.size
      });

    } catch (error) {
      console.error('❌ LmsDashboardGuard: Failed to update user with role info:', error);
    }
  }
}
