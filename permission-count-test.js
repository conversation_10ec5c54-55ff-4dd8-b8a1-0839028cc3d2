/**
 * Permission Count Discrepancy Test Script
 * 
 * Run this script in the browser console when on the role-permissions page
 * to help identify why the API returns 140 permissions but Angular shows 139
 */

// Test function to analyze permission count discrepancy
async function debugPermissionCount() {
  console.log('🔍 Starting Permission Count Debug Analysis...');
  
  try {
    // Get the Angular app's injector to access services
    const appElement = document.querySelector('app-root');
    if (!appElement) {
      console.error('❌ Could not find Angular app element');
      return;
    }

    // Try to get the Angular context (this might vary based on Angular version)
    const ng = window.ng;
    if (!ng) {
      console.error('❌ Angular debugging utilities not available');
      console.log('💡 Make sure you are running in development mode');
      return;
    }

    // Get the component context
    const componentRef = ng.getContext(appElement);
    if (!componentRef) {
      console.error('❌ Could not get Angular component context');
      return;
    }

    console.log('✅ Angular context found, proceeding with analysis...');

    // Direct API call to compare with Angular service
    console.log('📡 Making direct API call to /api/v1/roles/permissions/...');
    
    const apiResponse = await fetch('/api/v1/roles/permissions/?skip=0&limit=1000', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        'Content-Type': 'application/json'
      }
    });

    if (!apiResponse.ok) {
      console.error('❌ API call failed:', apiResponse.status, apiResponse.statusText);
      return;
    }

    const apiData = await apiResponse.json();
    console.log('📊 Direct API Response:', apiData);

    if (apiData.success && apiData.data) {
      const apiPermissions = apiData.data;
      console.log(`📈 Direct API Count: ${apiPermissions.length}`);

      // Analyze the API data for potential issues
      console.log('🔍 Analyzing API data for issues...');
      
      const issues = {
        duplicateIds: [],
        missingIds: [],
        invalidNames: [],
        emptyDescriptions: 0
      };

      const seenIds = new Set();
      
      apiPermissions.forEach((permission, index) => {
        // Check for missing or invalid ID
        if (!permission.id || permission.id === null || permission.id === undefined) {
          issues.missingIds.push({ index, permission });
        } else if (seenIds.has(permission.id)) {
          issues.duplicateIds.push({ index, id: permission.id });
        } else {
          seenIds.add(permission.id);
        }

        // Check for missing or invalid name
        if (!permission.name || permission.name.trim() === '') {
          issues.invalidNames.push({ index, permission });
        }

        // Count empty descriptions
        if (!permission.description || permission.description.trim() === '') {
          issues.emptyDescriptions++;
        }
      });

      // Report issues
      console.log('📋 Data Quality Analysis:');
      console.log(`   - Total permissions: ${apiPermissions.length}`);
      console.log(`   - Unique IDs: ${seenIds.size}`);
      console.log(`   - Duplicate IDs: ${issues.duplicateIds.length}`);
      console.log(`   - Missing IDs: ${issues.missingIds.length}`);
      console.log(`   - Invalid names: ${issues.invalidNames.length}`);
      console.log(`   - Empty descriptions: ${issues.emptyDescriptions}`);

      if (issues.duplicateIds.length > 0) {
        console.warn('⚠️ Duplicate IDs found:', issues.duplicateIds);
      }

      if (issues.missingIds.length > 0) {
        console.warn('⚠️ Missing IDs found:', issues.missingIds);
      }

      if (issues.invalidNames.length > 0) {
        console.warn('⚠️ Invalid names found:', issues.invalidNames);
      }

      // Calculate expected count after filtering
      const validPermissions = apiPermissions.filter(p => 
        p.id && p.id !== null && p.id !== undefined && 
        p.name && p.name.trim() !== ''
      );

      console.log(`🧮 Expected count after filtering invalid data: ${validPermissions.length}`);

      // Check if this matches the Angular application count
      const angularCount = 139; // The count shown in Angular
      const difference = apiPermissions.length - angularCount;
      
      console.log(`📊 Count Comparison:
        - API Response: ${apiPermissions.length}
        - Angular App: ${angularCount}
        - Difference: ${difference}
        - Valid after filtering: ${validPermissions.length}`);

      if (validPermissions.length === angularCount) {
        console.log('✅ FOUND THE ISSUE: Angular is filtering out invalid permissions');
        console.log('💡 The missing permission(s) likely have invalid data (missing ID or name)');
        
        // Show the invalid permissions
        const invalidPermissions = apiPermissions.filter(p => 
          !p.id || p.id === null || p.id === undefined || 
          !p.name || p.name.trim() === ''
        );
        
        if (invalidPermissions.length > 0) {
          console.log('🔍 Invalid permissions that are being filtered out:');
          invalidPermissions.forEach((perm, index) => {
            console.log(`   ${index + 1}. ID: ${perm.id}, Name: "${perm.name}", Description: "${perm.description}"`);
          });
        }
      } else {
        console.log('❓ The filtering doesn\'t account for the full difference');
        console.log('💡 There might be additional filtering logic in the Angular application');
      }

    } else {
      console.error('❌ API response structure is invalid:', apiData);
    }

  } catch (error) {
    console.error('❌ Error during debug analysis:', error);
    console.log('💡 Make sure you are logged in and have the necessary permissions');
  }
}

// Helper function to check current page permissions array
function checkCurrentPagePermissions() {
  console.log('🔍 Checking permissions array on current page...');
  
  // Try to find the component instance
  const rolePermissionsComponent = document.querySelector('app-role-permissions');
  if (rolePermissionsComponent) {
    const context = ng.getContext(rolePermissionsComponent);
    if (context && context.permissions) {
      console.log(`📊 Current page permissions count: ${context.permissions.length}`);
      console.log('🔍 First 3 permissions:', context.permissions.slice(0, 3));
      console.log('🔍 Last 3 permissions:', context.permissions.slice(-3));
      return context.permissions;
    }
  }
  
  console.log('❌ Could not find role-permissions component or permissions array');
  return null;
}

// Export functions to global scope for easy access
window.debugPermissionCount = debugPermissionCount;
window.checkCurrentPagePermissions = checkCurrentPagePermissions;

console.log(`
🛠️ Permission Debug Tools Loaded!

Available functions:
- debugPermissionCount() - Full analysis comparing API vs Angular
- checkCurrentPagePermissions() - Check current page permissions array

Usage:
1. Navigate to the role-permissions page
2. Open browser console
3. Run: debugPermissionCount()
4. Check the output for discrepancy analysis

Example:
> debugPermissionCount()
`);
