import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, throwError, forkJoin } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import {
  Role,
  Permission,
  RoleCreate,
  RoleUpdate,
  PermissionCreate,
  PermissionUpdate,
  RoleWithUsers,
  ActualApiResponse
} from '../models/role.model';

@Injectable({
  providedIn: 'root'
})
export class RoleService {
  private baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // Role Management
  getRoles(skip: number = 0, limit: number = 10, search?: string): Observable<ActualApiResponse<Role>> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<ActualApiResponse<Role>>(`${this.baseUrl}/api/v1/roles/`, { params });
  }

  /**
   * Get roles with page/size pagination (backward compatibility)
   */
  getRolesLegacy(page: number = 1, size: number = 10, search?: string): Observable<ActualApiResponse<Role>> {
    const skip = (page - 1) * size;
    return this.getRoles(skip, size, search);
  }

  getRole(roleId: string): Observable<Role> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/roles/${roleId}`).pipe(
      map(response => {
        // Handle the API response format
        if (response && response.success && response.data) {
          return response.data;
        }
        return response;
      })
    );
  }

  createRole(role: RoleCreate): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/api/v1/roles/`, role).pipe(
      map(response => {
        console.log('Create role API response:', response);
        // Handle different possible response formats
        if (response && response.success && response.data) {
          return response.data; // Extract the role data from success response
        } else if (response && response.id) {
          return response; // Direct role object
        } else {
          return response; // Return as-is and let component handle it
        }
      }),
      catchError(error => {
        console.error('Create role API error:', error);
        return throwError(() => error);
      })
    );
  }

  updateRole(roleId: string, role: RoleUpdate): Observable<Role> {
    return this.http.put<Role>(`${this.baseUrl}/api/v1/roles/${roleId}`, role);
  }

  deleteRole(roleId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/api/v1/roles/${roleId}`);
  }

  restoreRole(roleId: string): Observable<Role> {
    return this.http.post<Role>(`${this.baseUrl}/api/v1/roles/${roleId}/restore`, {});
  }

  getRoleWithUsers(roleId: string): Observable<RoleWithUsers> {
    return this.http.get<RoleWithUsers>(`${this.baseUrl}/api/v1/roles/${roleId}/users`);
  }

  // Permission Management
  getPermissions(skip: number = 0, limit: number = 10, search?: string): Observable<ActualApiResponse<Permission>> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<ActualApiResponse<Permission>>(`${this.baseUrl}/api/v1/roles/permissions/`, { params });
  }

  /**
   * Get permissions with page/size pagination (backward compatibility)
   */
  getPermissionsLegacy(page: number = 1, size: number = 10, search?: string): Observable<ActualApiResponse<Permission>> {
    const skip = (page - 1) * size;
    return this.getPermissions(skip, size, search);
  }

  getPermission(permissionId: string): Observable<Permission> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/roles/permissions/${permissionId}`).pipe(
      map(response => {
        console.log('Get permission API response:', response);
        // Handle different possible response formats
        if (response && response.success && response.data) {
          return response.data; // Extract the permission data from success response
        } else if (response && response.id) {
          return response; // Direct permission object
        } else {
          return response; // Return as-is and let component handle it
        }
      }),
      catchError(error => {
        console.error('Get permission API error:', error);
        return throwError(() => error);
      })
    );
  }

  createPermission(permission: PermissionCreate): Observable<Permission> {
    return this.http.post<Permission>(`${this.baseUrl}/api/v1/roles/permissions/`, permission);
  }

  updatePermission(permissionId: string, permission: PermissionUpdate): Observable<Permission> {
    return this.http.put<Permission>(`${this.baseUrl}/api/v1/roles/permissions/${permissionId}`, permission);
  }

  deletePermission(permissionId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/api/v1/roles/permissions/${permissionId}`);
  }

  restorePermission(permissionId: string): Observable<Permission> {
    return this.http.post<Permission>(`${this.baseUrl}/api/v1/roles/permissions/${permissionId}/restore`, {});
  }

  // Role-Permission Assignment - Using PUT endpoint to replace all permissions atomically
  assignPermissionsToRole(roleId: string, permissionIds: string[]): Observable<any> {
    console.log('🔄 API call - replacing role permissions:', { roleId, permissionIds });
    console.log('📋 Permission IDs being sent:', permissionIds);
    console.log('🔢 Number of permissions:', permissionIds.length);

    // Use PUT endpoint for atomic replacement of all role permissions
    const payload = { permission_ids: permissionIds };
    console.log('📤 Replacing permissions with payload:', JSON.stringify(payload, null, 2));

    return this.http.put(`${this.baseUrl}/api/v1/roles/${roleId}/permissions`, payload).pipe(
      tap((response: any) => {
        console.log('✅ Successfully replaced permissions:', response);
      }),
      catchError((error: any) => {
        console.error('❌ Failed to replace permissions:', error);
        return throwError(() => error);
      })
    );
  }



  // Update role with permissions - only updates permissions since role data doesn't change in permission management
  updateRoleWithPermissions(roleId: string, roleData: any, permissionIds: string[]): Observable<any> {
    console.log('🔄 Updating role permissions only:', { roleId, permissionIds });
    console.log('🔍 Role data received (will be ignored for permission-only updates):', roleData);

    // For permission management, we only need to update permissions
    // The role data (name, description) is not being changed in the permission management UI
    return this.assignPermissionsToRole(roleId, permissionIds).pipe(
      tap(response => {
        console.log('✅ Successfully updated role permissions:', response);
      }),
      catchError(error => {
        console.error('❌ Failed to update role permissions:', error);
        return throwError(() => error);
      })
    );
  }

  // Update role basic info (name, description) - separate from permission management
  updateRoleInfo(roleId: string, roleUpdate: RoleUpdate): Observable<any> {
    console.log('🔄 Updating role info:', { roleId, roleUpdate });

    return this.updateRole(roleId, roleUpdate).pipe(
      tap(response => {
        console.log('✅ Successfully updated role info:', response);
      }),
      catchError(error => {
        console.error('❌ Failed to update role info:', error);
        return throwError(() => error);
      })
    );
  }

  // Additional permission management methods for future use

  // Add specific permissions to a role (without removing existing ones)
  addPermissionsToRole(roleId: string, permissionIds: string[]): Observable<any> {
    console.log('🔄 Adding permissions to role:', { roleId, permissionIds });

    const payload = { permission_ids: permissionIds };
    return this.http.post(`${this.baseUrl}/api/v1/roles/${roleId}/permissions`, payload).pipe(
      tap((response: any) => {
        console.log('✅ Successfully added permissions:', response);
      }),
      catchError((error: any) => {
        console.error('❌ Failed to add permissions:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk update permissions (add, remove, or replace)
  bulkUpdateRolePermissions(roleId: string, operation: 'add' | 'remove' | 'replace', permissionIds: string[]): Observable<any> {
    console.log('🔄 Bulk updating role permissions:', { roleId, operation, permissionIds });

    const payload = {
      operation: operation,
      permission_ids: permissionIds
    };

    return this.http.patch(`${this.baseUrl}/api/v1/roles/${roleId}/permissions`, payload).pipe(
      tap((response: any) => {
        console.log('✅ Successfully bulk updated permissions:', response);
      }),
      catchError((error: any) => {
        console.error('❌ Failed to bulk update permissions:', error);
        return throwError(() => error);
      })
    );
  }

  removePermissionFromRole(roleId: string, permissionId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/api/v1/roles/${roleId}/permissions/${permissionId}`);
  }

  // Utility methods
  getAllPermissions(): Observable<Permission[]> {
    // Get all permissions without pagination
    let params = new HttpParams()
      .set('skip', '0')
      .set('limit', '1000'); // Large number to get all permissions

    return this.http.get<ActualApiResponse<Permission>>(`${this.baseUrl}/api/v1/roles/permissions/`, { params })
      .pipe(
        map(response => {
          console.log('🔍 API Response for getAllPermissions:', response);

          if (response.success && response.data) {
            const permissions = response.data;
            console.log(`📊 Permission Count Analysis:
              - API Response Count: ${permissions.length}
              - Response Success: ${response.success}
              - Data Type: ${Array.isArray(permissions) ? 'Array' : typeof permissions}`);

            // Log first few and last few permissions for debugging
            if (permissions.length > 0) {
              console.log('🔍 First 3 permissions:', permissions.slice(0, 3));
              console.log('🔍 Last 3 permissions:', permissions.slice(-3));
            }

            // Check for any duplicate IDs or invalid permissions
            const uniqueIds = new Set();
            const duplicates = [];
            const invalidPermissions = [];

            permissions.forEach((permission, index) => {
              if (!permission.id) {
                invalidPermissions.push({ index, permission });
              } else if (uniqueIds.has(permission.id)) {
                duplicates.push({ index, id: permission.id });
              } else {
                uniqueIds.add(permission.id);
              }
            });

            if (duplicates.length > 0) {
              console.warn('⚠️ Duplicate permission IDs found:', duplicates);
            }

            if (invalidPermissions.length > 0) {
              console.warn('⚠️ Invalid permissions (missing ID) found:', invalidPermissions);
            }

            console.log(`✅ Final permission count after processing: ${permissions.length}`);
            return permissions;
          }

          console.warn('❌ API response failed or no data:', response);
          return [];
        })
      );
  }

  getAllRoles(): Observable<Role[]> {
    return this.http.get<Role[]>(`${this.baseUrl}/api/v1/roles/`);
  }

  // Get role permissions - try multiple endpoints
  getRolePermissions(roleId: string): Observable<Permission[]> {
    // Validate roleId
    if (!roleId || roleId === undefined || roleId === null || roleId.trim() === '') {
      console.error('Invalid roleId provided to getRolePermissions:', roleId);
      return of([]);
    }

    console.log('🔍 Attempting to get permissions for role:', roleId);

    // First try the standard endpoint
    console.log('🔗 Trying endpoint 1: GET /api/v1/roles/{roleId}/permissions?limit=500');
    return this.http.get<any>(`${this.baseUrl}/api/v1/roles/${roleId}/permissions?limit=500`)
      .pipe(
        tap(response => {
          console.log('✅ Endpoint 1 SUCCESS:', response);
        }),
        catchError(error => {
          console.log('❌ Endpoint 1 FAILED:', error.status, error.message);

          // Try alternative endpoint structure - get role with permissions included
          console.log('🔗 Trying endpoint 2: GET /api/v1/roles/{roleId}');
          return this.http.get<any>(`${this.baseUrl}/api/v1/roles/${roleId}`).pipe(
            tap(response => {
              console.log('✅ Endpoint 2 SUCCESS:', response);
            }),
            map(roleResponse => {
              // Handle the API response format
              if (roleResponse && roleResponse.success && roleResponse.data && roleResponse.data.permissions) {
                console.log('📋 Found permissions in response.data.permissions:', roleResponse.data.permissions);
                return roleResponse.data.permissions;
              } else if (roleResponse && roleResponse.permissions) {
                console.log('📋 Found permissions in response.permissions:', roleResponse.permissions);
                return roleResponse.permissions;
              }
              console.log('📋 No permissions found in role response');
              return [];
            }),
            catchError(error2 => {
              console.log('❌ Endpoint 2 FAILED:', error2.status, error2.message);

              // Try query parameter approach
              console.log('🔗 Trying endpoint 3: GET /api/v1/roles/permissions/?role_id={roleId}');
              return this.http.get<any>(`${this.baseUrl}/api/v1/roles/permissions/?role_id=${roleId}`).pipe(
                tap(response => {
                  console.log('✅ Endpoint 3 SUCCESS:', response);
                }),
                map(response => {
                  if (response && response.success && response.data) {
                    console.log('📋 Found permissions in query response.data:', response.data);
                    return response.data;
                  } else if (response && Array.isArray(response)) {
                    console.log('📋 Found permissions as array:', response);
                    return response;
                  }
                  console.log('📋 No permissions found in query response');
                  return [];
                }),
                catchError(error3 => {
                  console.log('❌ Endpoint 3 FAILED:', error3.status, error3.message);

                  // Try one more alternative - permissions endpoint with role filter
                  console.log('🔗 Trying endpoint 4: GET /api/v1/permissions/?role={roleId}');
                  return this.http.get<any>(`${this.baseUrl}/api/v1/permissions/?role=${roleId}`).pipe(
                    tap(response => {
                      console.log('✅ Endpoint 4 SUCCESS:', response);
                    }),
                    map(response => {
                      if (response && response.success && response.data) {
                        console.log('📋 Found permissions in permissions endpoint response.data:', response.data);
                        return response.data;
                      } else if (response && Array.isArray(response)) {
                        console.log('📋 Found permissions as array from permissions endpoint:', response);
                        return response;
                      }
                      console.log('📋 No permissions found in permissions endpoint response');
                      return [];
                    }),
                    catchError(error4 => {
                      console.log('❌ Endpoint 4 FAILED:', error4.status, error4.message);
                      console.log('❌ All endpoints failed, returning empty array');
                      return of([]);
                    })
                  );
                })
              );
            })
          );
        }),
        map(response => {
          // Handle different possible response formats
          if (Array.isArray(response)) {
            console.log('📋 Final result: Array with', response.length, 'permissions');
            return response;
          } else if (response && typeof response === 'object') {
            // Check for different possible response structures
            if ('data' in response && Array.isArray(response.data)) {
              console.log('📋 Final result: Found in response.data with', response.data.length, 'permissions');
              return response.data;
            } else if ('permissions' in response && Array.isArray(response.permissions)) {
              console.log('📋 Final result: Found in response.permissions with', response.permissions.length, 'permissions');
              return response.permissions;
            } else if ('items' in response && Array.isArray(response.items)) {
              console.log('📋 Final result: Found in response.items with', response.items.length, 'permissions');
              return response.items;
            } else if ('success' in response && response.data && Array.isArray(response.data)) {
              console.log('📋 Final result: Found in success response.data with', response.data.length, 'permissions');
              return response.data;
            } else {
              console.log('📋 Final result: No recognizable array found, returning empty');
              return [];
            }
          } else {
            console.log('📋 Final result: Invalid response format, returning empty');
            return [];
          }
        }),
        catchError(error => {
          console.error('❌ Final error in getRolePermissions:', error);
          return of([]);
        })
      );
  }
}
