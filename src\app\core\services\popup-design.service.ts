import { Injectable } from '@angular/core';
import Swal, { SweetAlertOptions, SweetAlertResult } from 'sweetalert2';

export interface ModernPopupOptions {
  title?: string;
  message?: string;
  type?: 'success' | 'error' | 'warning' | 'info' | 'question';
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
  html?: string;
  timer?: number;
  showProgressBar?: boolean;
  allowOutsideClick?: boolean;
  allowEscapeKey?: boolean;
  customClass?: string;
  width?: string;
  position?: 'center' | 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end';
}

@Injectable({
  providedIn: 'root'
})
export class PopupDesignService {

  private readonly defaultConfig: SweetAlertOptions = {
    // Modern styling
    customClass: {
      popup: 'modern-popup',
      title: 'modern-popup-title',
      confirmButton: 'modern-btn modern-btn-primary',
      cancelButton: 'modern-btn modern-btn-secondary',
      actions: 'modern-popup-actions',
      container: 'modern-popup-container'
    },

    // Enhanced UX
    allowOutsideClick: false,
    allowEscapeKey: true,
    showCloseButton: true,
    focusConfirm: false,
    focusCancel: true,
    reverseButtons: true,

    // Animations
    showClass: {
      popup: 'animate__animated animate__fadeInDown animate__faster',
      backdrop: 'animate__animated animate__fadeIn animate__faster'
    },
    hideClass: {
      popup: 'animate__animated animate__fadeOutUp animate__faster',
      backdrop: 'animate__animated animate__fadeOut animate__faster'
    },

    // Responsive
    heightAuto: false,
    width: '480px',
    padding: '2rem'
  };

  constructor() {
    this.initializeGlobalStyles();
  }

  /**
   * Show success popup with modern design
   */
  async showSuccess(options: ModernPopupOptions): Promise<SweetAlertResult> {
    return this.showPopup({
      ...options,
      type: 'success',
      confirmText: options.confirmText || 'Great!',
      timer: options.timer || 3000,
      showProgressBar: options.showProgressBar !== false
    });
  }

  /**
   * Show error popup with modern design
   */
  async showError(options: ModernPopupOptions): Promise<SweetAlertResult> {
    return this.showPopup({
      ...options,
      type: 'error',
      confirmText: options.confirmText || 'I Understand',
      allowOutsideClick: false
    });
  }

  /**
   * Show warning popup with modern design
   */
  async showWarning(options: ModernPopupOptions): Promise<SweetAlertResult> {
    return this.showPopup({
      ...options,
      type: 'warning',
      confirmText: options.confirmText || 'Proceed',
      cancelText: options.cancelText || 'Cancel',
      showCancel: options.showCancel !== false
    });
  }

  /**
   * Show info popup with modern design
   */
  async showInfo(options: ModernPopupOptions): Promise<SweetAlertResult> {
    return this.showPopup({
      ...options,
      type: 'info',
      confirmText: options.confirmText || 'Got it!'
    });
  }

  /**
   * Show confirmation popup with modern design
   */
  async showConfirmation(options: ModernPopupOptions): Promise<SweetAlertResult> {
    return this.showPopup({
      ...options,
      type: 'question',
      confirmText: options.confirmText || 'Yes, Continue',
      cancelText: options.cancelText || 'Cancel',
      showCancel: true,
      allowOutsideClick: false
    });
  }

  /**
   * Show loading popup
   */
  showLoading(title: string = 'Processing...', message: string = 'Please wait while we process your request.'): void {
    Swal.fire({
      ...this.defaultConfig,
      title,
      html: `
        <div class="modern-loading-content">
          <div class="modern-spinner"></div>
          <p class="modern-loading-message">${message}</p>
        </div>
      `,
      showConfirmButton: false,
      showCancelButton: false,
      allowOutsideClick: false,
      allowEscapeKey: false,
      customClass: {
        popup: 'modern-popup modern-loading-popup',
        title: 'modern-popup-title',
        confirmButton: 'modern-btn modern-btn-primary',
        cancelButton: 'modern-btn modern-btn-secondary',
        actions: 'modern-popup-actions',
        container: 'modern-popup-container'
      }
    });
  }

  /**
   * Close any open popup
   */
  close(): void {
    Swal.close();
  }

  /**
   * Show toast notification
   */
  showToast(options: ModernPopupOptions): void {
    const toast = Swal.mixin({
      toast: true,
      position: options.position || 'top-end',
      showConfirmButton: false,
      timer: options.timer || 3000,
      timerProgressBar: true,
      customClass: {
        popup: 'modern-toast',
        title: 'modern-toast-title'
      },
      showClass: {
        popup: 'animate__animated animate__slideInRight animate__faster'
      },
      hideClass: {
        popup: 'animate__animated animate__slideOutRight animate__faster'
      }
    });

    toast.fire({
      icon: options.type,
      title: options.title,
      text: options.message,
      html: options.html
    });
  }

  /**
   * Core popup method with modern styling
   */
  private async showPopup(options: ModernPopupOptions): Promise<SweetAlertResult> {
    const config: SweetAlertOptions = {
      ...this.defaultConfig,
      title: options.title,
      text: options.message,
      html: options.html,
      icon: options.type,
      confirmButtonText: options.confirmText,
      cancelButtonText: options.cancelText,
      showCancelButton: options.showCancel,
      timer: options.timer,
      timerProgressBar: options.showProgressBar,
      allowOutsideClick: options.allowOutsideClick,
      allowEscapeKey: options.allowEscapeKey,
      width: options.width || this.defaultConfig.width,
      position: options.position || 'center'
    };

    // Add custom class if provided
    if (options.customClass && typeof config.customClass === 'object') {
      config.customClass = {
        ...config.customClass,
        popup: `${config.customClass.popup || ''} ${options.customClass}`.trim()
      };
    }

    // Enhanced styling based on type
    if (options.type === 'success') {
      config.confirmButtonColor = '#05a34a';
      config.iconColor = '#05a34a';
    } else if (options.type === 'error') {
      config.confirmButtonColor = '#f60002';
      config.iconColor = '#f60002';
    } else if (options.type === 'warning') {
      config.confirmButtonColor = '#fbbc06';
      config.cancelButtonColor = '#7987a1';
      config.iconColor = '#fbbc06';
    } else if (options.type === 'info') {
      config.confirmButtonColor = '#66d1d1';
      config.iconColor = '#66d1d1';
    } else if (options.type === 'question') {
      config.confirmButtonColor = '#3F828B';
      config.cancelButtonColor = '#7987a1';
      config.iconColor = '#3F828B';
    }

    return Swal.fire(config);
  }

  /**
   * Initialize global styles for modern popups
   */
  private initializeGlobalStyles(): void {
    if (document.getElementById('modern-popup-styles')) {
      return; // Styles already injected
    }

    const styleElement = document.createElement('style');
    styleElement.id = 'modern-popup-styles';
    styleElement.innerHTML = `
      /* Modern Popup Styles */
      .modern-popup {
        border-radius: 16px !important;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        border: none !important;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      }

      .modern-popup-header {
        border-bottom: 1px solid #e5e7eb !important;
        padding-bottom: 1rem !important;
      }

      .modern-popup-title {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
        margin: 0 !important;
      }

      .modern-popup-content {
        padding: 1.5rem 0 !important;
        color: #6b7280 !important;
        font-size: 1rem !important;
        line-height: 1.6 !important;
      }

      .modern-popup-actions {
        gap: 0.75rem !important;
        padding-top: 1rem !important;
        border-top: 1px solid #e5e7eb !important;
      }

      .modern-btn {
        border-radius: 8px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        transition: all 0.2s ease !important;
        border: none !important;
        cursor: pointer !important;
      }

      .modern-btn-primary {
        background: linear-gradient(135deg, #3F828B 0%, #2d5a61 100%) !important;
        color: white !important;
      }

      .modern-btn-primary:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 10px 25px -5px rgba(63, 130, 139, 0.4) !important;
      }

      .modern-btn-secondary {
        background: #f3f4f6 !important;
        color: #6b7280 !important;
      }

      .modern-btn-secondary:hover {
        background: #e5e7eb !important;
        color: #374151 !important;
      }

      /* Loading Popup */
      .modern-loading-popup {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
      }

      .modern-loading-content {
        text-align: center !important;
        padding: 1rem !important;
      }

      .modern-spinner {
        width: 40px !important;
        height: 40px !important;
        border: 3px solid #e5e7eb !important;
        border-top: 3px solid #3F828B !important;
        border-radius: 50% !important;
        animation: spin 1s linear infinite !important;
        margin: 0 auto 1rem !important;
      }

      .modern-loading-message {
        color: #6b7280 !important;
        margin: 0 !important;
        font-size: 0.875rem !important;
      }

      /* Toast Notifications */
      .modern-toast {
        border-radius: 12px !important;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid #e5e7eb !important;
      }

      .modern-toast-title {
        font-size: 0.875rem !important;
        font-weight: 600 !important;
      }

      .modern-toast-content {
        font-size: 0.8rem !important;
      }

      /* Animations */
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Dark mode support */
      [data-theme="dark"] .modern-popup {
        background: #1f2937 !important;
        color: #f9fafb !important;
      }

      [data-theme="dark"] .modern-popup-title {
        color: #f9fafb !important;
      }

      [data-theme="dark"] .modern-popup-content {
        color: #d1d5db !important;
      }

      [data-theme="dark"] .modern-popup-header,
      [data-theme="dark"] .modern-popup-actions {
        border-color: #374151 !important;
      }

      [data-theme="dark"] .modern-btn-secondary {
        background: #374151 !important;
        color: #d1d5db !important;
      }

      [data-theme="dark"] .modern-btn-secondary:hover {
        background: #4b5563 !important;
        color: #f9fafb !important;
      }
    `;

    document.head.appendChild(styleElement);
  }
}
