import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Setting } from '../../../../../core/services/settings.service';

@Component({
  selector: 'app-setting-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-settings me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Setting
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Setting Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ setting?.key }}</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced form will include comprehensive setting management with:
        <ul class="mt-2 mb-0">
          <li><strong>Dynamic Setting Types</strong> - 12 data types with custom validation and formatting</li>
          <li><strong>Category Management</strong> - 10 predefined categories with custom grouping</li>
          <li><strong>Advanced Validation</strong> - Multiple validation rules with custom messages</li>
          <li><strong>Security Configuration</strong> - Encryption, access control, and visibility settings</li>
          <li><strong>Environment Management</strong> - Environment-specific values and deployment settings</li>
          <li><strong>User Permissions</strong> - User-configurable vs admin-only settings</li>
          <li><strong>Default Value Management</strong> - Default values with reset functionality</li>
          <li><strong>Dependency Management</strong> - Setting dependencies and restart requirements</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-shield me-2"></i>
        <strong>Security Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Automatic encryption for sensitive data types (passwords, API keys)</li>
          <li>Role-based access control with granular permissions</li>
          <li>Audit logging for all setting changes with user tracking</li>
          <li>Data masking for sensitive values in UI and logs</li>
          <li>Secure storage with encryption at rest and in transit</li>
          <li>Environment isolation to prevent cross-environment leaks</li>
          <li>Compliance features for GDPR, SOX, and other regulations</li>
          <li>Backup and recovery with point-in-time restoration</li>
        </ul>
      </div>

      <div class="alert alert-success">
        <i class="feather icon-zap me-2"></i>
        <strong>Advanced Configuration:</strong>
        <ul class="mt-2 mb-0">
          <li>Real-time validation with immediate feedback</li>
          <li>Dynamic form generation based on data type</li>
          <li>Conditional field display based on other settings</li>
          <li>Import/export functionality with version control</li>
          <li>Template system for common setting patterns</li>
          <li>Bulk operations with transaction support</li>
          <li>Configuration drift detection and alerts</li>
          <li>Performance impact analysis for setting changes</li>
        </ul>
      </div>

      <div class="alert alert-primary">
        <i class="feather icon-layers me-2"></i>
        <strong>Data Types & Categories:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <strong>Basic Types:</strong>
            <ul class="mb-2">
              <li>String, Number, Boolean</li>
              <li>Date, Time, DateTime</li>
              <li>Email, URL, Password</li>
            </ul>
            <strong>Complex Types:</strong>
            <ul class="mb-2">
              <li>JSON Objects & Arrays</li>
              <li>File Uploads</li>
              <li>Rich Text Content</li>
            </ul>
          </div>
          <div class="col-md-6">
            <strong>System Categories:</strong>
            <ul class="mb-2">
              <li>System, Security, Performance</li>
              <li>User, Notifications, Integrations</li>
              <li>Appearance, Backup, Audit</li>
            </ul>
            <strong>Custom Categories:</strong>
            <ul class="mb-2">
              <li>Business Logic Settings</li>
              <li>Feature Flags & Toggles</li>
              <li>Third-party Configurations</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="alert alert-secondary">
        <i class="feather icon-tool me-2"></i>
        <strong>Validation & Rules:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <strong>Built-in Validators:</strong>
            <ul class="mb-2">
              <li>Required, Min/Max Length</li>
              <li>Pattern Matching (Regex)</li>
              <li>Numeric Range Validation</li>
              <li>Email & URL Format</li>
            </ul>
          </div>
          <div class="col-md-6">
            <strong>Custom Validators:</strong>
            <ul class="mb-2">
              <li>Business Rule Validation</li>
              <li>Cross-field Dependencies</li>
              <li>External API Validation</li>
              <li>Conditional Logic Rules</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="alert alert-dark">
        <i class="feather icon-database me-2"></i>
        <strong>Environment & Deployment:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <strong>Environment Features:</strong>
            <ul class="mb-2">
              <li>Environment-specific values</li>
              <li>Configuration inheritance</li>
              <li>Deployment automation</li>
              <li>Rollback capabilities</li>
            </ul>
          </div>
          <div class="col-md-6">
            <strong>Operations:</strong>
            <ul class="mb-2">
              <li>Hot-reload for runtime changes</li>
              <li>Restart requirements tracking</li>
              <li>Change impact analysis</li>
              <li>Configuration versioning</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
    
    .row {
      margin: 0;
    }
    
    .col-md-6 {
      padding: 0 0.5rem;
    }
  `]
})
export class SettingFormComponent {
  @Input() isEditMode = false;
  @Input() setting: Setting | null = null;

  settingForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.settingForm = this.fb.group({
      key: ['', [Validators.required]],
      value: ['', [Validators.required]],
      category: ['', [Validators.required]],
      data_type: ['', [Validators.required]]
    });
  }
}
