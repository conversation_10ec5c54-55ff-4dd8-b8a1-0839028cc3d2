/**
 * Core Services Index
 * 
 * This file provides a centralized export point for all core services
 * in the application. It makes it easier to import services and ensures
 * consistent service availability across the application.
 */

// Authentication & User Management
export * from './auth.service';
export * from './user.service';
export * from './auto-login.service';
export * from './session-expiration.service';

// Employee Management
export * from './employee.service';

// Leave Management
export * from './leave.service';
export * from './leave-report.service';

// Attendance Management
export * from './attendance-report.service';

// Role & Permission Management
export * from './role.service';

// Calendar & Events
export * from './calendar.service';
export * from './new-year-activity.service';

// Salary & Payroll
export * from './salary-slip.service';

// Master Data Management
export * from './master.service';
export * from './master-data.service';
export * from './profession.service';

// System Management
export * from './audit.service';
export * from './settings.service';
export * from './health.service';

// Internationalization
export * from './i18n.service';

// Utility Services
export * from './dynamic-menu.service';
export * from './error-message.service';
export * from './http-error-handler.service';
export * from './template-download.service';
export * from './theme-css-variable.service';
export * from './theme-mode.service';

// Testing Services (for development/testing purposes)
export * from './error-testing.service';
export * from './product-data.service';
export * from './refresh-token-test.service';
export * from './token-expiration-test.service';

/**
 * Service Categories for easier organization
 */
export const AUTH_SERVICES = [
  'AuthService',
  'UserService',
  'AutoLoginService',
  'SessionExpirationService'
] as const;

export const EMPLOYEE_SERVICES = [
  'EmployeeService'
] as const;

export const LEAVE_SERVICES = [
  'LeaveService',
  'LeaveReportService'
] as const;

export const ATTENDANCE_SERVICES = [
  'AttendanceReportService'
] as const;

export const ROLE_SERVICES = [
  'RoleService'
] as const;

export const CALENDAR_SERVICES = [
  'CalendarService',
  'NewYearActivityService'
] as const;

export const PAYROLL_SERVICES = [
  'SalarySlipService'
] as const;

export const MASTER_DATA_SERVICES = [
  'MasterService',
  'MasterDataService',
  'ProfessionService'
] as const;

export const SYSTEM_SERVICES = [
  'AuditService',
  'SettingsService',
  'HealthService'
] as const;

export const I18N_SERVICES = [
  'I18nService'
] as const;

export const UTILITY_SERVICES = [
  'DynamicMenuService',
  'ErrorMessageService',
  'HttpErrorHandlerService',
  'TemplateDownloadService',
  'ThemeCssVariableService',
  'ThemeModeService'
] as const;

export const TESTING_SERVICES = [
  'ErrorTestingService',
  'ProductDataService',
  'RefreshTokenTestService',
  'TokenExpirationTestService'
] as const;

/**
 * All available services
 */
export const ALL_SERVICES = [
  ...AUTH_SERVICES,
  ...EMPLOYEE_SERVICES,
  ...LEAVE_SERVICES,
  ...ATTENDANCE_SERVICES,
  ...ROLE_SERVICES,
  ...CALENDAR_SERVICES,
  ...PAYROLL_SERVICES,
  ...MASTER_DATA_SERVICES,
  ...SYSTEM_SERVICES,
  ...I18N_SERVICES,
  ...UTILITY_SERVICES,
  ...TESTING_SERVICES
] as const;

/**
 * Service configuration for dependency injection
 */
export interface ServiceConfig {
  name: string;
  category: string;
  description: string;
  dependencies?: string[];
}

export const SERVICE_CONFIGS: ServiceConfig[] = [
  {
    name: 'AuthService',
    category: 'Authentication',
    description: 'Handles user authentication, token management, and session control',
    dependencies: ['HttpClient', 'Router']
  },
  {
    name: 'UserService',
    category: 'User Management',
    description: 'Manages user profiles, soft delete, restore, and password reset',
    dependencies: ['HttpClient']
  },
  {
    name: 'EmployeeService',
    category: 'Employee Management',
    description: 'Handles employee CRUD operations, roles, and bulk operations',
    dependencies: ['HttpClient']
  },
  {
    name: 'LeaveService',
    category: 'Leave Management',
    description: 'Manages leave applications, approvals, and leave type configurations',
    dependencies: ['HttpClient', 'HttpErrorHandlerService']
  },
  {
    name: 'SalarySlipService',
    category: 'Payroll',
    description: 'Handles salary slip generation, approval, and component management',
    dependencies: ['HttpClient']
  },
  {
    name: 'AuditService',
    category: 'System Management',
    description: 'Provides audit log management and filtering capabilities',
    dependencies: ['HttpClient']
  },
  {
    name: 'SettingsService',
    category: 'System Management',
    description: 'Manages application settings and configuration',
    dependencies: ['HttpClient']
  },
  {
    name: 'HealthService',
    category: 'System Management',
    description: 'Monitors system health and provides status checks',
    dependencies: ['HttpClient']
  },
  {
    name: 'I18nService',
    category: 'Internationalization',
    description: 'Handles multi-language support and translations',
    dependencies: ['HttpClient']
  },
  {
    name: 'MasterDataService',
    category: 'Master Data',
    description: 'Manages master data entities like banks, NBFCs, institutes',
    dependencies: ['HttpClient']
  },
  {
    name: 'ProfessionService',
    category: 'Master Data',
    description: 'Handles profession master data management',
    dependencies: ['HttpClient']
  },
  {
    name: 'LocationService',
    category: 'Master Data',
    description: 'Handles location master data management',
    dependencies: ['HttpClient']
  }
];

/**
 * Helper function to get service configuration
 * @param serviceName Name of the service
 * @returns Service configuration or undefined if not found
 */
export function getServiceConfig(serviceName: string): ServiceConfig | undefined {
  return SERVICE_CONFIGS.find(config => config.name === serviceName);
}

/**
 * Helper function to get services by category
 * @param category Service category
 * @returns Array of service names in the category
 */
export function getServicesByCategory(category: string): string[] {
  return SERVICE_CONFIGS
    .filter(config => config.category === category)
    .map(config => config.name);
}

/**
 * Helper function to get all service categories
 * @returns Array of unique service categories
 */
export function getServiceCategories(): string[] {
  return [...new Set(SERVICE_CONFIGS.map(config => config.category))];
}
