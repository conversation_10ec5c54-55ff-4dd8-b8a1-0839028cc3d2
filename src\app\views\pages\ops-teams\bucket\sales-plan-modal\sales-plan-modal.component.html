<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Sales Plan</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #salesPlanForm="ngForm">
    <div class="row mb-3">
      <div class=" col-12 col-md-6 col-lg-4 ">
        <label for="quarter" class="form-label">Quarter <span class="text-danger">*</span></label>
        <select class="form-select" id="quarter" name="quarter" [(ngModel)]="formData.quarter" required>
          <option value="">Select Quarter</option>
          <option *ngFor="let quarter of quarters" [value]="quarter.value">{{ quarter.label }}</option>
        </select>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-4">
        <label for="flatsBooked" class="form-label">No. of flats booked / sold <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="flatsBooked" name="flatsBooked" [(ngModel)]="formData.flatsBooked" required min="0" (change)="calculateSaleValue()">
      </div>
      <div class="col-md-4">
        <label for="averageArea" class="form-label">Average area of the flat <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="averageArea" name="averageArea" [(ngModel)]="formData.averageArea" required min="0" (change)="calculateSaleValue()">
      </div>
      <div class="col-md-4">
        <label for="ratePerSqFt" class="form-label">Rate per sq. ft. <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="ratePerSqFt" name="ratePerSqFt" [(ngModel)]="formData.ratePerSqFt" required min="0" (change)="calculateSaleValue()">
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-12">
        <label for="saleValue" class="form-label">Sale value</label>
        <div class="input-group">
          <span class="input-group-text">₹</span>
          <input type="text" class="form-control" id="saleValue" name="saleValue" [value]="formatNumber(formData.saleValue)" readonly>
        </div>
        <small class="form-text text-muted">Calculated automatically based on the inputs above</small>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="salesPlanForm.invalid" (click)="saveChanges()">Save</button>
</div>
