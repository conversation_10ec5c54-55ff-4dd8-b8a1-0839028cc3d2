<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <!-- Header with title and add button -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Professional List</h6>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="forceReloadData()" [disabled]="loading">
              <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
              Refresh
            </button>
            <button class="btn btn-primary" (click)="openProfessionModal(professionModal)">
              <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
              Add New Profession
            </button>
          </div>
        </div>

        <!-- Search and filter -->
        <div class="row mb-3">
          <div class="col-12 col-md-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span> <input type="text" class="form-control" [formControl]="searchTerm"
                placeholder="Search by name, type, or status...">
              <button *ngIf="searchTerm.value" class="input-group-text bg-light text-danger"
                (click)="searchTerm.setValue(''); loadProfessions()">
                <i data-feather="x" class="icon-sm" appFeatherIcon></i>
              </button>
            </div>
            <small class="text-muted" *ngIf="searchTerm.value">
              Searching for: "{{ searchTerm.value }}"
            </small>
          </div>

          <div class="col-md-6 col-lg-2 d-flex align-items-center mb-3">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="Edit Professionform-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="loadProfessions()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div *ngIf="loading" class="d-flex justify-content-center my-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive" *ngIf="!loading">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>

                <th scope="col">Actions</th>
                <th scope="col" sortable="name" (sort)="onSort($event)">Professional</th>
                <th scope="col" sortable="type" (sort)="onSort($event)">Type</th>
                <th scope="col" sortable="status" (sort)="onSort($event)">Status</th>

              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let profession of professions">
                <td class="action-icons">
                  <button type="button" class="action-icon text-info" ngbTooltip="View"
                    (click)="viewProfession(profession, viewProfessionModal)">
                    <i data-feather="eye" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Edit"
                    (click)="openProfessionModal(professionModal, profession)">
                    <i data-feather="edit" class="icon-sm" appFeatherIcon></i>
                  </button>

                  <button type="button" class="action-icon" ngbTooltip="Delete"
                    (click)="deleteProfession(profession)"
                    [disabled]="deleting && deletingProfessionId === profession.id">
                    <span *ngIf="deleting && deletingProfessionId === profession.id"
                      class="spinner-border spinner-border-sm text-danger" role="status"></span>
                    <i *ngIf="!(deleting && deletingProfessionId === profession.id)"
                      data-feather="trash" class="icon-sm text-danger" appFeatherIcon></i>
                  </button>

                  <button *ngIf="profession.deleted_at" type="button" class="action-icon text-success"
                    ngbTooltip="Restore" (click)="restoreProfession(profession)"
                    [disabled]="restoring && restoringProfessionId === profession.id">
                    <span *ngIf="restoring && restoringProfessionId === profession.id"
                      class="spinner-border spinner-border-sm text-success" role="status"></span>
                    <i *ngIf="!(restoring && restoringProfessionId === profession.id)"
                      data-feather="refresh-cw" class="icon-sm" appFeatherIcon></i>
                  </button>

                </td>
                <td>{{ profession.name }}</td>
                <td>
                  <span class="badge bg-light text-dark">
                    {{ profession.type }}
                  </span>
                </td>
                <td>
                  <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(profession.status)">
                    {{ profession.status | titlecase }}
                  </span>
                </td>

              </tr>
              <tr *ngIf="professions.length === 0">
                <td colspan="4" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="briefcase" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No professions found</p>
                    <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
                    <small class="text-muted" *ngIf="searchTerm.value">You can search by name, type (Profession/Non
                      Profession), or status (active/inactive)</small>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="!loading && totalItems > 0">
          <div>
            <span class="text-muted">Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, totalItems)
              }} of {{ totalItems }} entries</span>
          </div>
          <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="pageSize" [maxSize]="5"
            [rotate]="true" [boundaryLinks]="true" (pageChange)="onPageChange($event)"
            class="pagination-sm"></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Profession Modal -->
<ng-template #professionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">{{ formMode === 'create' ? 'Add New Profession' : 'Edit Profession' }}</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="professionForm">
      <div class="mb-3">
        <label for="name" class="form-label">Profession Name</label>
        <input type="text" class="form-control" id="name" formControlName="name" placeholder="Enter profession name">
        <div *ngIf="professionForm.get('name')?.invalid && professionForm.get('name')?.touched" class="text-danger">
          Name is required
        </div>
      </div>

      <div class="mb-3">
        <label for="type" class="form-label">Type</label>
        <select class="form-select" id="type" formControlName="type">
          <option value="Profession">Profession</option>
          <option value="Non Profession">Non Profession</option>
        </select>
      </div>

      <div class="mb-3">
        <label for="status" class="form-label">Status</label>
        <select class="form-select" id="status" formControlName="status">
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    <button type="button" class="btn btn-primary" [disabled]="professionForm.invalid || submitting"
      (click)="saveProfession()">
      <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
      {{ formMode === 'create' ? 'Create' : 'Update' }}
    </button>
  </div>
</ng-template>

<!-- View Profession Modal -->
<ng-template #viewProfessionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">View Profession</h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body" *ngIf="selectedProfession">
    <div class="card">
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Name:</div>
          <div class="col-md-8">{{ selectedProfession.name }}</div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Type:</div>
          <div class="col-md-8">{{ selectedProfession.type }}</div>
        </div>
        <div class="row mb-3">
          <div class="col-md-4 fw-bold">Status:</div>
          <div class="col-md-8">
            <span class="badge rounded-pill text-white" [ngClass]="getStatusClass(selectedProfession.status)">
              {{ selectedProfession.status | titlecase }}
            </span>
          </div>
        </div>
        <!-- <div class="row mb-3" *ngIf="selectedProfession.created_at">
          <div class="col-md-4 fw-bold">Created:</div>
          <div class="col-md-8">{{ selectedProfession.created_at | date:'medium' }}</div>
        </div>
        <div class="row mb-3" *ngIf="selectedProfession.updated_at">
          <div class="col-md-4 fw-bold">Last Updated:</div>
          <div class="col-md-8">{{ selectedProfession.updated_at | date:'medium' }}</div>
        </div> -->
        <div class="row mb-3" *ngIf="selectedProfession.deleted_at">
          <div class="col-md-4 fw-bold">Deleted:</div>
          <div class="col-md-8">{{ selectedProfession.deleted_at | date:'medium' }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Close</button>
    <button type="button" class="btn btn-primary" (click)="editFromViewModal(professionModal, modal)">
      <i data-feather="edit" class="icon-sm me-1" appFeatherIcon></i>
      Edit
    </button>
  </div>
</ng-template>