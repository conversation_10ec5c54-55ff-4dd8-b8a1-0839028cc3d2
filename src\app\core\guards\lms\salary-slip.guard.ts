import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class SalarySlipGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): boolean {

    console.log('🛡️ SALARY SLIP GUARD - Checking access for Salary Slip');
    console.log('🔑 Required permission: salary:read');
    console.log('📝 Note: This should be ADMIN ONLY - Managers should NOT have access');

    // Check if user is authenticated
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.log('❌ SALARY SLIP GUARD - No authenticated user, redirecting to login');
      this.router.navigate(['/auth/login']);
      return false;
    }

    // Check permission directly from AuthService
    console.log('🔍 SALARY SLIP GUARD - Checking permission');
    console.log('👤 User role:', currentUser?.role);
    console.log('🔑 User permissions:', currentUser?.permissions);

    // Check for salary:read permission using AuthService
    const hasPermission = this.authService.hasPermission('salary:read');

    // Special warning for managers
    if (currentUser?.role === 'manager' && hasPermission) {
      console.warn('🚨 WARNING: Manager has salary:read permission but should NOT!');
      console.warn('This indicates incorrect API permission assignment');
    }

    if (!hasPermission) {
      console.log('❌ SALARY SLIP GUARD - Permission DENIED');
      console.log('🔑 Required: salary:read');
      console.log('👤 User has:', currentUser?.permissions);
      console.log('✅ This is CORRECT for managers - they should not access salary data');
      this.router.navigate(['/lms/dashboard']);
      return false;
    }

    console.log('✅ SALARY SLIP GUARD - Permission GRANTED');
    console.log('🎯 User can access Salary Slip functionality');
    console.log('📝 Note: This should typically be Admin users only');
    return true;
  }
}
