// Analytics Dashboard Header
.analytics-header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 0 0 1rem 1rem;

  .page-header {
    .page-title {
      font-size: 1.75rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .page-subtitle {
      font-size: 1rem;
      opacity: 0.9;
    }
  }

  .header-actions {
    .btn {
      border-radius: 0.5rem;
      font-weight: 500;
      
      &.btn-outline-light {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
          color: white;
        }
      }

      &.btn-light {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        color: #495057;
        
        &:hover {
          background: white;
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Loading Section
.loading-section {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

// Metrics Section
.metrics-section {
  .metric-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    height: 100%;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    }

    .metric-icon {
      width: 4rem;
      height: 4rem;
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1.5rem;
      color: white;
      flex-shrink: 0;

      &.bg-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.bg-info { background: linear-gradient(135deg, #3ca55c 0%, #b5ac49 100%); }
      &.bg-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
      &.bg-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
      &.bg-danger { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); }

      i {
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    .metric-content {
      flex: 1;

      .metric-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: #2d3748;
        line-height: 1;
      }

      .metric-label {
        font-size: 1rem;
        color: #718096;
        margin-bottom: 0.5rem;
        font-weight: 500;
      }

      .metric-detail {
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }
}

// Analytics Cards
.analytics-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid #f1f3f4;
  overflow: hidden;
  height: 100%;

  .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #f1f3f4;
    padding: 1.5rem;

    .card-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #2d3748;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Permission Distribution
.permission-distribution {
  .distribution-chart {
    margin-bottom: 2rem;

    .chart-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .donut-chart {
        position: relative;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: conic-gradient(
          #667eea 0deg 72deg,
          #764ba2 72deg 144deg,
          #f093fb 144deg 216deg,
          #f5576c 216deg 288deg,
          #4facfe 288deg 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;

        &::before {
          content: '';
          position: absolute;
          width: 100px;
          height: 100px;
          background: white;
          border-radius: 50%;
        }

        .donut-center {
          position: relative;
          z-index: 1;
          text-align: center;

          .donut-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
          }

          .donut-label {
            display: block;
            font-size: 0.75rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }
  }

  .distribution-legend {
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;

      &:last-child {
        margin-bottom: 0;
      }

      .legend-color {
        width: 1rem;
        height: 1rem;
        border-radius: 0.25rem;
        margin-right: 0.75rem;
        flex-shrink: 0;
      }

      .legend-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .legend-label {
          font-size: 0.875rem;
          color: #2d3748;
          font-weight: 500;
        }

        .legend-value {
          font-size: 0.875rem;
          color: #718096;
          font-weight: 500;
        }
      }
    }
  }
}

// Health Analysis
.health-analysis {
  .health-score-circle {
    display: flex;
    justify-content: center;

    .circle-progress {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: conic-gradient(
        #28a745 0deg calc(var(--score, 0) * 3.6deg),
        #f8f9fa calc(var(--score, 0) * 3.6deg) 360deg
      );
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        position: absolute;
        width: 80px;
        height: 80px;
        background: white;
        border-radius: 50%;
      }

      .circle-content {
        position: relative;
        z-index: 1;
        text-align: center;

        .score-number {
          display: block;
          font-size: 1.25rem;
          font-weight: 700;
          color: #2d3748;
        }

        .score-label {
          display: block;
          font-size: 0.75rem;
          color: #718096;
          font-weight: 500;
        }
      }
    }
  }

  .health-indicators {
    .indicator-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f1f3f4;

      &:last-child {
        border-bottom: none;
      }

      .indicator-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
      }

      .indicator-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .indicator-label {
          font-size: 0.875rem;
          color: #2d3748;
          font-weight: 500;
        }

        .indicator-value {
          font-size: 1rem;
          font-weight: 600;
          color: #718096;
        }
      }
    }
  }
}

// Data Tables
.table {
  .rank-badge {
    width: 1.5rem;
    height: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .role-name {
    font-weight: 500;
    color: #2d3748;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;

    &.bg-primary-subtle { background-color: #cfe2ff !important; color: #084298 !important; }
    &.bg-warning-subtle { background-color: #fff3cd !important; color: #664d03 !important; }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .analytics-header-section {
    padding: 1.5rem 0;
    
    .page-title {
      font-size: 1.5rem !important;
    }
    
    .header-actions {
      margin-top: 1rem;
      
      .btn {
        width: 100%;
        margin-bottom: 0.5rem;
      }
    }
  }

  .metric-card {
    flex-direction: column;
    text-align: center;

    .metric-icon {
      margin-right: 0;
      margin-bottom: 1rem;
    }
  }

  .distribution-chart {
    .chart-container {
      height: 150px;

      .donut-chart {
        width: 120px;
        height: 120px;

        &::before {
          width: 80px;
          height: 80px;
        }
      }
    }
  }
}
