import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * Sales API interfaces based on BizzCorp API specification
 */
export interface SalesCreateRequest {
  lead_name: string;
  company?: string;
  location?: string;
  location_id?: string;
  sub_location?: string;
  in_house_team?: string;
  email?: string;
  university_affiliation?: string;
  status: string;
  lead_category_id?: string;
  source_id?: string;
  lead_data_type_id?: string;
  product_type_id?: string;
  sub_product_type_id?: string;
  created_by_id?: string;
  handover_to_id?: string;
  profession_type_id?: string;
  connect_with_id?: string;
  constitution_id?: string;
  board_affiliation_id?: string;
  associate_id?: string;
  associate_name?: string;
  associate_company_name?: string;
  associate_location_id?: string;
  associate_sub_location?: string;
  associate_profession_type?: string;
  associate_profession_id?: string;
  lead_category_associate_details?: {
    associate_name: string;
    professional_category: string;
    profession_category: string;
    associate_location: string;
    associate_sub_location: string;
    lead_name: string;
    lead_location: string;
    lead_sub_location: string;
  };
  form_data?: any;
  people_information?: PeopleInformation[];
}

export interface PeopleInformation {
  name: string;
  mobile: string;
  email: string;
  is_primary_contact: boolean;
  is_active: boolean;
  notes: string;
  role_id: string | null;
  connect_with_id?: string | null;
}

export interface SalesResponse {
  id: string;
  lead_name: string;
  company?: string;
  location_id: string;
  sub_location?: string;
  lead_category_id?: string;
  source_id?: string;
  lead_data_type_id?: string;
  status: string;
  form_data?: any;
  people_information?: PeopleInformation[];
  product_type_id?: string;
  sub_product_type_id?: string;
  handover_to_id?: string;
  profession_type_id?: string;
  constitution_id?: string;
  board_affiliation_id?: string;
  university_affiliation?: string;
  in_house_team?: string;
  email?: string;
  created_by_id?: string;
  created_at: string;
  updated_at: string;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

@Injectable({
  providedIn: 'root'
})
export class SalesService {
  private baseUrl = `${environment.apiUrl}/api/v1/sales`;

  // BehaviorSubject to notify components when sales data changes
  private salesUpdatedSubject = new BehaviorSubject<boolean>(false);
  public salesUpdated$ = this.salesUpdatedSubject.asObservable();

  constructor(private http: HttpClient) {}



  /**
   * Create a new sales lead
   * POST /api/v1/sales/
   */
  createSales(salesData: SalesCreateRequest): Observable<APIResponse<SalesResponse>> {
    return this.http.post<APIResponse<SalesResponse>>(`${this.baseUrl}/`, salesData).pipe(
      tap(response => {
        // Trigger sales update notification with a small delay to ensure DB transaction is committed
        if (response.success) {
          setTimeout(() => {
            this.notifySalesUpdated();
          }, 200); // Small delay to ensure database transaction is committed
        }
      }),
      catchError(error => {
        console.error('❌ SalesService: Failed to create sales lead:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create sales lead with custom error handling (skips automatic error interceptor)
   */
  createSalesWithCustomErrorHandling(salesData: SalesCreateRequest): Observable<APIResponse<SalesResponse>> {
    console.log('🚀 SalesService: Creating sales lead with custom error handling:', salesData);

    // Add header to skip error interceptor so component can handle errors manually
    const headers = {
      'X-Skip-Error-Interceptor': 'true'
    };

    return this.http.post<APIResponse<SalesResponse>>(`${this.baseUrl}/`, salesData, { headers }).pipe(
      tap(response => {
        console.log('✅ SalesService: Sales lead created successfully:', response);
      }),
      catchError(error => {
        console.error('❌ SalesService: Failed to create sales lead:', error);
        console.error('❌ SalesService: Error details:', {
          status: error.status,
          statusText: error.statusText,
          error: error.error,
          message: error.message
        });
        return throwError(() => error);
      })
    );
  }

  /**
   * Get all sales leads with pagination and search using skip/limit
   * GET /api/v1/sales/
   */
  getAllSales(
    skip: number = 0,
    limit: number = 10,
    search?: string,
    bustCache: boolean = false
  ): Observable<APIResponse<SalesResponse[]>> {
    // Build query parameters
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    if (bustCache) {
      params = params.set('_t', Date.now().toString());
    }

    const url = `${this.baseUrl}/`;

    return this.http.get<APIResponse<SalesResponse[]>>(url, { params }).pipe(
      catchError(error => {
        console.error('❌ SalesService: Failed to load sales leads:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get all sales leads with page/size pagination (backward compatibility)
   * Converts page/size to skip/limit format
   */
  getAllSalesWithPageSize(
    page: number = 1,
    size: number = 10,
    search?: string,
    bustCache: boolean = false
  ): Observable<APIResponse<SalesResponse[]>> {
    const skip = (page - 1) * size;
    return this.getAllSales(skip, size, search, bustCache);
  }

  /**
   * Get all sales leads without pagination (backward compatibility)
   * This method loads all data by using a large limit
   */
  getAllSalesLegacy(bustCache: boolean = false): Observable<APIResponse<SalesResponse[]>> {
    return this.getAllSales(0, 1000, undefined, bustCache);
  }

  /**
   * Get a single sales lead by ID
   * GET /api/v1/sales/{id}
   */
  getSalesById(id: string): Observable<APIResponse<SalesResponse>> {
    return this.http.get<APIResponse<SalesResponse>>(`${this.baseUrl}/${id}`).pipe(
      catchError(error => {
        console.error('❌ SalesService: Failed to load sales lead:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Update an existing sales lead
   * PUT /api/v1/sales/{id}
   */
  updateSales(id: string, salesData: Partial<SalesCreateRequest>): Observable<APIResponse<SalesResponse>> {
    return this.http.put<APIResponse<SalesResponse>>(`${this.baseUrl}/${id}`, salesData).pipe(
      tap(response => {
        // Trigger sales update notification
        if (response.success) {
          setTimeout(() => {
            this.notifySalesUpdated();
          }, 200);
        }
      }),
      catchError(error => {
        console.error('❌ SalesService: Failed to update sales lead:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a sales lead (soft delete)
   * DELETE /api/v1/sales/{id}
   */
  deleteSales(id: string, hardDelete: boolean = false): Observable<APIResponse<boolean>> {
    const params = hardDelete ? { hard_delete: 'true' } : {};

    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${id}`, { params }).pipe(
      tap(response => {
        // Trigger sales update notification
        if (response.success) {
          setTimeout(() => {
            this.notifySalesUpdated();
          }, 200);
        }
      }),
      catchError(error => {
        console.error('❌ SalesService: Failed to delete sales lead:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Notify subscribers that sales data has been updated
   */
  notifySalesUpdated(): void {
    this.salesUpdatedSubject.next(true);
  }

  /**
   * Reset the sales update notification
   */
  resetSalesUpdateNotification(): void {
    this.salesUpdatedSubject.next(false);
  }
}
