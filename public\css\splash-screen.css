/* Splash screen */

.splash-screen {
  --primary: #6571ff;
  --bg: #f9fafb;

  background: var(--bg);
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.splash-screen .logo {
  background-image: url(../images/logo-mini-light.png);
  width: 30px;
  height: 30px;
  background-size: 30px;
  position: absolute;
  z-index: 1;
 }

.splash-screen .spinner {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: -moz-linear-gradient(left, var(--primary) 10%, rgba(0, 0, 0, 0) 42%);
  background: -webkit-linear-gradient(left, var(--primary) 10%, rgba(0, 0, 0, 0) 42%);
  background: -o-linear-gradient(left, var(--primary) 10%, rgba(0, 0, 0, 0) 42%);
  background: -ms-linear-gradient(left, var(--primary) 10%, rgba(0, 0, 0, 0) 42%);
  background: linear-gradient(to right, var(--primary) 10%, rgba(0, 0, 0, 0) 42%);
  -webkit-animation: loading 1.4s infinite linear;
  animation: loading 1.4s infinite linear;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}

.splash-screen .spinner:before {
  width: 50%;
  height: 50%;
  background: var(--primary);
  border-radius: 100% 0 0 0;
  position: absolute;
  top: 0;
  left: 0;
  content: '';
}

.splash-screen .spinner:after {
  background: var(--bg);
  width: 90%;
  height: 90%;
  border-radius: 50%;
  content: '';
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

@-webkit-keyframes loading {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
 }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
 }
}
@keyframes loading {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
 }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
 }
}

[data-bs-theme="dark"] .splash-screen,
[data-bs-theme="dark"] .splash-screen .spinner:after {
  background: #070d19;
}

[data-bs-theme="dark"] .splash-screen .logo {
  background-image: url(../images/logo-mini-dark.png);
}