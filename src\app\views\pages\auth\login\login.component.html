<div class="auth-container">
  <div class="auth-card" [@fadeInUp]>
    <!-- Logo and header section -->
    <div class="auth-header">
      <div class="logo-container" [@pulse]>
        <img src="images/login/brand-logo.jpg" alt="Bizz Corp Logo" class="logo">
      </div>
      <h1 class="auth-title" [@slideInRight]>Welcome Back</h1>
      <p class="auth-subtitle" [@slideInRight]>Sign in to your account to continue</p>
    </div>

    <!-- Animated decorative elements -->
    <div class="decoration-circle circle-1"></div>
    <div class="decoration-circle circle-2"></div>
    <div class="decoration-circle circle-3"></div>    <!-- Login form -->
    <form class="auth-form" [@fadeIn] [formGroup]="loginForm" (ngSubmit)="onLoggedin($event)">
      <!-- Display error message if login fails -->
      <div *ngIf="errorMessage" class="alert alert-danger mb-3" role="alert">
        {{ errorMessage }}
      </div>

      <!-- Username field -->
      <div class="form-floating mb-4">
        <div class="input-group">
          <span class="input-group-text">
            <i data-feather="user" appFeatherIcon></i>
          </span>
          <input
            type="text"
            class="form-control"
            id="username"
            placeholder="Username"
            formControlName="username"
            [ngClass]="{'is-invalid': loginForm.get('username')?.invalid && loginForm.get('username')?.touched}"
          >
          <div *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched" class="invalid-feedback">
            <span *ngIf="loginForm.get('username')?.errors?.['required']">Username is required</span>
          </div>
        </div>
      </div>

      <!-- Password field -->
      <div class="form-floating mb-3">
        <div class="input-group">
          <span class="input-group-text">
            <i data-feather="lock" appFeatherIcon></i>
          </span>
          <input
            [type]="showPassword ? 'text' : 'password'"
            class="form-control"
            id="password"
            placeholder="Password"
            formControlName="password"
            [ngClass]="{'is-invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"
            autocomplete="current-password"
          >
          <span class="input-group-text password-toggle" (click)="togglePasswordVisibility()">
            <i [attr.data-feather]="showPassword ? 'eye-off' : 'eye'" appFeatherIcon></i>
          </span>
          <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="invalid-feedback">
            Password is required
          </div>
        </div>
      </div>
      <!-- Remember me and forgot password -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="form-check">
          <input
            type="checkbox"
            class="form-check-input"
            id="rememberMe"
            formControlName="rememberMe"
            (change)="onRememberMeChange()"
          >
          <label class="form-check-label" for="rememberMe">
            Remember me
          </label>
        </div>
        <a href="javascript:void(0)" class="forgot-password">
          Forgot password?
        </a>
      </div>

      <!-- Login button -->
      <button
        type="submit"
        class="btn btn-primary w-100 login-btn"
        [disabled]="loginForm.invalid || loading"
        [@pulseAnimation]
      >
        <span *ngIf="!loading">Sign In</span>
        <i *ngIf="!loading" data-feather="log-in" appFeatherIcon class="ms-2"></i>

        <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        <span *ngIf="loading">Signing in...</span>
      </button>




    </form>

    <!-- Footer text -->
    <p class="auth-footer" [@fadeIn]>
      © 2023 Bizz Corp. All rights reserved.
    </p>
  </div>
</div>