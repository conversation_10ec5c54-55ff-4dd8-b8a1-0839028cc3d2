import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { SessionExpirationService } from './session-expiration.service';

@Injectable({
  providedIn: 'root'
})
export class TokenExpirationTestService {

  constructor(
    private authService: AuthService,
    private sessionExpirationService: SessionExpirationService
  ) {}

  /**
   * Test token expiration scenarios for demonstration
   */
  
  // Test 1: Simulate token expiring soon (5 minutes)
  simulateTokenExpiringSoon(): void {
    console.log('🧪 Testing: Token expiring soon...');
    
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      // Set token to expire in 4 minutes (triggers warning)
      const fourMinutesFromNow = Date.now() + (4 * 60 * 1000);
      const updatedUser = {
        ...currentUser,
        token_expiry: fourMinutesFromNow
      };
      
      localStorage.setItem('currentUser', JSON.stringify(updatedUser));
      console.log('✅ Token set to expire in 4 minutes - warning should appear soon');
    }
  }

  // Test 2: Simulate expired token
  simulateExpiredToken(): void {
    console.log('🧪 Testing: Expired token...');
    
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      // Set token to expired (1 minute ago)
      const oneMinuteAgo = Date.now() - (1 * 60 * 1000);
      const updatedUser = {
        ...currentUser,
        token_expiry: oneMinuteAgo
      };
      
      localStorage.setItem('currentUser', JSON.stringify(updatedUser));
      console.log('✅ Token set to expired - next API call or login check should trigger logout');
    }
  }

  // Test 3: Simulate invalid refresh token
  simulateInvalidRefreshToken(): void {
    console.log('🧪 Testing: Invalid refresh token...');
    
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      // Corrupt the refresh token
      const updatedUser = {
        ...currentUser,
        refresh_token: 'invalid_refresh_token_12345'
      };
      
      localStorage.setItem('currentUser', JSON.stringify(updatedUser));
      console.log('✅ Refresh token corrupted - next refresh attempt should fail and logout user');
    }
  }

  // Test 4: Check current token status
  checkTokenStatus(): void {
    console.log('🔍 Current Token Status:');
    console.log('- Is Logged In:', this.authService.isLoggedIn());
    console.log('- Is Token Expired:', this.authService.isTokenExpired());
    console.log('- Is Token Expiring Soon:', this.authService.isTokenExpiringSoon());
    
    const currentUser = this.authService.currentUserValue;
    if (currentUser?.token_expiry) {
      const timeUntilExpiry = currentUser.token_expiry - Date.now();
      const minutesUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60));
      console.log(`- Time Until Expiry: ${minutesUntilExpiry} minutes`);
    }
  }

  // Test 5: Force token refresh
  forceTokenRefresh(): void {
    console.log('🔄 Testing: Force token refresh...');
    
    this.authService.refreshToken().subscribe({
      next: (response) => {
        console.log('✅ Token refresh successful:', response);
      },
      error: (error) => {
        console.error('❌ Token refresh failed:', error);
      }
    });
  }

  // Test 6: Reset to normal token expiry
  resetToNormalExpiry(): void {
    console.log('🔄 Resetting to normal token expiry...');
    
    const currentUser = this.authService.currentUserValue;
    if (currentUser) {
      // Set token to expire in 15 minutes (normal)
      const fifteenMinutesFromNow = Date.now() + (15 * 60 * 1000);
      const updatedUser = {
        ...currentUser,
        token_expiry: fifteenMinutesFromNow
      };
      
      localStorage.setItem('currentUser', JSON.stringify(updatedUser));
      console.log('✅ Token expiry reset to 15 minutes from now');
    }
  }

  // Test 7: Simulate user activity
  simulateUserActivity(): void {
    console.log('👆 Simulating user activity...');
    this.sessionExpirationService.onUserActivity();
    console.log('✅ User activity simulated - session warnings should reset');
  }

  // Test 8: Show all available test methods
  showAvailableTests(): void {
    console.log('🧪 Available Token Expiration Tests:');
    console.log('1. simulateTokenExpiringSoon() - Triggers session warning');
    console.log('2. simulateExpiredToken() - Forces immediate logout');
    console.log('3. simulateInvalidRefreshToken() - Causes refresh failure');
    console.log('4. checkTokenStatus() - Shows current token status');
    console.log('5. forceTokenRefresh() - Manually refresh token');
    console.log('6. resetToNormalExpiry() - Reset to 15-minute expiry');
    console.log('7. simulateUserActivity() - Reset session warnings');
    console.log('8. showAvailableTests() - Show this help');
    console.log('');
    console.log('Usage: tokenExpirationTest.simulateTokenExpiringSoon()');
  }
}

// Make available globally for testing
if (typeof window !== 'undefined') {
  (window as any).tokenExpirationTest = {
    simulateTokenExpiringSoon: () => {
      // This will be set up in the component or service
    },
    showHelp: () => {
      console.log('Token expiration test methods will be available after login');
    }
  };
}
