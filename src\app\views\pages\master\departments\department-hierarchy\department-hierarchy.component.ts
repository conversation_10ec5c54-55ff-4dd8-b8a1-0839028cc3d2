import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';
import { 
  DepartmentService, 
  DepartmentHierarchy 
} from '../../../../../core/services/department.service';

@Component({
  selector: 'app-department-hierarchy',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective
  ],
  templateUrl: './department-hierarchy.component.html',
  styleUrls: ['./department-hierarchy.component.scss']
})
export class DepartmentHierarchyComponent implements OnInit {
  @Input() departmentId!: string;
  @Input() departmentName!: string;

  hierarchy: DepartmentHierarchy | null = null;
  loading = false;
  error: string | null = null;

  constructor(
    private departmentService: DepartmentService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.loadHierarchy();
  }

  /**
   * Load department hierarchy
   */
  loadHierarchy(): void {
    this.loading = true;
    this.error = null;

    this.departmentService.getDepartmentHierarchy(this.departmentId).subscribe({
      next: (response) => {
        if (response.success) {
          this.hierarchy = response.data;
        } else {
          this.error = response.error || 'Failed to load hierarchy';
        }
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
      }
    });
  }

  /**
   * Get level indicator for hierarchy display
   */
  getLevelIndicator(level: number): string {
    return '│  '.repeat(Math.max(0, level - 1)) + (level > 0 ? '├─ ' : '');
  }

  /**
   * Close modal
   */
  close(): void {
    this.activeModal.close();
  }
}
