// User List Component Styles

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;

  .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.5rem;

    .card-title {
      color: #495057;
      font-weight: 600;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Avatar styles
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;

  &.avatar-sm {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  &.avatar-md {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .avatar-initial {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-weight: 600;
  }
}

// Table styles
.table {
  th {
    border-top: none;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;

    &[sortable] {
      cursor: pointer;
      user-select: none;
      position: relative;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      &::after {
        content: '';
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        opacity: 0.3;
      }

      &.asc::after {
        border-bottom: 4px solid #6c757d;
        opacity: 1;
      }

      &.desc::after {
        border-top: 4px solid #6c757d;
        opacity: 1;
      }
    }
  }

  td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
  }

  tbody tr {
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: rgba(0, 0, 0, 0.025);
    }

    &.table-danger {
      background-color: rgba(220, 53, 69, 0.1);

      &:hover {
        background-color: rgba(220, 53, 69, 0.15);
      }
    }
  }
}

// Badge styles
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;

  &.bg-success {
    background-color: #198754 !important;
  }

  &.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
  }

  &.bg-danger {
    background-color: #dc3545 !important;
  }

  &.bg-secondary {
    background-color: #6c757d !important;
  }
}

// Button group styles
.btn-group {
  .btn {
    border-radius: 0.375rem;
    margin-right: 0.25rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

// Card view specific styles
.card.h-100 {
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }

  &.border-danger {
    border-color: #dc3545 !important;
    border-width: 2px;
  }
}

// Search and filter styles
.input-group {
  .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #6c757d;
  }

  .form-control {
    border-color: #ced4da;

    &:focus {
      border-color: #86b7fe;
      box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
  }
}

.form-select {
  border-color: #ced4da;

  &:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }
}

// Loading and empty states
.spinner-border {
  &.text-primary {
    color: #0d6efd !important;
  }

  &.spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Pagination styles
::ng-deep .pagination {
  .page-link {
    color: #6c757d;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;

    &:hover {
      color: #0d6efd;
      background-color: #e9ecef;
      border-color: #dee2e6;
    }

    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
  }

  .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
  }

  .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
  }
}

// Dropdown styles
.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 0.5rem 0;

  .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: #f8f9fa;
    }

    &.text-danger:hover {
      background-color: rgba(220, 53, 69, 0.1);
      color: #dc3545 !important;
    }

    &.text-success:hover {
      background-color: rgba(25, 135, 84, 0.1);
      color: #198754 !important;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-header {
    .d-flex {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group {
    .btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #212529;
    color: #fff;

    .card-header {
      border-bottom-color: rgba(255, 255, 255, 0.125);
    }
  }

  .table {
    color: #fff;

    th, td {
      border-color: rgba(255, 255, 255, 0.125);
    }

    tbody tr:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }
  }

  .input-group-text {
    background-color: #343a40;
    border-color: #495057;
    color: #adb5bd;
  }

  .form-control, .form-select {
    background-color: #212529;
    border-color: #495057;
    color: #fff;
  }
}
