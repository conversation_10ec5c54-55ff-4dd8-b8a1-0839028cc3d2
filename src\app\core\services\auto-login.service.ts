import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AutoLoginService {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Check if auto-login should be attempted
   */
  shouldAttemptAutoLogin(): boolean {
    console.log('🔍 Checking auto-login conditions...');

    // Don't auto-login if user explicitly logged out
    const userLoggedOut = localStorage.getItem('user_logged_out') === 'true';
    if (userLoggedOut) {
      console.log('🚫 Auto-login blocked: User explicitly logged out');
      return false;
    }

    // Don't auto-login if user is already logged in
    if (this.authService.isLoggedIn()) {
      console.log('✅ User already logged in, skipping auto-login');
      return false;
    }

    // Check if remember me credentials exist
    const credentials = this.authService.getRememberedCredentials();
    console.log('🔍 Remembered credentials check:', {
      hasCredentials: !!credentials,
      rememberMe: credentials?.rememberMe,
      hasUsername: !!credentials?.username,
      hasPassword: !!credentials?.password,
      username: credentials?.username || 'N/A'
    });

    if (!credentials || !credentials.rememberMe || !credentials.username || !credentials.password) {
      console.log('❌ Auto-login not available: No valid remembered credentials');
      return false;
    }

    console.log('✅ Auto-login conditions met for user:', credentials.username);
    return true;
  }

  /**
   * Attempt automatic login with remembered credentials
   */
  attemptAutoLogin(): Observable<boolean> {
    if (!this.shouldAttemptAutoLogin()) {
      return of(false);
    }

    const credentials = this.authService.getRememberedCredentials();
    if (!credentials) {
      return of(false);
    }

    console.log('🔄 Starting auto-login process for user:', credentials.username);

    return this.authService.login(credentials.username, credentials.password, credentials.rememberMe)
      .pipe(
        tap(response => {
          console.log('✅ Auto-login successful:', response);
          
          // Initialize user permissions
          this.authService.initializeUserPermissions().subscribe({
            next: () => {
              console.log('✅ User permissions initialized after auto-login');
            },
            error: (error) => {
              console.error('❌ Failed to initialize permissions after auto-login:', error);
            }
          });
        }),
        tap(() => true), // Return true on success
        catchError(error => {
          console.error('❌ Auto-login failed:', error);
          
          // Clear invalid credentials if authentication failed
          if (error.status === 401 || error.status === 403) {
            console.log('🗑️ Clearing invalid remembered credentials due to auth failure');
            this.authService.clearRememberedCredentials();
          }
          
          // Return false on error (don't throw, just indicate failure)
          return of(false);
        })
      );
  }

  /**
   * Handle auto-login on app startup
   */
  handleStartupAutoLogin(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.shouldAttemptAutoLogin()) {
        resolve(false);
        return;
      }

      console.log('🚀 Handling startup auto-login...');

      this.attemptAutoLogin().subscribe({
        next: (success) => {
          if (success) {
            console.log('✅ Startup auto-login completed successfully');
            // Don't redirect here - let the app handle routing naturally
          } else {
            console.log('❌ Startup auto-login failed');
          }
          resolve(success);
        },
        error: (error) => {
          console.error('❌ Startup auto-login error:', error);
          resolve(false);
        }
      });
    });
  }

  /**
   * Clear auto-login data (called on explicit logout)
   */
  clearAutoLoginData(): void {
    this.authService.clearRememberedCredentials();
    localStorage.setItem('user_logged_out', 'true');
    console.log('🗑️ Auto-login data cleared');
  }

  /**
   * Enable auto-login for current session
   */
  enableAutoLogin(username: string, password: string): void {
    this.authService.saveRememberedCredentials(username, password, true);
    localStorage.removeItem('user_logged_out');
    console.log('💾 Auto-login enabled for user:', username);
  }

  /**
   * Disable auto-login but keep user logged in
   */
  disableAutoLogin(): void {
    this.authService.clearRememberedCredentials();
    console.log('🚫 Auto-login disabled');
  }

  /**
   * Check if auto-login is currently enabled
   */
  isAutoLoginEnabled(): boolean {
    const credentials = this.authService.getRememberedCredentials();
    return !!(credentials && credentials.rememberMe && credentials.username && credentials.password);
  }

  /**
   * Get auto-login status for debugging
   */
  getAutoLoginStatus(): any {
    const credentials = this.authService.getRememberedCredentials();
    const status = {
      isEnabled: this.isAutoLoginEnabled(),
      shouldAttempt: this.shouldAttemptAutoLogin(),
      hasCredentials: !!credentials,
      username: credentials?.username || 'N/A',
      userLoggedOut: localStorage.getItem('user_logged_out') === 'true',
      isCurrentlyLoggedIn: this.authService.isLoggedIn()
    };

    console.log('📊 Auto-login Status:', status);
    return status;
  }
}
