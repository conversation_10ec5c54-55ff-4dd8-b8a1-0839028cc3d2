/*
* NobleUI - Angular Admin Dashboard Template v3.0 (https://nobleui.com/)
* Copyright © 2024 NobleUI
* Licensed under ThemeForest License
*/

// Theme style for demo1 (Vertical Menu Layout)

// Bootstrap functions
@import "bootstrap/scss/functions";

// Custom variables
@import "./variables";
@import "./variables-dark";


// Bootstrap stylesheets
@import "bootstrap/scss/variables";
@import "bootstrap/scss/variables-dark";
@import "bootstrap/scss/maps";
@import "bootstrap/scss/mixins";
@import "bootstrap/scss/utilities";

// Bootstrap layout & components
@import "bootstrap/scss/root";
@import "bootstrap/scss/reboot";
@import "bootstrap/scss/type";
@import "bootstrap/scss/images";
@import "bootstrap/scss/containers";
@import "bootstrap/scss/grid";
@import "bootstrap/scss/tables";
@import "bootstrap/scss/forms";
@import "bootstrap/scss/buttons";
@import "bootstrap/scss/transitions";
@import "bootstrap/scss/dropdown";
@import "bootstrap/scss/button-group";
@import "bootstrap/scss/nav";
@import "bootstrap/scss/navbar";
@import "bootstrap/scss/card";
@import "bootstrap/scss/accordion";
@import "bootstrap/scss/breadcrumb";
@import "bootstrap/scss/pagination";
@import "bootstrap/scss/badge";
@import "bootstrap/scss/alert";
@import "bootstrap/scss/progress";
@import "bootstrap/scss/list-group";
@import "bootstrap/scss/close";
@import "bootstrap/scss/toasts";
@import "bootstrap/scss/modal";
@import "bootstrap/scss/tooltip";
@import "bootstrap/scss/popover";
@import "bootstrap/scss/carousel";
@import "bootstrap/scss/spinners";
@import "bootstrap/scss/offcanvas";

// Bootstrap helpers
@import "bootstrap/scss/helpers";

// Bootstrap utilities
@import "./utilities";
@import "bootstrap/scss/utilities/api";


// Custom mixins
@import "./mixins/animation";
@import "./mixins/buttons";
@import "./mixins/misc";


// Core styles
@import "./root";
@import "./reboot";
@import "./background";
@import "./functions";
@import "./misc";
@import "./helpers";
@import "./typography";
@import "./spinner";
@import "./demo";

// Layout
@import "./vertical-wrapper";
@import "./navbar";
@import "./sidebar";
@import "./layouts";

// components
@import "./components/alert";
@import "./components/badges";
@import "./components/breadcrumbs";
@import "./components/buttons";
@import "./components/close";
@import "./components/cards";
@import "./components/datepicker";
@import "./components/dropdown";
@import "./components/forms";
@import "./components/icons";
@import "./components/nav";
@import "./components/pagination";
@import "./components/rating";
@import "./components/tables";

// Pages
@import "./components/dashboard";
@import "./components/timeline";
@import "./components/auth";
@import "./components/chat";
@import "./components/email";


// Plugin overrides
@import "./plugin-overrides/angular-archwizard";
@import "./plugin-overrides/apex-charts";
@import "./plugin-overrides/dropzone";
@import "./plugin-overrides/full-calendar";
@import "./plugin-overrides/ng-select";
@import "./plugin-overrides/ngx-chips";
@import "./plugin-overrides/ngx-color-picker";
@import "./plugin-overrides/ngx-datatable";
@import "./plugin-overrides/ngx-quill";
@import "./plugin-overrides/ngx-scrollbar";
@import "./plugin-overrides/sweet-alert";


// Custom scss
@import "./custom";

// Modern popup and modal components
@import "./components/modern-modals";

