import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  evictions: number;
  totalRequests: number;
}

/**
 * Cache Manager Service
 * 
 * Provides centralized cache management, monitoring, and control functionality.
 * Works in conjunction with the CachingInterceptor to provide comprehensive
 * caching capabilities.
 */
@Injectable({
  providedIn: 'root'
})
export class CacheManagerService {
  private metricsSubject = new BehaviorSubject<CacheMetrics>({
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0
  });

  public metrics$ = this.metricsSubject.asObservable();

  // Cache invalidation patterns for different data types
  private readonly INVALIDATION_PATTERNS = {
    // When employee data changes, invalidate related caches
    employees: ['/api/v1/employees', '/api/v1/users/me'],
    
    // When master data changes, invalidate all master data caches
    masterData: [
      '/api/v1/master-data',
      '/api/v1/private-banks',
      '/api/v1/nbfcs',
      '/api/v1/institutes',
      '/api/v1/corporate-consultancies'
    ],
    
    // When settings change, invalidate settings and i18n caches
    settings: ['/api/v1/settings', '/api/v1/i18n'],
    
    // When leave data changes, invalidate leave-related caches
    leave: ['/api/v1/leave', '/api/v1/calendar'],
    
    // When role/permission data changes
    roles: ['/api/v1/roles', '/api/v1/permissions']
  };

  constructor() {
    // Make cache manager available globally for debugging
    (window as any).cacheManager = {
      getStats: () => this.getCacheStats(),
      clearCache: () => this.clearAllCache(),
      clearByPattern: (pattern: string) => this.clearCacheByPattern(pattern),
      getMetrics: () => this.metricsSubject.value,
      invalidateDataType: (dataType: string) => this.invalidateByDataType(dataType as keyof typeof this.INVALIDATION_PATTERNS)
    };

    console.log('🎯 CacheManagerService: Cache management tools available in window.cacheManager');
  }

  /**
   * Record a cache hit
   */
  recordHit(): void {
    const current = this.metricsSubject.value;
    this.metricsSubject.next({
      ...current,
      hits: current.hits + 1,
      totalRequests: current.totalRequests + 1
    });
  }

  /**
   * Record a cache miss
   */
  recordMiss(): void {
    const current = this.metricsSubject.value;
    this.metricsSubject.next({
      ...current,
      misses: current.misses + 1,
      totalRequests: current.totalRequests + 1
    });
  }

  /**
   * Record a cache eviction
   */
  recordEviction(): void {
    const current = this.metricsSubject.value;
    this.metricsSubject.next({
      ...current,
      evictions: current.evictions + 1
    });
  }

  /**
   * Get current cache statistics
   */
  getCacheStats(): Observable<CacheStats> {
    return new Observable(observer => {
      // This would typically get stats from the caching interceptor
      // For now, we'll return mock data structure
      const stats: CacheStats = {
        totalEntries: 0,
        totalSize: 0,
        hitRate: 0,
        missRate: 0,
        oldestEntry: 0,
        newestEntry: 0
      };
      
      observer.next(stats);
      observer.complete();
    });
  }

  /**
   * Clear all cache entries
   */
  clearAllCache(): void {
    // This would call the caching interceptor's clearCache method
    console.log('🗑️ CacheManagerService: Clearing all cache entries');
    
    // Reset metrics
    this.metricsSubject.next({
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0
    });
  }

  /**
   * Clear cache entries matching a pattern
   */
  clearCacheByPattern(pattern: string): void {
    console.log(`🗑️ CacheManagerService: Clearing cache entries matching pattern: ${pattern}`);
    // This would call the caching interceptor's clearCacheByPattern method
  }

  /**
   * Invalidate cache based on data type
   */
  invalidateByDataType(dataType: keyof typeof this.INVALIDATION_PATTERNS): void {
    const patterns = this.INVALIDATION_PATTERNS[dataType];
    if (patterns) {
      console.log(`🔄 CacheManagerService: Invalidating cache for data type: ${dataType}`);
      patterns.forEach(pattern => {
        this.clearCacheByPattern(pattern);
      });
    } else {
      console.warn(`⚠️ CacheManagerService: Unknown data type: ${dataType}`);
    }
  }

  /**
   * Preload cache for critical data
   */
  preloadCriticalData(): void {
    console.log('🚀 CacheManagerService: Preloading critical data...');
    
    // This would trigger requests for critical data that should be cached
    // Examples: user profile, settings, master data, etc.
    const criticalEndpoints = [
      '/api/v1/users/me',
      '/api/v1/settings',
      '/api/v1/leave/types',
      '/api/v1/calendar/holidays'
    ];
    
    // Note: Actual implementation would make HTTP requests to these endpoints
    console.log(`📋 CacheManagerService: Would preload ${criticalEndpoints.length} critical endpoints`);
  }

  /**
   * Get cache efficiency metrics
   */
  getCacheEfficiency(): Observable<{ hitRate: number; efficiency: string }> {
    return new Observable(observer => {
      const metrics = this.metricsSubject.value;
      const hitRate = metrics.totalRequests > 0 
        ? (metrics.hits / metrics.totalRequests) * 100 
        : 0;
      
      let efficiency = 'Poor';
      if (hitRate >= 80) efficiency = 'Excellent';
      else if (hitRate >= 60) efficiency = 'Good';
      else if (hitRate >= 40) efficiency = 'Fair';
      
      observer.next({ hitRate, efficiency });
      observer.complete();
    });
  }

  /**
   * Schedule periodic cache cleanup
   */
  scheduleCleanup(): void {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      console.log('🧹 CacheManagerService: Running scheduled cache cleanup');
      // This would trigger cleanup in the caching interceptor
    }, 5 * 60 * 1000);
  }

  /**
   * Get cache recommendations based on usage patterns
   */
  getCacheRecommendations(): string[] {
    const metrics = this.metricsSubject.value;
    const recommendations: string[] = [];
    
    if (metrics.totalRequests > 100) {
      const hitRate = (metrics.hits / metrics.totalRequests) * 100;
      
      if (hitRate < 30) {
        recommendations.push('Consider increasing cache TTL for frequently accessed endpoints');
      }
      
      if (metrics.evictions > metrics.hits * 0.1) {
        recommendations.push('Cache size may be too small, consider increasing memory allocation');
      }
      
      if (hitRate > 90) {
        recommendations.push('Excellent cache performance! Consider expanding caching to more endpoints');
      }
    }
    
    return recommendations;
  }
}
