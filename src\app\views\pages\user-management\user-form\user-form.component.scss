// User Form Component Styles

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;

  .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.5rem;

    .card-title {
      color: #495057;
      font-weight: 600;
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Form styles
.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;

  .text-danger {
    color: #dc3545 !important;
  }
}

.form-control {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: 0;
  }

  &.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }
  }

  &::placeholder {
    color: #6c757d;
    opacity: 1;
  }
}

// Input group styles
.input-group {
  .input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.375rem 0 0 0.375rem;
    color: #6c757d;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .form-control {
    border-radius: 0 0.375rem 0.375rem 0;
    border-left: 0;

    &:focus {
      border-left: 1px solid #86b7fe;
    }

    &.is-invalid {
      border-left: 1px solid #dc3545;
    }
  }
}

// Form validation styles
.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #dc3545;

  &.d-block {
    display: block !important;
  }
}

// Form switch styles
.form-check {
  &.form-switch {
    padding-left: 2.5rem;

    .form-check-input {
      width: 2rem;
      height: 1rem;
      margin-left: -2.5rem;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280,0,0,.25%29'/%3e%3c/svg%3e");
      background-position: left center;
      border-radius: 2rem;
      transition: background-position 0.15s ease-in-out;

      &:checked {
        background-position: right center;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
        background-color: #0d6efd;
        border-color: #0d6efd;
      }

      &:focus {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280,0,0,.25%29'/%3e%3c/svg%3e");
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        border-color: #86b7fe;
      }

      &:checked:focus {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
      }
    }

    .form-check-label {
      font-weight: 500;
      cursor: pointer;

      .text-success {
        color: #198754 !important;
      }

      .text-warning {
        color: #ffc107 !important;
      }

      .text-danger {
        color: #dc3545 !important;
      }

      .text-secondary {
        color: #6c757d !important;
      }
    }
  }
}

// Avatar styles
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;

  &.avatar-md {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .avatar-initial {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-weight: 600;
    border-radius: 50%;
  }
}

// Button styles
.btn {
  border-radius: 0.375rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;

  &.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;

    &:hover {
      background-color: #0b5ed7;
      border-color: #0a58ca;
    }

    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.5);
    }

    &:disabled {
      background-color: #0d6efd;
      border-color: #0d6efd;
      opacity: 0.65;
    }
  }

  &.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;

    &:hover {
      color: #fff;
      background-color: #6c757d;
      border-color: #6c757d;
    }

    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.5);
    }
  }

  &.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;

    &:hover {
      color: #000;
      background-color: #ffc107;
      border-color: #ffc107;
    }

    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);
    }
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Alert styles
.alert {
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;

  &.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
  }

  &.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c2c7;
  }
}

// Loading spinner
.spinner-border {
  &.text-primary {
    color: #0d6efd !important;
  }
}

// User preview section
.bg-light {
  background-color: #f8f9fa !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.rounded {
  border-radius: 0.375rem !important;
}

// Responsive adjustments
@media (max-width: 992px) {
  .card-header {
    .d-flex {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .col-lg-8,
  .col-lg-4 {
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .form-control {
    font-size: 16px; // Prevent zoom on iOS
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;

    .btn {
      width: 100%;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #212529;
    color: #fff;

    .card-header {
      border-bottom-color: rgba(255, 255, 255, 0.125);
    }
  }

  .form-label {
    color: #f8f9fa;
  }

  .form-control {
    background-color: #212529;
    border-color: #495057;
    color: #fff;

    &:focus {
      background-color: #212529;
      border-color: #86b7fe;
      color: #fff;
    }

    &::placeholder {
      color: #6c757d;
    }
  }

  .input-group-text {
    background-color: #343a40;
    border-color: #495057;
    color: #adb5bd;
  }

  .bg-light {
    background-color: #343a40 !important;
  }

  .border {
    border-color: #495057 !important;
  }
}
