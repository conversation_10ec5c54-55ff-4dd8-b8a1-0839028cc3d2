import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Source } from '../../../../../core/services/source.service';

@Component({
  selector: 'app-source-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-trending-up me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Source
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Source Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ source?.name }}</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This advanced form will include comprehensive source management with:
        <ul class="mt-2 mb-0">
          <li><strong>Source Type Configuration</strong> - 17 source types with custom tracking parameters</li>
          <li><strong>Attribution Model Setup</strong> - 6 attribution models with custom weighting</li>
          <li><strong>Campaign Integration</strong> - Google Ads, Facebook, LinkedIn, and custom platform integration</li>
          <li><strong>Conversion Tracking</strong> - Multi-goal tracking with cross-device attribution</li>
          <li><strong>Cost Tracking & ROI</strong> - CPC, CPM, CPA models with budget management</li>
          <li><strong>Analytics Integration</strong> - Google Analytics, Facebook Pixel, LinkedIn Insight</li>
          <li><strong>Auto-Assignment Rules</strong> - Lead routing based on source criteria</li>
          <li><strong>Follow-up Templates</strong> - Automated email, SMS, and task templates</li>
        </ul>
      </div>

      <div class="alert alert-warning">
        <i class="feather icon-settings me-2"></i>
        <strong>Advanced Features:</strong>
        <ul class="mt-2 mb-0">
          <li>Real-time campaign data synchronization with external platforms</li>
          <li>Custom tracking parameter validation and URL generation</li>
          <li>Multi-touch attribution with configurable window periods</li>
          <li>Offline conversion tracking and import capabilities</li>
          <li>Quality score calculation based on conversion performance</li>
          <li>Lead score modifiers for source-based lead qualification</li>
          <li>Budget alerts and automated bid management</li>
          <li>Cross-platform campaign performance comparison</li>
        </ul>
      </div>

      <div class="alert alert-success">
        <i class="feather icon-bar-chart-2 me-2"></i>
        <strong>Analytics & Reporting:</strong>
        <ul class="mt-2 mb-0">
          <li>Attribution path analysis and visualization</li>
          <li>Source performance benchmarking and optimization</li>
          <li>Cost per acquisition tracking across all channels</li>
          <li>Revenue attribution and lifetime value analysis</li>
          <li>Campaign ROI calculation and forecasting</li>
          <li>A/B testing framework for source optimization</li>
          <li>Custom dashboard creation for stakeholder reporting</li>
        </ul>
      </div>

      <div class="alert alert-primary">
        <i class="feather icon-layers me-2"></i>
        <strong>Source Categories & Types:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <strong>Digital Sources:</strong>
            <ul class="mb-2">
              <li>Website, Social Media, Email</li>
              <li>SEO, PPC, Content Marketing</li>
              <li>Affiliate, Online Advertising</li>
            </ul>
            <strong>Traditional Sources:</strong>
            <ul class="mb-2">
              <li>Print, Radio, Television</li>
              <li>Events, Trade Shows</li>
              <li>Direct Mail, Outdoor</li>
            </ul>
          </div>
          <div class="col-md-6">
            <strong>Relationship Sources:</strong>
            <ul class="mb-2">
              <li>Referrals, Word of Mouth</li>
              <li>Partner Channels</li>
              <li>Cold Outreach</li>
            </ul>
            <strong>Internal Sources:</strong>
            <ul class="mb-2">
              <li>Direct Contact, Walk-ins</li>
              <li>Internal Sales Team</li>
              <li>Customer Service</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="alert alert-secondary">
        <i class="feather icon-target me-2"></i>
        <strong>Attribution Models:</strong>
        <div class="row mt-2">
          <div class="col-md-6">
            <ul class="mb-2">
              <li><strong>First Touch:</strong> Credit to first interaction</li>
              <li><strong>Last Touch:</strong> Credit to final interaction</li>
              <li><strong>Linear:</strong> Equal credit distribution</li>
            </ul>
          </div>
          <div class="col-md-6">
            <ul class="mb-2">
              <li><strong>Time Decay:</strong> More credit to recent interactions</li>
              <li><strong>Position Based:</strong> Emphasis on first and last</li>
              <li><strong>Custom:</strong> Configurable attribution rules</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: [`
    .alert {
      border-radius: 0.5rem;
    }
    
    .alert ul {
      padding-left: 1.5rem;
    }
    
    .alert li {
      margin-bottom: 0.25rem;
    }
    
    .row {
      margin: 0;
    }
    
    .col-md-6 {
      padding: 0 0.5rem;
    }
  `]
})
export class SourceFormComponent {
  @Input() isEditMode = false;
  @Input() source: Source | null = null;

  sourceForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.sourceForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      source_type: ['', [Validators.required]],
      source_category: ['', [Validators.required]],
      attribution_model: ['', [Validators.required]]
    });
  }
}
