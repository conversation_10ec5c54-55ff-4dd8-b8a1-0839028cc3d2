import { Injectable } from '@angular/core';

export interface VendorChunkConfig {
  name: string;
  test: RegExp | string;
  priority: number;
  enforce?: 'pre' | 'post';
  chunks?: 'all' | 'async' | 'initial';
  minSize?: number;
  maxSize?: number;
}

export interface ChunkOptimizationStats {
  totalChunks: number;
  vendorChunks: number;
  appChunks: number;
  averageChunkSize: number;
  largestChunk: number;
  smallestChunk: number;
}

/**
 * Vendor Chunk Optimizer Service
 * 
 * Provides intelligent vendor chunk splitting strategies to optimize
 * bundle loading and caching efficiency.
 */
@Injectable({
  providedIn: 'root'
})
export class VendorChunkOptimizerService {
  
  // Optimal vendor chunk configurations
  private vendorChunkConfigs: VendorChunkConfig[] = [
    {
      name: 'angular-core',
      test: /[\\/]node_modules[\\/]@angular[\\/](core|common|platform-browser|platform-browser-dynamic)[\\/]/,
      priority: 30,
      chunks: 'all',
      minSize: 20000,
      maxSize: 500000
    },
    {
      name: 'angular-forms',
      test: /[\\/]node_modules[\\/]@angular[\\/](forms|reactive-forms)[\\/]/,
      priority: 25,
      chunks: 'all',
      minSize: 10000,
      maxSize: 200000
    },
    {
      name: 'angular-router',
      test: /[\\/]node_modules[\\/]@angular[\\/]router[\\/]/,
      priority: 25,
      chunks: 'all',
      minSize: 10000,
      maxSize: 200000
    },
    {
      name: 'angular-animations',
      test: /[\\/]node_modules[\\/]@angular[\\/](animations|cdk)[\\/]/,
      priority: 20,
      chunks: 'all',
      minSize: 10000,
      maxSize: 300000
    },
    {
      name: 'angular-material',
      test: /[\\/]node_modules[\\/]@angular[\\/]material[\\/]/,
      priority: 15,
      chunks: 'async',
      minSize: 10000,
      maxSize: 400000
    },
    {
      name: 'bootstrap',
      test: /[\\/]node_modules[\\/](@ng-bootstrap|bootstrap)[\\/]/,
      priority: 20,
      chunks: 'all',
      minSize: 10000,
      maxSize: 200000
    },
    {
      name: 'rxjs',
      test: /[\\/]node_modules[\\/]rxjs[\\/]/,
      priority: 25,
      chunks: 'all',
      minSize: 10000,
      maxSize: 150000
    },
    {
      name: 'charts',
      test: /[\\/]node_modules[\\/](apexcharts|ng-apexcharts)[\\/]/,
      priority: 10,
      chunks: 'async',
      minSize: 10000,
      maxSize: 300000
    },
    {
      name: 'ui-libraries',
      test: /[\\/]node_modules[\\/](sweetalert2|@ng-select|ngx-|@fullcalendar)[\\/]/,
      priority: 15,
      chunks: 'async',
      minSize: 10000,
      maxSize: 250000
    },
    {
      name: 'utilities',
      test: /[\\/]node_modules[\\/](lodash|moment|date-fns|uuid)[\\/]/,
      priority: 10,
      chunks: 'all',
      minSize: 5000,
      maxSize: 100000
    },
    {
      name: 'polyfills',
      test: /[\\/]node_modules[\\/](zone\.js|core-js|regenerator-runtime)[\\/]/,
      priority: 30,
      chunks: 'all',
      minSize: 5000,
      maxSize: 100000
    }
  ];

  constructor() {
    console.log('📦 VendorChunkOptimizerService: Initialized with optimal chunk configurations');
  }

  /**
   * Get webpack optimization configuration for vendor chunks
   */
  getWebpackOptimizationConfig(): any {
    const cacheGroups: any = {};

    // Add vendor chunk configurations
    this.vendorChunkConfigs.forEach(config => {
      cacheGroups[config.name] = {
        test: config.test,
        name: config.name,
        priority: config.priority,
        chunks: config.chunks || 'all',
        enforce: config.enforce || false,
        minSize: config.minSize || 10000,
        maxSize: config.maxSize || 250000,
        reuseExistingChunk: true
      };
    });

    // Default chunk for remaining vendor code
    cacheGroups.vendor = {
      test: /[\\/]node_modules[\\/]/,
      name: 'vendors',
      priority: 5,
      chunks: 'all',
      minSize: 10000,
      maxSize: 200000,
      reuseExistingChunk: true
    };

    // Common chunk for shared application code
    cacheGroups.common = {
      name: 'common',
      minChunks: 2,
      priority: 0,
      chunks: 'all',
      minSize: 5000,
      maxSize: 100000,
      reuseExistingChunk: true
    };

    return {
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 500000,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        automaticNameDelimiter: '-',
        cacheGroups
      },
      runtimeChunk: {
        name: 'runtime'
      },
      usedExports: true,
      sideEffects: false
    };
  }

  /**
   * Analyze current chunk configuration
   */
  analyzeChunkConfiguration(): ChunkOptimizationStats {
    // This would analyze the current webpack build output
    // For now, return mock data structure
    return {
      totalChunks: 0,
      vendorChunks: 0,
      appChunks: 0,
      averageChunkSize: 0,
      largestChunk: 0,
      smallestChunk: 0
    };
  }

  /**
   * Get recommendations for chunk optimization
   */
  getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];

    // Analyze current configuration and provide recommendations
    recommendations.push('Split Angular core modules into separate chunks for better caching');
    recommendations.push('Create dedicated chunks for large UI libraries');
    recommendations.push('Implement async loading for non-critical vendor libraries');
    recommendations.push('Use tree-shaking to eliminate unused vendor code');
    recommendations.push('Configure appropriate chunk size limits (20KB-500KB)');

    return recommendations;
  }

  /**
   * Get vendor chunk loading strategy
   */
  getVendorLoadingStrategy(): {
    critical: string[];
    high: string[];
    medium: string[];
    low: string[];
  } {
    return {
      critical: ['angular-core', 'rxjs', 'polyfills'],
      high: ['angular-forms', 'angular-router', 'bootstrap'],
      medium: ['angular-animations', 'utilities'],
      low: ['charts', 'ui-libraries', 'angular-material']
    };
  }

  /**
   * Optimize chunk loading order
   */
  optimizeChunkLoadingOrder(): string[] {
    const strategy = this.getVendorLoadingStrategy();
    
    return [
      ...strategy.critical,
      ...strategy.high,
      ...strategy.medium,
      ...strategy.low
    ];
  }

  /**
   * Get chunk preloading configuration
   */
  getChunkPreloadingConfig(): {
    preload: string[];
    prefetch: string[];
    lazy: string[];
  } {
    return {
      preload: ['angular-core', 'rxjs', 'bootstrap'], // Load immediately
      prefetch: ['angular-forms', 'angular-router', 'utilities'], // Load when idle
      lazy: ['charts', 'ui-libraries', 'angular-material'] // Load on demand
    };
  }

  /**
   * Monitor chunk loading performance
   */
  monitorChunkPerformance(): void {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource' && entry.name.includes('.js')) {
            const resourceEntry = entry as PerformanceResourceTiming;
            
            // Log chunk loading performance
            if (resourceEntry.duration > 500) {
              console.warn(`⚠️ Slow chunk load: ${this.extractChunkName(entry.name)} took ${resourceEntry.duration.toFixed(2)}ms`);
            } else {
              console.log(`✅ Chunk loaded: ${this.extractChunkName(entry.name)} in ${resourceEntry.duration.toFixed(2)}ms`);
            }
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
      console.log('📊 VendorChunkOptimizerService: Chunk performance monitoring enabled');
    }
  }

  /**
   * Extract chunk name from URL
   */
  private extractChunkName(url: string): string {
    const match = url.match(/([^\/]+)\.js$/);
    return match ? match[1] : 'unknown';
  }

  /**
   * Get chunk size recommendations
   */
  getChunkSizeRecommendations(): {
    initial: { min: number; max: number; optimal: number };
    async: { min: number; max: number; optimal: number };
    vendor: { min: number; max: number; optimal: number };
  } {
    return {
      initial: { min: 20000, max: 500000, optimal: 250000 }, // 20KB - 500KB
      async: { min: 10000, max: 300000, optimal: 150000 },   // 10KB - 300KB
      vendor: { min: 30000, max: 600000, optimal: 400000 }   // 30KB - 600KB
    };
  }

  /**
   * Validate chunk configuration
   */
  validateChunkConfiguration(): {
    isValid: boolean;
    warnings: string[];
    errors: string[];
  } {
    const warnings: string[] = [];
    const errors: string[] = [];

    // Validate chunk size configurations
    this.vendorChunkConfigs.forEach(config => {
      if (config.minSize && config.maxSize && config.minSize >= config.maxSize) {
        errors.push(`Invalid size configuration for ${config.name}: minSize >= maxSize`);
      }

      if (config.maxSize && config.maxSize > 1000000) {
        warnings.push(`Large max size for ${config.name}: ${config.maxSize} bytes`);
      }
    });

    return {
      isValid: errors.length === 0,
      warnings,
      errors
    };
  }
}
