version: '3.9'



services:
  # Production service
  bizzcorp-angular-prod:
    container_name: bizzcorp-angular-prod
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: bizzcorpfrontend:latest
    ports:
      - "8020:8020"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8020/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - webnet
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.bizzcorp.rule=Host(`bizzcorp.antllp.com`)"
      - "traefik.http.services.bizzcorp.loadbalancer.server.port=8020"

  # Development service (optional)
  bizzcorp-angular-dev:
    container_name: bizzcorp-angular-dev
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: bizzcorp-angular:development
    ports:
      - "4200:8020"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    networks:
      - webnet
    profiles:
      - dev

networks:
  webnet:
    driver: bridge