<!-- Enhanced Permission Management Header -->
<div class="permission-management-header">
  <div class="container-fluid">
    <div class="row align-items-center mb-4">
      <div class="col-md-8">
        <div class="page-header">
          <div class="d-flex align-items-center mb-2">
            <button class="btn btn-outline-light me-3" (click)="goBack()">
              <i data-feather="arrow-left" class="icon-sm me-1" appFeatherIcon></i>
              Back to Roles
            </button>
            <div>
              <h4 class="page-title mb-1">
                <i data-feather="key" class="icon-md me-2" appFeatherIcon></i>
                Manage Permissions
              </h4>
              <p class="page-subtitle mb-0" *ngIf="selectedRole">
                Configuring permissions for <strong>{{ selectedRole.name }}</strong>
                <span *ngIf="selectedRole.description" class="text-muted"> - {{ selectedRole.description }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 text-end">
        <div class="header-actions">
          <button class="btn btn-success me-2" (click)="savePermissions()" [disabled]="!hasChanges()">
            <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i>
            Save Changes
          </button>
          <button class="btn btn-outline-light" (click)="resetChanges()" [disabled]="!hasChanges()">
            <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
            Reset
          </button>
        </div>
      </div>
    </div>

    <!-- Permission Summary Cards -->
    <div class="permission-summary-section">
      <div class="row g-3 mb-4">
        <div class="col-md-3">
          <div class="summary-card">
            <div class="summary-icon bg-primary">
              <i data-feather="shield" appFeatherIcon></i>
            </div>
            <div class="summary-content">
              <h6 class="summary-number">{{ selectedPermissions.size }}</h6>
              <p class="summary-label">Assigned Permissions</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="summary-card">
            <div class="summary-icon bg-info">
              <i data-feather="list" appFeatherIcon></i>
            </div>
            <div class="summary-content">
              <h6 class="summary-number">{{ permissions.length }}</h6>
              <p class="summary-label">Total Available</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="summary-card">
            <div class="summary-icon bg-warning">
              <i data-feather="clock" appFeatherIcon></i>
            </div>
            <div class="summary-content">
              <h6 class="summary-number">{{ getPendingChanges() }}</h6>
              <p class="summary-label">Pending Changes</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="summary-card">
            <div class="summary-icon bg-success">
              <i data-feather="check-circle" appFeatherIcon></i>
            </div>
            <div class="summary-content">
              <h6 class="summary-number">{{ getCompletionPercentage() }}%</h6>
              <p class="summary-label">Coverage</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Search and Filter Section -->
<div class="permission-controls-section">
  <div class="container-fluid">
    <div class="row g-3 mb-4">
      <div class="col-md-4">
        <div class="search-box">
          <div class="input-group">
            <span class="input-group-text">
              <i data-feather="search" class="icon-sm" appFeatherIcon></i>
            </span>
            <input
              type="text"
              class="form-control"
              [formControl]="searchTerm"
              placeholder="Search permissions by name or description..."
            >
            <button
              *ngIf="searchTerm.value"
              class="btn btn-outline-secondary"
              type="button"
              (click)="searchTerm.setValue('')">
              <i data-feather="x" class="icon-sm" appFeatherIcon></i>
            </button>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <select class="form-select" [(ngModel)]="filterStatus" (ngModelChange)="applyFilters()">
          <option value="all">All Permissions</option>
          <option value="assigned">Assigned Only</option>
          <option value="unassigned">Unassigned Only</option>
        </select>
      </div>

      <div class="col-md-2">
        <select class="form-select" [(ngModel)]="categoryFilter" (ngModelChange)="applyFilters()">
          <option value="all">All Categories</option>
          <option value="users">User Management</option>
          <option value="roles">Role Management</option>
          <option value="sales">Sales</option>
          <option value="employees">Employees</option>
          <option value="master">Master Data</option>
          <option value="reports">Reports</option>
          <option value="admin">Administration</option>
        </select>
      </div>

      <div class="col-md-3">
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary flex-fill" (click)="toggleSelectAll()">
            <i *ngIf="!areAllFilteredSelected()" data-feather="check-square" class="icon-sm me-1" appFeatherIcon></i>
            <i *ngIf="areAllFilteredSelected()" data-feather="square" class="icon-sm me-1" appFeatherIcon></i>
            {{ areAllFilteredSelected() ? 'Deselect All' : 'Select All' }}
          </button>
          <button class="btn btn-outline-secondary" (click)="toggleViewMode()">
            <i [attr.data-feather]="viewMode === 'grid' ? 'list' : 'grid'" class="icon-sm" appFeatherIcon></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Permissions Content -->
<div class="permissions-content-section">
  <div class="container-fluid">

    <!-- Loading indicator -->
    <div *ngIf="loading" class="loading-section">
      <div class="d-flex justify-content-center align-items-center" style="min-height: 300px;">
        <div class="text-center">
          <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading permissions...</span>
          </div>
          <p class="text-muted">Loading permissions...</p>
        </div>
      </div>
    </div>

    <!-- Grid View -->
    <div *ngIf="!loading && viewMode === 'grid'" class="permissions-grid">
      <div class="row g-4">
        <div class="col-xl-4 col-lg-6 col-md-6" *ngFor="let permission of filteredPermissions">
          <div class="permission-card"
               [class.selected]="isPermissionSelected(permission.id)"
               [class.category-users]="getPermissionCategory(permission.name) === 'users'"
               [class.category-roles]="getPermissionCategory(permission.name) === 'roles'"
               [class.category-sales]="getPermissionCategory(permission.name) === 'sales'"
               [class.category-employees]="getPermissionCategory(permission.name) === 'employees'"
               [class.category-master]="getPermissionCategory(permission.name) === 'master'"
               [class.category-admin]="getPermissionCategory(permission.name) === 'admin'">

            <div class="permission-card-header">
              <div class="permission-checkbox">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [id]="'permission-' + permission.id"
                  [checked]="isPermissionSelected(permission.id)"
                  (change)="onPermissionChange(permission.id, $event)">
              </div>
              <div class="permission-category">
                <span class="category-badge">{{ getPermissionCategory(permission.name) }}</span>
              </div>
            </div>

            <div class="permission-card-body">
              <label class="permission-label" [for]="'permission-' + permission.id">
                <h6 class="permission-name">{{ permission.name }}</h6>
                <p class="permission-description" *ngIf="permission.description">
                  {{ permission.description }}
                </p>
                <p class="permission-description text-muted" *ngIf="!permission.description">
                  No description available
                </p>
              </label>
            </div>

            <div class="permission-card-footer">
              <div class="permission-actions">
                <small class="text-muted">
                  <i data-feather="key" class="icon-xs me-1" appFeatherIcon></i>
                  {{ getPermissionType(permission.name) }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- List View -->
    <div *ngIf="!loading && viewMode === 'list'" class="permissions-list">
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th width="50">
                    <input
                      type="checkbox"
                      class="form-check-input"
                      [checked]="areAllFilteredSelected()"
                      [indeterminate]="isSomeSelected()"
                      (change)="toggleSelectAll()">
                  </th>
                  <th>Permission Name</th>
                  <th>Category</th>
                  <th>Type</th>
                  <th>Description</th>
                  <th class="text-center">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let permission of filteredPermissions"
                    [class.table-primary]="isPermissionSelected(permission.id)">
                  <td>
                    <input
                      class="form-check-input"
                      type="checkbox"
                      [id]="'list-permission-' + permission.id"
                      [checked]="isPermissionSelected(permission.id)"
                      (change)="onPermissionChange(permission.id, $event)">
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="permission-icon me-2">
                        <i data-feather="key" class="icon-sm" appFeatherIcon></i>
                      </div>
                      <div>
                        <h6 class="mb-0">{{ permission.name }}</h6>
                        <small class="text-muted">ID: {{ permission.id }}</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge category-badge-{{ getPermissionCategory(permission.name) }}">
                      {{ getPermissionCategory(permission.name) | titlecase }}
                    </span>
                  </td>
                  <td>
                    <span class="badge bg-secondary-subtle text-secondary">
                      {{ getPermissionType(permission.name) }}
                    </span>
                  </td>
                  <td>
                    <span class="text-truncate" style="max-width: 200px; display: inline-block;"
                          [ngbTooltip]="permission.description"
                          *ngIf="permission.description">
                      {{ permission.description }}
                    </span>
                    <span class="text-muted fst-italic" *ngIf="!permission.description">No description</span>
                  </td>
                  <td class="text-center">
                    <span *ngIf="isPermissionSelected(permission.id)" class="badge bg-success">Assigned</span>
                    <span *ngIf="!isPermissionSelected(permission.id)" class="badge bg-light text-dark">Available</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && filteredPermissions.length === 0" class="empty-state-section">
      <div class="text-center py-5">
        <div class="empty-state-icon mb-4">
          <i data-feather="key" class="icon-xxl text-muted" appFeatherIcon></i>
        </div>
        <h5 class="text-muted mb-3">No permissions found</h5>
        <p class="text-muted mb-4" *ngIf="searchTerm.value || filterStatus !== 'all' || categoryFilter !== 'all'">
          Try adjusting your search criteria or filters
        </p>
        <p class="text-muted mb-4" *ngIf="!searchTerm.value && filterStatus === 'all' && categoryFilter === 'all'">
          No permissions are available for this role
        </p>
      </div>
    </div>

    <!-- Action Bar -->
    <div class="action-bar-section" *ngIf="!loading">
      <div class="d-flex justify-content-between align-items-center">
        <div class="selection-info">
          <span class="text-muted">
            <strong>{{ selectedPermissions.size }}</strong> of <strong>{{ permissions.length }}</strong> permissions selected
            <span *ngIf="hasChanges()" class="text-warning ms-2">
              <i data-feather="alert-circle" class="icon-xs me-1" appFeatherIcon></i>
              {{ getPendingChanges() }} pending changes
            </span>
          </span>
        </div>
        <div class="action-buttons d-flex gap-2">
          <button class="btn btn-outline-secondary" (click)="resetChanges()" [disabled]="!hasChanges()">
            <i data-feather="refresh-cw" class="icon-sm me-1" appFeatherIcon></i>
            Reset Changes
          </button>
          <button class="btn btn-success" (click)="savePermissions()" [disabled]="submitting || !hasChanges()">
            <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
            <i data-feather="save" class="icon-sm me-1" appFeatherIcon></i>
            Save Changes
          </button>
        </div>
      </div>
    </div>

  </div>
</div>
