<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a routerLink=".">Forms</a></li>
    <li class="breadcrumb-item active" aria-current="page">Editors</li>
  </ol>
</nav>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">Ngx-quill</h4>
        <p class="text-secondary">Read the <a href="https://github.com/KillerCodeMonkey/ngx-quill" target="_blank"> Official Ngx-quill Documentation </a>for a full list of instructions and other options.</p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12 grid-margin">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title">Example</h6>

        <quill-editor [(ngModel)]="htmlText"
          placeholder="Enter Text"
          [modules]="quillConfig"
          (onSelectionChanged)="onSelectionChanged($event)"
          (onContentChanged)="onContentChanged($event)">
        </quill-editor>

        <div class="mt-3">
          <pre class="mb-0">{{htmlText}}</pre>
        </div>

      </div>
    </div>
  </div> <!-- col -->
</div>