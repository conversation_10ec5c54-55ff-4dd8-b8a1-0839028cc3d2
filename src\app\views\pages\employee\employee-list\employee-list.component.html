<!-- Employee List Template -->
<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h4 class="card-title">Employee Management</h4>
          <div class="d-flex gap-2">
            <button
              class="btn btn-outline-success"
              (click)="exportEmployees()"
              [disabled]="exporting">
              <span *ngIf="exporting" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i *ngIf="!exporting" data-feather="download" appFeatherIcon class="me-2"></i>
              {{ exporting ? 'Exporting...' : 'Export Excel' }}
            </button>
            <a routerLink="/employee/bulk-upload" class="btn btn-outline-info">
              <i data-feather="upload" appFeatherIcon class="me-2"></i>
              Bulk Upload
            </a>
            <a routerLink="/employee/create" class="btn btn-primary">
              <i data-feather="user-plus" appFeatherIcon class="me-2"></i>
              Add Employee
            </a>
          </div>
        </div>

        <!-- Filters Section -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="input-group">
              <span class="input-group-text">
                <i data-feather="search" appFeatherIcon></i>
              </span>
              <input
                type="text"
                class="form-control"
                placeholder="Search employees by name, email, department..."
                [(ngModel)]="searchTerm"
                (input)="onSearchInput($event)"
              >
            </div>
          </div>
          <div class="col-md-2">
            <select class="form-select" [(ngModel)]="statusFilter" (change)="applyFilters()">
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <div class="col-md-2">
            <select class="form-select" [(ngModel)]="departmentFilter" (change)="applyFilters()">
              <option value="all">All Departments</option>
              <option *ngFor="let dept of departments" [value]="dept">{{ dept }}</option>
            </select>
          </div>
          <div class="col-md-2">
            <select class="form-select" [(ngModel)]="designationFilter" (change)="applyFilters()">
              <option value="all">All Designations</option>
              <option *ngFor="let designation of designations" [value]="designation">{{ designation }}</option>
            </select>
          </div>
          <div class="col-md-3">
            <div class="d-flex gap-2">
              <button class="btn btn-outline-secondary" (click)="resetFilters()" title="Reset all filters">
                <i data-feather="refresh-cw" appFeatherIcon class="me-2"></i>
                Reset
              </button>
              <button class="btn btn-outline-info" (click)="refreshEmployees()" title="Refresh employee list">
                <i data-feather="refresh-ccw" appFeatherIcon class="me-2"></i>
                Refresh
              </button>
              <button class="btn btn-outline-primary" (click)="loadActiveEmployees()" title="Show only active employees">
                <i data-feather="users" appFeatherIcon class="me-2"></i>
                Active Only
              </button>
            </div>
          </div>
        </div>

        <!-- Loading and Error States -->
        <div *ngIf="loading" class="text-center my-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Loading employees...</p>
        </div>

        <div *ngIf="error" class="alert alert-danger mt-3" role="alert">
          {{ error }}
          <button type="button" class="btn btn-link" (click)="loadEmployees()">Try again</button>
        </div>

        <!-- Employee Statistics -->
        <div *ngIf="!loading && !error && employees.length > 0" class="row mb-3">
          <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex gap-3">
                <span class="badge bg-primary fs-6">
                  Total: {{ totalEmployees }}
                </span>
                <span class="badge bg-success fs-6">
                  Active: {{ activeEmployeesCount }}
                </span>
                <span class="badge bg-warning fs-6">
                  Inactive: {{ inactiveEmployeesCount }}
                </span>
                <span class="badge bg-info fs-6" *ngIf="searchTerm">
                  Filtered: {{ filteredEmployees.length }}
                </span>
              </div>
              <small class="text-muted">
                Showing {{ filteredEmployees.length }} of {{ totalEmployees }} employees
              </small>
            </div>
          </div>
        </div>

        <!-- High-Performance Virtual Scroll Employee Table -->
        <div *ngIf="!loading && !error" class="virtual-table-container" style="height: 600px;">
          <app-virtual-scroll-table
            [items]="filteredEmployees"
            [columns]="virtualScrollColumns"
            [config]="virtualScrollConfig"
            [loading]="loading"
            [emptyMessage]="'No employees found. Try adjusting your search or filters.'"
            (rowClick)="onVirtualScrollRowClick($event)"
            (sort)="onVirtualScrollSort($event)"
            (action)="onVirtualScrollAction($event)">
          </app-virtual-scroll-table>
        </div>
      </div>
    </div>
  </div>
</div>
