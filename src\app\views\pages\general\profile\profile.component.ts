import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, ColumnMode } from '@siemens/ngx-datatable';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { debounceTime, distinctUntilChanged, finalize, catchError, map } from 'rxjs/operators';
import { forkJoin, of, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { RoleService } from '../../../../core/services/role.service';
import { Role } from '../../../../core/models/role.model';
import { AuthService, User } from '../../../../core/services/auth.service';
import {
  EmployeeService,
  BizzCorpEmployee,
  Department,
  Designation,
  BizzCorpRole,
  EmployeeServiceError
} from '../../../../core/services/employee.service';

// BizzCorp Employee data interface for profile display
export interface ProfileEmployeeData {
  id: string;
  employeeCode: string;
  firstName: string;
  middleName: string;
  lastName: string;
  bloodGroup: string;
  dateOfBirth: string;
  joiningDate: string;
  reportingDate: string;
  gender: string;
  maritalStatus: string;
  personalEmail: string;
  officeEmail: string;
  phoneNo: string; // Primary phone from API
  alternateNo: string; // Alternative phone
  address: string;
  panNo: string;
  aadharNo: string;
  uanNo: string;
  esicNo: string;
  ctc: number;
  attendanceBonus: number;
  pf: string;
  bankName: string;
  bankAccountNo: string;
  ifscNo: string;
  officeLocation: string;
  shiftTime: string;
  department: string; // Resolved department name
  designation: string; // Resolved designation name
  role: string;
  subRole: string; // Resolved sub-role name
  isActive: boolean;
  approverCode: string;
  secondApproverCode: string;
  resignedStartDate: string;
  resignedEndDate: string;

  // Additional fields for API integration
  departmentId: string;
  designationId: string;
  subRoleId: string;
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgbDropdownModule,
    NgbTooltipModule,
    NgxDatatableModule,
    FeatherIconDirective
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {

  // Column mode for ngx-datatable
  ColumnMode = ColumnMode;

  // Search and filter properties
  searchControl = new FormControl('');
  departmentFilter = new FormControl('');
  statusFilter = new FormControl('');

  // Data arrays
  originalData: ProfileEmployeeData[] = [];
  filteredData: ProfileEmployeeData[] = [];

  // BizzCorp API data
  currentEmployee: BizzCorpEmployee | null = null;
  departments: Department[] = [];
  designations: Designation[] = [];
  bizzCorpRoles: BizzCorpRole[] = [];

  // Role-related properties (legacy)
  roles: Role[] = [];
  selectedSubRole = new FormControl('');
  selectedRoleName = '';
  showRoleNameField = false;

  // Loading and error states
  loading = false;
  error: EmployeeServiceError | null = null;

  // Authentication states
  currentUser: User | null = null;
  currentEmployeeId: string | null = null;
  authenticationLoading = false;
  authenticationError: string | null = null;

  // Dropdown options
  departmentOptions: Department[] = [];
  designationOptions: Designation[] = [];
  roleOptions = ['U','UAE','UEX'];
  subRoleOptions: string[] = [];

  bloodGroups = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
  genders = ['Male', 'Female', 'Other'];
  maritalStatuses = ['Single', 'Married', 'Divorced', 'Widowed'];
  officeLocations = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Pune'];

  constructor(
    private roleService: RoleService,
    private employeeService: EmployeeService,
    private authService: AuthService,
    private router: Router
  ) {}

  // Profile table data (will be populated from API)
  profileTableData: ProfileEmployeeData[] = [];

  // Additional profile data for comprehensive display
  profileData: {
    // Personal Information
    bloodGroup: string;
    dateOfBirth: string;
    gender: string;
    maritalStatus: string;
    personalEmail: string;
    phoneNo: string;
    alternateNo: string;
    address: string;

    // Employment Information
    joiningDate: string;
    reportingDate: string;
    shiftTime: string;
    officeLocation: string;
    approverCode: string;
    secondApproverCode: string;

    // Financial Information
    ctc: number | null;
    attendanceBonus: number | null;

    // Banking Information
    bankName: string;
    bankAccountNo: string;
    ifscNo: string;
    panNo: string;
    aadharNo: string;
    uanNo: string;
    esicNo: string;
    pf: string;

    // Status Information
    isActive: boolean;
    resignedStartDate: string;
    resignedEndDate: string;
  } = {
    // Initialize with default values
    bloodGroup: '',
    dateOfBirth: '',
    gender: '',
    maritalStatus: '',
    personalEmail: '',
    phoneNo: '',
    alternateNo: '',
    address: '',
    joiningDate: '',
    reportingDate: '',
    shiftTime: '',
    officeLocation: '',
    approverCode: '',
    secondApproverCode: '',
    ctc: null,
    attendanceBonus: null,
    bankName: '',
    bankAccountNo: '',
    ifscNo: '',
    panNo: '',
    aadharNo: '',
    uanNo: '',
    esicNo: '',
    pf: '',
    isActive: true,
    resignedStartDate: '',
    resignedEndDate: ''
  };

  ngOnInit(): void {
    // First, get the current employee ID, then load profile data
    this.initializeCurrentEmployee();

    // Set up search functionality with debouncing
    this.searchControl.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.applyFilters();
    });

    // Set up department filter
    this.departmentFilter.valueChanges.subscribe(() => {
      this.applyFilters();
    });

    // Set up status filter
    this.statusFilter.valueChanges.subscribe(() => {
      this.applyFilters();
    });

    // Set up subrole change handler
    this.selectedSubRole.valueChanges.subscribe((subRole) => {
      this.onSubRoleChange(subRole);
    });

    // Skip loading roles for normal employees to avoid 403 errors
    // Roles are only needed for admin/HR functions, not for viewing own profile
    // this.loadRoles(); // Commented out to prevent unnecessary API calls

    console.log('Profile component initialized');
  }

  /**
   * Initialize current employee by getting employee ID from authentication
   */
  private initializeCurrentEmployee(): void {
    this.authenticationLoading = true;
    this.authenticationError = null;

    console.log('🔍 ProfileComponent: Initializing current employee...');

    // Check if user is authenticated
    if (!this.authService.isLoggedIn()) {
      console.error('❌ ProfileComponent: User is not authenticated');
      this.authenticationError = 'You must be logged in to view your profile.';
      this.authenticationLoading = false;
      this.router.navigate(['/auth/login']);
      return;
    }

    // Get current user
    this.currentUser = this.authService.currentUserValue;
    if (!this.currentUser) {
      console.error('❌ ProfileComponent: No current user found');
      this.authenticationError = 'Unable to determine current user. Please log in again.';
      this.authenticationLoading = false;
      this.router.navigate(['/auth/login']);
      return;
    }

    console.log('✅ ProfileComponent: Current user found:', {
      email: this.currentUser.email,
      name: this.currentUser.name,
      id: this.currentUser.id
    });

    // Get current employee ID using the same method as Leave Service
    this.getCurrentEmployeeId().subscribe({
      next: (employeeId) => {
        this.authenticationLoading = false;
        if (employeeId) {
          this.currentEmployeeId = employeeId;
          console.log('✅ ProfileComponent: Current employee ID resolved:', employeeId);
          // Now load the profile data
          this.loadBizzCorpProfileData();
        } else {
          this.authenticationError = 'Unable to find your employee record. Please contact support.';
          console.error('❌ ProfileComponent: Could not resolve employee ID');
        }
      },
      error: (error) => {
        this.authenticationLoading = false;
        this.authenticationError = 'Error loading employee information. Please try again.';
        console.error('❌ ProfileComponent: Error getting employee ID:', error);
      }
    });
  }

  /**
   * Get current user's employee ID using the same method as Leave Service
   * Step 1: Get current user's email from authentication
   * Step 2: Find employee record where office_email matches user's email
   * Step 3: Extract employee.id from the matched record
   * @returns Observable of employee ID
   */
  private getCurrentEmployeeId(): Observable<string | null> {
    console.log('🔍 ProfileComponent: Starting employee_id retrieval...');

    // Step 1: Get current authenticated user
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      console.error('❌ ProfileComponent: No current user found in authentication');
      return of(null);
    }

    console.log('🔍 ProfileComponent: Current user email:', currentUser.email);

    // Step 2: Get all employees and find the current user by email
    return this.employeeService.getAllEmployees().pipe(
      map((response: any) => {
        console.log('🔍 ProfileComponent: Employee API response:', response);

        let employees: any[] = [];
        if (response && typeof response === 'object' && response.success && response.data && Array.isArray(response.data)) {
          employees = response.data;
        } else if (Array.isArray(response)) {
          employees = response;
        }

        console.log('🔍 ProfileComponent: Searching for current user in employees:', employees.length, 'employees');

        // Try to find employee by email first, then by employee code (same logic as apply-leave)
        let currentEmployee = employees.find(emp =>
          emp.office_email === currentUser.email ||
          emp.personal_email === currentUser.email
        );

        // If not found by email, try by employee code (if user ID matches employee code)
        if (!currentEmployee) {
          currentEmployee = employees.find(emp =>
            emp.employee_code === currentUser.id.toString()
          );
        }

        if (currentEmployee) {
          // Ensure we have a proper UUID string (EXACT same as apply-leave)
          const employeeUuid = currentEmployee.id ? currentEmployee.id.toString() : null;
          console.log('✅ ProfileComponent: Found current employee UUID:', employeeUuid);
          console.log('✅ ProfileComponent: Employee UUID type:', typeof employeeUuid);
          console.log('✅ ProfileComponent: Employee details:', currentEmployee);
          return employeeUuid;
        } else {
          console.error('❌ ProfileComponent: Could not find current employee in employee list');
          console.log('❌ ProfileComponent: Current user email:', currentUser.email);
          console.log('❌ ProfileComponent: Current user ID:', currentUser.id);
          console.log('❌ ProfileComponent: Available employees:', employees.map(emp => ({
            id: emp.id,
            employee_code: emp.employee_code,
            office_email: emp.office_email,
            personal_email: emp.personal_email,
            name: `${emp.first_name} ${emp.last_name}`
          })));

          // Final fallback: Use current user's ID if it's a valid UUID (same as apply-leave)
          const userUuid = currentUser.id;
          if (userUuid && this.isValidUUID(userUuid)) {
            console.log('🔄 ProfileComponent: Using current user UUID as final fallback:', userUuid);
            console.warn('⚠️ ProfileComponent: This fallback might cause "Employee not found" errors if user ID doesn\'t exist in employee table');
            return userUuid;
          } else {
            console.error('❌ ProfileComponent: Current user ID is not a valid UUID:', userUuid);
            return null;
          }
        }
      }),
      catchError(error => {
        console.error('❌ ProfileComponent: Error fetching employees from API:', error);

        // Error fallback: Try to use current user's ID if it's a valid UUID
        const currentUser = this.authService.currentUserValue;
        const userUuid = currentUser?.id;
        if (userUuid && this.isValidUUID(userUuid)) {
          console.log('🔄 ProfileComponent: Using current user UUID as error fallback:', userUuid);
          console.warn('⚠️ ProfileComponent: This fallback might cause "Employee not found" errors');
          return of(userUuid);
        } else {
          console.error('❌ ProfileComponent: Current user ID is not a valid UUID:', userUuid);
          return of(null);
        }
      })
    );
  }

  /**
   * Check if a string is a valid UUID format
   * @param uuid String to check
   * @returns True if valid UUID format
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Load BizzCorp employee profile data and related master data
   */
  private loadBizzCorpProfileData(): void {
    if (!this.currentEmployeeId) {
      console.error('❌ ProfileComponent: No employee ID available for loading profile data');
      this.error = {
        code: 'NO_EMPLOYEE_ID',
        message: 'Unable to determine your employee ID. Please contact support.',
        timestamp: new Date().toISOString()
      };
      return;
    }

    this.loading = true;
    this.error = null;

    console.log('Loading BizzCorp profile data for employee:', this.currentEmployeeId);

    // Load all required data in parallel
    forkJoin({
      employee: this.employeeService.getBizzCorpEmployeeProfile(this.currentEmployeeId),
      departments: this.employeeService.getDepartmentsMasterData(),
      designations: this.employeeService.getDesignationsMasterData()
    }).pipe(
      finalize(() => {
        this.loading = false;
      }),
      catchError(error => {
        console.error('Error loading BizzCorp profile data:', error);
        this.error = error;
        return of(null);
      })
    ).subscribe(result => {
      if (result) {
        this.currentEmployee = result.employee;
        this.departments = result.departments;
        this.designations = result.designations;

        // Update dropdown options
        this.departmentOptions = result.departments;
        this.designationOptions = result.designations;

        // Convert to profile display format and populate table
        this.convertAndPopulateProfileData(result.employee);

        // Load additional related data
        this.loadRelatedData(result.employee);

        console.log('BizzCorp profile data loaded successfully:', {
          employee: result.employee,
          departments: result.departments.length,
          designations: result.designations.length
        });
      }
    });
  }

  /**
   * Convert BizzCorp employee data to profile display format
   */
  private convertAndPopulateProfileData(employee: BizzCorpEmployee): void {
    const profileData: ProfileEmployeeData = {
      id: employee.id,
      employeeCode: employee.employee_code,
      firstName: employee.first_name,
      middleName: employee.middle_name || '',
      lastName: employee.last_name,
      bloodGroup: employee.blood_group || '',
      dateOfBirth: employee.date_of_birth || '',
      joiningDate: employee.joining_date || '',
      reportingDate: employee.reporting_date || '',
      gender: employee.gender || '',
      maritalStatus: employee.marital_status || '',
      personalEmail: employee.personal_email || '',
      officeEmail: employee.office_email || '',
      phoneNo: employee.phone_no || '', // Primary phone from API
      alternateNo: employee.alternet_no || '', // Alternative phone
      address: employee.address || '',
      panNo: employee.pan_no || '',
      aadharNo: employee.aadhar_no || '',
      uanNo: employee.uan_no || '',
      esicNo: employee.esic_no || '',
      ctc: employee.ctc || 0,
      attendanceBonus: employee.attendance_bonus || 0,
      pf: employee.pf ? employee.pf.toString() : '',
      bankName: employee.bank_name || '',
      bankAccountNo: employee.bank_account_no || '',
      ifscNo: employee.ifsc_no || '',
      officeLocation: employee.office_location || '',
      shiftTime: employee.shift_time || '',
      department: employee.department_id ? '' : 'Not Assigned', // Will be resolved from department_id if exists
      designation: employee.designation_id ? '' : 'Not Assigned', // Will be resolved from designation_id if exists
      role: employee.role || '',
      subRole: 'Loading...', // Will be resolved from User Roles API
      isActive: ((employee as any).is_active || (employee as any).user_active) !== false, // API may return either field
      approverCode: employee.approver_code || '',
      secondApproverCode: employee.second_approver_code || '',
      resignedStartDate: employee.resigned_stared_date || '',
      resignedEndDate: employee.resigned_end_date || '',

      // Store IDs for reference
      departmentId: employee.department_id || '',
      designationId: employee.designation_id || '',
      subRoleId: employee.sub_role_id || ''
    };

    // Set as single employee data for profile view
    this.profileTableData = [profileData];
    this.originalData = [profileData];
    this.filteredData = [profileData];

    // Also populate the comprehensive profile data object
    this.profileData = {
      // Personal Information
      bloodGroup: employee.blood_group || 'Not Provided',
      dateOfBirth: employee.date_of_birth || '',
      gender: employee.gender || 'Not Provided',
      maritalStatus: employee.marital_status || 'Not Provided',
      personalEmail: employee.personal_email || 'Not Provided',
      phoneNo: employee.phone_no || 'Not Provided',
      alternateNo: employee.alternet_no || 'Not Provided',
      address: employee.address || 'Not Provided',

      // Employment Information
      joiningDate: employee.joining_date || '',
      reportingDate: employee.reporting_date || '',
      shiftTime: employee.shift_time || 'Not Provided',
      officeLocation: employee.office_location || 'Not Provided',
      approverCode: employee.approver_code || 'Not Provided',
      secondApproverCode: employee.second_approver_code || 'Not Provided',

      // Financial Information
      ctc: employee.ctc || null,
      attendanceBonus: employee.attendance_bonus || null,

      // Banking Information
      bankName: employee.bank_name || 'Not Provided',
      bankAccountNo: employee.bank_account_no || 'Not Provided',
      ifscNo: employee.ifsc_no || 'Not Provided',
      panNo: employee.pan_no || 'Not Provided',
      aadharNo: employee.aadhar_no || 'Not Provided',
      uanNo: employee.uan_no || 'Not Provided',
      esicNo: employee.esic_no || 'Not Provided',
      pf: employee.pf ? employee.pf.toString() : 'Not Provided',

      // Status Information
      isActive: ((employee as any).is_active || (employee as any).user_active) !== false, // API may return either field
      resignedStartDate: employee.resigned_stared_date || '',
      resignedEndDate: employee.resigned_end_date || ''
    };
  }

  /**
   * Load related data (department name, designation name, sub-role name)
   * Handles null/undefined IDs gracefully by setting "Not Assigned"
   */
  private loadRelatedData(employee: BizzCorpEmployee): void {
    console.log('🔍 ProfileComponent: Loading related data for employee:', {
      department_id: employee.department_id,
      designation_id: employee.designation_id,
      sub_role_id: employee.sub_role_id
    });

    const relatedDataCalls: any[] = [];

    // Resolve department name
    if (employee.department_id && employee.department_id.trim() !== '') {
      console.log('🔍 ProfileComponent: Resolving department name for ID:', employee.department_id);
      const department = this.departments.find(d => d.id === employee.department_id);
      if (department) {
        console.log('✅ ProfileComponent: Department found:', department.name);
        this.updateProfileField('department', department.name);
      } else {
        console.warn('⚠️ ProfileComponent: Department not found in cached departments for ID:', employee.department_id);
        this.updateProfileField('department', 'Department Not Found');
      }
    } else {
      console.log('ℹ️ ProfileComponent: No department_id provided, setting to "Not Assigned"');
      this.updateProfileField('department', 'Not Assigned');
    }

    // Resolve designation name
    if (employee.designation_id && employee.designation_id.trim() !== '') {
      console.log('🔍 ProfileComponent: Fetching designation details for ID:', employee.designation_id);
      relatedDataCalls.push(
        this.employeeService.getDesignationById(employee.designation_id).pipe(
          catchError(error => {
            console.warn('❌ ProfileComponent: Could not load designation details:', error);
            this.updateProfileField('designation', 'Designation Not Found');
            return of(null);
          })
        )
      );
    } else {
      console.log('ℹ️ ProfileComponent: No designation_id provided, setting to "Not Assigned"');
      this.updateProfileField('designation', 'Not Assigned');
    }

    // Handle sub-role resolution with fallback for API access issues
    console.log('🔍 ProfileComponent: Resolving sub-role information');

    // Try to resolve sub-role, but handle 403/permission errors gracefully
    if (employee.sub_role_id && employee.sub_role_id.trim() !== '') {
      console.log('🔍 ProfileComponent: Attempting to resolve sub_role_id:', employee.sub_role_id);

      relatedDataCalls.push(
        this.employeeService.getUserRoles().pipe(
          map(userRoles => {
            console.log('🔍 ProfileComponent: User roles received:', userRoles);

            if (userRoles && userRoles.length > 0) {
              const primaryRole = userRoles[0];
              console.log('✅ ProfileComponent: Primary role found:', primaryRole.name);
              this.updateProfileField('subRole', primaryRole.name);
              return primaryRole;
            } else {
              console.error('❌ ProfileComponent: No roles found in user roles response - role is required');
              this.updateProfileField('subRole', 'Role not assigned');
              return null;
            }
          }),
          catchError(error => {
            console.error('❌ ProfileComponent: User roles API failed - role is required:', error);
            this.updateProfileField('subRole', 'Role not assigned');
            return of(null);
          })
        )
      );
    } else {
      console.log('ℹ️ ProfileComponent: No sub_role_id provided');
      this.updateProfileField('subRole', 'Role not assigned');
    }

    // Execute related data calls only if there are valid IDs to resolve
    if (relatedDataCalls.length > 0) {
      console.log('🔄 ProfileComponent: Executing', relatedDataCalls.length, 'related data API calls');
      forkJoin(relatedDataCalls).subscribe(results => {
        let resultIndex = 0;

        // Update designation name if we made the API call
        if (employee.designation_id && employee.designation_id.trim() !== '') {
          const designation = results[resultIndex] as Designation;
          if (designation && designation.name) {
            console.log('✅ ProfileComponent: Designation resolved:', designation.name);
            this.updateProfileField('designation', designation.name);
          } else {
            console.warn('⚠️ ProfileComponent: Designation API returned null or invalid data');
            this.updateProfileField('designation', 'Designation Not Found');
          }
          resultIndex++;
        }

        // Sub-role resolution is now handled within the getUserRoles() call above
        // No additional processing needed here since updateProfileField() is called in the map operator
        console.log('✅ ProfileComponent: All related data resolution completed');
      });
    } else {
      console.log('ℹ️ ProfileComponent: No API calls needed, all related data set to "Not Assigned"');
    }
  }

  /**
   * Update a specific field in the profile data
   */
  private updateProfileField(field: keyof ProfileEmployeeData, value: string): void {
    if (this.profileTableData.length > 0) {
      (this.profileTableData[0] as any)[field] = value;
      (this.originalData[0] as any)[field] = value;
      (this.filteredData[0] as any)[field] = value;
    }
  }

  /**
   * Retry loading profile data
   */
  retryLoadProfile(): void {
    if (this.currentEmployeeId) {
      // If we have employee ID, just reload profile data
      this.loadBizzCorpProfileData();
    } else {
      // If no employee ID, reinitialize from authentication
      this.initializeCurrentEmployee();
    }
  }

  /**
   * Retry authentication and employee ID resolution
   */
  retryAuthentication(): void {
    this.initializeCurrentEmployee();
  }

  /**
   * Get department name by ID
   * Returns "Not Assigned" for null/undefined/empty IDs
   */
  getDepartmentName(departmentId: string): string {
    if (!departmentId || departmentId.trim() === '') {
      return 'Not Assigned';
    }
    if (!this.departments || this.departments.length === 0) {
      return 'Loading...';
    }
    const department = this.departments.find(d => d.id === departmentId);
    return department ? department.name : 'Department Not Found';
  }

  /**
   * Get designation name by ID (from cached designations)
   * Returns "Not Assigned" for null/undefined/empty IDs
   */
  getDesignationName(designationId: string): string {
    if (!designationId || designationId.trim() === '') {
      return 'Not Assigned';
    }
    if (!this.designations || this.designations.length === 0) {
      return 'Loading...';
    }
    const designation = this.designations.find(d => d.id === designationId);
    return designation ? designation.name : 'Designation Not Found';
  }

  /**
   * Get role name by role name (not ID) from current profile data
   * Returns "Not Assigned" for null/undefined/empty role names
   */
  getRoleName(roleName: string): string {
    if (!roleName || roleName.trim() === '') {
      return 'Not Assigned';
    }
    // Return the resolved role name from profile data
    if (this.profileTableData.length > 0) {
      return this.profileTableData[0].subRole || 'Loading...';
    }
    return 'Loading...';
  }

  /**
   * Get current user's role information from User Roles API
   * This method can be used to fetch fresh role data if needed
   */
  refreshUserRoles(): void {
    console.log('🔄 ProfileComponent: Refreshing user roles...');
    this.employeeService.getUserRoles().subscribe({
      next: (userRoles) => {
        console.log('🔍 ProfileComponent: User roles refreshed:', userRoles);

        if (userRoles && userRoles.length > 0) {
          const primaryRole = userRoles[0]; // Get the first/primary role
          console.log('✅ ProfileComponent: Primary role refreshed:', primaryRole.name, primaryRole.description);

          // Display only role name (no description in parentheses)
          this.updateProfileField('subRole', primaryRole.name);
        } else {
          console.error('❌ ProfileComponent: No roles found during refresh - role is required');
          this.updateProfileField('subRole', 'Role not assigned');
        }
      },
      error: (error) => {
        console.error('❌ ProfileComponent: Error refreshing user roles - role is required:', error);
        this.updateProfileField('subRole', 'Role not assigned');
      }
    });
  }

  /**
   * Get employee full name
   */
  getEmployeeFullName(): string {
    if (!this.currentEmployee) return '';
    const parts = [
      this.currentEmployee.first_name,
      this.currentEmployee.middle_name,
      this.currentEmployee.last_name
    ].filter(part => part && part.trim());
    return parts.join(' ');
  }

  /**
   * Format date for display (converts ISO date to readable format)
   */
  formatDate(dateString: string): string {
    if (!dateString) {
      return 'Not Provided';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', dateString, error);
      return 'Invalid Date';
    }
  }

  /**
   * Format currency for display (Indian Rupees)
   */
  formatCurrency(amount: number | null): string {
    if (amount === null || amount === undefined) {
      return 'Not Provided';
    }

    try {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    } catch (error) {
      console.warn('Error formatting currency:', amount, error);
      return `₹${amount.toLocaleString('en-IN')}`;
    }
  }

  /**
   * Format phone number for display
   */
  formatPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber || phoneNumber.trim() === '') {
      return 'Not Provided';
    }

    // Remove any non-digit characters for formatting
    const digits = phoneNumber.replace(/\D/g, '');

    // Format Indian mobile numbers (10 digits)
    if (digits.length === 10) {
      return `+91 ${digits.slice(0, 5)} ${digits.slice(5)}`;
    }

    // Return original if not standard format
    return phoneNumber;
  }

  /**
   * Get display value with fallback
   */
  getDisplayValue(value: string | null | undefined, fallback: string = 'Not Provided'): string {
    return (value && value.trim() !== '') ? value : fallback;
  }

  // Apply search and filter logic
  applyFilters(): void {
    let filtered = [...this.originalData];

    // Apply search filter
    const searchTerm = this.searchControl.value?.toLowerCase().trim();
    if (searchTerm) {
      filtered = filtered.filter(employee =>
        employee.employeeCode.toLowerCase().includes(searchTerm) ||
        employee.firstName.toLowerCase().includes(searchTerm) ||
        employee.middleName.toLowerCase().includes(searchTerm) ||
        employee.lastName.toLowerCase().includes(searchTerm) ||
        employee.personalEmail.toLowerCase().includes(searchTerm) ||
        employee.officeEmail.toLowerCase().includes(searchTerm) ||
        employee.phoneNo.toLowerCase().includes(searchTerm) ||
        employee.alternateNo.toLowerCase().includes(searchTerm) ||
        employee.department.toLowerCase().includes(searchTerm) ||
        employee.designation.toLowerCase().includes(searchTerm) ||
        employee.role.toLowerCase().includes(searchTerm) ||
        employee.subRole.toLowerCase().includes(searchTerm) ||
        employee.officeLocation.toLowerCase().includes(searchTerm) ||
        employee.bankName.toLowerCase().includes(searchTerm) ||
        employee.panNo.toLowerCase().includes(searchTerm)
      );
    }

    // Apply department filter
    const departmentFilter = this.departmentFilter.value;
    if (departmentFilter) {
      filtered = filtered.filter(employee => employee.department === departmentFilter);
    }

    // Apply status filter
    const statusFilter = this.statusFilter.value;
    if (statusFilter) {
      if (statusFilter === 'Active') {
        filtered = filtered.filter(employee => employee.isActive === true);
      } else if (statusFilter === 'Inactive') {
        filtered = filtered.filter(employee => employee.isActive === false);
      }
    }

    // Update filtered data
    this.filteredData = filtered;
    this.profileTableData = filtered;

    console.log(`Filtered ${filtered.length} records from ${this.originalData.length} total records`);
  }

  // Reset all filters
  resetFilters(): void {
    this.searchControl.setValue('');
    this.departmentFilter.setValue('');
    this.statusFilter.setValue('');
    this.applyFilters();
  }

  // Method to handle row actions
  onRowAction(action: string, row: ProfileEmployeeData): void {
    console.log(`${action} action for:`, row);

    switch(action) {
      case 'view':
        this.viewEmployee(row);
        break;
      case 'edit':
        this.editEmployee(row);
        break;
      case 'delete':
        this.deleteEmployee(row);
        break;
      case 'profile':
        this.viewProfile(row);
        break;
      case 'permissions':
        this.managePermissions(row);
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  // Individual action methods
  private viewEmployee(employee: ProfileEmployeeData): void {
    console.log('Viewing employee:', `${employee.firstName} ${employee.lastName}`);
    // Add view logic here
  }

  private editEmployee(employee: ProfileEmployeeData): void {
    console.log('Editing employee:', `${employee.firstName} ${employee.lastName}`);
    // Add edit logic here
  }

  private deleteEmployee(employee: ProfileEmployeeData): void {
    console.log('Deleting employee:', `${employee.firstName} ${employee.lastName}`);
    // Add delete logic here
  }

  private viewProfile(employee: ProfileEmployeeData): void {
    console.log('Viewing profile for:', `${employee.firstName} ${employee.lastName}`);
    // Add profile view logic here
  }

  private managePermissions(employee: ProfileEmployeeData): void {
    console.log('Managing permissions for:', `${employee.firstName} ${employee.lastName}`);
    // Add permissions logic here
  }

  // Load roles from API with fallback for permission errors
  loadRoles(): void {
    console.log('🔄 Loading roles from API...');

    this.roleService.getAllRoles().subscribe({
      next: (response: any) => {
        console.log('✅ Roles loaded from API:', response);

        // Handle different response formats
        if (response && response.success && response.data) {
          this.roles = response.data;
        } else if (Array.isArray(response)) {
          this.roles = response;
        } else {
          console.warn('⚠️ Unexpected roles response format:', response);
          this.roles = [];
        }

        // Extract role names for subRoleOptions
        this.subRoleOptions = this.roles
          .filter(role => role.name && role.name.trim() !== '')
          .map(role => role.name);

        console.log('📋 Processed roles:', this.roles);
        console.log('📋 SubRole options from API:', this.subRoleOptions);
      },
      error: (error) => {
        console.error('❌ Failed to load roles - roles are required:', error);
        this.roles = [];
        this.subRoleOptions = []; // No fallback roles
        console.log('📋 No roles available - API is required');
      }
    });
  }

  // Handle subrole selection change
  onSubRoleChange(subRole: string | null): void {
    console.log('🔄 Subrole changed to:', subRole);
    console.log('📋 Available roles:', this.roles);

    if (subRole && subRole.trim() !== '') {
      // Show role name field when subrole is selected
      this.showRoleNameField = true;

      // Since subRole is now the actual role name from API, find exact match
      const matchingRole = this.roles.find(role =>
        role.name && role.name === subRole
      );

      if (matchingRole) {
        this.selectedRoleName = matchingRole.name;
        console.log('✅ Found exact role match:', matchingRole.name);
        console.log('📋 Role details:', matchingRole);
      } else {
        this.selectedRoleName = subRole; // Use the selected value as fallback
        console.log('⚠️ No exact match found, using selected value:', subRole);
      }
    } else {
      // Hide role name field when no subrole is selected
      this.showRoleNameField = false;
      this.selectedRoleName = '';
      console.log('🔄 Hiding role name field');
    }
  }
}
