import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'number' | 'date' | 'select';
  required: boolean;
  value: any;
  options?: { value: string; label: string }[];
}

@Component({
  selector: 'app-generic-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-edit-3 me-2"></i>
        {{ title }}
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
    </div>
    <div class="modal-body">
      <form #genericForm="ngForm">
        <div class="row">
          <ng-container *ngFor="let field of formFields">
            <div [ngClass]="field.type === 'textarea' ? 'col-12 mb-3' : 'col-12 col-md-6 col-lg-4 mb-3'">
              <label [for]="field.name" class="form-label">
                {{ field.label }}
                <span class="text-danger" *ngIf="field.required">*</span>
              </label>

              <!-- Text Input -->
              <input *ngIf="field.type === 'text'"
                     type="text"
                     class="form-control"
                     [id]="field.name"
                     [name]="field.name"
                     [(ngModel)]="formData[field.name]"
                     [required]="field.required">

              <!-- Number Input -->
              <input *ngIf="field.type === 'number'"
                     type="number"
                     class="form-control"
                     [id]="field.name"
                     [name]="field.name"
                     [(ngModel)]="formData[field.name]"
                     [required]="field.required">

              <!-- Date Input -->
              <input *ngIf="field.type === 'date'"
                     type="date"
                     class="form-control"
                     [id]="field.name"
                     [name]="field.name"
                     [(ngModel)]="formData[field.name]"
                     [required]="field.required">

              <!-- Textarea Input -->
              <textarea *ngIf="field.type === 'textarea'"
                        class="form-control"
                        [id]="field.name"
                        [name]="field.name"
                        rows="3"
                        [(ngModel)]="formData[field.name]"
                        [required]="field.required"></textarea>

              <!-- Select Input -->
              <select *ngIf="field.type === 'select'"
                      class="form-select"
                      [id]="field.name"
                      [name]="field.name"
                      [(ngModel)]="formData[field.name]"
                      [required]="field.required">
                <option value="" disabled>Select {{ field.label }}</option>
                <option *ngFor="let option of field.options" [value]="option.value">{{ option.label }}</option>
              </select>
            </div>
          </ng-container>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="cancel()">
        <i class="feather icon-x me-1"></i>
        Cancel
      </button>
      <button type="button" class="btn btn-primary" [disabled]="genericForm.invalid" (click)="saveChanges()">
        <i class="feather icon-save me-1"></i>
        Save Changes
      </button>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }

    .modal-header {
      background-color: #3F828B;
      color: white;
    }

    .modal-title {
      margin-bottom: 0;
    }

    .form-label {
      font-weight: 500;
    }

    // Hide spinners on number inputs
    input[type=number]::-webkit-inner-spin-button,
    input[type=number]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    input[type=number] {
      -moz-appearance: textfield;
    }

    .btn-primary {
      background-color: #df5316;
      border-color: #df5316;

      &:hover, &:focus, &:active {
        background-color: darken(#df5316, 10%);
        border-color: darken(#df5316, 10%);
      }
    }

    .btn-secondary {
      background-color: #E25516;
      border-color: #E25516;

      &:hover, &:focus, &:active {
        background-color: darken(#E25516, 10%);
        border-color: darken(#E25516, 10%);
      }
    }
  `]
})
export class GenericModalComponent {
  @Input() title: string = 'Form';
  @Input() formFields: FormField[] = [];

  formData: { [key: string]: any } = {};

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit() {
    // Initialize form data from field values
    this.formFields.forEach(field => {
      this.formData[field.name] = field.value;
    });
  }

  saveChanges() {
    this.activeModal.close(this.formData);
  }

  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
