import { Injectable } from '@angular/core';

export interface ErrorMapping {
  code?: number;
  message: string;
  title?: string;
  type?: 'error' | 'warning' | 'info';
}

@Injectable({
  providedIn: 'root'
})
export class ErrorMessageService {

  private errorMappings: { [key: string]: ErrorMapping } = {
    // Network errors
    'network_error': {
      message: '',
      title: '',
      type: 'error'
    },
    
    // Authentication errors
    'auth_failed': {
      message: 'Your session has expired. Please log in again.',
      title: 'Authentication Required',
      type: 'warning'
    },
    
    // File upload errors
    'file_too_large': {
      message: 'The file you selected is too large. Please choose a file smaller than 10MB.',
      title: 'File Too Large',
      type: 'error'
    },
    'invalid_file_format': {
      message: 'Please select a valid Excel (.xlsx, .xls) or CSV file.',
      title: 'Invalid File Format',
      type: 'error'
    },
    'file_processing_error': {
      message: 'There was an error processing your file. Please check the format and try again.',
      title: 'File Processing Error',
      type: 'error'
    },
    
    // Validation errors
    'validation_error': {
      message: 'Please check your data and correct any errors before submitting.',
      title: 'Validation Error',
      type: 'warning'
    },
    'duplicate_entry': {
      message: 'This record already exists. Please check for duplicates.',
      title: 'Duplicate Entry',
      type: 'warning'
    },
    
    // Permission errors
    'access_denied': {
      message: 'You do not have permission to perform this action.',
      title: 'Access Denied',
      type: 'warning'
    },
    
    // Server errors
    'server_error': {
      message: 'Something went wrong on our end. Please try again in a few moments.',
      title: 'Server Error',
      type: 'error'
    },
    'service_unavailable': {
      message: 'The service is temporarily unavailable. Please try again later.',
      title: 'Service Unavailable',
      type: 'error'
    },
    
    // Default fallback
    'unknown_error': {
      message: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
      title: 'Unexpected Error',
      type: 'error'
    }
  };

  private statusCodeMappings: { [key: number]: ErrorMapping } = {
    // 4xx Client Errors
    400: {
      message: 'The request contains invalid data. Please check your input and try again.',
      title: 'Bad Request',
      type: 'warning'
    },
    401: {
      message: 'Your session has expired. Please log in again.',
      title: 'Authentication Required',
      type: 'warning'
    },
    402: {
      message: 'Payment is required to access this resource.',
      title: 'Payment Required',
      type: 'warning'
    },
    403: {
      message: 'You do not have permission to perform this action. Please contact your administrator.',
      title: 'Access Denied',
      type: 'warning'
    },
    404: {
      message: '',
      title: '',
      type: 'warning'
    },
    405: {
      message: 'This operation is not allowed. Please try a different action.',
      title: 'Method Not Allowed',
      type: 'warning'
    },
    406: {
      message: 'The request format is not acceptable. Please check your data format.',
      title: 'Not Acceptable',
      type: 'warning'
    },
    407: {
      message: 'Proxy authentication is required.',
      title: 'Proxy Authentication Required',
      type: 'warning'
    },
    408: {
      message: 'The request timed out. Please try again.',
      title: 'Request Timeout',
      type: 'warning'
    },
    409: {
      message: 'There is a conflict with the current state. Please refresh and try again.',
      title: 'Conflict',
      type: 'warning'
    },
    410: {
      message: 'This resource is no longer available.',
      title: 'Gone',
      type: 'warning'
    },
    411: {
      message: 'Content length is required for this request.',
      title: 'Length Required',
      type: 'warning'
    },
    412: {
      message: 'Precondition failed. Please check your request conditions.',
      title: 'Precondition Failed',
      type: 'warning'
    },
    413: {
      message: 'The file you selected is too large. Please choose a file smaller than 10MB.',
      title: 'File Too Large',
      type: 'error'
    },
    414: {
      message: 'The request URL is too long. Please shorten your request.',
      title: 'URI Too Long',
      type: 'warning'
    },
    415: {
      message: 'The file format is not supported. Please use a different file type.',
      title: 'Unsupported Media Type',
      type: 'warning'
    },
    416: {
      message: 'The requested range is not satisfiable.',
      title: 'Range Not Satisfiable',
      type: 'warning'
    },
    417: {
      message: 'The server cannot meet the requirements of the request.',
      title: 'Expectation Failed',
      type: 'warning'
    },
    418: {
      message: 'I\'m a teapot! This is an unusual error.',
      title: 'I\'m a Teapot',
      type: 'info'
    },
    421: {
      message: 'The request was directed at a server that cannot produce a response.',
      title: 'Misdirected Request',
      type: 'warning'
    },
    422: {
      message: 'Please check your data format and correct any validation errors.',
      title: 'Validation Error',
      type: 'warning'
    },
    423: {
      message: 'The resource is currently locked. Please try again later.',
      title: 'Locked',
      type: 'warning'
    },
    424: {
      message: 'The request failed due to failure of a previous request.',
      title: 'Failed Dependency',
      type: 'warning'
    },
    425: {
      message: 'The server is unwilling to risk processing a request that might be replayed.',
      title: 'Too Early',
      type: 'warning'
    },
    426: {
      message: 'The client should switch to a different protocol.',
      title: 'Upgrade Required',
      type: 'warning'
    },
    428: {
      message: 'The request must be conditional. Please include required headers.',
      title: 'Precondition Required',
      type: 'warning'
    },
    429: {
      message: 'Too many requests. Please wait a moment before trying again.',
      title: 'Rate Limited',
      type: 'warning'
    },
    431: {
      message: 'The request headers are too large. Please reduce header size.',
      title: 'Request Header Fields Too Large',
      type: 'warning'
    },
    451: {
      message: 'This content is unavailable for legal reasons.',
      title: 'Unavailable For Legal Reasons',
      type: 'warning'
    },

    // 5xx Server Errors
    500: {
      message: 'Something went wrong on our end. Please try again in a few moments.',
      title: 'Internal Server Error',
      type: 'error'
    },
    501: {
      message: 'This feature is not implemented yet. Please contact support.',
      title: 'Not Implemented',
      type: 'error'
    },
    502: {
      message: 'The server received an invalid response. Please try again later.',
      title: 'Bad Gateway',
      type: 'error'
    },
    503: {
      message: 'The service is temporarily unavailable. Please try again later.',
      title: 'Service Unavailable',
      type: 'error'
    },
    504: {
      message: 'The server request timed out. Please try again.',
      title: 'Gateway Timeout',
      type: 'error'
    },
    505: {
      message: 'The HTTP version is not supported.',
      title: 'HTTP Version Not Supported',
      type: 'error'
    },
    506: {
      message: 'The server has an internal configuration error.',
      title: 'Variant Also Negotiates',
      type: 'error'
    },
    507: {
      message: 'The server is out of storage space.',
      title: 'Insufficient Storage',
      type: 'error'
    },
    508: {
      message: 'The server detected an infinite loop in processing.',
      title: 'Loop Detected',
      type: 'error'
    },
    510: {
      message: 'Further extensions to the request are required.',
      title: 'Not Extended',
      type: 'error'
    },
    511: {
      message: 'Network authentication is required.',
      title: 'Network Authentication Required',
      type: 'error'
    }
  };

  /**
   * Get user-friendly error message based on error response
   */
  getUserFriendlyError(error: any): ErrorMapping {
    console.log('🔍 ErrorMessageService: Processing error:', error);
    console.log('🔍 ErrorMessageService: Error status:', error.status);
    console.log('🔍 ErrorMessageService: Error body:', error.error);

    // Check if it's a network error (status 0)
    if (error.status === 0) {
      console.log('🌐 ErrorMessageService: Network error detected');
      return this.errorMappings['network_error'];
    }

    // PRIORITY 1: Check status code mappings first (most reliable)
    if (error.status && this.statusCodeMappings[error.status]) {
      console.log(`✅ ErrorMessageService: Using status code mapping for ${error.status}`);
      return this.statusCodeMappings[error.status];
    }

    // PRIORITY 2: Try to extract specific error message from response
    let specificError = this.extractSpecificError(error);
    if (specificError) {
      console.log('✅ ErrorMessageService: Using specific error:', specificError);
      return specificError;
    }

    // PRIORITY 3: Handle unknown status codes with generic messages
    if (error.status >= 400 && error.status < 500) {
      console.log('⚠️ ErrorMessageService: Unknown 4xx client error');
      return {
        message: 'There was a problem with your request. Please check your input and try again.',
        title: `Client Error (${error.status})`,
        type: 'warning'
      };
    }

    if (error.status >= 500 && error.status < 600) {
      console.log('❌ ErrorMessageService: Unknown 5xx server error');
      return {
        message: 'Something went wrong on our end. Please try again in a few moments.',
        title: `Server Error (${error.status})`,
        type: 'error'
      };
    }

    // PRIORITY 4: Return default error for everything else
    console.log('❓ ErrorMessageService: Using default error mapping');
    return this.errorMappings['unknown_error'];
  }

  /**
   * Extract specific error messages from API response
   */
  private extractSpecificError(error: any): ErrorMapping | null {
    console.log('ErrorMessageService: Extracting specific error from:', error);

    // Handle FastAPI validation errors first
    const validationError = this.handleFastAPIValidationError(error);
    if (validationError) {
      console.log('ErrorMessageService: FastAPI validation error detected');
      return validationError;
    }

    // Handle bulk upload errors with success/error arrays
    if (error.error && error.error.errors && Array.isArray(error.error.errors)) {
      console.log('ErrorMessageService: Bulk upload error detected');
      const errorCount = error.error.errors.length;
      const successCount = error.error.success ? error.error.success.length : 0;

      if (errorCount > 0) {
        return {
          message: `Upload completed with ${errorCount} error(s) and ${successCount} successful record(s). Please check your data and try again.`,
          title: 'Partial Upload Error',
          type: 'warning'
        };
      }
    }

    // Try to extract any error message from the response
    const errorMessage = this.extractErrorMessage(error);
    console.log('ErrorMessageService: Extracted error message:', errorMessage);

    if (errorMessage) {
      const lowerMessage = errorMessage.toLowerCase();

      // Handle specific permission errors
      if (lowerMessage.includes('not enough permissions') ||
          lowerMessage.includes('permission denied') ||
          lowerMessage.includes('access denied') ||
          lowerMessage.includes('unauthorized') ||
          lowerMessage.includes('forbidden') ||
          lowerMessage.includes('insufficient permissions') ||
          lowerMessage.includes('no permission')) {
        console.log('ErrorMessageService: Permission error detected:', errorMessage);
        return {
          message: 'You do not have permission to perform this action. Please contact your administrator.',
          title: 'Access Denied',
          type: 'warning'
        };
      }

      // Handle file-related errors
      if (lowerMessage.includes('file') && (lowerMessage.includes('large') || lowerMessage.includes('size'))) {
        return this.errorMappings['file_too_large'];
      }

      if (lowerMessage.includes('file') && (lowerMessage.includes('format') || lowerMessage.includes('type'))) {
        return this.errorMappings['invalid_file_format'];
      }

      // Handle duplicate/unique constraint errors
      if (lowerMessage.includes('duplicate') || lowerMessage.includes('already exists') ||
          lowerMessage.includes('unique constraint') || lowerMessage.includes('integrity constraint')) {
        return {
          message: 'This record already exists. Please check for duplicates or use different values.',
          title: 'Duplicate Entry',
          type: 'warning'
        };
      }

      // Handle timeout errors
      if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
        return {
          message: 'The request took too long to complete. Please try again.',
          title: 'Request Timeout',
          type: 'warning'
        };
      }

      // Return cleaned error message
      return {
        message: this.cleanErrorMessage(errorMessage),
        title: 'Error',
        type: 'error'
      };
    }

    console.log('ErrorMessageService: No specific error pattern matched');
    return null;
  }

  /**
   * Clean error messages by removing technical prefixes
   */
  private cleanErrorMessage(message: string): string {
    if (!message) return message;

    // Remove common technical prefixes
    const prefixesToRemove = [
      'Validation errors:',
      'ValidationError:',
      'Error:',
      'Exception:',
      'HTTPException:',
      'FastAPI:',
      'Pydantic:',
      'SQLAlchemy:',
      'Database error:',
      'Internal server error:',
      'Bad request:',
      'Unauthorized:',
      'Forbidden:',
      'Not found:',
      'Conflict:',
      'Unprocessable entity:'
    ];

    let cleanMessage = message;

    // Remove prefixes (case insensitive)
    prefixesToRemove.forEach(prefix => {
      const regex = new RegExp(`^${prefix.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*`, 'i');
      cleanMessage = cleanMessage.replace(regex, '');
    });

    // Remove common technical suffixes
    const suffixesToRemove = [
      ' (422)',
      ' (400)',
      ' (401)',
      ' (403)',
      ' (404)',
      ' (409)',
      ' (500)',
      ' (502)',
      ' (503)'
    ];

    suffixesToRemove.forEach(suffix => {
      if (cleanMessage.endsWith(suffix)) {
        cleanMessage = cleanMessage.substring(0, cleanMessage.length - suffix.length);
      }
    });

    // Clean up extra whitespace and formatting
    cleanMessage = cleanMessage.trim();

    // Remove quotes if the entire message is wrapped in them
    if ((cleanMessage.startsWith('"') && cleanMessage.endsWith('"')) ||
        (cleanMessage.startsWith("'") && cleanMessage.endsWith("'"))) {
      cleanMessage = cleanMessage.slice(1, -1);
    }

    // Capitalize first letter if it's not already
    if (cleanMessage && cleanMessage.length > 0) {
      cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
    }

    // Ensure message ends with a period if it doesn't already end with punctuation
    if (cleanMessage && cleanMessage.length > 0 &&
        !cleanMessage.match(/[.!?]$/)) {
      cleanMessage += '.';
    }

    return cleanMessage;
  }

  /**
   * Handle FastAPI validation errors specifically
   */
  private handleFastAPIValidationError(error: any): ErrorMapping | null {
    // Check for FastAPI validation error structure
    if (error.error && error.error.detail && Array.isArray(error.error.detail)) {
      console.log('ErrorMessageService: Processing FastAPI validation errors');

      const validationErrors = error.error.detail;
      const errorMessages: string[] = [];

      validationErrors.forEach((validationError: any) => {
        if (validationError.msg) {
          // Clean up the field location for user-friendly display
          const field = validationError.loc && validationError.loc.length > 1
            ? validationError.loc[validationError.loc.length - 1]
            : 'field';

          // Create user-friendly field names
          const friendlyField = this.getFriendlyFieldName(field);

          // Create user-friendly error message
          let message = validationError.msg;
          if (validationError.type === 'missing') {
            message = `${friendlyField} is required`;
          } else if (validationError.type === 'string_too_short') {
            message = `${friendlyField} is too short`;
          } else if (validationError.type === 'string_too_long') {
            message = `${friendlyField} is too long`;
          } else if (validationError.type === 'value_error') {
            message = `${friendlyField} has an invalid value`;
          } else {
            // Clean the original message and add field context
            message = this.cleanErrorMessage(message);
            if (!message.toLowerCase().includes(friendlyField.toLowerCase())) {
              message = `${friendlyField}: ${message}`;
            }
          }

          errorMessages.push(message);
        }
      });

      if (errorMessages.length > 0) {
        return {
          message: errorMessages.join('. '),
          title: 'Validation Error',
          type: 'warning'
        };
      }
    }

    return null;
  }

  /**
   * Convert technical field names to user-friendly names
   */
  private getFriendlyFieldName(field: string): string {
    const fieldMappings: { [key: string]: string } = {
      'email': 'Email address',
      'password': 'Password',
      'first_name': 'First name',
      'last_name': 'Last name',
      'phone_number': 'Phone number',
      'employee_id': 'Employee ID',
      'role_id': 'Role',
      'department_id': 'Department',
      'leave_type': 'Leave type',
      'start_date': 'Start date',
      'end_date': 'End date',
      'file': 'File',
      'title': 'Title',
      'description': 'Description',
      'status': 'Status'
    };

    return fieldMappings[field] || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Extract error message from any error structure
   */
  private extractErrorMessage(error: any): string | null {
    // PRIORITY 1: Check for custom error response format with title/description
    if (error.error && typeof error.error === 'object') {
      // Handle custom error format: {title: 'Not enough permissions', description: '...', suggestions: []}
      if (error.error.title && typeof error.error.title === 'string') {
        console.log('ErrorMessageService: Found custom error format with title:', error.error.title);

        // Use title as the main message, optionally append description
        let message = error.error.title;
        if (error.error.description && typeof error.error.description === 'string') {
          // Only append description if it's different from title and adds value
          if (!error.error.description.toLowerCase().includes(message.toLowerCase())) {
            message += '. ' + error.error.description;
          }
        }
        return message;
      }
    }

    // PRIORITY 2: Try different possible error message locations
    const possiblePaths = [
      'error.detail',
      'error.message',
      'error.error',
      'error.msg',
      'message',
      'detail',
      'msg',
      'error'
    ];

    for (const path of possiblePaths) {
      const value = this.getNestedProperty(error, path);
      if (value) {
        if (typeof value === 'string') {
          return value;
        } else if (Array.isArray(value)) {
          // Handle array of error objects or strings
          return value.map(v => {
            if (typeof v === 'object' && v.msg) {
              return v.msg;
            } else if (typeof v === 'object' && v.message) {
              return v.message;
            } else if (typeof v === 'string') {
              return v;
            }
            return v.toString();
          }).join('. ');
        } else if (typeof value === 'object' && value.message) {
          return value.message;
        }
      }
    }

    return null;
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * Check if error should be handled by interceptor
   * Simplified logic - handle all errors except auth endpoints
   */
  shouldHandleError(url: string, error: any): boolean {
    console.log('🔍 ErrorMessageService: Checking if should handle error for URL:', url);

    // Don't handle auth endpoints (let auth interceptor handle)
    const isAuthEndpoint = url.includes('/auth/login') ||
                          url.includes('/auth/refresh-token') ||
                          url.includes('/auth/register');

    if (isAuthEndpoint) {
      console.log('⏭️ ErrorMessageService: Skipping auth endpoint');
      return false;
    }

    // Don't handle if component explicitly wants to handle it manually
    if (error.skipInterceptor === true) {
      console.log('⏭️ ErrorMessageService: Component requested to skip interceptor');
      return false;
    }

    console.log('✅ ErrorMessageService: Will handle this error');
    return true;
  }
}
