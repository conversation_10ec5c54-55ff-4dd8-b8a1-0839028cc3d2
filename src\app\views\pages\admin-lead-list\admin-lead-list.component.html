
<div class="row admin-lead-list">
  <!-- Position Cards -->
  <div class="col-md-12 grid-margin">
    <div class="d-flex align-items-center justify-content-between mb-3">
      <h6 class="card-title mb-0">Position Overview</h6>
    </div>

    <div class="row">
      <!-- Position Cards -->
      <div class="col-12 col-md-4 mb-3" *ngFor="let position of positionRows">
        <div class="card position-card h-100" [ngClass]="getPositionCardClass(position.position)">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <div class="position-badge">
                <span class="badge" [ngClass]="getPositionBadgeClass(position.position)">{{ position.position }}</span>
              </div>
              <div class="position-icon">
                <i [attr.data-feather]="getPositionIcon(position.position)" class="icon-md" appFeatherIcon></i>
              </div>
            </div>

            <h5 class="position-title">{{ position.name }}</h5>

            <div class="position-count mt-4">
              <div class="d-flex align-items-center">
                <div class="count-circle" [ngClass]="getPositionCountClass(position.position)">
                  <span class="count-number">{{ position.count }}</span>
                </div>
                <div class="count-label ms-3">
                  <span class="text-muted">Total Assigned</span>
                  <div class="progress mt-1" style="height: 4px; width: 100px;">
                    <div class="progress-bar" [ngClass]="getPositionProgressClass(position.position)"
                         [style.width.%]="getProgressWidth(position.count)"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Employee List (shown on hover) -->
            <div class="position-employee-list">
              <div class="employee-list-header">
                <h6>{{ position.name }} Employees</h6>
              </div>
              <ul class="employee-list">
                <li *ngFor="let employee of position.employees" class="employee-item">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="employee-name">{{ employee.name }}</span>
                    <span class="employee-count" [ngClass]="getPositionBadgeClass(position.position)">{{ employee.count }}</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Admin Lead List -->
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card modern-table-card">
      <div class="card-body">
        <div class="d-flex align-items-center justify-content-between mb-4">
          <h6 class="card-title mb-0">Admin - Lead List</h6>
        </div>

        <!-- Search and filter controls -->
        <div class="row mb-3">
          <div class="col-md-6 col-lg-4">
            <div class="input-group">
              <span class="input-group-text bg-light">
                <i data-feather="search" class="icon-sm" appFeatherIcon></i>
              </span>
              <input
                type="text"
                class="form-control"
                placeholder="Search leads..."
                [formControl]="searchTerm"
              >
            </div>
          </div>
          <div class="col-md-6 col-lg-2 d-flex align-items-center mt-2 mt-md-0">
            <div class="d-flex align-items-center">
              <span class="text-muted me-2">Show:</span>
              <select class="form-select form-select-sm" [(ngModel)]="pageSize" (ngModelChange)="refreshLeads()">
                <option [ngValue]="5">5</option>
                <option [ngValue]="10">10</option>
                <option [ngValue]="20">20</option>
                <option [ngValue]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
          <table class="table table-hover table-striped modern-table">
            <thead>
              <tr>
                <th scope="col" sortable="id" (sort)="onSort($event)">Sr. No</th>
                <th scope="col" sortable="companyName" (sort)="onSort($event)">Company</th>
                <th scope="col" sortable="location" (sort)="onSort($event)">Sub-Location</th>
                <th scope="col" sortable="projectName" (sort)="onSort($event)">Project</th>
                <th scope="col" sortable="productType" (sort)="onSort($event)">Product Type</th>
                <th scope="col" style="min-width: 300px;">Assign To</th>
                <th scope="col" *ngIf="anyRoleSelected()">Employee</th>
                <th scope="col" *ngIf="anyRoleSelected()">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let lead of filteredLeads; let i = index">
                <td><span class="fw-medium">{{ (page - 1) * pageSize + i + 1 }}</span></td>

                <td>
                  <span>{{ lead.companyName }}</span>
                </td>

                <td>
                  <span>{{ lead.location }}</span>
                </td>
                <td>
                  <span>{{ lead.projectName }}</span>
                </td>
                <td>
                  <span class="badge bg-light text-dark">
                    {{ lead.productType }}
                  </span>
                </td>
                <td>
                  <div class="d-flex flex-wrap align-items-center">
                    <div class="form-check form-check-inline" *ngFor="let user of assignToUsers">
                      <input
                        type="radio"
                        class="form-check-input"
                        [id]="'user-' + user.id + '-lead-' + lead.id"
                        [checked]="isRoleSelected(lead.id, user.id)"
                        [name]="'role-lead-' + lead.id"
                        (change)="onRoleRadioChange(lead.id, user.id, $event)"
                      >
                      <label class="form-check-label" [for]="'user-' + user.id + '-lead-' + lead.id">{{ user.name }}</label>
                    </div>
                  </div>
                </td>
                <td *ngIf="anyRoleSelected()">
                  <div class="dropdown" *ngIf="showEmployeeDropdown[lead.id]" ngbDropdown [autoClose]="false">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="employeeDropdown-{{lead.id}}" ngbDropdownToggle>
                      {{ getSelectedEmployeesText(lead.id) }}
                    </button>
                    <div class="dropdown-menu p-2" ngbDropdownMenu>
                      <div class="px-2 py-1" *ngFor="let employee of employees">
                        <div class="form-check">
                          <input
                            type="checkbox"
                            class="form-check-input"
                            id="employee-{{employee.id}}-lead-{{lead.id}}"
                            [checked]="isEmployeeSelected(lead.id, employee.id)"
                            (change)="onEmployeeCheckboxChange(lead.id, employee.id, $event)"
                          >
                          <label class="form-check-label" for="employee-{{employee.id}}-lead-{{lead.id}}">
                            {{ employee.name }}
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td *ngIf="anyRoleSelected()">
                  <button
                    class="btn btn-sm btn-primary"
                    *ngIf="hasSelectedEmployees(lead.id)"
                    (click)="submitAssignment(lead)"
                  >
                    <i data-feather="check" class="icon-sm me-1" appFeatherIcon></i>
                    Submit
                  </button>
                </td>
              </tr>

              <!-- Empty state -->
              <tr *ngIf="filteredLeads.length === 0">
                <td [attr.colspan]="anyRoleSelected() ? 11 : 9" class="text-center py-4">
                  <div class="empty-state">
                    <i data-feather="database" class="icon-lg mb-3" appFeatherIcon></i>
                    <p class="mb-0">No leads found</p>
                    <small class="text-muted" *ngIf="searchTerm.value">Try adjusting your search criteria</small>
                    <div class="mt-3">
                      <button class="btn btn-outline-primary btn-sm" routerLink="/sales-list/new">
                        <i data-feather="plus" class="icon-sm me-1" appFeatherIcon></i>
                        Add New Lead
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Submitted Assignments Section -->
        <div class="card modern-table-card mt-4" *ngIf="submittedAssignments.length > 0">
          <div class="card-body">
            <h6 class="card-title mb-3">Submitted Assignments</h6>
            <div class="table-responsive">
              <table class="table table-hover table-striped modern-table">
                <thead>
                  <tr>
                    <th scope="col">Sr No</th>

                    <th scope="col">Assigned Roles</th>
                    <th scope="col">Assigned Employee</th>
                    <th scope="col">Submitted At</th>

                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let assignment of submittedAssignments; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="avatar avatar-sm me-2 bg-primary">
                          <span>{{ assignment.lead.leadName.charAt(0) }}</span>
                        </div>
                        <div>
                          <span class="fw-bold">{{ assignment.lead.leadName }}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="d-flex flex-wrap">
                        <span class="badge bg-orange me-1 mb-1" *ngFor="let role of assignment.roles">{{ role }}</span>
                      </div>
                    </td>
                    <td>
                      <div class="d-flex flex-wrap">
                        <span class="badge bg-green me-1 mb-1" *ngFor="let emp of assignment.employeeNames">{{ emp }}</span>
                      </div>
                    </td>
                    <td>
                      <small class="text-muted">{{ assignment.submittedAt | date:'medium' }}</small>
                    </td>

                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="filteredLeads.length > 0">
          <div>
            <span class="text-muted" *ngIf="collectionSize > 0">
              Showing {{ (page - 1) * pageSize + 1 }} to {{ Math.min(page * pageSize, collectionSize) }} of {{ collectionSize }} entries
            </span>
          </div>
          <ngb-pagination
            [collectionSize]="collectionSize"
            [(page)]="page"
            [pageSize]="pageSize"
            [maxSize]="5"
            [rotate]="true"
            [boundaryLinks]="true"
            (pageChange)="refreshLeads()"
            class="pagination-sm"
          ></ngb-pagination>
        </div>
      </div>
    </div>
  </div>
</div>
