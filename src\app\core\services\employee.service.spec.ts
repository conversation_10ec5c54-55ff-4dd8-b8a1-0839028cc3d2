import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { EmployeeService, Employee, Department, Designation, Role, BizzCorpApiResponse } from './employee.service';
import { environment } from '../../../environments/environment';

describe('EmployeeService - BizzCorp API Integration', () => {
  let service: EmployeeService;
  let httpMock: HttpTestingController;
  const baseUrl = `${environment.apiUrl}/api/v1/employees`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [EmployeeService]
    });
    service = TestBed.inject(EmployeeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('getEmployeeProfile', () => {
    it('should fetch employee profile successfully', () => {
      const mockEmployee: Employee = {
        id: '1027',
        employee_code: 'EMP001',
        first_name: '<PERSON>h',
        middle_name: '',
        last_name: 'Sagar',
        date_of_birth: '1998-07-12',
        gender: 'Male',
        marital_status: 'Single',
        personal_email: '<EMAIL>',
        office_email: '<EMAIL>',
        alternet_no: '**********',
        address: '123 MG Road, Bangalore, Karnataka',
        joining_date: '2025-06-01',
        reporting_date: '2025-06-01',
        role: 'U',
        department_id: 'dept-123',
        designation_id: 'desig-456',
        sub_role_id: 'role-789',
        office_location: 'Bangalore',
        shift_time: '10:00 AM - 6:00 PM',
        created_at: '2025-06-01T10:00:00Z',
        updated_at: '2025-06-01T10:00:00Z'
      };

      const mockResponse: BizzCorpApiResponse<Employee> = {
        success: true,
        data: mockEmployee,
        meta: { message: 'Employee profile fetched successfully' }
      };

      service.getEmployeeProfile('1027').subscribe(employee => {
        expect(employee).toEqual(mockEmployee);
        expect(employee.id).toBe('1027');
        expect(employee.first_name).toBe('Santosh');
        expect(employee.alternet_no).toBe('**********'); // Test exact field name
      });

      const req = httpMock.expectOne(`${baseUrl}/1027`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle employee not found error', () => {
      service.getEmployeeProfile('invalid-id').subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.code).toBe('NOT_FOUND');
          expect(error.message).toContain('Employee with ID invalid-id was not found');
        }
      });

      const req = httpMock.expectOne(`${baseUrl}/invalid-id`);
      req.flush({ message: 'Employee not found' }, { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getDepartmentsMasterData', () => {
    it('should fetch departments successfully', () => {
      const mockDepartments: Department[] = [
        { id: 'dept-1', name: 'IT', is_active: true },
        { id: 'dept-2', name: 'HR', is_active: true }
      ];

      const mockResponse: BizzCorpApiResponse<Department[]> = {
        success: true,
        data: mockDepartments
      };

      service.getDepartmentsMasterData().subscribe(departments => {
        expect(departments).toEqual(mockDepartments);
        expect(departments.length).toBe(2);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/api/v1/master/departments/`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should return fallback departments on error', () => {
      service.getDepartmentsMasterData().subscribe(departments => {
        expect(departments.length).toBeGreaterThan(0);
        expect(departments[0].name).toBe('IT');
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/api/v1/master/departments/`);
      req.flush({ message: 'Server error' }, { status: 500, statusText: 'Internal Server Error' });
    });
  });

  describe('getDesignationsMasterData', () => {
    it('should fetch designations successfully', () => {
      const mockDesignations: Designation[] = [
        { id: 'desig-1', name: 'Software Engineer', is_active: true },
        { id: 'desig-2', name: 'Senior Engineer', is_active: true }
      ];

      service.getDesignationsMasterData().subscribe(designations => {
        expect(designations).toEqual(mockDesignations);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/api/v1/master/designations/`);
      req.flush({ success: true, data: mockDesignations });
    });
  });

  describe('getRoleById', () => {
    it('should fetch role by ID successfully', () => {
      const mockRole: Role = {
        id: 'role-789',
        name: 'Developer',
        description: 'Software Developer Role',
        is_active: true
      };

      service.getRoleById('role-789').subscribe(role => {
        expect(role).toEqual(mockRole);
        expect(role.name).toBe('Developer');
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/api/v1/roles/role-789`);
      req.flush({ success: true, data: mockRole });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', () => {
      service.getEmployeeProfile('test-id').subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.code).toBe('NETWORK_ERROR');
          expect(error.message).toContain('Unable to connect to the server');
        }
      });

      const req = httpMock.expectOne(`${baseUrl}/test-id`);
      req.error(new ErrorEvent('Network error'), { status: 0 });
    });

    it('should handle unauthorized errors', () => {
      service.getEmployeeProfile('test-id').subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.code).toBe('UNAUTHORIZED');
          expect(error.message).toContain('not authorized');
        }
      });

      const req = httpMock.expectOne(`${baseUrl}/test-id`);
      req.flush({ message: 'Unauthorized' }, { status: 401, statusText: 'Unauthorized' });
    });
  });
});
