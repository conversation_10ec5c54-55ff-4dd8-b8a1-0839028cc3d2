import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface Holiday {
  id: string;
  name: string;
  date: string; // YYYY-MM-DD format
  month: string;
  day: string;
  source: string;
}

export interface HolidayResponse {
  success: boolean;
  data: {
    year: number;
    month: number | null;
    new_year_activities: Holiday[];
  };
  error?: string;
  meta?: any;
}

@Injectable({
  providedIn: 'root'
})
export class HolidayService {
  private baseUrl = `${environment.apiUrl}/api/v1/calendar/test/holidays`;
  
  // Cache holidays to avoid repeated API calls
  private holidaysCache = new BehaviorSubject<Holiday[]>([]);
  public holidays$ = this.holidaysCache.asObservable();
  
  // Track if holidays are loaded
  private holidaysLoaded = false;

  constructor(private http: HttpClient) {}

  /**
   * Get all holidays from the API
   * @returns Observable of holidays array
   */
  getHolidays(): Observable<Holiday[]> {
    console.log('🎄 HOLIDAY SERVICE - Fetching holidays from API...');

    return this.http.get<HolidayResponse>(this.baseUrl).pipe(
      map(response => {
        console.log('🎄 HOLIDAY SERVICE - API response:', response);

        if (response && response.success && response.data && Array.isArray(response.data.new_year_activities)) {
          const holidays = response.data.new_year_activities;
          console.log('✅ HOLIDAY SERVICE - Holidays loaded:', holidays.length);
          console.log('🎄 HOLIDAY SERVICE - Holiday dates:', holidays.map(h => h.date));

          // Cache the holidays
          this.holidaysCache.next(holidays);
          this.holidaysLoaded = true;

          return holidays;
        } else {
          console.warn('⚠️ HOLIDAY SERVICE - Invalid API response structure:', response);
          return [];
        }
      }),
      catchError(error => {
        console.error('❌ HOLIDAY SERVICE - Error fetching holidays:', error);
        return of([]);
      })
    );
  }

  /**
   * Get cached holidays or fetch from API if not loaded
   * @returns Observable of holidays array
   */
  getCachedHolidays(): Observable<Holiday[]> {
    if (this.holidaysLoaded) {
      console.log('🎄 HOLIDAY SERVICE - Returning cached holidays');
      return this.holidays$;
    } else {
      console.log('🎄 HOLIDAY SERVICE - Loading holidays for first time');
      return this.getHolidays();
    }
  }

  /**
   * Check if a specific date is a holiday
   * @param date Date to check (Date object or string)
   * @returns Observable<boolean> indicating if the date is a holiday
   */
  isHoliday(date: Date | string): Observable<boolean> {
    const dateStr = this.formatDateToString(date);

    return this.getCachedHolidays().pipe(
      map(holidays => {
        const isHoliday = holidays.some(holiday => holiday.date === dateStr);
        console.log(`🎄 HOLIDAY CHECK - ${dateStr} is ${isHoliday ? 'a holiday' : 'not a holiday'}`);
        return isHoliday;
      })
    );
  }

  /**
   * Get holiday information for a specific date
   * @param date Date to check
   * @returns Observable<Holiday | null> holiday information or null
   */
  getHolidayInfo(date: Date | string): Observable<Holiday | null> {
    const dateStr = this.formatDateToString(date);

    return this.getCachedHolidays().pipe(
      map(holidays => {
        const holiday = holidays.find(h => h.date === dateStr);
        return holiday || null;
      })
    );
  }

  /**
   * Check if a date is a weekend (Saturday or Sunday)
   * @param date Date to check
   * @returns boolean indicating if the date is a weekend
   */
  isWeekend(date: Date): boolean {
    const day = date.getDay();
    const isWeekend = day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
    console.log(`📅 WEEKEND CHECK - ${this.formatDateToString(date)} is ${isWeekend ? 'a weekend' : 'a weekday'}`);
    return isWeekend;
  }

  /**
   * Check if a date is either a weekend or a holiday
   * @param date Date to check
   * @returns Observable<boolean> indicating if the date is restricted
   */
  isRestrictedDate(date: Date | string): Observable<boolean> {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const isWeekend = this.isWeekend(dateObj);
    
    if (isWeekend) {
      console.log(`🚫 RESTRICTED DATE - ${this.formatDateToString(dateObj)} is restricted (weekend)`);
      return of(true);
    }
    
    return this.isHoliday(dateObj).pipe(
      map(isHoliday => {
        const isRestricted = isHoliday;
        if (isRestricted) {
          console.log(`🚫 RESTRICTED DATE - ${this.formatDateToString(dateObj)} is restricted (holiday)`);
        }
        return isRestricted;
      })
    );
  }

  /**
   * Get all restricted dates (weekends + holidays) for a given month
   * @param year Year to check
   * @param month Month to check (0-11)
   * @returns Observable<Date[]> array of restricted dates
   */
  getRestrictedDatesForMonth(year: number, month: number): Observable<Date[]> {
    console.log(`📅 RESTRICTED DATES - Getting restricted dates for ${year}-${month + 1}`);
    
    return this.getCachedHolidays().pipe(
      map(holidays => {
        const restrictedDates: Date[] = [];
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        // Check each day of the month
        for (let day = 1; day <= daysInMonth; day++) {
          const date = new Date(year, month, day);
          
          // Check if it's a weekend
          if (this.isWeekend(date)) {
            restrictedDates.push(date);
          } else {
            // Check if it's a holiday
            const dateStr = this.formatDateToString(date);
            const isHoliday = holidays.some(holiday => holiday.date === dateStr);
            if (isHoliday) {
              restrictedDates.push(date);
            }
          }
        }
        
        console.log(`✅ RESTRICTED DATES - Found ${restrictedDates.length} restricted dates for ${year}-${month + 1}`);
        return restrictedDates;
      })
    );
  }

  /**
   * Format date to YYYY-MM-DD string
   * @param date Date to format
   * @returns Formatted date string
   */
  private formatDateToString(date: Date | string): string {
    if (typeof date === 'string') {
      // Ensure the string is in YYYY-MM-DD format
      const dateObj = new Date(date);
      return dateObj.toISOString().split('T')[0];
    }
    return date.toISOString().split('T')[0];
  }

  /**
   * Clear the holidays cache (useful for refreshing data)
   */
  clearCache(): void {
    console.log('🗑️ HOLIDAY SERVICE - Clearing holidays cache');
    this.holidaysCache.next([]);
    this.holidaysLoaded = false;
  }

  /**
   * Refresh holidays from the API
   * @returns Observable of holidays array
   */
  refreshHolidays(): Observable<Holiday[]> {
    console.log('🔄 HOLIDAY SERVICE - Refreshing holidays from API');
    this.clearCache();
    return this.getHolidays();
  }
}
