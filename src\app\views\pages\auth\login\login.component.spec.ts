import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

import { LoginComponent } from './login.component';
import { AuthService } from '../../../../core/services/auth.service';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    // Create spy objects
    mockAuthService = jasmine.createSpyObj('AuthService', ['login', 'isLoggedIn']);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: {
        queryParamMap: {
          get: jasmine.createSpy('get').and.returnValue(null)
        },
        queryParams: {}
      }
    };

    await TestBed.configureTestingModule({
      imports: [
        LoginComponent,
        ReactiveFormsModule,
        FeatherIconDirective
      ],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    
    // Mock localStorage
    spyOn(localStorage, 'getItem').and.returnValue(null);
    spyOn(localStorage, 'setItem');
    spyOn(localStorage, 'removeItem');
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values when no remembered credentials', () => {
    expect(component.loginForm.get('username')?.value).toBe('');
    expect(component.loginForm.get('password')?.value).toBe('');
    expect(component.loginForm.get('rememberMe')?.value).toBe(false);
  });

  it('should save credentials when remember me is checked', () => {
    // Set form values
    component.loginForm.patchValue({
      username: 'testuser',
      password: 'testpass',
      rememberMe: true
    });

    // Call the private method through the public login method
    mockAuthService.login.and.returnValue(of({ access_token: 'test-token' }));
    
    component.onLoggedin(new Event('submit'));

    expect(localStorage.setItem).toHaveBeenCalledWith(
      'rememberedCredentials',
      jasmine.any(String)
    );
  });

  it('should not save credentials when remember me is unchecked', () => {
    // Set form values
    component.loginForm.patchValue({
      username: 'testuser',
      password: 'testpass',
      rememberMe: false
    });

    // Call the private method through the public login method
    mockAuthService.login.and.returnValue(of({ access_token: 'test-token' }));
    
    component.onLoggedin(new Event('submit'));

    expect(localStorage.removeItem).toHaveBeenCalledWith('rememberedCredentials');
  });

  it('should toggle password visibility', () => {
    expect(component.showPassword).toBe(false);
    
    component.togglePasswordVisibility();
    
    expect(component.showPassword).toBe(true);
  });

  it('should handle remember me checkbox change', () => {
    spyOn(component['cdr'], 'detectChanges');
    
    component.onRememberMeChange();
    
    expect(component['cdr'].detectChanges).toHaveBeenCalled();
  });

  it('should clean up interval on destroy', () => {
    spyOn(window, 'clearInterval');
    component['pulseInterval'] = 123;
    
    component.ngOnDestroy();
    
    expect(clearInterval).toHaveBeenCalledWith(123);
  });
});
