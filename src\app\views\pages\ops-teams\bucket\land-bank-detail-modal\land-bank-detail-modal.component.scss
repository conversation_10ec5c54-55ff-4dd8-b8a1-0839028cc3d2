.modal-header {
  background-color: #3F828B;
  color: white;
}

.modal-title {
  margin-bottom: 0;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border-radius: 0.25rem;
  border: 1px solid #ced4da;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  border-color: #3F828B;
  box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
}

textarea.form-control {
  resize: vertical;
}

.btn-primary {
  background-color: #df5316;
  border-color: #df5316;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #c94813;
  border-color: #c94813;
}

.btn-secondary {
  background-color: #E25516;
  border-color: #E25516;
  color: white;
}

.btn-secondary:hover, .btn-secondary:focus {
  background-color: #c94813;
  border-color: #c94813;
}

// Hide spinners on number inputs
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

input[type=number] {
  -moz-appearance: textfield;
}
