// Page styling
.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

// Summary card styling
.summary-card {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  }

  .summary-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }
}

// Stat icon styling
.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

// Master data card styling
.master-data-card {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    
    .action-buttons i {
      transform: translateX(4px);
    }
  }

  &.loading {
    pointer-events: none;
    opacity: 0.7;
  }

  &.error {
    border-left: 4px solid var(--bs-danger);
  }

  .master-data-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .master-data-content {
    .card-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #2c3e50;
    }

    .master-data-stats {
      padding: 0.75rem;
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }
  }

  .master-data-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 0.75rem;

    .action-buttons i {
      transition: transform 0.2s ease;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
}

// Card styling
.card {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 12px;

  .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Badge styling
.badge {
  font-weight: 500;
  padding: 0.4em 0.8em;
  border-radius: 6px;
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &.w-100 {
    padding: 0.75rem 1rem;
  }
}

// Alert styling
.alert {
  border: none;
  border-radius: 8px;
  
  &.alert-danger {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
  }
}

// Icon styling
.icon-xs {
  width: 12px;
  height: 12px;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-md {
  width: 20px;
  height: 20px;
}

.icon-lg {
  width: 32px;
  height: 32px;
}

// Color utilities
.text-primary { color: var(--bs-primary) !important; }
.text-success { color: var(--bs-success) !important; }
.text-info { color: var(--bs-info) !important; }
.text-warning { color: var(--bs-warning) !important; }
.text-danger { color: var(--bs-danger) !important; }
.text-secondary { color: var(--bs-secondary) !important; }
.text-dark { color: var(--bs-dark) !important; }

.bg-primary { background-color: var(--bs-primary) !important; }
.bg-success { background-color: var(--bs-success) !important; }
.bg-info { background-color: var(--bs-info) !important; }
.bg-warning { background-color: var(--bs-warning) !important; }
.bg-danger { background-color: var(--bs-danger) !important; }
.bg-secondary { background-color: var(--bs-secondary) !important; }
.bg-dark { background-color: var(--bs-dark) !important; }

// Spinner styling
.spinner-border {
  &.spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem;
  }

  .master-data-card {
    margin-bottom: 1rem;
    
    &:hover {
      transform: none;
    }
  }

  .summary-card {
    &:hover {
      transform: none;
    }
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
    
    &.align-items-center {
      align-items: flex-start !important;
    }
  }

  .btn.w-100 {
    margin-bottom: 0.5rem;
  }
}

// Animation for loading states
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

// Hover effects
.master-data-card:hover .master-data-icon {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

// Focus states
.btn:focus,
.card:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

// Custom scrollbar for overflow content
.master-data-content::-webkit-scrollbar {
  width: 4px;
}

.master-data-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.master-data-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.master-data-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
