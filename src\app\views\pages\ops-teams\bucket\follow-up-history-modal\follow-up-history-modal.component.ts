import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal, NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-follow-up-history-modal',
  standalone: true,
  imports: [
    CommonModule,
    NgbPaginationModule
  ],
  templateUrl: './follow-up-history-modal.component.html',
  styleUrls: ['./follow-up-history-modal.component.scss']
})
export class FollowUpHistoryModalComponent {
  @Input() docId: number;
  @Input() documentName: string;
  @Input() followUps: Array<{ date: string, time: string, notes: string, timestamp: number }> = [];

  // Pagination
  page = 1;
  pageSize = 5;

  constructor(public activeModal: NgbActiveModal) {}

  // Get sorted follow-ups (newest first)
  get sortedFollowUps(): Array<{ date: string, time: string, notes: string, timestamp: number }> {
    return [...this.followUps].sort((a, b) => b.timestamp - a.timestamp);
  }

  // Get current page items
  get paginatedFollowUps(): Array<{ date: string, time: string, notes: string, timestamp: number }> {
    const startIndex = (this.page - 1) * this.pageSize;
    return this.sortedFollowUps.slice(startIndex, startIndex + this.pageSize);
  }
}
