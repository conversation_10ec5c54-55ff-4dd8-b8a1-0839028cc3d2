// Comp-off Management Component Styles

.container-fluid {
  padding: 1.5rem;
}

// Header styles
.page-header {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
  margin-bottom: 2rem;

  h4 {
    color: #495057;
    font-weight: 600;
  }

  .text-muted {
    font-size: 0.9rem;
  }
}

// Filter card styles
.filter-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;

  .card-body {
    padding: 1.5rem;
  }

  .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .form-select,
  .form-control {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    
    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  .input-group {
    .btn {
      border-color: #ced4da;
    }
  }
}

// Table styles
.table-responsive {
  border-radius: 0.5rem;
  overflow: hidden;

  .table {
    margin-bottom: 0;

    th {
      background-color: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      font-weight: 600;
      color: #495057;
      padding: 1rem 0.75rem;
    }

    td {
      padding: 1rem 0.75rem;
      vertical-align: middle;
      border-bottom: 1px solid #e9ecef;
    }

    tbody tr {
      transition: background-color 0.15s ease-in-out;

      &:hover {
        background-color: #f8f9fa;
      }
    }
  }
}

// Badge styles
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;

  &.badge-warning {
    background-color: #ffc107;
    color: #212529;
  }

  &.badge-success {
    background-color: #198754;
    color: white;
  }

  &.badge-danger {
    background-color: #dc3545;
    color: white;
  }

  &.badge-secondary {
    background-color: #6c757d;
    color: white;
  }

  &.bg-info {
    background-color: #0dcaf0 !important;
    color: #000;
  }
}

// Button styles
.btn-group {
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;

    &:not(:last-child) {
      margin-right: 0.25rem;
    }

    i {
      font-size: 0.875rem;
    }
  }
}

.btn {
  &.btn-success {
    background-color: #198754;
    border-color: #198754;

    &:hover {
      background-color: #157347;
      border-color: #146c43;
    }
  }

  &.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;

    &:hover {
      background-color: #bb2d3b;
      border-color: #b02a37;
    }
  }

  &.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;

    &:hover {
      background-color: #0d6efd;
      border-color: #0d6efd;
      color: white;
    }
  }

  &.btn-outline-info {
    color: #0dcaf0;
    border-color: #0dcaf0;

    &:hover {
      background-color: #0dcaf0;
      border-color: #0dcaf0;
      color: #000;
    }
  }
}

// Loading and empty states
.loading-state,
.empty-state {
  text-align: center;
  padding: 3rem 1rem;

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  i.fa-inbox {
    color: #6c757d;
    margin-bottom: 1rem;
  }

  h5 {
    color: #6c757d;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6c757d;
    font-size: 0.9rem;
  }
}

// Pagination styles
.pagination {
  .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;

    &:hover {
      background-color: #e9ecef;
      border-color: #dee2e6;
    }
  }

  .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }

  .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
  }
}

// Modal styles
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;

  .modal-title {
    color: #495057;
    font-weight: 600;

    i {
      color: #0d6efd;
    }
  }
}

.modal-body {
  padding: 1.5rem;

  .card {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 0.75rem 1rem;

      h6 {
        color: #495057;
        font-weight: 600;
        margin: 0;
      }
    }

    .card-body {
      padding: 1rem;
    }
  }

  .form-check {
    margin-bottom: 0.75rem;

    .form-check-label {
      font-weight: 500;

      &.text-success {
        color: #198754 !important;
      }

      &.text-danger {
        color: #dc3545 !important;
      }

      i {
        font-size: 0.875rem;
      }
    }
  }

  .form-control,
  .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;

    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  textarea.form-control {
    resize: vertical;
    min-height: 80px;
  }
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;

  .btn {
    padding: 0.5rem 1rem;
    font-weight: 500;

    .spinner-border-sm {
      width: 1rem;
      height: 1rem;
    }
  }
}

// Alert styles
.alert {
  border-radius: 0.5rem;
  padding: 1rem;

  &.alert-success {
    background-color: #d1e7dd;
    border-color: #badbcc;
    color: #0f5132;
  }

  &.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
  }

  strong {
    font-weight: 600;
  }
}

// Text truncation
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }

  .table-responsive {
    font-size: 0.875rem;

    .table {
      th,
      td {
        padding: 0.5rem;
      }
    }
  }

  .btn-group {
    .btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }
  }

  .modal-body {
    padding: 1rem;

    .card .card-body {
      padding: 0.75rem;
    }
  }

  .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}

// Print styles
@media print {
  .btn,
  .pagination,
  .modal {
    display: none !important;
  }

  .table {
    font-size: 0.8rem;
  }
}
