// Lead Categories Component Styles

.lead-categories-component {
  .card-title {
    font-weight: 600;
    color: var(--bs-dark);
  }

  .nav-tabs {
    .nav-link {
      border: none;
      color: var(--bs-secondary);
      font-weight: 500;
      padding: 0.75rem 1.5rem;
      
      &.active {
        background-color: var(--bs-primary);
        color: white;
        border-radius: 0.375rem;
      }
      
      &:hover:not(.active) {
        background-color: var(--bs-light);
        color: var(--bs-primary);
      }
    }
  }

  .table {
    th {
      font-weight: 600;
      color: var(--bs-dark);
      border-bottom: 2px solid var(--bs-border-color);
      padding: 1rem 0.75rem;
    }

    td {
      padding: 1rem 0.75rem;
      vertical-align: middle;
    }

    .qualification-info,
    .automation-info {
      font-size: 0.875rem;
      
      .badge {
        font-size: 0.75rem;
      }
    }
  }

  .badge {
    font-size: 0.75rem;
    font-weight: 500;
    
    &.bg-light {
      border: 1px solid var(--bs-border-color);
    }
    
    // Priority-specific badge colors
    &.bg-danger {
      background-color: #dc3545 !important;
    }
    
    &.bg-warning {
      background-color: #fd7e14 !important;
      color: white !important;
    }
    
    &.bg-primary {
      background-color: #0d6efd !important;
    }
    
    &.bg-secondary {
      background-color: #6c757d !important;
    }
  }

  .dropdown-menu {
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    .dropdown-item {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      
      &:hover {
        background-color: var(--bs-light);
      }
      
      &.text-danger:hover {
        background-color: var(--bs-danger);
        color: white;
      }
      
      &.text-success:hover {
        background-color: var(--bs-success);
        color: white;
      }
    }
  }

  .input-group {
    .input-group-text {
      background-color: var(--bs-light);
      border-color: var(--bs-border-color);
      color: var(--bs-secondary);
    }
  }

  .form-select {
    border-color: var(--bs-border-color);
    
    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }

  .btn {
    font-weight: 500;
    
    &.btn-outline-secondary {
      border-color: var(--bs-border-color);
      
      &:hover {
        background-color: var(--bs-secondary);
        border-color: var(--bs-secondary);
      }
    }
  }

  .spinner-border {
    width: 2rem;
    height: 2rem;
  }

  .alert {
    border: none;
    border-radius: 0.5rem;
    
    &.alert-danger {
      background-color: rgba(var(--bs-danger-rgb), 0.1);
      color: var(--bs-danger);
    }
  }

  .card {
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    &.bg-primary,
    &.bg-success,
    &.bg-warning,
    &.bg-secondary {
      border: none;
      
      .card-body {
        padding: 1.5rem;
      }
    }
  }

  .text-muted {
    color: var(--bs-secondary) !important;
  }

  // Lead category specific styles
  .qualification-info {
    .badge {
      margin-bottom: 0.25rem;
    }
  }

  .automation-info {
    .badge {
      margin-bottom: 0.25rem;
    }
  }

  // Priority badge animations
  .badge.bg-danger {
    animation: pulse-critical 2s infinite;
  }

  @keyframes pulse-critical {
    0% {
      box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
  }

  // Default badge styling
  .badge.bg-info {
    background-color: #0dcaf0 !important;
    color: var(--bs-dark) !important;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .d-flex.gap-2 {
      flex-direction: column;
      gap: 0.5rem !important;
      
      .btn {
        width: 100%;
      }
    }
    
    .table-responsive {
      font-size: 0.875rem;
    }
    
    .nav-tabs {
      .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
      }
    }
  }

  @media (max-width: 576px) {
    .row.mb-3 {
      .col-md-2 {
        margin-bottom: 0.5rem;
      }
    }
    
    .card-title {
      font-size: 1.25rem;
    }
    
    .table {
      th, td {
        padding: 0.5rem;
      }
    }
  }
}

// Animation for loading states
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table tbody tr {
  animation: fadeIn 0.3s ease-in-out;
}

// Custom scrollbar for table
.table-responsive {
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bs-light);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
    border-radius: 4px;
    
    &:hover {
      background: var(--bs-dark);
    }
  }
}

// Special styling for complex data
.qualification-info,
.automation-info {
  max-width: 200px;
  
  .badge {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: inline-block;
  }
}

// Color coding for priority levels
.priority-low {
  color: #6c757d;
}

.priority-medium {
  color: #0d6efd;
}

.priority-high {
  color: #fd7e14;
}

.priority-critical {
  color: #dc3545;
}
