// Department Hierarchy Component Styles

.modal-header {
  background: linear-gradient(135deg, var(--bs-info) 0%, rgba(var(--bs-info-rgb), 0.8) 100%);
  color: white;
  border-bottom: none;
  
  .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    
    i {
      opacity: 0.9;
    }
  }
  
  .btn-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    opacity: 1;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.modal-body {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  background: var(--bs-gray-50);
  border-top: 1px solid var(--bs-gray-200);
  padding: 1rem 2rem;
}

// Hierarchy container
.hierarchy-container {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

.hierarchy-item {
  margin: 0.5rem 0;
  transition: background-color 0.2s ease;
  border-radius: 6px;
  padding: 0.5rem;
  
  &:hover {
    background: var(--bs-gray-50);
  }
  
  &.root-item {
    background: rgba(var(--bs-primary-rgb), 0.1);
    border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
    border-radius: 8px;
    
    .hierarchy-content {
      font-weight: 600;
      color: var(--bs-primary);
    }
    
    .hierarchy-icon {
      color: var(--bs-primary);
    }
  }
}

.hierarchy-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.hierarchy-connector {
  color: var(--bs-gray-500);
  font-size: 0.875rem;
  white-space: pre;
  font-family: 'Courier New', monospace;
}

.hierarchy-icon {
  width: 16px;
  height: 16px;
  color: var(--bs-gray-600);
  flex-shrink: 0;
}

.hierarchy-name {
  font-weight: 500;
  color: var(--bs-gray-700);
  flex-grow: 1;
}

.hierarchy-meta {
  font-size: 0.75rem;
  color: var(--bs-gray-500);
  background: var(--bs-gray-100);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  white-space: nowrap;
}

// Loading and error states
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.alert {
  border-radius: 8px;
  border: none;
  
  &.alert-danger {
    background: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
    border-left: 4px solid var(--bs-danger);
  }
}

// Empty state
.text-center {
  .feather {
    opacity: 0.5;
  }
  
  h6 {
    color: var(--bs-gray-600);
    font-weight: 500;
  }
  
  p {
    color: var(--bs-gray-500);
    font-size: 0.875rem;
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  
  &.btn-secondary {
    background: var(--bs-gray-200);
    border: 1px solid var(--bs-gray-300);
    color: var(--bs-gray-700);
    
    &:hover {
      background: var(--bs-gray-300);
      border-color: var(--bs-gray-400);
      color: var(--bs-gray-800);
    }
  }
  
  i {
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-footer {
    padding: 1rem 1.5rem;
  }
  
  .hierarchy-item {
    padding: 0.75rem 0.5rem;
  }
  
  .hierarchy-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    
    .hierarchy-name {
      font-size: 0.9rem;
    }
    
    .hierarchy-meta {
      font-size: 0.7rem;
      align-self: flex-end;
    }
  }
  
  .hierarchy-connector {
    display: none; // Hide connectors on mobile for cleaner look
  }
}

// Dark mode support
[data-theme="dark"] {
  .modal-header {
    background: linear-gradient(135deg, var(--bs-info) 0%, rgba(var(--bs-info-rgb), 0.9) 100%);
  }
  
  .modal-footer {
    background: var(--bs-gray-800);
    border-color: var(--bs-gray-700);
  }
  
  .hierarchy-item {
    &:hover {
      background: var(--bs-gray-800);
    }
    
    &.root-item {
      background: rgba(var(--bs-primary-rgb), 0.2);
      border-color: rgba(var(--bs-primary-rgb), 0.3);
    }
  }
  
  .hierarchy-name {
    color: var(--bs-gray-300);
  }
  
  .hierarchy-meta {
    background: var(--bs-gray-700);
    color: var(--bs-gray-300);
  }
  
  .hierarchy-connector {
    color: var(--bs-gray-400);
  }
  
  .hierarchy-icon {
    color: var(--bs-gray-400);
  }
  
  .btn-secondary {
    background: var(--bs-gray-700);
    border-color: var(--bs-gray-600);
    color: var(--bs-gray-300);
    
    &:hover {
      background: var(--bs-gray-600);
      border-color: var(--bs-gray-500);
      color: var(--bs-gray-200);
    }
  }
}

// Animation for hierarchy items
.hierarchy-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Hierarchy tree lines (enhanced visual)
.hierarchy-children {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 1px;
    background: var(--bs-gray-300);
    opacity: 0.5;
  }
}

// Custom scrollbar for modal body
.modal-body {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 3px;
    
    &:hover {
      background: var(--bs-gray-500);
    }
  }
}
