import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';

export interface PermissionErrorDetails {
  code: string;
  message: string;
  action: string;
  requiredPermissions: string[];
  userPermissions: string[];
  timestamp: Date;
  route?: string;
  component?: string;
  suggestions?: string[];
}

export interface UserFriendlyError {
  title: string;
  message: string;
  actionText?: string;
  actionCallback?: () => void;
  severity: 'error' | 'warning' | 'info';
}

@Injectable({
  providedIn: 'root'
})
export class PermissionErrorService {
  private errorSubject = new BehaviorSubject<PermissionErrorDetails | null>(null);
  public currentError$ = this.errorSubject.asObservable();

  private errorHistory: PermissionErrorDetails[] = [];

  constructor(private router: Router) {}

  /**
   * Handle permission error with user-friendly feedback
   */
  handlePermissionError(
    action: string,
    requiredPermissions: string[],
    userPermissions: string[],
    context?: {
      route?: string;
      component?: string;
      code?: string;
    }
  ): void {
    const error: PermissionErrorDetails = {
      code: context?.code || 'PERMISSION_DENIED',
      message: this.generateErrorMessage(action, requiredPermissions),
      action,
      requiredPermissions,
      userPermissions,
      timestamp: new Date(),
      route: context?.route || this.router.url,
      component: context?.component,
      suggestions: this.generateSuggestions(requiredPermissions, userPermissions)
    };

    // Store error for debugging
    this.errorHistory.push(error);
    this.errorSubject.next(error);

    // Log detailed error for developers
    console.group('🚫 Permission Error Details');
    console.error('Action:', action);
    console.error('Required Permissions:', requiredPermissions);
    console.error('User Permissions:', userPermissions);
    console.error('Route:', error.route);
    console.error('Component:', error.component);
    console.error('Suggestions:', error.suggestions);
    console.groupEnd();

    // Show user-friendly error
    this.showUserFriendlyError(error);
  }

  /**
   * Generate user-friendly error message
   */
  private generateErrorMessage(action: string, requiredPermissions: string[]): string {
    const permissionNames = this.humanizePermissions(requiredPermissions);

    if (requiredPermissions.length === 1) {
      return `You need ${permissionNames[0]} permission to ${action}.`;
    } else {
      const lastPermission = permissionNames.pop();
      return `You need ${permissionNames.join(', ')} or ${lastPermission} permission to ${action}.`;
    }
  }

  /**
   * Convert technical permission names to human-readable format
   */
  private humanizePermissions(permissions: string[]): string[] {
    const permissionMap: { [key: string]: string } = {
      'admin:access': 'Administrator Access',
      'users:read': 'View Users',
      'users:write': 'Edit Users',
      'users:create': 'Create Users',
      'users:delete': 'Delete Users',
      'roles:read': 'View Roles',
      'roles:create': 'Create Roles',
      'roles:update': 'Update Roles',
      'roles:delete': 'Delete Roles',
      'permissions:read': 'View Permissions',
      'permissions:create': 'Create Permissions',
      'permissions:update': 'Update Permissions',
      'permissions:delete': 'Delete Permissions',
      'sales:read': 'View Sales Data',
      'sales:create': 'Create Sales Records',
      'sales:update': 'Update Sales Records',
      'sales:delete': 'Delete Sales Records',
      'sales:manage': 'Manage Sales',
      'employees:read': 'View Employees',
      'employees:create': 'Add Employees',
      'employees:update': 'Update Employees',
      'employees:delete': 'Remove Employees',
      'leave:create': 'Apply for Leave',
      'leave:read': 'View Leave Information',
      'leave:approve': 'Approve Leave Requests',
      'leave:cancel': 'Cancel Leave',
      'leave:assign_compoff': 'Assign Comp-off',
      'leave:request_compoff': 'Request Comp-off',
      'master:read': 'View Master Data',
      'master:create': 'Create Master Data',
      'master:update': 'Update Master Data',
      'master:delete': 'Delete Master Data',
      'ops:access': 'Operations Team Access',
      'salary:read': 'View Salary Information',
      'salary:generate': 'Generate Salary Slips',
      'calendar:read': 'View Calendar',
      'calendar:employee': 'Employee Calendar Access',
      'attendance:read': 'View Attendance',
      'reports:read': 'View Reports'
    };

    return permissions.map(permission =>
      permissionMap[permission] || this.formatPermissionName(permission)
    );
  }

  /**
   * Format permission name as fallback
   */
  private formatPermissionName(permission: string): string {
    const [resource, action] = permission.split(':');
    const formattedResource = resource.charAt(0).toUpperCase() + resource.slice(1);
    const formattedAction = action ? action.charAt(0).toUpperCase() + action.slice(1) : '';
    return `${formattedAction} ${formattedResource}`.trim();
  }

  /**
   * Generate helpful suggestions for users
   */
  private generateSuggestions(requiredPermissions: string[], userPermissions: string[]): string[] {
    const suggestions: string[] = [];

    // Check if user has any permissions at all
    if (userPermissions.length === 0) {
      suggestions.push('Contact your administrator to assign you appropriate permissions.');
      return suggestions;
    }

    // Check for similar permissions
    const similarPermissions = this.findSimilarPermissions(requiredPermissions, userPermissions);
    if (similarPermissions.length > 0) {
      suggestions.push(`You have similar permissions: ${similarPermissions.join(', ')}. You may need additional access.`);
    }

    // Suggest contacting admin
    suggestions.push('Contact your system administrator to request the necessary permissions.');

    // Suggest alternative actions
    const alternatives = this.getAlternativeActions(requiredPermissions);
    if (alternatives.length > 0) {
      suggestions.push(`Alternative actions you can try: ${alternatives.join(', ')}`);
    }

    return suggestions;
  }

  /**
   * Find similar permissions user already has
   */
  private findSimilarPermissions(required: string[], userPerms: string[]): string[] {
    const similar: string[] = [];

    required.forEach(reqPerm => {
      const [reqResource] = reqPerm.split(':');
      userPerms.forEach(userPerm => {
        const [userResource] = userPerm.split(':');
        if (reqResource === userResource && !similar.includes(userPerm)) {
          similar.push(userPerm);
        }
      });
    });

    return similar;
  }

  /**
   * Get alternative actions based on required permissions
   */
  private getAlternativeActions(requiredPermissions: string[]): string[] {
    const alternatives: string[] = [];

    requiredPermissions.forEach(permission => {
      switch (permission) {
        case 'users:write':
        case 'users:update':
          if (requiredPermissions.includes('users:read')) {
            alternatives.push('View user information instead');
          }
          break;
        case 'sales:create':
          alternatives.push('View existing sales data');
          break;
        case 'leave:approve':
          alternatives.push('Apply for your own leave');
          break;
        case 'admin:access':
          alternatives.push('Use standard user features');
          break;
      }
    });

    return Array.from(new Set(alternatives)); // Remove duplicates
  }

  /**
   * Show user-friendly error notification
   */
  private showUserFriendlyError(error: PermissionErrorDetails): void {
    const userError: UserFriendlyError = {
      title: 'Access Denied',
      message: error.message,
      severity: 'error',
      actionText: 'Go to Dashboard',
      actionCallback: () => {
        this.router.navigate(['/lms/dashboard']);
        this.clearCurrentError();
      }
    };

    // In a real application, you would show this in a toast, modal, or notification component
    // For now, we'll use console and could integrate with a notification service
    console.warn('🔔 User Notification:', userError);

    // You could emit this to a notification service
    // this.notificationService.showError(userError);
  }

  /**
   * Clear current error
   */
  clearCurrentError(): void {
    this.errorSubject.next(null);
  }

  /**
   * Get error history for debugging
   */
  getErrorHistory(): PermissionErrorDetails[] {
    return [...this.errorHistory];
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Check if user should be redirected based on error
   */
  shouldRedirect(error: PermissionErrorDetails): boolean {
    // Redirect for critical permissions
    const criticalPermissions = ['admin:access', 'ops:access'];
    return error.requiredPermissions.some(perm => criticalPermissions.includes(perm));
  }

  /**
   * Get redirect route based on user permissions
   */
  getRedirectRoute(userPermissions: string[]): string {
    // Determine best route based on user's permissions
    if (userPermissions.includes('sales:read') || userPermissions.includes('sales:manage')) {
      return '/sales-list';
    }
    if (userPermissions.includes('employees:read')) {
      return '/lms/dashboard/employeelist';
    }
    if (userPermissions.includes('master:read')) {
      return '/master';
    }

    // Default fallback
    return '/lms/dashboard';
  }
}
