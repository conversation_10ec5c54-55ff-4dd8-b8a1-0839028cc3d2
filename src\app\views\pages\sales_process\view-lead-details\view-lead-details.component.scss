// Styling for the lead details page

// Detail item styling
.detail-item {
  margin-bottom: 0.5rem;

  .text-muted {
    font-size: 0.75rem;
    display: block;
    margin-bottom: 0.25rem;
  }
}

// Status card styling
.status-card {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);

  .progress {
    background-color: rgba(var(--bs-gray-500-rgb), 0.2);
  }
}

// Timeline styling
.timeline-wrapper {
  position: relative;
  padding-left: 3rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 1.25rem;
    width: 2px;
    background-color: rgba(var(--bs-gray-500-rgb), 0.2);
  }

  .timeline-item {
    position: relative;
    padding-bottom: 1.5rem;

    &:last-child {
      padding-bottom: 0;
    }

    .timeline-badge {
      position: absolute;
      left: -3rem;
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;

      i {
        width: 1rem;
        height: 1rem;
      }
    }

    .timeline-content {
      background-color: #fff;
      padding: 1rem;
      border-radius: 0.375rem;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
  }
}

// Document card styling
.document-card {
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-color: rgba(var(--bs-primary-rgb), 0.2) !important;
  }

  .document-icon {
    i {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

// Tab styling
.nav-tabs {
  border-bottom: 1px solid rgba(var(--bs-gray-500-rgb), 0.2);

  .nav-link {
    color: var(--bs-gray-700);
    border: none;
    padding: 0.75rem 1rem;
    font-weight: 500;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: transparent;
      transition: all 0.2s ease;
    }

    &.active {
      color: var(--bs-primary);
      background-color: transparent;

      &::after {
        background-color: var(--bs-primary);
      }
    }

    &:hover:not(.active) {
      color: var(--bs-gray-900);

      &::after {
        background-color: rgba(var(--bs-gray-500-rgb), 0.2);
      }
    }
  }
}

// Icon styling
.icon-lg {
  width: 3rem;
  height: 3rem;
}

// Badge styling for unique ID
.badge.bg-light-primary {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  font-family: monospace;
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 0.4em 0.8em;
}
