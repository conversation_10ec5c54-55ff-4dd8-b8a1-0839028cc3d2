import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ProductType } from '../../../../../core/services/product-type.service';

@Component({
  selector: 'app-product-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-package me-2"></i>
        {{ isEditMode ? 'Edit' : 'Create' }} Product Type
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Product Type Form - Coming Soon</p>
      <p *ngIf="isEditMode">Editing: {{ productType?.name }}</p>
      
      <div class="alert alert-info">
        <i class="feather icon-info me-2"></i>
        This form will include comprehensive product type management with:
        <ul class="mt-2 mb-0">
          <li>Product specifications and configurations</li>
          <li>Pricing models and business rules</li>
          <li>Support levels and warranty settings</li>
          <li>Custom fields and validation rules</li>
        </ul>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Cancel</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('saved')">Save</button>
    </div>
  `,
  styles: []
})
export class ProductTypeFormComponent {
  @Input() isEditMode = false;
  @Input() productType: ProductType | null = null;

  productTypeForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal
  ) {
    this.productTypeForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      category: ['', [Validators.required]],
      pricing_model: ['', [Validators.required]]
    });
  }
}
