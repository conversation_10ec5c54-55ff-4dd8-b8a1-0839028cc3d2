<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Completed Project</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #completedProjectForm="ngForm">
    <div class="row mb-3">
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectName" class="form-label">Name of the Project <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="projectName" name="projectName" [(ngModel)]="formData.projectName" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="entityName" class="form-label">Name of the Entity <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="entityName" name="entityName" [(ngModel)]="formData.entityName" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="promotersNames" class="form-label">Promoters' Names <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="promotersNames" name="promotersNames" [(ngModel)]="formData.promotersNames" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="profitSharing" class="form-label">Promoters' Profit Sharing <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="profitSharing" name="profitSharing" [(ngModel)]="formData.profitSharing" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="promotersRole" class="form-label">Promoters' Role & Responsibilities <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="promotersRole" name="promotersRole" [(ngModel)]="formData.promotersRole" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="developmentType" class="form-label">Type of Development <span class="text-danger">*</span></label>
        <select class="form-select" id="developmentType" name="developmentType" [(ngModel)]="formData.developmentType" required>
          <option value="">Select Development Type</option>
          <option value="Owned">Owned</option>
          <option value="JDA">JDA</option>
          <option value="JV">JV</option>
        </select>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="location" name="location" [(ngModel)]="formData.location" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectType" class="form-label">Type of Project <span class="text-danger">*</span></label>
        <select class="form-select" id="projectType" name="projectType" [(ngModel)]="formData.projectType" required>
          <option value="">Select Project Type</option>
          <option value="Residential">Residential</option>
          <option value="Commercial">Commercial</option>
          <option value="Mixed-Use">Mixed-Use</option>
          <option value="Industrial">Industrial</option>
          <option value="Retail">Retail</option>
          <option value="Hospitality">Hospitality</option>
          <option value="Other">Other</option>
        </select>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectStructure" class="form-label">Project Structure <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="projectStructure" name="projectStructure" [(ngModel)]="formData.projectStructure" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="surveyNumbers" class="form-label">S No & H No / CTS No <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="surveyNumbers" name="surveyNumbers" [(ngModel)]="formData.surveyNumbers" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="totalUnits" class="form-label">Total No of Units <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="totalUnits" name="totalUnits" [(ngModel)]="formData.totalUnits" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="constructionArea" class="form-label">Construction Area <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="constructionArea" name="constructionArea" [(ngModel)]="formData.constructionArea" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="constructionCost" class="form-label">Total Construction Cost (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="constructionCost" name="constructionCost" [(ngModel)]="formData.constructionCost" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="salesValue" class="form-label">Total Sales Value (₹) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="salesValue" name="salesValue" [(ngModel)]="formData.salesValue" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="unsoldUnits" class="form-label">Unsold Units (If Any) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="unsoldUnits" name="unsoldUnits" [(ngModel)]="formData.unsoldUnits" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="startDate" class="form-label">Start Date <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="startDate" name="startDate" [(ngModel)]="formData.startDate" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="endDate" class="form-label">End Date <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="endDate" name="endDate" [(ngModel)]="formData.endDate" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="occupancyReceived" class="form-label">TP Occupancy Received <span class="text-danger">*</span></label>
        <select class="form-select" id="occupancyReceived" name="occupancyReceived" [(ngModel)]="formData.occupancyReceived" required>
          <option value="">Select Option</option>
          <option value="Yes">Yes</option>
          <option value="No">No</option>
        </select>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="reraNumber" class="form-label">RERA Registration No <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="reraNumber" name="reraNumber" [(ngModel)]="formData.reraNumber" required>
      </div>
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="remarks" class="form-label">Other Remarks</label>
        <textarea class="form-control" id="remarks" name="remarks" rows="2" [(ngModel)]="formData.remarks"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="completedProjectForm.invalid" (click)="saveChanges()">Save</button>
</div>
