// Modern table card styling
.modern-table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  .card-body {
    padding: 1.5rem;
  }
}

// Modern table styling
.modern-table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;

  thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #495057;
    padding: 1rem 0.75rem;
    position: relative;

    &[sortable] {
      cursor: pointer;
      user-select: none;
      
      &:hover {
        background-color: #e9ecef;
      }

      &::after {
        content: '';
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid #6c757d;
        opacity: 0.3;
      }

      &.asc::after {
        border-bottom: 4px solid #007bff;
        border-top: none;
        opacity: 1;
      }

      &.desc::after {
        border-top: 4px solid #007bff;
        border-bottom: none;
        opacity: 1;
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
      }
    }
  }
}

// Action icons styling
.action-icons {
  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    transition: all 0.2s ease;
    color: #6c757d;
    text-decoration: none;

    &:hover:not(.disabled) {
      background-color: #e9ecef;
      color: #495057;
      transform: scale(1.1);
    }

    &.text-danger:hover:not(.disabled) {
      background-color: #f8d7da;
      color: #721c24;
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}

// Empty state styling
.empty-state {
  color: #6c757d;

  i {
    color: #adb5bd;
  }

  .btn {
    margin-top: 1rem;
  }
}

// Status badges
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;

  &.bg-primary {
    background-color: #0d6efd !important;
  }

  &.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
  }

  &.bg-success {
    background-color: #198754 !important;
  }

  &.bg-danger {
    background-color: #dc3545 !important;
  }

  &.bg-secondary {
    background-color: #6c757d !important;
  }
}

// Section titles in modals
.section-title {
  font-weight: 600;
  font-size: 1rem;
  color: #495057;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: #007bff;
  }
}

// Form controls
.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.2s ease;

  &:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  &.is-invalid {
    border-color: #dc3545;
    
    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
}

// Pagination styling
.pagination-sm {
  .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
      color: #495057;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
  }

  .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
  }
}

// Button styling
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;

  &.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// Table column widths for better layout
.modern-table {
  th:nth-child(1), td:nth-child(1) { // S.No.
    width: 80px;
    text-align: center;
  }

  th:nth-child(2), td:nth-child(2) { // Actions
    width: 120px;
    text-align: center;
  }

  th:nth-child(3), td:nth-child(3) { // Holiday/Activity
    min-width: 200px;
    width: 30%;
  }

  th:nth-child(4), td:nth-child(4) { // Month
    width: 150px;
  }

  th:nth-child(5), td:nth-child(5) { // Day
    width: 150px;
  }

  th:nth-child(6), td:nth-child(6) { // Date
    width: 150px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .modern-table-card .card-body {
    padding: 1rem;
  }

  .modern-table {
    font-size: 0.875rem;

    thead th,
    tbody td {
      padding: 0.75rem 0.5rem;
    }

    // Reset column widths on mobile
    th, td {
      width: auto !important;
      min-width: auto !important;
    }
  }

  .action-icons .action-icon {
    width: 28px;
    height: 28px;
  }
}

// Text truncation utility
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
