import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../../core/feather-icon/feather-icon.directive';

@Component({
  selector: 'app-bulk-upload',
  standalone: true,
  imports: [
    CommonModule,
    FeatherIconDirective
  ],
  template: `
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="feather icon-upload me-2"></i>
        Bulk Upload Profession Types
      </h5>
      <button type="button" class="btn-close" (click)="activeModal.dismiss()" aria-label="Close"></button>
    </div>

    <div class="modal-body">
      <p>Bulk Upload Component - Coming Soon</p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="activeModal.dismiss()">Close</button>
      <button type="button" class="btn btn-primary" (click)="activeModal.close('uploaded')">Upload</button>
    </div>
  `,
  styles: []
})
export class BulkUploadComponent {
  constructor(public activeModal: NgbActiveModal) {}
}
