import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Corporate Consultancy interfaces
export interface CorporateConsultancy {
  id: string;
  name: string;
  code: string;
  type: 'financial' | 'legal' | 'tax' | 'audit' | 'management' | 'technology' | 'hr' | 'other';
  specialization?: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active: boolean;
  client_count?: number;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  partnership_level?: 'preferred' | 'standard' | 'trial';
  rating?: number;
  annual_revenue?: number;
  employee_count?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CorporateConsultancyCreate {
  name: string;
  code: string;
  type: 'financial' | 'legal' | 'tax' | 'audit' | 'management' | 'technology' | 'hr' | 'other';
  specialization?: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  partnership_level?: 'preferred' | 'standard' | 'trial';
  rating?: number;
  annual_revenue?: number;
  employee_count?: number;
}

export interface CorporateConsultancyUpdate {
  name?: string;
  code?: string;
  type?: 'financial' | 'legal' | 'tax' | 'audit' | 'management' | 'technology' | 'hr' | 'other';
  specialization?: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  is_active?: boolean;
  established_date?: string;
  license_number?: string;
  regulatory_body?: string;
  partnership_level?: 'preferred' | 'standard' | 'trial';
  rating?: number;
  annual_revenue?: number;
  employee_count?: number;
}

export interface CorporateConsultancyStatistics {
  total_consultancies: number;
  active_consultancies: number;
  inactive_consultancies: number;
  consultancies_by_type: { [type: string]: number };
  consultancies_by_partnership_level: { [level: string]: number };
  total_clients: number;
  average_rating: number;
  popular_consultancies: CorporateConsultancy[];
}

export interface BulkUploadResult {
  success: boolean;
  total_processed: number;
  successful_imports: number;
  failed_imports: number;
  errors: string[];
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CorporateConsultancyService {
  private readonly baseUrl = `${environment.apiUrl}/api/v1/corporate-consultancies/`;
  private consultanciesSubject = new BehaviorSubject<CorporateConsultancy[]>([]);
  public consultancies$ = this.consultanciesSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all corporate consultancies with optional filtering and pagination
   */
  getConsultancies(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    is_active?: boolean;
    type?: string;
    partnership_level?: string;
    country?: string;
    regulatory_body?: string;
    include_deleted?: boolean;
  }): Observable<APIResponse<CorporateConsultancy[]>> {
    let httpParams = new HttpParams();

    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<CorporateConsultancy[]>>(this.baseUrl, { params: httpParams })
      .pipe(
        tap(response => {
          if (response.success && response.data) {
            this.consultanciesSubject.next(response.data);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get consultancy by ID
   */
  getConsultancyById(id: string): Observable<APIResponse<CorporateConsultancy>> {
    return this.http.get<APIResponse<CorporateConsultancy>>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create new consultancy
   */
  createConsultancy(consultancy: CorporateConsultancyCreate): Observable<APIResponse<CorporateConsultancy>> {
    return this.http.post<APIResponse<CorporateConsultancy>>(this.baseUrl, consultancy)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConsultancies();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update consultancy
   */
  updateConsultancy(id: string, consultancy: CorporateConsultancyUpdate): Observable<APIResponse<CorporateConsultancy>> {
    return this.http.put<APIResponse<CorporateConsultancy>>(`${this.baseUrl}/${id}`, consultancy)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConsultancies();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Soft delete consultancy
   */
  deleteConsultancy(id: string): Observable<APIResponse<void>> {
    return this.http.delete<APIResponse<void>>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConsultancies();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Restore deleted consultancy
   */
  restoreConsultancy(id: string): Observable<APIResponse<CorporateConsultancy>> {
    return this.http.post<APIResponse<CorporateConsultancy>>(`${this.baseUrl}/${id}/restore`, {})
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConsultancies();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get consultancy statistics
   */
  getConsultancyStatistics(): Observable<APIResponse<CorporateConsultancyStatistics>> {
    return this.http.get<APIResponse<CorporateConsultancyStatistics>>(`${this.baseUrl}/statistics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Bulk upload consultancies
   */
  bulkUpload(file: File): Observable<APIResponse<BulkUploadResult>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<APIResponse<BulkUploadResult>>(`${this.baseUrl}/bulk-upload`, formData)
      .pipe(
        tap(response => {
          if (response.success) {
            this.refreshConsultancies();
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Download bulk upload template
   */
  downloadTemplate(): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/template/download`, {
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
  }

  /**
   * Search consultancies
   */
  searchConsultancies(query: string, filters?: {
    is_active?: boolean;
    type?: string;
    partnership_level?: string;
    country?: string;
    regulatory_body?: string;
  }): Observable<APIResponse<CorporateConsultancy[]>> {
    let params = new HttpParams().set('search', query);

    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key as keyof typeof filters];
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<APIResponse<CorporateConsultancy[]>>(this.baseUrl, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get consultancies for dropdown (simplified data)
   */
  getConsultanciesDropdown(): Observable<{ id: string; name: string; code: string; type: string }[]> {
    return this.getConsultancies({ per_page: 1000, is_active: true }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map(consultancy => ({
            id: consultancy.id,
            name: consultancy.name,
            code: consultancy.code,
            type: consultancy.type
          }));
        }
        return [];
      })
    );
  }

  /**
   * Get active consultancies only
   */
  getActiveConsultancies(): Observable<CorporateConsultancy[]> {
    return this.getConsultancies({ is_active: true }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Get consultancies by type
   */
  getConsultanciesByType(type: string): Observable<CorporateConsultancy[]> {
    return this.getConsultancies({ type }).pipe(
      map(response => response.success ? response.data : [])
    );
  }

  /**
   * Refresh consultancies data
   */
  refreshConsultancies(): void {
    this.getConsultancies().subscribe();
  }

  /**
   * Clear consultancies cache
   */
  clearCache(): void {
    this.consultanciesSubject.next([]);
  }

  /**
   * Get consultancy types
   */
  getConsultancyTypes(): { value: string; label: string; description: string }[] {
    return [
      { value: 'financial', label: 'Financial Consulting', description: 'Financial advisory and planning services' },
      { value: 'legal', label: 'Legal Services', description: 'Legal advisory and compliance services' },
      { value: 'tax', label: 'Tax Consulting', description: 'Tax planning and compliance services' },
      { value: 'audit', label: 'Audit Services', description: 'Internal and external audit services' },
      { value: 'management', label: 'Management Consulting', description: 'Business strategy and operations consulting' },
      { value: 'technology', label: 'Technology Consulting', description: 'IT and digital transformation services' },
      { value: 'hr', label: 'HR Consulting', description: 'Human resources and talent management' },
      { value: 'other', label: 'Other', description: 'Other specialized consulting services' }
    ];
  }

  /**
   * Get partnership levels
   */
  getPartnershipLevels(): { value: string; label: string; description: string }[] {
    return [
      { value: 'preferred', label: 'Preferred Partner', description: 'Top-tier strategic partners with exclusive benefits' },
      { value: 'standard', label: 'Standard Partner', description: 'Regular business partners with standard terms' },
      { value: 'trial', label: 'Trial Partner', description: 'New partners under evaluation period' }
    ];
  }

  /**
   * Get country list for dropdown
   */
  getCountryList(): string[] {
    return [
      'India',
      'United States',
      'United Kingdom',
      'Canada',
      'Australia',
      'Singapore',
      'Hong Kong',
      'Japan',
      'Germany',
      'France',
      'Switzerland',
      'Netherlands',
      'Luxembourg',
      'UAE',
      'Malaysia',
      'South Africa',
      'Brazil',
      'Mexico'
    ];
  }

  /**
   * Get regulatory bodies list
   */
  getRegulatoryBodies(): string[] {
    return [
      'SEBI (India)',
      'ICAI (India)',
      'Bar Council of India',
      'SEC (USA)',
      'AICPA (USA)',
      'FCA (UK)',
      'ICAEW (UK)',
      'CPA Australia',
      'ACCA (Global)',
      'CISA (Global)',
      'PMI (Global)',
      'SHRM (Global)',
      'Local Bar Association',
      'Local CPA Board',
      'Other Professional Body'
    ];
  }

  /**
   * Format annual revenue
   */
  formatRevenue(amount?: number): string {
    if (!amount) return 'Not specified';

    if (amount >= 1000000000) {
      return `₹${(amount / 1000000000).toFixed(2)}B`;
    } else if (amount >= 1000000) {
      return `₹${(amount / 1000000).toFixed(2)}M`;
    } else if (amount >= 1000) {
      return `₹${(amount / 1000).toFixed(2)}K`;
    }

    return `₹${amount.toLocaleString()}`;
  }

  /**
   * Get consultancy type label
   */
  getConsultancyTypeLabel(type: string): string {
    const types = this.getConsultancyTypes();
    const typeObj = types.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  /**
   * Get partnership level label
   */
  getPartnershipLevelLabel(level: string): string {
    const levels = this.getPartnershipLevels();
    const levelObj = levels.find(l => l.value === level);
    return levelObj ? levelObj.label : level;
  }

  /**
   * Get rating stars
   */
  getRatingStars(rating?: number): string {
    if (!rating) return '☆☆☆☆☆';
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5 ? 1 : 0;
    const emptyStars = 5 - fullStars - halfStar;

    return '★'.repeat(fullStars) + (halfStar ? '☆' : '') + '☆'.repeat(emptyStars);
  }

  /**
   * Error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('Corporate Consultancy service error:', error);

    let errorMessage = 'An error occurred while processing your request.';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }
}
