<div class="modal-header">
  <h5 class="modal-title text-light">{{ formData.id ? 'Edit' : 'Add' }} Land Bank & Upcoming Project</h5>
  <button type="button" class="btn-close" (click)="activeModal.dismiss('Cross click')" aria-label="Close"></button>
</div>
<div class="modal-body">
  <form #landBankForm="ngForm">
    <div class="row mb-3">
      <!-- Project Name -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectName" class="form-label">Project Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="projectName" name="projectName" [(ngModel)]="formData.projectName" required>
      </div>

      <!-- Entity Name -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="entityName" class="form-label">Entity Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="entityName" name="entityName" [(ngModel)]="formData.entityName" required>
      </div>

      <!-- Ownership Type -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="ownershipType" class="form-label">Ownership Type <span class="text-danger">*</span></label>
        <select class="form-select" id="ownershipType" name="ownershipType" [(ngModel)]="formData.ownershipType" required>
          <option value="" disabled>Select Ownership Type</option>
          <option *ngFor="let option of ownershipTypeOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <!-- Documentation Status -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="documentationStatus" class="form-label">Documentation Status <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="documentationStatus" name="documentationStatus" [(ngModel)]="formData.documentationStatus" required>
      </div>

      <!-- Location -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="location" name="location" [(ngModel)]="formData.location" required>
      </div>

      <!-- Project Type -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="projectType" class="form-label">Project Type <span class="text-danger">*</span></label>
        <select class="form-select" id="projectType" name="projectType" [(ngModel)]="formData.projectType" required>
          <option value="" disabled>Select Project Type</option>
          <option *ngFor="let option of projectTypeOptions" [value]="option.value">{{ option.label }}</option>
        </select>
      </div>

      <!-- Expected Start Date -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="expectedStartDate" class="form-label">Expected Start Date <span class="text-danger">*</span></label>
        <input type="date" class="form-control" id="expectedStartDate" name="expectedStartDate" [(ngModel)]="formData.expectedStartDate" required>
      </div>

      <!-- Residential Area -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="residentialArea" class="form-label">Residential Area (Sq Ft)</label>
        <input type="number" class="form-control" id="residentialArea" name="residentialArea" [(ngModel)]="formData.residentialArea" min="0">
      </div>

      <!-- Residential Units -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="residentialUnits" class="form-label">Residential Units</label>
        <input type="number" class="form-control" id="residentialUnits" name="residentialUnits" [(ngModel)]="formData.residentialUnits" min="0">
      </div>

      <!-- Commercial Area -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="commercialArea" class="form-label">Commercial Area (Sq Ft)</label>
        <input type="number" class="form-control" id="commercialArea" name="commercialArea" [(ngModel)]="formData.commercialArea" min="0">
      </div>

      <!-- Commercial Units -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="commercialUnits" class="form-label">Commercial Units</label>
        <input type="number" class="form-control" id="commercialUnits" name="commercialUnits" [(ngModel)]="formData.commercialUnits" min="0">
      </div>

      <!-- Mixed Area -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="mixedArea" class="form-label">Mixed Area (Sq Ft)</label>
        <input type="number" class="form-control" id="mixedArea" name="mixedArea" [(ngModel)]="formData.mixedArea" min="0">
      </div>

      <!-- Mixed Units -->
      <div class="col-12 col-md-6 col-lg-4 mb-3">
        <label for="mixedUnits" class="form-label">Mixed Units</label>
        <input type="number" class="form-control" id="mixedUnits" name="mixedUnits" [(ngModel)]="formData.mixedUnits" min="0">
      </div>

      <!-- Remarks -->
      <div class="col-12 mb-3">
        <label for="remarks" class="form-label">Remarks</label>
        <textarea class="form-control" id="remarks" name="remarks" [(ngModel)]="formData.remarks" rows="3"></textarea>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
  <button type="button" class="btn btn-primary" [disabled]="landBankForm.invalid" (click)="saveChanges()">
    <i data-feather="save" class="icon-sm me-1"></i> Save
  </button>
</div>
