import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { map, Observable, tap, catchError, of, retry } from 'rxjs';
import { environment } from '../../../environments/environment';
import { SmartCacheService } from './smart-cache.service';
import {
  Profession,
  ProfessionResponse,
  ProfessionCreate,
  ProfessionUpdate
} from '../models/profession.model';
import {
  Associate,
  AssociateResponse,
  AssociateCreate,
  AssociateUpdate
} from '../models/associate.model';

@Injectable({
  providedIn: 'root'
})
export class MasterService {
  private baseUrl = environment.apiUrl;

  // Map to store original UUIDs by numeric IDs
  private professionIdMap: Map<number, string> = new Map();
  private associateIdMap: Map<number, string> = new Map();

  constructor(
    private http: HttpClient,
    private smartCache: SmartCacheService
  ) {}

  /**
   * Force refresh associate data by clearing cache
   */
  refreshAssociateCache(): void {
    this.associateIdMap.clear();
  }

  /**
   * Force refresh profession data by clearing cache
   */
  refreshProfessionCache(): void {
    this.professionIdMap.clear();
  }

  // Helper method to safely get the original UUID
  private getOriginalUuid(numericId: number): string {
    const originalId = this.professionIdMap.get(numericId);
    if (!originalId) {
      console.error(`No UUID mapping found for numeric ID: ${numericId}`);
      throw new Error(`No UUID mapping found for ID: ${numericId}`);
    }
    return originalId;
  }

  // Helper method to safely get the original UUID for associates
  private getAssociateOriginalUuid(numericId: number): string {
    const originalId = this.associateIdMap.get(numericId);
    if (!originalId) {
      console.error(`No UUID mapping found for associate ID: ${numericId}`);
      throw new Error(`No UUID mapping found for associate ID: ${numericId}`);
    }
    return originalId;
  }

  // Profession API Methods
//   getProfessions(page = 1, size = 10, search?: string): Observable<ProfessionResponse> {
//     let params = new HttpParams()
//       .set('page', page.toString())
//       .set('size', size.toString());

//     if (search) {
//       params = params.set('search', search);
//     }

//     return this.http.get<ProfessionResponse>(`${this.baseUrl}/api/v1/master/professions`, { params });
//   }



/**
 * Get professions with skip/limit pagination (new API format)
 */
getProfessions(skip = 0, limit = 10, search?: string): Observable<ProfessionResponse> {
  let params = new HttpParams()
    .set('skip', skip.toString())
    .set('limit', limit.toString());

  if (search) {
    params = params.set('search', search);
  }

  // Create cache key based on parameters
  const cacheKey = `professions_${skip}_${limit}_${search || 'all'}`;

  // Use smart cache for master data (1 hour TTL)
  return this.smartCache.cacheMasterData(cacheKey, () =>
    this.http.get<any>(`${this.baseUrl}/api/v1/master/professions`, { params })
      .pipe(
      retry(1), // Retry once on failure
      map(response => {
        // Clear the existing ID map when refreshing the list
        this.professionIdMap.clear();

        // Transform the API response to match the expected ProfessionResponse structure
        // AND convert UUID strings to numeric IDs
        const transformedProfessions = response.data?.map((prof: any, index: number) => {
          const numericId = index + 1;
          // Store the mapping between numeric ID and original UUID
          this.professionIdMap.set(numericId, prof.id);

          return {
            ...prof,
            id: numericId, // Generate numeric IDs (1, 2, 3, etc.)
            // Store the original UUID as a hidden property if needed
            _originalId: prof.id
          };
        }) || [];

        // Handle new pagination structure from API response
        let totalCount = transformedProfessions.length;
        let totalPages = Math.ceil(totalCount / limit);
        let currentPage = Math.floor(skip / limit) + 1;

        // Check if API response has new pagination structure
        if (response.meta && response.meta.pagination) {
          const pagination = response.meta.pagination;
          totalCount = pagination.total_count || totalCount;
          totalPages = pagination.total_pages || totalPages;
          currentPage = pagination.current_page || currentPage;
        }

        const transformedResponse: ProfessionResponse = {
          items: transformedProfessions,
          total: totalCount,
          page: currentPage,
          size: limit,
          pages: totalPages
        };

        return transformedResponse;
      }),

      catchError(error => {
        console.error('Error loading professions:', error);
        return of({
          items: [],
          total: 0,
          page: 1,
          size: limit,
          pages: 0
        } as ProfessionResponse);
      })
    )
  );
}

  /**
   * Get professions with page/size pagination (backward compatibility)
   * Converts page/size to skip/limit format
   */
  getProfessionsLegacy(page = 1, size = 10, search?: string): Observable<ProfessionResponse> {
    const skip = (page - 1) * size;
    return this.getProfessions(skip, size, search);
  }

  getProfession(id: number): Observable<Profession | null> {
  try {
    // Get the original UUID for the given numeric ID
    const originalId = this.getOriginalUuid(id);

    console.log(`Getting profession with numeric ID ${id}, using UUID ${originalId}`);

    // Add cache-busting parameter
    const params = new HttpParams().set('_t', Date.now().toString());

    return this.http.get<any>(`${this.baseUrl}/api/v1/master/professions/${originalId}`, { params })
      .pipe(
        map(response => {
          console.log('Raw profession response:', response);

          // Extract the profession data from the response
          const professionData = response.data || response;

          // Transform the response to use the numeric ID
          const transformedProfession = {
            ...professionData,
            id: id,
            _originalId: originalId
          };

          console.log('Transformed profession:', transformedProfession);
          return transformedProfession;
        }),
        catchError(error => {
          console.error(`Error fetching profession with ID ${id}:`, error);
          return of(null);
        })
      );
  } catch (error: any) {
    console.error(`Error in getProfession(${id}):`, error.message);
    return of(null);
  }
}

  createProfession(profession: ProfessionCreate): Observable<Profession> {
    return this.http.post<Profession>(`${this.baseUrl}/api/v1/master/professions`, profession);
  }

  updateProfession(id: number, profession: ProfessionUpdate): Observable<Profession | null> {
  try {
    // Get the original UUID for the given numeric ID
    const originalId = this.getOriginalUuid(id);

    console.log(`Updating profession with numeric ID ${id}, using UUID ${originalId}`);
    return this.http.put<any>(`${this.baseUrl}/api/v1/master/professions/${originalId}`, profession)
      .pipe(
        map(response => {
          // Return the response with the numeric ID
          return {
            ...response,
            id: id,
            _originalId: originalId
          };
        }),
        catchError(error => {
          console.error(`Error updating profession with ID ${id}:`, error);
          return of(null);
        })
      );
  } catch (error: any) {
    console.error(`Error in updateProfession(${id}):`, error.message);
    return of(null);
  }
}

  deleteProfession(id: number): Observable<any> {
  try {
    // Get the original UUID for the given numeric ID
    const originalId = this.getOriginalUuid(id);

    console.log(`Deleting profession with numeric ID ${id}, using UUID ${originalId}`);
    return this.http.delete<any>(`${this.baseUrl}/api/v1/master/professions/${originalId}`)
      .pipe(
        catchError(error => {
          console.error(`Error deleting profession with ID ${id}:`, error);
          return of(null);
        })
      );
  } catch (error: any) {
    console.error(`Error in deleteProfession(${id}):`, error.message);
    return of(null);
  }
}

  restoreProfession(id: number): Observable<Profession | null> {
  try {
    // Get the original UUID for the given numeric ID
    const originalId = this.getOriginalUuid(id);

    console.log(`Restoring profession with numeric ID ${id}, using UUID ${originalId}`);
    return this.http.post<any>(`${this.baseUrl}/api/v1/master/professions/${originalId}/restore`, {})
      .pipe(
        map(response => {
          // Return the response with the numeric ID
          return {
            ...response,
            id: id,
            _originalId: originalId
          };
        }),
        catchError(error => {
          console.error(`Error restoring profession with ID ${id}:`, error);
          return of(null);
        })
      );
  } catch (error: any) {
    console.error(`Error in restoreProfession(${id}):`, error.message);
    return of(null);
  }
}

  /**
   * Get all active professions for dropdown usage
   * Simplified method for UI components
   * @returns Observable of active professions with full structure for filtering
   */
  getActiveProfessions(): Observable<{id: string, name: string, type: string, status: string, description?: string}[]> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/master/professions`, {
      params: new HttpParams()
        .set('page', '1')
        .set('size', '1000')
        .set('status', 'active')
    }).pipe(
      map(response => {
        console.log('Professions API response:', response);
        if (response.success && response.data) {
          return response.data.map((prof: any) => ({
            id: prof.id,
            name: prof.name,
            type: prof.type,
            status: prof.status,
            description: prof.description
          }));
        }
        return [];
      }),
      catchError(error => {
        console.error('Error loading active professions:', error);
        return of([]);
      })
    );
  }

  /**
   * Get all profession types
   * @returns Observable of profession types
   */
  getProfessionTypes(): Observable<any[]> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/master/profession-types`)
      .pipe(
        map(response => response.data || response.items || []),
        catchError(error => {
          console.error('Error loading profession types:', error);
          return of([]);
        })
      );
  }

  /**
   * Get all professions (not filtered by status)
   * @returns Observable of all professions
   */
  getAllProfessions(): Observable<any[]> {
    return this.http.get<any>(`${this.baseUrl}/api/v1/master/professions`)
      .pipe(
        map(response => response.data || response.items || []),
        catchError(error => {
          console.error('Error loading professions:', error);
          return of([]);
        })
      );
  }

  // Associate API Methods

  // Get raw associates data for sales form (without transformation)
  getRawAssociates(): Observable<any> {
    const url = `${this.baseUrl}/api/v1/master/associates`;
    console.log('🌐 Making API call to get raw associates:', url);

    return this.http.get<any>(url)
      .pipe(
        retry(1),
        map(response => {
          console.log('🔍 Raw associates API response:', response);

          // Handle different response structures
          if (response && response.data) {
            return { success: true, data: response.data };
          } else if (Array.isArray(response)) {
            return { success: true, data: response };
          } else {
            console.warn('⚠️ Unexpected response structure:', response);
            return { success: false, data: [], error: 'Unexpected response structure' };
          }
        }),
        catchError(error => {
          console.error('❌ Error loading raw associates:', error);
          return of({ success: false, data: [], error: error.message });
        })
      );
  }

  getAssociates(skip = 0, limit = 10, search?: string): Observable<AssociateResponse> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    const url = `${this.baseUrl}/api/v1/master/associates`;
    console.log('🌐 Making API call to:', url);
    console.log('🌐 With params:', params.toString());

    // Create cache key based on parameters
    const cacheKey = `associates_${skip}_${limit}_${search || 'all'}`;

    // Use smart cache for master data (1 hour TTL)
    return this.smartCache.cacheMasterData(cacheKey, () =>
      this.http.get<any>(url, { params })
        .pipe(
        retry(1), // Retry once on failure
        map(response => {
          console.log('Original Associates API response:', response);

          // Clear the existing ID map when refreshing the list
          this.associateIdMap.clear();

          // Transform the API response to match the expected AssociateResponse structure
          // AND convert UUID strings to numeric IDs
          const transformedAssociates = response.data?.map((assoc: any, index: number) => {
            const numericId = index + 1;
            // Store the mapping between numeric ID and original UUID
            this.associateIdMap.set(numericId, assoc.id);

            return {
              id: numericId, // Generate numeric IDs (1, 2, 3, etc.)
              name: assoc.associate_name || assoc.name, // Map associate_name to name
              category: assoc.company_name || assoc.category || 'other', // Map company_name to category
              status: assoc.status ? 'active' : 'inactive', // Convert boolean to string
              created_at: assoc.created_at,
              updated_at: assoc.updated_at,
              deleted_at: assoc.deleted_at,
              // Store additional fields for reference
              company_name: assoc.company_name,
              location_id: assoc.location_id,
              location_name: assoc.location_name,
              sub_location: assoc.sub_location,
              profession_type: assoc.profession_type,
              profession_id: assoc.profession_id,
              profession_name: assoc.profession_name,
              // Store the original UUID as a hidden property if needed
              _originalId: assoc.id
            };
          }) || [];

          // Handle new pagination structure from API response
          let totalCount = transformedAssociates.length;
          let totalPages = Math.ceil(totalCount / limit);
          let currentPage = Math.floor(skip / limit) + 1;

          // Check if API response has new pagination structure
          if (response.meta && response.meta.pagination) {
            const pagination = response.meta.pagination;
            totalCount = pagination.total_count || totalCount;
            totalPages = pagination.total_pages || totalPages;
            currentPage = pagination.current_page || currentPage;
          }

          const transformedResponse: AssociateResponse = {
            items: transformedAssociates,
            total: totalCount,
            page: currentPage,
            size: limit,
            pages: totalPages
          };

          return transformedResponse;
        }),
        tap(transformedResponse => {
          console.log('Transformed associates response:', transformedResponse);
          console.log('Associate items:', transformedResponse.items);
          console.log('Associate ID mapping:', [...this.associateIdMap.entries()]);
        }),
        catchError(error => {
          console.error('Error loading associates:', error);
          return of({
            items: [],
            total: 0,
            page: 1,
            size: limit,
            pages: 0
          } as AssociateResponse);
        })
      )
    );
  }

  /**
   * Get associates with page/size pagination (backward compatibility)
   * Converts page/size to skip/limit format
   */
  getAssociatesLegacy(page = 1, size = 10, search?: string): Observable<AssociateResponse> {
    const skip = (page - 1) * size;
    return this.getAssociates(skip, size, search);
  }

  getAssociate(id: number): Observable<Associate | null> {
    try {
      // Get the original UUID for the given numeric ID
      const originalId = this.getAssociateOriginalUuid(id);

      console.log(`Getting associate with numeric ID ${id}, using UUID ${originalId}`);

      // Add cache-busting parameter
      const params = new HttpParams().set('_t', Date.now().toString());

      return this.http.get<any>(`${this.baseUrl}/api/v1/master/associates/${originalId}`, { params })
        .pipe(
          map(response => {
            console.log('Raw associate response:', response);

            // Extract the associate data from the response
            const associateData = response.data || response;
            console.log('Associate data extracted:', associateData);
            console.log('Associate name field:', associateData.associate_name);
            console.log('Associate status field:', associateData.status, typeof associateData.status);

            // Transform the response to use the numeric ID and consistent field mapping
            const transformedAssociate: Associate = {
              id: id,
              name: associateData.associate_name || associateData.name, // Map associate_name to name
              category: associateData.company_name || associateData.category || 'other', // Map company_name to category
              status: (associateData.status ? 'active' : 'inactive') as 'active' | 'inactive', // Convert boolean to string
              created_at: associateData.created_at,
              updated_at: associateData.updated_at,
              deleted_at: associateData.deleted_at,
              // Store additional fields for reference
              company_name: associateData.company_name,
              location_id: associateData.location_id,
              location_name: associateData.location_name,
              sub_location: associateData.sub_location,
              profession_type: associateData.profession_type,
              profession_id: associateData.profession_id,
              profession_name: associateData.profession_name,
              _originalId: originalId
            };

            console.log('Transformed associate:', transformedAssociate);
            return transformedAssociate;
          }),
          catchError(error => {
            console.error(`Error fetching associate with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in getAssociate(${id}):`, error.message);
      return of(null);
    }
  }

  createAssociate(associate: AssociateCreate): Observable<Associate> {
    return this.http.post<Associate>(`${this.baseUrl}/api/v1/master/associates`, associate);
  }
  updateAssociate(id: number, associate: AssociateUpdate): Observable<Associate | null> {
    try {
      // Get the original UUID for the given numeric ID
      const originalId = this.getAssociateOriginalUuid(id);

      console.log(`Updating associate with numeric ID ${id}, using UUID ${originalId}`);
      return this.http.put<any>(`${this.baseUrl}/api/v1/master/associates/${originalId}`, associate)
        .pipe(
          map(response => {
            // Return the response with the numeric ID
            return {
              ...response,
              id: id,
              _originalId: originalId
            };
          }),
          catchError(error => {
            console.error(`Error updating associate with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in updateAssociate(${id}):`, error.message);
      return of(null);
    }
  }

  deleteAssociate(id: number): Observable<any> {
    try {
      // Get the original UUID for the given numeric ID
      const originalId = this.getAssociateOriginalUuid(id);

      console.log(`Deleting associate with numeric ID ${id}, using UUID ${originalId}`);
      return this.http.delete<any>(`${this.baseUrl}/api/v1/master/associates/${originalId}`)
        .pipe(
          catchError(error => {
            console.error(`Error deleting associate with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in deleteAssociate(${id}):`, error.message);
      return of(null);
    }
  }

  restoreAssociate(id: number): Observable<Associate | null> {
    try {
      // Get the original UUID for the given numeric ID
      const originalId = this.getAssociateOriginalUuid(id);

      console.log(`Restoring associate with numeric ID ${id}, using UUID ${originalId}`);
      return this.http.post<any>(`${this.baseUrl}/api/v1/master/associates/${originalId}/restore`, {})
        .pipe(
          map(response => {
            // Return the response with the numeric ID
            return {
              ...response,
              id: id,
              _originalId: originalId
            };
          }),
          catchError(error => {
            console.error(`Error restoring associate with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in restoreAssociate(${id}):`, error.message);
      return of(null);
    }
  }

  /**
   * Get profession types for dropdown (simplified structure)
   * GET /api/v1/master/profession-types
   */
  getProfessionTypesForDropdown(): Observable<{id: string, value: string, label: string}[]> {
    return this.http.get<{success: boolean, data: any[], error?: string, meta?: any}>(`${this.baseUrl}/api/v1/master/profession-types`)
      .pipe(
        map(response => {
          console.log('Profession Types API response:', response);
          if (response && response.success && response.data) {
            return response.data.map((type: any) => ({
              id: type.id,
              value: type.value,
              label: type.label
            }));
          }
          return [];
        }),
        catchError(error => {
          console.error('Error fetching profession types for dropdown:', error);
          return of([]);
        })
      );
  }

  /**
   * Get Connect With list for dropdown
   * GET /api/v1/master/connect-with
   */
  getConnectWithList(): Observable<{id: string, name: string, description: string, created_at: string, updated_at: string}[]> {
    return this.http.get<{success: boolean, data: any[], error?: string, meta?: any}>(`${this.baseUrl}/api/v1/master/connect-with`)
      .pipe(
        map(response => {
          console.log('Connect With API response:', response);
          if (response && response.success && response.data) {
            return response.data.map((item: any) => ({
              id: item.id,
              name: item.name,
              description: item.description,
              created_at: item.created_at,
              updated_at: item.updated_at
            }));
          }
          return [];
        }),
        catchError(error => {
          console.error('Error fetching connect with list:', error);
          return of([]);
        })
      );
  }

  // Connect With CRUD API Methods
  private connectWithIdMap = new Map<number, string>();

  /**
   * Force refresh connect with data by clearing cache
   */
  refreshConnectWithCache(): void {
    this.connectWithIdMap.clear();
  }

  /**
   * Get Connect With items with pagination
   * GET /api/v1/master/connect-with
   */
  getConnectWithItems(skip = 0, limit = 10, search?: string): Observable<any> {
    let params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString())
      .set('_t', Date.now().toString()); // Cache busting parameter

    if (search) {
      params = params.set('search', search);
    }

    const url = `${this.baseUrl}/api/v1/master/connect-with`;
    console.log('🌐 Making Connect With API call to:', url);
    console.log('🌐 With params:', params.toString());

    return this.http.get<any>(url, { params })
      .pipe(
        retry(1),
        map(response => {
          console.log('Original Connect With API response:', response);

          // Clear the existing ID map when refreshing the list
          this.connectWithIdMap.clear();

          // Transform the API response to match the expected structure
          const transformedItems = response.data?.map((item: any, index: number) => {
            const numericId = index + 1;
            // Store the mapping between numeric ID and original UUID
            this.connectWithIdMap.set(numericId, item.id);

            return {
              id: numericId,
              name: item.name,
              description: item.description,
              created_at: item.created_at,
              updated_at: item.updated_at,
              deleted_at: item.deleted_at,
              _originalId: item.id
            };
          }) || [];

          // Handle new pagination structure from API response
          let totalCount = transformedItems.length;
          let totalPages = Math.ceil(totalCount / limit);
          let currentPage = Math.floor(skip / limit) + 1;

          // Check if API response has new pagination structure
          if (response.meta && response.meta.pagination) {
            const pagination = response.meta.pagination;
            totalCount = pagination.total_count || totalCount;
            totalPages = pagination.total_pages || totalPages;
            currentPage = pagination.current_page || currentPage;
          } else if (response.meta) {
            // Fallback to old structure
            totalCount = response.meta.total || totalCount;
            totalPages = response.meta.pages || totalPages;
            currentPage = response.meta.page || currentPage;
          }

          return {
            items: transformedItems,
            total: totalCount,
            page: currentPage,
            size: limit,
            pages: totalPages
          };
        }),
        catchError(error => {
          console.error('❌ Error loading connect with items:', error);
          return of({ items: [], total: 0, page: 1, size: limit, pages: 0 });
        })
      );
  }

  /**
   * Get connect-with items with page/size pagination (backward compatibility)
   * Converts page/size to skip/limit format
   */
  getConnectWithItemsLegacy(page = 1, size = 10, search?: string): Observable<any> {
    const skip = (page - 1) * size;
    return this.getConnectWithItems(skip, size, search);
  }

  /**
   * Get Connect With original UUID for a given numeric ID
   */
  private getConnectWithOriginalUuid(numericId: number): string {
    const originalId = this.connectWithIdMap.get(numericId);
    if (!originalId) {
      throw new Error(`No UUID mapping found for Connect With ID ${numericId}`);
    }
    return originalId;
  }

  /**
   * Get single Connect With item by ID
   * GET /api/v1/master/connect-with/{id}
   */
  getConnectWithItem(id: number): Observable<any | null> {
    try {
      const originalId = this.getConnectWithOriginalUuid(id);

      console.log(`Getting connect with item with numeric ID ${id}, using UUID ${originalId}`);

      // Add cache-busting parameter
      const params = new HttpParams().set('_t', Date.now().toString());

      return this.http.get<any>(`${this.baseUrl}/api/v1/master/connect-with/${originalId}`, { params })
        .pipe(
          map(response => {
            if (response && response.success && response.data) {
              return {
                ...response.data,
                id: id,
                _originalId: originalId
              };
            }
            return null;
          }),
          catchError(error => {
            console.error(`Error fetching connect with item with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in getConnectWithItem(${id}):`, error.message);
      return of(null);
    }
  }



  /**
   * Create new Connect With item
   * POST /api/v1/master/connect-with
   */
  createConnectWithItem(item: any): Observable<any | null> {
    console.log('Creating connect with item:', item);
    return this.http.post<any>(`${this.baseUrl}/api/v1/master/connect-with`, item)
      .pipe(
        map(response => {
          console.log('Connect with item created successfully:', response);
          return response;
        }),
        catchError(error => {
          console.error('Error creating connect with item:', error);
          return of(null);
        })
      );
  }

  /**
   * Update Connect With item
   * PUT /api/v1/master/connect-with/{id}
   */
  updateConnectWithItem(id: number, item: any): Observable<any | null> {
    try {
      const originalId = this.getConnectWithOriginalUuid(id);

      console.log(`Updating connect with item with numeric ID ${id}, using UUID ${originalId}`);
      return this.http.put<any>(`${this.baseUrl}/api/v1/master/connect-with/${originalId}`, item)
        .pipe(
          map(response => {
            return {
              ...response,
              id: id,
              _originalId: originalId
            };
          }),
          catchError(error => {
            console.error(`Error updating connect with item with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in updateConnectWithItem(${id}):`, error.message);
      return of(null);
    }
  }

  /**
   * Delete Connect With item
   * DELETE /api/v1/master/connect-with/{id}
   */
  deleteConnectWithItem(id: number): Observable<any | null> {
    try {
      const originalId = this.getConnectWithOriginalUuid(id);

      console.log(`Deleting connect with item with numeric ID ${id}, using UUID ${originalId}`);
      return this.http.delete<any>(`${this.baseUrl}/api/v1/master/connect-with/${originalId}`)
        .pipe(
          map(response => {
            console.log('Connect with item deleted successfully:', response);
            return response;
          }),
          catchError(error => {
            console.error(`Error deleting connect with item with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in deleteConnectWithItem(${id}):`, error.message);
      return of(null);
    }
  }

  /**
   * Restore Connect With item
   * POST /api/v1/master/connect-with/{id}/restore
   */
  restoreConnectWithItem(id: number): Observable<any | null> {
    try {
      const originalId = this.getConnectWithOriginalUuid(id);

      console.log(`Restoring connect with item with numeric ID ${id}, using UUID ${originalId}`);
      return this.http.post<any>(`${this.baseUrl}/api/v1/master/connect-with/${originalId}/restore`, {})
        .pipe(
          map(response => {
            console.log('Connect with item restored successfully:', response);
            return response;
          }),
          catchError(error => {
            console.error(`Error restoring connect with item with ID ${id}:`, error);
            return of(null);
          })
        );
    } catch (error: any) {
      console.error(`Error in restoreConnectWithItem(${id}):`, error.message);
      return of(null);
    }
  }
}
